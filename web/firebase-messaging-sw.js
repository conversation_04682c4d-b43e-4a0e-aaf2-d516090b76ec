// Firebase Messaging Service Worker for Web Push Notifications
// 這個文件必須放在 web 根目錄下

// Import Firebase scripts
importScripts('https://www.gstatic.com/firebasejs/10.7.0/firebase-app-compat.js');
importScripts('https://www.gstatic.com/firebasejs/10.7.0/firebase-messaging-compat.js');

// Firebase configuration - 請確保與您的 Firebase 專案配置一致
const firebaseConfig = {
  apiKey: "AIzaSyBHhYGKdOJBfcWJgJKOHhYGKdOJBfcWJgJ", // 請替換為您的實際 API Key
  authDomain: "astreal-app.firebaseapp.com",
  projectId: "astreal-app",
  storageBucket: "astreal-app.appspot.com",
  messagingSenderId: "470077449550",
  appId: "1:470077449550:web:your-app-id"
};

// 檢查 Firebase 配置是否正確載入
console.log('🔧 Firebase 配置載入:', {
  projectId: firebaseConfig.projectId,
  messagingSenderId: firebaseConfig.messagingSenderId
});

// Initialize Firebase
firebase.initializeApp(firebaseConfig);

// Initialize Firebase Messaging
const messaging = firebase.messaging();

console.log('🔧 Firebase Messaging Service Worker 已載入');

// Handle background messages
messaging.onBackgroundMessage((payload) => {
  console.log('📨 收到背景推播通知:', payload);

  const notificationTitle = payload.notification?.title || payload.data?.title || '新通知';
  const notificationOptions = {
    body: payload.notification?.body || payload.data?.body || '',
    icon: payload.notification?.icon || payload.data?.icon || '/icons/Icon-192.png',
    badge: '/icons/Icon-192.png',
    image: payload.notification?.image || payload.data?.image,
    data: {
      ...payload.data,
      click_action: payload.notification?.click_action || payload.data?.click_action || '/',
      url: payload.data?.actionUrl || '/'
    },
    actions: payload.data?.actions ? JSON.parse(payload.data.actions) : [],
    tag: payload.data?.tag || 'astreal-notification',
    requireInteraction: payload.data?.requireInteraction === 'true',
    silent: payload.data?.silent === 'true',
    timestamp: Date.now(),
    vibrate: [200, 100, 200]
  };

  // 顯示通知
  return self.registration.showNotification(notificationTitle, notificationOptions);
});

// Handle notification click
self.addEventListener('notificationclick', (event) => {
  console.log('🖱️ 用戶點擊通知:', event.notification);

  event.notification.close();

  const clickAction = event.notification.data?.click_action || event.notification.data?.url || '/';
  
  event.waitUntil(
    clients.matchAll({ type: 'window', includeUncontrolled: true }).then((clientList) => {
      // 檢查是否已有打開的窗口
      for (const client of clientList) {
        if (client.url.includes(self.location.origin) && 'focus' in client) {
          // 如果有打開的窗口，聚焦並導航
          client.focus();
          client.postMessage({
            type: 'NOTIFICATION_CLICKED',
            data: event.notification.data,
            url: clickAction
          });
          return;
        }
      }
      
      // 如果沒有打開的窗口，開啟新窗口
      if (clients.openWindow) {
        const targetUrl = clickAction.startsWith('http') 
          ? clickAction 
          : `${self.location.origin}${clickAction}`;
        return clients.openWindow(targetUrl);
      }
    })
  );
});

// Handle notification close
self.addEventListener('notificationclose', (event) => {
  console.log('❌ 用戶關閉通知:', event.notification);
  
  // 可以在這裡記錄通知關閉事件
  // 例如發送分析數據
});

// Handle push event (備用處理)
self.addEventListener('push', (event) => {
  console.log('📨 收到 Push 事件:', event);
  
  if (event.data) {
    try {
      const payload = event.data.json();
      console.log('Push 數據:', payload);
      
      // 如果 Firebase Messaging 沒有處理，手動處理
      if (!payload.notification && !payload.data) {
        const notificationTitle = '新通知';
        const notificationOptions = {
          body: '您有新的通知',
          icon: '/icons/Icon-192.png',
          badge: '/icons/Icon-192.png'
        };
        
        event.waitUntil(
          self.registration.showNotification(notificationTitle, notificationOptions)
        );
      }
    } catch (error) {
      console.error('❌ 處理 Push 事件失敗:', error);
    }
  }
});

console.log('✅ Firebase Messaging Service Worker 初始化完成');
