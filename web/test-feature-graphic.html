<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Play 封面圖測試 - AstReal</title>
    <style>
        body {
            font-family: 'Noto Sans TC', sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #3F51B5;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
            margin: 30px 0;
        }
        
        .test-item {
            border: 2px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
        }
        
        .test-item h3 {
            color: #3F51B5;
            margin-bottom: 15px;
        }
        
        canvas {
            border: 1px solid #ccc;
            border-radius: 4px;
            max-width: 100%;
            height: auto;
        }
        
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        
        button {
            background: #3F51B5;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 0 5px;
        }
        
        button:hover {
            background: #303F9F;
        }
        
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            font-weight: bold;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.loading {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@300;400;500;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <h1>AstReal Google Play 封面圖測試</h1>
        
        <div id="iconStatus" class="status loading">正在載入應用程式圖標...</div>
        
        <div class="controls">
            <button onclick="testAllVersions()">測試所有版本</button>
            <button onclick="downloadAll()">下載所有版本</button>
        </div>
        
        <div class="test-grid">
            <div class="test-item">
                <h3>標準版本 (Standard)</h3>
                <canvas id="standardCanvas" width="512" height="250"></canvas>
                <div class="controls">
                    <button onclick="generateVersion('standard')">生成</button>
                    <button onclick="downloadVersion('standard')">下載</button>
                </div>
            </div>
            
            <div class="test-item">
                <h3>簡約版本 (Minimal)</h3>
                <canvas id="minimalCanvas" width="512" height="250"></canvas>
                <div class="controls">
                    <button onclick="generateVersion('minimal')">生成</button>
                    <button onclick="downloadVersion('minimal')">下載</button>
                </div>
            </div>
            
            <div class="test-item">
                <h3>高級版本 (Premium)</h3>
                <canvas id="premiumCanvas" width="512" height="250"></canvas>
                <div class="controls">
                    <button onclick="generateVersion('premium')">生成</button>
                    <button onclick="downloadVersion('premium')">下載</button>
                </div>
            </div>
        </div>
        
        <div class="test-item">
            <h3>完整尺寸預覽 (1024x500)</h3>
            <canvas id="fullSizeCanvas" width="1024" height="500" style="max-width: 100%; height: auto;"></canvas>
            <div class="controls">
                <button onclick="generateFullSize('standard')">標準</button>
                <button onclick="generateFullSize('minimal')">簡約</button>
                <button onclick="generateFullSize('premium')">高級</button>
                <button onclick="downloadFullSize()">下載完整尺寸</button>
            </div>
        </div>
    </div>

    <script>
        // 載入應用程式圖標
        let appIcon = null;
        const iconImg = new Image();
        iconImg.onload = function() {
            appIcon = iconImg;
            document.getElementById('iconStatus').className = 'status success';
            document.getElementById('iconStatus').textContent = '✓ 應用程式圖標載入成功';
            testAllVersions();
        };
        iconImg.onerror = function() {
            document.getElementById('iconStatus').className = 'status error';
            document.getElementById('iconStatus').textContent = '✗ 應用程式圖標載入失敗，將使用備用設計';
            testAllVersions();
        };
        iconImg.src = '../assets/images/flutter_launcher_icons.png';
        
        // AstReal 品牌色彩
        const colors = {
            royalIndigo: '#3F51B5',
            solarAmber: '#F5A623',
            indigoSurface: '#303F9F',
            indigoLight: '#7986CB',
            white: '#FFFFFF'
        };
        
        function testAllVersions() {
            generateVersion('standard');
            generateVersion('minimal');
            generateVersion('premium');
            generateFullSize('standard');
        }
        
        function generateVersion(version) {
            const canvas = document.getElementById(version + 'Canvas');
            const ctx = canvas.getContext('2d');
            
            // 縮放比例（預覽版本是原尺寸的一半）
            const scale = 0.5;
            const originalWidth = 1024;
            const originalHeight = 500;
            
            // 清除畫布
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 縮放上下文
            ctx.save();
            ctx.scale(scale, scale);
            
            // 繪製內容（使用原始尺寸邏輯）
            drawFeatureGraphic(ctx, originalWidth, originalHeight, version);
            
            ctx.restore();
        }
        
        function generateFullSize(version) {
            const canvas = document.getElementById('fullSizeCanvas');
            const ctx = canvas.getContext('2d');
            
            // 清除畫布
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 繪製內容
            drawFeatureGraphic(ctx, canvas.width, canvas.height, version);
        }
        
        function drawFeatureGraphic(ctx, width, height, version) {
            // 安全區域計算（中間80%）
            const safeArea = {
                x: width * 0.1,
                y: height * 0.1,
                width: width * 0.8,
                height: height * 0.8
            };
            
            // 繪製背景漸變
            const gradient = ctx.createLinearGradient(0, 0, width, height);
            gradient.addColorStop(0, colors.royalIndigo);
            gradient.addColorStop(0.6, colors.indigoSurface);
            gradient.addColorStop(1, '#2A3F8F');
            
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, width, height);
            
            // 繪製裝飾元素（除了簡約版本）
            if (version !== 'minimal') {
                drawDecorations(ctx, width, height);
            }
            
            // 繪製主要內容
            drawMainContent(ctx, width, height, safeArea, version);
            
            // 繪製星座符號（標準和高級版本）
            if (version === 'premium' || version === 'standard') {
                drawZodiacSymbols(ctx, width, height);
            }
            
            // 高級版本的額外效果
            if (version === 'premium') {
                drawPremiumEffects(ctx, width, height);
            }
        }
        
        function drawDecorations(ctx, width, height) {
            // 繪製星空點點
            ctx.fillStyle = colors.white + '60';
            for (let i = 0; i < 30; i++) {
                const x = Math.random() * width;
                const y = Math.random() * height;
                const size = Math.random() * 2 + 1;
                
                ctx.beginPath();
                ctx.arc(x, y, size, 0, Math.PI * 2);
                ctx.fill();
            }
        }
        
        function drawMainContent(ctx, width, height, safeArea, version) {
            const centerX = safeArea.x + safeArea.width / 2;
            const centerY = safeArea.y + safeArea.height / 2;
            
            if (version === 'minimal') {
                drawMinimalContent(ctx, centerX, centerY);
                return;
            }
            
            // 繪製應用程式圖標
            const logoSize = 120;
            const logoX = centerX - 220;
            const logoY = centerY;
            
            if (appIcon) {
                drawAppIcon(ctx, logoX, logoY, logoSize);
            } else {
                drawFallbackLogo(ctx, logoX, logoY, logoSize);
            }
            
            // 繪製文字內容
            drawTextContent(ctx, centerX, centerY, logoX, logoSize);
        }
        
        function drawAppIcon(ctx, x, y, size) {
            // 繪製圓形背景
            const bgGradient = ctx.createRadialGradient(x, y, 0, x, y, size + 10);
            bgGradient.addColorStop(0, colors.royalIndigo);
            bgGradient.addColorStop(1, colors.indigoSurface);
            
            ctx.fillStyle = bgGradient;
            ctx.beginPath();
            ctx.arc(x, y, size + 10, 0, Math.PI * 2);
            ctx.fill();
            
            // 裁切為圓形並繪製圖標
            ctx.save();
            ctx.beginPath();
            ctx.arc(x, y, size, 0, Math.PI * 2);
            ctx.clip();
            
            ctx.drawImage(appIcon, x - size, y - size, size * 2, size * 2);
            ctx.restore();
            
            // 添加邊框
            ctx.strokeStyle = colors.solarAmber;
            ctx.lineWidth = 4;
            ctx.beginPath();
            ctx.arc(x, y, size, 0, Math.PI * 2);
            ctx.stroke();
        }
        
        function drawFallbackLogo(ctx, x, y, size) {
            // 備用設計
            const logoGradient = ctx.createRadialGradient(x, y, 0, x, y, size);
            logoGradient.addColorStop(0, colors.solarAmber);
            logoGradient.addColorStop(1, '#E6941A');
            
            ctx.fillStyle = logoGradient;
            ctx.beginPath();
            ctx.arc(x, y, size, 0, Math.PI * 2);
            ctx.fill();
            
            ctx.fillStyle = colors.white;
            ctx.font = `bold ${size * 0.8}px serif`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('✦', x, y);
        }
        
        function drawTextContent(ctx, centerX, centerY, logoX, logoSize) {
            // 文字從圖標右側開始
            const textStartX = logoX + logoSize + 80;

            // 主標題
            ctx.fillStyle = colors.white;
            ctx.font = 'bold 96px "Arial", sans-serif';
            ctx.textAlign = 'left';
            ctx.textBaseline = 'middle';
            ctx.shadowColor = 'rgba(0,0,0,0.3)';
            ctx.shadowBlur = 8;
            ctx.shadowOffsetX = 4;
            ctx.shadowOffsetY = 4;
            ctx.fillText('AstReal', textStartX, centerY - 30);

            // 副標題
            ctx.fillStyle = colors.solarAmber;
            ctx.font = '32px "Arial", sans-serif';
            ctx.shadowBlur = 4;
            ctx.shadowOffsetX = 2;
            ctx.shadowOffsetY = 2;
            ctx.fillText('Professional Astrology App', textStartX, centerY + 30);

            // 特色描述
            ctx.fillStyle = colors.white + 'E6';
            ctx.font = '24px "Arial", sans-serif';
            ctx.shadowBlur = 2;
            ctx.shadowOffsetX = 1;
            ctx.shadowOffsetY = 1;
            ctx.fillText('Birth Chart • Interpretation • Multiple Chart Types', textStartX, centerY + 75);

            // 清除陰影
            ctx.shadowColor = 'transparent';
            ctx.shadowBlur = 0;
            ctx.shadowOffsetX = 0;
            ctx.shadowOffsetY = 0;
        }
        
        function drawMinimalContent(ctx, centerX, centerY) {
            const logoSize = 140;
            const logoY = centerY - 80;

            if (appIcon) {
                drawAppIcon(ctx, centerX, logoY, logoSize);
            } else {
                drawFallbackLogo(ctx, centerX, logoY, logoSize);
            }

            // 主標題（僅英文）
            ctx.fillStyle = colors.white;
            ctx.font = 'bold 110px "Arial", sans-serif';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.shadowColor = 'rgba(0,0,0,0.5)';
            ctx.shadowBlur = 8;
            ctx.shadowOffsetX = 4;
            ctx.shadowOffsetY = 4;
            ctx.fillText('AstReal', centerX, centerY + 60);

            // 副標題（僅英文）
            ctx.fillStyle = colors.solarAmber;
            ctx.font = '38px "Arial", sans-serif';
            ctx.shadowBlur = 4;
            ctx.shadowOffsetX = 2;
            ctx.shadowOffsetY = 2;
            ctx.fillText('Professional Astrology', centerX, centerY + 120);

            ctx.shadowColor = 'transparent';
            ctx.shadowBlur = 0;
            ctx.shadowOffsetX = 0;
            ctx.shadowOffsetY = 0;
        }
        
        function drawZodiacSymbols(ctx, width, height) {
            const symbols = ['♈', '♉', '♊', '♋', '♌', '♍', '♎', '♏', '♐', '♑', '♒', '♓'];
            
            // 在四個角落放置星座符號
            const positions = [
                { x: 80, y: 80 },
                { x: width - 80, y: 80 },
                { x: 80, y: height - 80 },
                { x: width - 80, y: height - 80 }
            ];
            
            ctx.font = '36px serif';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            
            for (let i = 0; i < positions.length; i++) {
                const symbol = symbols[i * 3];
                const pos = positions[i];
                
                ctx.fillStyle = colors.solarAmber + '20';
                ctx.beginPath();
                ctx.arc(pos.x, pos.y, 30, 0, Math.PI * 2);
                ctx.fill();
                
                ctx.fillStyle = colors.solarAmber + '80';
                ctx.fillText(symbol, pos.x, pos.y);
            }
        }
        
        function drawPremiumEffects(ctx, width, height) {
            const centerX = width / 2;
            const centerY = height / 2;
            
            // 放射狀光線
            ctx.strokeStyle = colors.solarAmber + '20';
            ctx.lineWidth = 2;
            
            for (let i = 0; i < 12; i++) {
                const angle = (i / 12) * Math.PI * 2;
                const startRadius = 100;
                const endRadius = 250;
                
                const startX = centerX + Math.cos(angle) * startRadius;
                const startY = centerY + Math.sin(angle) * startRadius;
                const endX = centerX + Math.cos(angle) * endRadius;
                const endY = centerY + Math.sin(angle) * endRadius;
                
                ctx.beginPath();
                ctx.moveTo(startX, startY);
                ctx.lineTo(endX, endY);
                ctx.stroke();
            }
        }
        
        function downloadVersion(version) {
            const canvas = document.getElementById(version + 'Canvas');
            downloadCanvas(canvas, `astreal-google-play-${version}.png`);
        }
        
        function downloadFullSize() {
            const canvas = document.getElementById('fullSizeCanvas');
            downloadCanvas(canvas, 'astreal-google-play-feature-graphic.png');
        }
        
        function downloadAll() {
            downloadVersion('standard');
            downloadVersion('minimal');
            downloadVersion('premium');
            downloadFullSize();
        }
        
        function downloadCanvas(canvas, filename) {
            canvas.toBlob(function(blob) {
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = filename;
                a.click();
                URL.revokeObjectURL(url);
            }, 'image/png');
        }
    </script>
</body>
</html>
