<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AstReal - Platform 修復測試</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 { color: #3F51B5; text-align: center; }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button {
            background-color: #3F51B5;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 16px;
            width: 100%;
        }
        button:hover { background-color: #303F9F; }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Platform 修復測試</h1>
        
        <div id="status"></div>
        
        <button onclick="testPlatformFix()">測試 Platform 修復</button>
        
        <div id="log" class="log" style="display: none;"></div>
    </div>

    <script>
        let logMessages = [];

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${message}`;
            logMessages.push(logMessage);
            
            const logDiv = document.getElementById('log');
            logDiv.style.display = 'block';
            logDiv.innerHTML = logMessages.join('\n');
            logDiv.scrollTop = logDiv.scrollHeight;
            
            console.log(logMessage);
        }

        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        async function testPlatformFix() {
            try {
                logMessages = [];
                showStatus('🔧 開始測試 Platform 修復...', 'info');
                log('開始 Platform 修復測試');

                // 1. 測試基本的 Web 環境檢查
                log('1. 檢查 Web 環境');
                if (typeof window !== 'undefined') {
                    log('✅ Window 物件存在');
                } else {
                    log('❌ Window 物件不存在');
                    throw new Error('不在瀏覽器環境中');
                }

                // 2. 測試 Flutter Web 是否載入
                log('2. 檢查 Flutter Web');
                if (typeof flutter_bootstrap !== 'undefined' || document.querySelector('script[src*="flutter"]')) {
                    log('✅ Flutter Web 腳本已載入');
                } else {
                    log('⚠️ Flutter Web 腳本可能未載入');
                }

                // 3. 測試 Firebase SDK
                log('3. 檢查 Firebase SDK');
                if (typeof firebase !== 'undefined') {
                    log('✅ Firebase SDK 已載入');
                    log(`📍 Firebase 版本: ${firebase.SDK_VERSION || 'unknown'}`);
                } else {
                    log('❌ Firebase SDK 未載入');
                    throw new Error('Firebase SDK 未載入');
                }

                // 4. 測試 Service Worker 註冊
                log('4. 檢查 Service Worker');
                if ('serviceWorker' in navigator) {
                    log('✅ 瀏覽器支援 Service Worker');
                    
                    try {
                        const registration = await navigator.serviceWorker.register('/firebase-messaging-sw.js');
                        log('✅ Firebase Messaging Service Worker 註冊成功');
                        log(`📍 Scope: ${registration.scope}`);
                    } catch (error) {
                        log(`❌ Service Worker 註冊失敗: ${error.message}`);
                    }
                } else {
                    log('❌ 瀏覽器不支援 Service Worker');
                }

                // 5. 測試 Firebase 初始化
                log('5. 測試 Firebase 初始化');
                try {
                    const firebaseConfig = {
                        apiKey: "AIzaSyBHhYGKdOJBfcWJgJKOHhYGKdOJBfcWJgJ",
                        authDomain: "astreal-app.firebaseapp.com",
                        projectId: "astreal-app",
                        storageBucket: "astreal-app.appspot.com",
                        messagingSenderId: "470077449550",
                        appId: "1:470077449550:web:your-app-id"
                    };

                    if (!firebase.apps.length) {
                        firebase.initializeApp(firebaseConfig);
                        log('✅ Firebase 應用初始化成功');
                    } else {
                        log('✅ Firebase 應用已存在');
                    }

                    const messaging = firebase.messaging();
                    log('✅ Firebase Messaging 實例創建成功');

                } catch (error) {
                    log(`❌ Firebase 初始化失敗: ${error.message}`);
                    throw error;
                }

                // 6. 測試通知權限
                log('6. 測試通知權限');
                if ('Notification' in window) {
                    log('✅ 瀏覽器支援 Notification API');
                    log(`📍 當前權限狀態: ${Notification.permission}`);
                    
                    if (Notification.permission === 'default') {
                        log('🔔 請求通知權限...');
                        const permission = await Notification.requestPermission();
                        log(`📍 權限請求結果: ${permission}`);
                    }
                } else {
                    log('❌ 瀏覽器不支援 Notification API');
                }

                // 7. 測試 FCM Token 獲取（這是關鍵測試）
                log('7. 測試 FCM Token 獲取');
                try {
                    const messaging = firebase.messaging();
                    const vapidKey = "BDXI78bC3YqFM5iqf4OmRirP8KxZ_KC5XUv2Hzl2_PmARvsB1PXjAbwb23y7dl53fckVN4PJKi5nkz1sokpYtRs";
                    
                    log('🔑 嘗試獲取 FCM Token...');
                    const token = await messaging.getToken({ vapidKey: vapidKey });
                    
                    if (token) {
                        log('✅ FCM Token 獲取成功！');
                        log(`📍 Token (前20字符): ${token.substring(0, 20)}...`);
                        showStatus('🎉 Platform 修復測試成功！FCM Token 獲取正常', 'success');
                    } else {
                        log('❌ FCM Token 獲取失敗 - 返回空值');
                        showStatus('⚠️ FCM Token 獲取失敗，但沒有 Platform 錯誤', 'error');
                    }
                } catch (error) {
                    log(`❌ FCM Token 獲取失敗: ${error.message}`);
                    
                    if (error.message.includes('Platform._operatingSystem')) {
                        log('💥 仍然存在 Platform._operatingSystem 錯誤！');
                        showStatus('❌ Platform 修復失敗！仍然存在 Platform 錯誤', 'error');
                    } else {
                        log('✅ 沒有 Platform 相關錯誤，可能是其他問題');
                        showStatus('✅ Platform 修復成功！錯誤不是 Platform 相關', 'success');
                    }
                    
                    throw error;
                }

                log('🎉 所有測試完成！');
                
            } catch (error) {
                log(`💥 測試失敗: ${error.message}`);
                console.error('測試錯誤:', error);
                
                if (!document.getElementById('status').innerHTML.includes('Platform 修復')) {
                    showStatus(`❌ 測試失敗: ${error.message}`, 'error');
                }
            }
        }

        // 頁面載入時顯示初始狀態
        window.addEventListener('load', () => {
            showStatus('👋 點擊按鈕開始測試 Platform 修復是否成功', 'info');
            log('頁面載入完成，準備測試');
        });

        // 監聽 Flutter 應用的錯誤
        window.addEventListener('error', (event) => {
            log(`🚨 全域錯誤: ${event.error?.message || event.message}`);
            if (event.error?.message?.includes('Platform._operatingSystem')) {
                log('💥 檢測到 Platform._operatingSystem 錯誤！');
                showStatus('❌ 檢測到 Platform 錯誤！修復可能不完整', 'error');
            }
        });
    </script>
</body>
</html>
