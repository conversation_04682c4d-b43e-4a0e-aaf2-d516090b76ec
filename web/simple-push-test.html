<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AstReal - 簡單推播測試</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 { color: #3F51B5; text-align: center; }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button {
            background-color: #3F51B5;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 16px;
            width: 100%;
        }
        button:hover { background-color: #303F9F; }
        .token { 
            background: #f8f9fa; 
            padding: 10px; 
            border-radius: 5px; 
            word-break: break-all; 
            font-family: monospace; 
            font-size: 12px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔔 AstReal 簡單推播測試</h1>
        
        <div id="status"></div>
        
        <button onclick="testPushNotification()">開始測試推播通知</button>
        
        <div id="token-container" style="display: none;">
            <h3>FCM Token:</h3>
            <div id="token" class="token"></div>
            <button onclick="copyToken()">複製 Token</button>
        </div>
    </div>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/10.7.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.0/firebase-messaging-compat.js"></script>

    <script>
        // Firebase 配置
        const firebaseConfig = {
            apiKey: "AIzaSyBHhYGKdOJBfcWJgJKOHhYGKdOJBfcWJgJ",
            authDomain: "astreal-app.firebaseapp.com",
            projectId: "astreal-app",
            storageBucket: "astreal-app.appspot.com",
            messagingSenderId: "470077449550",
            appId: "1:470077449550:web:your-app-id"
        };

        const vapidKey = "BDXI78bC3YqFM5iqf4OmRirP8KxZ_KC5XUv2Hzl2_PmARvsB1PXjAbwb23y7dl53fckVN4PJKi5nkz1sokpYtRs";

        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        async function testPushNotification() {
            try {
                showStatus('🔧 開始測試...', 'info');

                // 1. 檢查瀏覽器支援
                if (!('serviceWorker' in navigator) || !('Notification' in window) || !('PushManager' in window)) {
                    throw new Error('瀏覽器不支援推播通知功能');
                }
                showStatus('✅ 瀏覽器支援檢查通過', 'success');

                // 2. 註冊 Service Worker
                const registration = await navigator.serviceWorker.register('/firebase-messaging-sw.js');
                console.log('Service Worker 註冊成功:', registration);
                showStatus('✅ Service Worker 註冊成功', 'success');

                // 3. 初始化 Firebase
                if (!firebase.apps.length) {
                    firebase.initializeApp(firebaseConfig);
                }
                const messaging = firebase.messaging();
                showStatus('✅ Firebase 初始化成功', 'success');

                // 4. 請求通知權限
                const permission = await Notification.requestPermission();
                if (permission !== 'granted') {
                    throw new Error('通知權限被拒絕');
                }
                showStatus('✅ 通知權限已授權', 'success');

                // 5. 獲取 FCM Token
                const token = await messaging.getToken({ vapidKey: vapidKey });
                if (!token) {
                    throw new Error('無法獲取 FCM Token');
                }
                
                console.log('FCM Token:', token);
                document.getElementById('token').textContent = token;
                document.getElementById('token-container').style.display = 'block';
                showStatus('✅ FCM Token 獲取成功！可以使用此 Token 發送測試通知', 'success');

                // 6. 設定前景通知監聽
                messaging.onMessage((payload) => {
                    console.log('收到前景通知:', payload);
                    showStatus(`📨 收到通知: ${payload.notification?.title}`, 'info');
                    
                    // 顯示本地通知
                    new Notification(payload.notification?.title || '新通知', {
                        body: payload.notification?.body || '',
                        icon: '/icons/Icon-192.png'
                    });
                });

                // 7. 發送測試本地通知
                const testNotification = new Notification('AstReal 測試通知', {
                    body: '推播通知系統測試成功！',
                    icon: '/icons/Icon-192.png'
                });

                testNotification.onclick = function() {
                    console.log('測試通知被點擊');
                    testNotification.close();
                };

            } catch (error) {
                console.error('測試失敗:', error);
                showStatus(`❌ 測試失敗: ${error.message}`, 'error');
            }
        }

        function copyToken() {
            const tokenText = document.getElementById('token').textContent;
            navigator.clipboard.writeText(tokenText).then(() => {
                showStatus('✅ Token 已複製到剪貼簿', 'success');
            }).catch(err => {
                console.error('複製失敗:', err);
                showStatus('❌ 複製失敗，請手動複製', 'error');
            });
        }

        // 頁面載入時顯示初始狀態
        window.addEventListener('load', () => {
            showStatus('👋 點擊按鈕開始測試推播通知功能', 'info');
        });
    </script>
</body>
</html>
