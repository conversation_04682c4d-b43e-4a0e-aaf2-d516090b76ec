// 版本檢查和自動更新腳本

(function() {
  'use strict';
  
  const VERSION_CHECK_INTERVAL = 60000; // 60秒檢查一次，減少頻率
  const CURRENT_VERSION = '{{BUILD_VERSION}}';
  let isUpdating = false; // 防止重複更新
  let hasShownNotification = false; // 防止重複顯示通知

  // 檢查是否支援 Service Worker
  if ('serviceWorker' in navigator) {
    // 註冊 Service Worker
    navigator.serviceWorker.register('/sw_custom.js')
      .then((registration) => {
        console.log('Service Worker registered:', registration);
        
        // 監聽 Service Worker 更新
        registration.addEventListener('updatefound', () => {
          const newWorker = registration.installing;
          newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
              // 有新版本可用
              showUpdateNotification();
            }
          });
        });
      })
      .catch((error) => {
        console.log('Service Worker registration failed:', error);
      });
  }
  
  // 顯示更新通知
  function showUpdateNotification() {
    // 防止重複顯示通知
    if (hasShownNotification || document.getElementById('update-notification')) {
      return;
    }
    hasShownNotification = true;

    const notification = document.createElement('div');
    notification.id = 'update-notification';
    notification.innerHTML = `
      <div style="
        position: fixed;
        top: 20px;
        right: 20px;
        background: #3F51B5;
        color: white;
        padding: 16px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        z-index: 10000;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        font-size: 14px;
        max-width: 300px;
      ">
        <div style="margin-bottom: 12px;">
          <strong>🚀 新版本可用！</strong>
        </div>
        <div style="margin-bottom: 16px; opacity: 0.9;">
          點擊重新載入以獲得最新功能
        </div>
        <div style="display: flex; gap: 8px;">
          <button id="update-now" style="
            background: #F5A623;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 500;
          ">立即更新</button>
          <button id="update-later" style="
            background: transparent;
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
          ">稍後提醒</button>
        </div>
      </div>
    `;
    
    document.body.appendChild(notification);
    
    // 綁定事件
    document.getElementById('update-now').addEventListener('click', () => {
      updateApp();
    });
    
    document.getElementById('update-later').addEventListener('click', () => {
      notification.remove();
      hasShownNotification = false; // 重置標記
      // 5分鐘後再次提醒
      setTimeout(() => {
        hasShownNotification = false;
        showUpdateNotification();
      }, 300000);
    });
  }
  
  // 更新應用
  function updateApp() {
    if (isUpdating) {
      return; // 防止重複更新
    }
    isUpdating = true;

    // 顯示更新中狀態
    const notification = document.getElementById('update-notification');
    if (notification) {
      notification.innerHTML = `
        <div style="
          position: fixed;
          top: 20px;
          right: 20px;
          background: #3F51B5;
          color: white;
          padding: 16px 20px;
          border-radius: 8px;
          box-shadow: 0 4px 12px rgba(0,0,0,0.3);
          z-index: 10000;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          font-size: 14px;
          max-width: 300px;
          text-align: center;
        ">
          <div style="margin-bottom: 12px;">
            🔄 正在更新...
          </div>
          <div style="opacity: 0.9;">
            請稍候，即將重新載入
          </div>
        </div>
      `;
    }

    // 延遲執行更新，讓用戶看到更新中狀態
    setTimeout(() => {
      // 清除所有快取
      if ('caches' in window) {
        caches.keys().then((cacheNames) => {
          return Promise.all(
            cacheNames.map((cacheName) => caches.delete(cacheName))
          );
        }).then(() => {
          // 清除 localStorage 和 sessionStorage
          try {
            localStorage.clear();
            sessionStorage.clear();
          } catch (e) {
            console.log('清除 storage 失敗:', e);
          }

          // 強制重新載入，使用時間戳避免快取
          window.location.href = window.location.href + '?t=' + Date.now();
        }).catch(() => {
          // 如果清除快取失敗，直接重新載入
          window.location.href = window.location.href + '?t=' + Date.now();
        });
      } else {
        // 如果不支援 Cache API，直接重新載入
        window.location.href = window.location.href + '?t=' + Date.now();
      }
    }, 1000);
  }
  
  // 定期檢查版本更新
  function checkForUpdates() {
    // 如果已經在更新中或已經顯示通知，跳過檢查
    if (isUpdating || hasShownNotification) {
      return;
    }

    fetch('/version.json?t=' + Date.now(), {
      cache: 'no-cache',
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    })
    .then(response => {
      if (!response.ok) {
        throw new Error('Network response was not ok');
      }
      return response.json();
    })
    .then(data => {
      console.log('Current version:', CURRENT_VERSION);
      console.log('Server version:', data.version);

      if (data.version && data.version !== CURRENT_VERSION && !hasShownNotification) {
        console.log('New version available:', data.version);
        showUpdateNotification();
      }
    })
    .catch(error => {
      console.log('Version check failed:', error);
      // 版本檢查失敗時不顯示錯誤，靜默處理
    });
  }
  
  // 頁面載入完成後開始檢查
  function startVersionCheck() {
    // 首次檢查延遲10秒，給應用足夠時間載入
    setTimeout(() => {
      if (!isUpdating) {
        checkForUpdates();
      }
    }, 10000);

    // 定期檢查
    setInterval(() => {
      if (!isUpdating) {
        checkForUpdates();
      }
    }, VERSION_CHECK_INTERVAL);
  }

  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', startVersionCheck);
  } else {
    startVersionCheck();
  }
  
  // 監聽 Service Worker 控制器變化
  if ('serviceWorker' in navigator) {
    navigator.serviceWorker.addEventListener('controllerchange', () => {
      // Service Worker 已更新，重新載入頁面
      window.location.reload();
    });
  }
  
  // 頁面可見性變化時檢查更新
  document.addEventListener('visibilitychange', () => {
    if (!document.hidden) {
      checkForUpdates();
    }
  });
  
})();
