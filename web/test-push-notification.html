<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AstReal - Web 推播通知測試</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #3F51B5;
            text-align: center;
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background-color: #3F51B5;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 16px;
        }
        button:hover { background-color: #303F9F; }
        button:disabled { background-color: #ccc; cursor: not-allowed; }
        .token-display {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            word-break: break-all;
            font-family: monospace;
            font-size: 12px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔔 AstReal Web 推播通知測試</h1>
        
        <div class="test-section">
            <h3>1. 瀏覽器支援檢查</h3>
            <div id="browser-support"></div>
        </div>

        <div class="test-section">
            <h3>2. Service Worker 狀態</h3>
            <div id="sw-status"></div>
            <button onclick="registerServiceWorker()">註冊 Service Worker</button>
        </div>

        <div class="test-section">
            <h3>3. Firebase 初始化</h3>
            <div id="firebase-status"></div>
            <button onclick="initializeFirebase()">初始化 Firebase</button>
        </div>

        <div class="test-section">
            <h3>4. 通知權限</h3>
            <div id="permission-status"></div>
            <button onclick="requestNotificationPermission()">請求通知權限</button>
        </div>

        <div class="test-section">
            <h3>5. FCM Token</h3>
            <div id="token-status"></div>
            <div id="token-display" class="token-display" style="display: none;"></div>
            <button onclick="getFCMToken()">獲取 FCM Token</button>
        </div>

        <div class="test-section">
            <h3>6. 測試通知</h3>
            <div id="test-notification-status"></div>
            <button onclick="sendTestNotification()">發送測試通知</button>
        </div>
    </div>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/10.7.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.0/firebase-messaging-compat.js"></script>

    <script>
        // Firebase 配置 - 請替換為您的實際配置
        const firebaseConfig = {
            apiKey: "AIzaSyBHhYGKdOJBfcWJgJKOHhYGKdOJBfcWJgJ", // 請替換
            authDomain: "astreal-app.firebaseapp.com",
            projectId: "astreal-app",
            storageBucket: "astreal-app.appspot.com",
            messagingSenderId: "470077449550",
            appId: "1:470077449550:web:your-app-id" // 請替換
        };

        console.log('🔧 測試頁面 Firebase 配置:', firebaseConfig);

        const vapidKey = "BDXI78bC3YqFM5iqf4OmRirP8KxZ_KC5XUv2Hzl2_PmARvsB1PXjAbwb23y7dl53fckVN4PJKi5nkz1sokpYtRs";

        let messaging = null;

        // 頁面載入時執行檢查
        window.addEventListener('load', function() {
            checkBrowserSupport();
            checkServiceWorkerStatus();
        });

        function showStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        function checkBrowserSupport() {
            let support = [];
            let issues = [];

            if ('serviceWorker' in navigator) {
                support.push('✅ Service Worker 支援');
            } else {
                issues.push('❌ Service Worker 不支援');
            }

            if ('Notification' in window) {
                support.push('✅ Notification API 支援');
            } else {
                issues.push('❌ Notification API 不支援');
            }

            if ('PushManager' in window) {
                support.push('✅ Push API 支援');
            } else {
                issues.push('❌ Push API 不支援');
            }

            const message = support.join('<br>') + (issues.length > 0 ? '<br>' + issues.join('<br>') : '');
            const type = issues.length > 0 ? 'error' : 'success';
            showStatus('browser-support', message, type);
        }

        function checkServiceWorkerStatus() {
            if ('serviceWorker' in navigator) {
                navigator.serviceWorker.getRegistration('/firebase-messaging-sw.js')
                    .then(registration => {
                        if (registration) {
                            showStatus('sw-status', '✅ Firebase Messaging Service Worker 已註冊', 'success');
                        } else {
                            showStatus('sw-status', '⚠️ Firebase Messaging Service Worker 未註冊', 'warning');
                        }
                    })
                    .catch(error => {
                        showStatus('sw-status', `❌ 檢查 Service Worker 失敗: ${error.message}`, 'error');
                    });
            } else {
                showStatus('sw-status', '❌ 瀏覽器不支援 Service Worker', 'error');
            }
        }

        function registerServiceWorker() {
            if ('serviceWorker' in navigator) {
                navigator.serviceWorker.register('/firebase-messaging-sw.js')
                    .then(registration => {
                        console.log('Service Worker 註冊成功:', registration);
                        showStatus('sw-status', '✅ Firebase Messaging Service Worker 註冊成功', 'success');
                    })
                    .catch(error => {
                        console.error('Service Worker 註冊失敗:', error);
                        showStatus('sw-status', `❌ Service Worker 註冊失敗: ${error.message}`, 'error');
                    });
            } else {
                showStatus('sw-status', '❌ 瀏覽器不支援 Service Worker', 'error');
            }
        }

        function initializeFirebase() {
            try {
                if (!firebase.apps.length) {
                    firebase.initializeApp(firebaseConfig);
                }
                messaging = firebase.messaging();
                showStatus('firebase-status', '✅ Firebase 初始化成功', 'success');
            } catch (error) {
                console.error('Firebase 初始化失敗:', error);
                showStatus('firebase-status', `❌ Firebase 初始化失敗: ${error.message}`, 'error');
            }
        }

        function requestNotificationPermission() {
            if (!messaging) {
                showStatus('permission-status', '❌ 請先初始化 Firebase', 'error');
                return;
            }

            messaging.requestPermission()
                .then(() => {
                    showStatus('permission-status', '✅ 通知權限已授權', 'success');
                })
                .catch(error => {
                    console.error('請求通知權限失敗:', error);
                    showStatus('permission-status', `❌ 請求通知權限失敗: ${error.message}`, 'error');
                });
        }

        function getFCMToken() {
            if (!messaging) {
                showStatus('token-status', '❌ 請先初始化 Firebase', 'error');
                return;
            }

            messaging.getToken({ vapidKey: vapidKey })
                .then(token => {
                    if (token) {
                        console.log('FCM Token:', token);
                        showStatus('token-status', '✅ FCM Token 獲取成功', 'success');
                        document.getElementById('token-display').style.display = 'block';
                        document.getElementById('token-display').textContent = token;
                    } else {
                        showStatus('token-status', '⚠️ 無法獲取 FCM Token - 請檢查權限和 VAPID key', 'warning');
                    }
                })
                .catch(error => {
                    console.error('獲取 FCM Token 失敗:', error);
                    showStatus('token-status', `❌ 獲取 FCM Token 失敗: ${error.message}`, 'error');
                });
        }

        function sendTestNotification() {
            if ('Notification' in window && Notification.permission === 'granted') {
                const notification = new Notification('AstReal 測試通知', {
                    body: '這是一個測試通知，確認瀏覽器通知功能正常',
                    icon: '/icons/Icon-192.png',
                    badge: '/icons/Icon-192.png'
                });

                notification.onclick = function() {
                    console.log('測試通知被點擊');
                    notification.close();
                };

                showStatus('test-notification-status', '✅ 測試通知已發送', 'success');
            } else {
                showStatus('test-notification-status', '❌ 通知權限未授權或瀏覽器不支援', 'error');
            }
        }

        // 監聽前景通知
        if (messaging) {
            messaging.onMessage(payload => {
                console.log('收到前景通知:', payload);
                showStatus('test-notification-status', `📨 收到前景通知: ${payload.notification?.title}`, 'info');
            });
        }
    </script>
</body>
</html>
