// 自定義 Service Worker - 解決 Flutter Web 快取問題

const CACHE_NAME = 'astreal-cache-v{{BUILD_VERSION}}';
const STATIC_CACHE_NAME = 'astreal-static-v{{BUILD_VERSION}}';

// 需要快取的靜態資源
const STATIC_RESOURCES = [
  '/',
  '/index.html',
  '/manifest.json',
  '/favicon.png',
];

// 需要強制更新的資源（不快取）
const NO_CACHE_RESOURCES = [
  '/main.dart.js',
  '/flutter_service_worker.js',
  '/flutter_bootstrap.js',
  '/version.json',
  '/index.html',
];

self.addEventListener('install', (event) => {
  console.log('Service Worker: Installing...');
  
  event.waitUntil(
    caches.open(STATIC_CACHE_NAME).then((cache) => {
      console.log('Service Worker: Caching static resources');
      return cache.addAll(STATIC_RESOURCES);
    })
  );
  
  // 強制激活新的 Service Worker
  self.skipWaiting();
});

self.addEventListener('activate', (event) => {
  console.log('Service Worker: Activating...');
  
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          // 刪除舊版本的快取
          if (cacheName !== CACHE_NAME && cacheName !== STATIC_CACHE_NAME) {
            console.log('Service Worker: Deleting old cache:', cacheName);
            return caches.delete(cacheName);
          }
        })
      );
    })
  );
  
  // 立即控制所有頁面
  self.clients.claim();
});

self.addEventListener('fetch', (event) => {
  const url = new URL(event.request.url);
  
  // 檢查是否為不快取的資源
  const shouldNotCache = NO_CACHE_RESOURCES.some(resource => 
    url.pathname.includes(resource)
  );
  
  if (shouldNotCache) {
    // 強制從網路獲取，不使用快取
    event.respondWith(
      fetch(event.request, {
        cache: 'no-cache',
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      }).catch(() => {
        // 網路失敗時的後備處理
        return new Response('Network error', { status: 503 });
      })
    );
    return;
  }
  
  // 對於其他資源，使用網路優先策略
  event.respondWith(
    fetch(event.request)
      .then((response) => {
        // 如果網路請求成功，更新快取
        if (response.status === 200) {
          const responseClone = response.clone();
          caches.open(CACHE_NAME).then((cache) => {
            cache.put(event.request, responseClone);
          });
        }
        return response;
      })
      .catch(() => {
        // 網路失敗時，嘗試從快取獲取
        return caches.match(event.request).then((cachedResponse) => {
          if (cachedResponse) {
            return cachedResponse;
          }
          // 如果快取中也沒有，返回離線頁面或錯誤
          return new Response('Offline', { status: 503 });
        });
      })
  );
});

// 監聽來自主線程的消息
self.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
  
  if (event.data && event.data.type === 'CLEAR_CACHE') {
    // 清除所有快取
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => caches.delete(cacheName))
      );
    }).then(() => {
      event.ports[0].postMessage({ success: true });
    });
  }
});

// 檢查更新
self.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'CHECK_UPDATE') {
    // 檢查是否有新版本
    fetch('/version.json', { cache: 'no-cache' })
      .then(response => response.json())
      .then(data => {
        const currentVersion = '{{BUILD_VERSION}}';
        if (data.version !== currentVersion) {
          // 通知主線程有新版本
          event.ports[0].postMessage({ 
            hasUpdate: true, 
            newVersion: data.version 
          });
        } else {
          event.ports[0].postMessage({ hasUpdate: false });
        }
      })
      .catch(() => {
        event.ports[0].postMessage({ hasUpdate: false });
      });
  }
});
