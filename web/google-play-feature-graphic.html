<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Play 封面圖生成器 - AstReal</title>
    <style>
        body {
            font-family: 'Noto Sans TC', sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 1100px;
        }
        
        h1 {
            color: #3F51B5;
            margin-bottom: 20px;
        }
        
        .specs {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: left;
        }
        
        canvas {
            border: 2px solid #ddd;
            border-radius: 8px;
            margin: 20px 0;
            max-width: 100%;
            height: auto;
        }
        
        .controls {
            margin: 20px 0;
        }
        
        button {
            background: #3F51B5;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 0 10px;
        }
        
        button:hover {
            background: #303F9F;
        }
        
        .download-link {
            display: none;
            margin-top: 10px;
        }
        
        .download-link a {
            background: #F5A623;
            color: white;
            text-decoration: none;
            padding: 12px 24px;
            border-radius: 6px;
            display: inline-block;
        }
    </style>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@300;400;500;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <h1>AstReal - Google Play 封面圖生成器</h1>
        
        <div class="specs">
            <h3>設計規範</h3>
            <ul>
                <li><strong>尺寸：</strong>1024 x 500 px（Google Play 推薦的封面圖尺寸）</li>
                <li><strong>格式：</strong>PNG，72 dpi</li>
                <li><strong>安全區域：</strong>文字與 Logo 保持在中間 80% 區域內</li>
                <li><strong>配色：</strong>使用 AstReal 品牌色彩（皇家靛藍 #3F51B5、流光黃 #F5A623）</li>
                <li><strong>圖標：</strong>使用實際的 flutter_launcher_icons.png 應用程式圖標</li>
                <li><strong>文字：</strong>純英文設計，避免遮擋圖標，提升國際化質感</li>
                <li><strong>布局：</strong>圖標左側，文字右側，層次分明</li>
            </ul>
        </div>
        
        <canvas id="featureGraphic" width="1024" height="500"></canvas>
        
        <div class="controls">
            <button onclick="generateGraphic('standard')">標準版本</button>
            <button onclick="generateGraphic('minimal')">簡約版本</button>
            <button onclick="generateGraphic('premium')">高級版本</button>
            <button onclick="downloadImage()">下載 PNG</button>
        </div>
        
        <div id="downloadLink" class="download-link">
            <a id="downloadAnchor" download="astreal-google-play-feature-graphic.png">
                點擊下載封面圖
            </a>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('featureGraphic');
        const ctx = canvas.getContext('2d');

        // AstReal 品牌色彩
        const colors = {
            royalIndigo: '#3F51B5',
            solarAmber: '#F5A623',
            indigoSurface: '#303F9F',
            indigoLight: '#7986CB',
            white: '#FFFFFF'
        };

        // 安全區域計算（中間80%）
        const safeArea = {
            x: canvas.width * 0.1,
            y: canvas.height * 0.1,
            width: canvas.width * 0.8,
            height: canvas.height * 0.8
        };

        // 載入實際的應用程式圖標
        let appIcon = null;
        const iconImg = new Image();
        iconImg.onload = function() {
            appIcon = iconImg;
            generateGraphic('standard'); // 圖標載入後重新生成
        };
        iconImg.src = '../assets/images/flutter_launcher_icons.png';
        
        let currentVersion = 'standard';

        function generateGraphic(version = 'standard') {
            currentVersion = version;

            // 清除畫布
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 繪製背景漸變
            drawBackground(version);

            // 繪製裝飾元素
            if (version !== 'minimal') {
                drawDecorations(version);
            }

            // 繪製主要內容
            drawMainContent(version);

            // 繪製星座符號
            if (version === 'premium' || version === 'standard') {
                drawZodiacSymbols(version);
            }

            // 高級版本的額外效果
            if (version === 'premium') {
                drawPremiumEffects();
            }
        }
        
        function drawBackground(version = 'standard') {
            // 深度漸變背景
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, '#1A237E');  // 深靛藍
            gradient.addColorStop(0.3, colors.royalIndigo);
            gradient.addColorStop(0.7, colors.indigoSurface);
            gradient.addColorStop(1, '#0D1B3C');  // 極深藍

            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // 多層光暈效果
            // 主光暈
            const mainGlow = ctx.createRadialGradient(
                canvas.width * 0.75, canvas.height * 0.25, 0,
                canvas.width * 0.75, canvas.height * 0.25, 200
            );
            mainGlow.addColorStop(0, colors.solarAmber + '30');
            mainGlow.addColorStop(0.5, colors.solarAmber + '15');
            mainGlow.addColorStop(1, colors.solarAmber + '00');

            ctx.fillStyle = mainGlow;
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // 次要光暈
            const secondaryGlow = ctx.createRadialGradient(
                canvas.width * 0.2, canvas.height * 0.8, 0,
                canvas.width * 0.2, canvas.height * 0.8, 150
            );
            secondaryGlow.addColorStop(0, colors.indigoLight + '20');
            secondaryGlow.addColorStop(1, colors.indigoLight + '00');

            ctx.fillStyle = secondaryGlow;
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // 添加微妙的紋理效果
            if (version === 'premium') {
                drawTextureOverlay();
            }
        }
        
        function drawDecorations(version = 'standard') {
            // 精緻的星空效果
            drawStarField();

            // 幾何裝飾元素
            drawGeometricElements();

            // 光線效果
            drawLightRays();
        }

        function drawStarField() {
            // 多層次星空
            const starLayers = [
                { count: 30, sizeRange: [1, 2], opacity: '80' },
                { count: 20, sizeRange: [2, 3], opacity: '60' },
                { count: 10, sizeRange: [3, 4], opacity: '40' }
            ];

            starLayers.forEach(layer => {
                for (let i = 0; i < layer.count; i++) {
                    const x = Math.random() * canvas.width;
                    const y = Math.random() * canvas.height;
                    const size = Math.random() * (layer.sizeRange[1] - layer.sizeRange[0]) + layer.sizeRange[0];

                    // 星星光暈
                    const starGlow = ctx.createRadialGradient(x, y, 0, x, y, size * 3);
                    starGlow.addColorStop(0, colors.white + layer.opacity);
                    starGlow.addColorStop(1, colors.white + '00');

                    ctx.fillStyle = starGlow;
                    ctx.beginPath();
                    ctx.arc(x, y, size * 3, 0, Math.PI * 2);
                    ctx.fill();

                    // 星星核心
                    ctx.fillStyle = colors.white + 'FF';
                    ctx.beginPath();
                    ctx.arc(x, y, size, 0, Math.PI * 2);
                    ctx.fill();
                }
            });
        }

        function drawGeometricElements() {
            // 精緻的幾何線條
            ctx.strokeStyle = colors.solarAmber + '25';
            ctx.lineWidth = 2;

            // 左上角優雅弧線
            ctx.beginPath();
            ctx.arc(-50, -50, 200, 0, Math.PI / 2);
            ctx.stroke();

            ctx.beginPath();
            ctx.arc(-30, -30, 160, 0, Math.PI / 2);
            ctx.stroke();

            // 右下角對稱弧線
            ctx.beginPath();
            ctx.arc(canvas.width + 50, canvas.height + 50, 180, Math.PI, Math.PI * 1.5);
            ctx.stroke();

            ctx.beginPath();
            ctx.arc(canvas.width + 30, canvas.height + 30, 140, Math.PI, Math.PI * 1.5);
            ctx.stroke();
        }

        function drawLightRays() {
            // 微妙的光線效果
            ctx.strokeStyle = colors.indigoLight + '15';
            ctx.lineWidth = 1;

            // 對角線光線
            for (let i = 0; i < 5; i++) {
                const offset = i * 100;
                ctx.beginPath();
                ctx.moveTo(-50 + offset, canvas.height + 50);
                ctx.lineTo(canvas.width + 50 - offset, -50);
                ctx.stroke();
            }
        }

        function drawTextureOverlay() {
            // 微妙的紋理效果
            const imageData = ctx.createImageData(canvas.width, canvas.height);
            const data = imageData.data;

            for (let i = 0; i < data.length; i += 4) {
                const noise = Math.random() * 10 - 5;
                data[i] = noise;     // Red
                data[i + 1] = noise; // Green
                data[i + 2] = noise; // Blue
                data[i + 3] = 8;     // Alpha (very subtle)
            }

            ctx.putImageData(imageData, 0, 0);
        }

        function drawMainContent(version = 'standard') {
            // 在安全區域內繪製主要內容
            const centerX = safeArea.x + safeArea.width / 2;
            const centerY = safeArea.y + safeArea.height / 2;

            // 簡約版本的特殊處理
            if (version === 'minimal') {
                drawMinimalContent(centerX, centerY);
                return;
            }

            // 繪製應用程式圖標（左側獨立區域）
            const logoSize = 140;
            const logoX = safeArea.x + logoSize + 50;  // 左側安全區域內
            const logoY = centerY;

            if (appIcon) {
                // 多層次光暈效果
                const glowLayers = [
                    { radius: logoSize + 50, opacity: '20', color: colors.solarAmber },
                    { radius: logoSize + 30, opacity: '30', color: colors.solarAmber },
                    { radius: logoSize + 15, opacity: '40', color: colors.indigoLight }
                ];

                glowLayers.forEach(layer => {
                    const glow = ctx.createRadialGradient(
                        logoX, logoY, logoSize,
                        logoX, logoY, layer.radius
                    );
                    glow.addColorStop(0, layer.color + layer.opacity);
                    glow.addColorStop(1, layer.color + '00');

                    ctx.fillStyle = glow;
                    ctx.beginPath();
                    ctx.arc(logoX, logoY, layer.radius, 0, Math.PI * 2);
                    ctx.fill();
                });

                // 深度背景
                const bgGradient = ctx.createRadialGradient(
                    logoX - logoSize * 0.3, logoY - logoSize * 0.3, 0,
                    logoX, logoY, logoSize + 15
                );
                bgGradient.addColorStop(0, '#4A5FBF');
                bgGradient.addColorStop(0.7, colors.royalIndigo);
                bgGradient.addColorStop(1, colors.indigoSurface);

                ctx.fillStyle = bgGradient;
                ctx.beginPath();
                ctx.arc(logoX, logoY, logoSize + 15, 0, Math.PI * 2);
                ctx.fill();

                // 裁切為圓形並繪製實際圖標
                ctx.save();
                ctx.beginPath();
                ctx.arc(logoX, logoY, logoSize, 0, Math.PI * 2);
                ctx.clip();

                // 繪製應用程式圖標
                ctx.drawImage(
                    appIcon,
                    logoX - logoSize,
                    logoY - logoSize,
                    logoSize * 2,
                    logoSize * 2
                );

                ctx.restore();

                // 精緻的多層邊框
                // 外邊框
                ctx.strokeStyle = colors.solarAmber + 'CC';
                ctx.lineWidth = 6;
                ctx.beginPath();
                ctx.arc(logoX, logoY, logoSize + 3, 0, Math.PI * 2);
                ctx.stroke();

                // 內邊框
                ctx.strokeStyle = colors.white + '60';
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.arc(logoX, logoY, logoSize - 2, 0, Math.PI * 2);
                ctx.stroke();

            } else {
                // 如果圖標未載入，使用備用設計
                drawFallbackLogo(logoX, logoY, logoSize);
            }

            // 清除陰影
            ctx.shadowColor = 'transparent';
            ctx.shadowBlur = 0;
            ctx.shadowOffsetX = 0;
            ctx.shadowOffsetY = 0;

            // 繪製主標題 "AstReal"（右側布局，避免遮擋圖標）
            ctx.fillStyle = colors.white;
            ctx.font = 'bold 96px "Arial", sans-serif';
            ctx.textAlign = 'left';
            ctx.textBaseline = 'middle';
            ctx.shadowColor = 'rgba(0,0,0,0.3)';
            ctx.shadowBlur = 8;
            ctx.shadowOffsetX = 4;
            ctx.shadowOffsetY = 4;
            const textStartX = logoX + logoSize + 80;  // 文字從圖標右側開始
            ctx.fillText('AstReal', textStartX, centerY - 30);

            // 繪製副標題（英文）
            ctx.fillStyle = colors.solarAmber;
            ctx.font = '32px "Arial", sans-serif';
            ctx.shadowBlur = 4;
            ctx.shadowOffsetX = 2;
            ctx.shadowOffsetY = 2;
            ctx.fillText('Professional Astrology App', textStartX, centerY + 30);

            // 繪製特色描述（英文）
            ctx.fillStyle = colors.white + 'E6';
            ctx.font = '24px "Arial", sans-serif';
            ctx.shadowBlur = 2;
            ctx.shadowOffsetX = 1;
            ctx.shadowOffsetY = 1;
            ctx.fillText('Birth Chart • Interpretation • Multiple Chart Types', textStartX, centerY + 75);

            // 清除陰影
            ctx.shadowColor = 'transparent';
            ctx.shadowBlur = 0;
            ctx.shadowOffsetX = 0;
            ctx.shadowOffsetY = 0;
        }
        
        function drawZodiacSymbols(version) {
            // 星座符號 - 更微妙和優雅
            const symbols = ['♈', '♉', '♊', '♋', '♌', '♍', '♎', '♏', '♐', '♑', '♒', '♓'];

            // 精緻的角落符號
            const cornerPositions = [
                { x: 120, y: 120 },      // 左上
                { x: canvas.width - 120, y: 120 },  // 右上
                { x: 120, y: canvas.height - 120 }, // 左下
                { x: canvas.width - 120, y: canvas.height - 120 } // 右下
            ];

            ctx.font = '32px serif';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';

            // 在角落放置主要星座符號
            for (let i = 0; i < cornerPositions.length; i++) {
                const symbol = symbols[i * 3]; // 選擇代表性符號
                const pos = cornerPositions[i];

                // 微妙的背景光暈
                const glow = ctx.createRadialGradient(pos.x, pos.y, 0, pos.x, pos.y, 40);
                glow.addColorStop(0, colors.solarAmber + '20');
                glow.addColorStop(1, colors.solarAmber + '00');

                ctx.fillStyle = glow;
                ctx.beginPath();
                ctx.arc(pos.x, pos.y, 40, 0, Math.PI * 2);
                ctx.fill();

                // 繪製符號
                ctx.fillStyle = colors.solarAmber + '70';
                ctx.shadowColor = 'rgba(0,0,0,0.3)';
                ctx.shadowBlur = 2;
                ctx.shadowOffsetX = 1;
                ctx.shadowOffsetY = 1;
                ctx.fillText(symbol, pos.x, pos.y);

                // 清除陰影
                ctx.shadowColor = 'transparent';
                ctx.shadowBlur = 0;
                ctx.shadowOffsetX = 0;
                ctx.shadowOffsetY = 0;
            }
        }

        function drawMinimalContent(centerX, centerY) {
            // 簡約版本：只有Logo和標題，居中對齊

            // 繪製 Logo
            const logoSize = 140;
            const logoY = centerY - 80;

            if (appIcon) {
                // 繪製圓形背景
                const bgGradient = ctx.createRadialGradient(
                    centerX, logoY, 0,
                    centerX, logoY, logoSize + 15
                );
                bgGradient.addColorStop(0, colors.royalIndigo);
                bgGradient.addColorStop(1, colors.indigoSurface);

                ctx.fillStyle = bgGradient;
                ctx.beginPath();
                ctx.arc(centerX, logoY, logoSize + 15, 0, Math.PI * 2);
                ctx.fill();

                // 裁切為圓形並繪製實際圖標
                ctx.save();
                ctx.beginPath();
                ctx.arc(centerX, logoY, logoSize, 0, Math.PI * 2);
                ctx.clip();

                ctx.drawImage(
                    appIcon,
                    centerX - logoSize,
                    logoY - logoSize,
                    logoSize * 2,
                    logoSize * 2
                );

                ctx.restore();

                // 添加圓形邊框
                ctx.strokeStyle = colors.solarAmber;
                ctx.lineWidth = 6;
                ctx.beginPath();
                ctx.arc(centerX, logoY, logoSize, 0, Math.PI * 2);
                ctx.stroke();
            } else {
                drawFallbackLogo(centerX, logoY, logoSize);
            }

            // 主標題（僅英文）
            ctx.fillStyle = colors.white;
            ctx.font = 'bold 110px "Arial", sans-serif';
            ctx.shadowBlur = 8;
            ctx.shadowOffsetX = 4;
            ctx.shadowOffsetY = 4;
            ctx.fillText('AstReal', centerX, centerY + 60);

            // 副標題（僅英文）
            ctx.fillStyle = colors.solarAmber;
            ctx.font = '38px "Arial", sans-serif';
            ctx.shadowBlur = 4;
            ctx.shadowOffsetX = 2;
            ctx.shadowOffsetY = 2;
            ctx.fillText('Professional Astrology', centerX, centerY + 120);

            // 清除陰影
            ctx.shadowColor = 'transparent';
            ctx.shadowBlur = 0;
            ctx.shadowOffsetX = 0;
            ctx.shadowOffsetY = 0;
        }

        function drawPremiumEffects() {
            // 高級版本的額外視覺效果

            // 添加動態光線效果
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;

            // 放射狀光線
            ctx.strokeStyle = colors.solarAmber + '20';
            ctx.lineWidth = 2;

            for (let i = 0; i < 12; i++) {
                const angle = (i / 12) * Math.PI * 2;
                const startRadius = 100;
                const endRadius = 250;

                const startX = centerX + Math.cos(angle) * startRadius;
                const startY = centerY + Math.sin(angle) * startRadius;
                const endX = centerX + Math.cos(angle) * endRadius;
                const endY = centerY + Math.sin(angle) * endRadius;

                ctx.beginPath();
                ctx.moveTo(startX, startY);
                ctx.lineTo(endX, endY);
                ctx.stroke();
            }

            // 添加更多星空效果
            ctx.fillStyle = colors.white + '80';
            for (let i = 0; i < 30; i++) {
                const x = Math.random() * canvas.width;
                const y = Math.random() * canvas.height;
                const size = Math.random() * 3 + 1;

                // 創建閃爍效果
                const opacity = 0.3 + Math.random() * 0.7;
                ctx.fillStyle = colors.white + Math.floor(opacity * 255).toString(16).padStart(2, '0');

                ctx.beginPath();
                ctx.arc(x, y, size, 0, Math.PI * 2);
                ctx.fill();
            }

            // 添加邊框光暈
            const borderGradient = ctx.createLinearGradient(0, 0, canvas.width, 0);
            borderGradient.addColorStop(0, colors.solarAmber + '00');
            borderGradient.addColorStop(0.5, colors.solarAmber + '60');
            borderGradient.addColorStop(1, colors.solarAmber + '00');

            ctx.strokeStyle = borderGradient;
            ctx.lineWidth = 4;
            ctx.strokeRect(2, 2, canvas.width - 4, canvas.height - 4);
        }

        function drawFallbackLogo(x, y, size) {
            // 備用 Logo 設計（當實際圖標未載入時使用）

            // 外圈光暈
            const outerGlow = ctx.createRadialGradient(
                x, y, 0,
                x, y, size + 20
            );
            outerGlow.addColorStop(0, colors.solarAmber + '60');
            outerGlow.addColorStop(1, colors.solarAmber + '00');

            ctx.fillStyle = outerGlow;
            ctx.beginPath();
            ctx.arc(x, y, size + 20, 0, Math.PI * 2);
            ctx.fill();

            // 主體漸變
            const logoGradient = ctx.createRadialGradient(
                x, y, 0,
                x, y, size
            );
            logoGradient.addColorStop(0, colors.solarAmber);
            logoGradient.addColorStop(0.7, '#E6941A');
            logoGradient.addColorStop(1, '#D4841A');

            ctx.fillStyle = logoGradient;
            ctx.beginPath();
            ctx.arc(x, y, size, 0, Math.PI * 2);
            ctx.fill();

            // 內圈高光
            const highlight = ctx.createRadialGradient(
                x - 20, y - 20, 0,
                x - 20, y - 20, 40
            );
            highlight.addColorStop(0, colors.white + '40');
            highlight.addColorStop(1, colors.white + '00');

            ctx.fillStyle = highlight;
            ctx.beginPath();
            ctx.arc(x, y, size, 0, Math.PI * 2);
            ctx.fill();

            // 星象符號
            ctx.fillStyle = colors.white;
            ctx.font = `bold ${size * 0.8}px serif`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.shadowColor = 'rgba(0,0,0,0.3)';
            ctx.shadowBlur = 4;
            ctx.shadowOffsetX = 2;
            ctx.shadowOffsetY = 2;
            ctx.fillText('✦', x, y);

            // 清除陰影
            ctx.shadowColor = 'transparent';
            ctx.shadowBlur = 0;
            ctx.shadowOffsetX = 0;
            ctx.shadowOffsetY = 0;
        }

        function downloadImage() {
            const link = document.getElementById('downloadAnchor');
            const downloadDiv = document.getElementById('downloadLink');
            
            canvas.toBlob(function(blob) {
                const url = URL.createObjectURL(blob);
                link.href = url;
                downloadDiv.style.display = 'block';
                
                // 自動觸發下載
                link.click();
                
                // 清理 URL
                setTimeout(() => {
                    URL.revokeObjectURL(url);
                }, 1000);
            }, 'image/png');
        }
        
        // 頁面載入時自動生成標準版本
        window.onload = function() {
            // 如果圖標已載入，直接生成；否則等待圖標載入
            if (appIcon) {
                generateGraphic('standard');
            } else {
                // 設置超時，如果圖標載入失敗則使用備用設計
                setTimeout(() => {
                    if (!appIcon) {
                        console.log('圖標載入失敗，使用備用設計');
                        generateGraphic('standard');
                    }
                }, 3000);
            }
        };
    </script>
</body>
</html>
