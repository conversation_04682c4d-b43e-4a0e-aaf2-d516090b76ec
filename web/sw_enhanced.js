// 增強版 Service Worker - 解決快取問題
// Version: {{BUILD_VERSION}}

const CACHE_NAME = 'astreal-cache-{{BUILD_VERSION}}';
const CURRENT_VERSION = '{{BUILD_VERSION}}';

// 需要快取的靜態資源
const STATIC_CACHE_URLS = [
  '/',
  '/index.html',
  '/manifest.json',
  '/favicon.png'
];

// 不需要快取的資源（總是從網路獲取）
const NETWORK_ONLY_URLS = [
  '/version.json',
  '/simple_version_check.js',
  '/sw_enhanced.js'
];

console.log('🔧 Service Worker starting, version:', CURRENT_VERSION);

// 安裝事件
self.addEventListener('install', event => {
  console.log('📦 Service Worker installing, version:', CURRENT_VERSION);
  
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(cache => {
        console.log('📦 Caching static resources');
        return cache.addAll(STATIC_CACHE_URLS);
      })
      .then(() => {
        console.log('✅ Service Worker installed successfully');
        // 強制激活新的 Service Worker
        return self.skipWaiting();
      })
      .catch(error => {
        console.error('❌ Service Worker installation failed:', error);
      })
  );
});

// 激活事件
self.addEventListener('activate', event => {
  console.log('🚀 Service Worker activating, version:', CURRENT_VERSION);
  
  event.waitUntil(
    Promise.all([
      // 清除舊版本的快取
      caches.keys().then(cacheNames => {
        return Promise.all(
          cacheNames.map(cacheName => {
            if (cacheName !== CACHE_NAME) {
              console.log('🗑️ Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      }),
      // 立即控制所有客戶端
      self.clients.claim()
    ]).then(() => {
      console.log('✅ Service Worker activated successfully');
      
      // 通知所有客戶端 Service Worker 已更新
      return self.clients.matchAll();
    }).then(clients => {
      clients.forEach(client => {
        client.postMessage({
          type: 'SW_UPDATED',
          version: CURRENT_VERSION
        });
      });
    })
  );
});

// 獲取事件
self.addEventListener('fetch', event => {
  const url = new URL(event.request.url);
  
  // 檢查是否為需要網路優先的資源
  const isNetworkOnly = NETWORK_ONLY_URLS.some(pattern => 
    url.pathname.includes(pattern)
  );
  
  if (isNetworkOnly) {
    // 網路優先策略
    event.respondWith(
      fetch(event.request)
        .catch(() => {
          // 網路失敗時的後備處理
          console.log('🌐 Network failed for:', url.pathname);
          return new Response('Network Error', { status: 503 });
        })
    );
    return;
  }
  
  // 對於其他資源使用快取優先策略
  event.respondWith(
    caches.match(event.request)
      .then(response => {
        if (response) {
          console.log('📦 Serving from cache:', url.pathname);
          return response;
        }
        
        // 快取中沒有，從網路獲取
        return fetch(event.request)
          .then(response => {
            // 檢查響應是否有效
            if (!response || response.status !== 200 || response.type !== 'basic') {
              return response;
            }
            
            // 複製響應以便快取
            const responseToCache = response.clone();
            
            caches.open(CACHE_NAME)
              .then(cache => {
                cache.put(event.request, responseToCache);
              });
            
            return response;
          })
          .catch(error => {
            console.error('🌐 Fetch failed:', error);
            
            // 對於 HTML 請求，返回離線頁面
            if (event.request.destination === 'document') {
              return caches.match('/index.html');
            }
            
            return new Response('Network Error', { status: 503 });
          });
      })
  );
});

// 監聽來自主線程的消息
self.addEventListener('message', event => {
  console.log('📨 Service Worker received message:', event.data);
  
  if (event.data && event.data.type === 'SKIP_WAITING') {
    console.log('⏭️ Skipping waiting...');
    self.skipWaiting();
  }
  
  if (event.data && event.data.type === 'GET_VERSION') {
    event.ports[0].postMessage({
      type: 'VERSION_RESPONSE',
      version: CURRENT_VERSION
    });
  }
  
  if (event.data && event.data.type === 'CLEAR_CACHE') {
    console.log('🗑️ Clearing all caches...');
    event.waitUntil(
      caches.keys().then(cacheNames => {
        return Promise.all(
          cacheNames.map(cacheName => caches.delete(cacheName))
        );
      }).then(() => {
        event.ports[0].postMessage({
          type: 'CACHE_CLEARED',
          success: true
        });
      }).catch(error => {
        console.error('❌ Failed to clear cache:', error);
        event.ports[0].postMessage({
          type: 'CACHE_CLEARED',
          success: false,
          error: error.message
        });
      })
    );
  }
});

// 錯誤處理
self.addEventListener('error', event => {
  console.error('❌ Service Worker error:', event.error);
});

self.addEventListener('unhandledrejection', event => {
  console.error('❌ Service Worker unhandled rejection:', event.reason);
});

console.log('✅ Service Worker script loaded, version:', CURRENT_VERSION);
