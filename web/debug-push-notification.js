// AstReal Web 推播通知除錯工具
// 在瀏覽器 Console 中執行此腳本來診斷推播通知問題

console.log('🔧 AstReal Web 推播通知除錯工具啟動');

// 檢查瀏覽器支援
function checkBrowserSupport() {
    console.log('\n=== 瀏覽器支援檢查 ===');
    
    const checks = [
        { name: 'Service Worker', supported: 'serviceWorker' in navigator },
        { name: 'Notification API', supported: 'Notification' in window },
        { name: 'Push Manager', supported: 'PushManager' in window },
        { name: 'Firebase Messaging', supported: typeof firebase !== 'undefined' && firebase.messaging }
    ];
    
    checks.forEach(check => {
        console.log(`${check.supported ? '✅' : '❌'} ${check.name}: ${check.supported ? '支援' : '不支援'}`);
    });
    
    return checks.every(check => check.supported);
}

// 檢查 Service Worker 狀態
async function checkServiceWorkerStatus() {
    console.log('\n=== Service Worker 狀態檢查 ===');
    
    if (!('serviceWorker' in navigator)) {
        console.log('❌ 瀏覽器不支援 Service Worker');
        return false;
    }
    
    try {
        const registration = await navigator.serviceWorker.getRegistration('/firebase-messaging-sw.js');
        
        if (registration) {
            console.log('✅ Firebase Messaging Service Worker 已註冊');
            console.log('📍 Scope:', registration.scope);
            console.log('📍 State:', registration.active?.state || 'unknown');
            
            // 檢查是否有更新
            await registration.update();
            console.log('🔄 Service Worker 更新檢查完成');
            
            return true;
        } else {
            console.log('⚠️ Firebase Messaging Service Worker 未註冊');
            console.log('💡 嘗試註冊 Service Worker...');
            
            const newRegistration = await navigator.serviceWorker.register('/firebase-messaging-sw.js');
            console.log('✅ Service Worker 註冊成功:', newRegistration);
            return true;
        }
    } catch (error) {
        console.error('❌ Service Worker 檢查失敗:', error);
        return false;
    }
}

// 檢查通知權限
async function checkNotificationPermission() {
    console.log('\n=== 通知權限檢查 ===');
    
    if (!('Notification' in window)) {
        console.log('❌ 瀏覽器不支援 Notification API');
        return false;
    }
    
    const permission = Notification.permission;
    console.log(`📍 當前權限狀態: ${permission}`);
    
    switch (permission) {
        case 'granted':
            console.log('✅ 通知權限已授權');
            return true;
        case 'denied':
            console.log('❌ 通知權限被拒絕');
            console.log('💡 請在瀏覽器設定中重置網站權限');
            return false;
        case 'default':
            console.log('⚠️ 通知權限未設定');
            console.log('💡 嘗試請求權限...');
            
            try {
                const result = await Notification.requestPermission();
                console.log(`📍 權限請求結果: ${result}`);
                return result === 'granted';
            } catch (error) {
                console.error('❌ 請求權限失敗:', error);
                return false;
            }
        default:
            console.log('❓ 未知的權限狀態');
            return false;
    }
}

// 檢查 Firebase 配置
function checkFirebaseConfig() {
    console.log('\n=== Firebase 配置檢查 ===');
    
    if (typeof firebase === 'undefined') {
        console.log('❌ Firebase SDK 未載入');
        console.log('💡 請確保在 HTML 中包含 Firebase SDK');
        return false;
    }
    
    console.log('✅ Firebase SDK 已載入');
    console.log('📍 Firebase 版本:', firebase.SDK_VERSION || 'unknown');
    
    try {
        const apps = firebase.apps;
        if (apps.length > 0) {
            console.log('✅ Firebase 應用已初始化');
            console.log('📍 應用數量:', apps.length);
            
            const defaultApp = apps.find(app => app.name === '[DEFAULT]');
            if (defaultApp) {
                console.log('✅ 預設 Firebase 應用存在');
                console.log('📍 專案 ID:', defaultApp.options.projectId);
                console.log('📍 Messaging Sender ID:', defaultApp.options.messagingSenderId);
            } else {
                console.log('⚠️ 未找到預設 Firebase 應用');
            }
        } else {
            console.log('⚠️ Firebase 應用未初始化');
        }
        
        return true;
    } catch (error) {
        console.error('❌ Firebase 配置檢查失敗:', error);
        return false;
    }
}

// 檢查 FCM Token
async function checkFCMToken() {
    console.log('\n=== FCM Token 檢查 ===');
    
    if (typeof firebase === 'undefined' || !firebase.messaging) {
        console.log('❌ Firebase Messaging 不可用');
        return null;
    }
    
    try {
        const messaging = firebase.messaging();
        console.log('✅ Firebase Messaging 實例已創建');
        
        // 使用您的 VAPID Key
        const vapidKey = "BDXI78bC3YqFM5iqf4OmRirP8KxZ_KC5XUv2Hzl2_PmARvsB1PXjAbwb23y7dl53fckVN4PJKi5nkz1sokpYtRs";
        
        console.log('🔑 嘗試獲取 FCM Token...');
        const token = await messaging.getToken({ vapidKey });
        
        if (token) {
            console.log('✅ FCM Token 獲取成功');
            console.log('📍 Token (前20字符):', token.substring(0, 20) + '...');
            console.log('📍 完整 Token:', token);
            return token;
        } else {
            console.log('❌ FCM Token 獲取失敗');
            console.log('💡 可能原因：');
            console.log('   - 通知權限未授權');
            console.log('   - VAPID Key 不正確');
            console.log('   - Service Worker 未正確註冊');
            console.log('   - Firebase 配置錯誤');
            return null;
        }
    } catch (error) {
        console.error('❌ FCM Token 獲取失敗:', error);
        console.log('💡 錯誤詳情:', error.message);
        return null;
    }
}

// 測試本地通知
function testLocalNotification() {
    console.log('\n=== 本地通知測試 ===');
    
    if (Notification.permission !== 'granted') {
        console.log('❌ 通知權限未授權，無法測試');
        return false;
    }
    
    try {
        const notification = new Notification('AstReal 測試通知', {
            body: '這是一個本地測試通知，確認瀏覽器通知功能正常',
            icon: '/icons/Icon-192.png',
            badge: '/icons/Icon-192.png',
            tag: 'astreal-test'
        });
        
        notification.onclick = function() {
            console.log('📱 測試通知被點擊');
            notification.close();
        };
        
        console.log('✅ 本地測試通知已發送');
        return true;
    } catch (error) {
        console.error('❌ 本地通知測試失敗:', error);
        return false;
    }
}

// 監聽前景通知
function setupForegroundMessageListener() {
    console.log('\n=== 設定前景通知監聽器 ===');
    
    if (typeof firebase === 'undefined' || !firebase.messaging) {
        console.log('❌ Firebase Messaging 不可用');
        return false;
    }
    
    try {
        const messaging = firebase.messaging();
        
        messaging.onMessage((payload) => {
            console.log('📨 收到前景通知:', payload);
            console.log('📍 標題:', payload.notification?.title);
            console.log('📍 內容:', payload.notification?.body);
            console.log('📍 數據:', payload.data);
            
            // 顯示本地通知
            if (Notification.permission === 'granted') {
                const notification = new Notification(
                    payload.notification?.title || '新通知',
                    {
                        body: payload.notification?.body || '',
                        icon: payload.notification?.icon || '/icons/Icon-192.png',
                        data: payload.data
                    }
                );
                
                notification.onclick = function() {
                    console.log('📱 前景通知被點擊');
                    notification.close();
                };
            }
        });
        
        console.log('✅ 前景通知監聽器已設定');
        return true;
    } catch (error) {
        console.error('❌ 設定前景通知監聽器失敗:', error);
        return false;
    }
}

// 執行完整診斷
async function runFullDiagnostic() {
    console.log('🚀 開始執行完整診斷...\n');
    
    const results = {
        browserSupport: checkBrowserSupport(),
        serviceWorker: await checkServiceWorkerStatus(),
        notificationPermission: await checkNotificationPermission(),
        firebaseConfig: checkFirebaseConfig(),
        fcmToken: await checkFCMToken(),
        localNotification: testLocalNotification(),
        foregroundListener: setupForegroundMessageListener()
    };
    
    console.log('\n=== 診斷結果摘要 ===');
    Object.entries(results).forEach(([key, value]) => {
        const status = value ? '✅' : '❌';
        console.log(`${status} ${key}: ${value ? '正常' : '異常'}`);
    });
    
    const allPassed = Object.values(results).every(result => result);
    
    if (allPassed) {
        console.log('\n🎉 所有檢查都通過！Web 推播通知應該可以正常工作。');
        console.log('💡 如果仍然收不到通知，請檢查：');
        console.log('   1. Firebase Console 中的通知發送設定');
        console.log('   2. 網路連線狀態');
        console.log('   3. 瀏覽器的通知設定');
    } else {
        console.log('\n⚠️ 發現問題，請根據上述檢查結果進行修復。');
    }
    
    return results;
}

// 匯出函數供外部使用
window.AstrealPushDebug = {
    checkBrowserSupport,
    checkServiceWorkerStatus,
    checkNotificationPermission,
    checkFirebaseConfig,
    checkFCMToken,
    testLocalNotification,
    setupForegroundMessageListener,
    runFullDiagnostic
};

console.log('✅ AstReal Web 推播通知除錯工具已載入');
console.log('💡 執行 AstrealPushDebug.runFullDiagnostic() 開始完整診斷');
