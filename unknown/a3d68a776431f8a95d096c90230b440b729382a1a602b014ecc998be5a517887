# 動態字體縮放系統實作文件

## 概述
本文件說明在星盤繪製系統中實現動態字體縮放功能的過程，根據星盤大小自動調整所有文字元素的字體大小，特別是當星盤大小超過 500 像素時進行字體放大，以提升大螢幕設備上的可讀性。

## 實作目標
- 統一管理所有 CustomPainter 中的 TextStyle
- 根據星盤大小動態調整字體大小
- 當星盤大小 > 500 像素時額外放大字體
- 保持各種文字元素的相對比例
- 提供調試信息以便開發者了解縮放效果

## 技術架構

### 1. 字體縮放管理工具類

#### ChartTextStyles 類設計
```dart
class ChartTextStyles {
  /// 基準星盤大小（用於計算縮放比例）
  static const double BASE_CHART_SIZE = 400.0;
  
  /// 大星盤的閾值（超過此大小需要放大字體）
  static const double LARGE_CHART_THRESHOLD = 500.0;
  
  /// 計算字體縮放比例
  static double _getFontScale(double chartSize) {
    if (chartSize <= BASE_CHART_SIZE) {
      // 小於基準大小時，使用標準比例
      return chartSize / BASE_CHART_SIZE;
    } else if (chartSize > LARGE_CHART_THRESHOLD) {
      // 大於閾值時，額外放大字體
      final baseScale = chartSize / BASE_CHART_SIZE;
      final extraScale = (chartSize - LARGE_CHART_THRESHOLD) / BASE_CHART_SIZE * 0.3;
      return baseScale + extraScale;
    } else {
      // 在基準大小和閾值之間時，使用線性縮放
      return chartSize / BASE_CHART_SIZE;
    }
  }
}
```

#### 縮放邏輯說明
1. **基準大小**：400 像素作為標準參考大小
2. **線性縮放**：400-500 像素範圍內線性縮放
3. **額外放大**：超過 500 像素時額外增加 30% 的縮放係數
4. **最小最大限制**：使用 `clamp()` 確保字體大小在合理範圍內

### 2. 統一的樣式方法

#### 宮位數字樣式
```dart
static TextStyle getHouseNumberStyle(double chartSize) {
  final scale = _getFontScale(chartSize);
  return TextStyle(
    color: Colors.black,
    fontSize: (12 * scale).clamp(8.0, 18.0),
    fontWeight: FontWeight.bold,
  );
}
```

#### 星座符號樣式
```dart
static TextStyle getZodiacSymbolStyle(double chartSize, Color color) {
  final scale = _getFontScale(chartSize);
  return TextStyle(
    color: color,
    fontSize: (16 * scale).clamp(12.0, 24.0),
    fontWeight: FontWeight.bold,
    fontFeatures: const [FontFeature.disable('liga')],
    fontFamily: 'astro_one_font',
  );
}
```

#### 行星符號樣式
```dart
static TextStyle getPlanetSymbolStyle(double chartSize, Color color) {
  final scale = _getFontScale(chartSize);
  return TextStyle(
    color: color,
    fontSize: (16 * scale).clamp(12.0, 24.0),
    fontWeight: FontWeight.normal,
    fontFamily: 'astro_one_font',
  );
}
```

### 3. 完整的樣式覆蓋

#### 支援的文字元素類型
1. **宮位數字**：`getHouseNumberStyle()`
2. **星座符號**：`getZodiacSymbolStyle()`
3. **行星符號**：`getPlanetSymbolStyle()`
4. **相位符號**：`getAspectSymbolStyle()`
5. **上升點標記**：`getAscendantStyle()`
6. **界主星符號**：`getTermRulerStyle()`
7. **宮位度數**：`getHouseDegreeStyle()`
8. **宮位分數**：`getHouseMinuteStyle()`
9. **行星度數**：`getPlanetDegreeStyle()`
10. **行星星座**：`getPlanetZodiacStyle()`
11. **行星分數**：`getPlanetMinuteStyle()`
12. **逆行符號**：`getRetrogradeStyle()`

#### 字體大小範圍設計
| 元素類型 | 基準大小 | 最小大小 | 最大大小 | 用途 |
|---------|---------|---------|---------|------|
| 星座符號 | 16px | 12px | 24px | 主要識別元素 |
| 行星符號 | 16px | 12px | 24px | 主要識別元素 |
| 相位符號 | 14px | 10px | 20px | 次要識別元素 |
| 宮位數字 | 12px | 8px | 18px | 輔助信息 |
| 上升點 | 12px | 8px | 18px | 特殊標記 |
| 界主星 | 10px | 7px | 15px | 詳細信息 |
| 度數信息 | 9-10px | 6-7px | 14-15px | 精確數據 |
| 分數信息 | 8px | 6px | 12px | 最精細數據 |

## 實際應用

### 1. 在 BaseChartPainter 中的應用

#### 修改前
```dart
void _drawHouseNumbers(Canvas canvas, Offset center, double radius) {
  const textStyle = TextStyle(
    color: Colors.black,
    fontSize: 12,
    fontWeight: FontWeight.bold,
  );
  // ...
}
```

#### 修改後
```dart
void _drawHouseNumbers(Canvas canvas, Offset center, double radius) {
  final chartSize = radius * 2; // 計算星盤大小
  final textStyle = ChartTextStyles.getHouseNumberStyle(chartSize);
  // ...
}
```

### 2. 在 ChartPainter 中的應用

#### 星座符號繪製
```dart
void _drawZodiacSymbols(Canvas canvas, Offset center, double radius) {
  // ...
  for (int i = 0; i < 12; i++) {
    final chartSize = radius * 2; // 計算星盤大小
    final zodiacTextStyle = ChartTextStyles.getZodiacSymbolStyle(
        chartSize, ZodiacSymbols.getZodiacColor(signs[i]));
    // ...
  }
}
```

#### 行星符號繪製
```dart
void drawPlanetSymbols(Canvas canvas, Offset center, double radius) {
  // ...
  for (final planet in sortedPlanets) {
    final chartSize = radius * 2; // 計算星盤大小
    final planetTextStyle = ChartTextStyles.getPlanetSymbolStyle(chartSize, planet.color);
    // ...
  }
}
```

### 3. 縮放效果示例

#### 不同星盤大小的字體效果
```
星盤大小 300px (小螢幕):
- 縮放比例: 0.75
- 星座符號: 12px (16 * 0.75)
- 宮位數字: 9px (12 * 0.75)
- 行星符號: 12px (16 * 0.75)

星盤大小 400px (標準):
- 縮放比例: 1.0
- 星座符號: 16px (16 * 1.0)
- 宮位數字: 12px (12 * 1.0)
- 行星符號: 16px (16 * 1.0)

星盤大小 600px (大螢幕):
- 縮放比例: 1.8 (1.5 + 0.3)
- 星座符號: 24px (16 * 1.8, 限制在最大值)
- 宮位數字: 18px (12 * 1.8, 限制在最大值)
- 行星符號: 24px (16 * 1.8, 限制在最大值)

星盤大小 800px (超大螢幕):
- 縮放比例: 2.4 (2.0 + 0.4)
- 星座符號: 24px (達到最大限制)
- 宮位數字: 18px (達到最大限制)
- 行星符號: 24px (達到最大限制)
```

## 調試功能

### 1. 字體縮放信息獲取
```dart
/// 獲取字體縮放比例（用於調試）
static double getFontScale(double chartSize) {
  return _getFontScale(chartSize);
}

/// 獲取字體大小信息（用於調試）
static Map<String, double> getFontSizeInfo(double chartSize) {
  final scale = _getFontScale(chartSize);
  return {
    'scale': scale,
    'houseNumber': (12 * scale).clamp(8.0, 18.0),
    'zodiacSymbol': (16 * scale).clamp(12.0, 24.0),
    'planetSymbol': (16 * scale).clamp(12.0, 24.0),
    // ... 其他字體大小
  };
}
```

### 2. 開發模式調試信息
在開發模式下，可以顯示當前的字體縮放信息：
```dart
if (kDebugMode) {
  final fontInfo = ChartTextStyles.getFontSizeInfo(chartSize);
  print('字體縮放比例: ${fontInfo['scale']}');
  print('星座符號大小: ${fontInfo['zodiacSymbol']}px');
  print('行星符號大小: ${fontInfo['planetSymbol']}px');
}
```

## 設計原則

### 1. 可讀性優先
- **主要元素**：星座和行星符號使用較大字體
- **輔助信息**：度數和分數使用較小字體
- **層次分明**：不同類型的信息有明確的大小層次

### 2. 比例協調
- **相對比例**：各元素間的大小比例保持一致
- **視覺平衡**：避免某些元素過大或過小
- **整體和諧**：所有文字元素協調統一

### 3. 設備適配
- **小螢幕**：確保文字不會太小而無法閱讀
- **大螢幕**：充分利用空間，提供更好的可讀性
- **響應式**：根據實際星盤大小動態調整

### 4. 性能考量
- **計算效率**：縮放計算簡單快速
- **記憶體優化**：不緩存樣式對象，每次動態生成
- **渲染性能**：不影響繪製性能

## 技術優勢

### 1. 統一管理
- **集中控制**：所有字體樣式在一個類中管理
- **一致性**：確保整個應用的字體風格一致
- **易於維護**：修改字體規則只需要在一個地方

### 2. 動態適配
- **自動縮放**：根據星盤大小自動調整
- **智能放大**：大螢幕上提供更好的可讀性
- **邊界保護**：防止字體過大或過小

### 3. 開發友好
- **調試支援**：提供詳細的縮放信息
- **易於擴展**：新增文字元素樣式很簡單
- **類型安全**：使用靜態方法確保類型安全

### 4. 用戶體驗
- **可讀性提升**：在各種設備上都有良好的可讀性
- **視覺一致**：保持專業的視覺效果
- **無縫適配**：用戶無需手動調整字體大小

## 未來擴展

### 1. 用戶自定義
- **字體大小偏好**：允許用戶設定字體大小偏好
- **縮放係數調整**：提供縮放係數的自定義選項
- **字體族選擇**：支援不同的字體族選擇

### 2. 智能優化
- **設備檢測**：根據設備類型優化縮放策略
- **DPI 感知**：考慮設備的像素密度
- **可訪問性**：支援系統的可訪問性設定

### 3. 高級功能
- **動畫過渡**：字體大小變化時的平滑動畫
- **主題整合**：與應用主題系統整合
- **國際化支援**：支援不同語言的字體需求

## 測試驗證

### 1. 功能測試
- ✅ 字體縮放計算正確
- ✅ 各種星盤大小下的顯示效果
- ✅ 邊界值處理正常
- ✅ 所有文字元素都正確應用縮放

### 2. 視覺測試
- ✅ 小螢幕上文字清晰可讀
- ✅ 大螢幕上文字大小適中
- ✅ 各元素間比例協調
- ✅ 整體視覺效果良好

### 3. 性能測試
- ✅ 縮放計算不影響繪製性能
- ✅ 記憶體使用正常
- ✅ 響應速度無明顯影響
- ✅ 大量文字元素下性能穩定

### 4. 兼容性測試
- ✅ 不同設備尺寸適配正常
- ✅ 不同解析度下顯示正確
- ✅ 橫豎屏切換正常
- ✅ 各種星盤配置下都正常工作

## 總結

動態字體縮放系統的實現帶來了以下改進：

1. **統一管理**：所有 TextStyle 集中在 ChartTextStyles 類中管理
2. **智能縮放**：根據星盤大小自動調整字體大小
3. **大螢幕優化**：星盤大小超過 500px 時額外放大字體
4. **可讀性提升**：在各種設備上都提供最佳的可讀性
5. **開發效率**：簡化了字體樣式的管理和維護

這個系統特別適合需要在不同設備上顯示複雜圖表的應用，確保無論在手機、平板還是大螢幕設備上，用戶都能獲得最佳的閱讀體驗。通過智能的縮放算法和合理的邊界限制，系統能夠在保持視覺美觀的同時，最大化提升文字的可讀性。
