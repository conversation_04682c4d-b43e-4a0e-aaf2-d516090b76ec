<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>聯絡我們 - AstReal</title>
    <meta name="description" content="聯絡 AstReal 團隊，獲取技術支援、合作洽談或任何問題諮詢。">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans TC', sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 20px 0;
            margin-bottom: 40px;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 1.8rem;
            font-weight: 700;
            color: white;
            text-decoration: none;
        }

        .nav-link {
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .nav-link:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .main-content {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            margin-bottom: 40px;
        }

        .page-header {
            background: linear-gradient(135deg, #3F51B5, #5C6BC0);
            color: white;
            padding: 60px 0;
            text-align: center;
        }

        .page-header h1 {
            font-size: 3rem;
            margin-bottom: 20px;
            font-weight: 700;
        }

        .page-header p {
            font-size: 1.2rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
        }

        .contact-content {
            padding: 60px 0;
        }

        .contact-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 40px;
            margin-bottom: 60px;
        }

        .contact-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 40px 30px;
            text-align: center;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .contact-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
            border-color: #3F51B5;
        }

        .contact-card .icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #3F51B5, #5C6BC0);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 2rem;
            color: white;
        }

        .contact-card h3 {
            font-size: 1.5rem;
            margin-bottom: 15px;
            color: #333;
        }

        .contact-card p {
            color: #666;
            margin-bottom: 20px;
            line-height: 1.6;
        }

        .contact-card a {
            display: inline-block;
            background: linear-gradient(135deg, #3F51B5, #5C6BC0);
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .contact-card a:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(63, 81, 181, 0.3);
        }

        .social-section {
            background: #f8f9fa;
            padding: 60px 0;
            text-align: center;
        }

        .social-section h2 {
            font-size: 2.5rem;
            margin-bottom: 20px;
            color: #333;
        }

        .social-section p {
            font-size: 1.1rem;
            color: #666;
            margin-bottom: 40px;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .social-links {
            display: flex;
            justify-content: center;
            gap: 30px;
            flex-wrap: wrap;
        }

        .social-link {
            display: flex;
            align-items: center;
            gap: 10px;
            background: white;
            padding: 15px 25px;
            border-radius: 50px;
            text-decoration: none;
            color: #333;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .social-link:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        .social-link.instagram:hover {
            background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
            color: white;
        }

        .social-link.facebook:hover {
            background: #1877f2;
            color: white;
        }

        .social-link i {
            font-size: 1.2rem;
        }

        .footer {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            color: white;
            text-align: center;
            padding: 30px 0;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .page-header h1 {
                font-size: 2rem;
            }
            
            .contact-grid {
                grid-template-columns: 1fr;
                gap: 30px;
            }
            
            .social-links {
                flex-direction: column;
                align-items: center;
            }
            
            .header-content {
                flex-direction: column;
                gap: 20px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <a href="/" class="logo">AstReal</a>
                <a href="/" class="nav-link">
                    <i class="fas fa-home"></i> 返回首頁
                </a>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <div class="container">
        <div class="main-content">
            <!-- Page Header -->
            <div class="page-header">
                <h1>聯絡我們</h1>
                <p>我們很樂意聽到您的聲音！無論是技術支援、合作洽談或任何問題，都歡迎與我們聯繫。</p>
            </div>

            <!-- Contact Content -->
            <div class="contact-content">
                <div class="container">
                    <div class="contact-grid">
                        <!-- 官方網站 -->
                        <div class="contact-card">
                            <div class="icon">
                                <i class="fas fa-globe"></i>
                            </div>
                            <h3>官方網站</h3>
                            <p>瀏覽我們的官方網站，了解更多關於 AstReal 的資訊和最新消息。</p>
                            <a href="https://astreal-website.web.app" target="_blank">前往官網</a>
                        </div>

                        <!-- 占星應用程式 -->
                        <div class="contact-card">
                            <div class="icon">
                                <i class="fas fa-mobile-alt"></i>
                            </div>
                            <h3>占星應用程式</h3>
                            <p>立即體驗 AstReal 占星應用程式，開始您的占星之旅。</p>
                            <a href="https://astreal.web.app" target="_blank">開始使用</a>
                        </div>

                        <!-- 電子郵件 -->
                        <div class="contact-card">
                            <div class="icon">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <h3>電子郵件</h3>
                            <p>有任何問題或建議？歡迎透過電子郵件與我們聯繫，我們會盡快回覆您。</p>
                            <a href="mailto:<EMAIL>">發送郵件</a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Social Media Section -->
            <div class="social-section">
                <div class="container">
                    <h2>關注我們</h2>
                    <p>在社群媒體上關注 AstReal，獲取最新的占星知識、應用程式更新和有趣的占星內容。</p>
                    
                    <div class="social-links">
                        <a href="https://instagram.com/astreal.astrology" target="_blank" class="social-link instagram">
                            <i class="fab fa-instagram"></i>
                            <span>Instagram</span>
                        </a>
                        
                        <a href="https://facebook.com/astreal.astrology" target="_blank" class="social-link facebook">
                            <i class="fab fa-facebook"></i>
                            <span>Facebook 粉絲專頁</span>
                        </a>

                        <a href="https://www.youtube.com/@AstReal-Astrology" target="_blank" class="social-link youtube">
                            <i class="fab fa-youtube"></i>
                            <span>YouTube</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2025 AstReal. 保留所有權利。</p>
        </div>
    </footer>
</body>
</html>
