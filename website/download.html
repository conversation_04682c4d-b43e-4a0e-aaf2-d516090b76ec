<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>下載 - AstReal</title>
    <meta name="description" content="下載AstReal，支援 iOS、Android、macOS 和 Web 平台。專業的占星應用程式，提供完整的本命盤分析和深入剖析。">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans TC', sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f8f9fa;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header */
        .header {
            background: linear-gradient(135deg, #3F51B5 0%, #303F9F 100%);
            color: white;
            padding: 60px 0;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 20px;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        /* Download Section */
        .download-section {
            padding: 80px 0;
        }

        .platform-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 40px;
            margin-top: 40px;
        }

        .platform-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            text-align: center;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .platform-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .platform-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            display: block;
        }

        .platform-card h2 {
            font-size: 1.8rem;
            margin-bottom: 15px;
            color: #2d3748;
        }

        .platform-info {
            margin-bottom: 30px;
        }

        .platform-info p {
            color: #718096;
            margin-bottom: 10px;
        }

        .requirements {
            background: #f7fafc;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
        }

        .requirements h4 {
            color: #4a5568;
            margin-bottom: 10px;
        }

        .requirements ul {
            color: #718096;
            margin-left: 20px;
        }

        .download-btn {
            display: inline-flex;
            align-items: center;
            gap: 10px;
            background: #F5A623;
            color: white;
            padding: 15px 30px;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 500;
            font-size: 1.1rem;
            transition: all 0.3s ease;
        }

        .download-btn:hover {
            background: #E6941A;
            transform: scale(1.05);
        }

        .coming-soon {
            background: #718096;
            cursor: not-allowed;
        }

        .coming-soon:hover {
            background: #718096;
            transform: none;
        }

        .available {
            background: #48bb78;
        }

        .available:hover {
            background: #38a169;
        }

        .features-list {
            text-align: left;
            margin: 20px 0;
        }

        .features-list h4 {
            color: #4a5568;
            margin-bottom: 10px;
        }

        .features-list ul {
            color: #718096;
            margin-left: 20px;
        }

        .features-list li {
            margin-bottom: 5px;
        }

        /* QR Code Section */
        .qr-section {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-top: 30px;
            text-align: center;
        }

        .qr-code {
            width: 150px;
            height: 150px;
            background: #f7fafc;
            border: 2px dashed #cbd5e0;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 20px auto;
            color: #a0aec0;
        }

        /* Back Button */
        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.9);
            color: #4a5568;
            padding: 10px 15px;
            border-radius: 50px;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 5px;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: white;
            transform: scale(1.05);
        }

        @media (max-width: 768px) {
            .platform-grid {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .platform-card {
                padding: 30px 20px;
            }
        }
    </style>
</head>
<body>
    <a href="/landing.html" class="back-btn">
        <span class="material-icons">arrow_back</span>
        返回首頁
    </a>

    <!-- Header -->
    <header class="header">
        <div class="container">
            <h1>下載AstReal</h1>
            <p>選擇適合您的平台，開始您的占星之旅</p>
        </div>
    </header>

    <!-- Download Section -->
    <section class="download-section">
        <div class="container">
            <div class="platform-grid">
                <!-- iOS -->
                <div class="platform-card">
                    <span class="platform-icon">📱</span>
                    <h2>iOS 版本</h2>
                    <div class="platform-info">
                        <p><strong>適用於 iPhone 和 iPad</strong></p>
                        <p>版本：1.0.0</p>
                        <p>大小：約 50 MB</p>
                        <p>語言：繁體中文</p>
                    </div>
                    
                    <div class="requirements">
                        <h4>系統需求</h4>
                        <ul>
                            <li>iOS 12.0 或更高版本</li>
                            <li>iPhone 6s 或更新機型</li>
                            <li>iPad Air 2 或更新機型</li>
                            <li>需要網路連線</li>
                        </ul>
                    </div>
                    
                    <div class="features-list">
                        <h4>iOS 專屬功能</h4>
                        <ul>
                            <li>iOS 原生界面設計</li>
                            <li>優化的觸控體驗</li>
                            <li>Face ID / Touch ID 支援</li>
                            <li>Siri 快捷指令</li>
                        </ul>
                    </div>
                    
                    <a href="https://apps.apple.com/app/astreal/id123456789" class="download-btn coming-soon">
                        <span class="material-icons">download</span>
                        即將在 App Store 推出
                    </a>
                </div>

                <!-- Android -->
                <div class="platform-card">
                    <span class="platform-icon">🤖</span>
                    <h2>Android 版本</h2>
                    <div class="platform-info">
                        <p><strong>適用於 Android 設備</strong></p>
                        <p>版本：1.0.0</p>
                        <p>大小：約 45 MB</p>
                        <p>語言：繁體中文</p>
                    </div>
                    
                    <div class="requirements">
                        <h4>系統需求</h4>
                        <ul>
                            <li>Android 6.0 (API 23) 或更高版本</li>
                            <li>RAM：至少 2GB</li>
                            <li>儲存空間：至少 100MB</li>
                            <li>需要網路連線</li>
                        </ul>
                    </div>
                    
                    <div class="features-list">
                        <h4>Android 專屬功能</h4>
                        <ul>
                            <li>Material Design 界面</li>
                            <li>優化的觸控體驗</li>
                            <li>Android 通知支援</li>
                            <li>小工具支援</li>
                        </ul>
                    </div>
                    
                    <a href="https://play.google.com/store/apps/details?id=com.one.astreal" class="download-btn coming-soon">
                        <span class="material-icons">download</span>
                        即將在 Google Play 推出
                    </a>
                </div>

                <!-- macOS -->
                <div class="platform-card">
                    <span class="platform-icon">💻</span>
                    <h2>macOS 版本</h2>
                    <div class="platform-info">
                        <p><strong>適用於 Mac 電腦</strong></p>
                        <p>版本：1.0.0</p>
                        <p>大小：約 80 MB</p>
                        <p>語言：繁體中文</p>
                    </div>
                    
                    <div class="requirements">
                        <h4>系統需求</h4>
                        <ul>
                            <li>macOS 10.15 (Catalina) 或更高版本</li>
                            <li>Intel 或 Apple Silicon 處理器</li>
                            <li>RAM：至少 4GB</li>
                            <li>需要網路連線</li>
                        </ul>
                    </div>
                    
                    <div class="features-list">
                        <h4>macOS 專屬功能</h4>
                        <ul>
                            <li>原生 macOS 界面</li>
                            <li>鍵盤快捷鍵支援</li>
                            <li>多視窗支援</li>
                            <li>Touch Bar 支援</li>
                        </ul>
                    </div>
                    
                    <a href="https://astreal.app/download/macos" class="download-btn coming-soon">
                        <span class="material-icons">download</span>
                        即將推出
                    </a>
                </div>

                <!-- Web -->
                <div class="platform-card">
                    <span class="platform-icon">🌐</span>
                    <h2>Web 版本</h2>
                    <div class="platform-info">
                        <p><strong>無需安裝，直接使用</strong></p>
                        <p>版本：1.0.0</p>
                        <p>語言：繁體中文</p>
                        <p>即時更新</p>
                    </div>
                    
                    <div class="requirements">
                        <h4>瀏覽器需求</h4>
                        <ul>
                            <li>Chrome 80+ / Safari 13+ / Firefox 75+</li>
                            <li>支援 JavaScript</li>
                            <li>建議螢幕解析度 1024x768 以上</li>
                            <li>穩定的網路連線</li>
                        </ul>
                    </div>
                    
                    <div class="features-list">
                        <h4>Web 版本特色</h4>
                        <ul>
                            <li>響應式設計</li>
                            <li>跨瀏覽器支援</li>
                            <li>無需安裝</li>
                            <li>即時同步</li>
                        </ul>
                    </div>
                    
                    <a href="https://astreal.web.app/" class="download-btn available">
                        <span class="material-icons">launch</span>
                        立即使用
                    </a>
                    
                    <div class="qr-section">
                        <h4>手機掃描 QR Code</h4>
                        <div class="qr-code">
                            <span class="material-icons" style="font-size: 3rem;">qr_code</span>
                        </div>
                        <p style="color: #718096; font-size: 0.9rem;">掃描後可在手機瀏覽器中使用</p>
                    </div>
                </div>
            </div>
        </div>
    </section>
</body>
</html>
