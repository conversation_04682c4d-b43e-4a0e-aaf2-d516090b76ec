<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AstReal - 官方網站</title>
    <meta name="description" content="AstReal 官方網站，專業的占星應用程式。">
    
    <!-- 自動重定向到官方網站 -->
    <meta http-equiv="refresh" content="0; url=/landing.html">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans TC', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
        }

        .loading-container {
            max-width: 500px;
            padding: 40px;
        }

        .logo {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            margin-bottom: 30px;
            opacity: 0.9;
        }

        .loading {
            display: inline-block;
            width: 40px;
            height: 40px;
            border: 3px solid rgba(255,255,255,0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
            margin-bottom: 20px;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .fallback-link {
            display: inline-block;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            text-decoration: none;
            margin-top: 20px;
            transition: all 0.3s ease;
        }

        .fallback-link:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="loading-container">
        <div class="logo">AstReal</div>
        <div class="subtitle">正在載入官方網站...</div>
        <div class="loading"></div>
        <p>如果頁面沒有自動跳轉，請點擊下方連結：</p>
        <a href="/landing.html" class="fallback-link">進入官方網站</a>
    </div>

    <script>
        // 備用重定向
        setTimeout(function() {
            if (window.location.pathname === '/home.html') {
                window.location.href = '/landing.html';
            }
        }, 2000);
    </script>
</body>
</html>
