<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>願景理念 - AstReal</title>
    <meta name="description" content="科技占星的願景理念，結合理性邏輯與直覺洞察，重新詮釋占星的價值與未來應用。">
    <meta name="keywords" content="占星,星盤,設計理念,科技占星,理性占星">

    <!-- Open Graph -->
    <meta property="og:title" content="願景理念 - AstReal">
    <meta property="og:description" content="科技占星的願景理念，結合理性邏輯與直覺洞察。">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://astreal-d3f70.web.app/vision.html">

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="../web/favicon.png">

    <style>
        :root {
            --royal-indigo: #3F51B5;
            --solar-amber: #F5A623;
            --indigo-surface: #303F9F;
            --indigo-light: #7986CB;
            --pastel-sky-blue: #E6F0FA;
            --light-cornsilk: #FFF8DC;
            --text-primary: #333;
            --text-secondary: #757575;
            --card-background: #FFFFFF;
            --radius: 16px;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Noto Sans TC', sans-serif;
            background: var(--pastel-sky-blue);
            color: var(--text-primary);
            line-height: 1.8;
        }

        main {
            max-width: 960px;
            margin: 60px auto;
            padding: 40px;
            background: var(--card-background);
            border-radius: var(--radius);
            box-shadow: 0 10px 30px rgba(63, 81, 181, 0.1);
        }

        h1 {
            font-size: 2.6em;
            text-align: center;
            margin-bottom: 20px;
            color: var(--royal-indigo);
            font-weight: 700;
        }

        h2 {
            font-size: 1.6em;
            margin: 40px 0 15px;
            color: var(--royal-indigo);
            border-bottom: 2px solid var(--royal-indigo);
            padding-bottom: 6px;
            font-weight: 600;
        }

        p {
            margin-bottom: 18px;
            text-align: justify;
            font-size: 1.05em;
            color: var(--text-primary);
        }

        ul {
            list-style: none;
            padding-left: 0;
        }

        li {
            background: var(--pastel-sky-blue);
            margin: 10px 0;
            padding: 15px 20px;
            border-left: 4px solid var(--royal-indigo);
            border-radius: 8px;
            transition: transform 0.2s ease;
        }

        li:hover {
            transform: translateX(5px);
        }

        li strong {
            display: block;
            margin-bottom: 6px;
            color: var(--royal-indigo);
            font-weight: 600;
        }

        .highlight {
            background: var(--royal-indigo);
            color: white;
            padding: 30px 20px;
            border-radius: 14px;
            text-align: center;
            font-size: 1.1em;
            margin: 40px 0;
            box-shadow: 0 8px 25px rgba(63, 81, 181, 0.2);
            border: 2px solid var(--solar-amber);
            position: relative;
            overflow: hidden;
        }

        .highlight::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: rgba(245, 166, 35, 0.1);
            transition: left 0.5s ease;
        }

        .highlight:hover::before {
            left: 0;
        }

        /* 新增一些視覺增強效果 */
        .section-divider {
            height: 2px;
            background: var(--solar-amber);
            margin: 40px 0;
            border-radius: 1px;
            opacity: 0.3;
        }

        .content-section {
            margin-bottom: 40px;
        }

        /* 響應式設計 */
        @media (max-width: 768px) {
            body {
                background: var(--light-cornsilk);
            }

            main {
                padding: 30px 20px;
                margin: 30px 15px;
                box-shadow: 0 5px 15px rgba(63, 81, 181, 0.1);
            }

            h1 {
                font-size: 2em;
            }

            h2 {
                font-size: 1.3em;
            }

            .highlight {
                font-size: 1em;
                padding: 20px;
                margin: 30px 0;
            }

            li {
                padding: 12px 16px;
            }
        }

        @media (max-width: 480px) {
            main {
                margin: 20px 10px;
                padding: 20px 15px;
            }

            h1 {
                font-size: 1.8em;
            }

            .highlight {
                padding: 15px;
                font-size: 0.95em;
            }
        }
    </style>
</head>
<body>
<main>
    <h1>AstReal 願景理念</h1>
    <div class="content-section">
        <p>在資訊爆炸與科學至上的當代社會，占星學長期遭遇誤解與簡化。AstReal 致力於將占星從迷思中解放，以現代科技與邏輯架構，重建其作為人類心理與時間感知工具的應用價值。</p>
    </div>

    <div class="highlight">
        占星是一套象徵語言，也是一種認知工具，其本質不應被神秘化，而應被理解、驗證、應用。
    </div>

    <div class="section-divider"></div>

    <div class="content-section">
        <h2>占星與科技的共振</h2>
        <p>星盤本質上是一種數據結構，背後涉及精確的天文計算與數理推演。藉由座標系統、相位邏輯與時序演算法，可轉化為視覺化與可操作的資訊介面。</p>

        <p>科技的參與，不僅提升了占星分析的準確度與效率，更讓其邁向大眾化、日常化的應用轉型。</p>
    </div>

    <div class="section-divider"></div>

    <div class="content-section">
        <h2>設計理念三大核心</h2>
        <ul>
            <li><strong>邏輯結構重建：</strong>以工程思維重構星盤運作邏輯，強化知識系統的一致性與可傳遞性。</li>
            <li><strong>資料實證導向：</strong>引入案例驗證、使用者回饋與長期觀察，作為技術與內容優化依據。</li>
            <li><strong>語言理性轉譯：</strong>使用簡潔、理性的語言詮釋占星，減少誤解與神秘化迷霧。</li>
        </ul>
    </div>

    <div class="section-divider"></div>

    <div class="content-section">
        <h2>超越傳統占星框架</h2>
        <p>AstReal 將占星視為一種「時間中的人性觀察工具」，關注的不只是行星位置，更是其所映照出的個人心理結構與人生節奏。</p>

        <p>在未來，將持續深化占星與心理學、資料科學的整合，打造兼具深度與實用性的工具與內容平台。</p>
    </div>
</main>
</body>
</html>