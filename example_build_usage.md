# 建置腳本使用範例

## 基本使用

### 建置網頁版本
```bash
# 正式版本
./scripts/build_with_timestamp.sh web release

# 輸出範例：
# === AstReal 建置腳本 ===
# 平台：web
# 建置類型：release
# 時間戳：********_143022
# 建置時間：2025-01-08 14:30:22
# 應用版本：1.0.0
# 建置號碼：********_143022
# 
# 開始建置...
# 執行命令：flutter build web --release --build-name=1.0.0 --build-number=********_143022
# 
# ✅ 建置成功！
# 平台：web
# 版本：1.0.0
# 建置號碼：********_143022
# 建置時間：2025-01-08 14:30:22
# 輸出位置：build/web/
```

### 建置 APK
```bash
# 正式版本
./scripts/build_with_timestamp.sh apk release

# 除錯版本
./scripts/build_with_timestamp.sh apk debug
```

## 特殊版本建置

### 強制更新版本
```bash
# 設定環境變數
FORCE_UPDATE=true ./scripts/build_with_timestamp.sh web release

# 建置號碼會變成：********_143022_force_update
# 在應用中顯示為：1.0.0 (********.1430-強制更新) [強制更新]
```

### 測試版本
```bash
# Beta 版本
BETA=true ./scripts/build_with_timestamp.sh apk release

# 建置號碼會變成：********_143022_beta
# 在應用中顯示為：1.0.0 (********.1430-測試版) [測試版]
```

### 內測版本
```bash
# Alpha 版本
ALPHA=true ./scripts/build_with_timestamp.sh apk debug

# 建置號碼會變成：********_143022_alpha
# 在應用中顯示為：1.0.0 (********.1430-內測版) [內測版]
```

## 版本顯示效果

### 原始顯示（問題）
```
AstReal 占星應用
版本: 1.0.0 (20250807_131801_force_update0)
建置時間: 2025-01-08 14:30 (當前時間，不正確)
```

### 優化後顯示
```
AstReal 占星應用 [強制更新]
版本: 1.0.0 (20250807.1318-強制更新)
建置時間: 2025-08-07 13:18:01
```

## Windows 使用方式

```cmd
REM 基本建置
scripts\build_with_timestamp.bat web release

REM 強制更新版本
set FORCE_UPDATE=true
scripts\build_with_timestamp.bat web release

REM 測試版本
set BETA=true
scripts\build_with_timestamp.bat apk release
```

## CI/CD 整合範例

### GitHub Actions
```yaml
name: Build and Deploy

on:
  push:
    branches: [ main ]
  workflow_dispatch:
    inputs:
      force_update:
        description: 'Force update version'
        required: false
        default: 'false'
        type: boolean
      beta:
        description: 'Beta version'
        required: false
        default: 'false'
        type: boolean

jobs:
  build:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.16.0'
    
    - name: Build Web Release
      run: |
        chmod +x scripts/build_with_timestamp.sh
        ./scripts/build_with_timestamp.sh web release
      env:
        FORCE_UPDATE: ${{ github.event.inputs.force_update }}
        BETA: ${{ github.event.inputs.beta }}
    
    - name: Deploy to Firebase Hosting
      uses: FirebaseExtended/action-hosting-deploy@v0
      with:
        repoToken: '${{ secrets.GITHUB_TOKEN }}'
        firebaseServiceAccount: '${{ secrets.FIREBASE_SERVICE_ACCOUNT }}'
        projectId: your-project-id
```

## 版本號格式說明

| 建置類型 | 環境變數 | 建置號碼範例 | 顯示格式 | 標籤 |
|---------|---------|-------------|----------|------|
| 正式版 | - | `********_143022` | `1.0.0 (********.1430)` | 正式版 |
| 強制更新 | `FORCE_UPDATE=true` | `********_143022_force_update` | `1.0.0 (********.1430-強制更新)` | 強制更新 |
| 測試版 | `BETA=true` | `********_143022_beta` | `1.0.0 (********.1430-測試版)` | 測試版 |
| 內測版 | `ALPHA=true` | `********_143022_alpha` | `1.0.0 (********.1430-內測版)` | 內測版 |
| 除錯版 | `debug` | `********_143022_debug` | `1.0.0 (********.1430-除錯版)` | 開發版 |

## 故障排除

### 常見問題

1. **腳本沒有執行權限**
   ```bash
   chmod +x scripts/build_with_timestamp.sh
   ```

2. **Flutter 命令找不到**
   ```bash
   # 檢查 Flutter 是否在 PATH 中
   which flutter
   
   # 或使用完整路徑
   export PATH="$PATH:/path/to/flutter/bin"
   ```

3. **建置時間仍顯示當前時間**
   - 確認使用建置腳本而非直接 `flutter build`
   - 檢查 `BuildInfoUtils.getBuildDate()` 是否正確解析建置號碼

4. **版本標籤不顯示**
   - 確認 `BuildInfo` 物件不為 null
   - 檢查建置號碼格式是否正確

### 除錯方法

在 `about_us_page.dart` 中加入除錯資訊：
```dart
print('Raw build number: ${buildInfo?.buildNumber}');
print('Parsed build date: ${buildInfo?.buildDate}');
print('Formatted version: ${buildInfo?.formattedVersion}');
print('Version tag: ${buildInfo?.versionTag}');
```

## 總結

通過使用新的建置腳本和 `BuildInfoUtils`，我們解決了：

1. ✅ **建置時間問題**：顯示實際打包時間而非當前時間
2. ✅ **版本號優化**：將 `20250807_131801_force_update0` 格式化為 `20250807.1318-強制更新`
3. ✅ **版本標籤**：視覺化區分不同類型的版本
4. ✅ **自動化建置**：簡化建置流程並確保一致性

現在用戶可以清楚地看到應用的實際建置時間和版本資訊，開發團隊也能更好地管理不同類型的版本發布。
