# scraper.py
import requests
import time
from bs4 import BeautifulSoup

from db import save_to_db
from parser import parse_birth_info
from utils import get_soup


def fetch_all_pages(start_letter):
    url = f"https://www.astro.com/wiki/astro-databank/index.php?title=Special:AllPages&from={start_letter}"
    soup = get_soup(url)
    links = soup.select("div.mw-allpages-body a")
    return [link.get("href") for link in links]

def scrape_celebrities(start_letter):
    urls = fetch_all_pages(start_letter)
    for i, url in enumerate(urls, start=1):
        print(f"Scraping {i}/{len(urls)}: {url}")
        soup = get_soup(url)
        data = parse_birth_info(soup)
        data["source_url"] = url
        if data["name"] and data["birth_date"]:
            save_to_db(data)
        time.sleep(1)
