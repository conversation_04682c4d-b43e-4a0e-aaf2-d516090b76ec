# utils.py
import re
import requests
from bs4 import BeautifulSoup


def get_soup(url):
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36"
    }
    resp = requests.get(url, headers=headers, timeout=10)
    resp.raise_for_status()
    return BeautifulSoup(resp.text, "html.parser")

def dms_to_decimal(dms_str):
    m = re.match(r"(\d+)([nsew])(\d+)", dms_str.lower())
    if not m:
        return None
    deg = int(m.group(1))
    direction = m.group(2)
    minutes = int(m.group(3))
    decimal = deg + minutes / 60
    if direction in ['s', 'w']:
        decimal = -decimal
    return round(decimal, 6)
