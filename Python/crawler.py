import requests
import time

from db import save_to_db, init_db
from parser import parse_list_page, parse_birth_info

HEADERS = {
    "User-Agent": ("Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
                   "AppleWebKit/537.36 (KHTML, like Gecko) "
                   "Chrome/114.0.0.0 Safari/537.36")
}

def get_html(url):
    resp = requests.get(url, headers=HEADERS, timeout=10)
    resp.raise_for_status()
    return resp.text

def crawl_list_page(url):
    print(f"🔍 抓取列表頁面：{url}")
    html = get_html(url)
    return parse_list_page(html)

def crawl_detail_page(url):
    print(f"🔍 抓取名人頁面：{url}")
    html = get_html(url)
    data = parse_birth_info(html)
    data["source_url"] = url
    return data

def batch_crawl(url_list):
    init_db()
    for idx, url in enumerate(url_list, 1):
        print(f"\n▶️ [{idx}/{len(url_list)}] 開始爬取：{url}")
        try:
            data = crawl_detail_page(url)
            if data.get("name") and data.get("birth_date"):
                print(f"✅ 成功解析：{data['name']} 出生日期：{data['birth_date']}")
                save_to_db(data)
            else:
                print(f"⚠️ 解析資料不完整，跳過：{data.get('name')}")
        except Exception as e:
            print(f"❌ 爬取失敗 {url}，錯誤：{e}")
        time.sleep(1)
