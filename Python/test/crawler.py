import requests
import sys
from bs4 import BeautifulSoup


def parse_astro_databank_table(html):
    soup = BeautifulSoup(html, 'html.parser')
    data = {
        "name": None,
        "gender": None,
        "birthname": None,
        "birth_date": None,
        "birth_time": None,
        "birth_place": None,
        "timezone": None,
        "rodden_rating": None,
        "source_url": None,
    }

    rows = soup.select("tbody > tr")
    for row in rows:
        label_td = row.find("td")
        if not label_td:
            continue
        label = label_td.get_text(strip=True).lower()

        value_td = label_td.find_next_sibling("td")
        if not value_td:
            continue

        if "name" in label:
            # 取名字欄位文字，但只要左邊的名字欄
            # 這裡用 value_td 裡的第一個 td 文字（避免抓到 Gender 的）
            inner_td = value_td.find("td")
            if inner_td:
                data["name"] = inner_td.get_text(strip=True)
            else:
                data["name"] = value_td.get_text(strip=True)
            # 性別從小字 a 旁文字抓
            gender_tag = value_td.select_one("small a[title='Help:Gender']")
            if gender_tag:
                gender_text = gender_tag.parent.get_text(strip=True)
                if ":" in gender_text:
                    data["gender"] = gender_text.split(":")[-1].strip()
        elif "birthname" in label:
            data["birthname"] = value_td.get_text(strip=True)
        elif "born on" in label or "born" in label:
            text = value_td.get_text(separator=" ", strip=True)
            parts = text.split(" at ")
            data["birth_date"] = parts[0].replace("born on", "").strip()
            if len(parts) > 1:
                time_part = parts[1].split(" ")[0]
                data["birth_time"] = time_part.strip()
        elif "place" in label:
            place_text = value_td.get_text(separator=" ", strip=True)
            place_main = place_text.split("  ")[0]
            data["birth_place"] = place_main.strip()
        elif "timezone" in label:
            data["timezone"] = value_td.get_text(strip=True)
        elif "data source" in label:
            # Rodden Rating 通常在內層 table，找出 AA、A、B... 等字串
            rr_tag = value_td.find(string=lambda text: text and "Rodden Rating" in text)
            if rr_tag:
                # 通常旁邊有 <b>AA</b> 這類文字，我們找兄弟元素
                b_tag = rr_tag.find_parent().find_next_sibling("b")
                if b_tag:
                    data["rodden_rating"] = b_tag.get_text(strip=True)
                else:
                    # 備用找法，找內部 b
                    b_tag2 = value_td.find("b")
                    if b_tag2:
                        data["rodden_rating"] = b_tag2.get_text(strip=True)
        else:
            continue

    return data

def main():
    urls = sys.argv[1:]
    if not urls:
        print("請提供至少一個 astro.com astro-databank 網址參數")
        sys.exit(1)

    for idx, url in enumerate(urls, 1):
        print(f"🔍 [{idx}/{len(urls)}] 正在解析：{url}")
        try:
            res = requests.get(url, timeout=10)
            res.raise_for_status()
            info = parse_astro_databank_table(res.text)
            info["source_url"] = url

            # 判斷資料是否完整，這邊簡單判斷 name 與 birth_date
            if not info["name"] or not info["birth_date"]:
                print(f"⚠️ 解析結果不完整，跳過儲存：{info}\n")
                continue

            print(f"✅ 解析成功：{info}\n")

        except Exception as e:
            print(f"❌ 網頁下載或解析失敗：{e}\n")

if __name__ == "__main__":
    main()
