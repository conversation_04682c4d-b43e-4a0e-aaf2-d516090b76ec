import sys
import time
from bs4 import BeautifulSoup
from selenium import webdriver
from selenium.common.exceptions import TimeoutException, WebDriverException
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import WebDriverWait
from webdriver_manager.chrome import ChromeDriverManager


def setup_driver():
    options = Options()
    options.add_argument('--headless=new')  # 無頭模式
    options.add_argument('--disable-gpu')
    options.add_argument('--no-sandbox')
    options.add_argument("--window-size=1920,1080")
    options.add_argument("--disable-blink-features=AutomationControlled")

    driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=options)
    return driver

def visit_page_with_retry(driver, url, retries=3, wait_time=10):
    for attempt in range(1, retries + 1):
        try:
            print(f"🔄 嘗試連線 {attempt}/{retries}：{url}")
            driver.get(url)
            # 等待頁面中 td b 元素出現，代表頁面已基本載入
            WebDriverWait(driver, wait_time).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "td b"))
            )
            return True
        except (TimeoutException, WebDriverException) as e:
            print(f"⚠️ 第 {attempt} 次連線失敗，原因：{e}")
            time.sleep(3)
    return False

def parse_person_data(html, url):
    soup = BeautifulSoup(html, 'html.parser')
    name_el = soup.select_one("td b")
    name = name_el.text.strip() if name_el else url.split("/")[-1]

    birthname = soup.find("td", class_="metadata-label", string="Birthname")
    birthname_value = birthname.find_next_sibling("td").text.strip() if birthname else ""

    birth_on = soup.find("td", string=lambda s: s and "born on" in s)
    birth_info = birth_on.find_next_sibling("td").text.strip() if birth_on else ""

    place_td = soup.find("td", string="Place")
    place_text = place_td.find_next_sibling("td").text.strip() if place_td else ""

    timezone_td = soup.find("td", string="Timezone")
    timezone = timezone_td.find_next_sibling("td").text.strip() if timezone_td else ""

    rodden_rating = soup.find("a", title="Help:RR")
    rating = rodden_rating.find_next("b").text.strip() if rodden_rating else ""

    return {
        "name": name,
        "birthname": birthname_value,
        "birth_info": birth_info,
        "place": place_text,
        "timezone": timezone,
        "rodden_rating": rating,
        "source_url": url
    }

def run_crawler(urls):
    driver = setup_driver()
    for idx, url in enumerate(urls, 1):
        print(f"\n🔍 [{idx}/{len(urls)}] 解析中：{url}")
        try:
            success = visit_page_with_retry(driver, url)
            if not success:
                print(f"❌ 無法連線到：{url}，已記錄")
                with open("failed_urls.txt", "a", encoding="utf-8") as f:
                    f.write(url + "\n")
                continue

            html = driver.page_source
            result = parse_person_data(html, url)
            if result['birth_info']:
                print("✅ 成功解析：", result)
            else:
                print("⚠️ 資料不完整，跳過：", result)
        except Exception as e:
            print(f"❌ 解析時發生例外：{e}")
    driver.quit()

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("請提供至少一個 Astro-Databank 網址作為參數")
    else:
        run_crawler(sys.argv[1:])
