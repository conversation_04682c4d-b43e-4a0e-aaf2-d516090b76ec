import re
import requests
from bs4 import BeautifulSoup


def get_soup(url):
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36"
    }
    resp = requests.get(url, headers=headers)
    resp.raise_for_status()
    return BeautifulSoup(resp.text, "html.parser")

def extract_birth_info(url):
    soup = get_soup(url)
    print(f"🔍 正在解析：{url}")

    # 先嘗試從infobox抓出生欄位
    birth_text = None
    info_box = soup.find("table", {"class": "infobox"})
    if info_box:
        rows = info_box.find_all("tr")
        for row in rows:
            th = row.find("th")
            td = row.find("td")
            if th and td and ("出生" in th.text or "Born" in th.text):
                birth_text = td.get_text(separator=" ", strip=True)
                print(f"從infobox找到出生資料：{birth_text}")
                break

    # 如果infobox沒抓到，再用全文搜尋出生相關段落
    if not birth_text:
        full_text = soup.get_text(separator="\n", strip=True)
        # 找包含 Born 或 出生 的行
        candidates = [line for line in full_text.split("\n") if re.search(r"\b(Born|出生)\b", line, re.I)]
        if candidates:
            # 取第一個可能是出生資料的段落
            birth_text = candidates[0]
            print(f"從全文找到出生資料：{birth_text}")

    if not birth_text:
        print("⚠️ 找不到任何出生資訊！")
        return None

    # 寬鬆的正則來擷取 日期、時間、地點
    pattern = re.compile(
        r"(?P<date>\d{1,2}\s\w+\s\d{4}|\w+\s\d{1,2},\s*\d{4})"    # 日期格式：15 July 1982 或 July 15, 1982
        r"(?:,?\s*(?P<time>\d{1,2}:\d{2}))?"                       # 時間：14:30（可選）
        r"(?:,?\s*(?:in|at)?\s*(?P<place>.+))?",                  # 地點（可選，in/at開頭可有可無）
        re.I
    )

    match = pattern.search(birth_text)
    if match:
        date = match.group("date")
        time = match.group("time") if match.group("time") else "未知"
        place = match.group("place").strip() if match.group("place") else "未知"
        print(f"✅ 解析結果：日期={date}, 時間={time}, 地點={place}")
        return {"date": date, "time": time, "place": place}
    else:
        print("⚠️ 無法解析出生日期、時間、地點")
        return None

if __name__ == "__main__":
    test_url = "https://www.astro.com/astro-databank/A,_Dominique"
    extract_birth_info(test_url)
