import sqlite3

DB_PATH = "astro_celebrities.db"

def init_db():
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    # 建立名人資料表
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS celebrities (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT UNIQUE,
        birth_date TEXT,
        birth_time TEXT,
        birth_place TEXT,
        astro_data TEXT,
        source_url TEXT UNIQUE
    )
    ''')

    # 建立爬蟲進度表，id固定為1方便更新
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS crawl_status (
        id INTEGER PRIMARY KEY CHECK (id = 1),
        last_crawled_url TEXT,
        last_crawled_person_url TEXT
    )
    ''')

    # 初始化爬蟲進度，只插入一次
    cursor.execute('''
    INSERT OR IGNORE INTO crawl_status (id, last_crawled_url, last_crawled_person_url)
    VALUES (1, NULL, NULL)
    ''')

    conn.commit()
    conn.close()
    print("✅ 資料庫與資料表建立完成")

if __name__ == "__main__":
    init_db()
