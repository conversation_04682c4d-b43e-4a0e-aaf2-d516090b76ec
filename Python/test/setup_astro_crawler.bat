@echo off
echo [🔮] 建立虛擬環境 venv...
python -m venv venv

echo [🚀] 啟動虛擬環境...
call venv\Scripts\activate.bat

echo [📦] 安裝必要套件：selenium、webdriver-manager、beautifulsoup4、requests、lxml...
pip install --upgrade pip
pip install selenium webdriver-manager beautifulsoup4 requests lxml

echo [🌟] 安裝完成！你已準備好召喚星盤資料啦！
echo [🔧] 若要啟動虛擬環境，請輸入：
echo call venv\Scripts\activate.bat
echo [👁️] 接下來可執行：
echo python crawler_selenium.py [網址1] [網址2] ...

pause
