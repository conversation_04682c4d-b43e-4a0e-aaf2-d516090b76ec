import re
import requests
import sqlite3
import time
from bs4 import BeautifulSoup

DB_PATH = "astro_celebrities.db"

def get_soup(url):
    headers = {
        "User-Agent": ("Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
                       "AppleWebKit/537.36 (KHTML, like Gecko) "
                       "Chrome/114.0.0.0 Safari/537.36")
    }
    resp = requests.get(url, headers=headers, timeout=10)
    resp.raise_for_status()
    return BeautifulSoup(resp.text, "html.parser")

def dms_to_decimal(dms_str):
    """
    把類似 "48n33" 或 "3e18" 這種格式轉成十進制度數
    格式說明：度 + 方向字母（n,s,e,w） + 分
    例：48n33 = 48度33分北 → 48 + 33/60 = 48.55
    """
    m = re.match(r"(\d+)([nsew])(\d+)", dms_str.lower())
    if not m:
        return None
    deg = int(m.group(1))
    direction = m.group(2)
    minutes = int(m.group(3))
    decimal = deg + minutes / 60
    if direction in ['s', 'w']:
        decimal = -decimal
    return round(decimal, 6)

def parse_birth_info(soup):
    data = {
        "name": None,
        "gender": None,
        "birthname": None,
        "birth_date": None,
        "birth_time": None,
        "birth_place": None,
        "timezone": None,
        "rodden_rating": None,
        "latitude": None,
        "longitude": None,
    }

    # 名字
    h1 = soup.select_one("h1#firstHeading")
    if h1:
        data["name"] = h1.text.strip()

    # 網頁內容主要區塊
    body = soup.select_one("#bodyContent")
    if not body:
        return data

    # 改用 string 參數，避免警告
    rating = body.find(string=lambda t: t and "Rodden Rating" in t)
    if rating:
        # 通常會是 "Rodden Rating AA"
        data["rodden_rating"] = rating.strip().split()[-1]

    birthname_tag = body.find(string=lambda t: t and "Birthname" in t)
    if birthname_tag:
        # 範例格式： "Birthname Dominique Ané"
        data["birthname"] = birthname_tag.replace("Birthname", "").strip()

    gender_tag = body.find(string=lambda t: t and "Gender:" in t)
    if gender_tag:
        data["gender"] = gender_tag.replace("Gender:", "").strip()

    born_on_tag = body.find(string=lambda t: t and "born on" in t)
    if born_on_tag:
        import re
        m = re.search(r"born on\s*([\d]{1,2} [A-Za-z]+ \d{4}) at ([\d:apm\s\(\)=]+)", born_on_tag)
        if m:
            data["birth_date"] = m.group(1)
            data["birth_time"] = m.group(2).strip()

    place_tag = body.find(string=lambda t: t and "Place" in t)
    if place_tag:
        # 可能會是 "Place Provins, France, 48n33, 3e18"
        place_text = place_tag.replace("Place", "").strip()
        data["birth_place"] = place_text

        # 解析緯經度
        import re
        lat_lon_match = re.search(r"(\d{1,2}n\d{1,2}),\s*(\d{1,2}e\d{1,2})", place_text, re.IGNORECASE)
        if lat_lon_match:
            # 轉成度數 (簡單版，需更完善處理)
            lat_str = lat_lon_match.group(1)  # 例：48n33
            lon_str = lat_lon_match.group(2)  # 例：3e18

            def dms_to_dec(dms):
                # 簡單轉換，N正S負, E正W負
                deg = int(dms[:-3])
                min_ = int(dms[-2:])
                dec = deg + min_ / 60
                if 's' in dms.lower() or 'w' in dms.lower():
                    dec = -dec
                return dec

            data["latitude"] = dms_to_dec(lat_str.lower())
            data["longitude"] = dms_to_dec(lon_str.lower())

    tz_tag = body.find(string=lambda t: t and "Timezone" in t)
    if tz_tag:
        data["timezone"] = tz_tag.replace("Timezone", "").strip()

    return data

def save_to_db(data):
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    cursor.execute('''
    CREATE TABLE IF NOT EXISTS celebrities (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT UNIQUE,
        gender TEXT,
        birthname TEXT,
        birth_date TEXT,
        birth_time TEXT,
        birth_place TEXT,
        timezone TEXT,
        rodden_rating TEXT,
        latitude REAL,
        longitude REAL,
        source_url TEXT
    )
    ''')

    try:
        cursor.execute('''
        INSERT INTO celebrities
        (name, gender, birthname, birth_date, birth_time, birth_place, timezone, rodden_rating, latitude, longitude, source_url)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ON CONFLICT(name) DO UPDATE SET
            gender=excluded.gender,
            birthname=excluded.birthname,
            birth_date=excluded.birth_date,
            birth_time=excluded.birth_time,
            birth_place=excluded.birth_place,
            timezone=excluded.timezone,
            rodden_rating=excluded.rodden_rating,
            latitude=excluded.latitude,
            longitude=excluded.longitude,
            source_url=excluded.source_url
        ''', (
            data.get("name"),
            data.get("gender"),
            data.get("birthname"),
            data.get("birth_date"),
            data.get("birth_time"),
            data.get("birth_place"),
            data.get("timezone"),
            data.get("rodden_rating"),
            data.get("latitude"),
            data.get("longitude"),
            data.get("source_url")
        ))
        print(f"📝 已寫入資料庫：{data.get('name')}")
    except Exception as e:
        print(f"❌ 寫入失敗：{e}")

    conn.commit()
    conn.close()

def batch_crawl(url_list):
    for i, url in enumerate(url_list, start=1):
        print(f"\n🔍 [{i}/{len(url_list)}] 正在解析：{url}")
        try:
            soup = get_soup(url)
            data = parse_birth_info(soup)
            data["source_url"] = url
            if data.get("name") and data.get("birth_date"):
                print(f"✅ 解析結果：{data}")
                save_to_db(data)
            else:
                print(f"⚠️ 解析結果不完整，跳過儲存：{data}")
            time.sleep(1)  # 避免請求過快
        except Exception as e:
            print(f"❌ 爬取失敗 {url}，原因：{e}")

def main():
    urls = [
        "https://www.astro.com/astro-databank/A,_Dominique",
        "https://www.astro.com/astro-databank/A-Trak",
        "https://www.astro.com/astro-databank/A._Chal"
    ]
    batch_crawl(urls)

if __name__ == "__main__":
    main()
