import csv
import requests
import time
from bs4 import BeautifulSoup

BASE_URL = "https://www.astro.com"
LIST_URL = BASE_URL + "/astro-databank/"

# 預設只爬 A-C，後續可擴充
#LETTERS = ["A", "B", "C"]
LETTERS = ["A"]

def get_people_links(letter):
    url = f"{LIST_URL}Category:{letter}"
    res = requests.get(url)
    soup = BeautifulSoup(res.text, "html.parser")
    links = soup.select("div.mw-category a")
    return [BASE_URL + link["href"] for link in links]

def parse_person_page(url):
    res = requests.get(url)
    soup = BeautifulSoup(res.text, "html.parser")

    title = soup.select_one("h1").text.strip()
    info = soup.select_one(".infobox")
    text = soup.select_one("div.mw-parser-output").text

    birth_date, birth_time, birth_place, rating = "", "", "", ""

    if info:
        for row in info.find_all("tr"):
            if "Born" in row.text:
                parts = row.text.split("\n")
                for part in parts:
                    if "Born" in part:
                        continue
                    if "," in part and not birth_date:
                        birth_date = part.strip()
                    elif ":" in part:
                        birth_time = part.strip()
                    elif not birth_place and part.strip() and not part.strip().isdigit():
                        birth_place = part.strip()
            if "Rodden Rating" in row.text:
                rating = row.find("td").text.strip()

    # 前 2 段文字作為簡介
    description = soup.select_one("p")
    desc_text = description.text.strip().replace("\n", " ") if description else ""

    return {
        "name": title,
        "birth_date": birth_date,
        "birth_time": birth_time,
        "birth_place": birth_place,
        "rating": rating,
        "astro_url": url,
        "description": desc_text,
    }

def main():
    with open("celebrities.csv", "w", newline="", encoding="utf-8") as f:
        writer = csv.DictWriter(f, fieldnames=[
            "name", "birth_date", "birth_time", "birth_place",
            "rating", "astro_url", "description"
        ])
        writer.writeheader()

        for letter in LETTERS:
            print(f"📖 爬取字母：{letter}")
            links = get_people_links(letter)
            for link in links:
                try:
                    person = parse_person_page(link)
                    writer.writerow(person)
                    print(f"✔ {person['name']} 儲存成功")
                    time.sleep(2)  # 減少伺服器負擔
                except Exception as e:
                    print(f"❌ 錯誤：{link} - {e}")

    print("✅ 全部完成，已儲存到 celebrities.csv")

if __name__ == "__main__":
    main()
