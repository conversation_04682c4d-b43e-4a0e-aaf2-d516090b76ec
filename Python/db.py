# db.py
import sqlite3

from config import DB_PATH


def init_db():
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS celebrities (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT UNIQUE,
            gender TEXT,
            birthname TEXT,
            birth_date TEXT,
            birth_time TEXT,
            birth_place TEXT,
            timezone TEXT,
            rodden_rating TEXT,
            latitude REAL,
            longitude REAL,
            source_url TEXT
        )
    ''')
    conn.commit()
    conn.close()

def save_to_db(data):
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    cursor.execute('''
        INSERT OR REPLACE INTO celebrities
        (name, gender, birthname, birth_date, birth_time, birth_place, timezone, rodden_rating, latitude, longitude, source_url)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ''', (
        data.get("name"),
        data.get("gender"),
        data.get("birthname"),
        data.get("birth_date"),
        data.get("birth_time"),
        data.get("birth_place"),
        data.get("timezone"),
        data.get("rodden_rating"),
        data.get("latitude"),
        data.get("longitude"),
        data.get("source_url")
    ))
    conn.commit()
    conn.close()
