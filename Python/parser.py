# parser.py
import re

from utils import dms_to_decimal


def parse_birth_info(soup):
    data = {
        "name": None,
        "gender": None,
        "birthname": None,
        "birth_date": None,
        "birth_time": None,
        "birth_place": None,
        "timezone": None,
        "rodden_rating": None,
        "latitude": None,
        "longitude": None,
        "source_url": None
    }

    h1 = soup.select_one("h1#firstHeading")
    if h1:
        data["name"] = h1.text.strip()

    body = soup.select_one("#bodyContent")
    if not body:
        return data

    rating = body.find(string=lambda t: t and "Rodden Rating" in t)
    if rating:
        data["rodden_rating"] = rating.strip().split()[-1]

    birthname_tag = body.find(string=lambda t: t and "Birthname" in t)
    if birthname_tag:
        data["birthname"] = birthname_tag.replace("Birthname", "").strip()

    gender_tag = body.find(string=lambda t: t and "Gender:" in t)
    if gender_tag:
        data["gender"] = gender_tag.replace("Gender:", "").strip()

    born_on_tag = body.find(string=lambda t: t and "born on" in t)
    if born_on_tag:
        m = re.search(r"born on\s*([\d]{1,2} [A-Za-z]+ \d{4}) at ([\d:apm\s\(\)=]+)", born_on_tag)
        if m:
            data["birth_date"] = m.group(1)
            data["birth_time"] = m.group(2).strip()

    place_tag = body.find(string=lambda t: t and "Place" in t)
    if place_tag:
        place_text = place_tag.replace("Place", "").strip()
        data["birth_place"] = place_text

        lat_lon_match = re.search(r"(\d{1,2}n\d{1,2}),\s*(\d{1,2}e\d{1,2})", place_text, re.IGNORECASE)
        if lat_lon_match:
            lat_str = lat_lon_match.group(1)
            lon_str = lat_lon_match.group(2)
            data["latitude"] = dms_to_decimal(lat_str.lower())
            data["longitude"] = dms_to_decimal(lon_str.lower())

    tz_tag = body.find(string=lambda t: t and "Timezone" in t)
    if tz_tag:
        data["timezone"] = tz_tag.replace("Timezone", "").strip()

    return data
