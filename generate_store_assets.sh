#!/bin/bash

# 星真占星 AstReal - 商店上架圖片生成腳本
# 此腳本會生成 Google Play Store 和 Apple App Store 所需的所有圖片

set -e

echo "🌟 開始生成星真占星商店上架圖片..."

# 創建輸出目錄
mkdir -p store_assets/google_play
mkdir -p store_assets/app_store
mkdir -p store_assets/icons

# 檢查是否安裝了 ImageMagick
if ! command -v convert &> /dev/null; then
    echo "❌ 需要安裝 ImageMagick 來處理圖片"
    echo "macOS: brew install imagemagick"
    echo "Ubuntu: sudo apt-get install imagemagick"
    exit 1
fi

# 檢查原始圖片是否存在
if [ ! -f "assets/images/flutter_launcher_icons.png" ]; then
    echo "❌ 找不到原始圖片: assets/images/flutter_launcher_icons.png"
    exit 1
fi

echo "📱 生成應用程式圖示..."

# Google Play Store 應用程式圖示 (512x512)
convert "assets/images/flutter_launcher_icons.png" -resize 512x512 "store_assets/google_play/app_icon_512.png"

# Apple App Store 應用程式圖示 (1024x1024)
convert "assets/images/flutter_launcher_icons.png" -resize 1024x1024 "store_assets/app_store/app_icon_1024.png"

# 各種尺寸的圖示
convert "assets/images/flutter_launcher_icons.png" -resize 192x192 "store_assets/icons/icon_192.png"
convert "assets/images/flutter_launcher_icons.png" -resize 256x256 "store_assets/icons/icon_256.png"
convert "assets/images/flutter_launcher_icons.png" -resize 128x128 "store_assets/icons/icon_128.png"
convert "assets/images/flutter_launcher_icons.png" -resize 96x96 "store_assets/icons/icon_96.png"
convert "assets/images/flutter_launcher_icons.png" -resize 72x72 "store_assets/icons/icon_72.png"
convert "assets/images/flutter_launcher_icons.png" -resize 48x48 "store_assets/icons/icon_48.png"

echo "🎨 生成精選圖片模板..."

# Google Play Store 精選圖片 (1024x500)
# 創建一個帶有應用名稱和圖示的精選圖片
convert -size 1024x500 xc:"#3F51B5" \
    \( "assets/images/flutter_launcher_icons.png" -resize 200x200 \) \
    -gravity west -geometry +100+0 -composite \
    -font "Arial-Bold" -pointsize 48 -fill white \
    -gravity center -geometry +150+0 \
    -annotate +0-50 "星真占星" \
    -font "Arial" -pointsize 24 -fill "#F5A623" \
    -annotate +0+20 "AstReal" \
    -font "Arial" -pointsize 18 -fill white \
    -annotate +0+60 "專業占星分析應用" \
    "store_assets/google_play/feature_graphic_1024x500.png"

echo "📸 創建螢幕截圖模板說明..."

# 創建螢幕截圖說明文件
cat > store_assets/screenshot_guide.md << 'EOF'
# 螢幕截圖指南

## Google Play Store 螢幕截圖要求：
- 手機螢幕截圖：至少 2 張，最多 8 張
- 建議尺寸：1080x1920 px 或 1080x2340 px
- 格式：PNG 或 JPG
- 內容：展示應用的主要功能

## Apple App Store 螢幕截圖要求：
- iPhone 6.7" 顯示器：1290x2796 px
- iPhone 6.5" 顯示器：1242x2688 px  
- iPhone 5.5" 顯示器：1242x2208 px
- iPad 12.9" 顯示器：2048x2732 px
- iPad 11" 顯示器：1668x2388 px

## 建議的螢幕截圖內容：
1. 主頁面 - 展示應用的整體界面
2. 星盤頁面 - 展示核心功能
3. 出生資料頁面 - 展示資料管理
4. AI 解讀頁面 - 展示智能分析
5. 設定頁面 - 展示個人化選項

## 如何獲取螢幕截圖：
1. 在模擬器或真機上運行應用
2. 導航到要截圖的頁面
3. 使用系統截圖功能
4. 確保截圖清晰且展示關鍵功能
EOF

echo "✅ 商店圖片生成完成！"
echo ""
echo "📁 生成的文件位置："
echo "   Google Play Store:"
echo "   - 應用程式圖示: store_assets/google_play/app_icon_512.png"
echo "   - 精選圖片: store_assets/google_play/feature_graphic_1024x500.png"
echo ""
echo "   Apple App Store:"
echo "   - 應用程式圖示: store_assets/app_store/app_icon_1024.png"
echo ""
echo "   其他圖示: store_assets/icons/"
echo ""
echo "📋 下一步："
echo "1. 查看生成的圖片"
echo "2. 根據需要調整精選圖片的設計"
echo "3. 使用模擬器或真機截取應用螢幕截圖"
echo "4. 參考 store_assets/screenshot_guide.md 獲取螢幕截圖"
echo ""
echo "🚀 準備上架到商店！"
