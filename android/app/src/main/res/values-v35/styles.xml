<?xml version="1.0" encoding="utf-8"?>
<resources>
        <!-- Theme applied to the Android Window while the process is starting when the OS's Dark Mode setting is off -->
        <style name="LaunchTheme" parent="@android:style/Theme.Light.NoTitleBar">
                <item name="android:forceDarkAllowed">false</item>
                <item name="android:windowFullscreen">true</item>
                <item name="android:windowDrawsSystemBarBackgrounds">true</item>
                <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
                <item name="android:windowSplashScreenBackground">#3F51B5</item>
                <item name="android:windowSplashScreenIconBackgroundColor">#3F51B5</item>
                <item name="android:navigationBarColor">#FFFFFF</item>
                <item name="android:windowOptOutEdgeToEdgeEnforcement">true</item>
                <item name="android:windowLightStatusBar">false</item>
        </style>
</resources>