
教學
https://medium.com/flutter/must-try-use-firebase-to-host-your-flutter-app-on-the-web-852ee533a469


flutter clean

1.firebase init hosting 輸入指令
firebase init


更新
npm install -g firebase-tools


需注意資料夾的選擇
enter
build/web
as the public directory and press enter

運行以下命令生成 release 版本的構建
2.flutter build web

署到站點
3.firebase deploy

firebase deploy --only hosting:astreal

切換專案
firebase use --add

Next, enter build/web as the public directory and press enter,
then enter y (for yes) to select the single page app option:

Step 4: Build and deploy!
Build your app for web:

$
flutter build web
This builds the necessary files in <root-directory>/build/web.
firebase deploy


pub upgrade sweph
flutter pub add sweph

設定啟動畫面
dart run flutter_native_splash:create





在 Flutter Web 開發中，要讓別人測試你的應用，可以考慮以下幾種方式：

1. 使用 Firebase Hosting（推薦）
   Firebase Hosting 提供免費的 HTTPS 託管，適合快速部署測試版本。

步驟：

安裝 Firebase CLI：
npm install -g firebase-tools

在 Flutter 專案中執行：
flutter build web

使用 Release 模式
flutter build web --release

在 build 時啟用混淆（需要 Dart 3.1+）：
flutter build web --release --dart-define=DART2JS_MINIFY=true

啟用 Dart Obfuscation
flutter build web --release --dart-define=DART2JS_EXTRA_OPTIONS="--minify"

初始化 Firebase Hosting：
firebase init hosting

選擇你的 Firebase 專案
設定 public 資料夾為 build/web
選擇 No（不設定單頁應用程式重寫）

部署：
firebase deploy


先透過預覽管道測試及分享變更，然後再發布
firebase hosting:channel:deploy preview_name

之後，Firebase 會提供一個測試網址，你可以直接分享給別人測試。

1. 查看已部署的版本
   firebase hosting:versions:list

2. 刪除特定版本
   firebase hosting:versions:delete version_id_12345

firebase hosting:versions:delete version_id_1 version_id_2 version_id_3

3. 刪除所有舊版本（批量刪除）
   firebase hosting:versions:list | ForEach-Object { ($_ -split '\s+')[0] } | ForEach-Object { firebase hosting:channel:delete $_ }
   firebase hosting:versions:list --limit=1000 → 列出最多 1000 個 Hosting 版本
   ForEach-Object { ($_ -split '\s+')[0] } → 取出版本 ID
   firebase hosting:versions:delete $_ → 刪除每個版本

4. 清除目前部署的版本（回復為空白狀態）
   如果你想讓 Firebase Hosting 回復為未部署狀態（沒有任何網站），請執行：
   firebase hosting:disable

firebase deploy --only functions


1. 如何查看 Flutter Web 的 assets/？
   ✅ 方法 1：透過瀏覽器開發者工具 (DevTools)
   開啟 Chrome 開發者工具（F12 或 Ctrl+Shift+I）
   前往 Network > Fetch/XHR
   搜尋 assets/ 內的檔案
   例如 https://yourwebsite.com/assets/config.json
   任何人都可以開啟該 URL，下載該檔案。
   ✅ 方法 2：直接訪問 URL
   只要知道你的 Flutter Web 部署網址，可以直接在瀏覽器輸入：
   https://yourwebsite.com/assets/你的檔案.json

生成原生啟動畫面
dart run flutter_native_splash:create


flutter clean
flutter pub get
flutter run