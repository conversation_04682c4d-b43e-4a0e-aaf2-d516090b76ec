當然可以！以下是為 Flutter 專案設計的 **跨平台打包腳本**，包含 Android APK/AAB、iOS（僅限 macOS）、Web 和桌面平台的打包指令。你可以將這些指令整理進一個 shell script，來加速並統一你的打包流程。

---

## 🛠 建議使用 shell script（macOS / Linux / WSL 適用）

建立檔案：`build_all.sh`

```bash
#!/bin/bash

set -e  # 有錯誤就中止腳本

echo "🔧 Cleaning build cache..."
flutter clean
flutter pub get

echo "📱 Building Android APK..."
flutter build apk --release

echo "📦 Building Android AAB..."
flutter build appbundle --release

if [[ "$OSTYPE" == "darwin"* ]]; then
  echo "🍎 Building iOS (macOS only)..."
  flutter build ios --release
else
  echo "🚫 Skipping iOS build (not running on macOS)"
fi

echo "🌐 Building Web..."
flutter build web

echo "🖥️ Building Desktop (macOS / Windows / Linux)..."
case "$OSTYPE" in
  darwin*)  flutter build macos ;;
  linux*)   flutter build linux ;;
  msys*)    flutter build windows ;;  # Git Bash for Windows
  *)        echo "⚠️ Unsupported desktop platform for this script" ;;
esac

echo "✅ Build complete!"
```

> 給它執行權限後使用：

```bash
chmod +x build_all.sh
./build_all.sh
```

---

## 🧰 Windows 用戶建議使用 PowerShell 腳本

建立 `build_all.ps1`：

```powershell
Write-Host "🔧 Cleaning build cache..."
flutter clean
flutter pub get

Write-Host "📱 Building Android APK..."
flutter build apk --release

Write-Host "📦 Building Android AAB..."
flutter build appbundle --release

Write-Host "🌐 Building Web..."
flutter build web

Write-Host "🖥️ Building Windows..."
flutter build windows

Write-Host "✅ Build complete!"
```

執行方式（在 PowerShell 中）：

```powershell
.\build_all.ps1
```

---

## 🧠 Bonus：帶參數版（選擇打包平台）

也可以設計支援參數的版本，例如：

```bash
./build_all.sh android      # 只打包 Android
./build_all.sh ios          # 只打包 iOS
./build_all.sh all          # 打包全部
```

我可以幫你擴充這種版本，要嗎？
或者如果你是用 CI 工具（GitHub Actions / Bitrise），我也能幫你寫 workflow 檔喔 😎
