# 啟動畫面統一優化說明

## 📱 功能概述

統一應用程式的啟動畫面設計，移除深藍色背景版本，只保留白底加Logo的啟動畫面，確保在所有平台和主題模式下都顯示一致的白色背景啟動畫面。

## 🎯 問題分析

### 原始問題
- **雙重啟動畫面**：應用程式會根據系統主題顯示兩種不同的啟動畫面
  - 深色模式：深藍色背景 + Logo
  - 淺色模式：白色背景 + Logo
- **用戶體驗不一致**：不同主題模式下的啟動體驗差異較大
- **設計複雜性**：需要維護兩套啟動畫面資源

### 改進目標
- **統一視覺體驗**：所有情況下都顯示白底加Logo的啟動畫面
- **簡化維護**：只需維護一套啟動畫面資源
- **品牌一致性**：確保啟動畫面與應用整體設計風格一致

## 🛠️ 技術實作

### 1. Web 平台優化

#### 修改前的配置
```html
<picture id="splash">
    <source srcset="splash/img/light-1x.png 1x, splash/img/light-2x.png 2x, splash/img/light-3x.png 3x, splash/img/light-4x.png 4x" media="(prefers-color-scheme: light)">
    <source srcset="splash/img/dark-1x.png 1x, splash/img/dark-2x.png 2x, splash/img/dark-3x.png 3x, splash/img/dark-4x.png 4x" media="(prefers-color-scheme: dark)">
    <img class="center" aria-hidden="true" src="splash/img/light-1x.png" alt="">
</picture>
```

**問題**：
- 使用 `media="(prefers-color-scheme: light/dark)"` 根據系統主題切換
- 深色模式會顯示 `dark-*.png` 圖片（深藍色背景）
- 淺色模式會顯示 `light-*.png` 圖片（白色背景）

#### 修改後的配置
```html
<picture id="splash">
    <img class="center" aria-hidden="true" src="splash/img/light-1x.png" alt="">
</picture>
```

**改進**：
- 移除了 `<source>` 標籤和媒體查詢
- 統一使用 `light-1x.png`（白色背景版本）
- 所有情況下都顯示相同的啟動畫面

### 2. Android 平台優化

#### 修改前的配置
```xml
<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <item>
        <bitmap android:gravity="fill" android:src="@drawable/background"/>
    </item>
    <item>
        <bitmap android:gravity="center" android:src="@drawable/splash"/>
    </item>
</layer-list>
```

**問題**：
- 使用 `@drawable/background` 圖片作為背景
- 可能是深藍色背景圖片
- 需要額外的背景圖片資源

#### 修改後的配置
```xml
<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <item>
        <shape android:shape="rectangle">
            <solid android:color="#FFFFFF"/>
        </shape>
    </item>
    <item>
        <bitmap android:gravity="center" android:src="@drawable/splash"/>
    </item>
</layer-list>
```

**改進**：
- 使用 `<shape>` 和 `<solid>` 定義純白色背景
- 移除對 `@drawable/background` 圖片的依賴
- 減少資源文件，提高載入速度

#### 修改的文件
1. `android/app/src/main/res/drawable/launch_background.xml`
2. `android/app/src/main/res/drawable-v21/launch_background.xml`

### 3. iOS 平台檢查

#### 當前配置
```xml
<color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
```

**狀態**：
- iOS 的 LaunchScreen.storyboard 已經設定為白色背景
- `backgroundColor` 設為 RGB(1,1,1) = 白色
- 無需修改

## ✨ 優化效果

### 1. 統一的視覺體驗

#### 修改前
```
系統淺色模式 → 白色背景啟動畫面
系統深色模式 → 深藍色背景啟動畫面
```

#### 修改後
```
系統淺色模式 → 白色背景啟動畫面
系統深色模式 → 白色背景啟動畫面
```

### 2. 跨平台一致性

| 平台 | 修改前 | 修改後 |
|------|--------|--------|
| Web | 根據系統主題切換 | 統一白色背景 |
| Android | 可能有深色背景 | 統一白色背景 |
| iOS | 已是白色背景 | 保持白色背景 |

### 3. 資源優化

#### Web 平台
- **減少圖片依賴**：不再需要 `dark-*.png` 系列圖片
- **簡化 HTML**：移除複雜的媒體查詢邏輯
- **提高載入速度**：減少條件判斷和資源載入

#### Android 平台
- **移除背景圖片**：不再需要 `@drawable/background`
- **使用向量背景**：純色背景載入更快
- **減少 APK 大小**：移除不必要的圖片資源

## 🎨 設計理念

### 1. 品牌一致性
- **主色調**：白色背景符合應用的整體設計風格
- **簡潔設計**：純色背景 + Logo 的簡潔設計
- **專業形象**：統一的視覺體驗提升品牌專業度

### 2. 用戶體驗
- **預期一致**：用戶每次啟動都看到相同的畫面
- **視覺舒適**：白色背景在各種環境下都較為舒適
- **載入快速**：簡化的資源配置提高啟動速度

### 3. 維護便利
- **單一版本**：只需維護一套啟動畫面設計
- **更新簡單**：修改啟動畫面時只需更新一個版本
- **測試容易**：不需要測試多種主題模式下的效果

## 🔧 技術細節

### 1. Web 平台實作

#### HTML 結構簡化
```html
<!-- 修改前：複雜的媒體查詢 -->
<picture id="splash">
    <source srcset="..." media="(prefers-color-scheme: light)">
    <source srcset="..." media="(prefers-color-scheme: dark)">
    <img class="center" aria-hidden="true" src="splash/img/light-1x.png" alt="">
</picture>

<!-- 修改後：簡單的圖片標籤 -->
<picture id="splash">
    <img class="center" aria-hidden="true" src="splash/img/light-1x.png" alt="">
</picture>
```

#### CSS 樣式保持
- 保持原有的 `.center` 樣式類別
- 保持原有的 JavaScript 移除邏輯
- 不影響啟動畫面的移除機制

### 2. Android 平台實作

#### XML 層級結構
```xml
<layer-list>
    <!-- 第一層：純白色背景 -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="#FFFFFF"/>
        </shape>
    </item>
    
    <!-- 第二層：Logo 圖片 -->
    <item>
        <bitmap android:gravity="center" android:src="@drawable/splash"/>
    </item>
</layer-list>
```

#### 顏色定義
- 使用標準的十六進制顏色碼 `#FFFFFF`
- 確保在所有 Android 版本上都能正確顯示
- 與 iOS 和 Web 平台的白色背景保持一致

### 3. iOS 平台檢查

#### Storyboard 配置
```xml
<color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
```

- RGB 值 (1,1,1) 對應純白色
- 使用 sRGB 色彩空間確保顏色準確性
- 已經符合統一白色背景的要求

## ✅ 完成狀態

- [x] **Web 平台**：移除深色模式啟動畫面，統一使用白色背景
- [x] **Android 平台**：修改啟動背景為純白色，移除背景圖片依賴
- [x] **iOS 平台**：確認已使用白色背景，無需修改
- [x] **跨平台一致性**：確保所有平台都顯示相同的白色背景啟動畫面
- [x] **資源優化**：簡化配置文件，減少不必要的資源依賴
- [x] **文件記錄**：撰寫完整的優化說明文件

## 🎯 優化效果

### 1. 用戶體驗改進
- ✅ **一致性**：所有情況下都顯示相同的啟動畫面
- ✅ **簡潔性**：白色背景 + Logo 的簡潔設計
- ✅ **專業性**：統一的視覺體驗提升應用專業度

### 2. 開發維護改進
- ✅ **簡化配置**：移除複雜的主題切換邏輯
- ✅ **減少資源**：不再需要維護多套啟動畫面
- ✅ **提高效率**：簡化的配置提高載入速度

### 3. 品牌形象改進
- ✅ **品牌一致**：啟動畫面與應用整體設計風格一致
- ✅ **視覺統一**：跨平台的視覺體驗統一
- ✅ **用戶認知**：建立一致的品牌認知

## 🔮 未來考慮

### 可能的改進
1. **動畫效果**：為啟動畫面添加淡入淡出動畫
2. **載入進度**：顯示應用載入進度指示器
3. **個人化**：根據用戶偏好自訂啟動畫面
4. **季節主題**：在特殊節日顯示主題化的啟動畫面

### 技術優化
1. **預載入**：在啟動畫面顯示期間預載入關鍵資源
2. **快取優化**：優化啟動畫面資源的快取策略
3. **效能監控**：監控啟動畫面的顯示時間和效能
4. **A/B 測試**：測試不同啟動畫面設計的用戶反應

## 📝 使用說明

### 對用戶的改進
- **統一體驗**：無論在什麼情況下都看到相同的啟動畫面
- **視覺舒適**：白色背景在各種環境下都較為舒適
- **載入快速**：簡化的配置提高應用啟動速度

### 對開發者的好處
- **維護簡單**：只需維護一套啟動畫面設計
- **測試容易**：不需要測試多種主題模式
- **更新方便**：修改啟動畫面時只需更新一個版本
- **資源節省**：減少不必要的圖片和配置文件

這次優化完美地統一了啟動畫面的視覺體驗，為用戶提供了一致、簡潔、專業的應用啟動體驗！
