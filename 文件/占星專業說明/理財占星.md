單一公司
界行向運法 
界主星配置法
單一市場的每月預測
SR 的界行向運法

Janus
Delphic Oracle


/// 星座赤經上升時間表
/// 表示在不同緯度下，各星座通過赤經所需的時間及每度所需時間
class AscensionTable 
/// 星座赤經上升時間表 [緯度][星座對][數據類型]
/// 緯度: 0°-39°
/// 星座對: 0=♈︎♓︎, 1=♉︎♒︎, 2=♊︎♑︎, 3=♋︎♐︎, 4=♌︎♏︎, 5=♍︎♎︎
/// 數據類型: 0=走完星座所需的赤經上升時間, 1=換算成一度要走多久

利用 星座赤經上升時間表
來判斷上升落在哪個界主星
表裡可以知道每個星座要走多久
先知道是哪個星座 再透過星座去查表
表裡可以查到每一度要走幾天
就可以計算出來一個走道各界所需時間的表

新增界主星配置法實作方式
步驟
1.取得上升星座
2.取得上升界主星
3.利用赤經上升時間表來抓出這個星座與緯度每一度(單位是天)需要多少時間
4.取得時間後計算到下一個界是需要多少時間與準確日期
