# 占星應用中的多重篩選器使用說明

## 📋 功能概述

占星多重篩選器是一個強大的工具，允許用戶根據各種占星條件來篩選和分析星盤資料。支援複雜的多條件組合篩選，幫助用戶快速找到符合特定占星特徵的星盤。

## 🌟 主要功能

### 1. 篩選條件類型

#### 🪐 行星所屬星座篩選
- **功能**：篩選特定行星位於指定星座的星盤
- **範例**：
  - 太陽在獅子座
  - 水星在風象星座（雙子、天秤、水瓶）
  - 金星不在火象星座

#### 🏠 行星落入宮位篩選
- **功能**：篩選特定行星位於指定宮位的星盤
- **範例**：
  - 金星在第7宮
  - 火星在第10宮
  - 水星在第3或第9宮

#### 🌀 宮位所落入星座篩選
- **功能**：篩選特定宮位頭落入指定星座的星盤
- **範例**：
  - 第一宮在牡羊座
  - 第二宮在土象星座
  - 第十宮不在水象星座

### 2. 篩選操作符

- **等於**：精確匹配指定條件
- **不等於**：排除指定條件
- **包含**：匹配多個條件中的任一個
- **不包含**：排除多個條件中的所有

### 3. 邏輯組合

#### 條件內邏輯
- **且 (AND)**：所有條件都必須滿足
- **或 (OR)**：任一條件滿足即可

#### 組間邏輯
- **且 (AND)**：所有篩選組都必須滿足
- **或 (OR)**：任一篩選組滿足即可

## 🚀 使用方法

### 基本使用流程

1. **開啟篩選器頁面**
   ```dart
   Navigator.push(
     context,
     MaterialPageRoute(
       builder: (context) => const ChartFilterPage(),
     ),
   );
   ```

2. **創建新篩選器**
   - 點擊右下角的「+」按鈕
   - 輸入篩選器名稱

3. **添加篩選條件**
   - 選擇篩選類型（行星在星座/行星在宮位/宮位在星座）
   - 選擇操作符（等於/不等於/包含/不包含）
   - 設定具體條件值

4. **設定邏輯關係**
   - 條件間關係：且/或
   - 組間關係：且/或

5. **應用篩選**
   - 點擊「應用篩選」按鈕
   - 查看篩選結果

### 進階使用技巧

#### 1. 元素篩選
使用「包含」操作符配合元素分類：
- 火象星座：牡羊、獅子、射手
- 土象星座：金牛、處女、摩羯
- 風象星座：雙子、天秤、水瓶
- 水象星座：巨蟹、天蠍、雙魚

#### 2. 性質篩選
- 基本星座：牡羊、巨蟹、天秤、摩羯
- 固定星座：金牛、獅子、天蠍、水瓶
- 變動星座：雙子、處女、射手、雙魚

#### 3. 宮位類型篩選
- 始宮（角宮）：第1、4、7、10宮
- 續宮：第2、5、8、11宮
- 果宮：第3、6、9、12宮

## 📝 實用範例

### 範例1：火象太陽
```
篩選條件：
- 太陽 包含 [牡羊座, 獅子座, 射手座]

用途：找出所有火象太陽的人，分析其共同特質
```

### 範例2：溝通型水星
```
篩選條件組：
- 水星 包含 [雙子座, 天秤座, 水瓶座] 且
- 水星 包含 [第3宮, 第9宮]

邏輯：且 (AND)
用途：找出水星在風象星座且位於溝通宮位的人
```

### 範例3：事業導向配置
```
篩選條件組1：
- 太陽 等於 摩羯座

篩選條件組2：
- 第十宮 包含 [牡羊座, 獅子座, 射手座]

組間邏輯：或 (OR)
用途：找出太陽摩羯或第十宮火象的事業導向人士
```

### 範例4：複雜組合篩選
```
篩選條件組1：
- 太陽 包含 [火象星座] 且
- 月亮 包含 [水象星座]

篩選條件組2：
- 金星 等於 第7宮 且
- 火星 包含 [第1宮, 第5宮, 第10宮]

組間邏輯：且 (AND)
用途：找出火日水月且金星7宮、火星在重要宮位的配置
```

## 💾 篩選器管理

### 保存篩選器
1. 創建完篩選條件後
2. 點擊右上角選單 → 「保存篩選器」
3. 篩選器會保存到本地存儲

### 載入篩選器
1. 切換到「已保存」標籤頁
2. 點擊要載入的篩選器
3. 系統會自動切換到篩選設定頁面

### 管理操作
- **複製**：創建篩選器副本
- **重新命名**：修改篩選器名稱
- **刪除**：移除不需要的篩選器

## 📊 結果分析

### 篩選結果顯示
- 總星盤數量 vs 符合條件數量
- 篩選成功率統計
- 詳細的星盤列表

### 結果操作
- **查看星盤**：開啟完整星盤頁面
- **詳細資訊**：顯示出生資料和基本資訊
- **匯出**：將結果匯出為文件

## 🔧 技術實作

### 核心類別

#### ChartFilter
```dart
class ChartFilter {
  final String id;
  final String name;
  final List<FilterGroup> groups;
  final LogicalOperator groupLogicalOperator;
  // ...
}
```

#### FilterCondition
```dart
class FilterCondition {
  final FilterType type;
  final FilterOperator operator;
  final String? planetName;
  final String? signName;
  final int? houseNumber;
  // ...
}
```

### 使用範例代碼

#### 創建篩選條件
```dart
final condition = FilterCondition(
  id: 'cond1',
  type: FilterType.planetInSign,
  operator: FilterOperator.contains,
  planetName: '太陽',
  signNames: ['牡羊座', '獅子座', '射手座'],
);
```

#### 應用篩選器
```dart
final filteredCharts = ChartFilterService.applyFilter(
  allCharts,
  filter,
);
```

## 🎯 使用建議

### 1. 篩選策略
- 從簡單條件開始，逐步增加複雜度
- 使用預設範例作為學習起點
- 定期檢查篩選結果的合理性

### 2. 效能優化
- 避免過於複雜的篩選條件
- 優先使用精確匹配而非模糊匹配
- 定期清理不需要的已保存篩選器

### 3. 實用技巧
- 為常用篩選器設定有意義的名稱
- 使用複製功能創建相似篩選器的變體
- 結合不同篩選條件探索占星模式

## 🐛 常見問題

### Q: 為什麼篩選結果為空？
A: 檢查篩選條件是否過於嚴格，嘗試放寬條件或使用「或」邏輯

### Q: 如何篩選多個行星的組合？
A: 創建多個篩選條件，使用適當的邏輯操作符組合

### Q: 篩選器保存失敗怎麼辦？
A: 檢查篩選器名稱是否重複，確保有足夠的存儲空間

### Q: 如何匯出篩選結果？
A: 在結果頁面點擊右上角選單，選擇「匯出結果」

## 📈 未來擴展

### 計劃功能
- 相位篩選支援
- 阿拉伯點篩選
- 時間範圍篩選
- 地理位置篩選
- 統計分析功能

### 改進方向
- 更直觀的UI設計
- 更快的篩選效能
- 更豐富的預設範例
- 雲端同步功能

---

*此文檔會隨著功能更新而持續完善，如有問題請參考技術文檔或聯繫開發團隊。*
