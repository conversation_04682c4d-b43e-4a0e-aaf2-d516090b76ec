# 原始內容查看功能

## 📋 功能概述

原始內容查看功能允許開發者和用戶查看發送給AI的完整原始輸入內容，包含星盤摘要、提示詞和解讀指導。這個功能主要用於調試和了解AI解讀的輸入來源。

## 🎯 功能特色

### 主要功能
- **完整內容顯示**：顯示發送給AI的完整原始內容
- **分段顯示**：將內容分為星盤摘要、解讀提示詞、解讀指導三個部分
- **一鍵複製**：支援複製完整原始內容到剪貼板
- **可選擇文字**：支援選擇和複製部分內容
- **錯誤處理**：完善的錯誤處理和重新載入機制

### 內容組成
1. **星盤數據摘要**：包含行星位置、宮位、相位等基本星盤資訊
2. **解讀提示詞**：根據解讀類型生成的專業提示詞
3. **解讀指導**：AI解讀的指導原則和注意事項

## 🚀 使用方式

### 進入原始內容頁面
1. 在AI解讀結果頁面
2. 點擊右上角的「⋮」菜單按鈕
3. 在開發模式下，選擇「查看原始內容」選項

### 頁面操作
- **查看內容**：滾動查看完整的原始內容
- **複製內容**：
  - 點擊頂部的「複製」按鈕複製全部內容
  - 或點擊右上角的複製圖標
- **選擇複製**：長按選擇特定文字進行複製
- **重新載入**：如果載入失敗，點擊「重新載入」按鈕

## 🏗️ 技術實現

### 核心組件
- **OriginalContentPage**：原始內容顯示頁面
- **AIApiService.buildChartSummary()**：構建星盤摘要
- **ChartInterpretationService.getPrompt()**：獲取解讀提示詞
- **InterpretationGuidanceService.getGuidance()**：獲取解讀指導

### 文件位置
```
lib/presentation/pages/original_content_page.dart
```

### 主要方法
```dart
class OriginalContentPage extends StatefulWidget {
  // 載入原始內容
  Future<void> _loadOriginalContent()
  
  // 複製原始內容
  Future<void> _copyOriginalContent()
  
  // 構建頁面UI
  Widget _buildBody()
}
```

## 🎨 UI設計

### 頁面布局
- **AppBar**：標題「原始內容」+ 複製按鈕
- **頂部說明區域**：功能說明 + 複製按鈕
- **內容顯示區域**：可滾動的原始內容文字

### 設計特色
- **等寬字體**：使用 monospace 字體便於閱讀代碼格式
- **分段標題**：使用 === 分隔不同內容段落
- **可選擇文字**：使用 SelectableText 支援文字選擇
- **載入狀態**：顯示載入動畫和進度提示

## 🔧 開發者說明

### 使用場景
1. **調試AI解讀**：查看發送給AI的完整輸入內容
2. **驗證數據**：確認星盤數據和提示詞是否正確
3. **優化提示詞**：了解當前使用的提示詞模板
4. **問題排查**：當AI解讀結果異常時查看輸入來源

### 開發模式限制
- 此功能目前僅在開發模式（kDebugMode）下可用
- 正式版本中不會顯示此選項
- 可根據需要調整可見性設定

### 擴展建議
1. **內容格式化**：可以添加語法高亮或更好的格式化
2. **搜索功能**：在長內容中添加搜索功能
3. **分享功能**：支援將原始內容分享給其他開發者
4. **歷史記錄**：保存查看過的原始內容歷史

## 📝 注意事項

### 性能考量
- 原始內容可能較長，注意記憶體使用
- 載入過程中顯示適當的載入提示
- 錯誤處理要完善，避免應用崩潰

### 用戶體驗
- 提供清晰的功能說明
- 複製成功後給予明確的反饋
- 支援重新載入以處理網路問題

### 安全性
- 原始內容可能包含敏感資訊
- 僅在開發模式下提供此功能
- 注意不要在正式版本中暴露調試資訊

## 🐛 問題修復

### TextInput 連接錯誤
**問題描述**：在內容顯示區域點擊兩下時出現 `TextInput._instance._currentConnection != null` 錯誤

**解決方案**：
- 將 `SelectableText` 替換為 `SelectableRegion` + `Text` 組合
- 使用 `SelectableRegion` 包裝整個內容區域，避免文字輸入連接問題
- 保持文字選擇功能的同時避免系統錯誤

**技術細節**：
```dart
SelectableRegion(
  focusNode: FocusNode(),
  selectionControls: MaterialTextSelectionControls(),
  child: Text(_originalContent, ...),
)
```
