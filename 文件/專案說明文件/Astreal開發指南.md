# Astreal 開發指南

## 🚀 快速開始

### 環境需求
- **Flutter SDK**: 3.16.0 或更高版本
- **Dart SDK**: 3.2.0 或更高版本
- **Android Studio**: 最新版本（Android 開發）
- **Xcode**: 15.0 或更高版本（iOS 開發）
- **Node.js**: 18.0 或更高版本（Firebase 功能）

### 專案設置

#### 1. 克隆專案
```bash
git clone https://github.com/your-org/astreal.git
cd astreal
```

#### 2. 安裝依賴
```bash
flutter pub get
```

#### 3. Firebase 配置
```bash
# 安裝 Firebase CLI
npm install -g firebase-tools

# 登入 Firebase
firebase login

# 配置 Firebase 專案
flutterfire configure
```

#### 4. 環境配置
```bash
# 複製環境配置檔案
cp .env.example .env

# 編輯環境變數
vim .env
```

### 必要的環境變數
```env
# Firebase 配置
FIREBASE_PROJECT_ID=astreal-project
FIREBASE_API_KEY=your-api-key

# AI 服務 API 金鑰（開發用）
OPENAI_API_KEY=your-openai-key
ANTHROPIC_API_KEY=your-anthropic-key
GROQ_API_KEY=your-groq-key
GEMINI_API_KEY=your-gemini-key
```

## 🏗️ 專案架構

### 資料夾結構
```
lib/
├── main.dart                    # 應用程式入口
├── astreal.dart                 # 核心匯出檔案
├── core/                        # 核心功能
│   ├── constants/              # 常數定義
│   ├── utils/                  # 工具函數
│   └── services/               # 核心服務
├── data/                        # 資料層
│   ├── models/                 # 資料模型
│   ├── services/               # 資料服務
│   └── repositories/           # 資料倉庫
├── features/                    # 功能模組
│   ├── astrology/              # 占星計算
│   ├── ai_interpretation/      # AI 解讀
│   └── user_management/        # 用戶管理
├── presentation/                # 展示層
│   ├── pages/                  # 頁面
│   ├── widgets/                # 組件
│   └── viewmodels/             # 視圖模型
└── shared/                      # 共用組件
    ├── widgets/                # 共用 UI 組件
    ├── themes/                 # 主題配置
    └── extensions/             # 擴展方法
```

### 命名規範

#### 檔案命名
- **頁面檔案**: `snake_case_page.dart`
- **組件檔案**: `snake_case_widget.dart`
- **服務檔案**: `snake_case_service.dart`
- **模型檔案**: `snake_case_model.dart`
- **ViewModel**: `snake_case_viewmodel.dart`

#### 類別命名
- **頁面類別**: `PascalCasePage`
- **組件類別**: `PascalCaseWidget`
- **服務類別**: `PascalCaseService`
- **模型類別**: `PascalCase`
- **ViewModel**: `PascalCaseViewModel`

#### 變數命名
- **私有變數**: `_camelCase`
- **公開變數**: `camelCase`
- **常數**: `UPPER_SNAKE_CASE`
- **方法名稱**: `camelCase`

## 🔧 開發工作流程

### Git 工作流程

#### 分支策略
- **main**: 生產環境分支
- **develop**: 開發環境分支
- **feature/**: 功能開發分支
- **hotfix/**: 緊急修復分支
- **release/**: 發布準備分支

#### 提交規範
```bash
# 功能開發
git commit -m "feat: 新增星盤快速切換功能"

# 錯誤修復
git commit -m "fix: 修復界主星計算錯誤"

# 文件更新
git commit -m "docs: 更新 API 文件"

# 樣式調整
git commit -m "style: 優化星盤顯示樣式"

# 重構代碼
git commit -m "refactor: 重構占星計算服務"

# 測試相關
git commit -m "test: 新增星盤計算單元測試"
```

### 開發流程

#### 1. 功能開發
```bash
# 從 develop 分支創建功能分支
git checkout develop
git pull origin develop
git checkout -b feature/chart-quick-switch

# 開發功能
# ... 編寫代碼 ...

# 提交變更
git add .
git commit -m "feat: 實作星盤快速切換功能"

# 推送到遠端
git push origin feature/chart-quick-switch

# 創建 Pull Request
```

#### 2. 代碼審查
- **自我檢查**: 提交前自我審查代碼
- **同儕審查**: 至少一位同事審查
- **自動化檢查**: CI/CD 自動檢查
- **測試驗證**: 確保所有測試通過

#### 3. 合併流程
```bash
# 審查通過後合併到 develop
git checkout develop
git pull origin develop
git merge feature/chart-quick-switch
git push origin develop

# 刪除功能分支
git branch -d feature/chart-quick-switch
git push origin --delete feature/chart-quick-switch
```

## 🧪 測試策略

### 測試類型

#### 單元測試
```dart
// test/services/astrology_service_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:astreal/data/services/api/astrology_service.dart';

void main() {
  group('AstrologyService', () {
    late AstrologyService service;
    
    setUp(() {
      service = AstrologyService();
    });
    
    test('should calculate planet positions correctly', () async {
      // Arrange
      final dateTime = DateTime(2024, 1, 1, 12, 0);
      final latitude = 25.0330;
      final longitude = 121.5654;
      
      // Act
      final planets = await service.calculatePlanetPositions(
        dateTime, 
        latitude: latitude, 
        longitude: longitude,
      );
      
      // Assert
      expect(planets, isNotEmpty);
      expect(planets.length, greaterThan(7));
    });
  });
}
```

#### 整合測試
```dart
// integration_test/app_test.dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:astreal/main.dart' as app;

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();
  
  group('App Integration Tests', () {
    testWidgets('should navigate to chart page', (tester) async {
      app.main();
      await tester.pumpAndSettle();
      
      // 點擊星盤按鈕
      await tester.tap(find.byIcon(Icons.pie_chart));
      await tester.pumpAndSettle();
      
      // 驗證導航成功
      expect(find.text('星盤分析'), findsOneWidget);
    });
  });
}
```

#### UI 測試
```dart
// test/widgets/chart_widget_test.dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:astreal/presentation/widgets/chart/chart_widget.dart';

void main() {
  testWidgets('ChartWidget should display correctly', (tester) async {
    // Arrange
    final chartData = createMockChartData();
    
    // Act
    await tester.pumpWidget(
      MaterialApp(
        home: ChartWidget(chartData: chartData),
      ),
    );
    
    // Assert
    expect(find.byType(CustomPaint), findsOneWidget);
  });
}
```

### 測試執行

#### 執行所有測試
```bash
# 單元測試和組件測試
flutter test

# 整合測試
flutter test integration_test/

# 測試覆蓋率
flutter test --coverage
genhtml coverage/lcov.info -o coverage/html
```

#### 持續整合
```yaml
# .github/workflows/test.yml
name: Tests
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.16.0'
      - run: flutter pub get
      - run: flutter analyze
      - run: flutter test
```

## 📱 平台特定開發

### Android 開發

#### 建置配置
```gradle
// android/app/build.gradle
android {
    compileSdkVersion 34
    
    defaultConfig {
        applicationId "com.astreal.app"
        minSdkVersion 21
        targetSdkVersion 34
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
    }
    
    signingConfigs {
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
            storePassword keystoreProperties['storePassword']
        }
    }
}
```

#### 權限配置
```xml
<!-- android/app/src/main/AndroidManifest.xml -->
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
```

### iOS 開發

#### Info.plist 配置
```xml
<!-- ios/Runner/Info.plist -->
<key>NSLocationWhenInUseUsageDescription</key>
<string>此應用需要位置權限來計算準確的星盤</string>

<key>NSPhotoLibraryUsageDescription</key>
<string>此應用需要相簿權限來保存星盤圖片</string>
```

#### 建置配置
```ruby
# ios/Podfile
platform :ios, '12.0'

target 'Runner' do
  use_frameworks!
  use_modular_headers!
  
  flutter_install_all_ios_pods File.dirname(File.realpath(__FILE__))
end
```

### Web 開發

#### index.html 配置
```html
<!-- web/index.html -->
<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Astreal - 專業占星分析</title>
  
  <!-- Firebase SDK -->
  <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-app.js"></script>
  <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-auth.js"></script>
</head>
<body>
  <script src="main.dart.js" type="application/javascript"></script>
</body>
</html>
```

## 🔍 除錯和性能優化

### 除錯工具

#### Flutter Inspector
```bash
# 啟動 Flutter Inspector
flutter run --debug
# 在 IDE 中開啟 Flutter Inspector 面板
```

#### 日誌系統
```dart
import 'package:logger/logger.dart';

class AppLogger {
  static final Logger _logger = Logger(
    printer: PrettyPrinter(
      methodCount: 2,
      errorMethodCount: 8,
      lineLength: 120,
      colors: true,
      printEmojis: true,
      printTime: true,
    ),
  );
  
  static void d(String message) => _logger.d(message);
  static void i(String message) => _logger.i(message);
  static void w(String message) => _logger.w(message);
  static void e(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.e(message, error, stackTrace);
  }
}
```

### 性能優化

#### 建置優化
```bash
# 發布建置
flutter build apk --release --obfuscate --split-debug-info=build/debug-info
flutter build ios --release --obfuscate --split-debug-info=build/debug-info
```

#### 代碼優化
```dart
// 使用 const 建構子
const Text('靜態文字');

// 避免在 build 方法中創建物件
class MyWidget extends StatelessWidget {
  static const _textStyle = TextStyle(fontSize: 16);
  
  @override
  Widget build(BuildContext context) {
    return Text('文字', style: _textStyle);
  }
}

// 使用 ListView.builder 處理大量資料
ListView.builder(
  itemCount: items.length,
  itemBuilder: (context, index) => ItemWidget(items[index]),
);
```

## 📦 發布流程

### 版本管理
```yaml
# pubspec.yaml
version: 1.2.3+4
# 1.2.3 是版本號，4 是建置號
```

### Android 發布
```bash
# 建置 AAB 檔案
flutter build appbundle --release

# 上傳到 Google Play Console
# 使用 Google Play Console 網頁介面上傳
```

### iOS 發布
```bash
# 建置 iOS 應用
flutter build ios --release

# 使用 Xcode 上傳到 App Store Connect
open ios/Runner.xcworkspace
```

### Web 部署
```bash
# 建置 Web 版本
flutter build web --release

# 部署到 Firebase Hosting
firebase deploy --only hosting
```

這個開發指南為團隊提供了完整的開發工作流程和最佳實踐，確保專案的高品質和可維護性。
