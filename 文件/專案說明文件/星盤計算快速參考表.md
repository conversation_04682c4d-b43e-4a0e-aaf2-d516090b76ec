# 星盤計算快速參考表

## 📊 星盤類型總覽

| 分類 | 星盤類型 | 英文名稱 | 計算要素 | 主要用途 |
|------|----------|----------|----------|----------|
| **個人星盤** | 本命盤 | Natal Chart | 出生時間+地點 | 性格分析、天賦潛能 |
| **預測星盤** | 行運盤 | Transit Chart | 本命盤+當前時間 | 當前運勢、時機分析 |
| | 次限推運盤 | Secondary Progression | 本命盤+目標日期 | 內在發展、心理成長 |
| | 三限推運盤 | Tertiary Progression | 本命盤+目標日期 | 短期變化、月度運勢 |
| | 太陽弧推運盤 | Solar Arc Direction | 本命盤+目標日期 | 重大轉折、關鍵事件 |
| **返照盤** | 太陽返照盤 | Solar Return | 本命盤+目標年份 | 年度主題、年運分析 |
| | 月亮返照盤 | Lunar Return | 本命盤+目標月份 | 月度情緒、短期發展 |
| **關係星盤** | 比較盤 | Synastry | 兩人本命盤 | 關係相容性、互動模式 |
| | 組合盤 | Composite Chart | 兩人中點計算 | 關係本質、共同目標 |
| | 時空中點盤 | Davison Chart | 時空中點計算 | 關係命運、宇宙意義 |
| | 馬克思盤 | Marks Chart | 特殊計算法 | 主觀感受、內心體驗 |
| **事件占星** | 卜卦盤 | Horary Chart | 問題時間+地點 | 具體問題解答 |
| | 事件盤 | Event Chart | 事件時間+地點 | 事件意義分析 |
| **特殊星盤** | 法達盤 | Firdaria Chart | 本命盤+年齡計算 | 人生階段分析 |
| | 小限盤 | Profection Chart | 本命盤+年齡推進 | 年度重點領域 |
| | 天象盤 | Mundane Chart | 特定時間+地點 | 集體趨勢分析 |
| | 日月蝕盤 | Eclipse Chart | 蝕相時間計算 | 轉化能量分析 |
| | 二分二至盤 | Equinox/Solstice | 節氣時間計算 | 季節能量影響 |

## 🧮 計算公式速查

### 基礎轉換

#### 儒略日轉換
```
JD = Sweph.swe_julday(year, month, day, hour_utc, SE_GREG_CAL)
```

#### 時區調整
```
UTC_hour = local_hour - timezone_offset
```

#### 角度標準化
```
normalized_angle = angle % 360
if (normalized_angle < 0) normalized_angle += 360
```

### 推運計算

#### 次限推運（一天=一年）
```
progressed_date = birth_date + age_in_days
```

#### 三限推運（一天=一月）
```
progressed_date = birth_date + age_in_months_as_days
```

#### 太陽弧推運
```
arc_degrees = age_in_years * 1.0  // 簡化公式
new_longitude = (planet_longitude + arc_degrees) % 360
```

### 返照盤計算

#### 太陽返照時間
```
找到太陽經度 = 出生時太陽經度的年度回歸時間
```

#### 月亮返照時間
```
找到月亮經度 = 出生時月亮經度的月度回歸時間
```

### 關係盤計算

#### 組合盤中點
```
midpoint_longitude = midpoint_angle(planet1_long, planet2_long)

function midpoint_angle(angle1, angle2):
  diff = (angle2 - angle1 + 360) % 360
  if (diff > 180) diff -= 360
  return (angle1 + diff/2 + 360) % 360
```

#### 時空中點
```
mid_datetime = (datetime1_timestamp + datetime2_timestamp) / 2
mid_latitude = (lat1 + lat2) / 2
mid_longitude = (long1 + long2) / 2
```

### 相位計算

#### 角度差計算
```
angle_diff = abs(planet1_longitude - planet2_longitude)
if (angle_diff > 180) angle_diff = 360 - angle_diff
```

#### 相位判斷
```
for each aspect_definition:
  orb = abs(angle_diff - aspect_angle)
  if (orb <= max_orb) return aspect_found
```

## 📐 宮位系統對照

| 宮位系統 | 代碼 | 特點 | 適用場景 |
|----------|------|------|----------|
| Placidus | P | 時間等分制 | 現代占星標準 |
| Koch | K | 出生地等分制 | 德國占星傳統 |
| Equal | A | 等宮制（30°/宮） | 簡化計算 |
| Whole Sign | W | 整宮制（星座=宮位） | 古典占星 |
| Campanus | C | 地平等分制 | 中世紀傳統 |
| Regiomontanus | R | 天球等分制 | 文藝復興時期 |
| Topocentric | T | 地心等分制 | 現代變體 |

## 🌟 行星計算參數

### Swiss Ephemeris 行星代碼

| 行星 | 代碼 | 符號 | 平均速度（°/日） |
|------|------|------|------------------|
| 太陽 | SE_SUN | ☉ | 0.9856 |
| 月亮 | SE_MOON | ☽ | 13.1764 |
| 水星 | SE_MERCURY | ☿ | 1.3833 |
| 金星 | SE_VENUS | ♀ | 1.6021 |
| 火星 | SE_MARS | ♂ | 0.5240 |
| 木星 | SE_JUPITER | ♃ | 0.0831 |
| 土星 | SE_SATURN | ♄ | 0.0335 |
| 天王星 | SE_URANUS | ♅ | 0.0116 |
| 海王星 | SE_NEPTUNE | ♆ | 0.0060 |
| 冥王星 | SE_PLUTO | ♇ | 0.0040 |

### 計算標誌

| 標誌 | 代碼 | 用途 |
|------|------|------|
| Swiss Ephemeris | SEFLG_SWIEPH | 使用 Swiss Ephemeris 資料 |
| 速度計算 | SEFLG_SPEED | 包含行星速度資訊 |
| 地心座標 | SEFLG_GEOCTR | 地心觀測點 |
| 日心座標 | SEFLG_HELCTR | 日心觀測點 |
| 真實位置 | SEFLG_TRUEPOS | 包含光行時修正 |
| 視位置 | SEFLG_APPARENT | 包含光行差和章動 |

## 🎯 相位系統

### 主要相位

| 相位 | 角度 | 符號 | 標準容許度 | 性質 |
|------|------|------|------------|------|
| 合相 | 0° | ☌ | ±8° | 中性/強化 |
| 六分相 | 60° | ⚹ | ±4° | 和諧 |
| 四分相 | 90° | □ | ±6° | 緊張/動力 |
| 三分相 | 120° | △ | ±6° | 和諧/流暢 |
| 對沖 | 180° | ☍ | ±8° | 緊張/對立 |

### 次要相位

| 相位 | 角度 | 符號 | 標準容許度 | 性質 |
|------|------|------|------------|------|
| 半六分相 | 30° | ⚺ | ±2° | 輕微和諧 |
| 半四分相 | 45° | ∠ | ±2° | 輕微緊張 |
| 五分相 | 72° | Q | ±2° | 創造性 |
| 補八分相 | 135° | ⚼ | ±2° | 調整性 |
| 倍五分相 | 144° | bQ | ±2° | 創造性 |

### 容許度計算

#### 標準容許度
```
orb = abs(actual_angle - perfect_aspect_angle)
is_valid_aspect = (orb <= max_orb_for_aspect)
```

#### 動態容許度（基於行星重要性）
```
sun_moon_orb = 8°
personal_planets_orb = 6°
social_planets_orb = 4°
outer_planets_orb = 2°
```

## 🌙 特殊點計算

### 阿拉伯點公式

#### 基本點位
```
幸運點 = 上升點 + 月亮 - 太陽（白天）
       = 上升點 + 太陽 - 月亮（夜晚）

精神點 = 上升點 + 太陽 - 月亮（白天）
       = 上升點 + 月亮 - 太陽（夜晚）
```

#### 專業點位
```
愛情點 = 上升點 + 金星 - 太陽
職業點 = 上升點 + 月亮 - 土星
疾病點 = 上升點 + 火星 - 土星
死亡點 = 上升點 + 第8宮宮頭 - 月亮
```

### 月交點
```
北交點 = SE_TRUE_NODE
南交點 = 北交點 + 180°
```

### 虛點
```
莉莉絲（黑月） = SE_MEAN_APOG
塞勒斯（穀神星） = SE_CERES
帕拉斯（智神星） = SE_PALLAS
```

## ⏰ 時間系統

### 時間標準

| 時間系統 | 縮寫 | 說明 | 用途 |
|----------|------|------|------|
| 世界協調時 | UTC | 國際標準時間 | 基礎計算 |
| 地球時 | TT | 天文計算標準 | 高精度計算 |
| 當地時間 | LT | 觀測地時間 | 用戶界面 |
| 恆星時 | ST | 天球座標系 | 宮位計算 |

### 時區處理
```
UTC = Local_Time - Timezone_Offset
JD = swe_julday(year, month, day, UTC_hour, SE_GREG_CAL)
```

## 🔧 計算優化

### 快取策略

| 快取類型 | 有效期 | 快取鍵 |
|----------|--------|--------|
| 行星位置 | 1小時 | `planet_${id}_${julianDay}` |
| 宮位資料 | 1小時 | `houses_${lat}_${lng}_${jd}_${system}` |
| 時區資料 | 24小時 | `timezone_${lat}_${lng}_${date}` |
| 相位計算 | 30分鐘 | `aspects_${planets_hash}` |

### 批量計算
```dart
// 並行計算多個行星
final futures = planets.map((planet) => 
  calculatePlanetPosition(julianDay, planet));
final results = await Future.wait(futures);
```

### 精度控制
```dart
// 角度精度：0.001°（約3.6角秒）
final roundedAngle = (angle * 1000).round() / 1000;

// 時間精度：1分鐘
final roundedTime = DateTime(year, month, day, hour, minute);
```

## 📊 驗證和測試

### 計算驗證點

| 驗證項目 | 標準值 | 容許誤差 |
|----------|--------|----------|
| 春分點 | 0°00'00" | ±1' |
| 恆星年長度 | 365.25636日 | ±0.00001日 |
| 月亮軌道週期 | 27.32166日 | ±0.00001日 |
| 歲差常數 | 50.29"/年 | ±0.01"/年 |

### 測試用例
```dart
// 標準測試日期：2000年1月1日 12:00 UTC
final testJD = 2451545.0;
final testLat = 51.5074; // 倫敦
final testLng = -0.1278;

// 預期結果驗證
assert(calculateSunPosition(testJD).longitude ≈ 280.46°);
assert(calculateMoonPosition(testJD).longitude ≈ 218.32°);
```

這個快速參考表為開發者和占星師提供了 Astreal 星盤計算的核心公式和參數，便於快速查閱和驗證計算結果。
