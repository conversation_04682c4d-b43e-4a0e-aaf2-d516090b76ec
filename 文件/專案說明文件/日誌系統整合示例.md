# 日誌系統整合示例

## 📱 在設定頁面添加日誌管理入口

### 1. 更新設定頁面

在現有的設定頁面中添加日誌管理選項：

```dart
// lib/presentation/pages/settings/settings_page.dart

import 'package:flutter/material.dart';
import 'log_management_page.dart';

class SettingsPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('設定')),
      body: ListView(
        children: [
          // ... 其他設定選項
          
          // 日誌管理選項
          ListTile(
            leading: Icon(Icons.bug_report),
            title: Text('日誌管理'),
            subtitle: Text('查看和管理應用程式日誌'),
            trailing: Icon(Icons.chevron_right),
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => LogManagementPage(),
                ),
              );
            },
          ),
          
          // ... 其他設定選項
        ],
      ),
    );
  }
}
```

### 2. 在應用程式中使用日誌

#### 星盤計算服務中的日誌記錄

```dart
// lib/data/services/api/astrology_service.dart

import '../../../core/utils/logger_utils.dart';
import '../../../core/services/app_initialization_service.dart';

class AstrologyService {
  Future<ChartData> calculateChart(BirthData birthData) async {
    logger.i('開始計算星盤: ${birthData.name}');
    
    return await PerformanceMonitor.monitor(
      '星盤計算',
      () async {
        try {
          // 星盤計算邏輯
          final chartData = await _performCalculation(birthData);
          
          logger.i('星盤計算完成: ${birthData.name}');
          return chartData;
          
        } catch (e, stackTrace) {
          logger.e('星盤計算失敗: ${birthData.name}', e, stackTrace);
          
          // 記錄崩潰報告
          await LogManagementService.instance.logCrashReport(
            error: '星盤計算失敗: $e',
            stackTrace: stackTrace,
            additionalInfo: {
              'birth_data': birthData.toJson(),
              'operation': 'chart_calculation',
            },
          );
          
          rethrow;
        }
      },
      metadata: {
        'chart_type': 'natal',
        'person_name': birthData.name,
      },
    );
  }
}
```

#### AI 解讀服務中的日誌記錄

```dart
// lib/data/services/api/ai_api_service.dart

import '../../../core/utils/logger_utils.dart';
import '../../../data/services/api/log_management_service.dart';

class AIApiService {
  Future<String> getInterpretation(
    ChartData chartData,
    String interpretationType,
  ) async {
    logger.i('開始 AI 解讀: $interpretationType');
    
    return await PerformanceMonitor.monitor(
      'AI 解讀',
      () async {
        try {
          final response = await _callAIAPI(chartData, interpretationType);
          
          logger.i('AI 解讀完成: $interpretationType');
          return response;
          
        } catch (e, stackTrace) {
          logger.e('AI 解讀失敗: $interpretationType', e, stackTrace);
          
          // 記錄崩潰報告
          await LogManagementService.instance.logCrashReport(
            error: 'AI 解讀失敗: $e',
            stackTrace: stackTrace,
            additionalInfo: {
              'interpretation_type': interpretationType,
              'chart_type': chartData.chartType.toString(),
              'ai_provider': _currentProvider,
            },
          );
          
          rethrow;
        }
      },
      metadata: {
        'interpretation_type': interpretationType,
        'ai_provider': _currentProvider,
      },
    );
  }
}
```

#### 用戶認證中的日誌記錄

```dart
// lib/data/services/api/firebase_auth_service.dart

import '../../../core/utils/logger_utils.dart';

class FirebaseAuthService {
  Future<User?> signInWithGoogle() async {
    logger.i('開始 Google 登入');
    
    try {
      final user = await _performGoogleSignIn();
      
      if (user != null) {
        logger.i('Google 登入成功: ${user.email}');
        
        // 記錄性能日誌
        await LogManagementService.instance.logPerformance(
          operation: 'google_sign_in',
          duration: Duration(milliseconds: 1500), // 實際測量的時間
          metadata: {
            'user_id': user.uid,
            'success': true,
          },
        );
      } else {
        logger.w('Google 登入取消');
      }
      
      return user;
      
    } catch (e, stackTrace) {
      logger.e('Google 登入失敗', e, stackTrace);
      
      // 記錄崩潰報告
      await LogManagementService.instance.logCrashReport(
        error: 'Google 登入失敗: $e',
        stackTrace: stackTrace,
        additionalInfo: {
          'auth_method': 'google',
          'platform': Platform.operatingSystem,
        },
      );
      
      rethrow;
    }
  }
}
```

### 3. 在 ViewModel 中使用日誌

```dart
// lib/presentation/viewmodels/chart_viewmodel.dart

import '../../core/utils/logger_utils.dart';
import '../../core/services/app_initialization_service.dart';

class ChartViewModel extends ChangeNotifier {
  Future<void> calculateChart(BirthData birthData) async {
    logger.i('ViewModel: 開始計算星盤');
    
    try {
      setLoading(true);
      
      final chartData = await PerformanceMonitor.monitor(
        'ViewModel 星盤計算',
        () async {
          return await _astrologyService.calculateChart(birthData);
        },
        metadata: {
          'viewmodel': 'ChartViewModel',
          'action': 'calculateChart',
        },
      );
      
      _chartData = chartData;
      logger.i('ViewModel: 星盤計算完成');
      
    } catch (e, stackTrace) {
      logger.e('ViewModel: 星盤計算失敗', e, stackTrace);
      _error = e.toString();
    } finally {
      setLoading(false);
      notifyListeners();
    }
  }
}
```

### 4. 在 UI 組件中使用日誌

```dart
// lib/presentation/widgets/chart/chart_widget.dart

import '../../../core/utils/logger_utils.dart';

class ChartWidget extends StatefulWidget {
  @override
  _ChartWidgetState createState() => _ChartWidgetState();
}

class _ChartWidgetState extends State<ChartWidget> {
  @override
  void initState() {
    super.initState();
    logger.d('ChartWidget 初始化');
  }
  
  @override
  Widget build(BuildContext context) {
    return PerformanceMonitor.monitorSync(
      'ChartWidget 建置',
      () {
        return CustomPaint(
          painter: ChartPainter(widget.chartData),
          child: Container(
            width: 300,
            height: 300,
          ),
        );
      },
      metadata: {
        'widget': 'ChartWidget',
        'chart_type': widget.chartData.chartType.toString(),
      },
    );
  }
  
  @override
  void dispose() {
    logger.d('ChartWidget 銷毀');
    super.dispose();
  }
}
```

### 5. 錯誤邊界處理

```dart
// lib/shared/widgets/error_boundary.dart

import '../../core/utils/logger_utils.dart';
import '../../data/services/api/log_management_service.dart';

class ErrorBoundary extends StatefulWidget {
  final Widget child;
  final Widget Function(Object error)? errorBuilder;
  
  const ErrorBoundary({
    Key? key,
    required this.child,
    this.errorBuilder,
  }) : super(key: key);
  
  @override
  _ErrorBoundaryState createState() => _ErrorBoundaryState();
}

class _ErrorBoundaryState extends State<ErrorBoundary> {
  Object? _error;
  
  @override
  Widget build(BuildContext context) {
    if (_error != null) {
      return widget.errorBuilder?.call(_error!) ?? 
        Center(child: Text('發生錯誤: $_error'));
    }
    
    return widget.child;
  }
  
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    
    // 捕獲構建過程中的錯誤
    FlutterError.onError = (FlutterErrorDetails details) {
      setState(() {
        _error = details.exception;
      });
      
      logger.e('UI 錯誤邊界捕獲錯誤', details.exception, details.stack);
      
      // 記錄崩潰報告
      LogManagementService.instance.logCrashReport(
        error: 'UI 錯誤: ${details.exception}',
        stackTrace: details.stack ?? StackTrace.current,
        additionalInfo: {
          'error_boundary': true,
          'widget_tree': details.context?.toString(),
        },
      );
    };
  }
}
```

### 6. 網路請求日誌

```dart
// lib/core/utils/http_client.dart

import 'package:dio/dio.dart';
import 'logger_utils.dart';

class LoggingInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    logger.d('HTTP 請求: ${options.method} ${options.uri}');
    super.onRequest(options, handler);
  }
  
  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    logger.d('HTTP 響應: ${response.statusCode} ${response.requestOptions.uri}');
    super.onResponse(response, handler);
  }
  
  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    logger.e('HTTP 錯誤: ${err.message}', err, err.stackTrace);
    
    // 記錄網路錯誤
    LogManagementService.instance.logCrashReport(
      error: 'HTTP 錯誤: ${err.message}',
      stackTrace: err.stackTrace ?? StackTrace.current,
      additionalInfo: {
        'url': err.requestOptions.uri.toString(),
        'method': err.requestOptions.method,
        'status_code': err.response?.statusCode,
        'response_data': err.response?.data?.toString(),
      },
    );
    
    super.onError(err, handler);
  }
}
```

## 🔧 最佳實踐總結

### 1. 日誌級別使用
- **Debug**: 開發調試信息
- **Info**: 重要操作記錄
- **Warning**: 潛在問題
- **Error**: 需要關注的錯誤

### 2. 性能監控
- 監控關鍵業務操作
- 記錄操作耗時和元數據
- 識別性能瓶頸

### 3. 崩潰報告
- 捕獲所有異常
- 提供詳細的上下文信息
- 包含用戶操作路徑

### 4. 隱私保護
- 不記錄敏感個人資料
- 可選的資料匿名化
- 用戶控制上傳權限

這個整合示例展示了如何在整個應用程式中有效使用日誌系統，幫助開發團隊更好地監控和維護應用程式。
