# 日誌查看功能使用說明

## 📋 功能概述

Astreal 應用程式現在提供了完整的日誌查看功能，讓用戶和開發者可以直接在應用程式內查看、搜尋、過濾和管理日誌內容。

## 🚀 功能特色

### 1. 完整的日誌查看器
- **實時日誌顯示**：查看應用程式運行時的即時日誌
- **多級別過濾**：支援 DEBUG、INFO、WARNING、ERROR 級別過濾
- **搜尋功能**：快速搜尋特定的日誌內容
- **語法高亮**：不同級別的日誌使用不同顏色顯示
- **自動刷新**：可開啟自動刷新模式，實時監控日誌

### 2. 日誌預覽組件
- **快速預覽**：在設定頁面快速查看最近的日誌
- **狀態指示器**：顯示系統運行狀態（正常/警告/錯誤）
- **一鍵跳轉**：從預覽直接跳轉到完整日誌查看器

### 3. 日誌管理功能
- **複製日誌**：一鍵複製日誌內容到剪貼板
- **分享日誌**：透過系統分享功能分享日誌
- **清空日誌**：清理本地日誌文件
- **上傳日誌**：上傳到 Firebase Storage 進行分析

## 📱 使用方式

### 存取日誌查看器

#### 方法 1：透過設定頁面
1. 開啟應用程式
2. 進入「設定」頁面
3. 點擊「日誌管理」
4. 點擊「查看日誌」按鈕

#### 方法 2：透過日誌預覽組件
1. 在設定頁面找到「最近日誌」預覽卡片
2. 點擊「查看全部」按鈕

### 日誌查看器操作

#### 基本操作
- **刷新日誌**：點擊右上角的刷新按鈕
- **複製日誌**：點擊複製按鈕將當前過濾的日誌複製到剪貼板
- **分享日誌**：點擊分享按鈕透過系統分享功能分享日誌
- **清空日誌**：透過選單選擇「清空日誌」

#### 過濾和搜尋
1. **級別過濾**：
   - 點擊頂部的級別標籤（ALL、DEBUG、INFO、WARNING、ERROR）
   - 只顯示選定級別的日誌

2. **文字搜尋**：
   - 在搜尋欄輸入關鍵字
   - 即時過濾包含關鍵字的日誌行
   - 點擊清除按鈕清空搜尋

#### 自動刷新
- **開啟自動刷新**：點擊右下角的播放按鈕
- **關閉自動刷新**：點擊暫停按鈕
- 自動刷新間隔：2 秒

#### 導航功能
- **滾動到頂部**：選單 → 滾動到頂部
- **滾動到底部**：選單 → 滾動到底部

## 🎨 界面說明

### 日誌查看器界面

```
┌─────────────────────────────────────┐
│ ← 日誌查看器    🔄 📋 📤 ⋮         │
├─────────────────────────────────────┤
│ 過濾器區域                          │
│ 級別: [ALL] [DEBUG] [INFO] [WARN]   │
│ 搜尋: [________________] [🔍]       │
├─────────────────────────────────────┤
│ 統計: 總計:150 顯示:25 錯誤:2 警告:5 │
├─────────────────────────────────────┤
│ 日誌內容區域                        │
│ 1  🔵 [INFO] 應用程式啟動...        │
│ 2  🟡 [WARN] 網路連線緩慢           │
│ 3  🔴 [ERROR] 計算失敗              │
│ ...                                 │
├─────────────────────────────────────┤
│                              [▶️]   │
└─────────────────────────────────────┘
```

### 日誌預覽組件

```
┌─────────────────────────────────────┐
│ 📜 最近日誌                    🔄   │
├─────────────────────────────────────┤
│ 🔵 [INFO] 用戶登入成功              │
│ 🟡 [WARN] API 響應緩慢              │
│ 🔵 [INFO] 星盤計算完成              │
│ ...                                 │
├─────────────────────────────────────┤
│ 顯示最近 10 條記錄        查看全部 → │
└─────────────────────────────────────┘
```

## 🔍 日誌內容說明

### 日誌格式
每行日誌包含以下信息：
```
[時間戳記] [級別] 訊息內容
```

例如：
```
[2024-01-15T10:30:45.123] [INFO] 用戶登入成功: <EMAIL>
[2024-01-15T10:30:46.456] [ERROR] 星盤計算失敗: 出生時間無效
```

### 日誌級別說明

| 級別 | 圖標 | 顏色 | 說明 |
|------|------|------|------|
| DEBUG | 🐛 | 灰色 | 開發調試信息 |
| INFO | ℹ️ | 藍色 | 一般信息記錄 |
| WARNING | ⚠️ | 橙色 | 警告信息 |
| ERROR | ❌ | 紅色 | 錯誤信息 |

### 常見日誌內容

#### 應用程式啟動
```
[INFO] 應用程式啟動中...
[INFO] Firebase 初始化成功
[INFO] 日誌系統初始化成功
[INFO] 應用程式初始化完成
```

#### 用戶操作
```
[INFO] 用戶登入成功: <EMAIL>
[INFO] 開始計算星盤: 張三
[INFO] 星盤計算完成: 張三
[INFO] 開始 AI 解讀: 性格特質分析
```

#### 錯誤情況
```
[ERROR] 星盤計算失敗: 出生時間無效
[ERROR] AI 解讀失敗: 網路連線超時
[ERROR] 上傳日誌文件失敗: 用戶未登入
```

## 🛠️ 開發者功能

### 除錯支援
- **即時監控**：開啟自動刷新監控應用程式運行狀態
- **錯誤追蹤**：快速定位錯誤和警告信息
- **性能分析**：查看操作耗時和性能指標

### 日誌分析
- **統計信息**：查看不同級別日誌的數量統計
- **搜尋過濾**：快速找到特定的錯誤或操作記錄
- **時間追蹤**：根據時間戳記追蹤問題發生的時間

### 問題報告
- **複製日誌**：複製相關日誌內容用於問題報告
- **分享日誌**：透過郵件或其他方式分享日誌給開發團隊
- **上傳分析**：上傳到雲端進行進一步分析

## 📊 日誌狀態指示器

### 狀態類型
- **🟢 正常**：沒有錯誤或警告
- **🟡 警告**：有警告信息，但應用程式正常運行
- **🔴 錯誤**：發現錯誤，可能影響功能

### 使用場景
- **快速檢查**：在設定頁面快速了解系統狀態
- **問題提醒**：當有錯誤時提醒用戶或開發者
- **狀態監控**：持續監控應用程式健康狀態

## 🔒 隱私和安全

### 本地存儲
- 日誌文件存儲在應用程式的私有目錄
- 只有應用程式本身可以存取
- 用戶可以隨時清空本地日誌

### 分享控制
- 用戶完全控制日誌的分享和上傳
- 不會自動分享敏感信息
- 可以選擇性分享特定級別的日誌

### 資料保護
- 不記錄用戶的敏感個人資料
- 密碼和 API 金鑰不會出現在日誌中
- 支援資料匿名化選項

## 💡 使用技巧

### 快速除錯
1. 開啟自動刷新模式
2. 過濾到 ERROR 級別
3. 重現問題操作
4. 查看即時錯誤日誌

### 性能監控
1. 搜尋「性能監控」或「耗時」
2. 查看各操作的執行時間
3. 識別性能瓶頸

### 問題報告
1. 過濾相關時間段的日誌
2. 複製錯誤相關的日誌內容
3. 包含上下文信息一起報告

## 🆘 常見問題

### Q: 日誌文件在哪裡？
A: 日誌文件存儲在應用程式文檔目錄的 `logs/astreal_app.log`

### Q: 如何清空日誌？
A: 在日誌查看器中，點擊右上角選單 → 清空日誌

### Q: 自動刷新會影響性能嗎？
A: 自動刷新只在日誌查看器開啟時運行，對應用程式整體性能影響很小

### Q: 可以匯出日誌嗎？
A: 可以透過複製或分享功能匯出日誌內容

### Q: 日誌會佔用多少空間？
A: 日誌文件會自動輪轉，單個文件最大 5MB，舊文件會自動清理

---

這個日誌查看功能為 Astreal 應用程式提供了強大的除錯和監控能力，幫助開發者和用戶更好地了解應用程式的運行狀況。
