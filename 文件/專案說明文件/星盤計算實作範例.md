# 星盤計算實作範例

## 📋 概述

本文件提供 Astreal 應用程式中星盤計算的具體實作範例，包含完整的代碼示例和使用說明。

## 🌟 基礎計算範例

### 1. 本命盤計算完整範例

```dart
import 'package:astreal/features/astrology/astrology.dart';

Future<void> calculateNatalChartExample() async {
  // 1. 準備出生資料
  final birthData = BirthData(
    name: '張三',
    dateTime: DateTime(1990, 5, 15, 14, 30), // 1990年5月15日 14:30
    latitude: 25.0330,  // 台北緯度
    longitude: 121.5654, // 台北經度
    timezone: 'Asia/Taipei',
  );
  
  // 2. 創建星盤計算參數
  final params = ChartCalculationParams(
    primaryPerson: birthData,
    chartType: ChartType.natal,
    houseSystem: 'Placidus',
    includeMinorAspects: false,
  );
  
  // 3. 執行計算
  try {
    final astrologyService = AstrologyService();
    final chartData = await astrologyService.calculateChartData(params);
    
    // 4. 輸出結果
    print('=== 本命盤計算結果 ===');
    print('姓名: ${chartData.primaryPerson.name}');
    print('出生時間: ${chartData.primaryPerson.dateTime}');
    print('');
    
    // 顯示行星位置
    print('行星位置:');
    for (final planet in chartData.planets) {
      print('${planet.name}: ${planet.longitude.toStringAsFixed(2)}° '
            '${planet.sign.name} 第${planet.house}宮');
    }
    
    // 顯示宮位界線
    print('\n宮位界線:');
    for (int i = 0; i < chartData.houses.cusps.length; i++) {
      final cusp = chartData.houses.cusps[i];
      print('第${i + 1}宮: ${cusp.toStringAsFixed(2)}°');
    }
    
    // 顯示主要相位
    print('\n主要相位:');
    for (final aspect in chartData.aspects) {
      print('${aspect.planet1.name} ${aspect.aspect} ${aspect.planet2.name} '
            '(容許度: ${aspect.orb.toStringAsFixed(2)}°)');
    }
    
  } catch (e) {
    print('計算失敗: $e');
  }
}
```

### 2. 行運盤計算範例

```dart
Future<void> calculateTransitChartExample() async {
  // 1. 本命盤資料
  final birthData = BirthData(
    name: '李四',
    dateTime: DateTime(1985, 8, 20, 10, 15),
    latitude: 39.9042,  // 北京緯度
    longitude: 116.4074, // 北京經度
    timezone: 'Asia/Shanghai',
  );
  
  // 2. 行運日期（當前時間）
  final transitDate = DateTime.now();
  
  // 3. 計算參數
  final params = ChartCalculationParams(
    primaryPerson: birthData,
    chartType: ChartType.transit,
    specificDate: transitDate,
    houseSystem: 'Placidus',
  );
  
  try {
    final astrologyService = AstrologyService();
    final chartData = await astrologyService.calculateChartData(params);
    
    print('=== 行運盤分析 ===');
    print('本命盤: ${birthData.dateTime}');
    print('行運日期: ${transitDate}');
    print('');
    
    // 顯示重要行運相位
    print('重要行運相位:');
    for (final aspect in chartData.transitAspects ?? []) {
      if (aspect.orb <= 2.0) { // 只顯示緊密相位
        print('行運${aspect.planet1.name} ${aspect.aspect} '
              '本命${aspect.planet2.name} (${aspect.orb.toStringAsFixed(1)}°)');
      }
    }
    
  } catch (e) {
    print('行運盤計算失敗: $e');
  }
}
```

### 3. 推運盤計算範例

```dart
Future<void> calculateProgressionExample() async {
  final birthData = BirthData(
    name: '王五',
    dateTime: DateTime(1980, 12, 3, 8, 45),
    latitude: 31.2304,  // 上海緯度
    longitude: 121.4737, // 上海經度
    timezone: 'Asia/Shanghai',
  );
  
  // 目標日期（30歲時的推運）
  final targetDate = birthData.dateTime.add(Duration(days: 30 * 365));
  
  // 次限推運計算
  final secondaryParams = ChartCalculationParams(
    primaryPerson: birthData,
    chartType: ChartType.secondaryProgression,
    specificDate: targetDate,
  );
  
  try {
    final astrologyService = AstrologyService();
    final progressionChart = await astrologyService.calculateChartData(secondaryParams);
    
    print('=== 次限推運分析（30歲） ===');
    print('');
    
    // 比較本命與推運位置
    print('推運行星位置變化:');
    for (int i = 0; i < progressionChart.planets.length; i++) {
      final natal = progressionChart.planets[i];
      final progressed = progressionChart.progressedPlanets![i];
      
      final movement = (progressed.longitude - natal.longitude + 360) % 360;
      if (movement > 180) movement -= 360;
      
      print('${natal.name}: 本命 ${natal.longitude.toStringAsFixed(1)}° → '
            '推運 ${progressed.longitude.toStringAsFixed(1)}° '
            '(移動 ${movement.toStringAsFixed(1)}°)');
    }
    
  } catch (e) {
    print('推運盤計算失敗: $e');
  }
}
```

## 💕 關係盤計算範例

### 1. 比較盤（合盤）範例

```dart
Future<void> calculateSynastryExample() async {
  // 第一個人的資料
  final person1 = BirthData(
    name: '張三',
    dateTime: DateTime(1990, 5, 15, 14, 30),
    latitude: 25.0330,
    longitude: 121.5654,
    timezone: 'Asia/Taipei',
  );
  
  // 第二個人的資料
  final person2 = BirthData(
    name: '李四',
    dateTime: DateTime(1988, 9, 22, 16, 45),
    latitude: 25.0330,
    longitude: 121.5654,
    timezone: 'Asia/Taipei',
  );
  
  final params = ChartCalculationParams(
    primaryPerson: person1,
    secondaryPerson: person2,
    chartType: ChartType.synastry,
  );
  
  try {
    final astrologyService = AstrologyService();
    final synastryChart = await astrologyService.calculateChartData(params);
    
    print('=== 合盤分析 ===');
    print('${person1.name} vs ${person2.name}');
    print('');
    
    // 分析重要的合盤相位
    final importantAspects = synastryChart.synastryAspects?.where((aspect) {
      // 篩選重要相位：太陽、月亮、金星、火星的相位
      final importantPlanets = ['太陽', '月亮', '金星', '火星'];
      return importantPlanets.contains(aspect.planet1.name) ||
             importantPlanets.contains(aspect.planet2.name);
    }).toList() ?? [];
    
    print('重要合盤相位:');
    for (final aspect in importantAspects) {
      final nature = _getAspectNature(aspect.aspect);
      print('${person1.name}的${aspect.planet1.name} ${aspect.aspect} '
            '${person2.name}的${aspect.planet2.name} '
            '($nature, 容許度: ${aspect.orb.toStringAsFixed(1)}°)');
    }
    
    // 計算相容性分數
    final compatibilityScore = _calculateCompatibilityScore(importantAspects);
    print('\n相容性評分: ${compatibilityScore.toStringAsFixed(1)}/10');
    
  } catch (e) {
    print('合盤計算失敗: $e');
  }
}

String _getAspectNature(String aspectName) {
  switch (aspectName) {
    case '合相': return '強化';
    case '六分相':
    case '三分相': return '和諧';
    case '四分相':
    case '對沖': return '挑戰';
    default: return '中性';
  }
}

double _calculateCompatibilityScore(List<AspectInfo> aspects) {
  double score = 5.0; // 基礎分數
  
  for (final aspect in aspects) {
    switch (aspect.aspect) {
      case '三分相':
      case '六分相':
        score += 1.0 - (aspect.orb / 6.0); // 和諧相位加分
        break;
      case '四分相':
      case '對沖':
        score -= 0.5 + (aspect.orb / 8.0); // 緊張相位扣分
        break;
      case '合相':
        // 合相根據行星組合判斷
        if (_isHarmoniousConjunction(aspect.planet1.name, aspect.planet2.name)) {
          score += 1.5;
        } else {
          score += 0.5;
        }
        break;
    }
  }
  
  return math.max(0.0, math.min(10.0, score));
}

bool _isHarmoniousConjunction(String planet1, String planet2) {
  final harmoniousPairs = [
    ['太陽', '月亮'],
    ['金星', '火星'],
    ['太陽', '金星'],
    ['月亮', '金星'],
  ];
  
  return harmoniousPairs.any((pair) =>
    (pair.contains(planet1) && pair.contains(planet2)));
}
```

### 2. 組合盤範例

```dart
Future<void> calculateCompositeExample() async {
  final person1 = BirthData(
    name: '張三',
    dateTime: DateTime(1990, 5, 15, 14, 30),
    latitude: 25.0330,
    longitude: 121.5654,
    timezone: 'Asia/Taipei',
  );
  
  final person2 = BirthData(
    name: '李四',
    dateTime: DateTime(1988, 9, 22, 16, 45),
    latitude: 25.0330,
    longitude: 121.5654,
    timezone: 'Asia/Taipei',
  );
  
  final params = ChartCalculationParams(
    primaryPerson: person1,
    secondaryPerson: person2,
    chartType: ChartType.composite,
  );
  
  try {
    final astrologyService = AstrologyService();
    final compositeChart = await astrologyService.calculateChartData(params);
    
    print('=== 組合盤分析 ===');
    print('${person1.name} & ${person2.name} 的關係星盤');
    print('');
    
    // 顯示組合盤的重要配置
    print('組合盤行星位置:');
    for (final planet in compositeChart.planets) {
      print('${planet.name}: ${planet.longitude.toStringAsFixed(1)}° '
            '${planet.sign.name} 第${planet.house}宮');
    }
    
    // 分析關係重點
    print('\n關係分析重點:');
    
    // 找到太陽和月亮的組合位置
    final compositeSun = compositeChart.planets.firstWhere((p) => p.name == '太陽');
    final compositeMoon = compositeChart.planets.firstWhere((p) => p.name == '月亮');
    
    print('關係核心 (組合太陽): ${compositeSun.sign.name}座 第${compositeSun.house}宮');
    print('情感基調 (組合月亮): ${compositeMoon.sign.name}座 第${compositeMoon.house}宮');
    
    // 計算太陽月亮相位
    final sunMoonAspect = _findAspectBetween(compositeSun, compositeMoon, compositeChart.aspects);
    if (sunMoonAspect != null) {
      print('太陽月亮相位: ${sunMoonAspect.aspect} (${sunMoonAspect.orb.toStringAsFixed(1)}°)');
    }
    
  } catch (e) {
    print('組合盤計算失敗: $e');
  }
}

AspectInfo? _findAspectBetween(
  PlanetPosition planet1,
  PlanetPosition planet2,
  List<AspectInfo> aspects,
) {
  return aspects.firstWhereOrNull((aspect) =>
    (aspect.planet1.name == planet1.name && aspect.planet2.name == planet2.name) ||
    (aspect.planet1.name == planet2.name && aspect.planet2.name == planet1.name));
}
```

## 🌅 返照盤計算範例

### 太陽返照盤範例

```dart
Future<void> calculateSolarReturnExample() async {
  final birthData = BirthData(
    name: '王五',
    dateTime: DateTime(1985, 3, 10, 12, 0),
    latitude: 39.9042,
    longitude: 116.4074,
    timezone: 'Asia/Shanghai',
  );
  
  // 計算 2024 年的太陽返照
  final targetYear = 2024;
  
  final params = ChartCalculationParams(
    primaryPerson: birthData,
    chartType: ChartType.solarReturn,
    specificDate: DateTime(targetYear, 3, 10), // 大概日期
  );
  
  try {
    final astrologyService = AstrologyService();
    final solarReturnChart = await astrologyService.calculateChartData(params);
    
    print('=== ${targetYear}年太陽返照盤 ===');
    print('返照時間: ${solarReturnChart.returnDate}');
    print('');
    
    // 分析年度主題
    final returnSun = solarReturnChart.planets.firstWhere((p) => p.name == '太陽');
    final returnMoon = solarReturnChart.planets.firstWhere((p) => p.name == '月亮');
    final returnAsc = solarReturnChart.houses.ascendant;
    
    print('年度重點分析:');
    print('太陽位置: ${returnSun.sign.name}座 第${returnSun.house}宮');
    print('月亮位置: ${returnMoon.sign.name}座 第${returnMoon.house}宮');
    print('上升星座: ${_getZodiacSign(returnAsc).name}座');
    
    // 找出年度挑戰和機會
    final challengingAspects = solarReturnChart.aspects.where((aspect) =>
      aspect.aspect == '四分相' || aspect.aspect == '對沖').toList();
    
    final harmoniousAspects = solarReturnChart.aspects.where((aspect) =>
      aspect.aspect == '三分相' || aspect.aspect == '六分相').toList();
    
    if (challengingAspects.isNotEmpty) {
      print('\n年度挑戰:');
      for (final aspect in challengingAspects.take(3)) {
        print('${aspect.planet1.name} ${aspect.aspect} ${aspect.planet2.name}');
      }
    }
    
    if (harmoniousAspects.isNotEmpty) {
      print('\n年度機會:');
      for (final aspect in harmoniousAspects.take(3)) {
        print('${aspect.planet1.name} ${aspect.aspect} ${aspect.planet2.name}');
      }
    }
    
  } catch (e) {
    print('太陽返照盤計算失敗: $e');
  }
}
```

## 🔮 特殊計算範例

### 阿拉伯點計算範例

```dart
Future<void> calculateArabicPartsExample() async {
  final birthData = BirthData(
    name: '趙六',
    dateTime: DateTime(1992, 7, 8, 18, 20),
    latitude: 31.2304,
    longitude: 121.4737,
    timezone: 'Asia/Shanghai',
  );
  
  try {
    final astrologyService = AstrologyService();
    final natalChart = await astrologyService.calculateChartData(
      ChartCalculationParams(
        primaryPerson: birthData,
        chartType: ChartType.natal,
      ),
    );
    
    // 獲取基本行星位置
    final sun = natalChart.planets.firstWhere((p) => p.name == '太陽');
    final moon = natalChart.planets.firstWhere((p) => p.name == '月亮');
    final ascendant = natalChart.houses.ascendant;
    final venus = natalChart.planets.firstWhere((p) => p.name == '金星');
    final saturn = natalChart.planets.firstWhere((p) => p.name == '土星');
    
    // 判斷日夜出生
    final isDaytime = _isDaytimeBirth(sun, ascendant);
    
    print('=== 阿拉伯點計算 ===');
    print('出生時間: ${isDaytime ? "白天" : "夜晚"}');
    print('');
    
    // 計算幸運點
    final fortunePoint = _calculateFortunePoint(
      ascendant, sun.longitude, moon.longitude, isDaytime);
    print('幸運點: ${fortunePoint.toStringAsFixed(2)}° '
          '${_getZodiacSign(fortunePoint).name}座');
    
    // 計算精神點
    final spiritPoint = _calculateSpiritPoint(
      ascendant, sun.longitude, moon.longitude, isDaytime);
    print('精神點: ${spiritPoint.toStringAsFixed(2)}° '
          '${_getZodiacSign(spiritPoint).name}座');
    
    // 計算愛情點
    final lovePoint = _calculateLovePoint(
      ascendant, sun.longitude, venus.longitude, isDaytime);
    print('愛情點: ${lovePoint.toStringAsFixed(2)}° '
          '${_getZodiacSign(lovePoint).name}座');
    
    // 計算職業點
    final professionPoint = _calculateProfessionPoint(
      ascendant, moon.longitude, saturn.longitude);
    print('職業點: ${professionPoint.toStringAsFixed(2)}° '
          '${_getZodiacSign(professionPoint).name}座');
    
  } catch (e) {
    print('阿拉伯點計算失敗: $e');
  }
}

bool _isDaytimeBirth(PlanetPosition sun, double ascendant) {
  // 簡化判斷：太陽在地平線上方為白天
  final descendant = (ascendant + 180) % 360;
  
  if (ascendant < descendant) {
    return sun.longitude >= ascendant && sun.longitude <= descendant;
  } else {
    return sun.longitude >= ascendant || sun.longitude <= descendant;
  }
}

double _calculateFortunePoint(
  double ascendant, double sunLong, double moonLong, bool isDaytime) {
  if (isDaytime) {
    return (ascendant + moonLong - sunLong + 360) % 360;
  } else {
    return (ascendant + sunLong - moonLong + 360) % 360;
  }
}

double _calculateSpiritPoint(
  double ascendant, double sunLong, double moonLong, bool isDaytime) {
  if (isDaytime) {
    return (ascendant + sunLong - moonLong + 360) % 360;
  } else {
    return (ascendant + moonLong - sunLong + 360) % 360;
  }
}

double _calculateLovePoint(
  double ascendant, double sunLong, double venusLong, bool isDaytime) {
  if (isDaytime) {
    return (ascendant + venusLong - sunLong + 360) % 360;
  } else {
    return (ascendant + sunLong - venusLong + 360) % 360;
  }
}

double _calculateProfessionPoint(
  double ascendant, double moonLong, double saturnLong) {
  return (ascendant + moonLong - saturnLong + 360) % 360;
}
```

## 🛠️ 實用工具函數

### 角度和星座轉換

```dart
// 角度標準化
double normalizeAngle(double angle) {
  angle = angle % 360;
  return angle < 0 ? angle + 360 : angle;
}

// 獲取星座
ZodiacSign _getZodiacSign(double longitude) {
  final normalizedLong = normalizeAngle(longitude);
  final signIndex = (normalizedLong / 30).floor();
  return ZodiacSign.values[signIndex];
}

// 角度轉換為度分秒格式
String formatAngleToDMS(double angle) {
  final degrees = angle.floor();
  final minutes = ((angle - degrees) * 60).floor();
  final seconds = (((angle - degrees) * 60 - minutes) * 60).floor();
  return '${degrees}°${minutes.toString().padLeft(2, '0')}'${seconds.toString().padLeft(2, '0')}"';
}

// 計算兩個角度的中點
double calculateMidpoint(double angle1, double angle2) {
  double diff = (angle2 - angle1 + 360) % 360;
  if (diff > 180) diff -= 360;
  return normalizeAngle(angle1 + diff / 2);
}
```

### 時間處理工具

```dart
// 計算年齡（精確到天）
int calculateAgeInDays(DateTime birthDate, DateTime targetDate) {
  return targetDate.difference(birthDate).inDays;
}

// 計算年齡（精確到年）
double calculateAgeInYears(DateTime birthDate, DateTime targetDate) {
  return calculateAgeInDays(birthDate, targetDate) / 365.25;
}

// 格式化日期時間
String formatDateTime(DateTime dateTime) {
  return '${dateTime.year}年${dateTime.month}月${dateTime.day}日 '
         '${dateTime.hour.toString().padLeft(2, '0')}:'
         '${dateTime.minute.toString().padLeft(2, '0')}';
}
```

這些實作範例展示了 Astreal 中各種星盤計算的具體用法，開發者可以參考這些範例來實作自己的占星計算功能。
