# Astreal 功能模組詳細說明

## 📋 目錄
1. [占星計算模組](#占星計算模組)
2. [AI 解讀模組](#ai-解讀模組)
3. [資料管理模組](#資料管理模組)
4. [用戶界面模組](#用戶界面模組)
5. [認證付費模組](#認證付費模組)
6. [配置管理模組](#配置管理模組)

---

## 🧮 占星計算模組

### 核心功能
**Swiss Ephemeris 整合**：高精度天文計算引擎
- 支援 BCE 5400 到 CE 5400 的天文資料
- 精度達到角秒級別
- 支援多種曆法系統

### 星盤類型支援

#### 個人星盤
- **本命盤 (Natal Chart)**
  - 出生時刻天體位置圖
  - 顯示個人性格、天賦、潛能
  - 支援多種宮位系統

#### 預測類星盤
- **行運盤 (Transit Chart)**
  - 當前天體位置對本命盤的影響
  - 實時更新天體位置
  - 支援特定日期查詢

- **次限推運盤 (Secondary Progression)**
  - 一天等於一年的推運技法
  - 內在心理發展軌跡
  - 重要人生階段預測

- **三限推運盤 (Tertiary Progression)**
  - 一天等於一月的推運技法
  - 短期心理變化分析
  - 月度運勢預測

- **太陽弧推運盤 (Solar Arc Direction)**
  - 太陽弧度推進技法
  - 重大人生轉折預測
  - 精確的時間點分析

#### 返照盤類
- **太陽返照盤 (Solar Return)**
  - 太陽回歸出生位置的時刻
  - 年度運勢分析
  - 自動計算返照日期

- **月亮返照盤 (Lunar Return)**
  - 月亮回歸出生位置的時刻
  - 月度情緒週期分析
  - 獨立參數避免狀態污染

#### 關係星盤
- **比較盤 (Synastry)**
  - 兩人星盤疊加比較
  - 關係相容性分析
  - 互動模式探索

- **組合盤 (Composite Chart)**
  - 兩人星盤中點圖
  - 關係本質分析
  - 關係化學反應

- **時空中點盤 (Davison Chart)**
  - 時間地點中點計算
  - 關係命運分析
  - 相遇意義探索

- **馬克思盤 (Marx Chart)**
  - 關係動力分析
  - 內心感受探索
  - 關係中的角色定位

#### 特殊星盤
- **法達盤 (Firdaria)**
  - 古典時序技術
  - 人生階段劃分
  - 行星主導期分析

- **卜卦盤 (Horary Chart)**
  - 問題提出時刻星盤
  - 具體問題解答
  - 古典卜卦技法

- **事件盤 (Event Chart)**
  - 重要事件發生時刻
  - 事件影響分析
  - 時機選擇參考

- **日月蝕盤 (Eclipse Chart)**
  - 蝕相發生時刻星盤
  - 命運轉折點分析
  - 集體事件預測

- **二分二至盤 (Equinox/Solstice Chart)**
  - 季節轉換時刻星盤
  - 自然能量分析
  - 社會趨勢預測

### 天體計算

#### 傳統行星
- **太陽 (Sun)**：生命力、自我表達
- **月亮 (Moon)**：情感、直覺、需求
- **水星 (Mercury)**：溝通、思維、學習
- **金星 (Venus)**：愛情、美感、價值觀
- **火星 (Mars)**：行動力、慾望、競爭
- **木星 (Jupiter)**：擴展、智慧、幸運
- **土星 (Saturn)**：限制、責任、結構

#### 現代行星
- **天王星 (Uranus)**：革新、獨立、突變
- **海王星 (Neptune)**：夢想、靈性、迷惑
- **冥王星 (Pluto)**：轉化、重生、深層

#### 小行星
- **穀神星 (Ceres)**：滋養、照顧、農業
- **智神星 (Pallas)**：智慧、策略、正義
- **婚神星 (Juno)**：婚姻、承諾、伴侶關係
- **灶神星 (Vesta)**：奉獻、專注、神聖之火

#### 特殊點
- **南北交點 (Lunar Nodes)**：業力方向、靈魂課題
- **莉莉絲 (Lilith)**：陰暗面、原始慾望
- **福點 (Part of Fortune)**：物質幸運點
- **其他阿拉伯點**：各種專門分析點

### 宮位系統

#### Placidus 制
- 現代占星標準宮位制
- 基於時間等分原理
- 適合心理占星分析

#### 整宮制 (Whole Sign)
- 古典占星常用
- 每個星座對應一個宮位
- 簡潔明確的宮位劃分

#### 等宮制 (Equal House)
- 上升點為起點等分
- 每宮 30 度
- 適合特殊研究

#### 其他系統
- **Koch 制**：德國占星師發展
- **Regiomontanus 制**：中世紀系統
- **Campanus 制**：空間等分系統

### 相位計算

#### 主要相位
- **合相 (Conjunction)**：0° ± 容許度
- **對相 (Opposition)**：180° ± 容許度
- **三分相 (Trine)**：120° ± 容許度
- **四分相 (Square)**：90° ± 容許度
- **六分相 (Sextile)**：60° ± 容許度

#### 次要相位
- **半四分相 (Semi-Square)**：45°
- **倍半四分相 (Sesquiquadrate)**：135°
- **半六分相 (Semi-Sextile)**：30°
- **五分相 (Quintile)**：72°
- **十二分相 (Quincunx)**：150°

#### 容許度設定
- 不同行星組合有不同容許度
- 支援古典和現代容許度標準
- 可自定義容許度設定

---

## 🤖 AI 解讀模組

### 多家 AI 服務整合

#### OpenAI 整合
- **GPT-4 系列**：最先進的語言模型
- **GPT-3.5 Turbo**：高效能選擇
- **API 金鑰管理**：安全的金鑰存儲
- **使用量統計**：Token 使用追蹤

#### Anthropic 整合
- **Claude 3 系列**：高品質對話模型
- **Claude 2**：穩定版本選擇
- **安全性優勢**：更安全的 AI 回應
- **長文本支援**：處理複雜星盤分析

#### Groq 整合
- **高速推理**：超快回應速度
- **成本效益**：經濟實惠的選擇
- **多模型支援**：Llama、Mixtral 等

#### Google Gemini 整合
- **多模態能力**：文字、圖像處理
- **Gemini Pro**：專業級分析
- **整合便利**：Google 生態系統

### 解讀配置系統

#### 動態配置檔案
每種星盤類型都有專門的 JSON 配置檔案：

```json
{
  "version": "1.0.0",
  "chartType": "eclipse",
  "displayName": "日月蝕盤解讀",
  "description": "分析蝕相事件的深層影響",
  "options": [
    {
      "id": "collective_events",
      "title": "集體事件預測",
      "icon": "🌍",
      "color": "#FF6B6B",
      "keyPoint": "分析蝕相對社會、政治、經濟的影響",
      "questions": [
        "這次蝕相會對社會產生什麼影響？",
        "政治局勢會有什麼變化？"
      ]
    }
  ]
}
```

#### 智能解讀引導
- **keyPoint 系統**：為 AI 提供專業分析重點
- **建議問題**：幫助用戶深入探索
- **視覺設計**：統一的圖標和顏色系統
- **多語言支援**：支援國際化配置

### 解讀類型

#### 個人分析
- **性格特質分析**：基於本命盤的深度性格解讀
- **天賦能力探索**：發現個人潛在才能
- **人生方向指導**：提供人生規劃建議
- **心理狀態分析**：了解內在心理模式

#### 關係分析
- **相容性評估**：分析兩人關係相容度
- **互動模式探索**：了解相處方式
- **關係發展預測**：預測關係走向
- **溝通建議**：提供改善關係的建議

#### 預測分析
- **運勢預測**：短期和長期運勢分析
- **重要時機**：把握人生重要時刻
- **挑戰與機會**：識別未來的機遇和挑戰
- **應對策略**：提供實用的應對建議

#### 專業分析
- **職業發展**：事業方向和發展建議
- **財務狀況**：財運分析和理財建議
- **健康狀況**：身心健康的占星指導
- **靈性成長**：精神層面的發展指引

### 解讀品質控制

#### 提示詞優化
- **專業術語**：使用準確的占星術語
- **結構化輸出**：清晰的解讀結構
- **個人化內容**：針對具體星盤的分析
- **實用建議**：提供可行的生活指導

#### 結果後處理
- **格式化輸出**：美觀的文字排版
- **關鍵詞高亮**：重要內容突出顯示
- **分段組織**：邏輯清晰的內容結構
- **摘要提取**：核心要點總結

---

## 💾 資料管理模組

### 出生資料管理

#### 資料模型設計
```dart
class BirthData {
  final String id;              // 唯一識別碼
  final String name;            // 姓名
  final DateTime dateTime;      // 出生日期時間
  final String birthPlace;      // 出生地點
  final double latitude;        // 緯度
  final double longitude;       // 經度
  final String? timezone;       // 時區
  final String category;        // 分類標籤
  final List<String> tags;      // 標籤列表
  final DateTime createdAt;     // 創建時間
  final DateTime updatedAt;     // 更新時間
}
```

#### CRUD 操作
- **Create**：新增出生資料
- **Read**：讀取和查詢資料
- **Update**：更新現有資料
- **Delete**：刪除不需要的資料

#### 分類系統
- **預設分類**：家人、朋友、客戶、名人
- **自定義分類**：用戶可創建專屬分類
- **標籤系統**：多標籤支援，靈活分類
- **顏色編碼**：視覺化分類識別

#### 搜尋功能
- **姓名搜尋**：支援模糊搜尋
- **地點搜尋**：按出生地點篩選
- **標籤搜尋**：按標籤快速篩選
- **日期範圍**：按出生日期範圍查詢

### 批量操作

#### CSV 匯入匯出
- **標準格式**：支援通用 CSV 格式
- **欄位映射**：靈活的欄位對應
- **錯誤處理**：匯入錯誤的友善提示
- **進度顯示**：批量操作進度條

#### 批量編輯
- **多選操作**：支援多筆資料同時操作
- **批量標籤**：一次為多筆資料添加標籤
- **批量分類**：批量修改資料分類
- **批量刪除**：安全的批量刪除功能

### 雲端同步

#### Firebase 整合
- **即時同步**：資料變更即時同步
- **離線支援**：離線時本地操作，上線後同步
- **衝突解決**：智能處理同步衝突
- **版本控制**：保留資料變更歷史

#### 備份還原
- **自動備份**：定期自動備份到雲端
- **手動備份**：用戶主動觸發備份
- **選擇性還原**：可選擇部分資料還原
- **合併策略**：備份還原時的資料合併

### 資料安全

#### 本地加密
- **敏感資料加密**：出生資料本地加密存儲
- **金鑰管理**：安全的加密金鑰管理
- **存取控制**：限制資料存取權限

#### 隱私保護
- **資料匿名化**：可選的資料匿名化
- **GDPR 合規**：符合歐盟資料保護法規
- **用戶控制**：用戶完全控制自己的資料
- **資料刪除**：徹底刪除用戶資料的能力

---

## 🎨 用戶界面模組

### 雙模式設計

#### Starmaster 專業模式
- **目標用戶**：專業占星師、進階用戶
- **主題色彩**：pastelSkyBlue (#E6F0FA)
- **功能特色**：
  - 完整功能存取權限
  - 專業占星術語
  - 詳細的設定選項
  - 複雜的分析工具

#### Starlight 初心者模式
- **目標用戶**：占星初學者、一般用戶
- **主題色彩**：lightCornsilk (#FFF8DC)
- **功能特色**：
  - 簡化的操作流程
  - 通俗化語言描述
  - 引導式用戶體驗
  - 基礎功能優先

### 核心頁面設計

#### 主頁面 (Home Page)
**Starmaster 版本**：
- 專業儀表板布局
- 快速操作區域
- 最近使用的星盤
- 專業工具快捷方式

**Starlight 版本**：
- 溫馨歡迎界面
- 簡化的功能入口
- 學習資源推薦
- 每日占星小貼士

#### 星盤頁面 (Chart Page)
**功能特色**：
- 高品質星盤繪製
- 互動式星盤操作
- 多種顯示模式
- 詳細資訊面板

**操作功能**：
- 縮放和平移
- 行星資訊查看
- 相位線顯示切換
- 星盤設定調整

#### 分析頁面 (Analysis Page)
**專業模式功能**：
- 完整的占星分析工具
- 專業術語和詳細解釋
- 多種分析技法選擇
- 自定義分析參數

**初心者模式功能**：
- 簡化的分析選項
- 通俗易懂的解釋
- 引導式分析流程
- 基礎分析結果

#### 設定頁面 (Settings Page)
**系統設定**：
- 主題和外觀設定
- 語言和地區設定
- 通知和提醒設定
- 資料備份設定

**占星設定**：
- 宮位系統選擇
- 行星顯示設定
- 相位容許度設定
- 占星模式選擇

### UI 組件系統

#### 基礎組件
- **StyledButton**：統一風格按鈕
- **StyledCard**：卡片式容器
- **StyledDialog**：對話框組件
- **StyledTextField**：輸入框組件

#### 專業組件
- **ChartWidget**：星盤顯示組件
- **PlanetInfoCard**：行星資訊卡片
- **AspectTable**：相位表格組件
- **HouseInfoPanel**：宮位資訊面板

#### 互動組件
- **PersonSelector**：人物選擇器
- **DateTimePicker**：日期時間選擇器
- **LocationPicker**：地點選擇器
- **ChartTypePicker**：星盤類型選擇器

### 響應式設計

#### 螢幕適配
- **手機版**：垂直布局，單欄顯示
- **平板版**：混合布局，雙欄顯示
- **桌面版**：水平布局，多欄顯示
- **Web 版**：完整桌面體驗

#### 動態布局
- **自適應網格**：根據螢幕尺寸調整
- **彈性容器**：內容自動調整大小
- **斷點系統**：不同尺寸的布局斷點
- **方向適配**：橫豎屏自動適配

### 主題系統

#### 色彩設計
- **主色調**：royalIndigo (#3F51B5)
- **輔助色**：solarAmber (#F5A623)
- **表面色**：indigoSurface (#303F9F)
- **強調色**：indigoLight (#7986CB)

#### 字體系統
- **標題字體**：Roboto Bold
- **內文字體**：Roboto Regular
- **占星符號**：astro_one_font.ttf
- **數字字體**：Roboto Mono

#### 圖標系統
- **Material Icons**：系統圖標
- **占星符號**：專業占星符號
- **自定義圖標**：品牌專屬圖標
- **SVG 圖標**：可縮放向量圖標

---

## 🔐 認證付費模組

### 用戶認證系統

#### 多平台登入
**Google 登入**：
- Android：原生 Google Sign-In SDK
- iOS：Google Sign-In for iOS
- Web：Firebase Auth + signInWithPopup()
- 統一的用戶體驗

**Apple 登入**：
- iOS：原生 Sign in with Apple
- 隱私保護優先
- 快速便捷的認證流程
- 符合 Apple 審核要求

**匿名登入**：
- 無需註冊即可使用基礎功能
- 後續可升級為正式帳戶
- 資料遷移支援
- 降低使用門檻

#### Firebase 認證整合
- **統一認證管理**：所有登入方式統一處理
- **用戶狀態管理**：即時的登入狀態更新
- **安全性保障**：企業級安全標準
- **跨平台同步**：多設備帳戶同步

### 付費系統架構

#### 解讀次數管理
**統一次數系統**：
- 免費試用次數
- 單次購買次數
- 訂閱無限次數
- 統一的扣費邏輯

**權限檢查流程**：
```dart
Future<bool> hasInterpretationPermission() async {
  // 1. 檢查訂閱狀態
  if (await isPremiumUser()) return true;

  // 2. 檢查單次購買次數
  final remaining = await getRemainingSinglePurchases();
  if (remaining > 0) return true;

  // 3. 無權限
  return false;
}
```

#### Firebase 支付記錄
**支付記錄管理**：
- 完整的購買記錄追蹤
- 用戶專屬支付集合
- 伺服器時間戳記錄
- 支付狀態即時更新

**資料結構**：
```dart
class PaymentRecord {
  final String id;
  final String userId;
  final String productId;
  final double amount;
  final String currency;
  final DateTime purchaseDate;
  final PaymentStatus status;
  final Map<String, dynamic> metadata;
}
```

#### Apple 規範合規引導

**合法引導方式**：
1. **登入提示法**：
   - "如果您已經是 VIP 會員，請直接登入帳號"
   - 不直接引導付款，讓用戶自然尋找

2. **關於我們頁面**：
   - 放置官網連結和社群平台
   - 間接引導到付費頁面
   - 符合 Apple 審核要求

3. **免費報告引導**：
   - 提供免費基礎分析
   - 在結果末尾提及完整功能
   - 引導到官網深入內容

4. **社群平台引導**：
   - IG、Facebook 主動推廣
   - 官網直接付費連結
   - App 外流量操作

### 訂閱管理

#### 訂閱方案
- **月度訂閱**：靈活的短期選擇
- **年度訂閱**：經濟實惠的長期方案
- **季度訂閱**：平衡的中期選擇
- **終身購買**：一次付費永久使用

#### 訂閱狀態管理
- **即時狀態檢查**：準確的訂閱狀態
- **自動續費處理**：無縫的續費體驗
- **取消訂閱處理**：友善的取消流程
- **恢復購買**：跨設備購買恢復

---

## ⚙️ 配置管理模組

### Remote Config 系統

#### 動態配置管理
**AI 模型配置**：
- 動態切換 AI 服務提供商
- 調整模型參數和設定
- A/B 測試不同配置
- 即時配置更新

**功能開關**：
- 新功能灰度發布
- 緊急功能關閉
- 用戶群體定向功能
- 平台特定功能控制

#### 解讀配置系統
**配置檔案結構**：
```json
{
  "version": "1.0.0",
  "chartType": "natal",
  "displayName": "本命盤解讀",
  "options": [
    {
      "id": "personality_analysis",
      "title": "性格特質分析",
      "icon": "👤",
      "color": "#4CAF50",
      "keyPoint": "分析個人核心性格特質",
      "questions": ["我的主要性格特點是什麼？"]
    }
  ]
}
```

**配置管理特色**：
- 版本控制和回滾
- 即時配置更新
- 配置驗證和錯誤處理
- 本地快取機制

### 占星模式配置

#### 三種預設模式
**古典占星模式**：
- 傳統七星（太陽到土星）
- 整宮制宮位系統
- 古典相位容許度
- 傳統占星技法

**現代占星模式**：
- 包含現代行星（天王星、海王星、冥王星）
- Placidus 宮位制
- 現代相位容許度
- 心理占星學取向

**特殊模式**：
- 包含小行星和特殊點
- 等宮制宮位系統
- 寬鬆的相位容許度
- 進階研究用途

#### 模式切換機制
- **一鍵切換**：快速切換占星模式
- **設定保存**：每種模式獨立設定
- **自定義模式**：用戶可創建專屬模式
- **設定同步**：跨設備模式同步

### 系統設定管理

#### 本地設定存儲
- **SharedPreferences**：輕量級設定存儲
- **加密存儲**：敏感設定加密保護
- **設定分類**：按功能模組分類管理
- **預設值管理**：合理的預設設定

#### 設定同步
- **雲端同步**：設定跨設備同步
- **衝突解決**：智能處理設定衝突
- **版本管理**：設定變更歷史記錄
- **備份還原**：設定備份和還原功能

這個詳細的功能模組說明為開發團隊提供了完整的功能實作指南，涵蓋了 Astreal 專案的所有核心模組和功能特色。
