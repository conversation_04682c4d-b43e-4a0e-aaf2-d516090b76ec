# Astreal 開發指南

## 開發環境設置

### 必要工具
- **Flutter SDK 3.x**：主要開發框架
- **Dart SDK**：程式語言支持
- **Android Studio / VS Code**：推薦 IDE
- **Git**：版本控制
- **Firebase CLI**：雲端服務（可選）

### 專案設置
```bash
# 克隆專案
git clone <repository-url>
cd astreal

# 安裝依賴
flutter pub get

# 運行專案
flutter run
```

### 開發環境配置
```yaml
# pubspec.yaml 主要依賴
dependencies:
  flutter:
    sdk: flutter
  provider: ^6.0.0
  shared_preferences: ^2.0.0
  sweph: ^2.10.0
  http: ^0.13.0
  intl: ^0.18.0
```

## 編碼規範

### 命名規範
```dart
// 類別名稱：PascalCase
class ChartViewModel extends ChangeNotifier {}

// 方法和變數：camelCase
void calculateChart() {}
String selectedPerson = '';

// 常數：SCREAMING_SNAKE_CASE
static const int MAX_PLANETS = 15;

// 私有成員：前綴下劃線
String _privateField = '';
void _privateMethod() {}
```

### 檔案組織
```
lib/
├── models/           # 數據模型
├── services/         # 業務服務
├── viewmodels/       # 視圖模型
├── ui/              # 用戶界面
│   ├── pages/       # 頁面組件
│   └── widgets/     # 共用組件
├── utils/           # 工具類別
└── constants/       # 常數定義
```

### 註釋規範
```dart
/// 星盤計算服務
/// 
/// 提供所有占星計算功能，包括：
/// - 行星位置計算
/// - 宮位系統計算
/// - 相位分析
class AstrologyService {
  /// 計算星盤數據
  /// 
  /// [chartData] 星盤基本資料
  /// [planetVisibility] 行星可見性設定
  /// 
  /// 返回完整的星盤計算結果
  Future<ChartData> calculateChartData(
    ChartData chartData, {
    Map<int, bool>? planetVisibility,
  }) async {
    // 實現邏輯
  }
}
```

## MVVM 架構實踐

### Model 層開發
```dart
// 數據模型應該：
// 1. 不可變（使用 final 字段）
// 2. 支持 JSON 序列化
// 3. 實現 copyWith 方法
// 4. 包含數據驗證

class BirthData {
  final String id;
  final String name;
  final DateTime birthDate;
  
  const BirthData({
    required this.id,
    required this.name,
    required this.birthDate,
  });
  
  // JSON 序列化
  factory BirthData.fromJson(Map<String, dynamic> json) => BirthData(
    id: json['id'] as String,
    name: json['name'] as String,
    birthDate: DateTime.parse(json['birthDate'] as String),
  );
  
  Map<String, dynamic> toJson() => {
    'id': id,
    'name': name,
    'birthDate': birthDate.toIso8601String(),
  };
  
  // 不可變更新
  BirthData copyWith({
    String? id,
    String? name,
    DateTime? birthDate,
  }) => BirthData(
    id: id ?? this.id,
    name: name ?? this.name,
    birthDate: birthDate ?? this.birthDate,
  );
}
```

### ViewModel 層開發
```dart
// ViewModel 應該：
// 1. 繼承 ChangeNotifier
// 2. 管理 UI 狀態
// 3. 調用 Service 處理業務邏輯
// 4. 提供錯誤處理

class ChartViewModel extends ChangeNotifier {
  // 私有狀態
  bool _isLoading = false;
  ChartData? _chartData;
  String? _errorMessage;
  
  // 公開 getter
  bool get isLoading => _isLoading;
  ChartData? get chartData => _chartData;
  String? get errorMessage => _errorMessage;
  
  // 業務方法
  Future<void> calculateChart(BirthData person) async {
    try {
      _setLoading(true);
      _clearError();
      
      final result = await AstrologyService().calculateChartData(
        ChartData(chartType: ChartType.natal, primaryPerson: person),
      );
      
      _chartData = result;
    } catch (e) {
      _setError('計算星盤失敗: $e');
    } finally {
      _setLoading(false);
    }
  }
  
  // 私有輔助方法
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }
  
  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }
  
  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }
}
```

### View 層開發
```dart
// UI 組件應該：
// 1. 使用 Consumer 監聽 ViewModel
// 2. 分離 UI 邏輯和業務邏輯
// 3. 處理載入和錯誤狀態
// 4. 保持組件的單一職責

class ChartPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('星盤')),
      body: Consumer<ChartViewModel>(
        builder: (context, viewModel, child) {
          // 錯誤狀態
          if (viewModel.errorMessage != null) {
            return ErrorWidget(message: viewModel.errorMessage!);
          }
          
          // 載入狀態
          if (viewModel.isLoading) {
            return LoadingWidget();
          }
          
          // 正常狀態
          if (viewModel.chartData != null) {
            return ChartViewWidget(chartData: viewModel.chartData!);
          }
          
          // 空狀態
          return EmptyStateWidget(
            message: '請選擇一個人物來計算星盤',
            onAction: () => _selectPerson(context),
          );
        },
      ),
    );
  }
}
```

## 服務層開發

### 服務類別設計原則
```dart
// 服務應該：
// 1. 單一職責
// 2. 無狀態（stateless）
// 3. 提供清晰的 API
// 4. 包含錯誤處理

class BirthDataService {
  static const String _storageKey = 'birthDataList';
  
  /// 獲取所有出生資料
  Future<List<BirthData>> getAllBirthData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString(_storageKey);
      
      if (jsonString == null) return [];
      
      final List<dynamic> jsonList = jsonDecode(jsonString);
      return jsonList.map((json) => BirthData.fromJson(json)).toList();
    } catch (e) {
      throw StorageException('無法載入出生資料: $e');
    }
  }
  
  /// 保存出生資料
  Future<void> saveBirthData(BirthData data) async {
    try {
      final allData = await getAllBirthData();
      
      // 檢查重複
      final existingIndex = allData.indexWhere((item) => item.id == data.id);
      if (existingIndex >= 0) {
        allData[existingIndex] = data;
      } else {
        allData.add(data);
      }
      
      // 保存到本地
      final prefs = await SharedPreferences.getInstance();
      final jsonString = jsonEncode(allData.map((item) => item.toJson()).toList());
      await prefs.setString(_storageKey, jsonString);
    } catch (e) {
      throw StorageException('無法保存出生資料: $e');
    }
  }
}
```

## 錯誤處理最佳實踐

### 自定義異常類別
```dart
// 定義應用程式特定的異常
abstract class AstrealException implements Exception {
  final String message;
  const AstrealException(this.message);
  
  @override
  String toString() => 'AstrealException: $message';
}

class CalculationException extends AstrealException {
  const CalculationException(String message) : super(message);
}

class StorageException extends AstrealException {
  const StorageException(String message) : super(message);
}

class NetworkException extends AstrealException {
  const NetworkException(String message) : super(message);
}
```

### 錯誤處理模式
```dart
// 在 ViewModel 中統一處理錯誤
Future<void> performAction() async {
  try {
    _setLoading(true);
    await _businessLogic();
  } on CalculationException catch (e) {
    _setError('計算錯誤: ${e.message}');
  } on NetworkException catch (e) {
    _setError('網路錯誤: ${e.message}');
  } catch (e) {
    _setError('未知錯誤: $e');
    // 記錄到日誌系統
    logger.error('Unexpected error', e);
  } finally {
    _setLoading(false);
  }
}
```

## 測試指南

### 單元測試
```dart
// 測試 ViewModel 邏輯
void main() {
  group('ChartViewModel', () {
    late ChartViewModel viewModel;
    
    setUp(() {
      viewModel = ChartViewModel();
    });
    
    test('should calculate chart successfully', () async {
      // Arrange
      final person = BirthData(
        id: '1',
        name: 'Test Person',
        birthDate: DateTime(1990, 1, 1),
        birthPlace: 'Test City',
        latitude: 25.0,
        longitude: 121.0,
      );
      
      // Act
      await viewModel.calculateChart(person);
      
      // Assert
      expect(viewModel.isLoading, false);
      expect(viewModel.chartData, isNotNull);
      expect(viewModel.errorMessage, isNull);
    });
  });
}
```

### Widget 測試
```dart
// 測試 UI 組件
void main() {
  testWidgets('ChartPage should show loading indicator', (tester) async {
    // Arrange
    final viewModel = MockChartViewModel();
    when(viewModel.isLoading).thenReturn(true);
    
    // Act
    await tester.pumpWidget(
      MaterialApp(
        home: ChangeNotifierProvider<ChartViewModel>.value(
          value: viewModel,
          child: ChartPage(),
        ),
      ),
    );
    
    // Assert
    expect(find.byType(CircularProgressIndicator), findsOneWidget);
  });
}
```

## 性能優化建議

### 1. 計算優化
```dart
// 使用 Isolate 進行重計算
Future<ChartData> calculateChartInBackground(ChartCalculationParams params) {
  return compute(_calculateChart, params);
}

static ChartData _calculateChart(ChartCalculationParams params) {
  // 在獨立線程中執行計算
  return AstrologyService().calculateChartData(params.chartData);
}
```

### 2. 記憶體優化
```dart
// 及時釋放資源
@override
void dispose() {
  _timer?.cancel();
  _subscription?.cancel();
  _controller.dispose();
  super.dispose();
}
```

### 3. UI 優化
```dart
// 使用 ListView.builder 處理大列表
ListView.builder(
  itemCount: items.length,
  itemBuilder: (context, index) => ItemWidget(items[index]),
)

// 使用 const 構造函數
const LoadingWidget({Key? key}) : super(key: key);
```

## 部署和發布

### 版本管理
```yaml
# pubspec.yaml
version: 1.2.3+4
# 1.2.3 是版本號，4 是構建號
```

### 構建命令
```bash
# Android APK
flutter build apk --release

# Android App Bundle
flutter build appbundle --release

# iOS
flutter build ios --release

# Web
flutter build web --release
```

### 代碼混淆
```bash
# 啟用代碼混淆
flutter build apk --obfuscate --split-debug-info=build/debug-info
```

這個開發指南提供了 Astreal 專案開發的最佳實踐和標準流程，確保代碼品質和開發效率。
