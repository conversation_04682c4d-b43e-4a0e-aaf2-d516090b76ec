# Astreal 日誌系統使用指南

## 📋 概述

Astreal 應用程式配備了完整的日誌管理系統，支援本地持久化存儲和 Firebase Storage 雲端上傳功能。這個系統可以幫助開發團隊追蹤應用程式運行狀況、診斷問題和優化性能。

## 🏗️ 系統架構

### 核心組件

1. **PersistentLogger** - 持久化日誌記錄器
   - 本地文件存儲
   - 日誌輪轉和清理
   - Firebase Storage 上傳

2. **LogManagementService** - 日誌管理服務
   - 崩潰報告收集
   - 性能日誌記錄
   - 設定管理

3. **AppInitializationService** - 應用初始化服務
   - 全域錯誤處理
   - 生命週期監控
   - 性能監控

## 🚀 快速開始

### 基本使用

```dart
import 'package:astreal/core/utils/logger_utils.dart';

// 基本日誌記錄
logger.d('Debug 訊息');
logger.i('Info 訊息');
logger.w('Warning 訊息');
logger.e('Error 訊息');

// 帶錯誤和堆疊追蹤的日誌
try {
  // 一些可能出錯的代碼
} catch (e, stackTrace) {
  logger.e('操作失敗', e, stackTrace);
}
```

### 性能監控

```dart
import 'package:astreal/core/services/app_initialization_service.dart';

// 監控異步操作
final result = await PerformanceMonitor.monitor(
  '星盤計算',
  () async {
    return await calculateChart(birthData);
  },
  metadata: {'chart_type': 'natal'},
);

// 監控同步操作
final result = PerformanceMonitor.monitorSync(
  '資料處理',
  () {
    return processData(data);
  },
);
```

### 崩潰報告

```dart
import 'package:astreal/data/services/api/log_management_service.dart';

// 手動記錄崩潰報告
try {
  // 危險操作
} catch (e, stackTrace) {
  await LogManagementService.instance.logCrashReport(
    error: e.toString(),
    stackTrace: stackTrace,
    additionalInfo: {
      'user_action': '計算星盤',
      'chart_type': 'natal',
      'user_id': currentUser?.uid,
    },
  );
}
```

## ⚙️ 配置設定

### 日誌管理設定

```dart
// 獲取當前設定
final settings = LogManagementService.instance.settings;

// 更新設定
final newSettings = LogManagementSettings(
  autoUpload: true,              // 自動上傳日誌
  autoUploadCrashes: true,       // 自動上傳崩潰報告
  enablePerformanceLogs: true,   // 啟用性能日誌
  maxLogFileSize: 5 * 1024 * 1024, // 最大日誌文件大小 (5MB)
  logRetentionDays: 7,           // 日誌保留天數
);

await LogManagementService.instance.updateSettings(newSettings);
```

### 初始化配置

在 `main.dart` 中，日誌系統會自動初始化：

```dart
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // 初始化應用程式服務（包含日誌系統）
  await AppInitializationService.initialize();
  
  // 註冊生命週期監聽器
  AstrealLifecycleObserver.instance.register();
  
  runApp(MyApp());
}
```

## 📊 日誌管理 UI

### 存取日誌管理頁面

```dart
import 'package:astreal/presentation/pages/settings/log_management_page.dart';

// 導航到日誌管理頁面
Navigator.push(
  context,
  MaterialPageRoute(builder: (context) => const LogManagementPage()),
);
```

### 功能特色

1. **日誌統計**
   - 本地日誌文件大小
   - 已上傳日誌數量
   - 待上傳崩潰報告數量
   - 最後上傳時間

2. **設定管理**
   - 自動上傳開關
   - 崩潰報告自動上傳
   - 性能日誌啟用/停用

3. **操作功能**
   - 手動上傳日誌
   - 清理本地日誌
   - 查看上傳歷史

## 🔧 進階功能

### 自定義日誌輸出

```dart
import 'package:astreal/core/utils/persistent_logger.dart';

// 直接使用持久化日誌器
PersistentLogger.instance.i('自定義訊息');

// 獲取日誌統計
final fileSize = await PersistentLogger.instance.getLogFileSize();
final uploadedLogs = PersistentLogger.instance.getUploadedLogs();
```

### 手動上傳日誌

```dart
// 上傳當前日誌文件
final success = await PersistentLogger.instance.uploadCurrentLogFile();

// 上傳所有類型的日誌
final success = await LogManagementService.instance.uploadLogs();
```

### 日誌清理

```dart
// 清理本地日誌文件
await PersistentLogger.instance.clearLocalLogs();

// 清理所有本地日誌（包括崩潰報告和性能日誌）
await LogManagementService.instance.clearAllLogs();
```

## 📁 文件結構

### 本地存儲位置

```
應用程式文檔目錄/
├── logs/
│   ├── astreal_app.log          # 主日誌文件
│   ├── astreal_app.log.1234.bak # 輪轉備份文件
│   └── ...
```

### Firebase Storage 結構

```
user_logs/
├── {user_id}/
│   ├── app_log_1234567890.log      # 應用程式日誌
│   ├── crashes/
│   │   └── crash_reports_1234.json # 崩潰報告
│   └── performance/
│       └── performance_logs_1234.json # 性能日誌
```

## 🔒 安全性和隱私

### 資料保護

1. **本地加密**：敏感日誌資料在本地加密存儲
2. **用戶控制**：用戶可以完全控制日誌上傳
3. **自動清理**：舊日誌文件會自動清理
4. **匿名化**：可選的資料匿名化功能

### 上傳條件

- 用戶必須已登入 Firebase
- 用戶必須明確同意上傳
- 網路連線正常
- 日誌文件不為空

## 📈 監控和分析

### 性能指標

系統會自動記錄以下性能指標：

- 星盤計算時間
- AI 解讀響應時間
- 資料庫操作時間
- 網路請求時間
- UI 渲染時間

### 錯誤追蹤

自動捕獲和記錄：

- Flutter 框架錯誤
- 平台特定錯誤
- 未處理的異常
- 網路錯誤
- 資料庫錯誤

## 🛠️ 開發最佳實踐

### 日誌級別使用指南

- **Debug (d)**：開發調試信息，生產環境不記錄
- **Info (i)**：一般信息，重要操作記錄
- **Warning (w)**：警告信息，可能的問題
- **Error (e)**：錯誤信息，需要關注的問題
- **Fatal (f)**：嚴重錯誤，可能導致崩潰

### 日誌內容建議

```dart
// ✅ 好的日誌實踐
logger.i('用戶登入成功: ${user.email}');
logger.w('API 響應緩慢: ${duration.inMilliseconds}ms');
logger.e('星盤計算失敗: $error', error, stackTrace);

// ❌ 避免的做法
logger.i('success'); // 信息不夠具體
logger.e('error'); // 沒有上下文信息
logger.d('${sensitiveData}'); // 記錄敏感資料
```

### 性能監控建議

```dart
// 監控關鍵操作
PerformanceMonitor.monitor('用戶登入', () async {
  return await authService.signIn(email, password);
});

// 監控資料庫操作
PerformanceMonitor.monitor('載入出生資料', () async {
  return await birthDataService.loadAll();
});

// 監控 AI 請求
PerformanceMonitor.monitor('AI 解讀', () async {
  return await aiService.interpret(chartData);
});
```

## 🔍 故障排除

### 常見問題

1. **日誌文件過大**
   - 系統會自動輪轉大文件
   - 可以手動清理舊日誌
   - 調整 `maxLogFileSize` 設定

2. **上傳失敗**
   - 檢查網路連線
   - 確認用戶已登入
   - 檢查 Firebase Storage 權限

3. **性能影響**
   - 日誌寫入是異步的，不會阻塞 UI
   - 使用緩衝區減少 I/O 操作
   - 可以停用性能日誌以提升性能

### 除錯技巧

```dart
// 檢查日誌系統狀態
final statistics = await LogManagementService.instance.getLogStatistics();
logger.i('日誌統計: ${statistics.localLogFileSize} bytes');

// 強制刷新日誌緩衝區
await PersistentLogger.instance.dispose();
await PersistentLogger.instance.initialize();
```

## 📞 支援

如果您在使用日誌系統時遇到問題：

1. 檢查應用程式是否正確初始化
2. 確認 Firebase 配置正確
3. 查看控制台輸出的錯誤信息
4. 聯絡開發團隊獲得支援

---

**注意**：日誌系統會記錄應用程式的運行狀況，請確保不要在日誌中記錄用戶的敏感個人資料。
