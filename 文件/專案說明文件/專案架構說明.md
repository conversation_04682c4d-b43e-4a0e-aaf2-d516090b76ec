# Astreal 占星應用專案架構說明

## 專案概述

Astreal 是一個基於 Flutter 開發的專業占星應用程式，採用 MVVM（Model-View-ViewModel）架構模式，整合 Swiss Ephemeris 占星計算引擎和多家 AI 服務提供商，為用戶提供全面的占星分析功能。

## 整體架構設計

### 架構模式
- **MVVM 架構**：採用 Model-View-ViewModel 設計模式
- **Provider 狀態管理**：使用 Provider + ChangeNotifier 進行狀態管理
- **服務導向架構**：核心業務邏輯封裝在獨立的服務類別中
- **組件化設計**：UI 組件高度模組化，支持重用和維護

### 目錄結構

```
lib/
├── main.dart                    # 應用程式入口點
├── constants/                   # 常數定義
│   ├── astrology_constants.dart # 占星學常數
│   └── ascension_table.dart     # 上升時間表
├── models/                      # 數據模型層
│   ├── birth_data.dart         # 出生資料模型
│   ├── chart_data.dart         # 星盤資料模型
│   ├── planet_position.dart    # 行星位置模型
│   ├── aspect_info.dart        # 相位資訊模型
│   ├── chart_type.dart         # 星盤類型枚舉
│   ├── chart_settings.dart     # 星盤設定模型
│   └── ...                     # 其他業務模型
├── services/                    # 服務層
│   ├── astrology_service.dart  # 占星計算核心服務
│   ├── birth_data_service.dart # 出生資料管理服務
│   ├── ai_api_service.dart     # AI API 整合服務
│   ├── chart_interpretation_service.dart # 星盤解讀服務
│   └── ...                     # 其他業務服務
├── viewmodels/                  # 視圖模型層
│   ├── home_viewmodel.dart     # 首頁視圖模型
│   ├── chart_viewmodel.dart    # 星盤視圖模型
│   ├── files_viewmodel.dart    # 檔案管理視圖模型
│   └── ...                     # 其他視圖模型
├── ui/                         # 用戶界面層
│   ├── pages/                  # 頁面組件
│   │   ├── main/              # 主要頁面
│   │   │   ├── home_page.dart # 首頁
│   │   │   ├── files_page.dart # 檔案管理頁
│   │   │   └── finance_page.dart # 理財占星頁
│   │   ├── chart_page.dart    # 星盤顯示頁
│   │   └── ...                # 其他頁面
│   ├── widgets/               # 共用組件
│   │   ├── chart_view_widget.dart # 星盤顯示組件
│   │   ├── planet_list_widget.dart # 行星列表組件
│   │   └── ...                # 其他組件
│   ├── AppTheme.dart          # 應用主題定義
│   └── theme_provider.dart    # 主題管理器
├── utils/                      # 工具類別
│   ├── astrology_calculator.dart # 占星計算工具
│   ├── julian_date_utils.dart # 儒略日轉換工具
│   ├── logger_utils.dart      # 日誌工具
│   └── ...                    # 其他工具類別
└── widgets/                    # 全域組件
    ├── styled_card.dart       # 樣式化卡片
    ├── loading_state.dart     # 載入狀態組件
    └── ...                    # 其他全域組件
```

## 核心架構層級說明

### 1. 模型層 (Models)

**職責**：定義應用程式的數據結構和業務實體

**主要類別**：
- `BirthData`：出生資料模型，包含個人基本資訊、出生時間地點等
- `ChartData`：星盤資料模型，包含行星位置、宮位、相位等計算結果
- `PlanetPosition`：行星位置模型，包含行星的天文座標和占星屬性
- `AspectInfo`：相位資訊模型，定義行星間的角度關係
- `ChartType`：星盤類型枚舉，支持本命盤、行運盤、合盤等多種類型

**設計特點**：
- 支持 JSON 序列化/反序列化
- 實現 `copyWith` 方法支持不可變更新
- 包含完整的數據驗證邏輯

### 2. 服務層 (Services)

**職責**：封裝核心業務邏輯和外部服務整合

**核心服務類別**：

#### AstrologyService
- **功能**：占星計算核心引擎
- **職責**：
  - Swiss Ephemeris 初始化和管理
  - 行星位置計算
  - 宮位系統計算
  - 相位分析
  - 多種星盤類型計算（本命盤、行運盤、推運盤等）
- **特點**：支持離線計算，高精度天文計算

#### BirthDataService
- **功能**：出生資料管理服務
- **職責**：
  - CRUD 操作（新增、讀取、更新、刪除）
  - 資料夾分類管理
  - 標籤系統
  - 匯入/匯出功能
  - 最近訪問記錄
- **特點**：基於 SharedPreferences 的本地存儲

#### AIApiService
- **功能**：AI 服務整合
- **職責**：
  - 多家 AI 提供商整合（OpenAI、Anthropic、Groq、Gemini）
  - API 金鑰管理
  - 使用量統計
  - 錯誤處理和重試機制
- **特點**：支持模型切換和自定義提示詞

### 3. 視圖模型層 (ViewModels)

**職責**：連接 UI 和業務邏輯，管理頁面狀態

**主要 ViewModel 類別**：

#### HomeViewModel
- **功能**：首頁狀態管理
- **職責**：
  - 今日星相數據載入
  - 選中人物管理
  - 行運盤資訊處理
  - 節氣資訊顯示

#### ChartViewModel
- **功能**：星盤頁面狀態管理
- **職責**：
  - 星盤數據計算和緩存
  - 載入狀態管理
  - 星盤設定同步
  - PDF 生成和分享

#### FilesViewModel
- **功能**：檔案管理頁面狀態管理
- **職責**：
  - 出生資料列表管理
  - 搜索和篩選功能
  - 批量操作處理
  - 資料夾管理

**設計模式**：
- 繼承 `ChangeNotifier` 實現響應式更新
- 使用 Provider 進行依賴注入
- 實現載入狀態和錯誤處理的統一管理

### 4. 用戶界面層 (UI)

**職責**：提供用戶交互界面和視覺呈現

**頁面組織結構**：
- **主要頁面**：首頁、檔案管理、理財占星、設定等
- **功能頁面**：星盤顯示、AI 解讀、資料編輯等
- **對話框和底部表單**：日期選擇、人物選擇等

**組件設計原則**：
- 高度模組化，支持重用
- 響應式設計，適配不同螢幕尺寸
- 一致的視覺風格和交互模式

### 5. 工具類別層 (Utils)

**職責**：提供通用工具函數和輔助功能

**主要工具類別**：
- `AstrologyCalculator`：占星計算輔助工具
- `JulianDateUtils`：儒略日轉換工具
- `LoggerUtils`：統一日誌管理
- `GeocodingService`：地理編碼服務
- `ChartPdfGenerator`：PDF 生成工具

## 數據流架構

### 數據流向
1. **用戶操作** → UI 組件
2. **UI 組件** → ViewModel（通過事件調用）
3. **ViewModel** → Service（業務邏輯處理）
4. **Service** → Model（數據操作）
5. **Model** → Service → ViewModel → UI（數據回流）

### 狀態管理
- 使用 Provider 模式進行全域狀態管理
- ChangeNotifier 實現響應式更新
- 本地狀態通過 StatefulWidget 管理
- 持久化狀態通過 SharedPreferences 存儲

## 外部依賴整合

### 核心依賴
- **Swiss Ephemeris (sweph)**：天文計算引擎
- **Provider**：狀態管理
- **SharedPreferences**：本地存儲
- **HTTP**：網路請求

### AI 服務整合
- OpenAI GPT 系列
- Anthropic Claude 系列
- Groq 高速推理
- Google Gemini

### 其他服務
- Firebase（可選雲端功能）
- 地理編碼服務
- PDF 生成服務

## 設計模式應用

### 1. MVVM 模式
- **Model**：數據模型和業務邏輯
- **View**：UI 組件和用戶交互
- **ViewModel**：狀態管理和數據綁定

### 2. 服務定位器模式
- 通過 Provider 實現服務的依賴注入
- 支持服務的生命週期管理

### 3. 策略模式
- AI 提供商的動態切換
- 星盤計算方法的選擇

### 4. 觀察者模式
- ChangeNotifier 實現狀態變化通知
- UI 自動響應數據更新

## 性能優化策略

### 1. 計算優化
- 星盤數據緩存機制
- 異步計算避免 UI 阻塞
- 懶加載減少初始化時間

### 2. 記憶體管理
- 及時釋放不需要的資源
- 圖片和大型數據的優化處理
- 避免記憶體洩漏

### 3. 網路優化
- API 請求緩存
- 錯誤重試機制
- 請求去重處理

## 可擴展性設計

### 1. 模組化架構
- 功能模組獨立開發和測試
- 支持功能的動態載入

### 2. 插件化支持
- AI 提供商的插件化整合
- 星盤類型的擴展支持

### 3. 配置化管理
- 用戶設定的靈活配置
- 功能開關的動態控制

## 特殊功能模組說明

### 1. 理財占星模組
**檔案位置**：`lib/ui/pages/main/finance_page.dart`、`lib/services/financial_analysis_service.dart`

**功能特點**：
- 行星會合分析（木土會合、火土會合）
- 二分二至經濟分析
- 投資性格分析
- AI 驅動的財經占星解讀
- 會合緩存機制優化性能

### 2. 星象日曆模組
**檔案位置**：`lib/ui/pages/astro_calendar_page.dart`、`lib/services/astro_calendar_service.dart`

**功能特點**：
- 月度星象事件顯示
- 日月蝕計算和顯示
- 行星逆行追蹤
- 重要相位提醒
- 可視化時間線展示

### 3. 日月蝕分析模組
**檔案位置**：`lib/ui/pages/eclipse_setup_page.dart`、相關服務類別

**功能特點**：
- 精確的日月蝕計算
- 蝕相可見性分析
- 個人和集體層面解讀
- 歷史蝕相數據查詢
- AI 輔助蝕相影響分析

### 4. 二分二至模組
**檔案位置**：`lib/ui/pages/equinox_solstice_page.dart`、`lib/services/equinox_solstice_service.dart`

**功能特點**：
- 精確的節氣時間計算
- 季節性星盤分析
- 經濟基調預測
- 年度和季度分析
- 地理位置自定義

## 數據持久化策略

### 本地存儲架構
```dart
// SharedPreferences 存儲結構
{
  "birthDataList": [...],           // 出生資料列表
  "chartSettings": {...},           // 星盤設定
  "folderData": [...],             // 資料夾資訊
  "recentCharts": [...],           // 最近查看的星盤
  "aiUsageStats": {...},           // AI 使用統計
  "conjunctionCache": {...}        // 會合分析緩存
}
```

### 緩存機制
- **星盤計算緩存**：避免重複計算相同參數的星盤
- **AI 解讀緩存**：相同提示詞的解讀結果緩存
- **會合分析緩存**：30天有效期，最多50個條目
- **圖片緩存**：星盤圖片的本地緩存

## 錯誤處理機制

### 分層錯誤處理
1. **UI 層**：用戶友好的錯誤提示
2. **ViewModel 層**：業務邏輯錯誤處理
3. **Service 層**：服務調用錯誤處理
4. **工具層**：底層計算錯誤處理

### 錯誤類型分類
```dart
// 自定義錯誤類型
class AstrologyException implements Exception {
  final String message;
  final ErrorType type;

  const AstrologyException(this.message, this.type);
}

enum ErrorType {
  calculation,    // 計算錯誤
  network,       // 網路錯誤
  storage,       // 存儲錯誤
  validation,    // 驗證錯誤
  permission     // 權限錯誤
}
```

## 國際化和本地化

### 多語言支持準備
- 字符串資源外部化
- 日期時間格式本地化
- 數字格式本地化
- 文化相關的占星術語適配

### 時區處理
- 自動時區檢測
- 手動時區選擇
- 歷史時區數據支持
- 夏令時自動調整

## 測試策略

### 測試分類
1. **單元測試**：核心計算邏輯測試
2. **Widget 測試**：UI 組件測試
3. **整合測試**：端到端功能測試
4. **性能測試**：計算性能和記憶體使用測試

### 測試覆蓋重點
- Swiss Ephemeris 計算準確性
- 數據模型序列化/反序列化
- ViewModel 狀態管理
- UI 組件響應性
- 錯誤處理機制

## 部署和發布

### 多平台構建
- **Android**：APK 和 AAB 格式
- **iOS**：App Store 發布
- **Web**：PWA 支持
- **Desktop**：Windows、macOS、Linux

### 版本管理
- 語義化版本控制
- 自動化構建流程
- 測試環境和生產環境分離
- 熱更新機制準備

## 監控和分析

### 性能監控
- 應用啟動時間
- 星盤計算耗時
- 記憶體使用情況
- 網路請求性能

### 用戶行為分析
- 功能使用頻率統計
- 用戶操作路徑分析
- 錯誤發生率監控
- 用戶滿意度追蹤

## 安全性考量

### 數據安全
- 本地數據加密存儲
- API 金鑰安全管理
- 用戶隱私保護
- 數據傳輸加密

### 應用安全
- 代碼混淆
- 反調試保護
- 完整性檢查
- 安全更新機制

這個架構設計確保了 Astreal 應用程式的可維護性、可擴展性和高性能，為用戶提供專業可靠的占星分析服務。通過模組化設計、完善的錯誤處理、多層緩存機制和全面的測試策略，該架構能夠支持應用程式的長期發展和持續優化。
