# Astreal 占星應用專案功能介紹與架構說明

## 📱 專案概述

**Astreal** 是一款專業的占星分析應用程式，採用 Flutter 跨平台開發，整合 Swiss Ephemeris 高精度天文計算引擎，提供完整的占星計算、分析和 AI 解讀功能。

### 🎯 核心定位
- **專業占星工具**：支援多種星盤類型和占星技法
- **AI 智能解讀**：整合多家 AI 服務提供深度分析
- **雙模式設計**：專業模式（Starmaster）和初心者模式（Starlight）
- **跨平台支援**：iOS、Android、Web、macOS 全平台覆蓋

## 🌟 主要功能模組

### 1. 占星計算引擎
#### 核心計算功能
- **Swiss Ephemeris 整合**：高精度天文計算
- **多種星盤類型**：
  - 個人星盤：本命盤
  - 預測星盤：行運盤、次限推運、三限推運、太陽弧推運
  - 返照盤：太陽返照、月亮返照
  - 關係星盤：比較盤、組合盤、時空中點盤、馬克思盤
  - 特殊星盤：法達盤、卜卦盤、事件盤、日月蝕盤、二分二至盤

#### 天體支援
- **傳統行星**：太陽、月亮、水星、金星、火星、木星、土星
- **現代行星**：天王星、海王星、冥王星
- **小行星**：穀神星、智神星、婚神星、灶神星
- **特殊點**：南北交點、莉莉絲、福點等阿拉伯點

#### 宮位系統
- **Placidus 制**：現代占星標準
- **整宮制**：古典占星常用
- **等宮制**：特殊研究用途
- **其他系統**：Koch、Regiomontanus 等

### 2. AI 解讀系統
#### 多家 AI 提供商整合
- **OpenAI**：GPT-4 系列模型
- **Anthropic**：Claude 系列模型
- **Groq**：高速推理服務
- **Google Gemini**：多模態 AI 服務

#### 智能解讀配置
每種星盤類型都有專門的解讀配置檔案，如日月蝕盤包含：
- **集體事件預測**：社會、政治、經濟影響分析
- **個人重大轉折**：人生重要轉折點預測
- **業力釋放與成長**：靈魂成長機會探索
- **蝕相觸發影響**：精確的個人影響分析
- **蝕相準備與應對**：實用的應對策略建議

#### 解讀特色
- **動態配置**：透過 JSON 配置檔案靈活調整解讀選項
- **專業引導**：每個解讀選項都有專業的 keyPoint 指導
- **互動問題**：提供相關問題幫助用戶深入探索
- **視覺設計**：統一的圖標和顏色系統

### 3. 用戶界面系統
#### 雙模式設計
**Starmaster（專業模式）**：
- 主題色：pastelSkyBlue (#E6F0FA)
- 完整功能存取
- 專業術語和詳細設定
- 適合占星師和進階用戶

**Starlight（初心者模式）**：
- 主題色：lightCornsilk (#FFF8DC)
- 簡化操作流程
- 通俗化語言描述
- 適合占星初學者

#### 核心頁面
- **主頁**：快速操作和常用功能
- **星盤頁面**：星盤顯示和分析
- **分析頁面**：各種占星分析工具
- **設定頁面**：個人化設定和系統配置

### 4. 資料管理系統
#### 出生資料管理
- **CRUD 操作**：新增、讀取、更新、刪除
- **分類系統**：家人、朋友、客戶等標籤分類
- **批量操作**：多選、批量刪除、批量匯出
- **搜尋功能**：姓名、地點、標籤搜尋

#### 備份與同步
- **CSV 匯入匯出**：標準格式資料交換
- **Firebase 整合**：雲端備份與跨設備同步
- **本地存儲**：SharedPreferences 本地快取

### 5. 付費與認證系統
#### 用戶認證
- **Google 登入**：跨平台統一認證
- **Apple 登入**：iOS 平台原生支援
- **Firebase Auth**：統一認證管理

#### 付費系統
- **解讀次數管理**：統一的可用次數系統
- **Firebase 支付記錄**：完整的購買記錄追蹤
- **Apple 規範合規**：符合 App Store 審核要求的引導方式

## 🏗️ 技術架構

### 架構模式
- **MVVM 架構**：Model-View-ViewModel 分層設計
- **Provider 狀態管理**：響應式狀態更新
- **依賴注入**：服務層解耦和測試友好

### 專案結構
```
lib/
├── main.dart                    # 應用程式入口
├── core/                        # 核心功能層
│   ├── constants/              # 常數定義
│   ├── utils/                  # 工具函數
│   └── services/               # 核心服務
├── data/                        # 資料層
│   ├── models/                 # 資料模型
│   ├── services/               # 資料服務
│   └── repositories/           # 資料倉庫
├── features/                    # 功能模組
│   ├── astrology/              # 占星計算模組
│   ├── ai_interpretation/      # AI 解讀模組
│   └── user_management/        # 用戶管理模組
├── presentation/                # 展示層
│   ├── pages/                  # 頁面
│   ├── widgets/                # 組件
│   └── viewmodels/             # 視圖模型
└── shared/                      # 共用組件
    ├── widgets/                # 共用 UI 組件
    ├── themes/                 # 主題配置
    └── extensions/             # 擴展方法
```

### 核心技術棧
- **Flutter 3.x**：跨平台 UI 框架
- **Dart**：程式語言
- **Swiss Ephemeris**：天文計算引擎
- **Firebase**：後端服務（認證、存儲、配置）
- **Provider**：狀態管理
- **SharedPreferences**：本地存儲

## 🎨 設計特色

### 視覺設計
- **Material Design 3**：現代化設計語言
- **自適應主題**：支援淺色和深色模式
- **統一色彩系統**：royalIndigo、solarAmber 等品牌色彩
- **響應式布局**：適配不同螢幕尺寸

### 用戶體驗
- **直觀操作**：簡化的操作流程
- **即時反饋**：載入狀態和錯誤處理
- **個人化設定**：豐富的自定義選項
- **無障礙支援**：符合無障礙設計標準

## 🔧 配置系統

### 解讀配置
採用 JSON 配置檔案動態管理解讀選項：
- **版本控制**：支援配置版本管理
- **動態更新**：透過 Remote Config 遠端更新
- **靈活配置**：圖標、顏色、問題等完全可配置
- **多語言支援**：支援國際化配置

### 占星模式
三種預設占星模式：
- **古典占星**：傳統七星、整宮制、古典相位
- **現代占星**：現代行星、Placidus 制、心理占星
- **特殊模式**：小行星、特殊點、等宮制

## 🚀 部署與發布

### 多平台支援
- **iOS**：App Store 發布
- **Android**：Google Play 發布
- **Web**：Firebase Hosting 部署
- **macOS**：桌面版本支援

### CI/CD 流程
- **自動化建置**：多平台自動建置
- **測試整合**：單元測試和整合測試
- **版本管理**：語義化版本控制

## 📈 未來發展

### 功能擴展
- **更多小行星支援**：擴展天體資料庫
- **預測提醒系統**：個人化行運提醒
- **社群功能**：用戶分享和交流
- **專業工具**：更多占星計算工具

### 技術優化
- **性能優化**：計算緩存和渲染優化
- **離線支援**：核心功能離線可用
- **多語言**：國際化支援
- **無障礙**：完整的無障礙功能

## 🔍 核心服務詳解

### AstrologyService - 占星計算核心
**職責**：整合 Swiss Ephemeris 引擎，提供所有占星計算功能

**核心方法**：
- `calculateChartData()`：計算完整星盤資料
- `calculatePlanetPositions()`：計算行星位置
- `calculateHouses()`：計算宮位
- `calculateAspects()`：計算相位
- `calculateArabicPoints()`：計算阿拉伯點
- `calculateTermRulerProgression()`：界主星配置法計算

### ChartInterpretationService - 解讀服務
**職責**：管理各種解讀模板和 AI 解讀配置

**特色功能**：
- 動態載入解讀配置檔案
- 支援自定義解讀提示詞
- 整合多家 AI 服務提供商
- 解讀結果快取和管理

### BirthDataService - 資料管理服務
**職責**：出生資料的完整生命週期管理

**功能特色**：
- 本地 SharedPreferences 存儲
- Firebase 雲端同步
- CSV 批量匯入匯出
- 分類標籤系統

### PaymentService - 付費服務
**職責**：處理付費邏輯和權限管理

**核心功能**：
- 解讀次數統一管理
- Firebase 支付記錄追蹤
- Apple/Google 內購整合
- 用戶權限驗證

## 📊 資料模型架構

### 核心資料模型

#### BirthData - 出生資料模型
```dart
class BirthData {
  final String id;
  final String name;
  final DateTime dateTime;
  final String birthPlace;
  final double latitude;
  final double longitude;
  final String? timezone;
  final String category;
  final List<String> tags;
}
```

#### ChartData - 星盤資料模型
```dart
class ChartData {
  final ChartType chartType;
  final BirthData primaryPerson;
  final BirthData? secondaryPerson;
  final List<PlanetPosition>? planets;
  final List<AspectInfo>? aspects;
  final HouseCuspData? houses;
  final ChartSettings settings;
}
```

#### PlanetPosition - 行星位置模型
```dart
class PlanetPosition {
  final int planetId;
  final double longitude;
  final double latitude;
  final double speed;
  final String sign;
  final int house;
  final PlanetDignity dignity;
}
```

## 🎯 設計模式應用

### MVVM 架構實現
- **Model**：資料模型和業務邏輯
- **View**：UI 組件和頁面
- **ViewModel**：狀態管理和 UI 邏輯

### Provider 狀態管理
- **ChartViewModel**：星盤狀態管理
- **SettingsViewModel**：設定狀態管理
- **AuthViewModel**：認證狀態管理
- **InterpretationViewModel**：解讀狀態管理

### 服務定位器模式
使用單例模式管理核心服務：
- 確保服務實例唯一性
- 提供全域存取點
- 支援依賴注入

## 🔐 安全性設計

### API 金鑰管理
- **Remote Config**：動態配置 API 金鑰
- **環境隔離**：開發/生產環境分離
- **加密存儲**：敏感資料加密保護

### 用戶資料保護
- **本地加密**：SharedPreferences 資料加密
- **傳輸安全**：HTTPS 通信協定
- **隱私合規**：符合 GDPR 和相關法規

## 🧪 測試策略

### 測試層級
- **單元測試**：核心業務邏輯測試
- **整合測試**：服務間整合測試
- **UI 測試**：用戶界面自動化測試
- **端到端測試**：完整流程測試

### 測試工具
- **Flutter Test**：官方測試框架
- **Mockito**：模擬物件框架
- **Golden Tests**：UI 回歸測試

## 📱 平台特性

### iOS 特性
- **Apple 登入**：原生認證整合
- **App Store 合規**：符合審核要求
- **iOS 設計規範**：Cupertino 風格適配

### Android 特性
- **Google 登入**：Android 原生整合
- **Material Design**：Android 設計語言
- **權限管理**：Android 權限系統

### Web 特性
- **響應式設計**：適配不同螢幕尺寸
- **PWA 支援**：漸進式 Web 應用
- **SEO 優化**：搜尋引擎優化

## 🚀 性能優化

### 計算優化
- **結果快取**：避免重複計算
- **懶載入**：按需載入資料
- **並行計算**：多執行緒計算支援

### UI 優化
- **虛擬化列表**：大量資料高效渲染
- **圖片快取**：減少網路請求
- **動畫優化**：流暢的用戶體驗

### 記憶體管理
- **物件池**：重用物件減少 GC
- **弱引用**：避免記憶體洩漏
- **資源釋放**：及時釋放不需要的資源

---

**Astreal** 致力於成為最專業、最易用的占星分析工具，結合傳統占星智慧與現代 AI 技術，為用戶提供深度的自我認知和人生指導。透過嚴謹的技術架構和用戶體驗設計，我們為占星愛好者和專業占星師提供了一個功能完整、性能優異的專業平台。
