# Remote Config 服務改進說明

## 問題描述

在應用程式中發現 Remote Config 服務存在以下問題：

1. **錯誤的日誌訊息**：第607行有重複且不完整的錯誤訊息
2. **配置間隔設定錯誤**：生產環境的 `minimumFetchInterval` 設定為1秒而非1小時
3. **初始化失敗處理不當**：當 Remote Config 初始化失敗時，後續所有配置獲取都會失敗
4. **缺乏重試機制**：網路問題導致的初始化失敗沒有重試機制
5. **日誌訊息不清楚**：使用默認配置時的日誌訊息容易造成困惑

## 解決方案

### 1. 修正錯誤的日誌訊息

**修正前：**
```dart
logger.w('Remote Config 未初始化， Remote Config 未初始化，使用默認版本配置<…>使用默認版本配置');
```

**修正後：**
```dart
logger.w('Remote Config 未初始化，使用默認版本配置');
```

### 2. 修正配置間隔設定

**修正前：**
```dart
minimumFetchInterval: kDebugMode
    ? const Duration(seconds: 1)  // 開發環境：10秒
    : const Duration(seconds: 1),    // 生產環境：1小時
```

**修正後：**
```dart
minimumFetchInterval: kDebugMode
    ? const Duration(seconds: 10)  // 開發環境：10秒
    : const Duration(hours: 1),    // 生產環境：1小時
```

### 3. 改善初始化邏輯

#### 增強的錯誤處理
- 分離 Firebase 實例創建和配置獲取的錯誤處理
- 即使初始化失敗也設置 `_isInitialized = true`，確保可以使用默認配置
- 添加詳細的錯誤堆疊記錄

#### 新增重試機制
```dart
/// 獲取並激活配置（帶重試機制）
static Future<bool> _fetchAndActivateWithRetry({int maxRetries = 3}) async {
  for (int attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      logger.i('嘗試獲取 Remote Config (第 $attempt/$maxRetries 次)...');
      
      bool success = await _fetchAndActivate();
      if (success) {
        logger.i('Remote Config 獲取成功 (第 $attempt 次嘗試)');
        return true;
      }
      
      if (attempt < maxRetries) {
        final delay = Duration(seconds: attempt * 2); // 遞增延遲
        logger.w('第 $attempt 次獲取失敗，${delay.inSeconds}秒後重試...');
        await Future.delayed(delay);
      }
    } catch (e) {
      logger.e('第 $attempt 次獲取 Remote Config 失敗: $e');
      if (attempt < maxRetries) {
        final delay = Duration(seconds: attempt * 2);
        logger.w('${delay.inSeconds}秒後重試...');
        await Future.delayed(delay);
      }
    }
  }
  
  logger.e('Remote Config 獲取失敗，已嘗試 $maxRetries 次');
  return false;
}
```

### 4. 改善日誌訊息

#### AI API Keys 配置
**修正前：**
```dart
logger.w('Remote Config 未初始化，使用默認配置');
```

**修正後：**
```dart
logger.i('Remote Config 未初始化，使用默認 AI API Keys 配置');
logger.d('成功載入默認 AI API Keys 配置');
```

#### AI 模型配置
**修正前：**
```dart
logger.w('Remote Config 未初始化，使用默認模型配置');
```

**修正後：**
```dart
logger.i('Remote Config 未初始化，使用默認 AI 模型配置');
final modelCount = (defaultConfig['models'] as List?)?.length ?? 0;
logger.d('成功載入默認 AI 模型配置，包含 $modelCount 個模型');
```

#### 商品配置
**修正前：**
```dart
logger.w('Remote Config 中 products_config 為空，使用默認配置');
```

**修正後：**
```dart
// 開發環境優先使用開發配置
if (kDebugMode) {
  final devConfigJson = _remoteConfig!.getString('products_config_dev');
  if (devConfigJson.isNotEmpty) {
    configJson = devConfigJson;
    logger.d('開發環境：使用 products_config_dev 配置');
  } else {
    logger.d('開發環境：products_config_dev 為空，使用 products_config');
  }
}
```

### 5. 新增健康狀態檢查

新增 `getHealthStatus()` 方法來提供完整的 Remote Config 健康狀態：

```dart
/// 檢查 Remote Config 健康狀態
static Map<String, dynamic> getHealthStatus() {
  final status = getStatus();
  final bool isHealthy = _isInitialized && _remoteConfig != null;
  
  Map<String, dynamic> healthInfo = {
    'isHealthy': isHealthy,
    'status': status,
    'configAvailability': {},
  };

  // 檢查各個配置的可用性
  // ... 檢查 AI API Keys、模型配置、商品配置等
  
  return healthInfo;
}
```

## 改進效果

1. **更穩定的初始化**：即使網路問題導致 Remote Config 獲取失敗，應用程式仍能正常運行
2. **更清楚的日誌**：用戶和開發者能更清楚地了解配置來源和狀態
3. **更好的錯誤恢復**：重試機制提高了配置獲取的成功率
4. **更完整的診斷**：健康狀態檢查提供了全面的配置可用性信息
5. **更合理的配置**：生產環境使用正確的獲取間隔，減少不必要的網路請求

## 使用建議

1. **監控健康狀態**：定期檢查 `getHealthStatus()` 來監控 Remote Config 的運行狀況
2. **日誌分析**：關注初始化過程中的日誌，及時發現配置問題
3. **測試環境驗證**：在測試環境中驗證 Remote Config 的各種失敗情況
4. **默認配置維護**：定期更新默認配置，確保在 Remote Config 不可用時應用程式仍能正常運行

## 🔧 Firebase 初始化整合

### 問題發現
在檢查過程中發現了一個重要問題：**Firebase 初始化邏輯重複且不完整**

#### 原始問題
1. **`main.dart`** 中有完整的 `_initializeFirebase()` 方法，包含：
   - Firebase App Check 初始化
   - Firebase 認證服務初始化
   - Remote Config 服務初始化
   - AI API Keys 設定

2. **`AppInitializationService`** 中有簡化的 `_initializeFirebase()` 方法，只做基本初始化

3. **`main.dart` 中的完整方法沒有被調用**，導致 Remote Config 等服務未正確初始化

#### 解決方案
**整合 Firebase 初始化邏輯到 `AppInitializationService`**：

1. **移除重複代碼**：
   - 刪除 `main.dart` 中的 `_initializeFirebase()`、`_loadAndSetAIApiKeys()`、`_initializeAppCheck()` 方法
   - 清理不需要的 import 語句

2. **增強 `AppInitializationService`**：
   - 完整的 Firebase 初始化流程
   - 新增 `_initializeFirebaseServices()` 方法
   - 新增 `_initializeAppCheck()` 方法
   - 新增 `_loadAndSetAIApiKeys()` 方法

3. **修正方法調用**：
   - 使用正確的 `AIApiService.setOpenAIApiKey()` 等方法
   - 修正 import 路徑

#### 修正後的初始化流程
```
main()
  └── AppInitializationService.initialize()
      ├── _initializeLogging()
      ├── _initializeFirebase()
      │   ├── Firebase.initializeApp()
      │   └── _initializeFirebaseServices()
      │       ├── _initializeAppCheck()
      │       ├── FirebaseAuthService.initialize()
      │       └── RemoteConfigService.initialize()
      │           └── _loadAndSetAIApiKeys()
      ├── _setupGlobalErrorHandling()
      └── _initializeOtherServices()
```

### 修正結果
- ✅ Firebase 初始化邏輯統一且完整
- ✅ Remote Config 服務正確初始化
- ✅ AI API Keys 正確設定
- ✅ 代碼結構更清晰，避免重複
- ✅ 所有編譯錯誤已修正

## 相關文件

- `lib/data/services/api/remote_config_service.dart` - Remote Config 服務主文件
- `lib/main.dart` - 應用程式入口點（已簡化）
- `lib/core/services/app_initialization_service.dart` - 應用程式初始化服務（已增強）
- `lib/core/config/firebase_options.dart` - Firebase 配置文件
