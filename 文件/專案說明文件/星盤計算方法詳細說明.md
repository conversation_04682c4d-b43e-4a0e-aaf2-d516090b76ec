# 星盤計算方法詳細說明

## 📋 概述

本文件詳細記錄 Astreal 應用程式中各種星盤類型的計算方法、數學原理和實作細節。所有計算都基於 Swiss Ephemeris 高精度天文計算引擎。

## 🧮 基礎計算原理

### 1. 儒略日轉換

所有天文計算的基礎是將日期時間轉換為儒略日（Julian Day）。

#### 計算步驟
```dart
// 1. 計算時區偏移
double offset = await calculateZonedDateTimeOffset(
  latitude, longitude, year, month, day, hour, minute
);

// 2. 調整為 UTC 時間
double utcHours = (hour + minute/60.0 + second/3600.0) - offset;

// 3. 轉換為儒略日
double julianDay = Sweph.swe_julday(
  year, month, day, utcHours, CalendarType.SE_GREG_CAL
);
```

#### 時區處理
- 使用地理座標查詢時區資料庫
- 自動處理夏令時轉換
- 支援歷史時區變更

### 2. 行星位置計算

使用 Swiss Ephemeris 計算行星在黃道座標系中的位置。

#### 計算參數
```dart
CoordinatesWithSpeed result = Sweph.swe_calc_ut(
  julianDay,           // 儒略日
  HeavenlyBody.planet, // 天體類型
  SwephFlag.flags      // 計算標誌
);
```

#### 計算標誌選項
- `SEFLG_SWIEPH`：使用 Swiss Ephemeris 資料
- `SEFLG_SPEED`：計算速度資訊
- `SEFLG_NONUT`：不包含章動（平均黃道）
- 預設使用真實黃道（包含章動）

### 3. 宮位系統計算

支援多種宮位系統，每種系統有不同的數學原理。

#### 支援的宮位系統
| 系統名稱 | 代碼 | 特點 |
|----------|------|------|
| Placidus | P | 時間等分制，現代最常用 |
| Koch | K | 出生地等分制 |
| Equal | A | 等宮制，每宮 30 度 |
| Whole Sign | W | 整宮制，以星座為宮位 |
| Campanus | C | 地平等分制 |
| Regiomontanus | R | 天球等分制 |
| Topocentric | T | 地心等分制 |

#### 計算方法
```dart
HouseCuspData houses = Sweph.swe_houses(
  julianDay,    // 儒略日
  latitude,     // 觀測地緯度
  longitude,    // 觀測地經度
  houseSystem   // 宮位系統代碼
);
```

## 🌟 個人星盤類型

### 1. 本命盤 (Natal Chart)

最基礎的星盤類型，顯示出生時刻的天體位置。

#### 計算要素
- **時間**：出生日期時間（精確到分鐘）
- **地點**：出生地經緯度
- **宮位系統**：用戶選擇的宮位系統

#### 計算流程
```dart
async ChartData _calculateNatalChart(ChartCalculationParams params) {
  // 1. 轉換為儒略日
  final julianDay = await JulianDateUtils.dateTimeToJulianDay(
    params.primaryPerson.dateTime,
    params.primaryPerson.latitude,
    params.primaryPerson.longitude,
  );
  
  // 2. 計算宮位
  final houses = await calculateHouses(
    params.primaryPerson.dateTime,
    params.primaryPerson.latitude,
    params.primaryPerson.longitude,
    houseSystem: params.houseSystem,
  );
  
  // 3. 計算行星位置
  final planets = await calculatePlanetPositions(
    params.primaryPerson.dateTime,
    housesData: houses,
    latitude: params.primaryPerson.latitude,
    longitude: params.primaryPerson.longitude,
  );
  
  // 4. 計算相位
  final aspects = calculateAspects(planets);
  
  return ChartData(
    chartType: ChartType.natal,
    planets: planets,
    houses: houses,
    aspects: aspects,
  );
}
```

## 🔮 預測類星盤

### 1. 行運盤 (Transit Chart)

將當前天空中的行星位置與本命盤對比。

#### 計算原理
- 計算指定日期的行星位置
- 與本命盤行星形成相位關係
- 分析當前時期的機會和挑戰

#### 計算方法
```dart
async ChartData _calculateTransitChart(ChartCalculationParams params) {
  // 1. 計算本命盤（內圈）
  final natalChart = await _calculateNatalChart(params);
  
  // 2. 計算行運日期的行星位置（外圈）
  final transitDate = params.specificDate ?? DateTime.now();
  final transitPlanets = await calculatePlanetPositions(
    transitDate,
    housesData: natalChart.houses,
    latitude: params.primaryPerson.latitude,
    longitude: params.primaryPerson.longitude,
  );
  
  // 3. 計算行運相位
  final transitAspects = calculateTransitAspects(
    natalChart.planets,
    transitPlanets,
  );
  
  return natalChart.copyWith(
    transitPlanets: transitPlanets,
    transitAspects: transitAspects,
  );
}
```

### 2. 次限推運盤 (Secondary Progression)

使用「一天等於一年」的原理計算內在心理發展。

#### 計算公式
```
推運日期 = 出生日期 + 目標年齡（天數）
```

#### 實作方法
```dart
async ChartData _calculateSecondaryProgressionChart(ChartCalculationParams params) {
  final birthDate = params.primaryPerson.dateTime;
  final targetDate = params.specificDate!;
  
  // 計算年齡（以天為單位）
  final ageInDays = targetDate.difference(birthDate).inDays;
  
  // 推運日期 = 出生日期 + 年齡天數
  final progressedDate = birthDate.add(Duration(days: ageInDays));
  
  // 計算推運後的行星位置
  final progressedPlanets = await calculatePlanetPositions(
    progressedDate,
    housesData: natalChart.houses,
    latitude: params.primaryPerson.latitude,
    longitude: params.primaryPerson.longitude,
  );
  
  return ChartData(
    chartType: ChartType.secondaryProgression,
    planets: natalChart.planets,
    progressedPlanets: progressedPlanets,
    houses: natalChart.houses,
  );
}
```

### 3. 三限推運盤 (Tertiary Progression)

使用「一天等於一月」的原理分析短期變化。

#### 計算公式
```
推運日期 = 出生日期 + 目標月數（天數）
```

#### 實作方法
```dart
async ChartData _calculateTertiaryProgressionChart(ChartCalculationParams params) {
  final birthDate = params.primaryPerson.dateTime;
  final targetDate = params.specificDate!;
  
  // 計算月數差異
  final monthsDifference = (targetDate.year - birthDate.year) * 12 + 
                          (targetDate.month - birthDate.month);
  
  // 推運日期 = 出生日期 + 月數（以天計算）
  final progressedDate = birthDate.add(Duration(days: monthsDifference));
  
  // 計算推運後的行星位置
  final progressedPlanets = await calculatePlanetPositions(
    progressedDate,
    housesData: natalChart.houses,
    latitude: params.primaryPerson.latitude,
    longitude: params.primaryPerson.longitude,
  );
  
  return ChartData(
    chartType: ChartType.tertiaryProgression,
    planets: natalChart.planets,
    progressedPlanets: progressedPlanets,
    houses: natalChart.houses,
  );
}
```

### 4. 太陽弧推運盤 (Solar Arc Direction)

所有行星按照太陽的推進速度同步移動。

#### 計算原理
1. 計算太陽從出生到目標日期的推進度數
2. 將所有行星按此度數同步推進
3. 用於預測重大人生轉折點

#### 實作方法
```dart
async ChartData _calculateSolarArcDirectionChart(ChartCalculationParams params) {
  final birthDate = params.primaryPerson.dateTime;
  final targetDate = params.specificDate!;
  
  // 計算太陽推進度數（約每年 1 度）
  final yearsDifference = targetDate.difference(birthDate).inDays / 365.25;
  final solarArcDegrees = yearsDifference; // 簡化計算
  
  // 將所有行星按太陽弧度數推進
  final arcDirectedPlanets = natalChart.planets.map((planet) {
    final newLongitude = (planet.longitude + solarArcDegrees) % 360;
    return planet.copyWith(
      longitude: newLongitude,
      sign: _getZodiacSign(newLongitude),
    );
  }).toList();
  
  return ChartData(
    chartType: ChartType.solarArcDirection,
    planets: natalChart.planets,
    arcDirectedPlanets: arcDirectedPlanets,
    houses: natalChart.houses,
  );
}
```

## 🌅 返照盤類型

### 1. 太陽返照盤 (Solar Return)

太陽回到出生時相同位置的時刻星盤。

#### 計算步驟
1. 找到太陽回歸出生位置的精確時間
2. 計算該時刻的星盤
3. 用於分析年度主題

#### 實作方法
```dart
async ChartData _calculateSolarReturnChart(ChartCalculationParams params) {
  final birthData = params.primaryPerson;
  final targetYear = params.specificDate?.year ?? DateTime.now().year;
  
  // 計算太陽返照時間
  final solarReturnTime = await _findSolarReturnTime(
    birthData,
    targetYear,
  );
  
  // 計算返照時刻的星盤
  final returnChart = await _calculateChartForDateTime(
    solarReturnTime,
    birthData.latitude,
    birthData.longitude,
  );
  
  return returnChart.copyWith(
    chartType: ChartType.solarReturn,
    returnDate: solarReturnTime,
  );
}

// 尋找太陽返照時間
Future<DateTime> _findSolarReturnTime(BirthData birthData, int year) async {
  final birthSunLongitude = await _getSunLongitude(birthData.dateTime);
  
  // 從目標年份開始搜尋
  DateTime searchDate = DateTime(year, birthData.dateTime.month, birthData.dateTime.day);
  
  for (int days = -30; days <= 30; days++) {
    final testDate = searchDate.add(Duration(days: days));
    final sunLongitude = await _getSunLongitude(testDate);
    
    // 找到太陽經度最接近出生時的時間
    if ((sunLongitude - birthSunLongitude).abs() < 0.1) {
      return testDate;
    }
  }
  
  return searchDate; // 備用返回值
}
```

### 2. 月亮返照盤 (Lunar Return)

月亮回到出生時相同位置的時刻星盤。

#### 計算特點
- 每月發生一次
- 用於分析月度情緒和短期發展
- 計算方法類似太陽返照盤

## 💕 關係星盤類型

### 1. 比較盤 (Synastry)

將兩個人的本命盤重疊比較。

#### 計算方法
```dart
async ChartData _calculateSynastryChart(ChartCalculationParams params) {
  // 計算兩人的本命盤
  final chartA = await _calculateNatalChart(params.copyWith(
    primaryPerson: params.primaryPerson,
  ));
  
  final chartB = await _calculateNatalChart(params.copyWith(
    primaryPerson: params.secondaryPerson!,
  ));
  
  // 計算兩人行星間的相位
  final synastryAspects = calculateSynastryAspects(
    chartA.planets,
    chartB.planets,
  );
  
  return ChartData(
    chartType: ChartType.synastry,
    planets: chartA.planets,
    secondaryPlanets: chartB.planets,
    houses: chartA.houses,
    secondaryHouses: chartB.houses,
    synastryAspects: synastryAspects,
  );
}
```

### 2. 組合盤 (Composite Chart)

將兩人對應行星的中點位置計算出新星盤。

#### 中點計算公式
```dart
double _midpointAngle(double angle1, double angle2) {
  double diff = (angle2 - angle1 + 360) % 360;
  if (diff > 180) {
    diff -= 360;
  }
  return (angle1 + diff / 2 + 360) % 360;
}
```

#### 實作方法
```dart
async ChartData _calculateCompositeChart(ChartCalculationParams params) {
  // 計算兩人的本命盤
  final chartA = await _calculateNatalChart(params.copyWith(
    primaryPerson: params.primaryPerson,
  ));
  
  final chartB = await _calculateNatalChart(params.copyWith(
    primaryPerson: params.secondaryPerson!,
  ));
  
  // 計算中點時間和地點
  final midDateTime = _calculateMidDateTime(
    params.primaryPerson.dateTime,
    params.secondaryPerson!.dateTime,
  );
  
  final midLatitude = (params.primaryPerson.latitude + 
                      params.secondaryPerson!.latitude) / 2;
  final midLongitude = (params.primaryPerson.longitude + 
                       params.secondaryPerson!.longitude) / 2;
  
  // 計算組合盤的行星位置（使用中點邏輯）
  final compositePlanets = <PlanetPosition>[];
  for (int i = 0; i < chartA.planets.length; i++) {
    final planetA = chartA.planets[i];
    final planetB = chartB.planets.firstWhere((p) => p.id == planetA.id);
    
    final midLongitude = _midpointAngle(planetA.longitude, planetB.longitude);
    final midLatitude = (planetA.latitude + planetB.latitude) / 2;
    
    compositePlanets.add(planetA.copyWith(
      longitude: midLongitude,
      latitude: midLatitude,
      sign: _getZodiacSign(midLongitude),
    ));
  }
  
  return ChartData(
    chartType: ChartType.composite,
    planets: compositePlanets,
    houses: await calculateHouses(midDateTime, midLatitude, midLongitude),
  );
}
```

### 3. 時空中點盤 (Davison Chart)

將兩人的出生時間和地點取平均值建立星盤。

#### 計算方法
```dart
async ChartData _calculateDavisonChart(ChartCalculationParams params) {
  // 計算中點時間
  final dateTime1 = params.primaryPerson.dateTime;
  final dateTime2 = params.secondaryPerson!.dateTime;
  
  final midTimestamp = (dateTime1.millisecondsSinceEpoch + 
                       dateTime2.millisecondsSinceEpoch) / 2;
  final midDateTime = DateTime.fromMillisecondsSinceEpoch(midTimestamp.round());
  
  // 計算中點地點
  final midLatitude = (params.primaryPerson.latitude + 
                      params.secondaryPerson!.latitude) / 2;
  final midLongitude = (params.primaryPerson.longitude + 
                       params.secondaryPerson!.longitude) / 2;
  
  // 在中點時空建立星盤
  return await _calculateChartForDateTime(
    midDateTime,
    midLatitude,
    midLongitude,
  ).then((chart) => chart.copyWith(chartType: ChartType.davison));
}
```

## 🎯 特殊星盤類型

### 1. 卜卦盤 (Horary Chart)

在提出問題的時刻建立星盤。

#### 計算特點
- 使用問題提出的精確時間
- 通常使用整宮制
- 重點分析特定宮位和行星

### 2. 事件盤 (Event Chart)

為重要事件的發生時刻建立星盤。

#### 應用場景
- 結婚典禮
- 公司成立
- 重要簽約
- 搬遷入宅

### 3. 法達盤 (Firdaria Chart)

古典占星學的時序分析技術。

#### 計算原理
- 將人生劃分為不同階段
- 每個階段由特定行星主導
- 基於出生時的宗主星確定順序

## 📐 相位計算

### 相位類型和角度

| 相位名稱 | 角度 | 容許度 | 性質 |
|----------|------|--------|------|
| 合相 | 0° | ±8° | 中性 |
| 六分相 | 60° | ±4° | 和諧 |
| 四分相 | 90° | ±6° | 緊張 |
| 三分相 | 120° | ±6° | 和諧 |
| 對沖 | 180° | ±8° | 緊張 |

### 計算方法
```dart
AspectInfo? calculateAspectBetweenPlanets(
  PlanetPosition planet1,
  PlanetPosition planet2,
) {
  // 計算角度差
  final angleDifference = calculateAngleDifference(
    planet1.longitude,
    planet2.longitude,
  );
  
  // 檢查是否形成相位
  for (final aspectDef in aspectDefinitions) {
    final orb = (angleDifference - aspectDef.angle).abs();
    if (orb <= aspectDef.maxOrb) {
      return AspectInfo(
        planet1: planet1,
        planet2: planet2,
        aspect: aspectDef.name,
        angle: aspectDef.angle,
        orb: orb,
        isApplying: _calculateAspectDirection(...),
      );
    }
  }
  
  return null;
}
```

## 🔧 計算優化

### 1. 快取機制
- 快取已計算的行星位置
- 避免重複的儒略日轉換
- 快取宮位計算結果

### 2. 精度控制
- 行星位置精確到角秒
- 時間精確到分鐘
- 地理位置精確到小數點後 4 位

### 3. 錯誤處理
- Swiss Ephemeris 初始化檢查
- 無效日期範圍處理
- 網路時區查詢失敗備案

## 🌙 月相和日月食計算

### 1. 日月食盤 (Eclipse Chart)

計算日月食發生時刻的星盤配置。

#### 日食計算原理
```dart
Future<List<EclipseEvent>> findSolarEclipses(int year) async {
  final eclipses = <EclipseEvent>[];
  final startJD = Sweph.swe_julday(year, 1, 1, 0, CalendarType.SE_GREG_CAL);
  final endJD = Sweph.swe_julday(year + 1, 1, 1, 0, CalendarType.SE_GREG_CAL);

  double searchJD = startJD;
  while (searchJD < endJD) {
    try {
      // 搜尋下一次日食
      final eclipseJD = Sweph.swe_sol_eclipse_when_glob(
        searchJD,
        SwephFlag.SEFLG_SWIEPH,
        EclipseType.SE_ECL_TOTAL | EclipseType.SE_ECL_PARTIAL,
      );

      if (eclipseJD > endJD) break;

      // 計算日食詳細資訊
      final eclipseInfo = await _calculateEclipseDetails(eclipseJD, true);
      eclipses.add(eclipseInfo);

      searchJD = eclipseJD + 30; // 搜尋下一次（間隔30天）
    } catch (e) {
      searchJD += 30;
    }
  }

  return eclipses;
}
```

#### 月食計算原理
```dart
Future<List<EclipseEvent>> findLunarEclipses(int year) async {
  final eclipses = <EclipseEvent>[];
  final startJD = Sweph.swe_julday(year, 1, 1, 0, CalendarType.SE_GREG_CAL);
  final endJD = Sweph.swe_julday(year + 1, 1, 1, 0, CalendarType.SE_GREG_CAL);

  double searchJD = startJD;
  while (searchJD < endJD) {
    try {
      // 搜尋下一次月食
      final eclipseJD = Sweph.swe_lun_eclipse_when(
        searchJD,
        SwephFlag.SEFLG_SWIEPH,
        EclipseType.SE_ECL_TOTAL | EclipseType.SE_ECL_PARTIAL,
      );

      if (eclipseJD > endJD) break;

      // 計算月食詳細資訊
      final eclipseInfo = await _calculateEclipseDetails(eclipseJD, false);
      eclipses.add(eclipseInfo);

      searchJD = eclipseJD + 15; // 搜尋下一次（間隔15天）
    } catch (e) {
      searchJD += 15;
    }
  }

  return eclipses;
}
```

### 2. 二分二至盤 (Equinox & Solstice Chart)

計算春分、夏至、秋分、冬至時刻的星盤。

#### 節氣時間計算
```dart
Future<DateTime> calculateEquinoxSolstice(int year, SeasonType season) async {
  double targetSunLongitude;

  switch (season) {
    case SeasonType.springEquinox:
      targetSunLongitude = 0.0;    // 春分：太陽進入牡羊座
      break;
    case SeasonType.summerSolstice:
      targetSunLongitude = 90.0;   // 夏至：太陽進入巨蟹座
      break;
    case SeasonType.autumnEquinox:
      targetSunLongitude = 180.0;  // 秋分：太陽進入天秤座
      break;
    case SeasonType.winterSolstice:
      targetSunLongitude = 270.0;  // 冬至：太陽進入摩羯座
      break;
  }

  // 從預估日期開始搜尋
  DateTime searchDate = _getApproximateDate(year, season);

  for (int hours = -48; hours <= 48; hours++) {
    final testDate = searchDate.add(Duration(hours: hours));
    final julianDay = await JulianDateUtils.dateTimeToJulianDay(
      testDate, 0, 0 // 使用 UTC
    );

    final sunPosition = Sweph.swe_calc_ut(
      julianDay,
      HeavenlyBody.SE_SUN,
      SwephFlag.SEFLG_SWIEPH,
    );

    // 找到太陽經度最接近目標的時間
    if ((sunPosition.longitude - targetSunLongitude).abs() < 0.01) {
      return testDate;
    }
  }

  return searchDate;
}
```

## 🏛️ 古典占星技法

### 1. 法達盤 (Firdaria Chart)

古典占星學的時序分析技術。

#### 法達週期計算
```dart
class FirdariaCalculator {
  // 法達行星順序（白天出生）
  static const List<int> daySequence = [
    AstrologyConstants.SUN,     // 太陽：10年
    AstrologyConstants.VENUS,   // 金星：8年
    AstrologyConstants.MERCURY, // 水星：13年
    AstrologyConstants.MOON,    // 月亮：9年
    AstrologyConstants.SATURN,  // 土星：11年
    AstrologyConstants.JUPITER, // 木星：12年
    AstrologyConstants.MARS,    // 火星：7年
  ];

  // 法達行星順序（夜晚出生）
  static const List<int> nightSequence = [
    AstrologyConstants.MOON,    // 月亮：9年
    AstrologyConstants.SATURN,  // 土星：11年
    AstrologyConstants.JUPITER, // 木星：12年
    AstrologyConstants.MARS,    // 火星：7年
    AstrologyConstants.SUN,     // 太陽：10年
    AstrologyConstants.VENUS,   // 金星：8年
    AstrologyConstants.MERCURY, // 水星：13年
  ];

  // 法達週期長度（年）
  static const Map<int, int> firdariaYears = {
    AstrologyConstants.SUN: 10,
    AstrologyConstants.MOON: 9,
    AstrologyConstants.MERCURY: 13,
    AstrologyConstants.VENUS: 8,
    AstrologyConstants.MARS: 7,
    AstrologyConstants.JUPITER: 12,
    AstrologyConstants.SATURN: 11,
  };

  static FirdariaResult calculateFirdaria(
    BirthData birthData,
    DateTime targetDate,
  ) {
    // 判斷日夜出生
    final isDaytime = _isDaytimeBirth(birthData);
    final sequence = isDaytime ? daySequence : nightSequence;

    // 計算年齡
    final ageInYears = targetDate.difference(birthData.dateTime).inDays / 365.25;

    // 找到當前法達週期
    double totalYears = 0;
    for (int i = 0; i < sequence.length; i++) {
      final planetId = sequence[i];
      final periodYears = firdariaYears[planetId]!;

      if (ageInYears < totalYears + periodYears) {
        // 找到當前週期
        final periodStart = totalYears;
        final periodEnd = totalYears + periodYears;
        final progressInPeriod = ageInYears - periodStart;

        return FirdariaResult(
          currentPlanet: planetId,
          periodStart: periodStart,
          periodEnd: periodEnd,
          progressInPeriod: progressInPeriod,
          totalProgress: progressInPeriod / periodYears,
        );
      }

      totalYears += periodYears;
    }

    // 如果超過 70 年，重新開始循環
    final cycleAge = ageInYears % 70;
    return calculateFirdaria(birthData,
      birthData.dateTime.add(Duration(days: (cycleAge * 365.25).round())));
  }
}
```

### 2. 小限盤 (Profection Chart)

每年上升點推進一個宮位的古典技法。

#### 小限計算
```dart
class ProfectionCalculator {
  static ProfectionResult calculateProfection(
    BirthData birthData,
    DateTime targetDate,
  ) {
    // 計算年齡
    final ageInYears = targetDate.difference(birthData.dateTime).inDays ~/ 365;

    // 小限宮位 = (年齡 % 12) + 1
    final profectionHouse = (ageInYears % 12) + 1;

    // 計算小限主星
    final profectionLord = _calculateHouseLord(profectionHouse, birthData);

    // 計算小限上升點
    final profectionAscendant = _calculateProfectionAscendant(
      birthData.ascendant,
      ageInYears,
    );

    return ProfectionResult(
      age: ageInYears,
      profectionHouse: profectionHouse,
      profectionLord: profectionLord,
      profectionAscendant: profectionAscendant,
    );
  }

  static double _calculateProfectionAscendant(
    double natalAscendant,
    int age,
  ) {
    // 每年推進 30 度（一個宮位）
    final progression = (age * 30.0) % 360.0;
    return (natalAscendant + progression) % 360.0;
  }
}
```

## 🌍 世俗占星

### 1. 天象盤 (Mundane Chart)

用於分析國家、社會、政治、經濟等集體層面的趨勢。

#### 計算特點
- 不需要個人出生資料
- 使用特定地點和時間
- 重點分析集體影響

#### 實作方法
```dart
async ChartData _calculateMundaneChart(ChartCalculationParams params) {
  final location = params.location!; // 分析地點
  final analysisDate = params.specificDate ?? DateTime.now();

  // 計算指定時間地點的星盤
  final mundaneChart = await _calculateChartForDateTime(
    analysisDate,
    location.latitude,
    location.longitude,
  );

  return mundaneChart.copyWith(
    chartType: ChartType.mundane,
    location: location,
  );
}
```

### 2. 重要天象盤

#### 土木會合盤 (Jupiter-Saturn Conjunction)
```dart
Future<DateTime> findJupiterSaturnConjunction(int startYear) async {
  final startJD = Sweph.swe_julday(startYear, 1, 1, 0, CalendarType.SE_GREG_CAL);

  double searchJD = startJD;
  double minDistance = double.infinity;
  double conjunctionJD = startJD;

  // 搜尋 3 年內的最小角距離
  for (int days = 0; days < 1095; days++) {
    final testJD = searchJD + days;

    final jupiterPos = Sweph.swe_calc_ut(
      testJD, HeavenlyBody.SE_JUPITER, SwephFlag.SEFLG_SWIEPH);
    final saturnPos = Sweph.swe_calc_ut(
      testJD, HeavenlyBody.SE_SATURN, SwephFlag.SEFLG_SWIEPH);

    final distance = _calculateAngularDistance(
      jupiterPos.longitude, saturnPos.longitude);

    if (distance < minDistance) {
      minDistance = distance;
      conjunctionJD = testJD;
    }
  }

  return JulianDateUtils.julianDayToDateTime(conjunctionJD, 0, 0);
}
```

## 🔮 阿拉伯點計算

### 基本阿拉伯點

#### 幸運點 (Lot of Fortune)
```dart
double calculateFortunePoint(
  double ascendant,
  double sunLongitude,
  double moonLongitude,
  bool isDaytime,
) {
  if (isDaytime) {
    // 白天公式：上升點 + 月亮 - 太陽
    return _normalizeAngle(ascendant + moonLongitude - sunLongitude);
  } else {
    // 夜晚公式：上升點 + 太陽 - 月亮
    return _normalizeAngle(ascendant + sunLongitude - moonLongitude);
  }
}
```

#### 精神點 (Lot of Spirit)
```dart
double calculateSpiritPoint(
  double ascendant,
  double sunLongitude,
  double moonLongitude,
  bool isDaytime,
) {
  if (isDaytime) {
    // 白天公式：上升點 + 太陽 - 月亮
    return _normalizeAngle(ascendant + sunLongitude - moonLongitude);
  } else {
    // 夜晚公式：上升點 + 月亮 - 太陽
    return _normalizeAngle(ascendant + moonLongitude - sunLongitude);
  }
}
```

### 複雜阿拉伯點

#### 愛情點 (Lot of Love)
```dart
double calculateLovePoint(
  double ascendant,
  double sunLongitude,
  double venusLongitude,
  bool isDaytime,
) {
  if (isDaytime) {
    return _normalizeAngle(ascendant + venusLongitude - sunLongitude);
  } else {
    return _normalizeAngle(ascendant + sunLongitude - venusLongitude);
  }
}
```

#### 職業點 (Lot of Profession)
```dart
double calculateProfessionPoint(
  double ascendant,
  double moonLongitude,
  double saturnLongitude,
) {
  return _normalizeAngle(ascendant + moonLongitude - saturnLongitude);
}
```

## 📊 計算精度和優化

### 1. 精度等級

| 計算類型 | 精度要求 | 實際精度 |
|----------|----------|----------|
| 行星位置 | 角秒級 | ±0.001° |
| 宮位界線 | 角分級 | ±0.01° |
| 相位容許度 | 角分級 | ±0.01° |
| 時間轉換 | 分鐘級 | ±1分鐘 |

### 2. 性能優化策略

#### 快取機制
```dart
class CalculationCache {
  static final Map<String, dynamic> _cache = {};

  static T? get<T>(String key) {
    return _cache[key] as T?;
  }

  static void set<T>(String key, T value) {
    _cache[key] = value;
  }

  static void clear() {
    _cache.clear();
  }
}
```

#### 批量計算
```dart
Future<List<PlanetPosition>> calculateMultiplePlanets(
  List<HeavenlyBody> planets,
  double julianDay,
) async {
  final results = <PlanetPosition>[];

  // 並行計算多個行星位置
  final futures = planets.map((planet) async {
    return await _calculateSinglePlanet(planet, julianDay);
  });

  final positions = await Future.wait(futures);
  results.addAll(positions);

  return results;
}
```

### 3. 錯誤處理和驗證

#### 日期範圍驗證
```dart
bool isValidDateRange(DateTime dateTime) {
  // Swiss Ephemeris 支援範圍：BCE 5400 - CE 5400
  final minDate = DateTime(-5400, 1, 1);
  final maxDate = DateTime(5400, 12, 31);

  return dateTime.isAfter(minDate) && dateTime.isBefore(maxDate);
}
```

#### 地理座標驗證
```dart
bool isValidCoordinates(double latitude, double longitude) {
  return latitude >= -90 && latitude <= 90 &&
         longitude >= -180 && longitude <= 180;
}
```

## 📚 參考資料和標準

### 1. 天文計算標準
- **Swiss Ephemeris**：JPL DE406 星曆表
- **時間系統**：UTC 和 TT（地球時）
- **座標系統**：J2000.0 黃道座標系

### 2. 占星學傳統
- **古典占星**：托勒密體系
- **現代占星**：心理占星學派
- **印度占星**：恆星黃道系統

### 3. 計算公式來源
- **宮位計算**：各宮位系統的標準公式
- **推運技法**：傳統占星學文獻
- **阿拉伯點**：中世紀阿拉伯占星學

這份完整的技術文件為 Astreal 應用程式的星盤計算提供了詳盡的說明，涵蓋了從基礎原理到高級技法的所有計算方法。
