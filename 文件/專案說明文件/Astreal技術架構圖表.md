# Astreal 技術架構圖表

## 🏗️ 整體架構圖

```mermaid
graph TB
    subgraph "展示層 (Presentation Layer)"
        UI[用戶界面]
        VM[ViewModel]
        W[Widgets]
    end
    
    subgraph "業務邏輯層 (Business Logic Layer)"
        AS[AstrologyService]
        CIS[ChartInterpretationService]
        BDS[BirthDataService]
        PS[PaymentService]
        AIS[AIApiService]
    end
    
    subgraph "資料層 (Data Layer)"
        M[Models]
        R[Repositories]
        SP[SharedPreferences]
        FB[Firebase]
    end
    
    subgraph "外部服務 (External Services)"
        SE[Swiss Ephemeris]
        AI[AI Services]
        FBS[Firebase Services]
    end
    
    UI --> VM
    VM --> AS
    VM --> CIS
    VM --> BDS
    VM --> PS
    
    AS --> SE
    CIS --> AIS
    AIS --> AI
    BDS --> SP
    PS --> FB
    
    AS --> M
    CIS --> M
    BDS --> M
    PS --> M
    
    FB --> FBS
```

## 📱 應用程式流程圖

```mermaid
flowchart TD
    Start([應用啟動]) --> Init[初始化服務]
    Init --> Auth{用戶認證}
    
    Auth -->|已登入| Home[主頁面]
    Auth -->|未登入| Login[登入頁面]
    
    Login --> Home
    
    Home --> BD[出生資料管理]
    Home --> Chart[星盤分析]
    Home --> AI[AI 解讀]
    Home --> Settings[設定]
    
    BD --> BDList[資料列表]
    BD --> BDForm[資料表單]
    BD --> BDImport[CSV 匯入]
    
    Chart --> ChartCalc[星盤計算]
    Chart --> ChartView[星盤顯示]
    Chart --> ChartSettings[星盤設定]
    
    AI --> AISelect[解讀選擇]
    AI --> AIProcess[AI 處理]
    AI --> AIResult[解讀結果]
    
    Settings --> Theme[主題設定]
    Settings --> Account[帳戶管理]
    Settings --> APIKeys[API 金鑰]
```

## 🔄 星盤計算流程

```mermaid
sequenceDiagram
    participant U as 用戶
    participant VM as ChartViewModel
    participant AS as AstrologyService
    participant SE as Swiss Ephemeris
    participant Cache as 快取系統
    
    U->>VM: 選擇星盤類型
    VM->>AS: calculateChartData()
    
    AS->>Cache: 檢查快取
    alt 快取存在
        Cache-->>AS: 返回快取資料
    else 快取不存在
        AS->>SE: 初始化星曆表
        AS->>SE: 計算行星位置
        AS->>SE: 計算宮位
        AS->>AS: 計算相位
        AS->>AS: 計算阿拉伯點
        AS->>Cache: 儲存到快取
    end
    
    AS-->>VM: 返回星盤資料
    VM-->>U: 顯示星盤
```

## 🤖 AI 解讀流程

```mermaid
sequenceDiagram
    participant U as 用戶
    participant VM as InterpretationViewModel
    participant CIS as ChartInterpretationService
    participant AIS as AIApiService
    participant AI as AI Provider
    participant PS as PaymentService
    
    U->>VM: 選擇解讀類型
    VM->>PS: 檢查權限
    
    alt 有權限
        PS-->>VM: 權限確認
        VM->>CIS: getInterpretation()
        CIS->>AIS: 呼叫 AI API
        AIS->>AI: 發送請求
        AI-->>AIS: 返回解讀結果
        AIS-->>CIS: 處理結果
        CIS-->>VM: 返回解讀
        VM->>PS: 扣除次數
        VM-->>U: 顯示解讀結果
    else 無權限
        PS-->>VM: 權限不足
        VM-->>U: 顯示購買提示
    end
```

## 💾 資料存儲架構

```mermaid
graph LR
    subgraph "本地存儲"
        SP[SharedPreferences]
        Cache[記憶體快取]
        Files[檔案系統]
    end
    
    subgraph "雲端存儲"
        Auth[Firebase Auth]
        Firestore[Cloud Firestore]
        Storage[Firebase Storage]
        RC[Remote Config]
    end
    
    subgraph "資料類型"
        BD[出生資料]
        Settings[設定資料]
        Payment[付費記錄]
        Config[配置資料]
    end
    
    BD --> SP
    BD --> Firestore
    Settings --> SP
    Payment --> Firestore
    Config --> RC
    
    SP --> Cache
    Files --> Storage
```

## 🔐 認證與授權流程

```mermaid
flowchart TD
    Start([用戶登入]) --> Method{登入方式}
    
    Method -->|Google| Google[Google 登入]
    Method -->|Apple| Apple[Apple 登入]
    Method -->|匿名| Anonymous[匿名登入]
    
    Google --> GoogleAuth[Google 認證]
    Apple --> AppleAuth[Apple 認證]
    Anonymous --> FirebaseAnon[Firebase 匿名認證]
    
    GoogleAuth --> Firebase[Firebase 認證]
    AppleAuth --> Firebase
    FirebaseAnon --> Firebase
    
    Firebase --> Success{認證成功?}
    
    Success -->|是| Profile[載入用戶資料]
    Success -->|否| Error[顯示錯誤]
    
    Profile --> Sync[同步雲端資料]
    Sync --> Home[進入主頁]
    
    Error --> Start
```

## 🎨 UI 組件架構

```mermaid
graph TD
    subgraph "頁面層 (Pages)"
        HP[主頁面]
        CP[星盤頁面]
        AP[分析頁面]
        SP[設定頁面]
    end
    
    subgraph "組件層 (Widgets)"
        CC[星盤組件]
        PC[行星組件]
        AC[相位組件]
        SC[設定組件]
    end
    
    subgraph "共用組件 (Shared)"
        Button[按鈕組件]
        Card[卡片組件]
        Dialog[對話框組件]
        Form[表單組件]
    end
    
    HP --> CC
    HP --> Button
    HP --> Card
    
    CP --> CC
    CP --> PC
    CP --> AC
    
    AP --> CC
    AP --> Dialog
    
    SP --> SC
    SP --> Form
    
    CC --> Button
    PC --> Card
    AC --> Card
    SC --> Form
```

## 🔄 狀態管理架構

```mermaid
graph TB
    subgraph "Provider 狀態管理"
        CVM[ChartViewModel]
        SVM[SettingsViewModel]
        AVM[AuthViewModel]
        IVM[InterpretationViewModel]
    end
    
    subgraph "狀態"
        CS[星盤狀態]
        SS[設定狀態]
        AS[認證狀態]
        IS[解讀狀態]
    end
    
    subgraph "UI 組件"
        ChartPage[星盤頁面]
        SettingsPage[設定頁面]
        LoginPage[登入頁面]
        AIPage[AI 解讀頁面]
    end
    
    CVM --> CS
    SVM --> SS
    AVM --> AS
    IVM --> IS
    
    ChartPage --> CVM
    SettingsPage --> SVM
    LoginPage --> AVM
    AIPage --> IVM
    
    CS --> ChartPage
    SS --> SettingsPage
    AS --> LoginPage
    IS --> AIPage
```

## 🚀 部署架構

```mermaid
graph TB
    subgraph "開發環境"
        Dev[開發機]
        Git[Git 倉庫]
    end
    
    subgraph "CI/CD"
        GHA[GitHub Actions]
        Build[建置系統]
        Test[測試系統]
    end
    
    subgraph "發布平台"
        iOS[App Store]
        Android[Google Play]
        Web[Firebase Hosting]
        macOS[Mac App Store]
    end
    
    subgraph "後端服務"
        Firebase[Firebase]
        AI[AI Services]
    end
    
    Dev --> Git
    Git --> GHA
    GHA --> Build
    Build --> Test
    Test --> iOS
    Test --> Android
    Test --> Web
    Test --> macOS
    
    iOS --> Firebase
    Android --> Firebase
    Web --> Firebase
    macOS --> Firebase
    
    Firebase --> AI
```

## 📊 性能監控架構

```mermaid
graph LR
    subgraph "客戶端監控"
        Crash[崩潰監控]
        Perf[性能監控]
        Analytics[使用分析]
    end
    
    subgraph "服務端監控"
        API[API 監控]
        DB[資料庫監控]
        Auth[認證監控]
    end
    
    subgraph "監控平台"
        Firebase[Firebase Analytics]
        Crashlytics[Firebase Crashlytics]
        Console[Firebase Console]
    end
    
    Crash --> Crashlytics
    Perf --> Firebase
    Analytics --> Firebase
    
    API --> Console
    DB --> Console
    Auth --> Console
    
    Crashlytics --> Console
    Firebase --> Console
```

這些圖表展示了 Astreal 專案的完整技術架構，從整體架構到具體的流程和組件關係，為開發團隊提供了清晰的技術藍圖和實作指導。
