# 快速星盤類型切換功能

## 功能概述
為星盤頁面新增了快速星盤類型切換組件，讓用戶可以在顯眼的位置快速切換不同的星盤類型，大幅提升用戶體驗和操作效率。

## 新增功能

### 1. 快速星盤類型選擇器
- **位置**：星盤標題上方的顯眼位置（最頂部）
- **功能**：
  - 水平滑動的星盤類型標籤
  - 顯示常用的星盤類型（本命盤、行運盤、比較盤等）
  - 可以往右滑看更多類型
  - 直接點選即可切換星盤類型
  - 當前選中的星盤類型會高亮顯示
- **美化設計**：
  - 半透明白色背景配上陰影效果
  - 圓角膠囊狀設計（25px 圓角）
  - 分隔線區分標籤區和更多按鈕
  - 選中項目使用漸層背景和陰影高亮

### 2. 完整星盤類型選擇器
- **觸發方式**：點擊「更多」按鈕
- **功能**：
  - 底部彈出式選單
  - 按類別分組顯示所有星盤類型
  - 網格佈局，方便瀏覽和選擇
  - 支援滾動查看所有類型
- **美化設計**：
  - 漸層背景（白色到淺灰色）
  - 24px 圓角和陰影效果
  - 拖拽指示器和精美標題欄
  - 每個分類都有獨立的卡片設計
  - 選中項目有漸層背景和勾選圖示

### 3. 常用星盤類型
快速選擇器中包含以下常用星盤類型：
- 本命盤 (Natal Chart)
- 行運盤 (Transit Chart)
- 比較盤 (Synastry Chart)
- 組合盤 (Composite Chart)
- 太陽返照盤 (Solar Return)
- 月亮返照盤 (Lunar Return)
- 次限推運盤 (Secondary Progression)
- 太陽弧推運盤 (Solar Arc Direction)

## 技術實作

### 新增的檔案

#### 1. `lib/ui/widgets/chart_type_quick_selector.dart`
**主要功能**：
- 快速星盤類型選擇器主組件
- 水平滑動的標籤列表
- 星盤類型切換邏輯
- 載入指示器和錯誤處理

**核心方法**：
```dart
Widget _buildChartTypeChip()          // 構建星盤類型標籤
Widget _buildMoreButton()             // 構建更多按鈕
void _onChartTypeSelected()           // 處理星盤類型選擇
void _showFullChartTypeSelector()     // 顯示完整選擇器
```

#### 2. `lib/ui/widgets/chart_type_quick_selector_sheet.dart`
**主要功能**：
- 完整的星盤類型選擇器底部表單
- 按類別分組顯示星盤類型
- 網格佈局和滾動支援

**核心方法**：
```dart
Widget _buildCategorySection()        // 構建分類區塊
Map<String, dynamic> _getChartTypeColorInfo()  // 獲取星盤類型顏色資訊
```

### 修改的檔案

#### 1. `lib/widgets/chart_view_widget.dart`
**主要變更**：
- 新增 `ChartTypeQuickSelector` 組件
- 將快速選擇器移到 `ChartTitleWidget` 上方
- 使用 `Positioned` 組件固定在頂部
- 調整頂部空間配置
- 新增切換回調處理

### 設計特點

#### UI 設計
- **快速選擇器**：
  - 半透明白色背景配上精美陰影
  - 圓角膠囊狀設計（25px 圓角）
  - 水平滑動標籤設計
  - 當前選中項目使用漸層背景和陰影高亮
  - 每個標籤包含圖示和簡短名稱
  - 分隔線區分標籤區和更多按鈕
  - 動畫過渡效果（200ms）

- **完整選擇器**：
  - 漸層背景（白色到淺灰色）
  - 24px 圓角和精美陰影效果
  - 拖拽指示器和美化標題欄
  - 按功能分類組織，每個分類都有獨立卡片
  - 網格佈局，2列顯示
  - 每個項目包含圖示容器、名稱和勾選圖示
  - 選中項目使用漸層背景和陰影高亮

- **顏色系統**：
  - 每種星盤類型都有專屬顏色和圖示
  - 選中狀態使用對應顏色的漸層背景
  - 符合應用的主題顏色規範
  - 無漸層設計，符合簡潔風格

#### 交互體驗
- **即時切換**：點擊即可切換星盤類型
- **載入反饋**：切換時顯示載入指示器
- **成功提示**：切換成功後顯示 SnackBar 提示
- **錯誤處理**：切換失敗時顯示錯誤訊息

### 星盤類型分類

#### 個人星盤
- 本命盤

#### 預測類星盤
- 行運盤
- 次限推運盤
- 三限推運盤
- 太陽弧推運盤

#### 返照盤類
- 太陽返照盤
- 月亮返照盤

#### 關係星盤
- 比較盤
- 組合盤
- 時空中點盤
- 馬克思盤

#### 關係推運盤
- 比較盤次限推運
- 比較盤三限推運
- 組合盤次限推運
- 組合盤三限推運
- 時空中點盤次限推運
- 時空中點盤三限推運
- 馬克思盤次限推運
- 馬克思盤三限推運

#### 特殊星盤
- 法達盤
- 卜卦盤
- 事件盤
- 世俗盤
- 日月蝕盤
- 二分二至盤

## 用戶體驗改善

### 1. 操作效率
- 星盤類型切換從多步驟簡化為一步完成
- 常用類型一目了然，快速存取
- 減少導航層級，提升操作流暢度

### 2. 視覺體驗
- 顯眼的位置設計，用戶容易發現
- 清晰的視覺層次和分類組織
- 一致的設計語言和顏色系統

### 3. 功能發現性
- 將原本隱藏在選單中的功能提升到主界面
- 「更多」按鈕引導用戶探索完整功能
- 直觀的圖示和命名幫助用戶理解

## 技術細節

### 狀態管理
- 使用 `ChartViewModel` 管理星盤狀態
- 切換時自動清除緩存的繪製器
- 支援載入狀態和錯誤狀態管理

### 性能優化
- 延遲載入完整選擇器
- 緩存星盤類型顏色和圖示資訊
- 避免不必要的重建和重繪

### 錯誤處理
- 完整的異常捕獲和處理
- 用戶友好的錯誤訊息
- 載入失敗時的回退機制

## 測試結果
- ✅ 編譯成功，無錯誤
- ✅ 快速選擇器正常顯示
- ✅ 星盤類型切換功能正常
- ✅ 完整選擇器正常彈出
- ✅ 載入和錯誤狀態正確處理
- ✅ UI 設計符合應用風格

## 後續建議
1. 可考慮新增星盤類型的收藏功能
2. 可新增最近使用的星盤類型記錄
3. 可考慮新增星盤類型的搜尋功能
4. 可新增星盤類型的自定義排序

## 美化優化更新

### 快速選擇器美化
- **背景設計**：半透明白色背景配上陰影效果
- **形狀設計**：圓角膠囊狀（25px 圓角）
- **標籤設計**：選中項目使用漸層背景和陰影
- **分隔設計**：分隔線區分功能區域
- **動畫效果**：200ms 平滑過渡動畫

### 完整選擇器美化
- **背景設計**：漸層背景（白色到淺灰色）
- **容器設計**：24px 圓角和精美陰影
- **標題設計**：拖拽指示器和漸層標題欄
- **分類設計**：每個分類獨立卡片，包含計數標籤
- **項目設計**：圖示容器、漸層背景、勾選圖示

### 位置優化
- **快速選擇器**：移到 `ChartTitleWidget` 上方，成為最頂部組件
- **固定定位**：使用 `Positioned` 組件固定在頂部
- **空間調整**：增加頂部空間適應新佈局

## 更新日期
2025-07-10

## 相關檔案
- `lib/ui/widgets/chart_type_quick_selector.dart`
- `lib/ui/widgets/chart_type_quick_selector_sheet.dart`
- `lib/widgets/chart_view_widget.dart`
- `lib/models/chart_type.dart`
