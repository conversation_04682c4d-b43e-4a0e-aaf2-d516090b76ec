Release Note:
- feat: 重構 ChartTypeSettings 以支持 Data Class 並保持向後相容性
- feat: 每日星象行星位置計算調整及UI優化
- fix: 調整比較盤和行運盤相位資訊中行星歸屬描述順序
- feat: 新增「原始內容」頁面，顯示AI解讀的輸入信息
- feat: 調整 OpenAI GPT 模型參數
- feat: 新增出生資料後自動導航至星盤頁面
- chore: 為腳本新增 Flutter PATH 環境變數
- fix: 移除性別選項中的「中性」並調整預設值
- feat: 優化出生資料表單 UI 與響應式佈局
- fix: 將「白羊座」統一修正為「牡羊座」
- feat: 新增占星學核心資料模型
- feat: 增強星座定義，引入類型安全的 `ZodiacSign` 模型並提供示例
- refactor: 星座符號統一由 `ZodiacDefinitions` 提供
- feat: 每日占星新增行星位置顯示
- docs: 更新開發文件與筆記
- feat: 增強每日星象頁面及個人化服務
- refactor: 更新星盤標題顯示邏輯
- feat: 新增 NotoSansTC-Bold 字體並移除 SemiBold
- refactor: 將 NotoSansTC-Bold.ttf 更名為 NotoSansTC-SemiBold.ttf
- feat: 增強每日星相頁面，新增個人化與天象盤AI解讀功能
- feat: 實作每日星相功能與推播設定頁面
- feat: 新增用戶個人分析功能與 Firebase 整合
- feat: 用戶個人資料頁面 UI 重構與功能增強
- feat: AI 解讀結果頁面新增快速回饋選項
- feat: Web 平台影片播放體驗優化
- feat: 調整占星分析項目順序及卡片UI
- feat: AI 解讀服務及結果頁面優化
- feat: 星盤選擇頁面優化，支持直接進行 AI 分析
- feat: 優化占星分析方法選擇器，支持響應式佈局
- feat: 重構 AI 結果頁自定義問題按鈕並更新提示
