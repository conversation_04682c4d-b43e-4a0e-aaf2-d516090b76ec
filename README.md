# astreal

A new Flutter project.

## Getting Started

This project is a starting point for a Flutter application.

A few resources to get you started if this is your first Flutter project:

- [Lab: Write your first Flutter app](https://docs.flutter.dev/get-started/codelab)
- [Cookbook: Useful Flutter samples](https://docs.flutter.dev/cookbook)

For help getting started with Flutter development, view the
[online documentation](https://docs.flutter.dev/), which offers tutorials,
samples, guidance on mobile development, and a full API reference.

firebase deploy --only hosting

https://astreal-d3f70.web.app/all_versions.json




AstReal 的 Flutter 專案基本結構
lib/
├── main.dart
├── core/               // 命盤邏輯與星體運算
│   ├── ephemeris.dart
│   ├── chart_calculator.dart
│   └── constants.dart
├── models/            // 命盤、星體、相位等資料模型
│   ├── birth_chart.dart
│   ├── planet.dart
│   ├── aspect.dart
│   └── house.dart
├── screens/           // 畫面頁面
│   ├── home_screen.dart
│   ├── chart_screen.dart
│   ├── daily_screen.dart
│   ├── match_screen.dart
│   └── counsel_screen.dart
├── widgets/           // 通用元件
│   ├── astro_card.dart
│   ├── planet_icon.dart
│   ├── section_title.dart
│   └── astro_chart_painter.dart
├── services/          // 資料處理與外部服務串接
│   ├── astro_service.dart
│   └── firebase_service.dart
└── utils/             // 工具函式
├── date_utils.dart
└── chart_utils.dart

// main.dart 會初始化頁面與主題，並導向首頁
void main() {
runApp(const AstrealApp());
}

class AstrealApp extends StatelessWidget {
const AstrealApp({super.key});

@override
Widget build(BuildContext context) {
return MaterialApp(
title: 'AstReal',
theme: ThemeData(
colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
useMaterial3: true,
),
home: const HomeScreen(),
);
}
}


我會幫您建立這個 Flutter 專案的基本結構。首先讓我們檢查目前的專案狀態。

Ran tool
很好，我看到這是一個標準的 Flutter 專案結構。讓我們按照您提供的結構開始建立檔案。

首先，我會建立 `main.dart` 檔案：

```dart:lib/main.dart
import 'package:flutter/material.dart';
import 'screens/home_screen.dart';

void main() {
  runApp(const AstrealApp());
}

class AstrealApp extends StatelessWidget {
  const AstrealApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'AstReal',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
        useMaterial3: true,
      ),
      home: const HomeScreen(),
    );
  }
}
```
現在讓我們建立基本的目錄結構：

```bash
mkdir -p lib/{core,models,screens,widgets,services,utils}
```
接下來，讓我們創建一些基本的檔案。首先是 `HomeScreen`：

```dart:lib/screens/home_screen.dart

```
現在讓我們建立一些基本的模型類別。首先是 `Planet` 類別：

```dart:lib/models/planet.dart
```
接下來建立 `BirthChart` 類別：

```
讓我們建立 `House` 類別：

```
最後建立 `Aspect` 類別：

```dart:lib/models/aspect.dart

```
我已經為您建立了基本的專案結構，包括：

1. 主要的 `main.dart` 檔案，設定了應用程式的入口點和基本主題
2. 基本的首頁畫面 `HomeScreen`，包含四個主要功能的導航卡片
3. 核心的資料模型：
    - `Planet`：行星資訊
    - `House`：宮位資訊
    - `Aspect`：相位資訊
    - `BirthChart`：完整的命盤資訊

接下來您可以：

1. 實作 `core/` 目錄下的命盤計算邏輯
2. 完成其他畫面（`chart_screen.dart`、`daily_screen.dart` 等）
3. 建立通用元件（`widgets/` 目錄）
4. 實作資料服務（`services/` 目錄）

您想要我先幫您實作哪一部分？

好的，我會幫您實作 `core/` 目錄下的命盤計算邏輯。首先，我們需要建立幾個核心檔案：

1. `constants.dart` - 定義占星常數
2. `ephemeris.dart` - 處理星體位置計算
3. `chart_calculator.dart` - 命盤計算邏輯

首先建立 `constants.dart`：

```dart:lib/core/constants.dart

```
接下來建立 `ephemeris.dart`：

```dart:lib/core/ephemeris.dart
```
最後建立 `chart_calculator.dart`：

```dart:lib/core/chart_calculator.dart
```
我已經完成了 `core/` 目錄下的基本命盤計算邏輯實作。這個實作包含以下主要功能：

1. `constants.dart`：
    - 定義了星座、相位、行星和宮位系統的常數
    - 提供了一些基本的工具函數，如根據黃經計算星座

2. `ephemeris.dart`：
    - 提供行星位置計算功能（目前是簡化版本）
    - 可以計算單個行星或所有行星的位置
    - 包含行星速度和逆行狀態的計算

3. `chart_calculator.dart`：
    - 提供完整的命盤計算功能
    - 計算宮位系統（目前使用簡化的等宮系統）
    - 分配行星到對應的宮位
    - 計算行星間的相位關係
    - 提供恆星時計算功能

需要注意的是，目前的實作是簡化版本，主要用於展示基本功能。在實際應用中，您可能需要：

1. 整合專業的星曆庫（如 Swiss Ephemeris）來獲得更精確的行星位置
2. 實作更複雜的宮位系統計算（如 Placidus、Koch 等）
3. 加入更多的相位類型和行星點（如小行星、固定星等）
4. 優化計算效能，特別是在計算多張命盤時

您想要我先幫您實作哪些額外的功能，或是繼續完成其他部分的開發？

