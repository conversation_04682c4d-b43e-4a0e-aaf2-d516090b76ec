import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:astreal/data/models/astrology/chart_settings_data_classes.dart';

void main() {
  group('PlanetVisibilitySettings', () {
    test('should create default settings correctly', () {
      const settings = PlanetVisibilitySettings();
      
      expect(settings.sun, true);
      expect(settings.moon, true);
      expect(settings.mercury, true);
      expect(settings.venus, true);
      expect(settings.mars, true);
      expect(settings.jupiter, true);
      expect(settings.saturn, true);
      expect(settings.uranus, true);
      expect(settings.neptune, true);
      expect(settings.pluto, true);
      expect(settings.ascendant, true);
      expect(settings.midheaven, true);
      expect(settings.descendant, true);
      expect(settings.imumCoeli, true);
      expect(settings.northNode, true);
      expect(settings.southNode, true);
      expect(settings.lilith, false);
      expect(settings.chiron, false);
      expect(settings.fortunePoint, true);
      expect(settings.spiritPoint, false);
    });

    test('should convert to Map correctly', () {
      const settings = PlanetVisibilitySettings(
        sun: true,
        moon: false,
        mercury: true,
      );
      
      final map = settings.toMap();
      
      expect(map['太陽'], true);
      expect(map['月亮'], false);
      expect(map['水星'], true);
      expect(map['金星'], true); // 預設值
    });

    test('should create from Map correctly', () {
      final map = {
        '太陽': false,
        '月亮': true,
        '水星': false,
        '金星': true,
      };
      
      final settings = PlanetVisibilitySettings.fromMap(map);
      
      expect(settings.sun, false);
      expect(settings.moon, true);
      expect(settings.mercury, false);
      expect(settings.venus, true);
      expect(settings.mars, true); // 預設值，因為 Map 中沒有
    });

    test('should copy with planet modification correctly', () {
      const original = PlanetVisibilitySettings();
      
      final modified = original.copyWithPlanet('太陽', false);
      
      expect(original.sun, true); // 原始物件不變
      expect(modified.sun, false); // 新物件已修改
      expect(modified.moon, true); // 其他屬性保持不變
    });

    test('should use predefined configurations correctly', () {
      // 測試分析模式預設值
      expect(PlanetVisibilitySettings.analysisDefault.lilith, true);
      expect(PlanetVisibilitySettings.analysisDefault.chiron, true);
      expect(PlanetVisibilitySettings.analysisDefault.sunMoonMidpoint, true);
      
      // 測試首頁模式預設值
      expect(PlanetVisibilitySettings.homeDefault.ascendant, false);
      expect(PlanetVisibilitySettings.homeDefault.lilith, false);
      expect(PlanetVisibilitySettings.homeDefault.fortunePoint, false);
    });

    test('should handle equality correctly', () {
      const settings1 = PlanetVisibilitySettings(sun: true, moon: false);
      const settings2 = PlanetVisibilitySettings(sun: true, moon: false);
      const settings3 = PlanetVisibilitySettings(sun: false, moon: false);
      
      expect(settings1, equals(settings2));
      expect(settings1, isNot(equals(settings3)));
    });
  });

  group('AspectOrbSettings', () {
    test('should create default settings correctly', () {
      const settings = AspectOrbSettings();
      
      expect(settings.conjunction, 8.0);
      expect(settings.sextile, 4.0);
      expect(settings.square, 8.0);
      expect(settings.trine, 8.0);
      expect(settings.opposition, 8.0);
      expect(settings.semisextile, 2.0);
    });

    test('should convert to Map correctly', () {
      const settings = AspectOrbSettings(
        conjunction: 10.0,
        square: 6.0,
      );
      
      final map = settings.toMap();
      
      expect(map['合相'], 10.0);
      expect(map['四分相'], 6.0);
      expect(map['三分相'], 8.0); // 預設值
    });

    test('should create from Map correctly', () {
      final map = {
        '合相': 12.0,
        '六分相': 5.0,
        '四分相': 10.0,
      };
      
      final settings = AspectOrbSettings.fromMap(map);
      
      expect(settings.conjunction, 12.0);
      expect(settings.sextile, 5.0);
      expect(settings.square, 10.0);
      expect(settings.trine, 8.0); // 預設值
    });

    test('should copy with aspect modification correctly', () {
      const original = AspectOrbSettings();
      
      final modified = original.copyWithAspect('合相', 12.0);
      
      expect(original.conjunction, 8.0); // 原始物件不變
      expect(modified.conjunction, 12.0); // 新物件已修改
      expect(modified.sextile, 4.0); // 其他屬性保持不變
    });

    test('should use predefined configurations correctly', () {
      // 測試古典模式
      expect(AspectOrbSettings.classicalDefault.conjunction, 10.0);
      expect(AspectOrbSettings.classicalDefault.sextile, 6.0);
      
      // 測試現代模式
      expect(AspectOrbSettings.modernDefault.conjunction, 8.0);
      expect(AspectOrbSettings.modernDefault.sextile, 4.0);
      
      // 測試首頁模式
      expect(AspectOrbSettings.homeDefault.conjunction, 3.0);
      expect(AspectOrbSettings.homeDefault.sextile, 3.0);
    });
  });

  group('AspectColorSettings', () {
    test('should create default settings correctly', () {
      const settings = AspectColorSettings();
      
      expect(settings.conjunction, const Color(0xFFFF6B6B));
      expect(settings.sextile, const Color(0xFF2999A4));
      expect(settings.square, Colors.red);
      expect(settings.trine, Colors.green);
      expect(settings.opposition, const Color(0xFF051883));
    });

    test('should convert to Map correctly', () {
      const settings = AspectColorSettings(
        conjunction: Colors.blue,
        square: Colors.orange,
      );
      
      final map = settings.toMap();
      
      expect(map['合相'], Colors.blue);
      expect(map['四分相'], Colors.orange);
      expect(map['三分相'], Colors.green); // 預設值
    });

    test('should copy with aspect color modification correctly', () {
      const original = AspectColorSettings();
      
      final modified = original.copyWithAspect('合相', Colors.purple);
      
      expect(original.conjunction, const Color(0xFFFF6B6B)); // 原始物件不變
      expect(modified.conjunction, Colors.purple); // 新物件已修改
      expect(modified.sextile, const Color(0xFF2999A4)); // 其他屬性保持不變
    });
  });

  group('PlanetColorSettings', () {
    test('should create default settings correctly', () {
      const settings = PlanetColorSettings();
      
      expect(settings.sun, const Color(0xFFFF0000));
      expect(settings.moon, const Color(0xFF0A0AFF));
      expect(settings.mercury, const Color(0xFF127116));
      expect(settings.venus, const Color(0xFFCC9933));
      expect(settings.mars, Colors.red);
    });

    test('should convert to Map correctly', () {
      const settings = PlanetColorSettings(
        sun: Colors.orange,
        moon: Colors.grey,
      );

      final map = settings.toMap();

      expect(map['太陽'], Colors.orange);
      expect(map['月亮'], Colors.grey);
      expect(map['水星'], const Color(0xFF127116)); // 預設值
    });

    test('should copy with planet color modification correctly', () {
      const original = PlanetColorSettings();
      
      final modified = original.copyWithPlanet('太陽', Colors.yellow);
      
      expect(original.sun, const Color(0xFFFF0000)); // 原始物件不變
      expect(modified.sun, Colors.yellow); // 新物件已修改
      expect(modified.moon, const Color(0xFF0A0AFF)); // 其他屬性保持不變
    });
  });

  group('Integration Tests', () {
    test('should work together in a complete settings scenario', () {
      // 建立完整的設定組合
      const planetSettings = PlanetVisibilitySettings(
        sun: true,
        moon: true,
        mercury: false,
        lilith: true,
      );
      
      const orbSettings = AspectOrbSettings(
        conjunction: 10.0,
        square: 6.0,
      );
      
      const colorSettings = AspectColorSettings(
        conjunction: Colors.blue,
        square: Colors.orange,
      );
      
      const planetColors = PlanetColorSettings(
        sun: Colors.yellow,
        moon: Colors.grey,
      );
      
      // 驗證設定可以正確轉換為 Map
      final planetMap = planetSettings.toMap();
      final orbMap = orbSettings.toMap();
      final colorMap = colorSettings.toMap();
      final planetColorMap = planetColors.toMap();
      
      expect(planetMap['太陽'], true);
      expect(planetMap['水星'], false);
      expect(orbMap['合相'], 10.0);
      expect(colorMap['合相'], Colors.blue);
      expect(planetColorMap['太陽'], Colors.yellow);
      
      // 驗證可以從 Map 重新建立
      final rebuiltPlanetSettings = PlanetVisibilitySettings.fromMap(planetMap);
      final rebuiltOrbSettings = AspectOrbSettings.fromMap(orbMap);
      
      expect(rebuiltPlanetSettings, equals(planetSettings));
      expect(rebuiltOrbSettings, equals(orbSettings));
    });
  });
}
