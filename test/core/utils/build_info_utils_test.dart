import 'package:flutter_test/flutter_test.dart';
import 'package:astreal/core/utils/build_info_utils.dart';

void main() {
  group('BuildInfoUtils', () {
    group('getBuildDate', () {
      test('should parse full timestamp correctly', () {
        final result = BuildInfoUtils.getBuildDate('20250807131801');
        expect(result, equals('2025-08-07 13:18:01'));
      });

      test('should parse timestamp with underscores correctly', () {
        final result = BuildInfoUtils.getBuildDate('20250807_131801');
        expect(result, equals('2025-08-07 13:18:01'));
      });

      test('should parse timestamp with suffix correctly', () {
        final result = BuildInfoUtils.getBuildDate('20250807_131801_force_update0');
        expect(result, equals('2025-08-07 13:18:01'));
      });

      test('should parse date only format correctly', () {
        final result = BuildInfoUtils.getBuildDate('20250807');
        expect(result, equals('2025-08-07'));
      });

      test('should handle simple build number', () {
        final result = BuildInfoUtils.getBuildDate('1');
        // 應該返回編譯時間格式，我們只檢查格式是否正確
        expect(result, matches(r'\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}'));
      });

      test('should handle invalid format gracefully', () {
        final result = BuildInfoUtils.getBuildDate('invalid');
        // 應該返回編譯時間格式
        expect(result, matches(r'\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}'));
      });
    });

    group('formatVersionDisplay', () {
      test('should format standard version correctly', () {
        final result = BuildInfoUtils.formatVersionDisplay('1.0.0', '20250807_131801');
        expect(result, equals('1.0.0 (20250807.1318)'));
      });

      test('should format force update version correctly', () {
        final result = BuildInfoUtils.formatVersionDisplay('1.0.0', '20250807_131801_force_update0');
        expect(result, equals('1.0.0 (20250807.1318-強制更新)'));
      });

      test('should format beta version correctly', () {
        final result = BuildInfoUtils.formatVersionDisplay('1.0.0', '20250807_131801_beta');
        expect(result, equals('1.0.0 (20250807.1318-測試版)'));
      });

      test('should format alpha version correctly', () {
        final result = BuildInfoUtils.formatVersionDisplay('1.0.0', '20250807_131801_alpha');
        expect(result, equals('1.0.0 (20250807.1318-內測版)'));
      });

      test('should handle simple build number', () {
        final result = BuildInfoUtils.formatVersionDisplay('1.0.0', '1');
        expect(result, equals('1.0.0 (1)'));
      });

      test('should handle complex suffix', () {
        final result = BuildInfoUtils.formatVersionDisplay('1.0.0', '20250807_131801_custom_suffix');
        expect(result, equals('1.0.0 (20250807.1318)'));
      });
    });

    group('version type detection', () {
      test('should detect development build correctly', () {
        expect(BuildInfoUtils.isDevelopmentBuild('1'), isTrue);
        expect(BuildInfoUtils.isDevelopmentBuild('dev'), isTrue);
        expect(BuildInfoUtils.isDevelopmentBuild('debug'), isTrue);
        expect(BuildInfoUtils.isDevelopmentBuild('20250807_131801'), isFalse);
      });

      test('should detect force update build correctly', () {
        expect(BuildInfoUtils.isForceUpdateBuild('20250807_131801_force_update0'), isTrue);
        expect(BuildInfoUtils.isForceUpdateBuild('20250807_131801'), isFalse);
      });

      test('should detect beta build correctly', () {
        expect(BuildInfoUtils.isBetaBuild('20250807_131801_beta'), isTrue);
        expect(BuildInfoUtils.isBetaBuild('20250807_131801_alpha'), isTrue);
        expect(BuildInfoUtils.isBetaBuild('20250807_131801'), isFalse);
      });
    });

    group('BuildInfo', () {
      test('should generate correct version tag for force update', () {
        const buildInfo = BuildInfo(
          appName: 'AstReal',
          packageName: 'com.example.astreal',
          version: '1.0.0',
          buildNumber: '20250807_131801_force_update0',
          buildDate: '2025-08-07 13:18:01',
          formattedVersion: '1.0.0 (20250807.1318-強制更新)',
        );

        expect(buildInfo.versionTag, equals('強制更新'));
      });

      test('should generate correct version tag for beta', () {
        const buildInfo = BuildInfo(
          appName: 'AstReal',
          packageName: 'com.example.astreal',
          version: '1.0.0',
          buildNumber: '20250807_131801_beta',
          buildDate: '2025-08-07 13:18:01',
          formattedVersion: '1.0.0 (20250807.1318-測試版)',
        );

        expect(buildInfo.versionTag, equals('測試版'));
      });

      test('should generate correct version tag for development', () {
        const buildInfo = BuildInfo(
          appName: 'AstReal',
          packageName: 'com.example.astreal',
          version: '1.0.0',
          buildNumber: '1',
          buildDate: '2025-08-07 13:18:01',
          formattedVersion: '1.0.0 (1)',
        );

        expect(buildInfo.versionTag, equals('開發版'));
      });

      test('should generate correct version tag for release', () {
        const buildInfo = BuildInfo(
          appName: 'AstReal',
          packageName: 'com.example.astreal',
          version: '1.0.0',
          buildNumber: '20250807_131801',
          buildDate: '2025-08-07 13:18:01',
          formattedVersion: '1.0.0 (20250807.1318)',
        );

        expect(buildInfo.versionTag, equals('正式版'));
      });
    });

    group('edge cases', () {
      test('should handle empty build number', () {
        final result = BuildInfoUtils.getBuildDate('');
        expect(result, matches(r'\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}'));
      });

      test('should handle malformed timestamp', () {
        final result = BuildInfoUtils.getBuildDate('2025080713180');  // 缺少一位
        // 這個會被解析為日期格式，因為前8位是有效日期
        expect(result, matches(r'\d{4}-\d{2}-\d{2}( \d{2}:\d{2}:\d{2})?'));
      });

      test('should handle invalid date', () {
        final result = BuildInfoUtils.getBuildDate('20251301_131801');  // 13月
        expect(result, matches(r'\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}'));
      });

      test('should handle version display with empty parameters', () {
        final result = BuildInfoUtils.formatVersionDisplay('', '');
        expect(result, equals(' ()'));
      });
    });
  });
}
