name: astreal
description: "A professional astrology app"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: '>=3.2.3 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # 核心功能依賴
  shared_preferences: ^2.5.3
  sweph: ^3.2.1+2.10.3
  path_provider: ^2.1.1
  turf: ^0.0.10
  logger: ^2.5.0
  timezone: ^0.10.0

  # PDF 和分享功能
  pdf: ^3.11.3
  printing: ^5.14.2
  share_plus: ^11.0.0
  flutter_pdfview: ^1.3.2

  # 地理位置和網路
  geocoding: ^4.0.0
  geolocator: ^14.0.1
  http: ^1.2.2

  # Firebase 相關
  firebase_core: ^3.13.0
  firebase_auth: ^5.3.1
  cloud_firestore: ^5.6.11
  firebase_remote_config: ^5.4.5
  firebase_storage: ^12.3.6
  firebase_app_check: ^0.3.1+4
  firebase_messaging: ^15.1.3

  # 本地通知
  flutter_local_notifications: ^18.0.1

  # 認證相關
  google_sign_in: ^6.2.1
  sign_in_with_apple: 7.0.1

  # UI 和工具
  provider: ^6.1.2
  flutter_markdown: ^0.7.7+1
  url_launcher: ^6.2.4
  clipboard: ^0.1.3
  table_calendar: ^3.0.9
  flutter_datetime_picker_plus: ^2.1.0
  webview_flutter: ^4.10.0

  # 文件和數據處理
  file_picker: ^10.1.2
  csv: ^6.0.0
  intl: ^0.20.2
  uuid: ^4.5.1
  package_info_plus: ^8.3.0
  device_info_plus: ^10.1.2
  crypto: ^3.0.6

  # 媒體和購買
  gal: ^2.3.1
  in_app_purchase: ^3.1.13

  # 郵件功能
  mailer: ^6.0.1


dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^6.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An images asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

  assets:
    - assets/json/planet.json
    - assets/json/sign.json
    - assets/config/
    - assets/ephe/
    - assets/ephe/seas_18.se1
    - assets/ephe/sefstars.txt
    - assets/ephe/seasnam.txt
    - assets/ephe/se00433s.se1
    - assets/ephe/sepl_18.se1
    - assets/ephe/semo_18.se1
    - assets/combined.json
    - assets/images/flutter_launcher_icons.png

  fonts:
    - family: astro_one_font
      fonts:
        - asset: assets/fonts/astro_one_font.ttf

    - family: NotoSansTC
      fonts:
#        - asset: assets/fonts/NotoSansTC-Bold.ttf
#        - asset: assets/fonts/NotoSansTC-VariableFont_wght.ttf
#        - asset: assets/fonts/NotoSansTC-Thin.ttf
        - asset: assets/fonts/NotoSansTC-SemiBold.ttf
#        - asset: assets/fonts/NotoSansTC-Regular.ttf
#        - asset: assets/fonts/NotoSansTC-ExtraBold.ttf
#        - asset: assets/fonts/NotoSansTC-Black.ttf



# 圖標和啟動畫面配置已移除，因為相關套件未被使用
# 如需要可以重新添加 flutter_launcher_icons 和 flutter_native_splash 套件