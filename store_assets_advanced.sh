#!/bin/bash

# 星真占星 AstReal - 進階商店上架圖片生成腳本
# 包含多種設計風格和自動化功能

set -e

echo "🌟 星真占星 AstReal - 進階商店資產生成器"
echo "============================================"

# 顏色定義
ROYAL_INDIGO="#3F51B5"
SOLAR_AMBER="#F5A623"
INDIGO_SURFACE="#303F9F"
INDIGO_LIGHT="#7986CB"
PASTEL_SKY_BLUE="#E6F0FA"
LIGHT_CORNSILK="#FFF8DC"

# 創建輸出目錄
mkdir -p store_assets/google_play/variations
mkdir -p store_assets/app_store/variations
mkdir -p store_assets/icons/adaptive
mkdir -p store_assets/screenshots/templates

echo "📱 生成多種風格的應用程式圖示..."

# 基本圖示
magick "assets/images/flutter_launcher_icons.png" -resize 512x512 "store_assets/google_play/app_icon_512.png"
magick "assets/images/flutter_launcher_icons.png" -resize 1024x1024 "store_assets/app_store/app_icon_1024.png"

# 帶背景的圖示變體
echo "🎨 生成帶背景的圖示變體..."

# 專業模式風格 (Royal Indigo)
magick -size 1024x1024 xc:"$ROYAL_INDIGO" \
    \( "assets/images/flutter_launcher_icons.png" -resize 800x800 \) \
    -gravity center -composite \
    "store_assets/app_store/variations/app_icon_professional_1024.png"

# 初心者模式風格 (Light Cornsilk)
magick -size 1024x1024 xc:"$LIGHT_CORNSILK" \
    \( "assets/images/flutter_launcher_icons.png" -resize 800x800 \) \
    -gravity center -composite \
    "store_assets/app_store/variations/app_icon_beginner_1024.png"

# 漸層背景版本
magick -size 1024x1024 gradient:"$ROYAL_INDIGO"-"$INDIGO_LIGHT" \
    \( "assets/images/flutter_launcher_icons.png" -resize 800x800 \) \
    -gravity center -composite \
    "store_assets/app_store/variations/app_icon_gradient_1024.png"

echo "🖼️ 生成多種精選圖片設計..."

# 設計 1: 簡潔專業風格
magick -size 1024x500 xc:"$ROYAL_INDIGO" \
    \( "assets/images/flutter_launcher_icons.png" -resize 180x180 \) \
    -gravity west -geometry +80+0 -composite \
    -font "Arial-Bold" -pointsize 42 -fill white \
    -gravity center -geometry +120+0 \
    -annotate +0-40 "星真占星" \
    -font "Arial" -pointsize 22 -fill "$SOLAR_AMBER" \
    -annotate +0+10 "AstReal" \
    -font "Arial" -pointsize 16 -fill white \
    -annotate +0+40 "專業占星分析應用" \
    "store_assets/google_play/feature_graphic_professional.png"

# 設計 2: 溫和初心者風格
magick -size 1024x500 xc:"$LIGHT_CORNSILK" \
    \( "assets/images/flutter_launcher_icons.png" -resize 180x180 \) \
    -gravity west -geometry +80+0 -composite \
    -font "Arial-Bold" -pointsize 42 -fill "$ROYAL_INDIGO" \
    -gravity center -geometry +120+0 \
    -annotate +0-40 "星真占星" \
    -font "Arial" -pointsize 22 -fill "$SOLAR_AMBER" \
    -annotate +0+10 "AstReal" \
    -font "Arial" -pointsize 16 -fill "$INDIGO_SURFACE" \
    -annotate +0+40 "輕鬆入門占星世界" \
    "store_assets/google_play/feature_graphic_beginner.png"

# 設計 3: 漸層現代風格
magick -size 1024x500 gradient:"$ROYAL_INDIGO"-"$PASTEL_SKY_BLUE" \
    \( "assets/images/flutter_launcher_icons.png" -resize 180x180 \) \
    -gravity west -geometry +80+0 -composite \
    -font "Arial-Bold" -pointsize 42 -fill white \
    -gravity center -geometry +120+0 \
    -annotate +0-40 "星真占星" \
    -font "Arial" -pointsize 22 -fill "$SOLAR_AMBER" \
    -annotate +0+10 "AstReal" \
    -font "Arial" -pointsize 16 -fill white \
    -annotate +0+40 "科技人眼中的占星" \
    "store_assets/google_play/feature_graphic_modern.png"

echo "📐 生成 Android Adaptive Icons..."

# Android Adaptive Icon 背景
magick -size 432x432 xc:"$ROYAL_INDIGO" "store_assets/icons/adaptive/background.png"
magick -size 432x432 xc:"$PASTEL_SKY_BLUE" "store_assets/icons/adaptive/background_light.png"

# Android Adaptive Icon 前景
magick "assets/images/flutter_launcher_icons.png" -resize 288x288 -background transparent -gravity center -extent 432x432 "store_assets/icons/adaptive/foreground.png"

echo "📱 生成完整的 Android 圖示集..."

# Android 各種密度的圖示
mkdir -p "store_assets/icons/android/mipmap-mdpi"
mkdir -p "store_assets/icons/android/mipmap-hdpi"
mkdir -p "store_assets/icons/android/mipmap-xhdpi"
mkdir -p "store_assets/icons/android/mipmap-xxhdpi"
mkdir -p "store_assets/icons/android/mipmap-xxxhdpi"

magick "assets/images/flutter_launcher_icons.png" -resize 48x48 "store_assets/icons/android/mipmap-mdpi/ic_launcher.png"
magick "assets/images/flutter_launcher_icons.png" -resize 72x72 "store_assets/icons/android/mipmap-hdpi/ic_launcher.png"
magick "assets/images/flutter_launcher_icons.png" -resize 96x96 "store_assets/icons/android/mipmap-xhdpi/ic_launcher.png"
magick "assets/images/flutter_launcher_icons.png" -resize 144x144 "store_assets/icons/android/mipmap-xxhdpi/ic_launcher.png"
magick "assets/images/flutter_launcher_icons.png" -resize 192x192 "store_assets/icons/android/mipmap-xxxhdpi/ic_launcher.png"

echo "🍎 生成完整的 iOS 圖示集..."

# iOS 各種尺寸的圖示
mkdir -p "store_assets/icons/ios"
magick "assets/images/flutter_launcher_icons.png" -resize 20x20 "store_assets/icons/ios/Icon-20x20.png"
magick "assets/images/flutter_launcher_icons.png" -resize 29x29 "store_assets/icons/ios/Icon-29x29.png"
magick "assets/images/flutter_launcher_icons.png" -resize 40x40 "store_assets/icons/ios/Icon-40x40.png"
magick "assets/images/flutter_launcher_icons.png" -resize 58x58 "store_assets/icons/ios/Icon-58x58.png"
magick "assets/images/flutter_launcher_icons.png" -resize 60x60 "store_assets/icons/ios/Icon-60x60.png"
magick "assets/images/flutter_launcher_icons.png" -resize 80x80 "store_assets/icons/ios/Icon-80x80.png"
magick "assets/images/flutter_launcher_icons.png" -resize 87x87 "store_assets/icons/ios/Icon-87x87.png"
magick "assets/images/flutter_launcher_icons.png" -resize 120x120 "store_assets/icons/ios/Icon-120x120.png"
magick "assets/images/flutter_launcher_icons.png" -resize 180x180 "store_assets/icons/ios/Icon-180x180.png"
magick "assets/images/flutter_launcher_icons.png" -resize 1024x1024 "store_assets/icons/ios/Icon-1024x1024.png"

echo "📸 創建螢幕截圖模板..."

# 創建螢幕截圖邊框模板
magick -size 1290x2796 xc:white \
    -fill "$ROYAL_INDIGO" -draw "rectangle 0,0 1289,100" \
    -fill "$ROYAL_INDIGO" -draw "rectangle 0,2695 1289,2795" \
    -font "Arial-Bold" -pointsize 24 -fill white \
    -gravity north -annotate +0+40 "星真占星 AstReal" \
    "store_assets/screenshots/templates/iphone_frame_template.png"

# 創建 iPad 螢幕截圖模板
magick -size 2048x2732 xc:white \
    -fill "$ROYAL_INDIGO" -draw "rectangle 0,0 2047,120" \
    -fill "$ROYAL_INDIGO" -draw "rectangle 0,2611 2047,2731" \
    -font "Arial-Bold" -pointsize 32 -fill white \
    -gravity north -annotate +0+50 "星真占星 AstReal - iPad" \
    "store_assets/screenshots/templates/ipad_frame_template.png"

echo "📋 創建商店描述文件..."

cat > store_assets/store_descriptions.md << 'EOF'
# 商店描述文案

## Google Play Store 描述

### 簡短描述 (80 字元以內)
專業占星分析應用，提供精準星盤解讀與AI智能分析

### 完整描述
🌟 星真占星 AstReal - 科技人眼中的占星

專為現代人設計的專業占星分析應用，結合傳統占星學與現代科技，為您提供精準的星盤分析與深入解讀。

✨ 主要功能：
• 精準星盤計算與繪製
• AI 智能星盤解讀
• 多種星盤類型支援（本命盤、合盤、推運等）
• 專業占星模式與初心者模式
• 完整的出生資料管理
• 雲端同步與備份

🎯 適合對象：
• 占星愛好者
• 專業占星師
• 對自我探索感興趣的朋友
• 想要了解人際關係的用戶

📱 特色：
• 簡潔直觀的使用界面
• 專業級的計算精度
• 豐富的占星知識庫
• 持續更新的功能

立即下載，開始您的占星探索之旅！

## Apple App Store 描述

### 副標題 (30 字元以內)
專業占星分析與AI智能解讀

### 描述 (與 Google Play 相同)
EOF

echo "✅ 進階商店資產生成完成！"
echo ""
echo "📁 生成的文件結構："
echo "   store_assets/"
echo "   ├── google_play/"
echo "   │   ├── app_icon_512.png"
echo "   │   ├── feature_graphic_professional.png"
echo "   │   ├── feature_graphic_beginner.png"
echo "   │   └── feature_graphic_modern.png"
echo "   ├── app_store/"
echo "   │   ├── app_icon_1024.png"
echo "   │   └── variations/ (多種風格圖示)"
echo "   ├── icons/"
echo "   │   ├── android/ (完整 Android 圖示集)"
echo "   │   ├── ios/ (完整 iOS 圖示集)"
echo "   │   └── adaptive/ (Android Adaptive Icons)"
echo "   ├── screenshots/"
echo "   │   └── templates/ (螢幕截圖模板)"
echo "   └── store_descriptions.md"
echo ""
echo "🚀 準備上架！下一步："
echo "1. 選擇最適合的精選圖片設計"
echo "2. 使用模板創建螢幕截圖"
echo "3. 參考 store_descriptions.md 準備商店描述"
echo "4. 上傳到 Google Play Console 和 App Store Connect"
