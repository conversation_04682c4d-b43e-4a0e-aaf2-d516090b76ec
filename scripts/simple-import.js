#!/usr/bin/env node

/**
 * 簡化的 Firebase Firestore 導入腳本
 * 
 * 此腳本使用 Firebase CLI 的模擬器或直接使用 REST API
 * 不需要服務帳戶金鑰，適合開發環境使用
 * 
 * 使用方法：
 * 1. 安裝 Firebase CLI: npm install -g firebase-tools
 * 2. 登入 Firebase: firebase login
 * 3. 設置項目: firebase use astreal-d3f70
 * 4. 運行腳本: node scripts/simple-import.js
 */

const fs = require('fs').promises;
const path = require('path');
const { execSync } = require('child_process');

// 配置
const PROJECT_ID = 'astreal-d3f70';
const COLLECTION_NAME = 'app_versions';
const DATA_DIR = path.join(__dirname, 'firebase_data');
const PLATFORMS = ['android', 'ios', 'macos', 'windows', 'web'];

/**
 * 檢查 Firebase CLI 是否可用
 */
function checkFirebaseCLI() {
  try {
    execSync('firebase --version', { stdio: 'pipe' });
    console.log('✅ Firebase CLI 可用');
    return true;
  } catch (error) {
    console.error('❌ Firebase CLI 未安裝或不可用');
    console.log('💡 請安裝 Firebase CLI:');
    console.log('   npm install -g firebase-tools');
    console.log('   firebase login');
    console.log('   firebase use astreal-d3f70');
    return false;
  }
}

/**
 * 檢查 Firebase 項目設置
 */
function checkFirebaseProject() {
  try {
    const result = execSync('firebase use', { encoding: 'utf8' });
    if (result.includes(PROJECT_ID)) {
      console.log(`✅ Firebase 項目設置正確: ${PROJECT_ID}`);
      return true;
    } else {
      console.error(`❌ Firebase 項目設置錯誤，當前項目不是 ${PROJECT_ID}`);
      console.log(`💡 請設置正確的項目: firebase use ${PROJECT_ID}`);
      return false;
    }
  } catch (error) {
    console.error('❌ 無法檢查 Firebase 項目設置');
    console.log('💡 請確保已登入並設置項目:');
    console.log('   firebase login');
    console.log(`   firebase use ${PROJECT_ID}`);
    return false;
  }
}

/**
 * 檢查數據文件
 */
async function checkDataFiles() {
  console.log('🔍 檢查數據文件...');
  
  try {
    await fs.access(DATA_DIR);
  } catch (error) {
    throw new Error(`數據目錄不存在: ${DATA_DIR}\n請先運行: dart run scripts/simple_init_versions.dart`);
  }

  const missingFiles = [];
  for (const platform of PLATFORMS) {
    const filePath = path.join(DATA_DIR, `${platform}_version.json`);
    try {
      await fs.access(filePath);
      console.log(`   ✅ ${platform}_version.json`);
    } catch (error) {
      missingFiles.push(`${platform}_version.json`);
    }
  }

  if (missingFiles.length > 0) {
    throw new Error(`缺少文件: ${missingFiles.join(', ')}\n請先運行: dart run scripts/simple_init_versions.dart`);
  }

  console.log('✅ 所有數據文件檢查完成\n');
}

/**
 * 讀取 JSON 文件
 */
async function readJsonFile(filePath) {
  try {
    const data = await fs.readFile(filePath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    throw new Error(`讀取文件失敗 ${filePath}: ${error.message}`);
  }
}

/**
 * 使用 Firebase CLI 導入數據
 */
async function importWithFirebaseCLI() {
  console.log('📱 使用 Firebase CLI 導入數據...\n');
  
  // 創建臨時的導入文件
  const importData = {};
  
  for (const platform of PLATFORMS) {
    console.log(`   正在處理 ${platform}...`);
    
    const filePath = path.join(DATA_DIR, `${platform}_version.json`);
    const versionData = await readJsonFile(filePath);
    
    // 添加到導入數據
    importData[platform] = versionData;
    console.log(`   ✅ ${platform} 數據準備完成`);
  }
  
  // 創建 Firestore 導入格式
  const firestoreData = {
    __collections__: {
      [COLLECTION_NAME]: importData
    }
  };
  
  // 寫入臨時文件
  const tempFile = path.join(DATA_DIR, 'firestore-import.json');
  await fs.writeFile(tempFile, JSON.stringify(firestoreData, null, 2));
  
  console.log('\n💾 正在導入到 Firestore...');
  
  try {
    console.log('⚠️  注意：Firebase CLI 的 firestore:import 功能可能不支持直接導入');
    console.log('💡 將改用逐個文檔設置的方式...\n');

    // 使用 firebase firestore:set 逐個設置文檔
    for (const platform of PLATFORMS) {
      console.log(`   正在設置 ${platform}...`);

      const versionData = importData[platform];
      const jsonString = JSON.stringify(versionData).replace(/"/g, '\\"');

      try {
        const command = `firebase firestore:set app_versions/${platform} "${jsonString}"`;
        execSync(command, { stdio: 'pipe' });
        console.log(`   ✅ ${platform} 設置成功`);
      } catch (error) {
        console.error(`   ❌ ${platform} 設置失敗:`, error.message);
        // 繼續處理其他平台
      }
    }

    console.log('\n✅ 數據導入完成');

    // 清理臨時文件
    await fs.unlink(tempFile);

  } catch (error) {
    console.error('❌ 導入失敗:', error.message);
    throw error;
  }
}

/**
 * 生成 Firebase CLI 命令
 */
async function generateFirebaseCommands() {
  console.log('📋 生成 Firebase CLI 命令...\n');
  
  const commands = [];
  
  // 清除現有數據的命令
  commands.push(`# 清除現有的 ${COLLECTION_NAME} 集合`);
  commands.push(`firebase firestore:delete ${COLLECTION_NAME} --recursive --force`);
  commands.push('');
  
  // 為每個平台生成設置命令
  for (const platform of PLATFORMS) {
    const filePath = path.join(DATA_DIR, `${platform}_version.json`);
    const versionData = await readJsonFile(filePath);
    
    commands.push(`# 設置 ${platform} 版本信息`);
    commands.push(`firebase firestore:set ${COLLECTION_NAME}/${platform} '${JSON.stringify(versionData, null, 0)}'`);
    commands.push('');
  }
  
  // 寫入命令文件
  const commandFile = path.join(DATA_DIR, 'firebase-commands.sh');
  await fs.writeFile(commandFile, commands.join('\n'));
  
  console.log(`✅ Firebase CLI 命令已生成: ${commandFile}`);
  console.log('\n💡 您可以手動運行這些命令：');
  console.log(`   chmod +x ${commandFile}`);
  console.log(`   ./${commandFile}`);
  
  return commandFile;
}

/**
 * 顯示手動導入說明
 */
function showManualImportInstructions() {
  console.log('\n📝 手動導入說明：\n');
  
  console.log('1. 打開 Firebase Console:');
  console.log(`   https://console.firebase.google.com/project/${PROJECT_ID}/firestore\n`);
  
  console.log('2. 創建 Collection "app_versions"\n');
  
  console.log('3. 為每個平台創建文檔：');
  for (const platform of PLATFORMS) {
    console.log(`   - 文檔 ID: ${platform}`);
    console.log(`   - 數據來源: firebase_data/${platform}_version.json`);
  }
  
  console.log('\n4. 複製對應的 JSON 內容到每個文檔中');
}

/**
 * 顯示完成信息
 */
function showCompletionInfo() {
  console.log('\n🎉 版本數據處理完成！\n');
  console.log('📝 下一步：');
  console.log('1. 在 Firebase Console 中確認數據：');
  console.log(`   https://console.firebase.google.com/project/${PROJECT_ID}/firestore/data/~2F${COLLECTION_NAME}`);
  console.log('2. 測試應用的版本檢查功能');
  console.log('3. 設置 Firestore 安全規則（如果尚未設置）');
}

/**
 * 主函數
 */
async function main() {
  console.log('🚀 簡化的 Firebase Firestore 導入腳本\n');
  
  try {
    // 檢查命令行參數
    if (process.argv.includes('--help') || process.argv.includes('-h')) {
      console.log('使用方法：');
      console.log('  node scripts/simple-import.js           # 嘗試自動導入');
      console.log('  node scripts/simple-import.js --manual  # 生成手動導入說明');
      console.log('  node scripts/simple-import.js --commands # 生成 Firebase CLI 命令');
      return;
    }
    
    // 檢查數據文件
    await checkDataFiles();
    
    if (process.argv.includes('--manual')) {
      // 手動導入模式
      showManualImportInstructions();
      return;
    }
    
    if (process.argv.includes('--commands')) {
      // 生成命令模式
      await generateFirebaseCommands();
      showCompletionInfo();
      return;
    }
    
    // 自動導入模式
    if (!checkFirebaseCLI()) {
      console.log('\n💡 由於 Firebase CLI 不可用，將生成手動導入說明...');
      showManualImportInstructions();
      return;
    }
    
    if (!checkFirebaseProject()) {
      console.log('\n💡 由於項目設置問題，將生成手動導入說明...');
      showManualImportInstructions();
      return;
    }
    
    // 嘗試自動導入
    await importWithFirebaseCLI();
    showCompletionInfo();
    
  } catch (error) {
    console.error('\n❌ 處理失敗:', error.message);
    console.log('\n💡 您可以嘗試手動導入：');
    console.log('   node scripts/simple-import.js --manual');
    process.exit(1);
  }
}

// 運行主函數
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  checkDataFiles,
  generateFirebaseCommands,
  showManualImportInstructions
};
