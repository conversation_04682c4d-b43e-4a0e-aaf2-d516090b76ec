#!/bin/bash

# Flutter Web 部署腳本 - 解決快取問題
# 使用方法:
#   ./scripts/deploy_web.sh                    # 一般部署（不觸發更新通知）
#   ./scripts/deploy_web.sh --notify-update    # 重要更新部署（觸發更新通知）
#   ./scripts/deploy_web.sh --version=1.2.0    # 指定語義化版本號
#   ./scripts/deploy_web.sh --force-cache-clear # 強制清除所有快取

set -e

echo "🚀 開始 Flutter Web 部署流程..."

# 解析命令行參數
NOTIFY_UPDATE=false
CUSTOM_VERSION=""
FORCE_CACHE_CLEAR=false

while [[ $# -gt 0 ]]; do
  case $1 in
    --notify-update)
      NOTIFY_UPDATE=true
      shift
      ;;
    --version=*)
      CUSTOM_VERSION="${1#*=}"
      shift
      ;;
    --force-cache-clear)
      FORCE_CACHE_CLEAR=true
      shift
      ;;
    *)
      echo "未知參數: $1"
      echo "使用方法: $0 [--notify-update] [--version=x.y.z] [--force-cache-clear]"
      exit 1
      ;;
  esac
done

# 生成版本號
if [ -n "$CUSTOM_VERSION" ]; then
  BUILD_VERSION="$CUSTOM_VERSION"
  echo "📦 使用自定義版本: $BUILD_VERSION"
elif [ "$NOTIFY_UPDATE" = true ]; then
  BUILD_VERSION="$(date +"%Y%m%d_%H%M%S")_update"
  echo "📦 重要更新版本: $BUILD_VERSION"
else
  BUILD_VERSION=$(date +"%Y%m%d_%H%M%S")
  echo "📦 一般建置版本: $BUILD_VERSION"
fi

# 如果是強制清除快取，在版本號後加上特殊標記
if [ "$FORCE_CACHE_CLEAR" = true ]; then
  BUILD_VERSION="${BUILD_VERSION}_force_clear"
  echo "🧹 強制清除快取模式: $BUILD_VERSION"
fi

# 清理舊的建置檔案
echo "🧹 清理舊的建置檔案..."
rm -rf build/web

# 建置 Flutter Web
echo "🔨 建置 Flutter Web..."
flutter build web --release --pwa-strategy=none

# 生成唯一的資源版本號（用於避免快取）
ASSET_VERSION=$(date +"%Y%m%d%H%M%S")
echo "📦 資源版本號: $ASSET_VERSION"

# 替換版本號
echo "🔄 更新版本資訊..."
if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS
    sed -i '' "s/{{BUILD_VERSION}}/$BUILD_VERSION/g" build/web/index.html
    sed -i '' "s/{{BUILD_VERSION}}/$BUILD_VERSION/g" build/web/simple_version_check.js
    # 如果存在舊文件也更新
    [ -f "build/web/version_check.js" ] && sed -i '' "s/{{BUILD_VERSION}}/$BUILD_VERSION/g" build/web/version_check.js
    [ -f "build/web/sw_custom.js" ] && sed -i '' "s/{{BUILD_VERSION}}/$BUILD_VERSION/g" build/web/sw_custom.js
else
    # Linux
    sed -i "s/{{BUILD_VERSION}}/$BUILD_VERSION/g" build/web/index.html
    sed -i "s/{{BUILD_VERSION}}/$BUILD_VERSION/g" build/web/simple_version_check.js
    # 如果存在舊文件也更新
    [ -f "build/web/version_check.js" ] && sed -i "s/{{BUILD_VERSION}}/$BUILD_VERSION/g" build/web/version_check.js
    [ -f "build/web/sw_custom.js" ] && sed -i "s/{{BUILD_VERSION}}/$BUILD_VERSION/g" build/web/sw_custom.js
fi

# 生成版本資訊文件
echo "📝 生成版本資訊文件..."
BUILD_TIMESTAMP=$(date -u +"%Y-%m-%dT%H:%M:%S.%3NZ")
BUILD_NUMBER=$(date +%s)

cat > build/web/version.json << EOF
{
  "version": "$BUILD_VERSION",
  "buildNumber": $BUILD_NUMBER,
  "buildTime": "$BUILD_TIMESTAMP",
  "assetVersion": "$ASSET_VERSION",
  "description": "AstReal 占星應用",
  "forceCacheClear": $( [ "$FORCE_CACHE_CLEAR" = true ] && echo "true" || echo "false" ),
  "updateType": "$( [ "$NOTIFY_UPDATE" = true ] && echo "important" || echo "normal" )",
  "updateMessage": "$( [ "$NOTIFY_UPDATE" = true ] && echo "重要更新可用，建議立即更新以獲得最佳體驗" || echo "新版本已發布，請重新載入以獲得最新功能" )",
  "features": [
    "效能優化與穩定性改善",
    "使用者介面優化",
    "錯誤修復與功能增強"
  ],
  "isActive": true
}
EOF

echo "📦 版本資訊已生成: $BUILD_VERSION (Build: $BUILD_NUMBER, Asset: $ASSET_VERSION)"

# 在主要 JS 檔案中添加版本查詢參數
echo "🔧 添加版本查詢參數..."
cd build/web

# 為所有 JS 和 CSS 檔案添加版本參數
find . -name "*.html" -type f -exec sed -i.bak "s/\\.js\"/\\.js?v=$ASSET_VERSION\"/g" {} \;
find . -name "*.html" -type f -exec sed -i.bak "s/\\.css\"/\\.css?v=$ASSET_VERSION\"/g" {} \;
find . -name "*.html" -type f -exec sed -i.bak "s/\\.dart\\.js\"/\\.dart\\.js?v=$ASSET_VERSION\"/g" {} \;

# 特別處理 main.dart.js 和 flutter_service_worker.js
if [ -f "main.dart.js" ]; then
    mv main.dart.js "main.dart.js.bak"
    sed "s/{{BUILD_VERSION}}/$BUILD_VERSION/g" main.dart.js.bak > main.dart.js
    rm main.dart.js.bak
fi

if [ -f "flutter_service_worker.js" ]; then
    # 在 Service Worker 中添加版本號，強制更新
    echo "// Version: $BUILD_VERSION" | cat - flutter_service_worker.js > temp && mv temp flutter_service_worker.js
fi

# 清理備份檔案
find . -name "*.bak" -delete

cd ../..

# 部署到 Firebase Hosting
echo "🚀 部署到 Firebase Hosting..."
firebase deploy --only hosting

echo "✅ 部署完成！"
echo "🌐 版本: $BUILD_VERSION"
echo "📝 建議用戶清除瀏覽器快取或使用無痕模式查看最新版本"

# 可選：自動開啟網站
read -p "是否要開啟網站檢查部署結果？(y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    open "https://astreal.web.app"
fi
