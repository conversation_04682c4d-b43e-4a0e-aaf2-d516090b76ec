#!/bin/bash

# 建置腳本 - 自動設定建置時間戳
# 使用方式：
# ./scripts/build_with_timestamp.sh [platform] [build_type] [additional_flags]
# 
# 範例：
# ./scripts/build_with_timestamp.sh web release
# ./scripts/build_with_timestamp.sh apk release
# ./scripts/build_with_timestamp.sh ios release

set -e  # 遇到錯誤時停止執行

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 函數：顯示使用說明
show_usage() {
    echo -e "${BLUE}建置腳本使用說明${NC}"
    echo ""
    echo "使用方式："
    echo "  $0 [platform] [build_type] [additional_flags]"
    echo ""
    echo "參數："
    echo "  platform     : web, apk, ios, macos, windows, linux"
    echo "  build_type   : debug, profile, release (預設: release)"
    echo "  additional_flags : 額外的建置標記 (可選)"
    echo ""
    echo "範例："
    echo "  $0 web release"
    echo "  $0 apk release"
    echo "  $0 ios release"
    echo "  $0 web release --dart-define=FLAVOR=production"
    echo ""
}

# 檢查參數
if [ $# -lt 1 ]; then
    echo -e "${RED}錯誤：缺少必要參數${NC}"
    show_usage
    exit 1
fi

PLATFORM=$1
BUILD_TYPE=${2:-release}
ADDITIONAL_FLAGS=${@:3}

# 驗證平台參數
case $PLATFORM in
    web|apk|ios|macos|windows|linux)
        ;;
    *)
        echo -e "${RED}錯誤：不支援的平台 '$PLATFORM'${NC}"
        show_usage
        exit 1
        ;;
esac

# 驗證建置類型
case $BUILD_TYPE in
    debug|profile|release)
        ;;
    *)
        echo -e "${RED}錯誤：不支援的建置類型 '$BUILD_TYPE'${NC}"
        show_usage
        exit 1
        ;;
esac

# 生成時間戳
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BUILD_DATE=$(date +"%Y-%m-%d %H:%M:%S")

echo -e "${BLUE}=== AstReal 建置腳本 ===${NC}"
echo -e "${YELLOW}平台：${NC}$PLATFORM"
echo -e "${YELLOW}建置類型：${NC}$BUILD_TYPE"
echo -e "${YELLOW}時間戳：${NC}$TIMESTAMP"
echo -e "${YELLOW}建置時間：${NC}$BUILD_DATE"
if [ -n "$ADDITIONAL_FLAGS" ]; then
    echo -e "${YELLOW}額外標記：${NC}$ADDITIONAL_FLAGS"
fi
echo ""

# 從 pubspec.yaml 讀取版本號
VERSION=$(grep '^version:' pubspec.yaml | sed 's/version: //' | sed 's/+.*//')
if [ -z "$VERSION" ]; then
    echo -e "${RED}錯誤：無法從 pubspec.yaml 讀取版本號${NC}"
    exit 1
fi

echo -e "${YELLOW}應用版本：${NC}$VERSION"

# 根據建置類型設定建置號碼
case $BUILD_TYPE in
    debug)
        BUILD_NUMBER="${TIMESTAMP}_debug"
        ;;
    profile)
        BUILD_NUMBER="${TIMESTAMP}_profile"
        ;;
    release)
        BUILD_NUMBER="${TIMESTAMP}"
        ;;
esac

# 檢查是否為強制更新版本（可以通過環境變數設定）
if [ "$FORCE_UPDATE" = "true" ]; then
    BUILD_NUMBER="${BUILD_NUMBER}_force_update"
    echo -e "${YELLOW}強制更新：${NC}是"
fi

# 檢查是否為測試版本
if [ "$BETA" = "true" ]; then
    BUILD_NUMBER="${BUILD_NUMBER}_beta"
    echo -e "${YELLOW}測試版本：${NC}是"
elif [ "$ALPHA" = "true" ]; then
    BUILD_NUMBER="${BUILD_NUMBER}_alpha"
    echo -e "${YELLOW}內測版本：${NC}是"
fi

echo -e "${YELLOW}建置號碼：${NC}$BUILD_NUMBER"
echo ""

# 生成編譯時間戳（用於 dart define）
COMPILATION_TIMESTAMP=$(date +"%Y%m%d%H%M%S")

# 建置命令
case $PLATFORM in
    web)
        BUILD_CMD="flutter build web --$BUILD_TYPE --build-name=$VERSION --build-number=$BUILD_NUMBER --dart-define=COMPILATION_TIMESTAMP=$COMPILATION_TIMESTAMP"
        ;;
    apk)
        BUILD_CMD="flutter build apk --$BUILD_TYPE --build-name=$VERSION --build-number=$BUILD_NUMBER --dart-define=COMPILATION_TIMESTAMP=$COMPILATION_TIMESTAMP"
        ;;
    ios)
        BUILD_CMD="flutter build ios --$BUILD_TYPE --build-name=$VERSION --build-number=$BUILD_NUMBER --dart-define=COMPILATION_TIMESTAMP=$COMPILATION_TIMESTAMP"
        ;;
    macos)
        BUILD_CMD="flutter build macos --$BUILD_TYPE --build-name=$VERSION --build-number=$BUILD_NUMBER --dart-define=COMPILATION_TIMESTAMP=$COMPILATION_TIMESTAMP"
        ;;
    windows)
        BUILD_CMD="flutter build windows --$BUILD_TYPE --build-name=$VERSION --build-number=$BUILD_NUMBER --dart-define=COMPILATION_TIMESTAMP=$COMPILATION_TIMESTAMP"
        ;;
    linux)
        BUILD_CMD="flutter build linux --$BUILD_TYPE --build-name=$VERSION --build-number=$BUILD_NUMBER --dart-define=COMPILATION_TIMESTAMP=$COMPILATION_TIMESTAMP"
        ;;
esac

# 加入額外標記
if [ -n "$ADDITIONAL_FLAGS" ]; then
    BUILD_CMD="$BUILD_CMD $ADDITIONAL_FLAGS"
fi

echo -e "${BLUE}開始建置...${NC}"
echo -e "${YELLOW}執行命令：${NC}$BUILD_CMD"
echo ""

# 執行建置
if eval $BUILD_CMD; then
    echo ""
    echo -e "${GREEN}✅ 建置成功！${NC}"
    echo -e "${YELLOW}平台：${NC}$PLATFORM"
    echo -e "${YELLOW}版本：${NC}$VERSION"
    echo -e "${YELLOW}建置號碼：${NC}$BUILD_NUMBER"
    echo -e "${YELLOW}建置時間：${NC}$BUILD_DATE"
    
    # 顯示輸出檔案位置
    case $PLATFORM in
        web)
            echo -e "${YELLOW}輸出位置：${NC}build/web/"
            ;;
        apk)
            echo -e "${YELLOW}輸出位置：${NC}build/app/outputs/flutter-apk/"
            ;;
        ios)
            echo -e "${YELLOW}輸出位置：${NC}build/ios/iphoneos/"
            ;;
        macos)
            echo -e "${YELLOW}輸出位置：${NC}build/macos/Build/Products/"
            ;;
        windows)
            echo -e "${YELLOW}輸出位置：${NC}build/windows/runner/Release/"
            ;;
        linux)
            echo -e "${YELLOW}輸出位置：${NC}build/linux/x64/release/bundle/"
            ;;
    esac
    
else
    echo ""
    echo -e "${RED}❌ 建置失敗！${NC}"
    exit 1
fi
