{"name": "astreal-firebase-scripts", "version": "1.0.0", "description": "Firebase 版本信息管理腳本", "main": "import-to-firestore.js", "scripts": {"import": "node import-selector.js", "import:admin": "node import-to-firestore.js", "import:cli": "node simple-import.js", "import:rest": "node rest-import.js", "import:manual": "node simple-import.js --manual", "import:check": "node import-selector.js --check", "import:help": "node import-to-firestore.js --help", "generate": "dart run simple_init_versions.dart", "generate:preview": "dart run simple_init_versions.dart --dry-run", "setup": "npm install && dart run simple_init_versions.dart", "deploy": "npm run generate && npm run import", "check": "node import-selector.js --check"}, "keywords": ["firebase", "firestore", "version-control", "astreal"], "author": "Astreal Team", "license": "MIT", "dependencies": {"firebase-admin": "^12.0.0"}, "engines": {"node": ">=16.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-org/astreal.git"}, "bugs": {"url": "https://github.com/your-org/astreal/issues"}, "homepage": "https://astreal.app"}