#!/bin/bash

# 部署驗證腳本 - 檢查 Web 版本是否正確部署
# 使用方法: ./scripts/verify_deployment.sh [expected_version]

set -e

EXPECTED_VERSION="$1"
SITE_URL="https://astreal.web.app"
MAX_RETRIES=10
RETRY_DELAY=5

echo "🔍 開始驗證部署狀態..."

if [ -n "$EXPECTED_VERSION" ]; then
    echo "📦 預期版本: $EXPECTED_VERSION"
fi

# 檢查網站是否可訪問
echo "🌐 檢查網站可訪問性..."
if ! curl -s --head "$SITE_URL" | head -n 1 | grep -q "200 OK"; then
    echo "❌ 網站無法訪問: $SITE_URL"
    exit 1
fi
echo "✅ 網站可正常訪問"

# 檢查版本文件
echo "📄 檢查版本文件..."
for i in $(seq 1 $MAX_RETRIES); do
    echo "   嘗試 $i/$MAX_RETRIES..."
    
    # 使用強快取破壞策略
    CACHE_BUSTER=$(date +%s)_$(openssl rand -hex 4)
    VERSION_URL="$SITE_URL/version.json?_=$CACHE_BUSTER"
    
    # 獲取版本資訊
    VERSION_RESPONSE=$(curl -s -H "Cache-Control: no-cache" -H "Pragma: no-cache" "$VERSION_URL" 2>/dev/null || echo "")
    
    if [ -n "$VERSION_RESPONSE" ]; then
        # 解析版本號
        DEPLOYED_VERSION=$(echo "$VERSION_RESPONSE" | grep -o '"version":"[^"]*' | cut -d'"' -f4 2>/dev/null || echo "")
        BUILD_TIME=$(echo "$VERSION_RESPONSE" | grep -o '"buildTime":"[^"]*' | cut -d'"' -f4 2>/dev/null || echo "")
        UPDATE_TYPE=$(echo "$VERSION_RESPONSE" | grep -o '"updateType":"[^"]*' | cut -d'"' -f4 2>/dev/null || echo "")
        
        if [ -n "$DEPLOYED_VERSION" ]; then
            echo "✅ 版本文件讀取成功"
            echo "   📦 部署版本: $DEPLOYED_VERSION"
            echo "   🕐 建置時間: $BUILD_TIME"
            echo "   🔄 更新類型: $UPDATE_TYPE"
            
            # 如果指定了預期版本，進行比較
            if [ -n "$EXPECTED_VERSION" ]; then
                if [ "$DEPLOYED_VERSION" = "$EXPECTED_VERSION" ]; then
                    echo "✅ 版本驗證成功！部署版本與預期版本一致"
                else
                    echo "⚠️  版本不符：部署版本 ($DEPLOYED_VERSION) ≠ 預期版本 ($EXPECTED_VERSION)"
                    if [ $i -lt $MAX_RETRIES ]; then
                        echo "   等待 $RETRY_DELAY 秒後重試..."
                        sleep $RETRY_DELAY
                        continue
                    else
                        echo "❌ 版本驗證失敗：達到最大重試次數"
                        exit 1
                    fi
                fi
            fi
            break
        fi
    fi
    
    if [ $i -lt $MAX_RETRIES ]; then
        echo "   版本文件讀取失敗，等待 $RETRY_DELAY 秒後重試..."
        sleep $RETRY_DELAY
    else
        echo "❌ 版本文件驗證失敗：達到最大重試次數"
        exit 1
    fi
done

# 檢查關鍵文件
echo "📁 檢查關鍵文件..."
CRITICAL_FILES=(
    "index.html"
    "main.dart.js"
    "flutter_service_worker.js"
    "simple_version_check.js"
    "manifest.json"
)

for file in "${CRITICAL_FILES[@]}"; do
    FILE_URL="$SITE_URL/$file?_=$(date +%s)"
    if curl -s --head "$FILE_URL" | head -n 1 | grep -q "200 OK"; then
        echo "   ✅ $file"
    else
        echo "   ❌ $file (無法訪問)"
    fi
done

# 檢查快取標頭
echo "🗂️  檢查快取設定..."
VERSION_HEADERS=$(curl -s -I "$SITE_URL/version.json?_=$(date +%s)" 2>/dev/null || echo "")
if echo "$VERSION_HEADERS" | grep -q "no-cache"; then
    echo "   ✅ version.json 快取設定正確"
else
    echo "   ⚠️  version.json 快取設定可能有問題"
fi

JS_HEADERS=$(curl -s -I "$SITE_URL/simple_version_check.js?_=$(date +%s)" 2>/dev/null || echo "")
if echo "$JS_HEADERS" | grep -q "no-cache"; then
    echo "   ✅ JavaScript 檔案快取設定正確"
else
    echo "   ⚠️  JavaScript 檔案快取設定可能有問題"
fi

echo ""
echo "🎉 部署驗證完成！"
echo "🌐 網站: $SITE_URL"
echo "📦 版本: $DEPLOYED_VERSION"
echo ""
echo "💡 建議用戶操作："
echo "   1. 按 Ctrl+Shift+R (或 Cmd+Shift+R) 強制重新載入"
echo "   2. 使用無痕模式開啟網站"
echo "   3. 清除瀏覽器快取"
echo "   4. 等待版本檢查器自動提示更新"
