<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>版本檢查邏輯測試</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-case {
            background: #f5f5f5;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .result {
            font-weight: bold;
            margin-top: 10px;
        }
        .pass { color: #28a745; }
        .fail { color: #dc3545; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>版本檢查邏輯測試</h1>
    
    <div class="test-case">
        <h3>測試案例</h3>
        <button onclick="runAllTests()">執行所有測試</button>
        <button onclick="clearLog()">清除日誌</button>
        <button onclick="clearStorage()">清除 localStorage</button>
    </div>
    
    <div id="log" class="log"></div>
    
    <script>
        // 複製版本檢查邏輯（簡化版）
        function shouldShowNotification(serverVersion, currentVersion, lastNotifiedVersion) {
            // 如果版本相同，不需要通知
            if (serverVersion === currentVersion) {
                return false;
            }
            
            // 如果已經為這個版本通知過，不再通知
            if (lastNotifiedVersion === serverVersion) {
                return false;
            }
            
            // 檢查版本是否有意義的更新（避免僅時間戳變化的通知）
            // 如果服務器版本包含特殊標記（如 'update' 或版本號格式），才顯示通知
            if (serverVersion && serverVersion.includes('update')) {
                return true;
            }
            
            // 檢查是否為語義化版本號（x.y.z 格式）
            const semanticVersionRegex = /^\d+\.\d+\.\d+/;
            if (semanticVersionRegex.test(serverVersion) && semanticVersionRegex.test(currentVersion)) {
                return compareSemanticVersions(serverVersion, currentVersion) > 0;
            }
            
            // 對於時間戳版本，檢查時間差是否超過閾值（避免頻繁的小更新通知）
            const timestampRegex = /^\d{8}_\d{6}$/;
            if (timestampRegex.test(serverVersion) && timestampRegex.test(currentVersion)) {
                return shouldNotifyForTimestampVersion(serverVersion, currentVersion);
            }
            
            return false;
        }
        
        // 比較語義化版本號
        function compareSemanticVersions(version1, version2) {
            const v1Parts = version1.split('.').map(Number);
            const v2Parts = version2.split('.').map(Number);
            
            for (let i = 0; i < Math.max(v1Parts.length, v2Parts.length); i++) {
                const v1Part = v1Parts[i] || 0;
                const v2Part = v2Parts[i] || 0;
                
                if (v1Part > v2Part) return 1;
                if (v1Part < v2Part) return -1;
            }
            
            return 0;
        }
        
        // 判斷時間戳版本是否需要通知
        function shouldNotifyForTimestampVersion(serverVersion, currentVersion) {
            try {
                const serverDate = parseTimestampVersion(serverVersion);
                const currentDate = parseTimestampVersion(currentVersion);
                
                if (!serverDate || !currentDate) return false;
                
                // 只有當服務器版本比當前版本新超過 1 小時才通知
                const timeDiff = serverDate.getTime() - currentDate.getTime();
                return timeDiff > 3600000; // 1 小時 = 3600000 毫秒
            } catch (e) {
                console.log('時間戳版本解析錯誤:', e);
                return false;
            }
        }
        
        // 解析時間戳版本號
        function parseTimestampVersion(version) {
            const match = version.match(/^(\d{4})(\d{2})(\d{2})_(\d{2})(\d{2})(\d{2})$/);
            if (!match) return null;
            
            const [, year, month, day, hour, minute, second] = match;
            return new Date(
                parseInt(year),
                parseInt(month) - 1, // 月份從 0 開始
                parseInt(day),
                parseInt(hour),
                parseInt(minute),
                parseInt(second)
            );
        }
        
        // 測試案例
        const testCases = [
            {
                name: '相同版本不應通知',
                serverVersion: '20250119_143000',
                currentVersion: '20250119_143000',
                lastNotified: null,
                expected: false
            },
            {
                name: '已通知過的版本不應再通知',
                serverVersion: '20250119_143000_update',
                currentVersion: '20250119_140000',
                lastNotified: '20250119_143000_update',
                expected: false
            },
            {
                name: '包含 update 標記應通知',
                serverVersion: '20250119_143000_update',
                currentVersion: '20250119_140000',
                lastNotified: null,
                expected: true
            },
            {
                name: '語義化版本升級應通知',
                serverVersion: '1.2.0',
                currentVersion: '1.1.0',
                lastNotified: null,
                expected: true
            },
            {
                name: '語義化版本降級不應通知',
                serverVersion: '1.1.0',
                currentVersion: '1.2.0',
                lastNotified: null,
                expected: false
            },
            {
                name: '時間戳版本差距小於1小時不應通知',
                serverVersion: '20250119_143000',
                currentVersion: '20250119_142000',
                lastNotified: null,
                expected: false
            },
            {
                name: '時間戳版本差距大於1小時應通知',
                serverVersion: '20250119_153000',
                currentVersion: '20250119_142000',
                lastNotified: null,
                expected: true
            }
        ];
        
        function log(message) {
            const logElement = document.getElementById('log');
            logElement.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('log').textContent = '';
        }
        
        function clearStorage() {
            localStorage.removeItem('lastNotifiedVersion');
            log('已清除 localStorage');
        }
        
        function runAllTests() {
            log('開始執行版本檢查邏輯測試...\n');
            
            let passCount = 0;
            let failCount = 0;
            
            testCases.forEach((testCase, index) => {
                const result = shouldShowNotification(
                    testCase.serverVersion,
                    testCase.currentVersion,
                    testCase.lastNotified
                );
                
                const passed = result === testCase.expected;
                if (passed) {
                    passCount++;
                } else {
                    failCount++;
                }
                
                log(`測試 ${index + 1}: ${testCase.name}`);
                log(`  服務器版本: ${testCase.serverVersion}`);
                log(`  當前版本: ${testCase.currentVersion}`);
                log(`  上次通知: ${testCase.lastNotified || '無'}`);
                log(`  預期結果: ${testCase.expected}`);
                log(`  實際結果: ${result}`);
                log(`  測試結果: ${passed ? '✅ 通過' : '❌ 失敗'}\n`);
            });
            
            log(`測試完成！通過: ${passCount}, 失敗: ${failCount}`);
            
            if (failCount === 0) {
                log('🎉 所有測試都通過了！');
            } else {
                log('⚠️ 有測試失敗，請檢查邏輯');
            }
        }
        
        // 頁面載入時自動執行測試
        window.addEventListener('load', () => {
            log('版本檢查邏輯測試頁面已載入');
            log('點擊「執行所有測試」按鈕開始測試\n');
        });
    </script>
</body>
</html>
