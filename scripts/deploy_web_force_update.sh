#!/bin/bash

# Flutter Web 強制更新部署腳本
# 專門用於解決瀏覽器快取問題，確保用戶能立即看到新版本

set -e

# 設定 Flutter PATH
export PATH="/Users/<USER>/development/flutter/bin:$PATH"

echo "🔥 開始強制更新部署流程..."

# 檢查是否有未提交的更改
if ! git diff-index --quiet HEAD --; then
    echo "⚠️  警告：有未提交的更改"
    read -p "是否繼續部署？(y/n): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "❌ 部署已取消"
        exit 1
    fi
fi

# 生成強制更新版本號
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BUILD_VERSION="${TIMESTAMP}_force_update"
ASSET_VERSION=$(date +"%Y%m%d%H%M%S")

echo "📦 強制更新版本: $BUILD_VERSION"
echo "📦 資源版本: $ASSET_VERSION"

# 清理舊的建置檔案
echo "🧹 清理舊的建置檔案..."
rm -rf build/web

# 建置 Flutter Web
echo "🔨 建置 Flutter Web..."
flutter build web --release --pwa-strategy=none

# 複製增強版 Service Worker
echo "🔧 設置增強版 Service Worker..."
cp web/sw_enhanced.js build/web/

# 替換版本號
echo "🔄 更新版本資訊..."
if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS
    sed -i '' "s/{{BUILD_VERSION}}/$BUILD_VERSION/g" build/web/index.html
    sed -i '' "s/{{BUILD_VERSION}}/$BUILD_VERSION/g" build/web/simple_version_check.js
    sed -i '' "s/{{BUILD_VERSION}}/$BUILD_VERSION/g" build/web/sw_enhanced.js
    [ -f "build/web/version_check.js" ] && sed -i '' "s/{{BUILD_VERSION}}/$BUILD_VERSION/g" build/web/version_check.js
    [ -f "build/web/sw_custom.js" ] && sed -i '' "s/{{BUILD_VERSION}}/$BUILD_VERSION/g" build/web/sw_custom.js
else
    # Linux
    sed -i "s/{{BUILD_VERSION}}/$BUILD_VERSION/g" build/web/index.html
    sed -i "s/{{BUILD_VERSION}}/$BUILD_VERSION/g" build/web/simple_version_check.js
    sed -i "s/{{BUILD_VERSION}}/$BUILD_VERSION/g" build/web/sw_enhanced.js
    [ -f "build/web/version_check.js" ] && sed -i "s/{{BUILD_VERSION}}/$BUILD_VERSION/g" build/web/version_check.js
    [ -f "build/web/sw_custom.js" ] && sed -i "s/{{BUILD_VERSION}}/$BUILD_VERSION/g" build/web/sw_custom.js
fi

# 生成強制更新版本資訊文件
echo "📝 生成強制更新版本資訊文件..."
cat > build/web/version.json << EOF
{
  "version": "$BUILD_VERSION",
  "buildTime": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")",
  "assetVersion": "$ASSET_VERSION",
  "description": "AstReal 應用 - 強制更新",
  "forceCacheClear": true,
  "updateType": "important",
  "forceUpdate": true,
  "updateMessage": "重要更新已發布，請立即重新載入以獲得最新功能和修復。"
}
EOF

# 在主要 JS 檔案中添加版本查詢參數
echo "🔧 添加版本查詢參數..."
cd build/web

# 為所有 JS 和 CSS 檔案添加版本參數
find . -name "*.html" -type f -exec sed -i.bak "s/\\.js\"/\\.js?v=$ASSET_VERSION\"/g" {} \;
find . -name "*.html" -type f -exec sed -i.bak "s/\\.css\"/\\.css?v=$ASSET_VERSION\"/g" {} \;
find . -name "*.html" -type f -exec sed -i.bak "s/\\.dart\\.js\"/\\.dart\\.js?v=$ASSET_VERSION\"/g" {} \;

# 特別處理關鍵檔案
if [ -f "main.dart.js" ]; then
    # 在 main.dart.js 開頭添加版本註釋
    echo "// Version: $BUILD_VERSION - $(date)" | cat - main.dart.js > temp && mv temp main.dart.js
fi

if [ -f "flutter_service_worker.js" ]; then
    # 在 Service Worker 中添加版本號和強制更新標記
    echo "// Force Update Version: $BUILD_VERSION - $(date)" | cat - flutter_service_worker.js > temp && mv temp flutter_service_worker.js
fi

# 創建快取破壞文件
echo "🔨 創建快取破壞文件..."
echo "/* Cache Buster: $ASSET_VERSION */" > cache_buster.css
echo "// Cache Buster: $ASSET_VERSION" > cache_buster.js

# 清理備份檔案
find . -name "*.bak" -delete

cd ../..

# 部署到 Firebase Hosting
echo "🚀 部署到 Firebase Hosting..."
firebase deploy --only hosting:astreal

# 等待部署完成並驗證
echo "⏳ 等待部署生效..."
sleep 10

# 驗證部署是否成功
echo "🔍 驗證部署狀態..."
DEPLOYED_VERSION=$(curl -s "https://astreal.web.app/version.json?_=$(date +%s)" | grep -o '"version":"[^"]*' | cut -d'"' -f4)
if [ "$DEPLOYED_VERSION" = "$BUILD_VERSION" ]; then
    echo "✅ 部署驗證成功！線上版本: $DEPLOYED_VERSION"
else
    echo "⚠️  部署驗證警告：線上版本 ($DEPLOYED_VERSION) 與預期版本 ($BUILD_VERSION) 不符"
    echo "   這可能是 CDN 快取延遲造成，請稍後再次檢查"
fi

echo ""
echo "✅ 強制更新部署完成！"
echo "🔥 版本: $BUILD_VERSION"
echo "📦 資源版本: $ASSET_VERSION"
echo "🌐 網站: https://astreal.web.app"
echo ""
echo "📋 用戶操作建議："
echo "   1. 清除瀏覽器快取 (Ctrl+Shift+R 或 Cmd+Shift+R)"
echo "   2. 使用無痕模式開啟網站"
echo "   3. 等待版本檢查器自動提示更新"
echo ""

# 可選：自動開啟網站並顯示開發者工具
read -p "是否要開啟網站並清除快取？(y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🌐 正在開啟網站..."
    
    # 根據作業系統開啟瀏覽器
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS - 開啟 Chrome 並自動開啟開發者工具
        open -a "Google Chrome" "https://astreal.web.app?force_refresh=$ASSET_VERSION" --args --auto-open-devtools-for-tabs
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        # Linux
        google-chrome "https://astreal.web.app?force_refresh=$ASSET_VERSION" --auto-open-devtools-for-tabs 2>/dev/null || \
        firefox "https://astreal.web.app?force_refresh=$ASSET_VERSION" 2>/dev/null || \
        xdg-open "https://astreal.web.app?force_refresh=$ASSET_VERSION"
    else
        echo "請手動開啟: https://astreal.web.app?force_refresh=$ASSET_VERSION"
    fi
fi

echo ""
echo "🎉 部署完成！用戶將在 3 分鐘內收到更新通知。"
