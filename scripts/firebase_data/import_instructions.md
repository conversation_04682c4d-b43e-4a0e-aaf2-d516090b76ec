# Firebase Firestore 導入說明

## 自動導入（推薦）

如果您有 Firebase CLI 和適當的權限，可以使用以下命令自動導入：

```bash
# 安裝 Firebase CLI（如果尚未安裝）
npm install -g firebase-tools

# 登入 Firebase
firebase login

# 設置項目
firebase use astreal-d3f70

# 導入數據（需要先安裝 firestore-import 工具）
# npm install -g firestore-import
# firestore-import --accountCredentials path/to/service-account.json --backupFile all_versions.json
```

## 手動導入

1. 打開 [Firebase Console](https://console.firebase.google.com/project/astreal-d3f70/firestore)
2. 進入 Firestore Database
3. 創建 Collection "app_versions"
4. 為每個平台創建文檔：

### Android 平台
- 文檔 ID: `android`
- 複製 `android_version.json` 的內容

### iOS 平台
- 文檔 ID: `ios`
- 複製 `ios_version.json` 的內容

### macOS 平台
- 文檔 ID: `macos`
- 複製 `macos_version.json` 的內容

### Windows 平台
- 文檔 ID: `windows`
- 複製 `windows_version.json` 的內容

### Web 平台
- 文檔 ID: `web`
- 複製 `web_version.json` 的內容

## 驗證導入

導入完成後，您應該在 Firestore 中看到：

```
app_versions/
├── android/
├── ios/
├── macos/
├── windows/
└── web/
```

每個文檔都應該包含完整的版本信息字段。

## 測試

1. 運行您的 Flutter 應用
2. 檢查應用啟動時是否正常檢查版本
3. 在設定頁面測試手動檢查更新功能

## 更新版本信息

要發布新版本時，只需更新對應平台文檔中的字段：

- **可選更新**: 更新 `version` 和 `buildNumber`，保持 `minRequiredVersion` 不變
- **強制更新**: 更新所有版本字段，並設置 `forceUpdate: true`
