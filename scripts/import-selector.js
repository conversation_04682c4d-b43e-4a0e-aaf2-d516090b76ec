#!/usr/bin/env node

/**
 * Firebase Firestore 導入腳本選擇器
 * 
 * 此腳本會自動檢測環境並選擇最適合的導入方式
 * 
 * 使用方法：
 * node scripts/import-selector.js
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

/**
 * 檢查命令是否可用
 */
function isCommandAvailable(command) {
  try {
    execSync(`${command} --version`, { stdio: 'pipe' });
    return true;
  } catch (error) {
    return false;
  }
}

/**
 * 檢查文件是否存在
 */
function fileExists(filePath) {
  try {
    return fs.existsSync(filePath);
  } catch (error) {
    return false;
  }
}

/**
 * 檢查環境變量
 */
function checkEnvironmentVariables() {
  const hasApiKey = process.env.FIREBASE_API_KEY && process.env.FIREBASE_API_KEY !== 'your-api-key';
  const hasServiceAccount = process.env.GOOGLE_APPLICATION_CREDENTIALS && fileExists(process.env.GOOGLE_APPLICATION_CREDENTIALS);
  
  return { hasApiKey, hasServiceAccount };
}

/**
 * 檢查 Firebase CLI
 */
function checkFirebaseCLI() {
  if (!isCommandAvailable('firebase')) {
    return { available: false, loggedIn: false, projectSet: false };
  }
  
  try {
    // 檢查是否已登入
    execSync('firebase projects:list', { stdio: 'pipe' });
    const loggedIn = true;
    
    // 檢查項目設置
    const result = execSync('firebase use', { encoding: 'utf8' });
    const projectSet = result.includes('astreal-d3f70');
    
    return { available: true, loggedIn, projectSet };
  } catch (error) {
    return { available: true, loggedIn: false, projectSet: false };
  }
}

/**
 * 檢查 Node.js 依賴
 */
function checkNodeDependencies() {
  const packageJsonPath = path.join(__dirname, 'package.json');
  const nodeModulesPath = path.join(__dirname, 'node_modules');
  
  const hasPackageJson = fileExists(packageJsonPath);
  const hasNodeModules = fileExists(nodeModulesPath);
  
  let hasFirebaseAdmin = false;
  if (hasNodeModules) {
    try {
      require.resolve('firebase-admin');
      hasFirebaseAdmin = true;
    } catch (error) {
      hasFirebaseAdmin = false;
    }
  }
  
  return { hasPackageJson, hasNodeModules, hasFirebaseAdmin };
}

/**
 * 檢查數據文件
 */
function checkDataFiles() {
  const dataDir = path.join(__dirname, 'firebase_data');
  const platforms = ['android', 'ios', 'macos', 'windows', 'web'];
  
  if (!fileExists(dataDir)) {
    return { hasDataDir: false, missingFiles: platforms.map(p => `${p}_version.json`) };
  }
  
  const missingFiles = [];
  for (const platform of platforms) {
    const filePath = path.join(dataDir, `${platform}_version.json`);
    if (!fileExists(filePath)) {
      missingFiles.push(`${platform}_version.json`);
    }
  }
  
  return { hasDataDir: true, missingFiles };
}

/**
 * 顯示環境檢查結果
 */
function showEnvironmentStatus() {
  console.log('🔍 環境檢查結果：\n');
  
  // 檢查數據文件
  const dataStatus = checkDataFiles();
  console.log('📁 數據文件：');
  if (dataStatus.hasDataDir && dataStatus.missingFiles.length === 0) {
    console.log('   ✅ 所有數據文件都存在');
  } else if (!dataStatus.hasDataDir) {
    console.log('   ❌ 數據目錄不存在');
    console.log('   💡 請運行: dart run scripts/simple_init_versions.dart');
  } else {
    console.log(`   ⚠️  缺少文件: ${dataStatus.missingFiles.join(', ')}`);
    console.log('   💡 請運行: dart run scripts/simple_init_versions.dart');
  }
  
  // 檢查 Firebase CLI
  const cliStatus = checkFirebaseCLI();
  console.log('\n🔧 Firebase CLI：');
  if (cliStatus.available) {
    console.log('   ✅ Firebase CLI 已安裝');
    if (cliStatus.loggedIn) {
      console.log('   ✅ 已登入 Firebase');
      if (cliStatus.projectSet) {
        console.log('   ✅ 項目設置正確 (astreal-d3f70)');
      } else {
        console.log('   ⚠️  項目設置不正確');
        console.log('   💡 請運行: firebase use astreal-d3f70');
      }
    } else {
      console.log('   ⚠️  未登入 Firebase');
      console.log('   💡 請運行: firebase login');
    }
  } else {
    console.log('   ❌ Firebase CLI 未安裝');
    console.log('   💡 請運行: npm install -g firebase-tools');
  }
  
  // 檢查環境變量
  const envStatus = checkEnvironmentVariables();
  console.log('\n🔑 環境變量：');
  if (envStatus.hasApiKey) {
    console.log('   ✅ FIREBASE_API_KEY 已設置');
  } else {
    console.log('   ⚠️  FIREBASE_API_KEY 未設置或無效');
  }
  if (envStatus.hasServiceAccount) {
    console.log('   ✅ GOOGLE_APPLICATION_CREDENTIALS 已設置');
  } else {
    console.log('   ⚠️  GOOGLE_APPLICATION_CREDENTIALS 未設置或文件不存在');
  }
  
  // 檢查 Node.js 依賴
  const nodeStatus = checkNodeDependencies();
  console.log('\n📦 Node.js 依賴：');
  if (nodeStatus.hasFirebaseAdmin) {
    console.log('   ✅ firebase-admin 已安裝');
  } else {
    console.log('   ⚠️  firebase-admin 未安裝');
    console.log('   💡 請運行: npm install firebase-admin');
  }
  
  return { dataStatus, cliStatus, envStatus, nodeStatus };
}

/**
 * 推薦導入方式
 */
function recommendImportMethod(status) {
  console.log('\n🎯 推薦的導入方式：\n');
  
  const { dataStatus, cliStatus, envStatus, nodeStatus } = status;
  
  // 檢查數據文件
  if (!dataStatus.hasDataDir || dataStatus.missingFiles.length > 0) {
    console.log('❌ 無法導入：缺少數據文件');
    console.log('💡 請先生成數據文件：');
    console.log('   dart run scripts/simple_init_versions.dart');
    return null;
  }
  
  // 方式 1: Firebase Admin SDK（最可靠）
  if (nodeStatus.hasFirebaseAdmin && envStatus.hasServiceAccount) {
    console.log('🥇 推薦方式 1: Firebase Admin SDK');
    console.log('   ✅ 最可靠的方式');
    console.log('   ✅ 支持批次操作');
    console.log('   ✅ 完整的錯誤處理');
    console.log('   📝 運行命令: node scripts/import-to-firestore.js');
    return 'admin-sdk';
  }
  
  // 方式 2: Firebase CLI（簡單易用）
  if (cliStatus.available && cliStatus.loggedIn && cliStatus.projectSet) {
    console.log('🥈 推薦方式 2: Firebase CLI');
    console.log('   ✅ 簡單易用');
    console.log('   ✅ 官方工具');
    console.log('   ⚠️  需要手動確認');
    console.log('   📝 運行命令: node scripts/simple-import.js');
    return 'firebase-cli';
  }
  
  // 方式 3: REST API（無額外依賴）
  if (envStatus.hasApiKey) {
    console.log('🥉 推薦方式 3: REST API');
    console.log('   ✅ 無需額外依賴');
    console.log('   ✅ 直接使用 Web API');
    console.log('   ⚠️  功能相對簡單');
    console.log('   📝 運行命令: node scripts/rest-import.js');
    return 'rest-api';
  }
  
  // 方式 4: 手動導入（最後選擇）
  console.log('🔧 備選方式: 手動導入');
  console.log('   ✅ 總是可用');
  console.log('   ⚠️  需要手動操作');
  console.log('   📝 運行命令: node scripts/simple-import.js --manual');
  return 'manual';
}

/**
 * 執行推薦的導入方式
 */
function executeRecommendedMethod(method) {
  console.log('\n🚀 執行導入...\n');
  
  try {
    switch (method) {
      case 'admin-sdk':
        execSync('node scripts/import-to-firestore.js', { stdio: 'inherit' });
        break;
      case 'firebase-cli':
        execSync('node scripts/simple-import.js', { stdio: 'inherit' });
        break;
      case 'rest-api':
        execSync('node scripts/rest-import.js', { stdio: 'inherit' });
        break;
      case 'manual':
        execSync('node scripts/simple-import.js --manual', { stdio: 'inherit' });
        break;
      default:
        console.log('❌ 未知的導入方式');
        return false;
    }
    return true;
  } catch (error) {
    console.error('❌ 導入失敗:', error.message);
    return false;
  }
}

/**
 * 主函數
 */
function main() {
  console.log('🚀 Firebase Firestore 導入腳本選擇器\n');
  
  // 檢查命令行參數
  if (process.argv.includes('--help') || process.argv.includes('-h')) {
    console.log('使用方法：');
    console.log('  node scripts/import-selector.js           # 自動選擇並執行導入');
    console.log('  node scripts/import-selector.js --check   # 只檢查環境，不執行導入');
    console.log('  node scripts/import-selector.js --help    # 顯示幫助');
    return;
  }
  
  // 檢查環境
  const status = showEnvironmentStatus();
  
  // 推薦導入方式
  const recommendedMethod = recommendImportMethod(status);
  
  if (!recommendedMethod) {
    console.log('\n❌ 無法進行導入，請先解決上述問題');
    process.exit(1);
  }
  
  // 如果只是檢查環境，不執行導入
  if (process.argv.includes('--check')) {
    console.log('\n✅ 環境檢查完成');
    return;
  }
  
  // 詢問是否執行
  const readline = require('readline');
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });
  
  rl.question('\n❓ 是否要執行推薦的導入方式？(Y/n): ', (answer) => {
    rl.close();
    
    if (answer.toLowerCase() === 'n' || answer.toLowerCase() === 'no') {
      console.log('❌ 操作已取消');
      return;
    }
    
    // 執行導入
    const success = executeRecommendedMethod(recommendedMethod);
    
    if (success) {
      console.log('\n🎉 導入完成！');
    } else {
      console.log('\n❌ 導入失敗，請檢查錯誤信息');
      process.exit(1);
    }
  });
}

// 運行主函數
if (require.main === module) {
  main();
}

module.exports = {
  showEnvironmentStatus,
  recommendImportMethod,
  executeRecommendedMethod
};
