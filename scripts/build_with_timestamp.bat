@echo off
setlocal enabledelayedexpansion

REM 建置腳本 - 自動設定建置時間戳 (Windows 版本)
REM 使用方式：
REM build_with_timestamp.bat [platform] [build_type] [additional_flags]
REM 
REM 範例：
REM build_with_timestamp.bat web release
REM build_with_timestamp.bat apk release

REM 檢查參數
if "%1"=="" (
    echo 錯誤：缺少必要參數
    goto :show_usage
)

set PLATFORM=%1
set BUILD_TYPE=%2
if "%BUILD_TYPE%"=="" set BUILD_TYPE=release

REM 驗證平台參數
if "%PLATFORM%"=="web" goto :platform_ok
if "%PLATFORM%"=="apk" goto :platform_ok
if "%PLATFORM%"=="ios" goto :platform_ok
if "%PLATFORM%"=="macos" goto :platform_ok
if "%PLATFORM%"=="windows" goto :platform_ok
if "%PLATFORM%"=="linux" goto :platform_ok

echo 錯誤：不支援的平台 '%PLATFORM%'
goto :show_usage

:platform_ok

REM 驗證建置類型
if "%BUILD_TYPE%"=="debug" goto :build_type_ok
if "%BUILD_TYPE%"=="profile" goto :build_type_ok
if "%BUILD_TYPE%"=="release" goto :build_type_ok

echo 錯誤：不支援的建置類型 '%BUILD_TYPE%'
goto :show_usage

:build_type_ok

REM 生成時間戳
for /f "tokens=1-4 delims=/ " %%a in ('date /t') do (
    set DATE_PART=%%d%%b%%c
)
for /f "tokens=1-2 delims=: " %%a in ('time /t') do (
    set TIME_PART=%%a%%b
)

REM 格式化時間戳 (移除可能的 AM/PM)
set TIME_PART=%TIME_PART:AM=%
set TIME_PART=%TIME_PART:PM=%
set TIME_PART=%TIME_PART: =%

REM 生成完整時間戳
for /f "tokens=2-4 delims=/ " %%a in ('date /t') do (
    set YEAR=%%c
    set MONTH=%%a
    set DAY=%%b
)

REM 確保月份和日期是兩位數
if %MONTH% LSS 10 set MONTH=0%MONTH%
if %DAY% LSS 10 set DAY=0%DAY%

for /f "tokens=1-3 delims=:." %%a in ('time /t') do (
    set HOUR=%%a
    set MINUTE=%%b
    set SECOND=00
)

REM 移除空格並確保是兩位數
set HOUR=%HOUR: =%
set MINUTE=%MINUTE: =%
if %HOUR% LSS 10 set HOUR=0%HOUR%
if %MINUTE% LSS 10 set MINUTE=0%MINUTE%

set TIMESTAMP=%YEAR%%MONTH%%DAY%_%HOUR%%MINUTE%%SECOND%
set COMPILATION_TIMESTAMP=%YEAR%%MONTH%%DAY%%HOUR%%MINUTE%%SECOND%

echo === AstReal 建置腳本 ===
echo 平台：%PLATFORM%
echo 建置類型：%BUILD_TYPE%
echo 時間戳：%TIMESTAMP%
echo.

REM 從 pubspec.yaml 讀取版本號
for /f "tokens=2" %%a in ('findstr "^version:" pubspec.yaml') do (
    set VERSION_LINE=%%a
)

REM 移除 +build_number 部分
for /f "tokens=1 delims=+" %%a in ("%VERSION_LINE%") do (
    set VERSION=%%a
)

if "%VERSION%"=="" (
    echo 錯誤：無法從 pubspec.yaml 讀取版本號
    exit /b 1
)

echo 應用版本：%VERSION%

REM 根據建置類型設定建置號碼
if "%BUILD_TYPE%"=="debug" (
    set BUILD_NUMBER=%TIMESTAMP%_debug
) else if "%BUILD_TYPE%"=="profile" (
    set BUILD_NUMBER=%TIMESTAMP%_profile
) else (
    set BUILD_NUMBER=%TIMESTAMP%
)

REM 檢查是否為強制更新版本
if "%FORCE_UPDATE%"=="true" (
    set BUILD_NUMBER=%BUILD_NUMBER%_force_update
    echo 強制更新：是
)

REM 檢查是否為測試版本
if "%BETA%"=="true" (
    set BUILD_NUMBER=%BUILD_NUMBER%_beta
    echo 測試版本：是
) else if "%ALPHA%"=="true" (
    set BUILD_NUMBER=%BUILD_NUMBER%_alpha
    echo 內測版本：是
)

echo 建置號碼：%BUILD_NUMBER%
echo.

REM 建置命令
if "%PLATFORM%"=="web" (
    set BUILD_CMD=flutter build web --%BUILD_TYPE% --build-name=%VERSION% --build-number=%BUILD_NUMBER% --dart-define=COMPILATION_TIMESTAMP=%COMPILATION_TIMESTAMP%
) else if "%PLATFORM%"=="apk" (
    set BUILD_CMD=flutter build apk --%BUILD_TYPE% --build-name=%VERSION% --build-number=%BUILD_NUMBER% --dart-define=COMPILATION_TIMESTAMP=%COMPILATION_TIMESTAMP%
) else if "%PLATFORM%"=="ios" (
    set BUILD_CMD=flutter build ios --%BUILD_TYPE% --build-name=%VERSION% --build-number=%BUILD_NUMBER% --dart-define=COMPILATION_TIMESTAMP=%COMPILATION_TIMESTAMP%
) else if "%PLATFORM%"=="macos" (
    set BUILD_CMD=flutter build macos --%BUILD_TYPE% --build-name=%VERSION% --build-number=%BUILD_NUMBER% --dart-define=COMPILATION_TIMESTAMP=%COMPILATION_TIMESTAMP%
) else if "%PLATFORM%"=="windows" (
    set BUILD_CMD=flutter build windows --%BUILD_TYPE% --build-name=%VERSION% --build-number=%BUILD_NUMBER% --dart-define=COMPILATION_TIMESTAMP=%COMPILATION_TIMESTAMP%
) else if "%PLATFORM%"=="linux" (
    set BUILD_CMD=flutter build linux --%BUILD_TYPE% --build-name=%VERSION% --build-number=%BUILD_NUMBER% --dart-define=COMPILATION_TIMESTAMP=%COMPILATION_TIMESTAMP%
)

REM 加入額外標記
if not "%3"=="" (
    set BUILD_CMD=%BUILD_CMD% %3 %4 %5 %6 %7 %8 %9
)

echo 開始建置...
echo 執行命令：%BUILD_CMD%
echo.

REM 執行建置
%BUILD_CMD%

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ 建置成功！
    echo 平台：%PLATFORM%
    echo 版本：%VERSION%
    echo 建置號碼：%BUILD_NUMBER%
    
    REM 顯示輸出檔案位置
    if "%PLATFORM%"=="web" echo 輸出位置：build\web\
    if "%PLATFORM%"=="apk" echo 輸出位置：build\app\outputs\flutter-apk\
    if "%PLATFORM%"=="ios" echo 輸出位置：build\ios\iphoneos\
    if "%PLATFORM%"=="macos" echo 輸出位置：build\macos\Build\Products\
    if "%PLATFORM%"=="windows" echo 輸出位置：build\windows\runner\Release\
    if "%PLATFORM%"=="linux" echo 輸出位置：build\linux\x64\release\bundle\
) else (
    echo.
    echo ❌ 建置失敗！
    exit /b 1
)

goto :end

:show_usage
echo 建置腳本使用說明
echo.
echo 使用方式：
echo   %0 [platform] [build_type] [additional_flags]
echo.
echo 參數：
echo   platform     : web, apk, ios, macos, windows, linux
echo   build_type   : debug, profile, release (預設: release)
echo   additional_flags : 額外的建置標記 (可選)
echo.
echo 範例：
echo   %0 web release
echo   %0 apk release
echo   %0 ios release
echo   %0 web release --dart-define=FLAVOR=production
echo.
exit /b 1

:end
