# 🚀 Firebase 版本信息初始化腳本總結

## ✅ 已創建的腳本和工具

### 1. 主要腳本文件

| 文件名 | 用途 | 特點 |
|--------|------|------|
| `simple_init_versions.dart` | 生成 Firebase 導入用的 JSON 文件 | ✅ 純 Dart，無 Flutter 依賴 |
| `init_versions_from_config.dart` | 從配置文件初始化版本信息 | ⚠️ 需要 Flutter 環境 |
| `init_versions_with_config.dart` | 使用內建配置初始化 | ⚠️ 需要 Flutter 環境 |
| `setup_firebase_versions.sh` | Linux/macOS 互動式腳本 | 🐧 跨平台支援 |
| `setup_firebase_versions.bat` | Windows 互動式腳本 | 🪟 Windows 專用 |

### 2. 配置文件

| 文件名 | 用途 |
|--------|------|
| `version_config.json` | 版本信息配置模板 |
| `README.md` | 詳細使用說明 |

### 3. 生成的數據文件

在 `scripts/firebase_data/` 目錄中：

| 文件名 | 內容 |
|--------|------|
| `android_version.json` | Android 平台版本信息 |
| `ios_version.json` | iOS 平台版本信息 |
| `macos_version.json` | macOS 平台版本信息 |
| `windows_version.json` | Windows 平台版本信息 |
| `web_version.json` | Web 平台版本信息 |
| `all_versions.json` | 所有平台的合併數據 |
| `example_configs.json` | 示例更新配置 |
| `import_instructions.md` | 導入說明文檔 |

## 🎯 推薦使用方式

### 方法一：簡單快速（推薦）

```bash
# 生成 JSON 文件
dart run scripts/simple_init_versions.dart

# 手動導入到 Firebase Console
# 按照 scripts/firebase_data/import_instructions.md 的說明操作
```

### 方法二：互動式腳本

```bash
# Linux/macOS
./scripts/setup_firebase_versions.sh

# Windows
scripts\setup_firebase_versions.bat
```

### 方法三：預覽模式

```bash
# 只查看將要生成的數據，不實際創建文件
dart run scripts/simple_init_versions.dart --dry-run
```

## 📋 Firebase 設置步驟

### 1. 準備工作
- ✅ Firebase 項目已配置
- ✅ Firestore 已啟用
- ✅ 具有管理員權限

### 2. 生成數據文件
```bash
dart run scripts/simple_init_versions.dart
```

### 3. 手動導入到 Firebase

1. 打開 [Firebase Console](https://console.firebase.google.com/project/astreal-d3f70/firestore)
2. 進入 Firestore Database
3. 創建 Collection `app_versions`
4. 為每個平台創建文檔：

| 平台 | 文檔 ID | JSON 文件 |
|------|---------|-----------|
| Android | `android` | `android_version.json` |
| iOS | `ios` | `ios_version.json` |
| macOS | `macos` | `macos_version.json` |
| Windows | `windows` | `windows_version.json` |
| Web | `web` | `web_version.json` |

### 4. 設置安全規則

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /app_versions/{platform} {
      allow read: if true;
      allow write: if false; // 只能通過管理後台更新
    }
  }
}
```

## 🧪 測試驗證

### 1. 驗證數據結構
確認 Firestore 中的數據結構正確：
```
app_versions/
├── android/
├── ios/
├── macos/
├── windows/
└── web/
```

### 2. 測試應用功能
1. 運行 Flutter 應用
2. 檢查應用啟動時的版本檢查
3. 測試設定頁面的手動檢查更新

### 3. 測試不同場景
- ✅ 最新版本（無提示）
- ✅ 可選更新（顯示對話框）
- ✅ 強制更新（必須更新）

## 🔄 版本更新流程

### 發布新版本時

1. **更新應用版本**
   ```yaml
   # pubspec.yaml
   version: 1.0.1+2
   ```

2. **更新 Firestore 數據**
   
   **可選更新**：
   ```json
   {
     "version": "1.0.1",
     "buildNumber": 2,
     "minRequiredVersion": "1.0.0",  // 保持不變
     "minRequiredBuildNumber": 1,    // 保持不變
     "forceUpdate": false
   }
   ```
   
   **強制更新**：
   ```json
   {
     "version": "1.1.0",
     "buildNumber": 3,
     "minRequiredVersion": "1.1.0",  // 更新為新版本
     "minRequiredBuildNumber": 3,    // 更新為新構建號
     "forceUpdate": true
   }
   ```

## 🛠️ 故障排除

### 常見問題

1. **腳本運行失敗**
   ```bash
   # 確保 Dart 可用
   dart --version
   
   # 確保在正確目錄
   ls pubspec.yaml
   ```

2. **Firebase 連接問題**
   - 檢查網路連接
   - 確認項目 ID 正確
   - 檢查 Firestore 是否啟用

3. **權限問題**
   - 確認具有 Firestore 寫入權限
   - 檢查安全規則設置

### 調試技巧

1. **使用乾運行模式**
   ```bash
   dart run scripts/simple_init_versions.dart --dry-run
   ```

2. **檢查生成的文件**
   ```bash
   ls -la scripts/firebase_data/
   cat scripts/firebase_data/android_version.json
   ```

3. **查看應用日誌**
   - 檢查版本檢查相關的日誌輸出
   - 確認 Firebase 連接狀態

## 📚 相關文檔

- [Firebase 版本控管設置說明](../docs/firebase_version_setup.md)
- [版本控管使用說明](../docs/version_control_usage.md)
- [實作總結](../docs/version_control_implementation_summary.md)
- [腳本使用說明](README.md)

## 🎉 總結

這套 Firebase 版本信息初始化腳本提供了：

- ✅ **多種初始化方式** - 從簡單的 JSON 生成到完整的自動化腳本
- ✅ **跨平台支援** - Linux、macOS、Windows 都有對應的腳本
- ✅ **詳細文檔** - 完整的使用說明和故障排除指南
- ✅ **示例配置** - 可選更新和強制更新的示例
- ✅ **測試驗證** - 完整的測試流程和驗證步驟

推薦使用 `simple_init_versions.dart` 腳本，它最簡單可靠，不依賴 Flutter 環境，可以快速生成所需的 JSON 文件用於手動導入到 Firebase Console。

🚀 **現在您可以開始使用完整的版本控管系統了！**
