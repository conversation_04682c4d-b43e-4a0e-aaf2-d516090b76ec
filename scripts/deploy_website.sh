#!/bin/bash

#  AstReal 官方網站部署腳本
# 用於部署官方網站到 Firebase Hosting

set -e  # 遇到錯誤時停止執行

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日誌函數
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 檢查必要工具
check_dependencies() {
    log_info "檢查必要工具..."
    
    if ! command -v firebase &> /dev/null; then
        log_error "Firebase CLI 未安裝，請先安裝："
        echo "npm install -g firebase-tools"
        exit 1
    fi
    
    if ! command -v git &> /dev/null; then
        log_error "Git 未安裝，請先安裝 Git"
        exit 1
    fi
    
    log_success "所有必要工具已安裝"
}

# 檢查 Firebase 登入狀態
check_firebase_auth() {
    log_info "檢查 Firebase 認證狀態..."
    
    if ! firebase projects:list &> /dev/null; then
        log_warning "未登入 Firebase，正在啟動登入流程..."
        firebase login
    fi
    
    log_success "Firebase 認證成功"
}

# 驗證網站文件
validate_files() {
    log_info "驗證網站文件..."
    
    local required_files=(
        "web/landing.html"
        "web/download.html"
        "web/home.html"
        "web/privacy-policy.html"
        "web/terms-of-service.html"
    )
    
    for file in "${required_files[@]}"; do
        if [[ ! -f "$file" ]]; then
            log_error "缺少必要文件: $file"
            exit 1
        fi
    done
    
    log_success "所有必要文件存在"
}

# 檢查 HTML 語法
validate_html() {
    log_info "檢查 HTML 語法..."
    
    # 簡單的 HTML 語法檢查
    local html_files=(
        "web/landing.html"
        "web/download.html"
        "web/home.html"
    )
    
    for file in "${html_files[@]}"; do
        if ! grep -q "</html>" "$file"; then
            log_error "HTML 文件可能不完整: $file"
            exit 1
        fi
    done
    
    log_success "HTML 語法檢查通過"
}

# 更新版本資訊
update_version_info() {
    log_info "更新版本資訊..."
    
    local version=$(grep -o 'version: [0-9.]*' pubspec.yaml | cut -d' ' -f2)
    local build=$(grep -o '+[0-9]*' pubspec.yaml | cut -d'+' -f2)
    local current_date=$(date '+%Y-%m-%d')
    
    log_info "當前版本: $version+$build"
    log_info "部署日期: $current_date"
    
    # 可以在這裡添加版本資訊更新邏輯
    # 例如更新 HTML 文件中的版本號
}

# 建立備份
create_backup() {
    log_info "建立當前部署備份..."
    
    local backup_dir="backups/website_$(date '+%Y%m%d_%H%M%S')"
    mkdir -p "$backup_dir"
    
    cp -r web/ "$backup_dir/"
    
    log_success "備份已建立: $backup_dir"
}

# 部署到 Firebase Hosting
deploy_to_firebase() {
    log_info "開始部署到 Firebase Hosting..."
    
    # 檢查 Firebase 專案
    local project_id=$(firebase use | grep -o 'astreal-[a-z0-9]*' || echo "")
    
    if [[ -z "$project_id" ]]; then
        log_error "未設定 Firebase 專案，請先執行: firebase use --add"
        exit 1
    fi
    
    log_info "部署到專案: $project_id"
    
    # 執行部署
    if firebase deploy --only hosting; then
        log_success "部署成功！"
        log_info "網站 URL: https://$project_id.web.app/"
        
        # 如果有自定義域名
        if [[ -f ".firebaserc" ]]; then
            local custom_domain=$(grep -o '"site".*"[^"]*"' .firebaserc | cut -d'"' -f4)
            if [[ -n "$custom_domain" && "$custom_domain" != "$project_id" ]]; then
                log_info "自定義域名: https://$custom_domain/"
            fi
        fi
    else
        log_error "部署失敗！"
        exit 1
    fi
}

# 驗證部署結果
verify_deployment() {
    log_info "驗證部署結果..."
    
    local project_id=$(firebase use | grep -o 'astreal-[a-z0-9]*' || echo "")
    local url="https://$project_id.web.app/landing.html"
    
    log_info "檢查網站可訪問性: $url"
    
    if command -v curl &> /dev/null; then
        if curl -s --head "$url" | head -n 1 | grep -q "200 OK"; then
            log_success "網站可正常訪問"
        else
            log_warning "網站可能無法正常訪問，請手動檢查"
        fi
    else
        log_info "請手動檢查網站是否可正常訪問: $url"
    fi
}

# 清理臨時文件
cleanup() {
    log_info "清理臨時文件..."
    # 如果有臨時文件需要清理，在這裡添加
    log_success "清理完成"
}

# 顯示部署後的操作建議
show_post_deployment_tips() {
    log_info "部署後建議操作："
    echo "1. 檢查網站各頁面是否正常顯示"
    echo "2. 測試所有下載連結"
    echo "3. 驗證響應式設計在不同設備上的表現"
    echo "4. 檢查 SEO 相關設定"
    echo "5. 更新 DNS 記錄（如果使用自定義域名）"
}

# 主函數
main() {
    log_info "開始部署 AstReal 官方網站"
    echo "========================================"
    
    # 檢查當前目錄
    if [[ ! -f "pubspec.yaml" ]]; then
        log_error "請在專案根目錄執行此腳本"
        exit 1
    fi
    
    # 執行部署流程
    check_dependencies
    check_firebase_auth
    validate_files
    validate_html
    update_version_info
    create_backup
    deploy_to_firebase
    verify_deployment
    cleanup
    
    echo "========================================"
    log_success "網站部署完成！"
    show_post_deployment_tips
}

# 處理中斷信號
trap cleanup EXIT

# 執行主函數
main "$@"
