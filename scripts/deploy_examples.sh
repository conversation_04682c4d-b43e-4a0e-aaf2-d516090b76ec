#!/bin/bash

# 部署範例腳本 - 展示不同的部署方式
# 這個腳本不會實際執行部署，只是展示命令

echo "🚀 AstReal Web 部署範例"
echo "======================="
echo ""

echo "1. 📝 一般開發部署（不觸發更新通知）"
echo "   適用於：小修改、樣式調整、日常開發"
echo "   命令：./scripts/deploy_web.sh"
echo "   版本格式：20250119_143000"
echo "   用戶體驗：不會收到更新通知"
echo ""

echo "2. 🔔 重要更新部署（觸發更新通知）"
echo "   適用於：新功能、重要修復、安全更新"
echo "   命令：./scripts/deploy_web.sh --notify-update"
echo "   版本格式：20250119_143000_update"
echo "   用戶體驗：會收到「新版本可用」通知"
echo ""

echo "3. 🎯 正式版本發布（語義化版本）"
echo "   適用於：里程碑版本、大版本發布"
echo "   命令：./scripts/deploy_web.sh --version=1.2.0"
echo "   版本格式：1.2.0"
echo "   用戶體驗：會收到更新通知，版本號清晰"
echo ""

echo "4. 🧪 測試版本檢查邏輯"
echo "   打開：scripts/test_version_logic.html"
echo "   功能：測試版本比較邏輯是否正確"
echo ""

echo "5. 📊 檢查當前部署狀態"
echo "   查看版本文件：cat build/web/version.json"
echo "   查看瀏覽器控制台：檢查版本檢查日誌"
echo ""

echo "💡 使用建議："
echo "   - 日常開發使用方式 1"
echo "   - 重要功能更新使用方式 2"
echo "   - 正式發布使用方式 3"
echo "   - 部署前先測試邏輯（方式 4）"
echo ""

echo "🔧 故障排除："
echo "   - 如果更新通知太頻繁：檢查是否使用了正確的部署方式"
echo "   - 如果重要更新沒通知：確認使用了 --notify-update 參數"
echo "   - 清除用戶通知記錄：localStorage.removeItem('lastNotifiedVersion')"
echo ""

echo "📚 更多資訊請參考：docs/version_update_system.md"
