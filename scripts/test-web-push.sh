#!/bin/bash

# AstReal Web 推播通知測試腳本
# 用於快速驗證 Web 推播通知配置是否正確

echo "🔔 AstReal Web 推播通知測試腳本"
echo "=================================="

# 檢查必要文件是否存在
echo "📁 檢查必要文件..."

files=(
    "web/firebase-messaging-sw.js"
    "web/manifest.json"
    "web/test-push-notification.html"
    "web/simple-push-test.html"
    "web/test-platform-fix.html"
    "web/debug-push-notification.js"
)

missing_files=()

for file in "${files[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file 存在"
    else
        echo "❌ $file 不存在"
        missing_files+=("$file")
    fi
done

if [ ${#missing_files[@]} -gt 0 ]; then
    echo ""
    echo "⚠️ 發現缺失文件，請確保以下文件存在："
    for file in "${missing_files[@]}"; do
        echo "   - $file"
    done
    exit 1
fi

echo ""
echo "📋 檢查 manifest.json 配置..."

# 檢查 manifest.json 是否包含 gcm_sender_id
if grep -q "gcm_sender_id" web/manifest.json; then
    echo "✅ manifest.json 包含 gcm_sender_id"
else
    echo "❌ manifest.json 缺少 gcm_sender_id"
fi

echo ""
echo "📋 檢查 firebase-messaging-sw.js 配置..."

# 檢查 Service Worker 是否包含必要的配置
if grep -q "firebaseConfig" web/firebase-messaging-sw.js; then
    echo "✅ Service Worker 包含 Firebase 配置"
else
    echo "❌ Service Worker 缺少 Firebase 配置"
fi

if grep -q "onBackgroundMessage" web/firebase-messaging-sw.js; then
    echo "✅ Service Worker 包含背景通知處理"
else
    echo "❌ Service Worker 缺少背景通知處理"
fi

echo ""
echo "📋 檢查 Flutter 代碼配置..."

# 檢查 FirebaseNotificationService 是否包含 Web 特殊處理
if grep -q "kIsWeb" lib/data/services/notification/firebase_notification_service.dart; then
    echo "✅ FirebaseNotificationService 包含 Web 平台處理"
else
    echo "❌ FirebaseNotificationService 缺少 Web 平台處理"
fi

if grep -q "vapidKey" lib/data/services/notification/firebase_notification_service.dart; then
    echo "✅ FirebaseNotificationService 包含 VAPID Key"
else
    echo "❌ FirebaseNotificationService 缺少 VAPID Key"
fi

echo ""
echo "🚀 啟動測試..."

# 檢查是否有 Flutter 開發服務器在運行
if lsof -i :3000 > /dev/null 2>&1; then
    echo "✅ 檢測到端口 3000 上有服務運行"
    TEST_URL="http://localhost:3000/test-push-notification.html"
elif lsof -i :8080 > /dev/null 2>&1; then
    echo "✅ 檢測到端口 8080 上有服務運行"
    TEST_URL="http://localhost:8080/test-push-notification.html"
else
    echo "⚠️ 未檢測到開發服務器，請先啟動 Flutter Web 開發服務器"
    echo "   執行: flutter run -d chrome --web-port 3000"
    TEST_URL="http://localhost:3000/test-push-notification.html"
fi

echo ""
echo "📖 測試指南："
echo "1. 啟動 Flutter Web 開發服務器："
echo "   flutter run -d chrome --web-port 3000"
echo ""
echo "2. 在瀏覽器中開啟測試頁面："
echo "   Platform 修復測試: http://localhost:3000/test-platform-fix.html (推薦)"
echo "   簡單推播測試: http://localhost:3000/simple-push-test.html"
echo "   完整功能測試: http://localhost:3000/test-push-notification.html"
echo ""
echo "3. 使用簡單測試頁面："
echo "   - 點擊「開始測試推播通知」按鈕"
echo "   - 按照頁面提示完成測試"
echo "   - 複製獲取的 FCM Token"
echo ""
echo "4. 在瀏覽器 Console 中執行完整診斷（可選）："
echo "   AstrealPushDebug.runFullDiagnostic()"
echo ""
echo "5. 使用 Firebase Console 發送測試通知："
echo "   - 前往 Firebase Console > 雲端通訊"
echo "   - 點擊「傳送您的第一則訊息」"
echo "   - 使用從測試頁面獲取的 FCM Token"
echo ""
echo "📚 更多資訊請參考："
echo "   docs/web-push-notification-setup.md"

echo ""
echo "✅ 測試腳本執行完成"
