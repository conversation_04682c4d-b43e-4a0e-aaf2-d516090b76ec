# 🚀 Node.js Firebase Firestore 導入腳本

這個目錄包含了多種 Node.js 腳本來自動導入 app_versions 相關的 JSON 數據到 Firebase Firestore。

## 📁 腳本文件概覽

| 腳本名稱 | 用途 | 依賴 | 推薦度 |
|----------|------|------|--------|
| `import-selector.js` | 🎯 **智能選擇器** - 自動檢測環境並推薦最佳導入方式 | 無 | ⭐⭐⭐⭐⭐ |
| `import-to-firestore.js` | 🔧 使用 Firebase Admin SDK 導入 | firebase-admin | ⭐⭐⭐⭐ |
| `simple-import.js` | 🛠️ 使用 Firebase CLI 導入 | Firebase CLI | ⭐⭐⭐ |
| `rest-import.js` | 🌐 使用 REST API 導入 | 無 | ⭐⭐ |

## 🚀 快速開始

### 方法一：智能選擇器（推薦）

```bash
# 1. 生成數據文件（如果尚未生成）
dart run scripts/simple_init_versions.dart

# 2. 運行智能選擇器
node scripts/import-selector.js
```

智能選擇器會：
- 🔍 自動檢測您的環境
- 🎯 推薦最適合的導入方式
- 🚀 一鍵執行導入

### 方法二：使用 npm 腳本

```bash
# 安裝依賴
cd scripts
npm install

# 生成數據並導入
npm run deploy

# 或者分步執行
npm run generate    # 生成數據文件
npm run import      # 導入到 Firestore
```

## 📋 詳細使用說明

### 1. 智能選擇器 (`import-selector.js`)

**最推薦的方式**，會自動檢測環境並選擇最佳導入方法。

```bash
# 自動選擇並執行導入
node scripts/import-selector.js

# 只檢查環境，不執行導入
node scripts/import-selector.js --check

# 顯示幫助
node scripts/import-selector.js --help
```

**特點：**
- ✅ 自動環境檢測
- ✅ 智能推薦導入方式
- ✅ 詳細的狀態報告
- ✅ 無需手動選擇

### 2. Firebase Admin SDK (`import-to-firestore.js`)

**最可靠的方式**，使用官方 Firebase Admin SDK。

```bash
# 安裝依賴
npm install firebase-admin

# 設置服務帳戶金鑰
export GOOGLE_APPLICATION_CREDENTIALS="path/to/service-account-key.json"

# 執行導入
node scripts/import-to-firestore.js
```

**特點：**
- ✅ 最可靠和穩定
- ✅ 支持批次操作
- ✅ 完整的錯誤處理
- ⚠️ 需要服務帳戶金鑰

**環境變量：**
- `GOOGLE_APPLICATION_CREDENTIALS`: 服務帳戶金鑰文件路徑
- `FIREBASE_PROJECT_ID`: Firebase 項目 ID（默認：astreal-d3f70）

### 3. Firebase CLI (`simple-import.js`)

**最簡單的方式**，使用 Firebase CLI 工具。

```bash
# 安裝 Firebase CLI
npm install -g firebase-tools

# 登入並設置項目
firebase login
firebase use astreal-d3f70

# 執行導入
node scripts/simple-import.js
```

**特點：**
- ✅ 簡單易用
- ✅ 官方工具
- ✅ 無需額外配置
- ⚠️ 需要手動確認操作

**可用選項：**
```bash
node scripts/simple-import.js           # 自動導入
node scripts/simple-import.js --manual  # 生成手動導入說明
node scripts/simple-import.js --commands # 生成 Firebase CLI 命令
```

### 4. REST API (`rest-import.js`)

**無依賴方式**，直接使用 Firebase REST API。

```bash
# 設置 API Key
export FIREBASE_API_KEY="your-firebase-web-api-key"

# 執行導入
node scripts/rest-import.js
```

**特點：**
- ✅ 無需額外依賴
- ✅ 直接使用 Web API
- ✅ 輕量級解決方案
- ⚠️ 功能相對簡單

**環境變量：**
- `FIREBASE_API_KEY`: Firebase Web API Key
- `FIREBASE_PROJECT_ID`: Firebase 項目 ID（默認：astreal-d3f70）

## 🔧 環境準備

### 1. 數據文件準備

所有導入腳本都需要先生成數據文件：

```bash
# 生成數據文件
dart run scripts/simple_init_versions.dart

# 預覽數據（不生成文件）
dart run scripts/simple_init_versions.dart --dry-run
```

### 2. Firebase 配置

根據選擇的導入方式，需要不同的配置：

#### Firebase Admin SDK 配置

1. 在 [Firebase Console](https://console.firebase.google.com/project/astreal-d3f70/settings/serviceaccounts/adminsdk) 下載服務帳戶金鑰
2. 設置環境變量：
   ```bash
   export GOOGLE_APPLICATION_CREDENTIALS="path/to/service-account-key.json"
   ```

#### Firebase CLI 配置

```bash
# 安裝 Firebase CLI
npm install -g firebase-tools

# 登入
firebase login

# 設置項目
firebase use astreal-d3f70
```

#### REST API 配置

1. 在 [Firebase Console](https://console.firebase.google.com/project/astreal-d3f70/settings/general) 找到 Web API Key
2. 設置環境變量：
   ```bash
   export FIREBASE_API_KEY="your-api-key"
   ```

## 📊 npm 腳本命令

```bash
# 主要命令
npm run import          # 智能導入（推薦）
npm run generate        # 生成數據文件
npm run deploy          # 生成並導入
npm run check           # 檢查環境

# 特定導入方式
npm run import:admin    # 使用 Admin SDK
npm run import:cli      # 使用 Firebase CLI
npm run import:rest     # 使用 REST API
npm run import:manual   # 生成手動導入說明

# 輔助命令
npm run generate:preview # 預覽數據
npm run import:check    # 檢查環境
npm run import:help     # 顯示幫助
```

## 🔍 故障排除

### 常見問題

1. **數據文件不存在**
   ```
   ❌ 數據目錄不存在: scripts/firebase_data
   ```
   **解決方案**: 先生成數據文件
   ```bash
   dart run scripts/simple_init_versions.dart
   ```

2. **Firebase CLI 未安裝**
   ```
   ❌ Firebase CLI 未安裝或不可用
   ```
   **解決方案**: 安裝並配置 Firebase CLI
   ```bash
   npm install -g firebase-tools
   firebase login
   firebase use astreal-d3f70
   ```

3. **服務帳戶金鑰問題**
   ```
   ❌ Firebase 初始化失敗
   ```
   **解決方案**: 檢查服務帳戶金鑰設置
   ```bash
   export GOOGLE_APPLICATION_CREDENTIALS="path/to/service-account-key.json"
   ```

4. **API Key 無效**
   ```
   ❌ Firebase 連接失敗
   ```
   **解決方案**: 檢查 API Key 設置
   ```bash
   export FIREBASE_API_KEY="correct-api-key"
   ```

### 調試技巧

1. **使用環境檢查**
   ```bash
   node scripts/import-selector.js --check
   ```

2. **查看詳細錯誤信息**
   ```bash
   node scripts/import-to-firestore.js --help
   ```

3. **手動導入作為備選**
   ```bash
   node scripts/simple-import.js --manual
   ```

## 🎯 推薦流程

1. **首次使用**：
   ```bash
   # 生成數據
   dart run scripts/simple_init_versions.dart
   
   # 智能導入
   node scripts/import-selector.js
   ```

2. **開發環境**：
   ```bash
   # 使用 Firebase CLI
   npm run import:cli
   ```

3. **生產環境**：
   ```bash
   # 使用 Admin SDK
   npm run import:admin
   ```

4. **CI/CD 環境**：
   ```bash
   # 使用 REST API
   npm run import:rest
   ```

## 📚 相關文檔

- [Firebase 版本控管設置說明](../docs/firebase_version_setup.md)
- [版本控管使用說明](../docs/version_control_usage.md)
- [Dart 腳本說明](README.md)
- [Firebase 初始化總結](FIREBASE_INIT_SUMMARY.md)

## 🎉 總結

這套 Node.js 導入腳本提供了：

- ✅ **多種導入方式** - 適應不同環境和需求
- ✅ **智能選擇器** - 自動推薦最佳方案
- ✅ **完整的錯誤處理** - 詳細的錯誤信息和解決建議
- ✅ **環境檢測** - 自動檢測配置和依賴
- ✅ **npm 腳本集成** - 方便的命令行界面

推薦使用 `import-selector.js` 作為主要入口點，它會自動選擇最適合您環境的導入方式！🚀
