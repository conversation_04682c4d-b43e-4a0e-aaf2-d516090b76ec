@echo off
setlocal enabledelayedexpansion

REM Firebase 版本信息設置腳本 (Windows)
REM 使用方法: scripts\setup_firebase_versions.bat [選項]

echo.
echo 🚀 Firebase 版本信息設置腳本
echo ================================
echo.

REM 檢查是否在正確的目錄
if not exist "pubspec.yaml" (
    echo ❌ 錯誤：請在 Flutter 項目根目錄運行此腳本
    pause
    exit /b 1
)

REM 檢查 Dart 是否可用
dart --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 錯誤：未找到 Dart 命令，請確保 Flutter SDK 已正確安裝
    pause
    exit /b 1
)

REM 處理命令行參數
if "%1"=="preview" goto preview_config
if "%1"=="-p" goto preview_config
if "%1"=="--preview" goto preview_config
if "%1"=="init" goto init_default
if "%1"=="-i" goto init_default
if "%1"=="--init" goto init_default
if "%1"=="help" goto show_help
if "%1"=="-h" goto show_help
if "%1"=="--help" goto show_help

REM 檢查依賴
call :check_dependencies
if errorlevel 1 exit /b 1

REM 顯示菜單
:main_menu
echo.
echo 請選擇操作：
echo 1. 預覽配置（乾運行）
echo 2. 初始化版本信息（使用默認配置）
echo 3. 初始化版本信息（使用自定義配置）
echo 4. 創建示例配置文件
echo 5. 查看現有版本信息
echo 6. 退出
echo.

set /p choice="請輸入選項 (1-6): "

if "%choice%"=="1" goto preview_config
if "%choice%"=="2" goto init_default
if "%choice%"=="3" goto init_custom
if "%choice%"=="4" goto create_example_config
if "%choice%"=="5" goto view_existing
if "%choice%"=="6" goto exit_script

echo ❌ 無效選項，請輸入 1-6
goto main_menu

REM 預覽配置
:preview_config
echo.
echo 📋 預覽將要創建的版本信息...
echo.

if not exist "scripts\version_config.json" (
    echo ❌ 配置文件不存在，請先創建配置文件
    goto continue_prompt
)

dart run scripts/init_versions_from_config.dart --dry-run
goto continue_prompt

REM 初始化版本信息（默認配置）
:init_default
echo.
echo 📱 使用默認配置初始化版本信息...
echo.

if not exist "scripts\version_config.json" (
    echo ⚠️  配置文件不存在，使用內建配置
    dart run scripts/init_versions_with_config.dart
) else (
    dart run scripts/init_versions_from_config.dart
)
goto continue_prompt

REM 初始化版本信息（自定義配置）
:init_custom
echo.
set /p config_path="📝 請輸入配置文件路徑: "

if not exist "%config_path%" (
    echo ❌ 配置文件不存在：%config_path%
    goto continue_prompt
)

echo.
echo 📱 使用自定義配置初始化版本信息...
echo.
dart run scripts/init_versions_from_config.dart --config="%config_path%"
goto continue_prompt

REM 創建示例配置文件
:create_example_config
set target_file=scripts\my_version_config.json

if exist "%target_file%" (
    echo ⚠️  文件已存在：%target_file%
    set /p confirm="是否要覆蓋？(y/N): "
    if /i not "!confirm!"=="y" (
        echo 操作已取消
        goto continue_prompt
    )
)

copy "scripts\version_config.json" "%target_file%" >nul
echo ✅ 示例配置文件已創建：%target_file%
echo 💡 您可以編輯此文件來自定義版本信息
goto continue_prompt

REM 查看現有版本信息
:view_existing
echo.
echo 🔍 查看現有版本信息...
echo.
echo 💡 請在 Firebase Console 中查看現有版本信息：
echo https://console.firebase.google.com/project/astreal-d3f70/firestore/data/~2Fapp_versions
goto continue_prompt

REM 檢查依賴
:check_dependencies
echo 🔍 檢查依賴...

findstr /c:"cloud_firestore:" pubspec.yaml >nul
if errorlevel 1 (
    echo ❌ 缺少依賴：cloud_firestore
    echo 💡 請運行：flutter pub add cloud_firestore
    exit /b 1
)

findstr /c:"firebase_core:" pubspec.yaml >nul
if errorlevel 1 (
    echo ❌ 缺少依賴：firebase_core
    echo 💡 請運行：flutter pub add firebase_core
    exit /b 1
)

echo ✅ 依賴檢查通過
echo.
exit /b 0

REM 顯示幫助
:show_help
echo 使用方法: %0 [選項]
echo.
echo 選項：
echo   preview, -p    預覽配置
echo   init, -i       初始化版本信息
echo   help, -h       顯示幫助
echo.
pause
exit /b 0

REM 繼續提示
:continue_prompt
echo.
echo 按任意鍵繼續...
pause >nul
goto main_menu

REM 退出腳本
:exit_script
echo 👋 再見！
pause
exit /b 0
