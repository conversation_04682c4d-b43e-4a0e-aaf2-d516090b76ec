#!/usr/bin/env node

/**
 * 簡化的 Firebase CLI 導入腳本
 * 
 * 使用 firebase firestore:set 命令逐個設置文檔
 * 這是最可靠的 Firebase CLI 導入方式
 * 
 * 使用方法：
 * 1. 確保已登入 Firebase CLI: firebase login
 * 2. 設置項目: firebase use astreal-d3f70
 * 3. 運行腳本: node scripts/firebase-cli-import.js
 */

const fs = require('fs').promises;
const path = require('path');
const { execSync } = require('child_process');

// 配置
const PROJECT_ID = 'astreal-d3f70';
const COLLECTION_NAME = 'app_versions';
const DATA_DIR = path.join(__dirname, 'firebase_data');
const PLATFORMS = ['android', 'ios', 'macos', 'windows', 'web'];

/**
 * 檢查 Firebase CLI 是否可用
 */
function checkFirebaseCLI() {
  try {
    const version = execSync('firebase --version', { encoding: 'utf8' });
    console.log(`✅ Firebase CLI 版本: ${version.trim()}`);
    return true;
  } catch (error) {
    console.error('❌ Firebase CLI 未安裝或不可用');
    console.log('💡 請安裝 Firebase CLI:');
    console.log('   npm install -g firebase-tools');
    console.log('   firebase login');
    console.log('   firebase use astreal-d3f70');
    return false;
  }
}

/**
 * 檢查 Firebase 項目設置
 */
function checkFirebaseProject() {
  try {
    const result = execSync('firebase use', { encoding: 'utf8' });
    console.log(`📋 當前項目: ${result.trim()}`);
    
    if (result.includes(PROJECT_ID)) {
      console.log(`✅ 項目設置正確: ${PROJECT_ID}`);
      return true;
    } else {
      console.error(`❌ 項目設置錯誤，當前項目不是 ${PROJECT_ID}`);
      console.log(`💡 請設置正確的項目: firebase use ${PROJECT_ID}`);
      return false;
    }
  } catch (error) {
    console.error('❌ 無法檢查 Firebase 項目設置');
    console.log('💡 請確保已登入並設置項目:');
    console.log('   firebase login');
    console.log(`   firebase use ${PROJECT_ID}`);
    return false;
  }
}

/**
 * 檢查數據文件
 */
async function checkDataFiles() {
  console.log('🔍 檢查數據文件...');
  
  try {
    await fs.access(DATA_DIR);
  } catch (error) {
    throw new Error(`數據目錄不存在: ${DATA_DIR}\n請先運行: dart run scripts/simple_init_versions.dart`);
  }

  const missingFiles = [];
  for (const platform of PLATFORMS) {
    const filePath = path.join(DATA_DIR, `${platform}_version.json`);
    try {
      await fs.access(filePath);
      console.log(`   ✅ ${platform}_version.json`);
    } catch (error) {
      missingFiles.push(`${platform}_version.json`);
    }
  }

  if (missingFiles.length > 0) {
    throw new Error(`缺少文件: ${missingFiles.join(', ')}\n請先運行: dart run scripts/simple_init_versions.dart`);
  }

  console.log('✅ 所有數據文件檢查完成\n');
}

/**
 * 讀取 JSON 文件
 */
async function readJsonFile(filePath) {
  try {
    const data = await fs.readFile(filePath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    throw new Error(`讀取文件失敗 ${filePath}: ${error.message}`);
  }
}

/**
 * 檢查現有數據
 */
async function checkExistingData() {
  console.log('🔍 檢查現有版本數據...');
  
  try {
    // 嘗試獲取現有文檔
    for (const platform of PLATFORMS) {
      try {
        const result = execSync(`firebase firestore:get ${COLLECTION_NAME}/${platform}`, { 
          encoding: 'utf8',
          stdio: 'pipe'
        });
        
        if (result && !result.includes('No document')) {
          console.log(`   📱 發現現有數據: ${platform}`);
        }
      } catch (error) {
        // 文檔不存在，這是正常的
      }
    }
    
    // 詢問是否繼續
    const readline = require('readline');
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
    
    const answer = await new Promise(resolve => {
      rl.question('\n❓ 是否要繼續導入（會覆蓋現有數據）？(Y/n): ', resolve);
    });
    rl.close();
    
    if (answer.toLowerCase() === 'n' || answer.toLowerCase() === 'no') {
      console.log('❌ 操作已取消');
      process.exit(0);
    }
    
    console.log('✅ 將繼續導入數據\n');
  } catch (error) {
    console.log('✅ 未發現現有數據或無法檢查，將繼續導入\n');
  }
}

/**
 * 使用 Firebase CLI 導入數據
 */
async function importWithFirebaseCLI() {
  console.log('📱 使用 Firebase CLI 導入數據...\n');
  
  const results = {};
  
  for (const platform of PLATFORMS) {
    try {
      console.log(`   正在處理 ${platform}...`);
      
      // 讀取 JSON 文件
      const filePath = path.join(DATA_DIR, `${platform}_version.json`);
      const versionData = await readJsonFile(filePath);
      
      // 創建臨時文件（Firebase CLI 需要文件路徑）
      const tempFile = path.join(DATA_DIR, `temp_${platform}.json`);
      await fs.writeFile(tempFile, JSON.stringify(versionData, null, 2));
      
      // 使用 firebase firestore:set 設置文檔
      const command = `firebase firestore:set ${COLLECTION_NAME}/${platform} ${tempFile}`;
      execSync(command, { stdio: 'pipe' });
      
      // 清理臨時文件
      await fs.unlink(tempFile);
      
      console.log(`   ✅ ${platform} 導入成功`);
      results[platform] = { success: true };
      
    } catch (error) {
      console.error(`   ❌ ${platform} 導入失敗:`, error.message);
      results[platform] = { success: false, error: error.message };
    }
  }
  
  return results;
}

/**
 * 驗證導入結果
 */
async function verifyImport() {
  console.log('\n🔍 驗證導入結果...\n');
  
  for (const platform of PLATFORMS) {
    try {
      const result = execSync(`firebase firestore:get ${COLLECTION_NAME}/${platform}`, { 
        encoding: 'utf8',
        stdio: 'pipe'
      });
      
      if (result && !result.includes('No document')) {
        // 嘗試解析版本信息
        const lines = result.split('\n');
        let version = 'unknown';
        let buildNumber = 'unknown';
        
        for (const line of lines) {
          if (line.includes('version:')) {
            version = line.split(':')[1]?.trim().replace(/"/g, '') || 'unknown';
          }
          if (line.includes('buildNumber:')) {
            buildNumber = line.split(':')[1]?.trim() || 'unknown';
          }
        }
        
        console.log(`   ✅ ${platform} 驗證成功 (v${version}, build ${buildNumber})`);
      } else {
        console.error(`   ❌ ${platform} 文檔不存在`);
      }
      
    } catch (error) {
      console.error(`   ❌ ${platform} 驗證失敗:`, error.message);
    }
  }
}

/**
 * 顯示完成信息
 */
function showCompletionInfo() {
  console.log('\n🎉 版本數據導入完成！\n');
  console.log('📝 下一步：');
  console.log('1. 在 Firebase Console 中確認數據：');
  console.log(`   https://console.firebase.google.com/project/${PROJECT_ID}/firestore/data/~2F${COLLECTION_NAME}`);
  console.log('2. 測試應用的版本檢查功能');
  console.log('3. 設置 Firestore 安全規則（如果尚未設置）');
}

/**
 * 主函數
 */
async function main() {
  console.log('🚀 Firebase CLI 導入腳本\n');
  
  try {
    // 檢查命令行參數
    if (process.argv.includes('--help') || process.argv.includes('-h')) {
      console.log('使用方法：');
      console.log('  node scripts/firebase-cli-import.js');
      console.log('');
      console.log('前提條件：');
      console.log('  1. 已安裝 Firebase CLI: npm install -g firebase-tools');
      console.log('  2. 已登入 Firebase: firebase login');
      console.log('  3. 已設置項目: firebase use astreal-d3f70');
      console.log('  4. 已生成數據文件: dart run scripts/simple_init_versions.dart');
      return;
    }
    
    // 檢查 Firebase CLI
    if (!checkFirebaseCLI()) {
      throw new Error('Firebase CLI 不可用');
    }
    
    // 檢查項目設置
    if (!checkFirebaseProject()) {
      throw new Error('Firebase 項目設置錯誤');
    }
    
    // 檢查數據文件
    await checkDataFiles();
    
    // 檢查現有數據
    await checkExistingData();
    
    // 導入版本數據
    const results = await importWithFirebaseCLI();
    
    // 驗證導入結果
    await verifyImport();
    
    // 顯示結果摘要
    const successCount = Object.values(results).filter(r => r.success).length;
    const totalCount = Object.keys(results).length;
    
    console.log(`\n📊 導入結果: ${successCount}/${totalCount} 成功`);
    
    if (successCount === totalCount) {
      showCompletionInfo();
    } else {
      console.log('\n⚠️  部分導入失敗，請檢查錯誤信息');
      console.log('💡 您可以重新運行腳本來重試失敗的項目');
    }
    
  } catch (error) {
    console.error('\n❌ 導入失敗:', error.message);
    console.log('\n💡 請檢查：');
    console.log('1. Firebase CLI 是否已安裝並登入');
    console.log('2. 項目是否設置正確');
    console.log('3. 數據文件是否存在');
    console.log('4. 網路連接是否正常');
    process.exit(1);
  }
}

// 運行主函數
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  checkFirebaseCLI,
  checkFirebaseProject,
  importWithFirebaseCLI
};
