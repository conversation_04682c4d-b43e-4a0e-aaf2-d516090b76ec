{"updatePolicy": {"description": "版本更新策略配置", "timestampVersionThreshold": 3600000, "comment": "時間戳版本的通知閾值（毫秒），預設 1 小時", "dismissTimeout": 1800000, "comment2": "用戶點擊稍後後的重新提醒間隔（毫秒），預設 30 分鐘", "checkInterval": 300000, "comment3": "版本檢查間隔（毫秒），預設 5 分鐘", "initialCheckDelay": 60000, "comment4": "首次檢查延遲（毫秒），預設 1 分鐘"}, "versionTypes": {"semantic": {"description": "語義化版本號（如 1.0.0, 1.2.3）", "pattern": "^\\d+\\.\\d+\\.\\d+", "alwaysNotify": true, "comment": "語義化版本總是通知更新"}, "timestamp": {"description": "時間戳版本號（如 20250119_143000）", "pattern": "^\\d{8}_\\d{6}$", "alwaysNotify": false, "comment": "時間戳版本需要檢查時間差"}, "updateMarked": {"description": "標記為更新的版本（包含 'update' 字樣）", "pattern": ".*update.*", "alwaysNotify": true, "comment": "包含 update 標記的版本總是通知"}}, "deploymentModes": {"normal": {"description": "一般部署模式", "versionFormat": "timestamp", "notifyUpdate": false, "example": "20250119_143000"}, "important": {"description": "重要更新部署模式", "versionFormat": "timestamp_with_marker", "notifyUpdate": true, "example": "20250119_143000_update"}, "release": {"description": "正式版本發布模式", "versionFormat": "semantic", "notifyUpdate": true, "example": "1.2.0"}}, "messages": {"updateAvailable": {"zh": "🚀 新版本可用", "en": "🚀 New Version Available"}, "updateDescription": {"zh": "重新載入以獲得最新功能", "en": "Reload to get the latest features"}, "updateNow": {"zh": "重新載入", "en": "Reload"}, "updateLater": {"zh": "稍後", "en": "Later"}, "updating": {"zh": "🔄 正在重新載入...", "en": "🔄 Reloading..."}}, "examples": {"normalDeploy": {"command": "./scripts/deploy_web.sh", "description": "一般部署，不會觸發更新通知"}, "importantUpdate": {"command": "./scripts/deploy_web.sh --notify-update", "description": "重要更新，會觸發用戶更新通知"}, "releaseVersion": {"command": "./scripts/deploy_web.sh --version=1.2.0", "description": "正式版本發布，使用語義化版本號"}}}