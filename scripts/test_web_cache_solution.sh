#!/bin/bash

# Web 快取解決方案測試腳本
# 用於驗證版本檢查和快取清除機制是否正常工作

set -e

echo "🧪 開始測試 Web 快取解決方案..."

# 檢查必要檔案是否存在
echo "📋 檢查必要檔案..."

required_files=(
    "web/simple_version_check.js"
    "web/sw_enhanced.js"
    "web/index.html"
    "firebase.json"
    "scripts/deploy_web.sh"
    "scripts/deploy_web_force_update.sh"
)

missing_files=()
for file in "${required_files[@]}"; do
    if [ ! -f "$file" ]; then
        missing_files+=("$file")
    else
        echo "✅ $file"
    fi
done

if [ ${#missing_files[@]} -ne 0 ]; then
    echo "❌ 缺少必要檔案："
    for file in "${missing_files[@]}"; do
        echo "   - $file"
    done
    exit 1
fi

# 檢查腳本執行權限
echo ""
echo "🔐 檢查腳本執行權限..."
if [ -x "scripts/deploy_web.sh" ]; then
    echo "✅ scripts/deploy_web.sh 有執行權限"
else
    echo "⚠️  scripts/deploy_web.sh 沒有執行權限，正在修復..."
    chmod +x scripts/deploy_web.sh
fi

if [ -x "scripts/deploy_web_force_update.sh" ]; then
    echo "✅ scripts/deploy_web_force_update.sh 有執行權限"
else
    echo "⚠️  scripts/deploy_web_force_update.sh 沒有執行權限，正在修復..."
    chmod +x scripts/deploy_web_force_update.sh
fi

# 檢查版本檢查腳本語法
echo ""
echo "🔍 檢查版本檢查腳本語法..."
if node -c web/simple_version_check.js 2>/dev/null; then
    echo "✅ simple_version_check.js 語法正確"
else
    echo "❌ simple_version_check.js 語法錯誤"
    exit 1
fi

if node -c web/sw_enhanced.js 2>/dev/null; then
    echo "✅ sw_enhanced.js 語法正確"
else
    echo "❌ sw_enhanced.js 語法錯誤"
    exit 1
fi

# 檢查 Firebase 配置
echo ""
echo "🔥 檢查 Firebase 配置..."
if command -v firebase &> /dev/null; then
    echo "✅ Firebase CLI 已安裝"
    
    # 檢查是否已登入
    if firebase projects:list &> /dev/null; then
        echo "✅ Firebase 已登入"
        
        # 檢查專案配置
        if [ -f ".firebaserc" ]; then
            echo "✅ Firebase 專案已配置"
        else
            echo "⚠️  Firebase 專案未配置"
        fi
    else
        echo "⚠️  Firebase 未登入，請執行 'firebase login'"
    fi
else
    echo "❌ Firebase CLI 未安裝，請執行 'npm install -g firebase-tools'"
fi

# 檢查 Flutter 環境
echo ""
echo "🐦 檢查 Flutter 環境..."
if command -v flutter &> /dev/null; then
    echo "✅ Flutter 已安裝"
    flutter --version | head -1
    
    # 檢查 Web 支援
    if flutter devices | grep -q "Chrome"; then
        echo "✅ Flutter Web 支援已啟用"
    else
        echo "⚠️  Flutter Web 支援未啟用，請執行 'flutter config --enable-web'"
    fi
else
    echo "❌ Flutter 未安裝"
    exit 1
fi

# 模擬建置測試（不實際建置）
echo ""
echo "🔨 模擬建置測試..."

# 檢查是否有 build/web 目錄
if [ -d "build/web" ]; then
    echo "✅ 找到現有的 build/web 目錄"
    
    # 檢查關鍵檔案
    if [ -f "build/web/index.html" ]; then
        echo "✅ index.html 存在"
    else
        echo "⚠️  index.html 不存在，需要重新建置"
    fi
    
    if [ -f "build/web/version.json" ]; then
        echo "✅ version.json 存在"
        echo "   當前版本：$(cat build/web/version.json | grep -o '"version":"[^"]*' | cut -d'"' -f4)"
    else
        echo "⚠️  version.json 不存在"
    fi
else
    echo "⚠️  build/web 目錄不存在，需要執行建置"
fi

# 測試版本號生成邏輯
echo ""
echo "📦 測試版本號生成邏輯..."

TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
ASSET_VERSION=$(date +"%Y%m%d%H%M%S")

echo "✅ 時間戳版本：$TIMESTAMP"
echo "✅ 資源版本：$ASSET_VERSION"
echo "✅ 重要更新版本：${TIMESTAMP}_update"
echo "✅ 強制更新版本：${TIMESTAMP}_force_update"

# 檢查網路連接
echo ""
echo "🌐 檢查網路連接..."
if ping -c 1 firebase.google.com &> /dev/null; then
    echo "✅ 可以連接到 Firebase"
else
    echo "⚠️  無法連接到 Firebase，請檢查網路連接"
fi

# 生成測試報告
echo ""
echo "📊 生成測試報告..."

cat > test_report.txt << EOF
Web 快取解決方案測試報告
生成時間：$(date)

檔案檢查：
$(for file in "${required_files[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file"
    else
        echo "❌ $file (缺少)"
    fi
done)

環境檢查：
- Flutter: $(command -v flutter &> /dev/null && echo "已安裝" || echo "未安裝")
- Firebase CLI: $(command -v firebase &> /dev/null && echo "已安裝" || echo "未安裝")
- Node.js: $(command -v node &> /dev/null && echo "已安裝" || echo "未安裝")

版本號測試：
- 時間戳版本：$TIMESTAMP
- 資源版本：$ASSET_VERSION
- 重要更新版本：${TIMESTAMP}_update
- 強制更新版本：${TIMESTAMP}_force_update

建議：
1. 如果所有檢查都通過，可以執行 './scripts/deploy_web.sh' 進行測試部署
2. 如果需要解決快取問題，執行 './scripts/deploy_web_force_update.sh'
3. 部署後使用無痕模式測試版本檢查功能
4. 監控瀏覽器控制台的版本檢查日誌

EOF

echo "✅ 測試報告已保存到 test_report.txt"

# 總結
echo ""
echo "🎉 測試完成！"
echo ""
echo "📋 下一步操作："
echo "   1. 查看測試報告：cat test_report.txt"
echo "   2. 執行測試部署：./scripts/deploy_web.sh --version=test_$(date +%H%M%S)"
echo "   3. 檢查部署結果：https://astreal.web.app"
echo "   4. 測試版本檢查：開啟瀏覽器控制台查看日誌"
echo ""

# 詢問是否執行測試部署
read -p "是否要執行測試部署？(y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🚀 開始測試部署..."
    ./scripts/deploy_web.sh --version="test_$(date +%H%M%S)"
else
    echo "✅ 測試完成，可以手動執行部署"
fi
