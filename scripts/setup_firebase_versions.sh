#!/bin/bash

# Firebase 版本信息設置腳本
# 使用方法: ./scripts/setup_firebase_versions.sh [選項]

set -e  # 遇到錯誤時退出

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 顯示標題
echo -e "${BLUE}🚀 Firebase 版本信息設置腳本${NC}"
echo -e "${BLUE}================================${NC}\n"

# 檢查是否在正確的目錄
if [ ! -f "pubspec.yaml" ]; then
    echo -e "${RED}❌ 錯誤：請在 Flutter 項目根目錄運行此腳本${NC}"
    exit 1
fi

# 檢查 Dart 是否可用
if ! command -v dart &> /dev/null; then
    echo -e "${RED}❌ 錯誤：未找到 Dart 命令，請確保 Flutter SDK 已正確安裝${NC}"
    exit 1
fi

# 顯示選項菜單
show_menu() {
    echo -e "${YELLOW}請選擇操作：${NC}"
    echo "1. 預覽配置（乾運行）"
    echo "2. 初始化版本信息（使用默認配置）"
    echo "3. 初始化版本信息（使用自定義配置）"
    echo "4. 創建示例配置文件"
    echo "5. 查看現有版本信息"
    echo "6. 退出"
    echo ""
}

# 預覽配置
preview_config() {
    echo -e "${BLUE}📋 預覽將要創建的版本信息...${NC}\n"
    
    if [ ! -f "scripts/version_config.json" ]; then
        echo -e "${RED}❌ 配置文件不存在，請先創建配置文件${NC}"
        return 1
    fi
    
    dart run scripts/init_versions_from_config.dart --dry-run
}

# 初始化版本信息（默認配置）
init_default() {
    echo -e "${BLUE}📱 使用默認配置初始化版本信息...${NC}\n"
    
    if [ ! -f "scripts/version_config.json" ]; then
        echo -e "${YELLOW}⚠️  配置文件不存在，使用內建配置${NC}"
        dart run scripts/init_versions_with_config.dart
    else
        dart run scripts/init_versions_from_config.dart
    fi
}

# 初始化版本信息（自定義配置）
init_custom() {
    echo -e "${YELLOW}📝 請輸入配置文件路徑：${NC}"
    read -r config_path
    
    if [ ! -f "$config_path" ]; then
        echo -e "${RED}❌ 配置文件不存在：$config_path${NC}"
        return 1
    fi
    
    echo -e "${BLUE}📱 使用自定義配置初始化版本信息...${NC}\n"
    dart run scripts/init_versions_from_config.dart --config="$config_path"
}

# 創建示例配置文件
create_example_config() {
    local target_file="scripts/my_version_config.json"
    
    if [ -f "$target_file" ]; then
        echo -e "${YELLOW}⚠️  文件已存在：$target_file${NC}"
        echo -e "${YELLOW}是否要覆蓋？(y/N)${NC}"
        read -r confirm
        if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
            echo -e "${BLUE}操作已取消${NC}"
            return 0
        fi
    fi
    
    cp scripts/version_config.json "$target_file"
    echo -e "${GREEN}✅ 示例配置文件已創建：$target_file${NC}"
    echo -e "${BLUE}💡 您可以編輯此文件來自定義版本信息${NC}"
}

# 查看現有版本信息
view_existing() {
    echo -e "${BLUE}🔍 查看現有版本信息...${NC}\n"
    
    # 這裡可以添加查看 Firestore 數據的腳本
    echo -e "${YELLOW}💡 請在 Firebase Console 中查看現有版本信息：${NC}"
    echo "https://console.firebase.google.com/project/astreal-d3f70/firestore/data/~2Fapp_versions"
}

# 檢查依賴
check_dependencies() {
    echo -e "${BLUE}🔍 檢查依賴...${NC}"
    
    # 檢查必要的包
    if ! grep -q "cloud_firestore:" pubspec.yaml; then
        echo -e "${RED}❌ 缺少依賴：cloud_firestore${NC}"
        echo -e "${YELLOW}💡 請運行：flutter pub add cloud_firestore${NC}"
        exit 1
    fi
    
    if ! grep -q "firebase_core:" pubspec.yaml; then
        echo -e "${RED}❌ 缺少依賴：firebase_core${NC}"
        echo -e "${YELLOW}💡 請運行：flutter pub add firebase_core${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ 依賴檢查通過${NC}\n"
}

# 主程序
main() {
    # 檢查依賴
    check_dependencies
    
    # 如果有命令行參數，直接執行
    case "$1" in
        "preview"|"--preview"|"-p")
            preview_config
            exit 0
            ;;
        "init"|"--init"|"-i")
            init_default
            exit 0
            ;;
        "help"|"--help"|"-h")
            echo "使用方法: $0 [選項]"
            echo ""
            echo "選項："
            echo "  preview, -p    預覽配置"
            echo "  init, -i       初始化版本信息"
            echo "  help, -h       顯示幫助"
            echo ""
            exit 0
            ;;
    esac
    
    # 互動模式
    while true; do
        show_menu
        echo -e "${YELLOW}請輸入選項 (1-6):${NC}"
        read -r choice
        
        case $choice in
            1)
                preview_config
                ;;
            2)
                init_default
                ;;
            3)
                init_custom
                ;;
            4)
                create_example_config
                ;;
            5)
                view_existing
                ;;
            6)
                echo -e "${GREEN}👋 再見！${NC}"
                exit 0
                ;;
            *)
                echo -e "${RED}❌ 無效選項，請輸入 1-6${NC}"
                ;;
        esac
        
        echo ""
        echo -e "${BLUE}按 Enter 繼續...${NC}"
        read -r
        echo ""
    done
}

# 運行主程序
main "$@"
