# 🚀 快速手動導入指南

由於自動化腳本遇到問題，這裡提供最簡單可靠的手動導入方式。

## 📋 步驟概覽

1. ✅ 數據文件已生成（在 `scripts/firebase_data/` 目錄中）
2. 🔗 打開 Firebase Console
3. 📝 手動創建文檔
4. ✅ 驗證結果

## 🔗 第一步：打開 Firebase Console

點擊這個連結直接打開 Firestore：
**[https://console.firebase.google.com/project/astreal-d3f70/firestore](https://console.firebase.google.com/project/astreal-d3f70/firestore)**

## 📝 第二步：創建 Collection 和文檔

### 1. 創建 Collection
- 點擊「開始收集」或「+ 開始收集」
- Collection ID 輸入：`app_versions`
- 點擊「下一步」

### 2. 創建第一個文檔（Android）
- 文檔 ID 輸入：`android`
- 添加以下字段：

| 字段名 | 類型 | 值 |
|--------|------|-----|
| `version` | string | `1.0.0` |
| `buildNumber` | number | `1` |
| `minRequiredVersion` | string | `1.0.0` |
| `minRequiredBuildNumber` | number | `1` |
| `forceUpdate` | boolean | `false` |
| `updateMessage` | string | `歡迎使用 Astreal 占星應用！這是我們的首個正式版本，專為 Android 設備優化，建議您更新以獲得最佳體驗。` |
| `updateUrl` | string | `https://play.google.com/store/apps/details?id=com.one.astreal` |
| `releaseDate` | string | `2025-01-17T10:00:00.000Z` |
| `features` | array | 見下方 |
| `isActive` | boolean | `true` |

**features 數組內容**（每個都是 string 類型）：
- `完整的本命盤分析功能`
- `多種星盤類型支持`
- `深入剖析`
- `專業的占星計算引擎`
- `詳細的行星和相位信息`
- `Android 原生界面`
- `觸控優化`
- `通知支援`

### 3. 創建其他平台文檔

重複上述步驟，創建以下文檔：

#### iOS 文檔
- 文檔 ID：`ios`
- 所有字段與 Android 相同，除了：
  - `updateMessage`: `歡迎使用 Astreal 占星應用！這是我們的首個正式版本，專為 iPhone 和 iPad 優化，建議您更新以獲得最佳體驗。`
  - `updateUrl`: `https://apps.apple.com/app/astreal/id123456789`
  - `features` 最後三項改為：`iOS 原生界面`、`觸控優化`、`Face ID 支援`

#### macOS 文檔
- 文檔 ID：`macos`
- 所有字段與 Android 相同，除了：
  - `updateMessage`: `歡迎使用 Astreal 占星應用！這是我們的首個正式版本，專為 Mac 桌面環境優化，建議您更新以獲得最佳體驗。`
  - `updateUrl`: `https://astreal.app/download/macos`
  - `features` 最後三項改為：`macOS 原生界面`、`鍵盤快捷鍵`、`多視窗支援`

#### Windows 文檔
- 文檔 ID：`windows`
- 所有字段與 Android 相同，除了：
  - `updateMessage`: `歡迎使用 Astreal 占星應用！這是我們的首個正式版本，專為 Windows 桌面環境優化，建議您更新以獲得最佳體驗。`
  - `updateUrl`: `https://astreal.app/download/windows`
  - `features` 最後三項改為：`Windows 原生界面`、`鍵盤快捷鍵`、`多視窗支援`

#### Web 文檔
- 文檔 ID：`web`
- 所有字段與 Android 相同，除了：
  - `updateMessage`: `歡迎使用 Astreal 占星應用！這是我們的首個正式版本，支援所有現代瀏覽器，建議您更新以獲得最佳體驗。`
  - `updateUrl`: `https://astreal.app`
  - `features` 最後三項改為：`響應式設計`、`跨瀏覽器支援`、`無需安裝`

## 📋 快速複製貼上版本

如果您想更快速地創建，可以使用 JSON 模式：

### Android JSON
```json
{
  "version": "1.0.0",
  "buildNumber": 1,
  "minRequiredVersion": "1.0.0",
  "minRequiredBuildNumber": 1,
  "forceUpdate": false,
  "updateMessage": "歡迎使用 Astreal 占星應用！這是我們的首個正式版本，專為 Android 設備優化，建議您更新以獲得最佳體驗。",
  "updateUrl": "https://play.google.com/store/apps/details?id=com.one.astreal",
  "releaseDate": "2025-01-17T10:00:00.000Z",
  "features": [
    "完整的本命盤分析功能",
    "多種星盤類型支持",
    "深入剖析",
    "專業的占星計算引擎",
    "詳細的行星和相位信息",
    "Android 原生界面",
    "觸控優化",
    "通知支援"
  ],
  "isActive": true
}
```

### 使用 JSON 模式創建文檔
1. 在 Firebase Console 中點擊「+ 添加文檔」
2. 文檔 ID 輸入平台名稱（如 `android`）
3. 點擊右上角的「{}」圖標切換到 JSON 模式
4. 複製貼上上面的 JSON 內容
5. 修改對應的 `updateMessage`、`updateUrl` 和 `features`
6. 點擊「保存」

## ✅ 第三步：驗證結果

創建完成後，您應該在 Firestore 中看到：

```
app_versions/
├── android/
├── ios/
├── macos/
├── windows/
└── web/
```

每個文檔都包含完整的版本信息字段。

## 🧪 第四步：測試

1. 運行您的 Flutter 應用
2. 檢查應用啟動時是否正常（應該顯示版本是最新的）
3. 在設定頁面測試「檢查更新」功能

## 🔒 第五步：設置安全規則

在 Firestore 的「規則」標籤中，確保安全規則如下：

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /app_versions/{platform} {
      allow read: if true;
      allow write: if false; // 只能通過管理後台更新
    }
  }
}
```

## 🎉 完成！

手動導入完成後，您的版本控管系統就可以正常工作了！

## 💡 後續更新

當您需要發布新版本時，只需要在 Firebase Console 中更新對應平台的文檔：

- **可選更新**：只更新 `version` 和 `buildNumber`
- **強制更新**：同時更新 `minRequiredVersion`、`minRequiredBuildNumber` 並設置 `forceUpdate: true`

## 🔧 如果遇到問題

1. 確認所有字段名稱拼寫正確
2. 確認數據類型正確（string、number、boolean、array）
3. 確認 `features` 是字符串數組
4. 檢查 Firebase Console 是否有錯誤提示

這種手動方式雖然需要一些時間，但是最可靠的，不會有任何技術問題！🚀
