#!/bin/bash

# 時區資料更新腳本
# 用於下載最新的 timezone-boundary-builder 資料

set -e

echo "🌍 時區資料更新工具"
echo "===================="

# 設定變數
ASSETS_DIR="../assets"
BACKUP_DIR="../assets/backup"
TEMP_DIR="/tmp/timezone_update"
CURRENT_VERSION="2025b"

# 可選的資料版本
declare -A VERSIONS=(
    ["complete"]="timezones.geojson.zip"
    ["1970"]="timezones-1970.geojson.zip" 
    ["now"]="timezones-now.geojson.zip"
)

# 顯示選項
echo "請選擇要下載的時區資料版本："
echo "1. 完整版 (timezones.geojson) - 約 200MB，包含所有時區"
echo "2. 1970年後版 (timezones-1970.geojson) - 約 150MB，1970年後相同的時區"
echo "3. 當前版 (timezones-now.geojson) - 約 100MB，當前相同的時區"
echo "4. 取消"

read -p "請輸入選項 (1-4): " choice

case $choice in
    1)
        VERSION_KEY="complete"
        echo "✅ 選擇完整版時區資料"
        ;;
    2)
        VERSION_KEY="1970"
        echo "✅ 選擇 1970年後版時區資料"
        ;;
    3)
        VERSION_KEY="now"
        echo "✅ 選擇當前版時區資料"
        ;;
    4)
        echo "❌ 取消更新"
        exit 0
        ;;
    *)
        echo "❌ 無效選項"
        exit 1
        ;;
esac

FILENAME=${VERSIONS[$VERSION_KEY]}
DOWNLOAD_URL="https://github.com/evansiroky/timezone-boundary-builder/releases/download/${CURRENT_VERSION}/${FILENAME}"

echo "📥 準備下載: $FILENAME"
echo "🔗 下載地址: $DOWNLOAD_URL"

# 創建必要的目錄
mkdir -p "$ASSETS_DIR"
mkdir -p "$BACKUP_DIR"
mkdir -p "$TEMP_DIR"

# 備份現有資料
if [ -f "$ASSETS_DIR/combined.json" ]; then
    echo "💾 備份現有時區資料..."
    cp "$ASSETS_DIR/combined.json" "$BACKUP_DIR/combined.json.backup.$(date +%Y%m%d_%H%M%S)"
    echo "✅ 備份完成"
fi

# 下載新資料
echo "📥 開始下載時區資料..."
cd "$TEMP_DIR"

if command -v wget >/dev/null 2>&1; then
    wget -O "$FILENAME" "$DOWNLOAD_URL"
elif command -v curl >/dev/null 2>&1; then
    curl -L -o "$FILENAME" "$DOWNLOAD_URL"
else
    echo "❌ 錯誤: 需要 wget 或 curl 來下載檔案"
    exit 1
fi

echo "✅ 下載完成"

# 解壓縮
echo "📦 解壓縮資料..."
if command -v unzip >/dev/null 2>&1; then
    unzip -o "$FILENAME"
else
    echo "❌ 錯誤: 需要 unzip 來解壓縮檔案"
    exit 1
fi

# 找到解壓後的 JSON 檔案
JSON_FILE=""
if [ -f "combined.json" ]; then
    JSON_FILE="combined.json"
elif [ -f "timezones.geojson" ]; then
    JSON_FILE="timezones.geojson"
elif [ -f "timezones-1970.geojson" ]; then
    JSON_FILE="timezones-1970.geojson"
elif [ -f "timezones-now.geojson" ]; then
    JSON_FILE="timezones-now.geojson"
else
    echo "❌ 錯誤: 找不到解壓後的 JSON 檔案"
    exit 1
fi

echo "📄 找到資料檔案: $JSON_FILE"

# 驗證 JSON 格式
echo "🔍 驗證資料格式..."
if command -v jq >/dev/null 2>&1; then
    if jq empty "$JSON_FILE" 2>/dev/null; then
        echo "✅ JSON 格式驗證通過"
    else
        echo "❌ 錯誤: JSON 格式無效"
        exit 1
    fi
else
    echo "⚠️ 警告: 未安裝 jq，跳過 JSON 格式驗證"
fi

# 檢查檔案大小
FILE_SIZE=$(du -h "$JSON_FILE" | cut -f1)
echo "📊 檔案大小: $FILE_SIZE"

# 檢查時區數量
if command -v jq >/dev/null 2>&1; then
    TIMEZONE_COUNT=$(jq '.features | length' "$JSON_FILE")
    echo "🌐 時區數量: $TIMEZONE_COUNT"
    
    # 檢查關鍵時區
    echo "🔍 檢查關鍵時區..."
    KEY_TIMEZONES=("Pacific/Honolulu" "Europe/Zurich" "Asia/Taipei" "America/New_York" "Europe/London")
    
    for tz in "${KEY_TIMEZONES[@]}"; do
        if jq -e ".features[] | select(.properties.tzid == \"$tz\")" "$JSON_FILE" >/dev/null; then
            echo "  ✅ $tz"
        else
            echo "  ❌ $tz (未找到)"
        fi
    done
fi

# 複製到 assets 目錄
echo "📁 更新應用資料..."
cp "$JSON_FILE" "$ASSETS_DIR/combined.json"
echo "✅ 資料更新完成"

# 清理臨時檔案
echo "🧹 清理臨時檔案..."
rm -rf "$TEMP_DIR"
echo "✅ 清理完成"

# 顯示完成信息
echo ""
echo "🎉 時區資料更新成功！"
echo "📊 統計信息:"
echo "  • 檔案大小: $FILE_SIZE"
if [ -n "$TIMEZONE_COUNT" ]; then
    echo "  • 時區數量: $TIMEZONE_COUNT"
fi
echo "  • 資料版本: $CURRENT_VERSION ($VERSION_KEY)"
echo "  • 更新時間: $(date)"
echo ""
echo "💡 建議:"
echo "  1. 重新啟動應用以載入新資料"
echo "  2. 運行測試驗證時區查詢功能"
echo "  3. 檢查應用效能是否受影響"
echo ""
echo "🔄 如需回滾，備份檔案位於: $BACKUP_DIR"
