import 'dart:convert';
import 'dart:io';

/// 簡單的 Firebase 版本信息初始化腳本
/// 
/// 此腳本生成 Firebase Firestore 導入用的 JSON 文件
/// 不依賴 Flutter，可以直接用 Dart 運行
/// 
/// 使用方法：
/// 1. 運行: dart run scripts/simple_init_versions.dart
/// 2. 將生成的 JSON 文件導入到 Firebase Console

void main(List<String> args) async {
  print('🚀 Firebase 版本信息生成器\n');

  // 解析命令行參數
  final outputDir = _getOutputDir(args);
  final isDryRun = args.contains('--dry-run');

  if (isDryRun) {
    print('🔍 乾運行模式：只顯示數據，不生成文件\n');
  }

  try {
    // 創建版本數據
    final versionData = _createAllPlatformVersions();

    if (isDryRun) {
      // 乾運行：只顯示數據
      _showVersionData(versionData);
      return;
    }

    // 確保輸出目錄存在
    final outputDirectory = Directory(outputDir);
    if (!await outputDirectory.exists()) {
      await outputDirectory.create(recursive: true);
    }

    // 生成文件
    await _generateFiles(versionData, outputDir);

    print('\n🎉 版本信息文件生成完成！');
    print('📁 輸出目錄: $outputDir');
    print('\n📝 下一步：');
    print('1. 打開 Firebase Console: https://console.firebase.google.com/project/astreal-d3f70/firestore');
    print('2. 進入 Firestore Database');
    print('3. 創建 Collection "app_versions"');
    print('4. 為每個平台創建文檔，並複製對應的 JSON 內容');
    
  } catch (e) {
    print('❌ 生成失敗: $e');
    _showHelp();
    exit(1);
  }
}

/// 獲取輸出目錄
String _getOutputDir(List<String> args) {
  for (final arg in args) {
    if (arg.startsWith('--output=')) {
      return arg.substring(9);
    }
  }
  return 'scripts/firebase_data';
}

/// 創建所有平台的版本數據
Map<String, Map<String, dynamic>> _createAllPlatformVersions() {
  final platforms = ['android', 'ios', 'macos', 'windows', 'web'];
  final versionData = <String, Map<String, dynamic>>{};

  for (final platform in platforms) {
    versionData[platform] = _createVersionData(platform);
  }

  return versionData;
}

/// 創建平台特定的版本數據
Map<String, dynamic> _createVersionData(String platform) {
  final now = DateTime.now();
  
  return {
    'version': '1.0.0',
    'buildNumber': 1,
    'minRequiredVersion': '1.0.0',
    'minRequiredBuildNumber': 1,
    'forceUpdate': false,
    'updateMessage': _getUpdateMessage(platform),
    'updateUrl': _getUpdateUrl(platform),
    'releaseDate': now.toIso8601String(),
    'features': _getFeatures(platform),
    'isActive': true,
  };
}

/// 獲取平台特定的更新訊息
String _getUpdateMessage(String platform) {
  const baseMessage = '歡迎使用 Astreal 占星應用！這是我們的首個正式版本，提供完整的占星功能。';
  
  switch (platform) {
    case 'android':
      return '$baseMessage專為 Android 設備優化，建議您更新以獲得最佳體驗。';
    case 'ios':
      return '$baseMessage專為 iPhone 和 iPad 優化，建議您更新以獲得最佳體驗。';
    case 'macos':
      return '$baseMessage專為 Mac 桌面環境優化，建議您更新以獲得最佳體驗。';
    case 'windows':
      return '$baseMessage專為 Windows 桌面環境優化，建議您更新以獲得最佳體驗。';
    case 'web':
      return '$baseMessage支援所有現代瀏覽器，建議您更新以獲得最佳體驗。';
    default:
      return '$baseMessage建議您更新以獲得最佳體驗。';
  }
}

/// 獲取平台特定的更新 URL
String _getUpdateUrl(String platform) {
  switch (platform) {
    case 'android':
      return 'https://play.google.com/store/apps/details?id=com.one.astreal';
    case 'ios':
      return 'https://apps.apple.com/app/astreal/id123456789';
    case 'macos':
      return 'https://astreal.app/download/macos';
    case 'windows':
      return 'https://astreal.app/download/windows';
    case 'web':
      return 'https://astreal.app';
    default:
      return 'https://astreal.app/download';
  }
}

/// 獲取平台特定的功能列表
List<String> _getFeatures(String platform) {
  const commonFeatures = [
    '完整的本命盤分析功能',
    '多種星盤類型支持',
    '深入剖析',
    '專業的占星計算引擎',
    '詳細的行星和相位信息',
  ];

  final platformFeatures = <String, List<String>>{
    'android': ['Android 原生界面', '觸控優化', '通知支援'],
    'ios': ['iOS 原生界面', '觸控優化', 'Face ID 支援'],
    'macos': ['macOS 原生界面', '鍵盤快捷鍵', '多視窗支援'],
    'windows': ['Windows 原生界面', '鍵盤快捷鍵', '多視窗支援'],
    'web': ['響應式設計', '跨瀏覽器支援', '無需安裝'],
  };

  return [
    ...commonFeatures,
    ...platformFeatures[platform] ?? [],
  ];
}

/// 顯示版本數據（乾運行模式）
void _showVersionData(Map<String, Map<String, dynamic>> versionData) {
  print('📋 將要生成的版本信息：\n');

  for (final platform in versionData.keys) {
    final data = versionData[platform]!;
    print('📱 $platform:');
    print('   版本: ${data['version']} (build ${data['buildNumber']})');
    print('   最低要求: ${data['minRequiredVersion']} (build ${data['minRequiredBuildNumber']})');
    print('   強制更新: ${data['forceUpdate']}');
    print('   更新 URL: ${data['updateUrl']}');
    print('   功能數量: ${(data['features'] as List).length}');
    print('');
  }
}

/// 生成文件
Future<void> _generateFiles(Map<String, Map<String, dynamic>> versionData, String outputDir) async {
  print('📁 正在生成文件到: $outputDir\n');

  // 生成每個平台的 JSON 文件
  for (final platform in versionData.keys) {
    final data = versionData[platform]!;
    final fileName = '$outputDir/${platform}_version.json';
    
    final file = File(fileName);
    final jsonString = const JsonEncoder.withIndent('  ').convert(data);
    
    await file.writeAsString(jsonString);
    print('✅ 已生成: ${platform}_version.json');
  }

  // 生成合併的 JSON 文件
  final allDataFile = File('$outputDir/all_versions.json');
  final allDataJson = const JsonEncoder.withIndent('  ').convert(versionData);
  await allDataFile.writeAsString(allDataJson);
  print('✅ 已生成: all_versions.json');

  // 生成導入說明文件
  await _generateImportInstructions(outputDir);
  print('✅ 已生成: import_instructions.md');

  // 生成示例更新配置
  await _generateExampleConfigs(outputDir);
  print('✅ 已生成: example_configs.json');
}

/// 生成導入說明文件
Future<void> _generateImportInstructions(String outputDir) async {
  final instructions = '''
# Firebase Firestore 導入說明

## 自動導入（推薦）

如果您有 Firebase CLI 和適當的權限，可以使用以下命令自動導入：

\`\`\`bash
# 安裝 Firebase CLI（如果尚未安裝）
npm install -g firebase-tools

# 登入 Firebase
firebase login

# 設置項目
firebase use astreal-d3f70

# 導入數據（需要先安裝 firestore-import 工具）
# npm install -g firestore-import
# firestore-import --accountCredentials path/to/service-account.json --backupFile all_versions.json
\`\`\`

## 手動導入

1. 打開 [Firebase Console](https://console.firebase.google.com/project/astreal-d3f70/firestore)
2. 進入 Firestore Database
3. 創建 Collection "app_versions"
4. 為每個平台創建文檔：

### Android 平台
- 文檔 ID: `android`
- 複製 `android_version.json` 的內容

### iOS 平台
- 文檔 ID: `ios`
- 複製 `ios_version.json` 的內容

### macOS 平台
- 文檔 ID: `macos`
- 複製 `macos_version.json` 的內容

### Windows 平台
- 文檔 ID: `windows`
- 複製 `windows_version.json` 的內容

### Web 平台
- 文檔 ID: `web`
- 複製 `web_version.json` 的內容

## 驗證導入

導入完成後，您應該在 Firestore 中看到：

\`\`\`
app_versions/
├── android/
├── ios/
├── macos/
├── windows/
└── web/
\`\`\`

每個文檔都應該包含完整的版本信息字段。

## 測試

1. 運行您的 Flutter 應用
2. 檢查應用啟動時是否正常檢查版本
3. 在設定頁面測試手動檢查更新功能

## 更新版本信息

要發布新版本時，只需更新對應平台文檔中的字段：

- **可選更新**: 更新 `version` 和 `buildNumber`，保持 `minRequiredVersion` 不變
- **強制更新**: 更新所有版本字段，並設置 `forceUpdate: true`
''';

  final file = File('$outputDir/import_instructions.md');
  await file.writeAsString(instructions);
}

/// 生成示例配置
Future<void> _generateExampleConfigs(String outputDir) async {
  final examples = {
    'optional_update_example': {
      'version': '1.0.1',
      'buildNumber': 2,
      'minRequiredVersion': '1.0.0',
      'minRequiredBuildNumber': 1,
      'forceUpdate': false,
      'updateMessage': '新版本可用！此版本包含了一些改進和新功能，建議您更新以獲得更好的使用體驗。',
      'updateUrl': 'https://play.google.com/store/apps/details?id=com.one.astreal',
      'releaseDate': DateTime.now().toIso8601String(),
      'features': [
        '新增星盤分享功能',
        '改進 AI 解讀準確性',
        '優化用戶界面',
        '修復小問題',
        '提升應用性能'
      ],
      'isActive': true,
    },
    'force_update_example': {
      'version': '1.1.0',
      'buildNumber': 3,
      'minRequiredVersion': '1.1.0',
      'minRequiredBuildNumber': 3,
      'forceUpdate': true,
      'updateMessage': '重要安全更新！此版本修復了關鍵的安全問題，必須立即更新才能繼續使用應用。',
      'updateUrl': 'https://play.google.com/store/apps/details?id=com.one.astreal',
      'releaseDate': DateTime.now().toIso8601String(),
      'features': [
        '修復重要安全漏洞',
        '提升應用穩定性',
        '優化性能表現',
        '修復已知問題'
      ],
      'isActive': true,
    }
  };

  final file = File('$outputDir/example_configs.json');
  final jsonString = const JsonEncoder.withIndent('  ').convert(examples);
  await file.writeAsString(jsonString);
}

/// 顯示幫助信息
void _showHelp() {
  print('\n💡 使用說明：');
  print('   dart run scripts/simple_init_versions.dart [選項]');
  print('');
  print('選項：');
  print('   --output=path/to/output    指定輸出目錄（默認：scripts/firebase_data）');
  print('   --dry-run                  只顯示將要生成的數據，不生成文件');
  print('');
  print('示例：');
  print('   dart run scripts/simple_init_versions.dart --dry-run');
  print('   dart run scripts/simple_init_versions.dart --output=my_output');
}
