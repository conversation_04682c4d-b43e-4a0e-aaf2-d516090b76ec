#!/usr/bin/env node

/**
 * 使用 REST API 的 Firebase Firestore 導入腳本
 * 
 * 此腳本使用 Firebase REST API 直接導入數據
 * 不需要額外的依賴，只需要 Node.js 內建模組
 * 
 * 使用方法：
 * 1. 獲取 Firebase Web API Key（在 Firebase Console 的項目設置中）
 * 2. 設置環境變量或直接修改腳本中的配置
 * 3. 運行腳本: node scripts/rest-import.js
 * 
 * 環境變量：
 * - FIREBASE_API_KEY: Firebase Web API Key
 * - FIREBASE_PROJECT_ID: Firebase 項目 ID
 */

const https = require('https');
const fs = require('fs').promises;
const path = require('path');

// 配置
const PROJECT_ID = process.env.FIREBASE_PROJECT_ID || 'astreal-d3f70';
const API_KEY = process.env.FIREBASE_API_KEY || 'AIzaSyAeCMo-vuea1Z6nS1_EwOygN8TOY3ncmMc';
const COLLECTION_NAME = 'app_versions';
const DATA_DIR = path.join(__dirname, 'firebase_data');
const PLATFORMS = ['android', 'ios', 'macos', 'windows', 'web'];

/**
 * 發送 HTTPS 請求
 */
function httpsRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = https.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsedData = responseData ? JSON.parse(responseData) : {};
          if (res.statusCode >= 200 && res.statusCode < 300) {
            resolve(parsedData);
          } else {
            reject(new Error(`HTTP ${res.statusCode}: ${parsedData.error?.message || responseData}`));
          }
        } catch (error) {
          reject(new Error(`解析響應失敗: ${error.message}`));
        }
      });
    });
    
    req.on('error', reject);
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

/**
 * 檢查數據文件
 */
async function checkDataFiles() {
  console.log('🔍 檢查數據文件...');
  
  try {
    await fs.access(DATA_DIR);
  } catch (error) {
    throw new Error(`數據目錄不存在: ${DATA_DIR}\n請先運行: dart run scripts/simple_init_versions.dart`);
  }

  const missingFiles = [];
  for (const platform of PLATFORMS) {
    const filePath = path.join(DATA_DIR, `${platform}_version.json`);
    try {
      await fs.access(filePath);
      console.log(`   ✅ ${platform}_version.json`);
    } catch (error) {
      missingFiles.push(`${platform}_version.json`);
    }
  }

  if (missingFiles.length > 0) {
    throw new Error(`缺少文件: ${missingFiles.join(', ')}\n請先運行: dart run scripts/simple_init_versions.dart`);
  }

  console.log('✅ 所有數據文件檢查完成\n');
}

/**
 * 讀取 JSON 文件
 */
async function readJsonFile(filePath) {
  try {
    const data = await fs.readFile(filePath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    throw new Error(`讀取文件失敗 ${filePath}: ${error.message}`);
  }
}

/**
 * 檢查 Firebase 連接
 */
async function checkFirebaseConnection() {
  console.log('🔍 檢查 Firebase 連接...');
  
  const options = {
    hostname: 'firestore.googleapis.com',
    port: 443,
    path: `/v1/projects/${PROJECT_ID}/databases/(default)/documents`,
    method: 'GET',
    headers: {
      'Content-Type': 'application/json'
    }
  };
  
  try {
    await httpsRequest(options);
    console.log('✅ Firebase 連接正常\n');
    return true;
  } catch (error) {
    console.error('❌ Firebase 連接失敗:', error.message);
    console.log('💡 請檢查：');
    console.log('1. 網路連接是否正常');
    console.log('2. 項目 ID 是否正確');
    console.log('3. Firestore 是否已啟用');
    return false;
  }
}

/**
 * 檢查現有文檔
 */
async function checkExistingDocuments() {
  console.log('🔍 檢查現有版本數據...');
  
  const options = {
    hostname: 'firestore.googleapis.com',
    port: 443,
    path: `/v1/projects/${PROJECT_ID}/databases/(default)/documents/${COLLECTION_NAME}`,
    method: 'GET',
    headers: {
      'Content-Type': 'application/json'
    }
  };
  
  try {
    const response = await httpsRequest(options);
    
    if (response.documents && response.documents.length > 0) {
      console.log('⚠️  發現現有版本數據：');
      for (const doc of response.documents) {
        const docId = doc.name.split('/').pop();
        const version = doc.fields.version?.stringValue || 'unknown';
        const buildNumber = doc.fields.buildNumber?.integerValue || 'unknown';
        console.log(`   📱 ${docId}: v${version} (build ${buildNumber})`);
      }
      
      // 詢問是否覆蓋
      const readline = require('readline');
      const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout
      });
      
      const answer = await new Promise(resolve => {
        rl.question('\n❓ 是否要覆蓋現有數據？(y/N): ', resolve);
      });
      rl.close();
      
      if (answer.toLowerCase() !== 'y' && answer.toLowerCase() !== 'yes') {
        console.log('❌ 操作已取消');
        process.exit(0);
      }
      
      console.log('✅ 將覆蓋現有數據\n');
    } else {
      console.log('✅ 未發現現有數據，可以安全導入\n');
    }
  } catch (error) {
    if (error.message.includes('404')) {
      console.log('✅ Collection 不存在，將創建新的\n');
    } else {
      console.warn('⚠️  檢查現有數據時發生錯誤:', error.message);
    }
  }
}

/**
 * 轉換數據為 Firestore 格式
 */
function convertToFirestoreFormat(data) {
  const firestoreData = { fields: {} };
  
  for (const [key, value] of Object.entries(data)) {
    if (typeof value === 'string') {
      firestoreData.fields[key] = { stringValue: value };
    } else if (typeof value === 'number') {
      firestoreData.fields[key] = { integerValue: value.toString() };
    } else if (typeof value === 'boolean') {
      firestoreData.fields[key] = { booleanValue: value };
    } else if (Array.isArray(value)) {
      firestoreData.fields[key] = {
        arrayValue: {
          values: value.map(item => ({ stringValue: item }))
        }
      };
    } else {
      firestoreData.fields[key] = { stringValue: JSON.stringify(value) };
    }
  }
  
  return firestoreData;
}

/**
 * 導入單個文檔
 */
async function importDocument(platform, data) {
  const firestoreData = convertToFirestoreFormat(data);
  
  const options = {
    hostname: 'firestore.googleapis.com',
    port: 443,
    path: `/v1/projects/${PROJECT_ID}/databases/(default)/documents/${COLLECTION_NAME}?documentId=${platform}`,
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(JSON.stringify(firestoreData))
    }
  };
  
  try {
    await httpsRequest(options, firestoreData);
    console.log(`   ✅ ${platform} 導入成功`);
    return true;
  } catch (error) {
    // 如果文檔已存在，嘗試更新
    if (error.message.includes('already exists')) {
      return await updateDocument(platform, data);
    }
    throw error;
  }
}

/**
 * 更新現有文檔
 */
async function updateDocument(platform, data) {
  const firestoreData = convertToFirestoreFormat(data);
  
  const options = {
    hostname: 'firestore.googleapis.com',
    port: 443,
    path: `/v1/projects/${PROJECT_ID}/databases/(default)/documents/${COLLECTION_NAME}/${platform}`,
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(JSON.stringify(firestoreData))
    }
  };
  
  await httpsRequest(options, firestoreData);
  console.log(`   ✅ ${platform} 更新成功`);
  return true;
}

/**
 * 導入所有版本數據
 */
async function importAllVersionData() {
  console.log('📱 開始導入版本數據...\n');
  
  const results = {};
  
  for (const platform of PLATFORMS) {
    try {
      console.log(`   正在處理 ${platform}...`);
      
      // 讀取 JSON 文件
      const filePath = path.join(DATA_DIR, `${platform}_version.json`);
      const versionData = await readJsonFile(filePath);
      
      // 導入到 Firestore
      await importDocument(platform, versionData);
      
      results[platform] = { success: true, data: versionData };
      
    } catch (error) {
      console.error(`   ❌ ${platform} 導入失敗:`, error.message);
      results[platform] = { success: false, error: error.message };
    }
  }
  
  return results;
}

/**
 * 驗證導入結果
 */
async function verifyImport() {
  console.log('\n🔍 驗證導入結果...\n');
  
  for (const platform of PLATFORMS) {
    try {
      const options = {
        hostname: 'firestore.googleapis.com',
        port: 443,
        path: `/v1/projects/${PROJECT_ID}/databases/(default)/documents/${COLLECTION_NAME}/${platform}`,
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      };
      
      const response = await httpsRequest(options);
      
      if (response.fields) {
        const version = response.fields.version?.stringValue || 'unknown';
        const buildNumber = response.fields.buildNumber?.integerValue || 'unknown';
        console.log(`   ✅ ${platform} 驗證成功 (v${version}, build ${buildNumber})`);
      } else {
        console.error(`   ❌ ${platform} 數據格式異常`);
      }
      
    } catch (error) {
      console.error(`   ❌ ${platform} 驗證失敗:`, error.message);
    }
  }
}

/**
 * 顯示完成信息
 */
function showCompletionInfo() {
  console.log('\n🎉 版本數據導入完成！\n');
  console.log('📝 下一步：');
  console.log('1. 在 Firebase Console 中確認數據：');
  console.log(`   https://console.firebase.google.com/project/${PROJECT_ID}/firestore/data/~2F${COLLECTION_NAME}`);
  console.log('2. 測試應用的版本檢查功能');
  console.log('3. 設置 Firestore 安全規則（如果尚未設置）');
}

/**
 * 主函數
 */
async function main() {
  console.log('🚀 REST API Firebase Firestore 導入腳本\n');
  console.log(`📋 項目 ID: ${PROJECT_ID}`);
  console.log(`🔑 API Key: ${API_KEY.substring(0, 10)}...`);
  console.log('');
  
  try {
    // 檢查命令行參數
    if (process.argv.includes('--help') || process.argv.includes('-h')) {
      console.log('使用方法：');
      console.log('  node scripts/rest-import.js');
      console.log('');
      console.log('環境變量：');
      console.log('  FIREBASE_PROJECT_ID: Firebase 項目 ID');
      console.log('  FIREBASE_API_KEY: Firebase Web API Key');
      return;
    }
    
    // 檢查配置
    if (!API_KEY || API_KEY === 'your-api-key') {
      throw new Error('請設置正確的 Firebase API Key\n環境變量: FIREBASE_API_KEY');
    }
    
    // 檢查數據文件
    await checkDataFiles();
    
    // 檢查 Firebase 連接
    if (!(await checkFirebaseConnection())) {
      throw new Error('Firebase 連接失敗');
    }
    
    // 檢查現有數據
    await checkExistingDocuments();
    
    // 導入版本數據
    const results = await importAllVersionData();
    
    // 驗證導入結果
    await verifyImport();
    
    // 顯示結果摘要
    const successCount = Object.values(results).filter(r => r.success).length;
    const totalCount = Object.keys(results).length;
    
    console.log(`\n📊 導入結果: ${successCount}/${totalCount} 成功`);
    
    if (successCount === totalCount) {
      showCompletionInfo();
    } else {
      console.log('\n⚠️  部分導入失敗，請檢查錯誤信息並重試');
    }
    
  } catch (error) {
    console.error('\n❌ 導入失敗:', error.message);
    console.log('\n💡 請檢查：');
    console.log('1. Firebase API Key 是否正確');
    console.log('2. 項目 ID 是否正確');
    console.log('3. Firestore 是否已啟用');
    console.log('4. 網路連接是否正常');
    process.exit(1);
  }
}

// 運行主函數
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  checkDataFiles,
  importAllVersionData,
  verifyImport
};
