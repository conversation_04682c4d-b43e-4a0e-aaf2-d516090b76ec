#!/bin/bash

# Web 導航解決方案測試腳本
# 用於驗證網頁版返回按鈕處理是否正常工作

set -e

echo "🧪 開始測試 Web 導航解決方案..."

# 檢查必要檔案是否存在
echo "📋 檢查必要檔案..."

required_files=(
    "lib/core/utils/web_navigation_helper.dart"
    "lib/core/utils/web_navigation_helper_stub.dart"
    "lib/shared/widgets/web_aware_app_bar.dart"
    "lib/shared/widgets/web_aware_pop_scope.dart"
    "lib/main.dart"
)

missing_files=()
for file in "${required_files[@]}"; do
    if [ ! -f "$file" ]; then
        missing_files+=("$file")
    else
        echo "✅ $file"
    fi
done

if [ ${#missing_files[@]} -ne 0 ]; then
    echo "❌ 缺少必要檔案："
    for file in "${missing_files[@]}"; do
        echo "   - $file"
    done
    exit 1
fi

# 檢查 main.dart 中的初始化
echo ""
echo "🔍 檢查 main.dart 中的初始化..."
if grep -q "WebNavigationHelper.initialize()" lib/main.dart; then
    echo "✅ WebNavigationHelper.initialize() 已添加到 main.dart"
else
    echo "❌ WebNavigationHelper.initialize() 未在 main.dart 中找到"
    exit 1
fi

if grep -q "import.*web_navigation_helper" lib/main.dart; then
    echo "✅ WebNavigationHelper 已導入到 main.dart"
else
    echo "❌ WebNavigationHelper 未導入到 main.dart"
    exit 1
fi

# 檢查 MainScreen 是否使用了 WebAwarePopScope
echo ""
echo "🔍 檢查 MainScreen 是否使用了 WebAwarePopScope..."
if grep -q "WebAwarePopScope" lib/main.dart; then
    echo "✅ MainScreen 已使用 WebAwarePopScope"
else
    echo "❌ MainScreen 未使用 WebAwarePopScope"
    exit 1
fi

# 檢查語法錯誤
echo ""
echo "🔍 檢查 Dart 語法..."
if dart analyze --fatal-infos lib/core/utils/web_navigation_helper.dart 2>/dev/null; then
    echo "✅ web_navigation_helper.dart 語法正確"
else
    echo "❌ web_navigation_helper.dart 語法錯誤"
    dart analyze lib/core/utils/web_navigation_helper.dart
    exit 1
fi

if dart analyze --fatal-infos lib/shared/widgets/web_aware_app_bar.dart 2>/dev/null; then
    echo "✅ web_aware_app_bar.dart 語法正確"
else
    echo "❌ web_aware_app_bar.dart 語法錯誤"
    dart analyze lib/shared/widgets/web_aware_app_bar.dart
    exit 1
fi

if dart analyze --fatal-infos lib/shared/widgets/web_aware_pop_scope.dart 2>/dev/null; then
    echo "✅ web_aware_pop_scope.dart 語法正確"
else
    echo "❌ web_aware_pop_scope.dart 語法錯誤"
    dart analyze lib/shared/widgets/web_aware_pop_scope.dart
    exit 1
fi

# 檢查 Flutter 環境
echo ""
echo "🐦 檢查 Flutter 環境..."
if command -v flutter &> /dev/null; then
    echo "✅ Flutter 已安裝"
    flutter --version | head -1
    
    # 檢查 Web 支援
    if flutter devices | grep -q "Chrome"; then
        echo "✅ Flutter Web 支援已啟用"
    else
        echo "⚠️  Flutter Web 支援未啟用，請執行 'flutter config --enable-web'"
    fi
else
    echo "❌ Flutter 未安裝"
    exit 1
fi

# 測試編譯
echo ""
echo "🔨 測試 Web 編譯..."
if flutter build web --no-pub --no-tree-shake-icons --dart-define=FLUTTER_WEB_USE_SKIA=true 2>/dev/null; then
    echo "✅ Web 編譯成功"
else
    echo "❌ Web 編譯失敗"
    echo "正在嘗試詳細編譯以查看錯誤..."
    flutter build web --no-pub --no-tree-shake-icons --dart-define=FLUTTER_WEB_USE_SKIA=true
    exit 1
fi

# 檢查編譯產物
echo ""
echo "📦 檢查編譯產物..."
if [ -f "build/web/index.html" ]; then
    echo "✅ index.html 已生成"
else
    echo "❌ index.html 未生成"
    exit 1
fi

if [ -f "build/web/main.dart.js" ]; then
    echo "✅ main.dart.js 已生成"
else
    echo "❌ main.dart.js 未生成"
    exit 1
fi

# 檢查 Web 組件是否正確編譯
echo ""
echo "🔍 檢查 Web 組件編譯..."
if grep -q "WebNavigationHelper" build/web/main.dart.js; then
    echo "✅ WebNavigationHelper 已編譯到 JS 中"
else
    echo "⚠️  WebNavigationHelper 可能未正確編譯（這在生產模式下是正常的）"
fi

# 生成測試報告
echo ""
echo "📊 生成測試報告..."

cat > web_navigation_test_report.txt << EOF
Web 導航解決方案測試報告
生成時間：$(date)

檔案檢查：
$(for file in "${required_files[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file"
    else
        echo "❌ $file (缺少)"
    fi
done)

初始化檢查：
- WebNavigationHelper.initialize(): $(grep -q "WebNavigationHelper.initialize()" lib/main.dart && echo "✅ 已添加" || echo "❌ 未添加")
- WebNavigationHelper 導入: $(grep -q "import.*web_navigation_helper" lib/main.dart && echo "✅ 已導入" || echo "❌ 未導入")
- MainScreen WebAwarePopScope: $(grep -q "WebAwarePopScope" lib/main.dart && echo "✅ 已使用" || echo "❌ 未使用")

環境檢查：
- Flutter: $(command -v flutter &> /dev/null && echo "已安裝" || echo "未安裝")
- Flutter Web: $(flutter devices | grep -q "Chrome" && echo "已啟用" || echo "未啟用")

編譯檢查：
- Web 編譯: $([ -f "build/web/index.html" ] && echo "✅ 成功" || echo "❌ 失敗")
- JS 生成: $([ -f "build/web/main.dart.js" ] && echo "✅ 成功" || echo "❌ 失敗")

測試建議：
1. 在瀏覽器中開啟 build/web/index.html
2. 導航到不同頁面
3. 測試瀏覽器返回按鈕
4. 檢查瀏覽器控制台是否有錯誤
5. 測試手機版本的返回按鈕

已知問題：
- 在開發模式下，熱重載可能會重置導航狀態
- 某些瀏覽器可能需要用戶手勢才能修改歷史記錄

EOF

echo "✅ 測試報告已保存到 web_navigation_test_report.txt"

# 提供測試建議
echo ""
echo "🎉 測試完成！"
echo ""
echo "📋 下一步測試："
echo "   1. 查看測試報告：cat web_navigation_test_report.txt"
echo "   2. 啟動開發服務器：flutter run -d chrome"
echo "   3. 測試返回按鈕：導航到不同頁面並按瀏覽器返回按鈕"
echo "   4. 檢查控制台：開啟瀏覽器開發者工具查看日誌"
echo ""

# 詢問是否啟動開發服務器
read -p "是否要啟動 Flutter Web 開發服務器進行測試？(y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🚀 啟動 Flutter Web 開發服務器..."
    echo "📝 測試步驟："
    echo "   1. 等待應用載入完成"
    echo "   2. 導航到星盤頁面或其他頁面"
    echo "   3. 按瀏覽器的返回按鈕"
    echo "   4. 檢查是否正確返回上一頁（而不是啟動畫面）"
    echo "   5. 開啟瀏覽器控制台查看 WebNavigationHelper 的日誌"
    echo ""
    flutter run -d chrome --web-port=8080
else
    echo "✅ 測試完成，可以手動啟動開發服務器進行測試"
    echo "   執行：flutter run -d chrome"
fi
