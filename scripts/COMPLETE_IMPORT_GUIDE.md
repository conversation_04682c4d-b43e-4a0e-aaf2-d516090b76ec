# 🚀 完整的 Firebase Firestore 導入指南

## 📋 概覽

本指南提供了多種方式來將 app_versions 相關的 JSON 數據導入到 Firebase Firestore，適應不同的環境和需求。

## 🎯 可用的導入方式

### 1. 🤖 智能選擇器（最推薦）

**文件**: `import-selector.js`

自動檢測環境並推薦最佳導入方式。

```bash
# 前提：安裝 Node.js
# macOS: brew install node
# Ubuntu: sudo apt install nodejs npm
# Windows: 下載安裝包 https://nodejs.org

# 使用方法
node scripts/import-selector.js
```

### 2. 🔧 Firebase Admin SDK（最可靠）

**文件**: `import-to-firestore.js`

使用官方 Firebase Admin SDK，最穩定可靠。

```bash
# 安裝依賴
cd scripts
npm install firebase-admin

# 設置服務帳戶金鑰
export GOOGLE_APPLICATION_CREDENTIALS="path/to/service-account-key.json"

# 執行導入
node import-to-firestore.js
```

### 3. 🛠️ Firebase CLI（最簡單）

**文件**: `simple-import.js`

使用 Firebase CLI 工具，簡單易用。

```bash
# 安裝 Firebase CLI
npm install -g firebase-tools

# 登入並設置項目
firebase login
firebase use astreal-d3f70

# 執行導入
node scripts/simple-import.js
```

### 4. 🌐 REST API（無依賴）

**文件**: `rest-import.js`

直接使用 Firebase REST API，無需額外依賴。

```bash
# 設置 API Key
export FIREBASE_API_KEY="AIzaSyAeCMo-vuea1Z6nS1_EwOygN8TOY3ncmMc"

# 執行導入
node scripts/rest-import.js
```

### 5. 📝 手動導入（總是可用）

當自動化方式都不可用時的備選方案。

```bash
# 生成手動導入說明
node scripts/simple-import.js --manual

# 或者直接查看說明文件
cat scripts/firebase_data/import_instructions.md
```

## 🔄 完整流程

### 步驟 1: 生成數據文件

```bash
# 使用 Dart 腳本生成 JSON 文件
dart run scripts/simple_init_versions.dart

# 預覽數據（可選）
dart run scripts/simple_init_versions.dart --dry-run
```

### 步驟 2: 選擇導入方式

根據您的環境選擇合適的導入方式：

#### 如果您有 Node.js：
```bash
# 使用智能選擇器（推薦）
node scripts/import-selector.js
```

#### 如果您有 Firebase CLI：
```bash
# 檢查 Firebase CLI
firebase --version
firebase use

# 使用 CLI 導入
node scripts/simple-import.js
```

#### 如果您有服務帳戶金鑰：
```bash
# 使用 Admin SDK
cd scripts
npm install firebase-admin
node import-to-firestore.js
```

#### 如果您只有 API Key：
```bash
# 使用 REST API
node scripts/rest-import.js
```

#### 如果都沒有：
```bash
# 手動導入
node scripts/simple-import.js --manual
```

### 步驟 3: 驗證導入結果

1. 打開 [Firebase Console](https://console.firebase.google.com/project/astreal-d3f70/firestore)
2. 檢查 `app_versions` collection 是否存在
3. 確認每個平台（android, ios, macos, windows, web）都有對應的文檔
4. 驗證數據格式正確

## 🔧 環境準備

### Node.js 安裝

```bash
# macOS (使用 Homebrew)
brew install node

# Ubuntu/Debian
sudo apt update
sudo apt install nodejs npm

# CentOS/RHEL
sudo yum install nodejs npm

# Windows
# 下載安裝包: https://nodejs.org
```

### Firebase CLI 安裝

```bash
# 使用 npm 安裝
npm install -g firebase-tools

# 登入 Firebase
firebase login

# 設置項目
firebase use astreal-d3f70

# 驗證設置
firebase projects:list
```

### 服務帳戶金鑰設置

1. 打開 [Firebase Console](https://console.firebase.google.com/project/astreal-d3f70/settings/serviceaccounts/adminsdk)
2. 點擊「生成新的私密金鑰」
3. 下載 JSON 文件
4. 設置環境變量：
   ```bash
   export GOOGLE_APPLICATION_CREDENTIALS="/path/to/service-account-key.json"
   ```

### API Key 獲取

1. 打開 [Firebase Console](https://console.firebase.google.com/project/astreal-d3f70/settings/general)
2. 在「您的應用程式」區域找到 Web API Key
3. 複製 API Key
4. 設置環境變量：
   ```bash
   export FIREBASE_API_KEY="your-api-key"
   ```

## 📊 導入方式比較

| 方式 | 優點 | 缺點 | 適用場景 |
|------|------|------|----------|
| 智能選擇器 | 自動選擇、易用 | 需要 Node.js | 所有場景 |
| Admin SDK | 最可靠、功能完整 | 需要服務帳戶金鑰 | 生產環境 |
| Firebase CLI | 簡單、官方工具 | 需要登入 | 開發環境 |
| REST API | 無額外依賴 | 功能有限 | CI/CD 環境 |
| 手動導入 | 總是可用 | 需要手動操作 | 備選方案 |

## 🔍 故障排除

### 常見錯誤及解決方案

#### 1. 數據文件不存在
```
❌ 數據目錄不存在: scripts/firebase_data
```
**解決方案**:
```bash
dart run scripts/simple_init_versions.dart
```

#### 2. Node.js 未安裝
```
❌ command not found: node
```
**解決方案**:
```bash
# macOS
brew install node

# Ubuntu
sudo apt install nodejs npm
```

#### 3. Firebase CLI 未安裝
```
❌ Firebase CLI 未安裝或不可用
```
**解決方案**:
```bash
npm install -g firebase-tools
firebase login
firebase use astreal-d3f70
```

#### 4. 權限錯誤
```
❌ Firebase 初始化失敗: 權限不足
```
**解決方案**:
- 檢查服務帳戶金鑰是否正確
- 確認 Firestore 已啟用
- 檢查安全規則設置

#### 5. 網路連接問題
```
❌ Firebase 連接失敗
```
**解決方案**:
- 檢查網路連接
- 確認防火牆設置
- 嘗試使用 VPN

## 🎯 推薦流程

### 首次使用者
```bash
# 1. 生成數據
dart run scripts/simple_init_versions.dart

# 2. 智能導入
node scripts/import-selector.js
```

### 開發者
```bash
# 使用 Firebase CLI
firebase login
firebase use astreal-d3f70
node scripts/simple-import.js
```

### DevOps/CI
```bash
# 使用 REST API
export FIREBASE_API_KEY="your-api-key"
node scripts/rest-import.js
```

### 系統管理員
```bash
# 使用 Admin SDK
export GOOGLE_APPLICATION_CREDENTIALS="service-account.json"
cd scripts && npm install firebase-admin
node import-to-firestore.js
```

## 📚 相關文檔

- [Node.js 導入腳本詳細說明](NODEJS_IMPORT_README.md)
- [Dart 腳本使用說明](README.md)
- [Firebase 設置說明](../docs/firebase_version_setup.md)
- [版本控管使用說明](../docs/version_control_usage.md)

## 🎉 總結

這套完整的導入工具提供了：

- ✅ **多種導入方式** - 適應不同環境和技術棧
- ✅ **智能選擇** - 自動推薦最佳方案
- ✅ **完整文檔** - 詳細的使用說明和故障排除
- ✅ **備選方案** - 手動導入作為最後保障
- ✅ **環境檢測** - 自動檢測配置和依賴

無論您的技術背景如何，都能找到適合的導入方式！🚀

## 🔗 快速連結

- [Firebase Console](https://console.firebase.google.com/project/astreal-d3f70/firestore)
- [Node.js 下載](https://nodejs.org)
- [Firebase CLI 文檔](https://firebase.google.com/docs/cli)
- [Firebase Admin SDK 文檔](https://firebase.google.com/docs/admin/setup)
