#!/bin/bash

# 快速部署腳本 - 解決 Flutter Web 快取問題
# 使用方法: ./scripts/quick_deploy.sh

set -e

echo "🚀 開始快速部署流程..."

# 檢查是否在正確的目錄
if [ ! -f "pubspec.yaml" ]; then
    echo "❌ 錯誤：請在 Flutter 專案根目錄執行此腳本"
    exit 1
fi

# 生成版本號
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BUILD_VERSION="${TIMESTAMP}_quick"
ASSET_VERSION=$(date +"%Y%m%d%H%M%S")

echo "📦 快速部署版本: $BUILD_VERSION"

# 清理並建置
echo "🧹 清理舊檔案..."
rm -rf build/web

echo "🔨 建置 Flutter Web..."
flutter build web --release --pwa-strategy=none

# 更新版本資訊
echo "🔄 更新版本資訊..."
cd build/web

# 替換版本號
if [[ "$OSTYPE" == "darwin"* ]]; then
    sed -i '' "s/{{BUILD_VERSION}}/$BUILD_VERSION/g" index.html
    sed -i '' "s/{{BUILD_VERSION}}/$BUILD_VERSION/g" simple_version_check.js
else
    sed -i "s/{{BUILD_VERSION}}/$BUILD_VERSION/g" index.html
    sed -i "s/{{BUILD_VERSION}}/$BUILD_VERSION/g" simple_version_check.js
fi

# 生成版本文件
cat > version.json << EOF
{
  "version": "$BUILD_VERSION",
  "buildNumber": $(date +%s),
  "buildTime": "$(date -u +"%Y-%m-%dT%H:%M:%S.%3NZ")",
  "assetVersion": "$ASSET_VERSION",
  "description": "AstReal 占星應用 - 快速部署",
  "forceCacheClear": false,
  "updateType": "normal",
  "updateMessage": "新版本已發布，請重新載入以獲得最新功能",
  "features": [
    "快速部署更新",
    "效能優化",
    "使用者體驗改善"
  ],
  "isActive": true
}
EOF

# 添加快取破壞參數
find . -name "*.html" -type f -exec sed -i.bak "s/\\.js\"/\\.js?v=$ASSET_VERSION\"/g" {} \;
find . -name "*.html" -type f -exec sed -i.bak "s/\\.css\"/\\.css?v=$ASSET_VERSION\"/g" {} \;

# 清理備份檔案
find . -name "*.bak" -delete

cd ../..

# 部署
echo "🚀 部署到 Firebase Hosting..."
firebase deploy --only hosting:astreal

echo ""
echo "✅ 快速部署完成！"
echo "🌐 版本: $BUILD_VERSION"
echo "🔗 網址: https://astreal.web.app"
echo ""
echo "💡 提示："
echo "   - 如果看不到更新，請按 Ctrl+Shift+R (或 Cmd+Shift+R) 強制重新載入"
echo "   - 或使用無痕模式開啟網站"
echo "   - 版本檢查器會在 1 分鐘後開始檢查更新"
