#!/usr/bin/env node

/**
 * Firebase Firestore 版本信息導入腳本
 * 
 * 使用方法：
 * 1. 安裝依賴：npm install firebase-admin
 * 2. 設置服務帳戶金鑰（見下方說明）
 * 3. 運行腳本：node scripts/import-to-firestore.js
 * 
 * 環境變量：
 * - GOOGLE_APPLICATION_CREDENTIALS: 服務帳戶金鑰文件路徑
 * - FIREBASE_PROJECT_ID: Firebase 項目 ID（默認：astreal-d3f70）
 */

const admin = require('firebase-admin');
const fs = require('fs').promises;
const path = require('path');

// Firebase 配置
const PROJECT_ID = process.env.FIREBASE_PROJECT_ID || 'astreal-d3f70';
const COLLECTION_NAME = 'app_versions';
const DATA_DIR = path.join(__dirname, 'firebase_data');

// 平台列表
const PLATFORMS = ['android', 'ios', 'macos', 'windows', 'web'];

/**
 * 初始化 Firebase Admin SDK
 */
function initializeFirebase() {
  try {
    // 檢查是否已經初始化
    if (admin.apps.length === 0) {
      // 如果設置了服務帳戶金鑰文件
      if (process.env.GOOGLE_APPLICATION_CREDENTIALS) {
        admin.initializeApp({
          credential: admin.credential.applicationDefault(),
          projectId: PROJECT_ID
        });
      } else {
        // 使用項目 ID 初始化（需要在有權限的環境中運行）
        admin.initializeApp({
          projectId: PROJECT_ID
        });
      }
    }
    
    console.log('✅ Firebase Admin SDK 初始化成功');
    console.log(`📋 項目 ID: ${PROJECT_ID}`);
    return admin.firestore();
  } catch (error) {
    console.error('❌ Firebase 初始化失敗:', error.message);
    console.log('\n💡 請確保：');
    console.log('1. 已安裝 firebase-admin: npm install firebase-admin');
    console.log('2. 已設置服務帳戶金鑰或在有權限的環境中運行');
    console.log('3. 項目 ID 正確');
    process.exit(1);
  }
}

/**
 * 讀取 JSON 文件
 */
async function readJsonFile(filePath) {
  try {
    const data = await fs.readFile(filePath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    throw new Error(`讀取文件失敗 ${filePath}: ${error.message}`);
  }
}

/**
 * 檢查數據目錄和文件
 */
async function checkDataFiles() {
  console.log('🔍 檢查數據文件...');
  
  try {
    await fs.access(DATA_DIR);
  } catch (error) {
    throw new Error(`數據目錄不存在: ${DATA_DIR}\n請先運行: dart run scripts/simple_init_versions.dart`);
  }

  const missingFiles = [];
  for (const platform of PLATFORMS) {
    const filePath = path.join(DATA_DIR, `${platform}_version.json`);
    try {
      await fs.access(filePath);
      console.log(`   ✅ ${platform}_version.json`);
    } catch (error) {
      missingFiles.push(`${platform}_version.json`);
    }
  }

  if (missingFiles.length > 0) {
    throw new Error(`缺少文件: ${missingFiles.join(', ')}\n請先運行: dart run scripts/simple_init_versions.dart`);
  }

  console.log('✅ 所有數據文件檢查完成\n');
}

/**
 * 檢查現有數據
 */
async function checkExistingData(db) {
  console.log('🔍 檢查現有版本數據...');
  
  try {
    const snapshot = await db.collection(COLLECTION_NAME).get();
    
    if (!snapshot.empty) {
      console.log('⚠️  發現現有版本數據：');
      snapshot.forEach(doc => {
        const data = doc.data();
        console.log(`   📱 ${doc.id}: v${data.version} (build ${data.buildNumber})`);
      });
      
      // 詢問是否覆蓋
      const readline = require('readline');
      const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout
      });
      
      const answer = await new Promise(resolve => {
        rl.question('\n❓ 是否要覆蓋現有數據？(y/N): ', resolve);
      });
      rl.close();
      
      if (answer.toLowerCase() !== 'y' && answer.toLowerCase() !== 'yes') {
        console.log('❌ 操作已取消');
        process.exit(0);
      }
      
      // 刪除現有數據
      console.log('\n🗑️  正在清除現有數據...');
      const batch = db.batch();
      snapshot.forEach(doc => {
        batch.delete(doc.ref);
      });
      await batch.commit();
      console.log('✅ 現有數據已清除\n');
    } else {
      console.log('✅ 未發現現有數據，可以安全導入\n');
    }
  } catch (error) {
    console.warn('⚠️  檢查現有數據時發生錯誤:', error.message);
  }
}

/**
 * 導入版本數據
 */
async function importVersionData(db) {
  console.log('📱 開始導入版本數據...\n');
  
  const batch = db.batch();
  const importedData = {};
  
  for (const platform of PLATFORMS) {
    try {
      console.log(`   正在處理 ${platform}...`);
      
      // 讀取 JSON 文件
      const filePath = path.join(DATA_DIR, `${platform}_version.json`);
      const versionData = await readJsonFile(filePath);
      
      // 驗證數據格式
      validateVersionData(versionData, platform);
      
      // 添加到批次操作
      const docRef = db.collection(COLLECTION_NAME).doc(platform);
      batch.set(docRef, versionData);
      
      importedData[platform] = versionData;
      console.log(`   ✅ ${platform} 數據準備完成`);
      
    } catch (error) {
      console.error(`   ❌ ${platform} 處理失敗:`, error.message);
      throw error;
    }
  }
  
  // 執行批次寫入
  console.log('\n💾 正在寫入到 Firestore...');
  await batch.commit();
  console.log('✅ 所有數據寫入完成\n');
  
  return importedData;
}

/**
 * 驗證版本數據格式
 */
function validateVersionData(data, platform) {
  const requiredFields = [
    'version', 'buildNumber', 'minRequiredVersion', 'minRequiredBuildNumber',
    'forceUpdate', 'updateMessage', 'updateUrl', 'releaseDate', 'features', 'isActive'
  ];
  
  for (const field of requiredFields) {
    if (!(field in data)) {
      throw new Error(`${platform} 缺少必要字段: ${field}`);
    }
  }
  
  // 驗證版本號格式
  if (!/^\d+\.\d+\.\d+$/.test(data.version)) {
    throw new Error(`${platform} 版本號格式錯誤: ${data.version}`);
  }
  
  if (!/^\d+\.\d+\.\d+$/.test(data.minRequiredVersion)) {
    throw new Error(`${platform} 最低版本號格式錯誤: ${data.minRequiredVersion}`);
  }
  
  // 驗證構建號
  if (!Number.isInteger(data.buildNumber) || data.buildNumber <= 0) {
    throw new Error(`${platform} 構建號必須是正整數: ${data.buildNumber}`);
  }
  
  if (!Number.isInteger(data.minRequiredBuildNumber) || data.minRequiredBuildNumber <= 0) {
    throw new Error(`${platform} 最低構建號必須是正整數: ${data.minRequiredBuildNumber}`);
  }
  
  // 驗證功能列表
  if (!Array.isArray(data.features)) {
    throw new Error(`${platform} features 必須是數組`);
  }
}

/**
 * 驗證導入結果
 */
async function verifyImport(db, importedData) {
  console.log('🔍 驗證導入結果...\n');
  
  for (const platform of PLATFORMS) {
    try {
      const doc = await db.collection(COLLECTION_NAME).doc(platform).get();
      
      if (!doc.exists) {
        console.error(`   ❌ ${platform} 文檔不存在`);
        continue;
      }
      
      const firestoreData = doc.data();
      const originalData = importedData[platform];
      
      // 簡單驗證關鍵字段
      if (firestoreData.version === originalData.version &&
          firestoreData.buildNumber === originalData.buildNumber) {
        console.log(`   ✅ ${platform} 驗證成功 (v${firestoreData.version})`);
      } else {
        console.error(`   ❌ ${platform} 數據不匹配`);
      }
      
    } catch (error) {
      console.error(`   ❌ ${platform} 驗證失敗:`, error.message);
    }
  }
}

/**
 * 顯示完成信息
 */
function showCompletionInfo() {
  console.log('\n🎉 版本數據導入完成！\n');
  console.log('📝 下一步：');
  console.log('1. 在 Firebase Console 中確認數據：');
  console.log(`   https://console.firebase.google.com/project/${PROJECT_ID}/firestore/data/~2F${COLLECTION_NAME}`);
  console.log('2. 測試應用的版本檢查功能');
  console.log('3. 設置 Firestore 安全規則（如果尚未設置）\n');
  
  console.log('🔒 建議的安全規則：');
  console.log('```javascript');
  console.log('rules_version = \'2\';');
  console.log('service cloud.firestore {');
  console.log('  match /databases/{database}/documents {');
  console.log(`    match /${COLLECTION_NAME}/{platform} {`);
  console.log('      allow read: if true;');
  console.log('      allow write: if false; // 只能通過管理後台更新');
  console.log('    }');
  console.log('  }');
  console.log('}');
  console.log('```');
}

/**
 * 顯示幫助信息
 */
function showHelp() {
  console.log('\n💡 使用說明：');
  console.log('');
  console.log('1. 安裝依賴：');
  console.log('   npm install firebase-admin');
  console.log('');
  console.log('2. 設置服務帳戶金鑰（選擇其中一種方式）：');
  console.log('   方式 A - 環境變量：');
  console.log('     export GOOGLE_APPLICATION_CREDENTIALS="path/to/service-account-key.json"');
  console.log('   方式 B - 在 Google Cloud 環境中運行（如 Cloud Shell）');
  console.log('');
  console.log('3. 生成數據文件（如果尚未生成）：');
  console.log('   dart run scripts/simple_init_versions.dart');
  console.log('');
  console.log('4. 運行導入腳本：');
  console.log('   node scripts/import-to-firestore.js');
  console.log('');
  console.log('環境變量：');
  console.log('   FIREBASE_PROJECT_ID: Firebase 項目 ID（默認：astreal-d3f70）');
  console.log('   GOOGLE_APPLICATION_CREDENTIALS: 服務帳戶金鑰文件路徑');
}

/**
 * 主函數
 */
async function main() {
  console.log('🚀 Firebase Firestore 版本信息導入腳本\n');
  
  try {
    // 檢查命令行參數
    if (process.argv.includes('--help') || process.argv.includes('-h')) {
      showHelp();
      return;
    }
    
    // 檢查數據文件
    await checkDataFiles();
    
    // 初始化 Firebase
    const db = initializeFirebase();
    
    // 檢查現有數據
    await checkExistingData(db);
    
    // 導入版本數據
    const importedData = await importVersionData(db);
    
    // 驗證導入結果
    await verifyImport(db, importedData);
    
    // 顯示完成信息
    showCompletionInfo();
    
  } catch (error) {
    console.error('\n❌ 導入失敗:', error.message);
    showHelp();
    process.exit(1);
  }
}

// 運行主函數
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  initializeFirebase,
  importVersionData,
  validateVersionData
};
