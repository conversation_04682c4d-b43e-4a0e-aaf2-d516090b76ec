# Firebase 版本信息初始化腳本

這個目錄包含了用於初始化 Firebase Firestore 版本信息的腳本和配置文件。

## 📁 文件說明

### 腳本文件
- `setup_firebase_versions.sh` - Linux/macOS 互動式設置腳本
- `setup_firebase_versions.bat` - Windows 互動式設置腳本
- `init_versions_with_config.dart` - 使用內建配置的初始化腳本
- `init_versions_from_config.dart` - 使用 JSON 配置文件的初始化腳本
- `init_firebase_versions.dart` - 通用初始化腳本模板

### 配置文件
- `version_config.json` - 版本信息配置文件
- `README.md` - 本說明文件

## 🚀 快速開始

### 方法一：使用互動式腳本（推薦）

#### Linux/macOS:
```bash
# 給腳本執行權限
chmod +x scripts/setup_firebase_versions.sh

# 運行腳本
./scripts/setup_firebase_versions.sh
```

#### Windows:
```cmd
# 運行腳本
scripts\setup_firebase_versions.bat
```

### 方法二：直接運行 Dart 腳本

#### 預覽配置（不實際寫入）:
```bash
dart run scripts/init_versions_from_config.dart --dry-run
```

#### 初始化版本信息:
```bash
dart run scripts/init_versions_from_config.dart
```

#### 使用自定義配置:
```bash
dart run scripts/init_versions_from_config.dart --config=my_config.json
```

## ⚙️ 配置說明

### version_config.json 結構

```json
{
  "defaultVersion": {
    "version": "1.0.0",                    // 版本號
    "buildNumber": 1,                      // 構建號
    "minRequiredVersion": "1.0.0",         // 最低要求版本
    "minRequiredBuildNumber": 1,           // 最低要求構建號
    "forceUpdate": false,                  // 是否強制更新
    "isActive": true,                      // 是否啟用版本檢查
    "features": [...]                      // 通用功能列表
  },
  "platforms": {
    "android": {
      "updateMessage": "...",              // 更新訊息
      "updateUrl": "...",                  // 更新連結
      "additionalFeatures": [...]          // 平台特定功能
    },
    // ... 其他平台
  }
}
```

### 支援的平台
- `android` - Android 應用
- `ios` - iOS 應用
- `macos` - macOS 應用
- `windows` - Windows 應用
- `web` - Web 應用

## 📋 使用步驟

### 1. 準備工作
確保您的項目已經：
- ✅ 配置了 Firebase
- ✅ 啟用了 Firestore
- ✅ 安裝了必要的依賴（firebase_core, cloud_firestore）

### 2. 檢查依賴
```bash
# 確保已安裝必要的包
flutter pub add firebase_core cloud_firestore
```

### 3. 運行腳本
選擇以下任一方式：

#### 互動式腳本（推薦新手）:
```bash
# Linux/macOS
./scripts/setup_firebase_versions.sh

# Windows
scripts\setup_firebase_versions.bat
```

#### 命令行腳本（推薦進階用戶）:
```bash
# 預覽配置
dart run scripts/init_versions_from_config.dart --dry-run

# 實際初始化
dart run scripts/init_versions_from_config.dart
```

### 4. 驗證結果
1. 打開 [Firebase Console](https://console.firebase.google.com)
2. 選擇您的項目
3. 進入 Firestore Database
4. 確認 `app_versions` collection 已創建
5. 檢查各平台文檔的數據是否正確

## 🔧 自定義配置

### 創建自定義配置文件
1. 複製 `version_config.json` 為新文件
2. 修改版本信息、更新訊息、功能列表等
3. 使用 `--config` 參數指定配置文件

### 配置示例

#### 可選更新配置:
```json
{
  "defaultVersion": {
    "version": "1.0.1",
    "buildNumber": 2,
    "minRequiredVersion": "1.0.0",        // 不變
    "minRequiredBuildNumber": 1,          // 不變
    "forceUpdate": false                  // 可選更新
  }
}
```

#### 強制更新配置:
```json
{
  "defaultVersion": {
    "version": "1.1.0",
    "buildNumber": 3,
    "minRequiredVersion": "1.1.0",        // 更新為新版本
    "minRequiredBuildNumber": 3,          // 更新為新構建號
    "forceUpdate": true                   // 強制更新
  }
}
```

## 🛡️ 安全考慮

### Firestore 安全規則
確保您的 Firestore 安全規則只允許讀取版本信息：

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /app_versions/{platform} {
      allow read: if true;
      allow write: if false; // 只能通過管理後台更新
    }
  }
}
```

### 權限管理
- 客戶端應用只能讀取版本信息
- 版本信息的更新應該通過：
  - Firebase Console
  - 管理後台
  - 服務端腳本
  - CI/CD 流程

## 🧪 測試建議

### 測試流程
1. **乾運行測試**: 使用 `--dry-run` 參數預覽配置
2. **開發環境測試**: 在開發環境中測試版本檢查邏輯
3. **不同版本測試**: 測試可選更新和強制更新場景
4. **錯誤處理測試**: 測試網路錯誤等異常情況

### 測試場景
- ✅ 最新版本（不顯示更新提示）
- ✅ 可選更新（顯示可選更新對話框）
- ✅ 強制更新（顯示強制更新對話框）
- ✅ 網路錯誤（優雅降級）
- ✅ 數據格式錯誤（錯誤處理）

## 🔍 故障排除

### 常見問題

#### 1. 腳本運行失敗
```
❌ 錯誤：未找到 Dart 命令
```
**解決方案**: 確保 Flutter SDK 已正確安裝並添加到 PATH

#### 2. Firebase 連接失敗
```
❌ 初始化失敗: Firebase 連接錯誤
```
**解決方案**: 
- 檢查網路連接
- 確認 Firebase 配置正確
- 檢查項目 ID 是否正確

#### 3. 權限錯誤
```
❌ 寫入 Firestore 失敗: 權限不足
```
**解決方案**:
- 確認 Firestore 已啟用
- 檢查安全規則設置
- 確認具有寫入權限

#### 4. 配置文件錯誤
```
❌ 配置文件不存在或格式錯誤
```
**解決方案**:
- 確認配置文件路徑正確
- 檢查 JSON 格式是否有效
- 使用 `--dry-run` 預覽配置

### 調試技巧
1. 使用 `--dry-run` 參數預覽配置
2. 檢查 Firebase Console 中的數據
3. 查看腳本輸出的詳細日誌
4. 確認網路連接和權限設置

## 📚 相關文檔

- [Firebase 版本控管設置說明](../docs/firebase_version_setup.md)
- [版本控管使用說明](../docs/version_control_usage.md)
- [實作總結](../docs/version_control_implementation_summary.md)

## 💡 提示

- 建議先使用 `--dry-run` 預覽配置再實際執行
- 定期備份 Firestore 數據
- 在生產環境中謹慎使用強制更新
- 保持版本號的語義化（major.minor.patch）
- 為每個版本提供清晰的更新說明
