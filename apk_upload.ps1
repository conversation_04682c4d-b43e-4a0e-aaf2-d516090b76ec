# apk_upload.ps1 - 修正編碼與括號錯誤  .\apk_upload.ps1

# ====== 專案參數（請依實際情況修改）======
$APP_ID = "1:470077449550:android:4971c9e15686127296aa1f"             # Firebase Android App ID
$TESTERS = "<EMAIL>"            # 測試者 Email，可用逗號分隔

# ====== 防呆檢查 ======
if (-not (Get-Command flutter -ErrorAction SilentlyContinue)) {
    Write-Host "❌ Flutter 未安裝或未加入 PATH" -ForegroundColor Red
    exit 1
}
if (-not (Get-Command firebase -ErrorAction SilentlyContinue)) {
    Write-Host "❌ Firebase CLI 未安裝或未加入 PATH" -ForegroundColor Red
    exit 1
}

# ====== 取得最新 Git commit message ======
$GIT_MESSAGE = git log -1 --pretty=%B
Set-Content -Path "RELEASE_NOTE.txt" -Value @(
    "Release Note:"
    $GIT_MESSAGE
)

# ====== 讀取 pubspec.yaml 的版本號 ======
$pubspec = Get-Content "pubspec.yaml"
$versionLine = $pubspec | Where-Object { $_ -match "^version:" }
if ($versionLine -match "version:\s*([\d\.]+)\+(\d+)") {
    $VERSION = $matches[1]
    $BUILD = $matches[2]
} else {
    Write-Host "❌ 找不到 pubspec.yaml 的版本號" -ForegroundColor Red
    exit 1
}

# ====== 打包 APK ======
flutter build apk --debug
# flutter build apk --release

# ====== 檢查 APK 是否存在 ======
$apkPath = "build\app\outputs\flutter-apk\app-debug.apk"
if (-not (Test-Path $apkPath)) {Write-Host "❌ 找不到 APK，打包可能失敗" -ForegroundColor Red exit 1}

# ====== 重新命名 APK ======
$apkName = "Astreal_debug_v${VERSION}+${BUILD}.apk"
$newApkPath = "build\app\outputs\flutter-apk\$apkName"
Rename-Item -Path $apkPath -NewName $apkName

# ====== 上傳到 Firebase App Distribution ======
firebase appdistribution:distribute $newApkPath `
  --app $APP_ID `
  --release-notes "$GIT_MESSAGE" `
  --testers "$TESTERS"

Write-Host "`n✅ APK 已上傳：$apkName 至 Firebase Distribution。" -ForegroundColor Green
