# 天象盤時間與地點選擇功能

## 📐 功能概述

我已經為天象盤添加了完整的時間選擇與地點選擇功能，讓用戶可以指定任意時間和地點來查看天象配置，用於觀察全球或特定地區的整體運勢。

## ✨ 新增功能

### 🎯 **天象盤特殊處理**

#### **ChartType 擴展**
- ✅ **requiresSpecificDate**：天象盤現在需要特定日期
- ✅ **requiresLocationSelection**：新增地點選擇需求檢查
- ✅ **無需人物選擇**：天象盤不需要預先選擇人物

#### **時間選擇功能**
- ✅ **日期選擇器**：支援選擇任意日期（1900-2100年）
- ✅ **時間選擇器**：支援精確到分鐘的時間選擇
- ✅ **預設當前時間**：預設為當前日期時間

#### **地點選擇功能**
- ✅ **預設城市**：提供台灣主要城市和國際城市選項
- ✅ **自定義地點**：支援手動輸入經緯度
- ✅ **地點驗證**：確保輸入的經緯度有效

### 🔧 **技術實現**

#### **ChartType 修改**
```dart
// 檢查是否需要特定日期
bool get requiresSpecificDate {
  final result = isPredictiveChart || isReturnChart || isEventChart || this == ChartType.mundane;
  return result;
}

// 檢查是否需要地點選擇
bool get requiresLocationSelection {
  final result = [ChartType.mundane, ChartType.event, ChartType.horary].contains(this);
  return result;
}
```

#### **ChartSelectionPage 狀態管理**
```dart
// 選擇的地點（用於天象盤）
String _selectedLocation = '台北市';
double _selectedLatitude = 25.0330;
double _selectedLongitude = 121.5654;
```

#### **動態UI顯示**
```dart
// 日期選擇器
if (_selectedChartType.requiresSpecificDate) ...[
  _buildDateSelector(),
  const SizedBox(height: 12),
],

// 地點選擇器（天象盤專用）
if (_selectedChartType.requiresLocationSelection) ...[
  _buildLocationSelector(),
  const SizedBox(height: 12),
],
```

### 🎨 **UI 設計**

#### **地點選擇器**
```dart
Widget _buildLocationSelector() {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      const Text('選擇地點', style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
      Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: AppColors.textLight),
        ),
        child: ListTile(
          title: Text(_selectedLocation),
          subtitle: Text('${_selectedLatitude.toStringAsFixed(4)}, ${_selectedLongitude.toStringAsFixed(4)}'),
          trailing: const Icon(Icons.location_on),
          onTap: () => _showLocationSelectionDialog(),
        ),
      ),
    ],
  );
}
```

#### **地點選擇對話框**
- ✅ **預設城市列表**：台灣主要城市 + 國際城市
- ✅ **自定義地點**：支援手動輸入經緯度
- ✅ **視覺反饋**：選中的地點有特殊標示

### 📍 **預設地點列表**

#### **台灣城市**
- 台北市 (25.0330, 121.5654)
- 台中市 (24.1477, 120.6736)
- 高雄市 (22.6273, 120.3014)
- 台南市 (22.9999, 120.2269)
- 新北市 (25.0173, 121.4437)
- 桃園市 (24.9936, 121.3010)

#### **國際城市**
- 香港 (22.3193, 114.1694)
- 澳門 (22.1987, 113.5439)
- 新加坡 (1.3521, 103.8198)
- 東京 (35.6762, 139.6503)
- 首爾 (37.5665, 126.9780)
- 北京 (39.9042, 116.4074)
- 上海 (31.2304, 121.4737)

### 🎯 **天象盤特殊邏輯**

#### **無需人物驗證**
```dart
// 天象盤不需要選擇人物，其他星盤需要
if (_selectedChartType != ChartType.mundane && _primaryPerson == null) {
  ScaffoldMessenger.of(context).showSnackBar(
    const SnackBar(content: Text('請先選擇主要人物')),
  );
  return;
}
```

#### **虛擬人物創建**
```dart
if (_selectedChartType == ChartType.mundane) {
  // 天象盤使用選擇的時間和地點創建虛擬人物
  chartData = ChartData(
    chartType: _selectedChartType,
    primaryPerson: BirthData(
      id: 'mundane_${_selectedDate.millisecondsSinceEpoch}',
      name: '天象盤',
      birthDate: _selectedDate,
      birthPlace: _selectedLocation,
      latitude: _selectedLatitude,
      longitude: _selectedLongitude,
    ),
    specificDate: _selectedDate,
  );
}
```

## 🎯 **用戶體驗流程**

### **天象盤使用流程**
1. **進入星盤選擇頁面**：從首頁點擊星盤按鈕
2. **選擇天象盤**：點擊天象盤卡片
3. **選擇時間**：點擊日期選擇器，選擇日期和時間
4. **選擇地點**：點擊地點選擇器，選擇城市或自定義地點
5. **查看星盤**：點擊「查看星盤」按鈕
6. **分析天象**：查看指定時間地點的星象配置

### **地點選擇流程**
1. **點擊地點選擇器**：顯示地點選擇對話框
2. **選擇預設城市**：從列表中選擇城市
3. **或自定義地點**：點擊「自定義地點」
4. **輸入資訊**：輸入地點名稱和經緯度
5. **確認選擇**：點擊確定按鈕

## 📊 **功能特色**

### **✅ 完整的時間控制**
- **精確時間**：支援到分鐘級別的時間選擇
- **廣泛範圍**：支援1900-2100年的日期範圍
- **直觀操作**：使用系統原生的日期時間選擇器

### **✅ 靈活的地點選擇**
- **常用城市**：預設台灣和國際主要城市
- **自定義地點**：支援任意經緯度輸入
- **地點驗證**：確保輸入的經緯度格式正確

### **✅ 智能的UI適配**
- **條件顯示**：只在選擇天象盤時顯示地點選擇器
- **統一設計**：與現有UI保持一致的設計語言
- **清晰反饋**：選中狀態有明確的視覺標示

## 🔧 **技術亮點**

### **動態UI渲染**
- 根據星盤類型動態顯示所需的選擇器
- 天象盤顯示時間和地點選擇器
- 其他星盤類型顯示相應的選擇器

### **數據驗證**
- 地點名稱不能為空
- 經緯度必須是有效的數字
- 時間範圍在合理區間內

### **狀態管理**
- 選擇的時間和地點狀態正確保存
- UI更新及時反映狀態變化
- 導航時正確傳遞數據

## 🎨 **UI 設計原則**

### **一致性**
- 與現有的日期選擇器保持相同的設計風格
- 使用統一的卡片樣式和圓角設計
- 保持相同的顏色主題和字體大小

### **易用性**
- 地點選擇器顯示當前選擇的地點和經緯度
- 預設常用城市，減少用戶輸入
- 支援自定義地點滿足特殊需求

### **清晰性**
- 明確的標題和說明文字
- 選中狀態有視覺標示
- 錯誤提示清晰明確

## 🔮 **未來擴展**

### **地點功能增強**
- **GPS定位**：支援獲取當前位置
- **地點搜尋**：支援地點名稱搜尋
- **歷史記錄**：記住常用的地點選擇

### **時間功能增強**
- **快速選擇**：提供「現在」、「今日中午」等快速選項
- **時區支援**：支援不同時區的時間選擇
- **天文時間**：支援恆星時等天文時間系統

### **預設優化**
- **智能預設**：根據用戶位置智能預設地點
- **個人化**：記住用戶的偏好設置
- **批量操作**：支援批量生成不同時間的天象盤

## 🎉 **總結**

天象盤的時間與地點選擇功能現在已經完全實現：

1. **✅ 完整的時間選擇**：支援精確的日期時間選擇
2. **✅ 靈活的地點選擇**：預設城市 + 自定義地點
3. **✅ 智能的UI適配**：根據星盤類型動態顯示
4. **✅ 無需人物選擇**：天象盤專用的簡化流程
5. **✅ 完整的數據驗證**：確保輸入數據的有效性

這個功能讓用戶可以輕鬆查看任意時間、任意地點的天象配置，為占星分析提供了強大的工具支援！
