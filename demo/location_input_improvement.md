# 天象盤地點輸入功能改進

## 📐 改進概述

我已經將天象盤的地點選擇功能改進為類似出生資料地點輸入的方式，提供更直觀和強大的地點輸入體驗，包括地點搜尋、當前位置獲取和智能地理編碼功能。

## ✨ 改進內容

### 🎯 **從對話框選擇改為輸入框**

#### **改進前：對話框選擇**
- ❌ **預設城市列表**：只能從固定的城市列表中選擇
- ❌ **自定義地點**：需要手動輸入經緯度
- ❌ **操作複雜**：需要多步驟操作

#### **改進後：智能輸入框**
- ✅ **直接輸入**：可以直接輸入任何地點名稱
- ✅ **自動搜尋**：使用地理編碼服務自動獲取經緯度
- ✅ **當前位置**：一鍵獲取當前位置
- ✅ **操作簡單**：一步到位的輸入體驗

### 🔧 **技術實現**

#### **UI 組件升級**
```dart
// 地點輸入框
TextFormField(
  controller: _locationController,
  decoration: InputDecoration(
    hintText: '請輸入地點（如：台北市）',
    border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
    prefixIcon: const Icon(Icons.location_on_outlined),
    suffixIcon: Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // 清除按鈕
        IconButton(icon: const Icon(Icons.clear), onPressed: _clearLocation),
        // 搜尋按鈕
        IconButton(icon: const Icon(Icons.search), onPressed: _searchLocation),
      ],
    ),
  ),
  onChanged: (value) => setState(() => _selectedLocation = value),
)
```

#### **當前位置按鈕**
```dart
TextButton.icon(
  onPressed: _isLoadingLocation ? null : _getCurrentLocation,
  icon: _isLoadingLocation 
      ? const CircularProgressIndicator(strokeWidth: 2)
      : const Icon(Icons.my_location, size: 16),
  label: Text(_isLoadingLocation ? '定位中...' : '當前位置'),
  style: TextButton.styleFrom(foregroundColor: AppColors.royalIndigo),
)
```

### 🌍 **地理編碼服務整合**

#### **GeocodingService 功能**
- ✅ **地址轉經緯度**：`getCoordinatesFromAddress()`
- ✅ **經緯度轉地址**：`getAddressFromCoordinates()`
- ✅ **當前位置獲取**：`getCurrentLocation()`
- ✅ **多重備援**：Flutter geocoding + OpenStreetMap Nominatim API

#### **智能搜尋邏輯**
```dart
Future<void> _searchLocation() async {
  try {
    // 使用 GeocodingService 將地點名稱轉換為經緯度
    Map<String, double> coordinates = await GeocodingService.getCoordinatesFromAddress(
      _locationController.text.trim(),
    );

    setState(() {
      _selectedLocation = _locationController.text.trim();
      _selectedLatitude = coordinates['latitude']!;
      _selectedLongitude = coordinates['longitude']!;
    });

    // 顯示成功訊息
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('已找到地點: $_selectedLocation'), backgroundColor: Colors.green),
    );
  } catch (e) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('搜尋地點時出錯: $e')),
    );
  }
}
```

#### **當前位置獲取**
```dart
Future<void> _getCurrentLocation() async {
  try {
    // 獲取當前位置的經緯度
    final coordinates = await GeocodingService.getCurrentLocation();
    
    // 根據經緯度獲取地址
    final address = await GeocodingService.getAddressFromCoordinates(
      coordinates['latitude']!,
      coordinates['longitude']!,
    );

    setState(() {
      _selectedLocation = address ?? '當前位置';
      _selectedLatitude = coordinates['latitude']!;
      _selectedLongitude = coordinates['longitude']!;
      _locationController.text = address ?? '當前位置';
    });
  } catch (e) {
    // 錯誤處理
  }
}
```

### 🎨 **UI/UX 改進**

#### **視覺設計**
- ✅ **統一風格**：與出生資料輸入保持一致的設計語言
- ✅ **清晰標示**：顯示經緯度信息確認位置準確性
- ✅ **載入狀態**：按鈕顯示載入動畫和狀態文字
- ✅ **錯誤反饋**：清晰的錯誤提示和成功確認

#### **交互體驗**
```dart
// 經緯度顯示
if (_selectedLatitude != 0.0 && _selectedLongitude != 0.0) ...[
  const SizedBox(height: 4),
  Text(
    '經緯度: ${_selectedLatitude.toStringAsFixed(4)}, ${_selectedLongitude.toStringAsFixed(4)}',
    style: TextStyle(fontSize: 12, color: Colors.grey[600]),
  ),
],
```

#### **操作便利性**
- ✅ **清除功能**：一鍵清除輸入內容
- ✅ **即時更新**：輸入時即時更新選擇狀態
- ✅ **智能提示**：提供使用提示和範例

### 📍 **支援的地點類型**

#### **城市名稱**
- 台北市、台中市、高雄市
- 香港、澳門、新加坡
- 東京、首爾、北京、上海

#### **詳細地址**
- 台北市信義區市府路1號
- 台中市西屯區台灣大道三段99號
- 高雄市前金區中正四路211號

#### **英文地名**
- Taipei, Taiwan
- Tokyo, Japan
- Seoul, South Korea
- Beijing, China

#### **座標格式**
- 緯度: 25.0330, 經度: 121.5654
- 25.0330, 121.5654
- 25°01'58.8"N 121°33'55.4"E

## 🎯 **用戶體驗流程**

### **地點輸入完整流程**
1. **進入天象盤選擇**：選擇天象盤類型
2. **查看地點輸入框**：系統自動顯示地點選擇器
3. **選擇輸入方式**：
   - **直接輸入**：在輸入框中輸入地點名稱
   - **當前位置**：點擊「當前位置」按鈕
4. **執行搜尋**：點擊搜尋按鈕或當前位置按鈕
5. **確認結果**：查看經緯度確認位置正確
6. **查看星盤**：點擊「查看星盤」按鈕

### **錯誤處理流程**
1. **輸入無效地點**：顯示「無法找到該地點，請檢查地點名稱」
2. **位置服務關閉**：顯示「無法獲取當前位置，請確保已開啟位置服務和權限」
3. **網路連線問題**：顯示「搜尋地點時出錯」並提供重試建議

## 📊 **功能對比**

### **改進前 vs 改進後**

| 功能 | 改進前 | 改進後 |
|------|--------|--------|
| **輸入方式** | 對話框選擇 | 直接輸入框 |
| **地點範圍** | 13個預設城市 | 全球任意地點 |
| **當前位置** | 不支援 | ✅ 一鍵獲取 |
| **地理編碼** | 手動輸入經緯度 | ✅ 自動轉換 |
| **操作步驟** | 3-4步 | 1-2步 |
| **錯誤處理** | 基本驗證 | ✅ 完整錯誤處理 |
| **載入狀態** | 無 | ✅ 視覺反饋 |
| **地址驗證** | 無 | ✅ 即時驗證 |

### **技術優勢**

#### **✅ 更強大的功能**
- **全球地點支援**：不限於預設城市列表
- **智能搜尋**：支援多種地點格式輸入
- **自動定位**：GPS定位 + 地址解析

#### **✅ 更好的用戶體驗**
- **操作簡化**：減少操作步驟
- **即時反饋**：載入狀態和結果確認
- **錯誤引導**：清晰的錯誤提示和解決建議

#### **✅ 更高的準確性**
- **地理編碼驗證**：確保地點真實存在
- **經緯度顯示**：用戶可以確認位置準確性
- **多重備援**：多個地理編碼服務確保可靠性

## 🔧 **技術細節**

### **狀態管理**
```dart
// 地點相關狀態
String _selectedLocation = '台北市';
double _selectedLatitude = 25.0330;
double _selectedLongitude = 121.5654;
final TextEditingController _locationController = TextEditingController();
bool _isLoadingLocation = false;
```

### **生命週期管理**
```dart
@override
void initState() {
  super.initState();
  _locationController.text = _selectedLocation; // 初始化輸入框
}

@override
void dispose() {
  _locationController.dispose(); // 清理資源
  super.dispose();
}
```

### **錯誤處理機制**
- **網路錯誤**：自動重試機制
- **權限錯誤**：引導用戶開啟權限
- **格式錯誤**：提供輸入格式建議
- **服務不可用**：提供替代方案

## 🔮 **未來擴展**

### **功能增強**
- **地點歷史**：記住常用地點
- **地點建議**：智能地點建議
- **離線支援**：離線地理編碼
- **地圖選擇**：地圖界面選擇地點

### **性能優化**
- **快取機制**：地理編碼結果快取
- **批量處理**：批量地點查詢
- **預載入**：常用地點預載入

## 🎉 **總結**

天象盤地點輸入功能的改進帶來了顯著的用戶體驗提升：

1. **✅ 操作更簡單**：從多步驟對話框改為一步輸入
2. **✅ 功能更強大**：支援全球任意地點搜尋
3. **✅ 體驗更流暢**：即時反饋和載入狀態
4. **✅ 準確性更高**：地理編碼驗證和經緯度顯示
5. **✅ 錯誤處理更完善**：清晰的錯誤提示和引導

這個改進參考了出生資料地點輸入的成功經驗，為天象盤提供了與整個應用一致的高品質地點輸入體驗，讓用戶可以輕鬆準確地選擇任何地點來查看天象配置！
