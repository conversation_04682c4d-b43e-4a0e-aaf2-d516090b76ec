# 二分二至盤年份與地點選擇功能

## 📐 功能概述

我已經為二分二至盤添加了完整的年份選擇與地點選擇功能，讓用戶可以指定任意年份和地點來查看該年度的春分、夏至、秋分、冬至四個重要時刻的星象配置，用於分析年度運勢和季節性影響。

## ✨ 新增功能

### 🎯 **二分二至盤特殊處理**

#### **ChartType 擴展**
- ✅ **requiresLocationSelection**：二分二至盤現在需要地點選擇
- ✅ **requiresYearSelection**：新增年份選擇需求檢查
- ✅ **無需人物選擇**：二分二至盤不需要預先選擇人物

#### **年份選擇功能**
- ✅ **年份選擇器**：支援選擇任意年份（當前年份±50年）
- ✅ **YearPicker 組件**：使用 Flutter 原生年份選擇器
- ✅ **預設當前年份**：預設為當前年份

#### **地點選擇功能**
- ✅ **智能輸入框**：與天象盤共用地點選擇功能
- ✅ **地理編碼服務**：自動將地點名稱轉換為經緯度
- ✅ **當前位置獲取**：一鍵獲取當前位置

### 🔧 **技術實現**

#### **ChartType 修改**
```dart
// 檢查是否需要地點選擇
bool get requiresLocationSelection {
  final result = [ChartType.mundane, ChartType.event, ChartType.horary, ChartType.equinoxSolstice].contains(this);
  return result;
}

// 檢查是否需要年份選擇
bool get requiresYearSelection {
  final result = [ChartType.equinoxSolstice].contains(this);
  return result;
}
```

#### **ChartSelectionPage 狀態管理**
```dart
// 選擇的年份（用於二分二至盤）
int _selectedYear = DateTime.now().year;

// 選擇的地點（用於天象盤和二分二至盤）
String _selectedLocation = '台北市';
double _selectedLatitude = 25.0330;
double _selectedLongitude = 121.5654;
```

#### **動態UI顯示**
```dart
// 年份選擇器（二分二至盤專用）
if (_selectedChartType.requiresYearSelection) ...[
  _buildYearSelector(),
  const SizedBox(height: 12),
],

// 地點選擇器（天象盤和二分二至盤專用）
if (_selectedChartType.requiresLocationSelection) ...[
  _buildLocationSelector(),
  const SizedBox(height: 12),
],
```

### 🎨 **UI 設計**

#### **年份選擇器**
```dart
Widget _buildYearSelector() {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      const Text('選擇年份', style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
      Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: AppColors.textLight),
        ),
        child: ListTile(
          title: Text('$_selectedYear年'),
          subtitle: const Text('二分二至圖年份'),
          trailing: const Icon(Icons.date_range),
          onTap: () => _showYearPicker(),
        ),
      ),
    ],
  );
}
```

#### **年份選擇對話框**
```dart
Future<void> _showYearPicker() async {
  final currentYear = DateTime.now().year;
  final selectedYear = await showDialog<int>(
    context: context,
    builder: (context) => AlertDialog(
      title: const Text('選擇年份'),
      content: SizedBox(
        width: double.maxFinite,
        height: 300,
        child: YearPicker(
          firstDate: DateTime(currentYear - 50),
          lastDate: DateTime(currentYear + 50),
          selectedDate: DateTime(_selectedYear),
          onChanged: (DateTime dateTime) {
            Navigator.pop(context, dateTime.year);
          },
        ),
      ),
    ),
  );
}
```

### 🎯 **二分二至盤特殊邏輯**

#### **無需人物驗證**
```dart
// 天象盤和二分二至盤不需要選擇人物，其他星盤需要
if (_selectedChartType != ChartType.mundane && 
    _selectedChartType != ChartType.equinoxSolstice && 
    _primaryPerson == null) {
  ScaffoldMessenger.of(context).showSnackBar(
    const SnackBar(content: Text('請先選擇主要人物')),
  );
  return;
}
```

#### **虛擬人物創建**
```dart
if (_selectedChartType == ChartType.equinoxSolstice) {
  // 二分二至盤使用選擇的年份和地點創建虛擬人物
  // 使用春分作為代表時間（實際計算會在 EquinoxSolsticeService 中進行）
  final springEquinoxDate = DateTime(_selectedYear, 3, 20, 12, 0); // 大約的春分時間
  chartData = ChartData(
    chartType: _selectedChartType,
    primaryPerson: BirthData(
      id: 'equinox_solstice_${_selectedYear}_${_selectedLocation.hashCode}',
      name: '${_selectedYear}年二分二至圖',
      birthDate: springEquinoxDate,
      birthPlace: _selectedLocation,
      latitude: _selectedLatitude,
      longitude: _selectedLongitude,
    ),
    secondaryPerson: _primaryPerson, // 如果有選擇人物，用於比較
    specificDate: springEquinoxDate,
  );
}
```

## 🎯 **用戶體驗流程**

### **二分二至盤使用流程**
1. **進入星盤選擇頁面**：從首頁點擊星盤按鈕
2. **選擇二分二至盤**：點擊二分二至盤卡片
3. **選擇年份**：點擊年份選擇器，選擇目標年份
4. **選擇地點**：點擊地點選擇器，輸入或選擇地點
5. **查看星盤**：點擊「查看星盤」按鈕
6. **分析季節運勢**：查看指定年份地點的四季星象配置

### **年份選擇流程**
1. **點擊年份選擇器**：顯示年份選擇對話框
2. **瀏覽年份**：使用 YearPicker 滾動選擇年份
3. **確認選擇**：點擊目標年份確認選擇
4. **返回主頁面**：年份選擇器顯示選中的年份

### **地點選擇流程**
1. **輸入地點名稱**：在地點輸入框中輸入地點
2. **搜尋地點**：點擊搜尋按鈕或使用當前位置
3. **確認經緯度**：查看顯示的經緯度確認位置正確
4. **完成選擇**：地點信息自動保存

## 📊 **功能特色**

### **✅ 靈活的年份選擇**
- **廣泛範圍**：支援當前年份前後50年的選擇
- **直觀操作**：使用系統原生的年份選擇器
- **即時更新**：選擇後立即更新顯示

### **✅ 智能的地點選擇**
- **全球支援**：支援世界各地的地點輸入
- **自動編碼**：地點名稱自動轉換為經緯度
- **當前位置**：一鍵獲取當前位置

### **✅ 完整的數據驗證**
- **年份範圍檢查**：確保年份在合理範圍內
- **地點有效性**：驗證地點名稱和經緯度
- **錯誤處理**：完善的錯誤提示和引導

## 🔧 **技術亮點**

### **動態UI渲染**
- 根據星盤類型動態顯示所需的選擇器
- 二分二至盤顯示年份和地點選擇器
- 其他星盤類型顯示相應的選擇器

### **數據結構設計**
- 使用虛擬人物存儲年份和地點信息
- 人物名稱包含年份信息便於識別
- 支援與個人星盤的比較分析

### **狀態管理**
- 年份和地點狀態正確保存
- UI更新及時反映狀態變化
- 導航時正確傳遞數據

## 🎨 **UI 設計原則**

### **一致性**
- 與現有的日期和地點選擇器保持相同的設計風格
- 使用統一的卡片樣式和圓角設計
- 保持相同的顏色主題和字體大小

### **易用性**
- 年份選擇器顯示當前選擇的年份
- 地點選擇器顯示地點名稱和經緯度
- 清晰的標題和說明文字

### **功能性**
- 年份範圍涵蓋實用需求
- 地點選擇支援全球範圍
- 錯誤提示清晰明確

## 🔮 **二分二至盤的占星意義**

### **四個重要時刻**
- **春分（Spring Equinox）**：新年度開始，生命力復甦
- **夏至（Summer Solstice）**：陽氣最盛，事業高峰
- **秋分（Autumn Equinox）**：收穫時節，平衡調和
- **冬至（Winter Solstice）**：陰氣最盛，內省沉澱

### **分析用途**
- **年度運勢**：分析整年的運勢走向
- **季節影響**：了解不同季節的能量特質
- **地區差異**：比較不同地點的季節影響
- **個人比較**：與個人星盤比較分析

## 🔮 **未來擴展**

### **功能增強**
- **季節詳情**：顯示每個季節的具體時間
- **多年比較**：支援多個年份的比較分析
- **地點收藏**：記住常用的地點選擇
- **歷史數據**：查看歷史年份的二分二至圖

### **分析工具**
- **季節報告**：生成詳細的季節分析報告
- **趨勢分析**：分析多年的季節變化趨勢
- **地區比較**：比較不同地區的季節差異

## 🎉 **總結**

二分二至盤的年份與地點選擇功能現在已經完全實現：

1. **✅ 靈活的年份選擇**：支援廣泛的年份範圍選擇
2. **✅ 智能的地點選擇**：全球地點支援和自動編碼
3. **✅ 無需人物選擇**：二分二至盤專用的簡化流程
4. **✅ 完整的數據驗證**：確保輸入數據的有效性
5. **✅ 統一的UI設計**：與應用整體保持一致

這個功能讓用戶可以輕鬆查看任意年份、任意地點的二分二至圖，為季節性占星分析提供了強大的工具支援。無論是分析個人的年度運勢，還是研究不同地區的季節影響，都能得到準確而詳細的星象信息！
