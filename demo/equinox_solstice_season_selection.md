# 二分二至盤季節選擇功能

## 📐 功能概述

我已經為二分二至盤添加了完整的季節選擇功能，讓用戶可以在年份和地點的基礎上，進一步選擇春分、夏至、秋分、冬至中的特定季節，生成對應的單一季節星盤，用於深入分析特定季節的能量影響。

## ✨ 新增功能

### 🎯 **季節選擇功能**

#### **四季選項**
- ✅ **春分**：春季開始，晝夜平分（約3月20日）
- ✅ **夏至**：夏季高峰，白晝最長（約6月21日）
- ✅ **秋分**：秋季開始，晝夜平分（約9月23日）
- ✅ **冬至**：冬季深度，黑夜最長（約12月21日）

#### **視覺設計**
- ✅ **季節圖標**：每個季節配有相應的圖標
- ✅ **選中狀態**：清晰的選中視覺反饋
- ✅ **季節描述**：每個季節的簡要說明

#### **精確計算**
- ✅ **日期計算**：根據季節和年份計算具體日期時間
- ✅ **星盤生成**：為選定季節生成專門的星盤
- ✅ **個人比較**：支援與個人星盤的比較分析

### 🔧 **技術實現**

#### **季節狀態管理**
```dart
// 選擇的季節（用於二分二至盤）
String _selectedSeason = '春分'; // 春分、夏至、秋分、冬至
```

#### **季節選擇器UI**
```dart
Widget _buildSeasonSelector() {
  const seasons = [
    {'name': '春分', 'description': '春季開始，晝夜平分', 'icon': Icons.local_florist},
    {'name': '夏至', 'description': '夏季高峰，白晝最長', 'icon': Icons.wb_sunny},
    {'name': '秋分', 'description': '秋季開始，晝夜平分', 'icon': Icons.eco},
    {'name': '冬至', 'description': '冬季深度，黑夜最長', 'icon': Icons.ac_unit},
  ];

  return Container(
    child: Column(
      children: seasons.map((season) {
        final isSelected = _selectedSeason == season['name'];
        return InkWell(
          onTap: () => setState(() => _selectedSeason = season['name']),
          child: Container(
            decoration: BoxDecoration(
              color: isSelected ? AppColors.royalIndigo.withOpacity(0.1) : null,
            ),
            child: Row(
              children: [
                Icon(season['icon'], color: isSelected ? AppColors.royalIndigo : AppColors.textMedium),
                Text(season['name'], style: TextStyle(color: isSelected ? AppColors.royalIndigo : AppColors.textDark)),
                Text(season['description']),
                if (isSelected) Icon(Icons.check_circle, color: AppColors.royalIndigo),
              ],
            ),
          ),
        );
      }).toList(),
    ),
  );
}
```

#### **日期時間計算**
```dart
DateTime _calculateSeasonDateTime(String season, int year) {
  switch (season) {
    case '春分':
      return DateTime(year, 3, 20, 12, 0); // 大約3月20日
    case '夏至':
      return DateTime(year, 6, 21, 12, 0); // 大約6月21日
    case '秋分':
      return DateTime(year, 9, 23, 12, 0); // 大約9月23日
    case '冬至':
      return DateTime(year, 12, 21, 12, 0); // 大約12月21日
    default:
      return DateTime(year, 3, 20, 12, 0); // 預設春分
  }
}
```

#### **星盤數據創建**
```dart
if (_selectedChartType == ChartType.equinoxSolstice) {
  final seasonDateTime = _calculateSeasonDateTime(_selectedSeason, _selectedYear);
  
  chartData = ChartData(
    chartType: _selectedChartType,
    primaryPerson: BirthData(
      id: 'equinox_solstice_${_selectedYear}_${_selectedSeason}_${_selectedLocation.hashCode}',
      name: '${_selectedYear}年${_selectedSeason}盤',
      birthDate: seasonDateTime,
      birthPlace: _selectedLocation,
      latitude: _selectedLatitude,
      longitude: _selectedLongitude,
    ),
    secondaryPerson: _primaryPerson, // 如果有選擇人物，用於比較
    specificDate: seasonDateTime,
  );
}
```

### 🎨 **UI 設計特色**

#### **季節圖標映射**
- 🌸 **春分**：`Icons.local_florist` - 花朵圖標，象徵春天生機
- ☀️ **夏至**：`Icons.wb_sunny` - 太陽圖標，象徵夏日陽光
- 🍃 **秋分**：`Icons.eco` - 葉子圖標，象徵秋天收穫
- ❄️ **冬至**：`Icons.ac_unit` - 雪花圖標，象徵冬日寧靜

#### **視覺反饋**
- **選中狀態**：背景色變為淡藍色，圖標和文字變為主題色
- **未選中狀態**：保持中性色調
- **確認圖標**：選中時顯示勾選圖標

#### **信息層次**
- **主標題**：季節名稱（春分、夏至等）
- **副標題**：季節描述和特點
- **視覺元素**：圖標和選中狀態指示

## 🎯 **用戶體驗流程**

### **完整使用流程**
1. **選擇二分二至盤**：在星盤選擇頁面選擇二分二至盤
2. **查看選擇器**：系統自動顯示年份、季節和地點選擇器
3. **選擇年份**：點擊年份選擇器，選擇目標年份
4. **選擇季節**：點擊四個季節中的一個（春分、夏至、秋分、冬至）
5. **選擇地點**：在地點輸入框中輸入地點或使用當前位置
6. **確認信息**：查看年份、季節和經緯度確認選擇正確
7. **查看星盤**：點擊「查看星盤」按鈕，進入對應季節的星盤

### **季節選擇詳細流程**
1. **查看四季選項**：春分、夏至、秋分、冬至四個選項
2. **了解季節特點**：每個季節都有描述說明
3. **點擊選擇**：點擊想要分析的季節
4. **視覺確認**：選中的季節會有明顯的視覺標示
5. **繼續設置**：完成其他設置後查看星盤

## 📊 **季節特色分析**

### **🌸 春分盤特色**
- **時間**：約3月20日，晝夜平分
- **能量**：新生、開始、成長
- **適用**：新計劃啟動、關係開始、事業起步
- **關鍵詞**：復甦、希望、活力

### **☀️ 夏至盤特色**
- **時間**：約6月21日，白晝最長
- **能量**：高峰、豐盛、外向
- **適用**：事業高峰、社交活躍、創造力爆發
- **關鍵詞**：光明、成就、表現

### **🍃 秋分盤特色**
- **時間**：約9月23日，晝夜平分
- **能量**：收穫、平衡、反思
- **適用**：成果檢視、關係調和、智慧積累
- **關鍵詞**：收穫、平衡、感恩

### **❄️ 冬至盤特色**
- **時間**：約12月21日，黑夜最長
- **能量**：內省、沉澱、轉化
- **適用**：內在探索、精神成長、深度療癒
- **關鍵詞**：沉靜、轉化、重生

## 🔧 **技術亮點**

### **動態UI渲染**
- 根據星盤類型動態顯示季節選擇器
- 只在二分二至盤時顯示季節選項
- 與年份和地點選擇器協調工作

### **精確時間計算**
- 每個季節都有對應的大約日期
- 使用中午12點作為標準時間
- 支援任意年份的季節計算

### **數據結構優化**
- 季節信息包含在虛擬人物的ID和名稱中
- 支援與個人星盤的比較分析
- 完整的星盤數據結構

## 🎨 **UI 設計原則**

### **一致性**
- 與現有的年份和地點選擇器保持相同的設計風格
- 使用統一的卡片樣式和圓角設計
- 保持相同的顏色主題和字體大小

### **直觀性**
- 每個季節都有對應的圖標和描述
- 選中狀態有明確的視覺反饋
- 季節順序按照自然順序排列

### **功能性**
- 點擊即可選擇，操作簡單
- 選中狀態持久保存
- 與其他選擇器無縫配合

## 🔮 **占星分析價值**

### **單一季節深度分析**
- **專注性**：針對特定季節的深入分析
- **精確性**：具體到某個季節的能量特質
- **實用性**：可以指導該季節的生活安排

### **個人季節比較**
- **個人適應性**：分析個人與特定季節的匹配度
- **能量共振**：了解個人在不同季節的能量狀態
- **時機把握**：選擇最適合的季節進行重要決策

### **地區季節差異**
- **地理影響**：不同地點的季節能量差異
- **文化背景**：結合當地的季節文化特色
- **實際應用**：適合當地氣候的占星指導

## 🔮 **未來擴展**

### **功能增強**
- **精確時間**：整合 EquinoxSolsticeService 的精確計算
- **季節比較**：支援多個季節的對比分析
- **歷史趨勢**：分析多年同一季節的變化趨勢

### **分析工具**
- **季節報告**：生成詳細的季節分析報告
- **個人指導**：基於個人星盤的季節建議
- **最佳時機**：推薦最適合的季節活動

## 🎉 **總結**

二分二至盤的季節選擇功能現在已經完全實現：

1. **✅ 四季完整選擇**：春分、夏至、秋分、冬至四個選項
2. **✅ 直觀的UI設計**：圖標、描述和選中狀態清晰明確
3. **✅ 精確的時間計算**：根據季節和年份計算具體日期
4. **✅ 完整的星盤生成**：為選定季節生成專門的星盤
5. **✅ 個人比較支援**：可與個人星盤進行比較分析

這個功能讓用戶可以深入分析特定季節的能量影響，無論是春天的新生力量、夏天的豐盛能量、秋天的收穫智慧，還是冬天的內省轉化，都能得到精確而詳細的占星指導！

現在用戶的完整使用流程是：
**選擇二分二至盤 → 選擇年份 → 選擇季節 → 選擇地點 → 查看對應季節的星盤**
