/// 版本信息模型
class VersionInfo {
  /// 版本號（如 "1.0.0"）
  final String version;
  
  /// 構建號
  final int buildNumber;
  
  /// 最低要求版本
  final String minRequiredVersion;
  
  /// 最低要求構建號
  final int minRequiredBuildNumber;
  
  /// 是否強制更新
  final bool forceUpdate;
  
  /// 更新訊息
  final String updateMessage;
  
  /// 更新下載連結
  final String updateUrl;
  
  /// 發布日期
  final String releaseDate;
  
  /// 新功能列表
  final List<String> features;
  
  /// 是否啟用
  final bool isActive;
  
  const VersionInfo({
    required this.version,
    required this.buildNumber,
    required this.minRequiredVersion,
    required this.minRequiredBuildNumber,
    required this.forceUpdate,
    required this.updateMessage,
    required this.updateUrl,
    required this.releaseDate,
    required this.features,
    required this.isActive,
  });
  
  /// 從 JSON 創建 VersionInfo
  factory VersionInfo.fromJson(Map<String, dynamic> json) {
    return VersionInfo(
      version: json['version'] as String? ?? '1.0.0',
      buildNumber: json['buildNumber'] as int? ?? 1,
      minRequiredVersion: json['minRequiredVersion'] as String? ?? '1.0.0',
      minRequiredBuildNumber: json['minRequiredBuildNumber'] as int? ?? 1,
      forceUpdate: json['forceUpdate'] as bool? ?? false,
      updateMessage: json['updateMessage'] as String? ?? '',
      updateUrl: json['updateUrl'] as String? ?? '',
      releaseDate: json['releaseDate'] as String? ?? '',
      features: (json['features'] as List<dynamic>?)?.cast<String>() ?? [],
      isActive: json['isActive'] as bool? ?? true,
    );
  }
  
  /// 轉換為 JSON
  Map<String, dynamic> toJson() {
    return {
      'version': version,
      'buildNumber': buildNumber,
      'minRequiredVersion': minRequiredVersion,
      'minRequiredBuildNumber': minRequiredBuildNumber,
      'forceUpdate': forceUpdate,
      'updateMessage': updateMessage,
      'updateUrl': updateUrl,
      'releaseDate': releaseDate,
      'features': features,
      'isActive': isActive,
    };
  }
  
  /// 複製並修改部分屬性
  VersionInfo copyWith({
    String? version,
    int? buildNumber,
    String? minRequiredVersion,
    int? minRequiredBuildNumber,
    bool? forceUpdate,
    String? updateMessage,
    String? updateUrl,
    String? releaseDate,
    List<String>? features,
    bool? isActive,
  }) {
    return VersionInfo(
      version: version ?? this.version,
      buildNumber: buildNumber ?? this.buildNumber,
      minRequiredVersion: minRequiredVersion ?? this.minRequiredVersion,
      minRequiredBuildNumber: minRequiredBuildNumber ?? this.minRequiredBuildNumber,
      forceUpdate: forceUpdate ?? this.forceUpdate,
      updateMessage: updateMessage ?? this.updateMessage,
      updateUrl: updateUrl ?? this.updateUrl,
      releaseDate: releaseDate ?? this.releaseDate,
      features: features ?? this.features,
      isActive: isActive ?? this.isActive,
    );
  }
  
  @override
  String toString() {
    return 'VersionInfo(version: $version, buildNumber: $buildNumber, '
           'forceUpdate: $forceUpdate, isActive: $isActive)';
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is VersionInfo &&
        other.version == version &&
        other.buildNumber == buildNumber &&
        other.minRequiredVersion == minRequiredVersion &&
        other.minRequiredBuildNumber == minRequiredBuildNumber &&
        other.forceUpdate == forceUpdate &&
        other.updateMessage == updateMessage &&
        other.updateUrl == updateUrl &&
        other.releaseDate == releaseDate &&
        _listEquals(other.features, features) &&
        other.isActive == isActive;
  }
  
  @override
  int get hashCode {
    return Object.hash(
      version,
      buildNumber,
      minRequiredVersion,
      minRequiredBuildNumber,
      forceUpdate,
      updateMessage,
      updateUrl,
      releaseDate,
      features,
      isActive,
    );
  }
  
  /// 比較兩個列表是否相等
  bool _listEquals<T>(List<T>? a, List<T>? b) {
    if (a == null) return b == null;
    if (b == null || a.length != b.length) return false;
    if (identical(a, b)) return true;
    for (int index = 0; index < a.length; index += 1) {
      if (a[index] != b[index]) return false;
    }
    return true;
  }
}
