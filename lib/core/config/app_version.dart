/// 應用版本信息模型
class AppVersion {
  final String version;
  final int buildNumber;
  final String minRequiredVersion;
  final int minRequiredBuildNumber;
  final bool forceUpdate;
  final String updateMessage;
  final String updateUrl;
  final DateTime releaseDate;
  final List<String> features;
  final bool isActive;

  const AppVersion({
    required this.version,
    required this.buildNumber,
    required this.minRequiredVersion,
    required this.minRequiredBuildNumber,
    required this.forceUpdate,
    required this.updateMessage,
    required this.updateUrl,
    required this.releaseDate,
    required this.features,
    this.isActive = true,
  });

  /// 從 JSON 創建 AppVersion 實例
  factory AppVersion.fromJson(Map<String, dynamic> json) {
    return AppVersion(
      version: json['version'] as String,
      buildNumber: json['buildNumber'] as int,
      minRequiredVersion: json['minRequiredVersion'] as String,
      minRequiredBuildNumber: json['minRequiredBuildNumber'] as int,
      forceUpdate: json['forceUpdate'] as bool,
      updateMessage: json['updateMessage'] as String,
      updateUrl: json['updateUrl'] as String,
      releaseDate: DateTime.parse(json['releaseDate'] as String),
      features: List<String>.from(json['features'] as List),
      isActive: json['isActive'] as bool? ?? true,
    );
  }

  /// 轉換為 JSON
  Map<String, dynamic> toJson() {
    return {
      'version': version,
      'buildNumber': buildNumber,
      'minRequiredVersion': minRequiredVersion,
      'minRequiredBuildNumber': minRequiredBuildNumber,
      'forceUpdate': forceUpdate,
      'updateMessage': updateMessage,
      'updateUrl': updateUrl,
      'releaseDate': releaseDate.toIso8601String(),
      'features': features,
      'isActive': isActive,
    };
  }

  /// 檢查當前版本是否需要更新
  bool needsUpdate(String currentVersion, int currentBuildNumber) {
    // 比較版本號
    final currentVersionParts = currentVersion.split('.').map(int.parse).toList();
    final minVersionParts = minRequiredVersion.split('.').map(int.parse).toList();
    
    // 確保版本號格式一致
    while (currentVersionParts.length < 3) currentVersionParts.add(0);
    while (minVersionParts.length < 3) minVersionParts.add(0);
    
    // 比較主版本號
    if (currentVersionParts[0] < minVersionParts[0]) return true;
    if (currentVersionParts[0] > minVersionParts[0]) return false;
    
    // 比較次版本號
    if (currentVersionParts[1] < minVersionParts[1]) return true;
    if (currentVersionParts[1] > minVersionParts[1]) return false;
    
    // 比較修訂版本號
    if (currentVersionParts[2] < minVersionParts[2]) return true;
    if (currentVersionParts[2] > minVersionParts[2]) return false;
    
    // 版本號相同時，比較 build number
    return currentBuildNumber < minRequiredBuildNumber;
  }

  /// 檢查是否有新版本可用（非強制更新）
  bool hasNewVersion(String currentVersion, int currentBuildNumber) {
    // 比較版本號
    final currentVersionParts = currentVersion.split('.').map(int.parse).toList();
    final latestVersionParts = version.split('.').map(int.parse).toList();
    
    // 確保版本號格式一致
    while (currentVersionParts.length < 3) currentVersionParts.add(0);
    while (latestVersionParts.length < 3) latestVersionParts.add(0);
    
    // 比較主版本號
    if (currentVersionParts[0] < latestVersionParts[0]) return true;
    if (currentVersionParts[0] > latestVersionParts[0]) return false;
    
    // 比較次版本號
    if (currentVersionParts[1] < latestVersionParts[1]) return true;
    if (currentVersionParts[1] > latestVersionParts[1]) return false;
    
    // 比較修訂版本號
    if (currentVersionParts[2] < latestVersionParts[2]) return true;
    if (currentVersionParts[2] > latestVersionParts[2]) return false;
    
    // 版本號相同時，比較 build number
    return currentBuildNumber < buildNumber;
  }

  @override
  String toString() {
    return 'AppVersion(version: $version, buildNumber: $buildNumber, forceUpdate: $forceUpdate)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AppVersion &&
        other.version == version &&
        other.buildNumber == buildNumber;
  }

  @override
  int get hashCode => version.hashCode ^ buildNumber.hashCode;
}

/// 版本檢查結果
enum VersionCheckResult {
  /// 版本是最新的
  upToDate,
  /// 有新版本可用（可選更新）
  updateAvailable,
  /// 需要強制更新
  forceUpdateRequired,
  /// 檢查失敗
  checkFailed,
}

/// 版本檢查狀態
class VersionCheckStatus {
  final VersionCheckResult result;
  final AppVersion? latestVersion;
  final String? errorMessage;

  const VersionCheckStatus({
    required this.result,
    this.latestVersion,
    this.errorMessage,
  });

  /// 創建成功狀態
  factory VersionCheckStatus.success(AppVersion latestVersion, VersionCheckResult result) {
    return VersionCheckStatus(
      result: result,
      latestVersion: latestVersion,
    );
  }

  /// 創建失敗狀態
  factory VersionCheckStatus.failure(String errorMessage) {
    return VersionCheckStatus(
      result: VersionCheckResult.checkFailed,
      errorMessage: errorMessage,
    );
  }

  bool get isSuccess => result != VersionCheckResult.checkFailed;
  bool get needsUpdate => result == VersionCheckResult.updateAvailable || result == VersionCheckResult.forceUpdateRequired;
  bool get isForceUpdate => result == VersionCheckResult.forceUpdateRequired;
}
