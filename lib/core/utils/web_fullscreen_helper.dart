// ignore: avoid_web_libraries_in_flutter, deprecated_member_use
import 'dart:html' as html;
// ignore: avoid_web_libraries_in_flutter, deprecated_member_use
import 'dart:js' as js;

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

import '../../astreal.dart';

/// 網頁全螢幕輔助工具
class WebFullscreenHelper {
  static bool _isFullscreenEnabled = false;
  static bool _isMobileDevice = false;
  static Function()? _onFullscreenChanged;

  /// 檢查是否為移動設備
  static bool get isMobileDevice => _isMobileDevice;

  /// 檢查是否為iOS設備（在Web環境中）
  static bool get isIOSDevice {
    if (!kIsWeb) return false;

    try {
      final ua = html.window.navigator.userAgent.toLowerCase();
      return ua.contains('iphone') || ua.contains('ipad') || ua.contains('ipod');
    } catch (e) {
      logger.w('檢測iOS設備失敗: $e');
      return false;
    }
  }

  /// 檢查是否已啟用全螢幕
  static bool get isFullscreenEnabled {
    // 每次獲取時都檢查實際狀態，確保同步
    if (kIsWeb) {
      final actualState = _checkFullscreenState();
      if (actualState != _isFullscreenEnabled) {
        logger.d('檢測到全螢幕狀態不同步，更新狀態: $_isFullscreenEnabled -> $actualState');
        _isFullscreenEnabled = actualState;
      }
    }
    return _isFullscreenEnabled;
  }

  /// 初始化並檢測設備類型
  static Future<void> initialize() async {
    if (!kIsWeb) return;

    try {
      _detectMobileDevice();

      if (_isMobileDevice) {
        await _setupMobileWebFullscreen();
        logger.i('移動設備網頁全螢幕模式已設置');
      } else {
        logger.i('桌面設備，無需設置全螢幕模式');
      }

      logger.i('網頁全螢幕輔助工具初始化完成 - 移動設備: $_isMobileDevice');
    } catch (e) {
      logger.e('網頁全螢幕輔助工具初始化失敗: $e');
    }
  }

  /// 檢測是否為移動設備
  static void _detectMobileDevice() {
    if (kIsWeb) {
      try {
        final userAgent = html.window.navigator.userAgent.toLowerCase();
        final screenWidth = html.window.screen?.width ?? 0;
        final screenHeight = html.window.screen?.height ?? 0;

        // 檢查 User-Agent 中的移動設備標識
        final isMobileUserAgent = userAgent.contains('mobile') ||
            userAgent.contains('android') ||
            userAgent.contains('iphone') ||
            userAgent.contains('ipad') ||
            userAgent.contains('ipod') ||
            userAgent.contains('blackberry') ||
            userAgent.contains('windows phone');

        // 檢查螢幕尺寸（寬度小於等於 768px 或高度小於等於 1024px）
        final isSmallScreen = screenWidth <= 768 || screenHeight <= 1024;

        _isMobileDevice = isMobileUserAgent || isSmallScreen;

        logger.i('設備檢測 - UserAgent: $userAgent');
        logger.i('設備檢測 - 螢幕尺寸: ${screenWidth}x$screenHeight');
        logger.i('設備檢測 - 是否為移動設備: $_isMobileDevice');
      } catch (e) {
        logger.w('設備檢測失敗，假設為移動設備: $e');
        _isMobileDevice = true;
      }
    }
  }

  /// 設置移動網頁全螢幕模式
  static Future<void> _setupMobileWebFullscreen() async {
    try {
      // 設置全螢幕事件監聽器
      _setupFullscreenListeners();

      // 設置 viewport meta 標籤
      _setViewportMeta();

      // 隱藏瀏覽器 UI
      await _hideBrowserUI();

      logger.i('移動網頁全螢幕模式設置完成');
    } catch (e) {
      logger.w('設置移動網頁全螢幕模式失敗: $e');
    }
  }

  /// 設置 viewport meta 標籤
  static void _setViewportMeta() {
    try {
      // 檢查是否已存在 viewport meta 標籤
      var viewportMeta = html.document.querySelector('meta[name="viewport"]') as html.MetaElement?;

      if (viewportMeta == null) {
        // 創建新的 viewport meta 標籤
        viewportMeta = html.MetaElement()
          ..name = 'viewport'
          ..content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover';
        html.document.head?.append(viewportMeta);
      } else {
        // 更新現有的 viewport meta 標籤
        viewportMeta.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover';
      }

      logger.d('Viewport meta 標籤已設置');
    } catch (e) {
      logger.w('設置 viewport meta 標籤失敗: $e');
    }
  }

  /// 隱藏瀏覽器 UI
  static Future<void> _hideBrowserUI() async {
    try {
      // 使用 Flutter 的 SystemChrome API
      await SystemChrome.setEnabledSystemUIMode(
        SystemUiMode.immersiveSticky,
        overlays: [],
      );

      logger.d('瀏覽器 UI 已隱藏');
    } catch (e) {
      logger.w('隱藏瀏覽器 UI 失敗: $e');
    }
  }

  /// 設置全螢幕事件監聽器
  static void _setupFullscreenListeners() {
    try {
      // 監聽全螢幕變化事件
      html.document.onFullscreenChange.listen((event) {
        final wasFullscreen = _isFullscreenEnabled;
        _isFullscreenEnabled = html.document.fullscreenElement != null;

        logger.d('全螢幕狀態變化: $wasFullscreen -> $_isFullscreenEnabled');

        // 如果狀態真的改變了，觸發回調
        if (wasFullscreen != _isFullscreenEnabled && _onFullscreenChanged != null) {
          _onFullscreenChanged!();
        }
      });

      // 監聽視窗大小變化
      html.window.onResize.listen((event) {
        logger.d('視窗大小變化: ${html.window.innerWidth}x${html.window.innerHeight}');
      });

      logger.d('全螢幕事件監聽器已設置');
    } catch (e) {
      logger.w('設置全螢幕事件監聽器失敗: $e');
    }
  }

  /// iOS Safari 全螢幕 workaround（使用 video 元素）
  static void _enterFullscreenIOSVideo() {
    try {
      logger.d('🍎 建立 video 元素進行全螢幕 workaround');

      // 建立透明的 video 元素
      final video = html.VideoElement()
        ..src = ""
        ..autoplay = true
        ..muted = true
        ..loop = true
        ..style.display = 'none'
        ..style.position = 'fixed'
        ..style.top = '0'
        ..style.left = '0'
        ..style.width = '100%'
        ..style.height = '100%'
        ..style.zIndex = '-9999'
        ..style.backgroundColor = 'transparent';

      // 添加到頁面
      html.document.body?.append(video);

      // 嘗試進入全螢幕
      final jsVideo = js.JsObject.fromBrowserObject(video);

      try {
        if (jsVideo.hasProperty('webkitEnterFullscreen')) {
          jsVideo.callMethod('webkitEnterFullscreen');
          logger.d('🍎 使用 webkitEnterFullscreen API');
        } else if (jsVideo.hasProperty('webkitRequestFullscreen')) {
          jsVideo.callMethod('webkitRequestFullscreen');
          logger.d('🍎 使用 webkitRequestFullscreen API');
        } else if (jsVideo.hasProperty('requestFullscreen')) {
          jsVideo.callMethod('requestFullscreen');
          logger.d('🍎 使用標準 requestFullscreen API');
        } else {
          logger.w('🍎 video 不支援全螢幕方法，回退到 CSS 方案');
          video.remove();
          _enterFullscreenIOSCSS();
          return;
        }

        _isFullscreenEnabled = true;
        logger.d('🍎 已請求透過 video 進入全螢幕');

        // 設置事件監聽器
        video.onEnded.listen((_) {
          logger.d('🍎 video 結束，清理元素');
          video.remove();
          _isFullscreenEnabled = false;
          if (_onFullscreenChanged != null) {
            _onFullscreenChanged!();
          }
        });

        video.onError.listen((_) {
          logger.w('🍎 video 錯誤，清理元素');
          video.remove();
          _isFullscreenEnabled = false;
          if (_onFullscreenChanged != null) {
            _onFullscreenChanged!();
          }
        });

        // 監聽全螢幕變化
        video.onFullscreenChange.listen((_) {
          final isFullscreen = html.document.fullscreenElement != null;
          logger.d('🍎 video 全螢幕狀態變化: $isFullscreen');
          _isFullscreenEnabled = isFullscreen;
          if (_onFullscreenChanged != null) {
            _onFullscreenChanged!();
          }

          // 如果退出全螢幕，清理 video 元素
          if (!isFullscreen) {
            video.remove();
          }
        });

      } catch (e) {
        logger.w('🍎 video 全螢幕調用失敗: $e');
        video.remove();
        _enterFullscreenIOSCSS();
      }

    } catch (e) {
      logger.w('🍎 iOS video 全螢幕失敗: $e');
      _enterFullscreenIOSCSS();
    }
  }

  /// iOS Safari CSS 偽全螢幕（備用方案）
  static void _enterFullscreenIOSCSS() {
    logger.d('🍎 使用 CSS 偽全螢幕作為備用方案');

    try {
      html.document.body?.style
        ?..overflow = 'hidden'
        ..margin = '0'
        ..padding = '0'
        ..height = '100vh'
        ..width = '100vw';

      html.document.documentElement?.style
        ?..overflow = 'hidden'
        ..height = '100vh'
        ..width = '100vw';

      _isFullscreenEnabled = true;
      logger.d('🍎 已啟用 iOS CSS 偽全螢幕模式');

      if (_onFullscreenChanged != null) {
        _onFullscreenChanged!();
      }
    } catch (e) {
      logger.w('🍎 CSS 偽全螢幕也失敗: $e');
    }
  }

  /// 進入全螢幕模式
  static void _enterFullscreen() {
    if (!kIsWeb) {
      logger.d('❌ 非 Web 環境，不支援此方法');
      return;
    }

    final ua = html.window.navigator.userAgent.toLowerCase();
    final isIOS = ua.contains('iphone') || ua.contains('ipad');
    final isSafari = ua.contains('safari') && !ua.contains('crios') && !ua.contains('fxios');

    if (isIOS && isSafari) {
      // iPhone Safari 使用 video workaround
      logger.d('🍎 iOS Safari 使用 video workaround 進入全螢幕');
      _enterFullscreenIOSVideo();
      return;
    }

    // 桌機與 Android
    try {
      final docElm = html.document.documentElement;

      if (docElm != null) {
        final jsElement = js.JsObject.fromBrowserObject(docElm);

        if (jsElement.hasProperty('requestFullscreen')) {
          jsElement.callMethod('requestFullscreen');
          logger.d('🖥️ 使用標準 requestFullscreen API');
        } else if (jsElement.hasProperty('webkitRequestFullscreen')) {
          jsElement.callMethod('webkitRequestFullscreen');
          logger.d('🖥️ 使用 webkitRequestFullscreen API');
        } else if (jsElement.hasProperty('mozRequestFullScreen')) {
          jsElement.callMethod('mozRequestFullScreen');
          logger.d('🖥️ 使用 mozRequestFullScreen API');
        } else if (jsElement.hasProperty('msRequestFullscreen')) {
          jsElement.callMethod('msRequestFullscreen');
          logger.d('🖥️ 使用 msRequestFullscreen API');
        } else {
          logger.w('⚠️ 此瀏覽器不支援全螢幕 API');
          return;
        }

        _isFullscreenEnabled = true;
        logger.d('🖥️ 已請求進入全螢幕模式');
      } else {
        logger.w('❌ 無法獲取 document.documentElement');
      }
    } catch (e) {
      logger.w('❌ 進入全螢幕模式失敗: $e');
    }
  }


  /// iOS Safari 退出全螢幕
  static void _exitFullscreenIOS() {
    try {
      // 首先嘗試退出 video 全螢幕
      final videos = html.document.querySelectorAll('video');
      for (final video in videos) {
        try {
          final jsVideo = js.JsObject.fromBrowserObject(video);
          if (jsVideo.hasProperty('webkitExitFullscreen')) {
            jsVideo.callMethod('webkitExitFullscreen');
            logger.d('🍎 使用 webkitExitFullscreen 退出 video 全螢幕');
          }
          // 移除 video 元素
          video.remove();
        } catch (e) {
          logger.w('🍎 退出 video 全螢幕失敗: $e');
        }
      }

      // 還原 CSS 偽全螢幕樣式
      html.document.body?.style
        ?..overflow = ''
        ..margin = ''
        ..padding = ''
        ..height = ''
        ..width = '';

      html.document.documentElement?.style
        ?..overflow = ''
        ..height = ''
        ..width = '';

      _isFullscreenEnabled = false;
      logger.d('🍎 已退出 iOS 全螢幕模式');

      if (_onFullscreenChanged != null) {
        _onFullscreenChanged!();
      }

    } catch (e) {
      logger.w('🍎 iOS 退出全螢幕失敗: $e');
    }
  }

  /// 退出全螢幕模式
  static void _exitFullscreen() {
    if (!kIsWeb) {
      logger.d('❌ 非 Web 環境，不支援此方法');
      return;
    }

    final ua = html.window.navigator.userAgent.toLowerCase();
    final isIOS = ua.contains('iphone') || ua.contains('ipad');
    final isSafari = ua.contains('safari') && !ua.contains('crios') && !ua.contains('fxios');

    if (isIOS && isSafari) {
      // iOS Safari 退出全螢幕處理
      logger.d('🍎 iOS Safari 退出全螢幕');
      _exitFullscreenIOS();
      return;
    }

    try {
      final jsDocument = js.JsObject.fromBrowserObject(html.document);

      if (jsDocument.hasProperty('exitFullscreen')) {
        jsDocument.callMethod('exitFullscreen');
        logger.d('🖥️ 使用標準 exitFullscreen API');
      } else if (jsDocument.hasProperty('webkitExitFullscreen')) {
        jsDocument.callMethod('webkitExitFullscreen');
        logger.d('🖥️ 使用 webkitExitFullscreen API');
      } else if (jsDocument.hasProperty('mozCancelFullScreen')) {
        jsDocument.callMethod('mozCancelFullScreen');
        logger.d('🖥️ 使用 mozCancelFullScreen API');
      } else if (jsDocument.hasProperty('msExitFullscreen')) {
        jsDocument.callMethod('msExitFullscreen');
        logger.d('🖥️ 使用 msExitFullscreen API');
      } else {
        logger.w('⚠️ 此瀏覽器不支援退出全螢幕 API');
        return;
      }

      _isFullscreenEnabled = false;
      logger.d('🖥️ 已請求退出全螢幕模式');
    } catch (e) {
      logger.w('❌ 退出全螢幕模式失敗: $e');
    }
  }


  /// 手動觸發全螢幕模式
  static Future<void> enterFullscreen() async {
    if (kIsWeb) {
      logger.d('🖥️ 開始進入全螢幕模式...');
      final wasFullscreen = _isFullscreenEnabled;

      _enterFullscreen();

      // 給一點時間讓瀏覽器處理全螢幕請求
      await Future.delayed(const Duration(milliseconds: 300));

      // 檢查狀態是否改變，如果瀏覽器事件沒有觸發，手動更新
      final newFullscreenState = _checkFullscreenState();
      logger.d('🖥️ 全螢幕狀態檢查: $wasFullscreen -> $newFullscreenState');

      if (newFullscreenState != wasFullscreen) {
        _isFullscreenEnabled = newFullscreenState;
        if (_onFullscreenChanged != null) {
          logger.d('🖥️ 觸發回調更新 UI');
          _onFullscreenChanged!();
        }
        logger.d('🖥️ 手動觸發全螢幕狀態更新: $newFullscreenState');
      } else {
        logger.w('⚠️ 全螢幕狀態沒有改變，可能操作失敗');
      }
    }
  }

  /// 手動退出全螢幕模式
  static Future<void> exitFullscreen() async {
    if (kIsWeb) {
      logger.d('🖥️ 開始退出全螢幕模式...');
      final wasFullscreen = _isFullscreenEnabled;

      _exitFullscreen();

      // 給一點時間讓瀏覽器處理退出全螢幕請求
      await Future.delayed(const Duration(milliseconds: 300));

      // 檢查狀態是否改變，如果瀏覽器事件沒有觸發，手動更新
      final newFullscreenState = _checkFullscreenState();
      logger.d('🖥️ 全螢幕狀態檢查: $wasFullscreen -> $newFullscreenState');

      if (newFullscreenState != wasFullscreen) {
        _isFullscreenEnabled = newFullscreenState;
        if (_onFullscreenChanged != null) {
          logger.d('🖥️ 觸發回調更新 UI');
          _onFullscreenChanged!();
        }
        logger.d('🖥️ 手動觸發全螢幕狀態更新: $newFullscreenState');
      } else {
        logger.w('⚠️ 全螢幕狀態沒有改變，可能操作失敗');
      }
    }
  }

  /// 檢查當前全螢幕狀態
  static bool _checkFullscreenState() {
    if (kIsWeb) {
      try {
        return html.document.fullscreenElement != null;
      } catch (e) {
        logger.w('檢查全螢幕狀態失敗: $e');
        return false;
      }
    }
    return false;
  }

  /// 設置全螢幕狀態變化回調
  static void setOnFullscreenChanged(Function()? callback) {
    _onFullscreenChanged = callback;
  }

  /// 移除全螢幕狀態變化回調
  static void removeOnFullscreenChanged() {
    _onFullscreenChanged = null;
  }

  /// 調試方法：打印當前狀態
  static void debugPrintState() {
    if (kIsWeb) {
      final actualState = _checkFullscreenState();
      logger.d('🔍 調試狀態:');
      logger.d('  - 內部狀態: $_isFullscreenEnabled');
      logger.d('  - 實際狀態: $actualState');
      logger.d('  - 移動設備: $_isMobileDevice');
      logger.d('  - 回調已設置: ${_onFullscreenChanged != null}');
    }
  }
}
