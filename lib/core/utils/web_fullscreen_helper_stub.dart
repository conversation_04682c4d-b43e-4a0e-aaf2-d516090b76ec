/// 網頁全螢幕輔助工具 Stub（非 Web 平台）
class WebFullscreenHelper {
  /// 初始化（空實現）
  static Future<void> initialize() async {
    // 非 Web 平台不需要實現
  }

  /// 檢查是否為移動設備（總是返回 false）
  static bool get isMobileDevice => false;

  /// 檢查是否為iOS設備（總是返回 false）
  static bool get isIOSDevice => false;

  /// 檢查是否已啟用全螢幕（總是返回 false）
  static bool get isFullscreenEnabled => false;
  
  /// 手動觸發全螢幕模式（空實現）
  static Future<void> enterFullscreen() async {
    // 非 Web 平台不需要實現
  }
  
  /// 退出全螢幕模式（空實現）
  static Future<void> exitFullscreen() async {
    // 非 Web 平台不需要實現
  }

  /// 設置全螢幕狀態變化回調（空實現）
  static void setOnFullscreenChanged(Function()? callback) {
    // 非 Web 平台不需要實現
  }

  /// 移除全螢幕狀態變化回調（空實現）
  static void removeOnFullscreenChanged() {
    // 非 Web 平台不需要實現
  }

  /// 調試方法：打印當前狀態（空實現）
  static void debugPrintState() {
    // 非 Web 平台不需要實現
  }
}
