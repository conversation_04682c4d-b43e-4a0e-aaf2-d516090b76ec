import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:package_info_plus/package_info_plus.dart';

import '../../shared/utils/logger_utils.dart';

/// 建置資訊工具類
///
/// 提供建置時間、版本格式化等功能
class BuildInfoUtils {

  /// 編譯時間常數
  /// 這個值在編譯時會被替換為實際的建置時間
  static const String _compilationTimestamp = String.fromEnvironment(
    'COMPILATION_TIMESTAMP',
    defaultValue: '', // 如果沒有設定，使用空字串
  );
  
  /// 從建置號碼中提取建置時間
  /// 
  /// 支援的格式：
  /// - 標準格式：20250807_131801_force_update0 -> 2025-08-07 13:18:01
  /// - 簡單格式：20250807131801 -> 2025-08-07 13:18:01
  /// - 數字格式：1 -> 使用編譯時間
  static String getBuildDate(String buildNumber) {
    try {
      // 如果建置號碼為空或是簡單數字（如 "1"），使用編譯時間
      if (buildNumber.isEmpty || buildNumber == '1' || buildNumber.contains('dev') || buildNumber.contains('debug')) {
        return _getCompileTime();
      }

      // 處理帶下劃線的格式：20250807_131801 或 20250807_131801_suffix
      String cleanBuildNumber;
      if (buildNumber.contains('_')) {
        List<String> parts = buildNumber.split('_');
        if (parts.length >= 2 && RegExp(r'^\d{6,8}$').hasMatch(parts[1])) {
          // 第二部分是時間：20250807 + 131801
          cleanBuildNumber = parts[0] + parts[1];
        } else {
          // 只有日期部分
          cleanBuildNumber = parts[0];
        }
      } else {
        cleanBuildNumber = buildNumber;
      }

      // 檢查是否為完整時間戳格式（14位數字）
      if (cleanBuildNumber.length >= 14 && RegExp(r'^\d{14}').hasMatch(cleanBuildNumber)) {
        // 格式：YYYYMMDDHHMMSS
        String year = cleanBuildNumber.substring(0, 4);
        String month = cleanBuildNumber.substring(4, 6);
        String day = cleanBuildNumber.substring(6, 8);
        String hour = cleanBuildNumber.substring(8, 10);
        String minute = cleanBuildNumber.substring(10, 12);
        String second = cleanBuildNumber.substring(12, 14);

        // 驗證日期時間的有效性
        DateTime buildDateTime = DateTime(
          int.parse(year),
          int.parse(month),
          int.parse(day),
          int.parse(hour),
          int.parse(minute),
          int.parse(second),
        );

        return DateFormat('yyyy-MM-dd HH:mm:ss').format(buildDateTime);
      }

      // 檢查是否為日期格式（8位數字）
      if (cleanBuildNumber.length >= 8 && RegExp(r'^\d{8}').hasMatch(cleanBuildNumber)) {
        // 格式：YYYYMMDD
        String year = cleanBuildNumber.substring(0, 4);
        String month = cleanBuildNumber.substring(4, 6);
        String day = cleanBuildNumber.substring(6, 8);

        DateTime buildDate = DateTime(
          int.parse(year),
          int.parse(month),
          int.parse(day),
        );

        return DateFormat('yyyy-MM-dd').format(buildDate);
      }

      // 如果無法解析，返回編譯時間
      return _getCompileTime();
    } catch (e) {
      // 解析失敗，返回編譯時間
      return _getCompileTime();
    }
  }
  
  /// 格式化版本號顯示
  /// 
  /// 將複雜的建置號碼轉換為更友好的顯示格式
  static String formatVersionDisplay(String version, String buildNumber) {
    try {
      // 如果建置號碼包含時間戳，提取並格式化
      if (buildNumber.contains('_')) {
        List<String> parts = buildNumber.split('_');
        String datePart = parts[0];
        String timePart = parts.length > 1 ? parts[1] : '';

        // 檢查是否為日期時間格式
        if (datePart.length >= 8 && RegExp(r'^\d{8}').hasMatch(datePart) &&
            timePart.length >= 6 && RegExp(r'^\d{6}').hasMatch(timePart)) {
          // 格式：20250807_131801 -> 20250807.1318
          String year = datePart.substring(0, 4);
          String month = datePart.substring(4, 6);
          String day = datePart.substring(6, 8);
          String hour = timePart.substring(0, 2);
          String minute = timePart.substring(2, 4);

          String formattedBuild = '$year$month$day.$hour$minute';

          // 檢查是否有特殊標記
          if (parts.length > 2) {
            String suffix = parts.sublist(2).join('_');
            if (suffix.contains('force_update')) {
              return '$version ($formattedBuild-強制更新)';
            } else if (suffix.contains('beta')) {
              return '$version ($formattedBuild-測試版)';
            } else if (suffix.contains('alpha')) {
              return '$version ($formattedBuild-內測版)';
            } else {
              return '$version ($formattedBuild)';
            }
          }

          return '$version ($formattedBuild)';
        }
      }

      // 檢查是否為連續的時間戳格式（14位數字）
      if (buildNumber.length >= 14 && RegExp(r'^\d{14}').hasMatch(buildNumber)) {
        String year = buildNumber.substring(0, 4);
        String month = buildNumber.substring(4, 6);
        String day = buildNumber.substring(6, 8);
        String hour = buildNumber.substring(8, 10);
        String minute = buildNumber.substring(10, 12);

        String formattedBuild = '$year$month$day.$hour$minute';
        return '$version ($formattedBuild)';
      }

      // 標準格式
      return '$version ($buildNumber)';
    } catch (e) {
      // 格式化失敗，返回原始格式
      return '$version ($buildNumber)';
    }
  }
  
  /// 獲取編譯時間（當無法從建置號碼解析時使用）
  static String _getCompileTime() {
    // 優先使用編譯時設定的時間戳
    if (_compilationTimestamp.isNotEmpty) {
      try {
        // 嘗試解析編譯時間戳
        if (_compilationTimestamp.length >= 14 && RegExp(r'^\d{14}').hasMatch(_compilationTimestamp)) {
          String year = _compilationTimestamp.substring(0, 4);
          String month = _compilationTimestamp.substring(4, 6);
          String day = _compilationTimestamp.substring(6, 8);
          String hour = _compilationTimestamp.substring(8, 10);
          String minute = _compilationTimestamp.substring(10, 12);
          String second = _compilationTimestamp.substring(12, 14);

          DateTime compilationTime = DateTime(
            int.parse(year),
            int.parse(month),
            int.parse(day),
            int.parse(hour),
            int.parse(minute),
            int.parse(second),
          );

          return DateFormat('yyyy-MM-dd HH:mm:ss').format(compilationTime);
        }
      } catch (e) {
        // 解析失敗，繼續使用預設邏輯
      }
    }

    // 如果沒有編譯時間戳或解析失敗，返回更友好的顯示
    return _getFallbackBuildTime();
  }

  /// 獲取後備建置時間顯示
  static String _getFallbackBuildTime() {
    // 嘗試使用當前日期作為開發版本的參考時間
    final now = DateTime.now();
    final today = DateFormat('yyyy-MM-dd HH:mm:ss').format(now);

    // 根據不同環境顯示不同資訊
    // if (kIsWeb) {
    //   return '$today\n(網頁版)';
    // } else if (kDebugMode) {
    //   return '$today\n(除錯版本)';
    // } else if (kProfileMode) {
    //   return '$today\n(效能分析版)';
    // } else {
    //   return '$today\n(本地建置)';
    // }
    return today;
  }
  
  /// 獲取完整的建置資訊
  static Future<BuildInfo> getBuildInfo() async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    logger.d('getBuildInfo() - updateTime: ${packageInfo.updateTime} installedTime: ${packageInfo.installTime}');
    return BuildInfo(
      appName: packageInfo.appName,
      packageName: packageInfo.packageName,
      version: packageInfo.version,
      buildNumber: packageInfo.buildNumber,
      buildDate: getBuildDate(packageInfo.version),
      formattedVersion: formatVersionDisplay(packageInfo.version, packageInfo.buildNumber),
    );
  }
  
  /// 檢查是否為開發版本
  static bool isDevelopmentBuild(String buildNumber) {
    return buildNumber == '1' || 
           buildNumber.contains('dev') || 
           buildNumber.contains('debug');
  }
  
  /// 檢查是否為強制更新版本
  static bool isForceUpdateBuild(String buildNumber) {
    return buildNumber.contains('force_update');
  }
  
  /// 檢查是否為測試版本
  static bool isBetaBuild(String buildNumber) {
    return buildNumber.contains('beta') || buildNumber.contains('alpha');
  }
}

/// 建置資訊資料類別
class BuildInfo {
  final String appName;
  final String packageName;
  final String version;
  final String buildNumber;
  final String buildDate;
  final String formattedVersion;
  
  const BuildInfo({
    required this.appName,
    required this.packageName,
    required this.version,
    required this.buildNumber,
    required this.buildDate,
    required this.formattedVersion,
  });
  
  /// 獲取版本標籤
  String get versionTag {
    if (BuildInfoUtils.isForceUpdateBuild(buildNumber)) {
      return '強制更新';
    } else if (BuildInfoUtils.isBetaBuild(buildNumber)) {
      return '測試版';
    } else if (BuildInfoUtils.isDevelopmentBuild(buildNumber)) {
      return '開發版';
    }
    return '正式版';
  }
  
  /// 獲取版本標籤顏色
  Color get versionTagColor {
    if (BuildInfoUtils.isForceUpdateBuild(buildNumber)) {
      return const Color(0xFFE53E3E); // 紅色
    } else if (BuildInfoUtils.isBetaBuild(buildNumber)) {
      return const Color(0xFFED8936); // 橙色
    } else if (BuildInfoUtils.isDevelopmentBuild(buildNumber)) {
      return const Color(0xFF38B2AC); // 青色
    }
    return const Color(0xFF48BB78); // 綠色
  }
  
  @override
  String toString() {
    return 'BuildInfo(appName: $appName, version: $version, buildNumber: $buildNumber, buildDate: $buildDate)';
  }
}
