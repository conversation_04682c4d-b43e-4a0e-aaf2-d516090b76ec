import 'package:flutter/material.dart';

/// Web 導航輔助工具 Stub 實現
/// 用於非 Web 平台的空實現
class WebNavigationHelper {
  /// 初始化 Web 導航輔助工具（空實現）
  static void initialize() {
    // 非 Web 平台不需要實現
  }

  /// 推送新路由到歷史記錄（空實現）
  static void pushRoute(String route) {
    // 非 Web 平台不需要實現
  }

  /// 替換當前路由（空實現）
  static void replaceRoute(String route) {
    // 非 Web 平台不需要實現
  }

  /// 設置自定義返回處理（空實現）
  static void setBackPressedHandler(VoidCallback? handler) {
    // 非 Web 平台不需要實現
  }

  /// 清除自定義返回處理（空實現）
  static void clearBackPressedHandler() {
    // 非 Web 平台不需要實現
  }

  /// 獲取當前路由（空實現）
  static String? get currentRoute => null;

  /// 獲取路由歷史記錄（空實現）
  static List<String> get routeHistory => [];

  /// 檢查是否可以返回（空實現）
  static bool get canGoBack => false;

  /// 手動觸發返回（空實現）
  static void goBack() {
    // 非 Web 平台不需要實現
  }

  /// 清除路由歷史記錄（空實現）
  static void clearHistory() {
    // 非 Web 平台不需要實現
  }

  /// 設置頁面標題（空實現）
  static void setPageTitle(String title) {
    // 非 Web 平台不需要實現
  }

  /// 獲取當前 URL 參數（空實現）
  static Map<String, String> getUrlParameters() {
    return {};
  }

  /// 更新 URL 參數（空實現）
  static void updateUrlParameters(Map<String, String> parameters) {
    // 非 Web 平台不需要實現
  }

  /// 檢查是否為移動設備（空實現）
  static bool get isMobileDevice => false;

  /// 銷毀資源（空實現）
  static void dispose() {
    // 非 Web 平台不需要實現
  }
}
