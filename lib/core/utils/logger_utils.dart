import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';

import 'persistent_logger.dart';

/// 全域日誌實例（向後兼容）
final logger = _AppLogger();

/// 應用程式日誌管理器
/// 整合控制台輸出和持久化存儲
class _AppLogger {
  late final Logger _consoleLogger;

  _AppLogger() {
    _consoleLogger = Logger(
      printer: kReleaseMode
          ? SimplePrinter() // 不顯示顏色、時間、stack trace 等
          : PrettyPrinter(
        methodCount: 0,
        errorMethodCount: 8,
        lineLength: 120,
        colors: false,
        printEmojis: false,
        dateTimeFormat: DateTimeFormat.onlyTimeAndSinceStart,
      ),
    );
  }

  /// Debug 級別日誌
  void d(String message, [dynamic error, StackTrace? stackTrace]) {
    if (!kReleaseMode) {
      _consoleLogger.d(message, error: error, stackTrace: stackTrace);
    }
    PersistentLogger.instance.d(message, error, stackTrace);
  }

  /// Info 級別日誌
  void i(String message, [dynamic error, StackTrace? stackTrace]) {
    if (!kReleaseMode) {
      _consoleLogger.i(message, error: error, stackTrace: stackTrace);
    }
    PersistentLogger.instance.i(message, error, stackTrace);
  }

  /// Warning 級別日誌
  void w(String message, [dynamic error, StackTrace? stackTrace]) {
    if (!kReleaseMode) {
      _consoleLogger.w(message, error: error, stackTrace: stackTrace);
    }
    PersistentLogger.instance.w(message, error, stackTrace);
  }

  /// Error 級別日誌
  void e(String message, [dynamic error, StackTrace? stackTrace]) {
    if (!kReleaseMode) {
      _consoleLogger.e(message, error: error, stackTrace: stackTrace);
    }
    PersistentLogger.instance.e(message, error, stackTrace);
  }

  /// Verbose 級別日誌（僅控制台輸出）
  void v(String message, [dynamic error, StackTrace? stackTrace]) {
    if (!kReleaseMode) {
      _consoleLogger.t(message, error: error, stackTrace: stackTrace);
    }
  }

  /// Fatal 級別日誌（嚴重錯誤）
  void f(String message, [dynamic error, StackTrace? stackTrace]) {
    if (!kReleaseMode) {
      _consoleLogger.f(message, error: error, stackTrace: stackTrace);
    }
    PersistentLogger.instance.e('[FATAL] $message', error, stackTrace);
  }
}