import 'dart:html' as html;

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import '../services/navigation_service.dart';
import 'logger_utils.dart';

/// Web 導航輔助工具
/// 處理網頁版的瀏覽器返回按鈕和路由管理
class WebNavigationHelper {
  static bool _initialized = false;
  static final List<String> _routeHistory = [];
  static String? _currentRoute;
  static VoidCallback? _onBackPressed;

  /// 初始化 Web 導航輔助工具
  static void initialize() {
    if (!kIsWeb || _initialized) return;

    try {
      // 監聽瀏覽器的 popstate 事件
      html.window.addEventListener('popstate', _handlePopState);
      
      // 監聽 beforeunload 事件，防止意外關閉
      html.window.addEventListener('beforeunload', _handleBeforeUnload);
      
      // 初始化當前路由
      _currentRoute = html.window.location.pathname;
      _routeHistory.add(_currentRoute!);
      
      _initialized = true;
      logger.i('🌐 Web 導航輔助工具已初始化');
    } catch (e) {
      logger.e('Web 導航輔助工具初始化失敗: $e');
    }
  }

  /// 處理瀏覽器返回事件
  static void _handlePopState(html.Event event) {
    logger.d('🔙 瀏覽器返回按鈕被按下');
    
    try {
      final context = NavigationService.currentContext;
      if (context == null) {
        logger.w('NavigationService.currentContext 為 null');
        return;
      }

      // 檢查是否可以在 Flutter 路由中返回
      if (Navigator.canPop(context)) {
        logger.d('🔙 使用 Flutter Navigator 返回');
        Navigator.pop(context);
      } else {
        logger.d('🔙 無法使用 Flutter Navigator 返回，觸發自定義返回處理');
        
        // 如果有自定義的返回處理，執行它
        if (_onBackPressed != null) {
          _onBackPressed!();
        } else {
          // 預設行為：導航到主頁面
          _navigateToHome(context);
        }
      }
    } catch (e) {
      logger.e('處理瀏覽器返回事件時出錯: $e');
    }
  }

  /// 處理頁面關閉前事件
  static void _handleBeforeUnload(html.Event event) {
    // 可以在這裡添加確認對話框或保存數據的邏輯
    logger.d('🌐 頁面即將關閉或重新載入');
  }

  /// 導航到主頁面
  static void _navigateToHome(BuildContext context) {
    try {
      // 清除路由堆疊並導航到主頁面
      Navigator.pushNamedAndRemoveUntil(
        context,
        '/main',
        (route) => false,
      );
      logger.d('🏠 已導航到主頁面');
    } catch (e) {
      logger.e('導航到主頁面時出錯: $e');
    }
  }

  /// 推送新路由到歷史記錄
  static void pushRoute(String route) {
    if (!kIsWeb) return;

    try {
      _currentRoute = route;
      _routeHistory.add(route);
      
      // 更新瀏覽器的 URL（不觸發頁面重新載入）
      html.window.history.pushState(null, '', route);
      
      logger.d('📍 推送路由到歷史記錄: $route');
    } catch (e) {
      logger.e('推送路由到歷史記錄時出錯: $e');
    }
  }

  /// 替換當前路由
  static void replaceRoute(String route) {
    if (!kIsWeb) return;

    try {
      _currentRoute = route;
      if (_routeHistory.isNotEmpty) {
        _routeHistory[_routeHistory.length - 1] = route;
      } else {
        _routeHistory.add(route);
      }
      
      // 替換瀏覽器的當前 URL
      html.window.history.replaceState(null, '', route);
      
      logger.d('🔄 替換當前路由: $route');
    } catch (e) {
      logger.e('替換當前路由時出錯: $e');
    }
  }

  /// 設置自定義返回處理
  static void setBackPressedHandler(VoidCallback? handler) {
    _onBackPressed = handler;
    logger.d('🔙 設置自定義返回處理: ${handler != null ? '已設置' : '已清除'}');
  }

  /// 清除自定義返回處理
  static void clearBackPressedHandler() {
    _onBackPressed = null;
    logger.d('🔙 已清除自定義返回處理');
  }

  /// 獲取當前路由
  static String? get currentRoute => _currentRoute;

  /// 獲取路由歷史記錄
  static List<String> get routeHistory => List.unmodifiable(_routeHistory);

  /// 檢查是否可以返回
  static bool get canGoBack => _routeHistory.length > 1;

  /// 手動觸發返回
  static void goBack() {
    if (!kIsWeb) return;

    try {
      html.window.history.back();
      logger.d('🔙 手動觸發瀏覽器返回');
    } catch (e) {
      logger.e('手動觸發瀏覽器返回時出錯: $e');
    }
  }

  /// 清除路由歷史記錄
  static void clearHistory() {
    _routeHistory.clear();
    _currentRoute = null;
    logger.d('🗑️ 已清除路由歷史記錄');
  }

  /// 設置頁面標題
  static void setPageTitle(String title) {
    if (!kIsWeb) return;

    try {
      html.document.title = title;
      logger.d('📄 設置頁面標題: $title');
    } catch (e) {
      logger.e('設置頁面標題時出錯: $e');
    }
  }

  /// 獲取當前 URL 參數
  static Map<String, String> getUrlParameters() {
    if (!kIsWeb) return {};

    try {
      final uri = Uri.parse(html.window.location.href);
      return uri.queryParameters;
    } catch (e) {
      logger.e('獲取 URL 參數時出錯: $e');
      return {};
    }
  }

  /// 更新 URL 參數（不重新載入頁面）
  static void updateUrlParameters(Map<String, String> parameters) {
    if (!kIsWeb) return;

    try {
      final currentUri = Uri.parse(html.window.location.href);
      final newUri = currentUri.replace(queryParameters: parameters);
      
      html.window.history.replaceState(null, '', newUri.toString());
      logger.d('🔗 更新 URL 參數: $parameters');
    } catch (e) {
      logger.e('更新 URL 參數時出錯: $e');
    }
  }

  /// 檢查是否為移動設備
  static bool get isMobileDevice {
    if (!kIsWeb) return false;
    
    try {
      final userAgent = html.window.navigator.userAgent.toLowerCase();
      return userAgent.contains('mobile') || 
             userAgent.contains('android') || 
             userAgent.contains('iphone') || 
             userAgent.contains('ipad');
    } catch (e) {
      logger.e('檢查移動設備時出錯: $e');
      return false;
    }
  }

  /// 銷毀資源
  static void dispose() {
    if (!kIsWeb || !_initialized) return;

    try {
      html.window.removeEventListener('popstate', _handlePopState);
      html.window.removeEventListener('beforeunload', _handleBeforeUnload);
      
      _routeHistory.clear();
      _currentRoute = null;
      _onBackPressed = null;
      _initialized = false;
      
      logger.i('🌐 Web 導航輔助工具已銷毀');
    } catch (e) {
      logger.e('銷毀 Web 導航輔助工具時出錯: $e');
    }
  }
}
