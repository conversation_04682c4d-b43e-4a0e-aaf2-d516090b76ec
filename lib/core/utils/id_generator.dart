import 'dart:convert';
import 'dart:math';

import 'package:crypto/crypto.dart';
import 'package:uuid/uuid.dart';

/// 安全的 ID 生成工具
class IdGenerator {
  static const Uuid _uuid = Uuid();
  static final Random _random = Random();

  /// 生成標準的 UUID v4
  /// 這是最安全的 ID 生成方式，保證全球唯一性
  static String generateUuid() {
    return _uuid.v4();
  }

  /// 基於內容生成確定性的 ID
  /// 相同的內容總是生成相同的 ID，避免重複
  static String generateContentBasedId(String content) {
    final bytes = utf8.encode(content);
    final digest = sha256.convert(bytes);
    return digest.toString().substring(0, 32); // 取前32位作為ID
  }

  /// 基於人物資訊生成確定性的 ID
  /// 用於確保相同的人物資訊總是生成相同的 ID
  static String generatePersonId({
    required String name,
    required DateTime birthDate,
    required String birthPlace,
    required double latitude,
    required double longitude,
  }) {
    // 創建唯一的內容字符串
    final content = [
      name.trim().toLowerCase(),
      birthDate.toIso8601String(),
      birthPlace.trim().toLowerCase(),
      latitude.toStringAsFixed(6),
      longitude.toStringAsFixed(6),
    ].join('|');

    return 'person_${generateContentBasedId(content)}';
  }

  /// 生成帶時間戳的 ID
  /// 用於需要時間順序的場景
  static String generateTimestampId({String? prefix}) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final randomSuffix = _random.nextInt(9999).toString().padLeft(4, '0');
    final baseId = '${timestamp}_$randomSuffix';
    
    return prefix != null ? '${prefix}_$baseId' : baseId;
  }

  /// 生成短 ID（8位字符）
  /// 用於用戶友好的顯示
  static String generateShortId() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    return List.generate(8, (index) => chars[_random.nextInt(chars.length)]).join();
  }

  /// 驗證 ID 是否為有效的 UUID 格式
  static bool isValidUuid(String id) {
    final uuidRegex = RegExp(
      r'^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$',
      caseSensitive: false,
    );
    return uuidRegex.hasMatch(id);
  }

  /// 驗證 ID 是否為有效格式（UUID 或自定義格式）
  static bool isValidId(String id) {
    if (id.isEmpty) return false;
    
    // 檢查是否為 UUID
    if (isValidUuid(id)) return true;
    
    // 檢查是否為自定義格式（字母數字和下劃線，長度至少8位）
    final customIdRegex = RegExp(r'^[a-zA-Z0-9_]{8,}$');
    return customIdRegex.hasMatch(id);
  }

  /// 為現有的無效 ID 生成新的有效 ID
  static String fixInvalidId(String? oldId, {String? fallbackContent}) {
    // 如果舊 ID 有效，直接返回
    if (oldId != null && isValidId(oldId)) {
      return oldId;
    }

    // 如果有回退內容，基於內容生成 ID
    if (fallbackContent != null && fallbackContent.isNotEmpty) {
      return generateContentBasedId(fallbackContent);
    }

    // 否則生成新的 UUID
    return generateUuid();
  }

  /// 檢查 ID 列表中是否有重複
  static List<String> findDuplicateIds(List<String> ids) {
    final seen = <String>{};
    final duplicates = <String>{};

    for (final id in ids) {
      if (seen.contains(id)) {
        duplicates.add(id);
      } else {
        seen.add(id);
      }
    }

    return duplicates.toList();
  }

  /// 為重複的 ID 生成新的唯一 ID
  static Map<String, String> generateUniqueIds(List<String> duplicateIds) {
    final result = <String, String>{};
    
    for (final duplicateId in duplicateIds) {
      result[duplicateId] = generateUuid();
    }
    
    return result;
  }
}
