import 'package:flutter/material.dart';

import '../../astreal.dart';
import '../../presentation/pages/notification/notification_page.dart';

/// 全局導航服務
/// 用於處理通知點擊等需要全局導航的場景
class NavigationService {
  static final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();
  
  /// 獲取當前上下文
  static BuildContext? get currentContext => navigatorKey.currentContext;
  
  /// 導航到通知中心頁面
  static Future<void> navigateToNotificationCenter({String? notificationId}) async {
    try {
      final context = currentContext;
      if (context == null) {
        logger.w('無法獲取當前上下文，無法導航到通知中心');
        return;
      }

      logger.i('導航到通知中心頁面${notificationId != null ? '，通知ID: $notificationId' : ''}');
      
      await Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => const NotificationPage(),
        ),
      );
    } catch (e) {
      logger.e('導航到通知中心失敗: $e');
    }
  }
  
  /// 根據通知類型和動作URL進行導航
  static Future<void> handleNotificationNavigation({
    required String notificationId,
    String? actionUrl,
    Map<String, dynamic>? data,
  }) async {
    try {
      logger.i('處理通知導航: $notificationId, actionUrl: $actionUrl');
      
      // 解析動作URL
      if (actionUrl != null && actionUrl.isNotEmpty) {
        await _handleActionUrl(actionUrl, data);
      } else {
        // 默認導航到通知中心
        await navigateToNotificationCenter(notificationId: notificationId);
      }
    } catch (e) {
      logger.e('處理通知導航失敗: $e');
      // 出錯時默認導航到通知中心
      await navigateToNotificationCenter(notificationId: notificationId);
    }
  }
  
  /// 處理動作URL
  static Future<void> _handleActionUrl(String actionUrl, Map<String, dynamic>? data) async {
    try {
      final uri = Uri.parse(actionUrl);
      final path = uri.path;
      
      logger.i('處理動作URL: $actionUrl, 路徑: $path');
      
      switch (path) {
        case '/notifications':
        case '/notification-center':
          await navigateToNotificationCenter();
          break;
          
        case '/system-announcement':
          // 導航到系統公告頁面
          await _navigateToSystemAnnouncement(uri.queryParameters);
          break;
          
        case '/feature-update':
          // 導航到功能更新頁面
          await _navigateToFeatureUpdate(uri.queryParameters);
          break;
          
        case '/promotion':
          // 導航到促銷活動頁面
          await _navigateToPromotion(uri.queryParameters);
          break;
          
        case '/astro-event':
          // 導航到占星事件頁面
          await _navigateToAstroEvent(uri.queryParameters);
          break;
          
        default:
          logger.w('未知的動作URL路徑: $path，導航到通知中心');
          await navigateToNotificationCenter();
          break;
      }
    } catch (e) {
      logger.e('處理動作URL失敗: $e');
      await navigateToNotificationCenter();
    }
  }
  
  /// 導航到系統公告頁面
  static Future<void> _navigateToSystemAnnouncement(Map<String, String> params) async {
    // 暫時導航到通知中心，後續可以實現專門的系統公告頁面
    await navigateToNotificationCenter();
  }
  
  /// 導航到功能更新頁面
  static Future<void> _navigateToFeatureUpdate(Map<String, String> params) async {
    // 暫時導航到通知中心，後續可以實現專門的功能更新頁面
    await navigateToNotificationCenter();
  }
  
  /// 導航到促銷活動頁面
  static Future<void> _navigateToPromotion(Map<String, String> params) async {
    // 暫時導航到通知中心，後續可以實現專門的促銷活動頁面
    await navigateToNotificationCenter();
  }
  
  /// 導航到占星事件頁面
  static Future<void> _navigateToAstroEvent(Map<String, String> params) async {
    // 暫時導航到通知中心，後續可以實現專門的占星事件頁面
    await navigateToNotificationCenter();
  }
  
  /// 顯示通知對話框（用於前景通知）
  static void showNotificationDialog({
    required String title,
    required String body,
    VoidCallback? onTap,
  }) {
    final context = currentContext;
    if (context == null) {
      logger.w('無法獲取當前上下文，無法顯示通知對話框');
      return;
    }
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            const Icon(Icons.notifications, color: Colors.blue),
            const SizedBox(width: 8),
            Expanded(child: Text(title)),
          ],
        ),
        content: Text(body),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('關閉'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              onTap?.call();
            },
            child: const Text('查看'),
          ),
        ],
      ),
    );
  }
  
  /// 顯示通知 SnackBar（用於前景通知）
  static void showNotificationSnackBar({
    required String title,
    required String body,
    VoidCallback? onTap,
  }) {
    final context = currentContext;
    if (context == null) {
      logger.w('無法獲取當前上下文，無法顯示通知 SnackBar');
      return;
    }
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              body,
              style: const TextStyle(color: Colors.white),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
        backgroundColor: Colors.blue,
        duration: const Duration(seconds: 4),
        action: onTap != null
            ? SnackBarAction(
                label: '查看',
                textColor: Colors.white,
                onPressed: onTap,
              )
            : null,
      ),
    );
  }
  
  /// 檢查是否可以導航
  static bool get canNavigate => currentContext != null;
  
  /// 返回上一頁
  static void goBack() {
    final context = currentContext;
    if (context != null && Navigator.canPop(context)) {
      Navigator.pop(context);
    }
  }
  
  /// 導航到指定路由
  static Future<void> navigateToRoute(String routeName, {Object? arguments}) async {
    final context = currentContext;
    if (context != null) {
      await Navigator.pushNamed(context, routeName, arguments: arguments);
    }
  }
  
  /// 替換當前路由
  static Future<void> replaceRoute(String routeName, {Object? arguments}) async {
    final context = currentContext;
    if (context != null) {
      await Navigator.pushReplacementNamed(context, routeName, arguments: arguments);
    }
  }
  
  /// 清除所有路由並導航到指定路由
  static Future<void> navigateAndClearStack(String routeName, {Object? arguments}) async {
    final context = currentContext;
    if (context != null) {
      await Navigator.pushNamedAndRemoveUntil(
        context,
        routeName,
        (route) => false,
        arguments: arguments,
      );
    }
  }
}
