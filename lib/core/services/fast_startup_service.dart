import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../data/services/api/remote_config_service.dart';
import '../utils/logger_utils.dart';

/// 快速啟動服務
/// 負責應用程式的快速啟動，將非關鍵服務延遲到背景初始化
class FastStartupService {
  static bool _isInitialized = false;
  static bool _backgroundInitialized = false;
  
  /// 快速初始化（只初始化啟動必需的服務）
  static Future<void> quickInitialize() async {
    if (_isInitialized) return;
    
    try {
      // logger.i('開始快速啟動初始化...');
      
      // 只初始化啟動必需的服務
      await _initializeEssentialServices();
      
      _isInitialized = true;
      // logger.i('快速啟動初始化完成');
      
      // 在背景初始化其他服務
      _initializeBackgroundServices();
      
    } catch (e) {
      logger.e('快速啟動初始化失敗: $e');
      rethrow;
    }
  }
  
  /// 初始化啟動必需的服務
  static Future<void> _initializeEssentialServices() async {
    // 這裡只包含應用啟動絕對必需的服務
    // 例如：用戶偏好設定的基本載入
    
    try {
      // 預載入 SharedPreferences 實例
      await SharedPreferences.getInstance();
      // logger.d('SharedPreferences 預載入完成');
    } catch (e) {
      logger.e('SharedPreferences 預載入失敗: $e');
      // 不拋出異常，因為這不是關鍵錯誤
    }
  }
  
  /// 在背景初始化其他服務
  static void _initializeBackgroundServices() {
    if (_backgroundInitialized) return;
    
    Future.microtask(() async {
      try {
        // logger.i('開始背景服務初始化...');
        
        // 1. 初始化 Remote Config（非阻塞）
        await _initializeRemoteConfigAsync();
        
        // 2. 其他非關鍵服務
        await _initializeOtherServices();
        
        _backgroundInitialized = true;
        // logger.i('背景服務初始化完成');
        
      } catch (e) {
        logger.e('背景服務初始化失敗: $e');
        // 背景服務失敗不影響應用運行
      }
    });
  }
  
  /// 異步初始化 Remote Config
  static Future<void> _initializeRemoteConfigAsync() async {
    try {
      // logger.i('開始異步初始化 Remote Config...');
      
      // 設置較短的超時時間，避免長時間等待
      await RemoteConfigService.initialize().timeout(
        const Duration(seconds: 8),
        onTimeout: () {
          logger.w('Remote Config 初始化超時，將使用默認配置');
        },
      );
      
      // logger.i('Remote Config 異步初始化完成');
    } catch (e) {
      logger.e('Remote Config 異步初始化失敗: $e');
      // 不拋出異常，使用默認配置
    }
  }
  
  /// 初始化其他非關鍵服務
  static Future<void> _initializeOtherServices() async {
    try {
      logger.i('初始化其他非關鍵服務...');
      
      // 這裡可以添加其他非關鍵服務的初始化
      // 例如：
      // - 分析服務
      // - 推送通知服務
      // - 廣告服務
      // - 等等
      
      // 模擬一些初始化工作
      await Future.delayed(const Duration(milliseconds: 100));
      
      logger.i('其他非關鍵服務初始化完成');
      
    } catch (e) {
      logger.e('其他非關鍵服務初始化失敗: $e');
      // 不拋出異常
    }
  }
  
  /// 檢查背景服務是否已初始化
  static bool get isBackgroundInitialized => _backgroundInitialized;
  
  /// 檢查是否已初始化
  static bool get isInitialized => _isInitialized;
  
  /// 等待背景服務初始化完成
  static Future<void> waitForBackgroundInitialization({
    Duration timeout = const Duration(seconds: 10),
  }) async {
    if (_backgroundInitialized) return;
    
    final stopwatch = Stopwatch()..start();
    
    while (!_backgroundInitialized && stopwatch.elapsed < timeout) {
      await Future.delayed(const Duration(milliseconds: 100));
    }
    
    if (!_backgroundInitialized) {
      logger.w('等待背景服務初始化超時');
    } else {
      logger.i('背景服務初始化等待完成，耗時: ${stopwatch.elapsedMilliseconds}ms');
    }
  }
  
  /// 強制初始化所有服務（用於需要完整功能的場景）
  static Future<void> ensureFullInitialization() async {
    if (_backgroundInitialized) return;
    
    logger.i('強制完整初始化...');
    
    try {
      // 等待背景服務初始化完成
      await waitForBackgroundInitialization();
      
      // 如果背景服務仍未初始化，手動初始化
      if (!_backgroundInitialized) {
        await _initializeRemoteConfigAsync();
        await _initializeOtherServices();
        _backgroundInitialized = true;
      }
      
      logger.i('強制完整初始化完成');
      
    } catch (e) {
      logger.e('強制完整初始化失敗: $e');
      throw Exception('完整初始化失敗: $e');
    }
  }
  
  /// 獲取初始化狀態資訊
  static Map<String, dynamic> getInitializationStatus() {
    return {
      'quick_initialized': _isInitialized,
      'background_initialized': _backgroundInitialized,
      'remote_config_initialized': true, // RemoteConfigService 沒有 isInitialized 方法
    };
  }
  
  /// 重置初始化狀態（用於測試）
  @visibleForTesting
  static void reset() {
    _isInitialized = false;
    _backgroundInitialized = false;
  }
}

/// 啟動性能監控
class StartupPerformanceMonitor {
  static final Map<String, DateTime> _timestamps = {};
  static final Map<String, Duration> _durations = {};
  
  /// 記錄時間戳
  static void recordTimestamp(String event) {
    _timestamps[event] = DateTime.now();
    logger.d('啟動事件: $event 於 ${_timestamps[event]}');
  }
  
  /// 記錄持續時間
  static void recordDuration(String event, Duration duration) {
    _durations[event] = duration;
    logger.d('啟動耗時: $event 耗時 ${duration.inMilliseconds}ms');
  }
  
  /// 計算兩個事件之間的持續時間
  static Duration? getDurationBetween(String startEvent, String endEvent) {
    final start = _timestamps[startEvent];
    final end = _timestamps[endEvent];
    
    if (start != null && end != null) {
      final duration = end.difference(start);
      logger.d('$startEvent 到 $endEvent 耗時: ${duration.inMilliseconds}ms');
      return duration;
    }
    
    return null;
  }
  
  /// 獲取啟動性能報告
  static Map<String, dynamic> getPerformanceReport() {
    final report = <String, dynamic>{
      'timestamps': _timestamps.map((k, v) => MapEntry(k, v.toIso8601String())),
      'durations': _durations.map((k, v) => MapEntry(k, v.inMilliseconds)),
    };
    
    // 計算總啟動時間
    final appStart = _timestamps['app_start'];
    final appReady = _timestamps['app_ready'];
    if (appStart != null && appReady != null) {
      report['total_startup_time'] = appReady.difference(appStart).inMilliseconds;
    }
    
    return report;
  }
  
  /// 清除性能數據
  static void clear() {
    _timestamps.clear();
    _durations.clear();
  }
}
