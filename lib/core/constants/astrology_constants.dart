import 'package:flutter/material.dart';
import 'package:sweph/sweph.dart';

import '../../features/astrology/constants/zodiac_definitions.dart';

/// 占星常量类
/// 存储行星、相位、星座等常量定义
class AstrologyConstants {
  // 行星常數定義
  static const int SUN = 0;
  static const int MOON = 1;
  static const int MERCURY = 2;
  static const int VENUS = 3;
  static const int MARS = 4;
  static const int JUPITER = 5;
  static const int SATURN = 6;
  static const int URANUS = 7;
  static const int NEPTUNE = 8;
  static const int PLUTO = 9;

  // 特殊點位常數定義
  static const int ASCENDANT = 30; // 上升點
  static const int MIDHEAVEN = 31; // 中天
  static const int DESCENDANT = 32; // 下降點
  static const int IMUM_COELI = 33; // 天底

  // 行星定義表
  static const List<Map<String, dynamic>> PLANETS = [
    {
      'id': SUN,
      'name': '太陽',
      'unicode': '☉',
      'symbol': 'A',
      'body': HeavenlyBody.SE_SUN,
      'color': Color(0xFFFF0000),
    },
    {
      'id': MOON,
      'name': '月亮',
      'unicode': '☽',
      'symbol': 'B',
      'body': HeavenlyBody.SE_MOON,
      'color': Color(0xFF0A0AFF),
    },
    {
      'id': MERCURY,
      'name': '水星',
      'unicode': '☿',
      'symbol': 'C',
      'body': HeavenlyBody.SE_MERCURY,
      'color': Color(0xFF127116),
    },
    {
      'id': VENUS,
      'name': '金星',
      'unicode': '♀',
      'symbol': 'D',
      'body': HeavenlyBody.SE_VENUS,
      'color': Color(0xFFCC9933),
    },
    {
      'id': MARS,
      'name': '火星',
      'unicode': '♂',
      'symbol': 'E',
      'body': HeavenlyBody.SE_MARS,
      'color': Colors.red,
    },
    {
      'id': JUPITER,
      'name': '木星',
      'unicode': '♃',
      'symbol': 'F',
      'body': HeavenlyBody.SE_JUPITER,
      'color': Colors.red,
    },
    {
      'id': SATURN,
      'name': '土星',
      'unicode': '♄',
      'symbol': 'G',
      'body': HeavenlyBody.SE_SATURN,
      'color': Color(0xFFCC9933),
    },
    {
      'id': URANUS,
      'name': '天王星',
      'unicode': '♅',
      'symbol': 'H',
      'body': HeavenlyBody.SE_URANUS,
      'color': Color(0xFF0C4B0E),
    },
    {
      'id': NEPTUNE,
      'name': '海王星',
      'unicode': '♆',
      'symbol': 'I',
      'body': HeavenlyBody.SE_NEPTUNE,
      'color': Color(0xFF0A0AFF),
    },
    {
      'id': PLUTO,
      'name': '冥王星',
      'unicode': '⯓',
      'symbol': 'J',
      'body': HeavenlyBody.SE_PLUTO,
      'color': Color(0xFF0A0AFF),
    },
    {
      'id': ASCENDANT,
      'name': '上升',
      'unicode': '!',
      'symbol': '!',
      'body': -1,
      'color': Colors.red,
    },
    {
      'id': MIDHEAVEN,
      'name': '中天',
      'unicode': '\$',
      'symbol': '\$',
      'body': -1,
      'color': Color(0xFF0C4B0E),
    },
    {
      'id': DESCENDANT,
      'name': '下降',
      'unicode': '"',
      'symbol': '"',
      'body': -1,
      'color': Color(0xFFCC9933),
    },
    {
      'id': IMUM_COELI,
      'name': '天底',
      'unicode': '#',
      'symbol': '#',
      'body': -1,
      'color': Color(0xFF0A0AFF),
    },
    {
      'id': 10,
      'name': '北交點',
      'unicode': '☊',
      'symbol': 'L',
      'body': HeavenlyBody.SE_MEAN_NODE,
      'color': Color(0xFF00B8B8),
    },
    {
      'id': 12,
      'name': '莉莉絲',
      'unicode': '⚸',
      'symbol': 'Q',
      'body': HeavenlyBody.SE_MEAN_APOG,
      'color': Color(0xFF00B8B8),
    },
    {
      'id': 60,
      'name': '南交點',
      'unicode': '☋',
      'symbol': 'M',
      'body': -1,
      'color': Color(0xFF00B8B8),
    },
    {
      'id': 15,
      'name': '凱龍星',
      'unicode': '⚷',
      'symbol': 'R',
      'body': HeavenlyBody.SE_CHIRON,
      'color': Color(0xFFFD33FD),
    },
    {
      'id': 16,
      'name': '人龍星',
      'unicode': 'X',
      'symbol': 'X',
      'body': HeavenlyBody.SE_PHOLUS,
      'color': Color(0xFFFD33FD),
    },
    {
      'id': 17,
      'name': '穀神星',
      'unicode': '⚳',
      'symbol': 'W',
      'body': HeavenlyBody.SE_CERES,
      'color': Color(0xFFFD33FD),
    },
    {
      'id': 18,
      'name': '智神星',
      'unicode': '⚴',
      'symbol': 'S',
      'body': HeavenlyBody.SE_PALLAS,
      'color': Color(0xFFFD33FD),
    },
    {
      'id': 19,
      'name': '婚神星',
      'unicode': '⚵',
      'symbol': 'N',
      'body': HeavenlyBody.SE_JUNO,
      'color': Color(0xFFFD33FD),
    },
    {
      'id': 20,
      'name': '灶神星',
      'unicode': '⚶',
      'symbol': 'T',
      'body': HeavenlyBody.SE_VESTA,
      'color': Color(0xFFFD33FD),
    },
  ];

  // 相位定義表
  static const List<Map<String, dynamic>> ASPECTS = [
    {
      'name': '合相',
      'short': 'Conj',
      'shortZh': '合',
      'angle': 0,
      'orb': 8.0,
      'symbol': '☌',
      'color': Color(0xFF224EA5)
    },
    {
      'name': '六分相',
      'short': 'Sext',
      'shortZh': '六合',
      'angle': 60,
      'orb': 4.0,
      'symbol': '⚹',
      'color': Color(0xFF2999A4)
    },
    {
      'name': '四分相',
      'short': 'Square',
      'shortZh': '刑',
      'angle': 90,
      'orb': 8.0,
      'symbol': '□',
      'color': Colors.red
    },
    {
      'name': '三分相',
      'short': 'Trine',
      'shortZh': '拱',
      'angle': 120,
      'orb': 8.0,
      'symbol': '△',
      'color': Colors.green
    },
    {
      'name': '對分相',
      'short': 'Opp',
      'shortZh': '衝',
      'angle': 180,
      'orb': 8.0,
      'symbol': '☍',
      'color': Color(0xFF051883)
    },
  ];

  // 星座定義表
  static const List<String> ZODIAC_SIGNS = [
    '牡羊座',
    '金牛座',
    '雙子座',
    '巨蟹座',
    '獅子座',
    '處女座',
    '天秤座',
    '天蠍座',
    '射手座',
    '摩羯座',
    '水瓶座',
    '雙魚座'
  ];

  // 星座符號表 - 使用 ZodiacDefinitions 作為統一來源
  static Map<String, String> get ZODIAC_SYMBOLS {
    final symbols = <String, String>{};
    for (final sign in ZodiacDefinitions.zodiacSigns) {
      symbols[sign['name'] as String] = sign['symbol'] as String;
    }
    return symbols;
  }

  // 阿拉伯點定義
  static const int FORTUNE_POINT = 100; // 幸運點/福點
  static const int SPIRIT_POINT = 101; // 精神點
  static const int EXALTATION_POINT = 102; // 擢升點/旺點
  static const int BASIS_POINT = 103; // 基礎點
  static const int CHILDREN_POINT = 104; // 子女點
  static const int MARRIAGE_POINT = 105; // 婚姻點
  static const int SUCCESS_POINT = 106; // 成功點
  static const int PROFESSION_POINT = 107; // 職業點
  static const int FATHER_POINT = 108; // 父親點
  static const int MOTHER_POINT = 109; // 母親點
  static const int DEBT_POINT = 110; // 債務點
  static const int LOVE_POINT = 111; // 愛情點
  static const int MARRIAGE_MALE_POINT = 112; // 男性婚姻點
  static const int MARRIAGE_FEMALE_POINT = 113; // 女性婚姻點
  static const int SON_POINT = 114; // 兒子點
  static const int DAUGHTER_POINT = 115; // 女兒點
  static const int BROTHER_POINT = 116; // 兄弟姐妹點
  static const int SPECULATION_POINT = 117; // 投機點
  static const int NEMESIS_POINT = 118; // 復仇點
  static const int DEATH_POINT = 119; // 死亡點
  static const int THEFT_POINT = 120; // 盜竊點
  static const int TRAGEDY_POINT = 121; // 悲劇點
  static const int HIGHER_EDUCATION_POINT = 122; // 高等教育點
  static const int ASSASSINATION_POINT = 123; // 暗殺點
  static const int FRIENDS_POINT = 124; // 朋友點
  static const int WORK_POINT = 125; // 工作點

  // 新增的阿拉伯點
  static const int SUN_MOON_MIDPOINT = 61; // 日月中點
  static const int VERTEX_POINT = 62; // 宿命點

  // static const int MONEY_POINT = 129; // 金錢點
  // static const int KNOWLEDGE_POINT = 200; // 知識點

  // 阿拉伯點定義表
  static List<Map<String, dynamic>> getArabicPoints() {
    return [
      // 新增的特殊點
      {
        'id': SUN_MOON_MIDPOINT,
        'name': '日月中點',
        'symbol': 'V',
        'color': const Color(0xFF00B8B8),
        'description': '日月中點（Sun-Moon Midpoint）代表個人內在的平衡點，融合了太陽的意識與月亮的潛意識，象徵個性的核心與情感的統一。',
      },
      {
        'id': VERTEX_POINT,
        'name': '宿命點',
        'symbol': 'Y',
        'color': const Color(0xFF00B8B8),
        'description': '宿命點（Vertex）被稱為命運的轉折點，代表重要的人際遇合與生命中的關鍵事件，常與業力和命定的相遇有關。',
      },
      {
        'id': FORTUNE_POINT,
        'name': '幸運點',
        'symbol': '⊗',
        'color': const Color(0xFF00B8B8), // 金色
        'description':
            '幸運點（Lot of Fortune）是最古老且最著名的阿拉伯點之一，象徵一個人在生命中能夠找到幸福、滿足與財富的領域，並顯示天生的成功機會。',
      },
      {
        'id': SPIRIT_POINT,
        'name': '精神點',
        'symbol': '⊛',
        'color': const Color(0xFFFD33FD),
        'description': '精神點（Lot of Spirit）與內在心靈追求有關，指引個人在精神層面尋求滿足、意義與才華展現的領域。',
      },
      {
        'id': EXALTATION_POINT,
        'name': '旺點',
        'symbol': '✷',
        'color': const Color(0xFFF39F02),
        'description':
            '旺點（Lot of Exaltation）顯示個人在哪些領域能發揮最大潛能，並在名譽、成就與社會地位上獲得卓越表現。',
      },
      {
        'id': BASIS_POINT,
        'name': '基礎點',
        'symbol': '⊛',
        'color': const Color(0xFF8B4513),
        'description': '基礎點（Lot of Basic）代表個人內在安全感與生命根基的來源，揭示如何建立穩固而可靠的生活基礎。',
      },
      {
        'id': CHILDREN_POINT,
        'name': '子女點',
        'symbol': '⊚',
        'color': const Color(0xFFFF69B4), // 粉色
        'description': '子女點（Lot of Children）與生育、子女以及創造力相關，反映個人在家庭及創作領域的潛能與關聯。',
      },
      // {
      //   'id': KNOWLEDGE_POINT,
      //   'name': '知識點',
      //   'symbol': '⊝',
      //   'color': const Color(0xFF4682B4), // 鋼藍色
      //   'description':
      //       '知識點（Lot of Knowledge）象徵智慧與學習能力，揭示個人如何獲取知識、理解世界，及在哪些領域展現才智。',
      // },
      {
        'id': MARRIAGE_POINT,
        'name': '婚姻點',
        'symbol': '⊞',
        'color': const Color(0xFFFF6347), // 番茄色
        'description': '婚姻點（Lot of Marriage）與親密關係、婚姻及伴侶連結有關，揭示個人在關係中的需求、期望與挑戰。',
      },
      {
        'id': FATHER_POINT,
        'name': '父親點',
        'symbol': '⊟',
        'color': const Color(0xFF2E8B57), // 海綠色
        'description': '父親點（Lot of Father）象徵個人對父親、權威及父性形象的認知與關係，反映權威議題的內在觀感。',
      },
      {
        'id': MOTHER_POINT,
        'name': '母親點',
        'symbol': '⊠',
        'color': const Color(0xFFDA70D6), // 蘭花色
        'description': '母親點（Lot of Mother）關聯養育、母性與情感支持，揭示個人與母親之間的關係及對情感連結的需求。',
      },
      {
        'id': DEBT_POINT, //
        'name': '債務點/危險點',
        'symbol': '⊡',
        'color': const Color(0xFF696969), // 暗灰色
        'description': '債務點（Lot of Debt）與財務負擔、業力及責任相關，揭示個人在人生中可能面對的挑戰與償還之路。',
      },
      // {
      //   'id': DANGER_POINT, //
      //   'name': '危險點',
      //   'symbol': '⚠',
      //   'color': const Color(0xFFFF4500), // 橙紅色
      //   'description': '危險點（Lot of Danger）顯示個人可能面臨的風險、危機與挑戰，以及如何應對這些困境。',
      // },
      {
        'id': SUCCESS_POINT, // solar fire 錯
        'name': '成功點',
        'symbol': '⊜',
        'color': const Color(0xFFFFD700), // 金色
        'description': '成功點（Lot of Success）顯示個人在哪些領域最有可能獲得成功與成就，以及如何實現人生目標。',
      },
      {
        'id': PROFESSION_POINT, // solar fire 錯
        'name': '職業點',
        'symbol': '⊘',
        'color': const Color(0xFF4682B4), // 鋼藍色
        'description': '職業點（Lot of Profession）揭示個人在職業生涯中的潛能、方向與最適合的工作類型。',
      },
      {
        'id': LOVE_POINT, // 愛占星對 但是 solar fire 錯
        'name': '愛情點',
        'symbol': '♥',
        'color': const Color(0xFFFF1493), // 深粉色
        'description': '愛情點（Lot of Love）與浪漫關係、吸引力及情感連結有關，顯示個人在愛情中的需求與表達方式。',
      },
      {
        'id': MARRIAGE_MALE_POINT,
        'name': '男性婚姻點',
        'symbol': '♂⚭',
        'color': const Color(0xFF0000FF), // 藍色
        'description': '男性婚姻點（Lot of Marriage for Male）特別針對男性在婚姻關係中的需求、期望與挑戰。',
      },
      {
        'id': MARRIAGE_FEMALE_POINT, //
        'name': '女性婚姻點',
        'symbol': '♀⚭',
        'color': const Color(0xFFFF69B4), // 粉色
        'description':
            '女性婚姻點（Lot of Marriage for Female）特別針對女性在婚姻關係中的需求、期望與挑戰。',
      },
      {
        'id': SON_POINT,
        'name': '兒子點',
        'symbol': '♂⚲',
        'color': const Color(0xFF1E90FF), // 道奇藍
        'description': '兒子點（Lot of Son）與男性後代相關，揭示與兒子的關係及其在家庭中的角色與影響。',
      },
      {
        'id': DAUGHTER_POINT, // 愛占星對 但是 solar fire 錯
        'name': '女兒點',
        'symbol': '♀⚲',
        'color': const Color(0xFFFF69B4), // 粉色
        'description': '女兒點（Lot of Daughter）與女性後代相關，揭示與女兒的關係及其在家庭中的角色與影響。',
      },
      {
        'id': BROTHER_POINT, //
        'name': '兄弟姐妹點',
        'symbol': '⚶⚶',
        'color': const Color(0xFF4169E1), // 皇家藍
        'description': '兄弟姐妹點（Lot of Siblings Brothers）與手足關係相關，特別是與兄弟之間的連結、互動與影響。',
      },
      {
        'id': SPECULATION_POINT, //
        'name': '投機點',
        'symbol': '⚄',
        'color': const Color(0xFFFFD700), // 金色
        'description': '投機點（Lot of Speculation）與冒險、賭博及投資相關，顯示個人在風險活動中的運勢與傾向。',
      },
      {
        'id': NEMESIS_POINT, //
        'name': '復仇點',
        'symbol': '⚔',
        'color': const Color(0xFF8B0000), // 深紅色
        'description': '復仇點（Lot of Nemesis）與報復、正義及因果報應相關，揭示個人如何面對不公與挑戰。',
      },
      {
        'id': DEATH_POINT, //
        'name': '死亡點',
        'symbol': '☠',
        'color': const Color(0xFF000000), // 黑色
        'description': '死亡點（Lot of Death）與轉變、結束及重生相關，象徵生命中的重大轉折與深層變革。',
      },
      {
        'id': THEFT_POINT, //
        'name': '盜竊點',
        'symbol': '⚐',
        'color': const Color(0xFF708090), // 板岩灰
        'description': '盜竊點（Lot of Theft）與損失、被盜及安全問題相關，顯示個人在財產保護方面的弱點與挑戰。',
      },
      {
        'id': TRAGEDY_POINT, //
        'name': '悲劇點',
        'symbol': '⚱',
        'color': const Color(0xFF4B0082), // 靛青色
        'description': '悲劇點（Lot of Tragedy）與重大損失、悲傷及情感創傷相關，揭示個人可能面臨的深層情感挑戰。',
      },
      {
        'id': HIGHER_EDUCATION_POINT, //
        'name': '高等教育點',
        'symbol': '⚘',
        'color': const Color(0xFF9370DB), // 紫色
        'description':
            '高等教育點（Lot of Higher Education）與學術追求、高等學習及智慧發展相關，顯示個人在教育領域的潛能與方向。',
      },
      {
        'id': ASSASSINATION_POINT, //
        'name': '暗殺點',
        'symbol': '⚒',
        'color': const Color(0xFF800000), // 栗色
        'description':
            '暗殺點（Lot of Assassination）與隱藏危機、突發事件及意外相關，揭示需要特別警惕的生命領域。',
      },
      {
        'id': FRIENDS_POINT, //
        'name': '朋友點',
        'symbol': '⚯',
        'color': const Color(0xFF32CD32), // 酸橙綠
        'description':
            '朋友點（Lot of Friends）與友誼、社交圈及人際連結相關，顯示個人在建立與維持友誼方面的特質與傾向。',
      },
      {
        'id': WORK_POINT, // Work Which Must be Done
        'name': '工作點',
        'symbol': '⚒',
        'color': const Color(0xFF8B4513), // 馬鞍棕色
        'description': '工作點（Lot of Work）與日常工作、職責及服務相關，揭示個人在工作環境中的表現與適應能力。',
      },
      // {
      //   'id': MONEY_POINT, //
      //   'name': '金錢點',
      //   'symbol': '⚙',
      //   'color': const Color(0xFFDAA520), // 金菊色
      //   'description': '金錢點（Lot of Money）與財富、物質資源及經濟安全相關，顯示個人在財務領域的潛能與挑戰。',
      // },
    ];
  }

  // 阿拉伯點列表
  static final List<Map<String, dynamic>> ARABIC_POINTS = getArabicPoints();

  // 特殊度數定義
  static const double ARIES_EXALTATION_DEGREE = 19.0; // 太陽在牡羊座19度為最強旺
  static const double TAURUS_EXALTATION_DEGREE = 3.0; // 月亮在金牛座3度為最強旺

  // 星座定義
  static const String ARIES = '牡羊座';
  static const String TAURUS = '金牛座';
  static const String GEMINI = '雙子座';
  static const String CANCER = '巨蟹座';
  static const String LEO = '獅子座';
  static const String VIRGO = '處女座';
  static const String LIBRA = '天秤座';
  static const String SCORPIO = '天蠍座';
  static const String SAGITTARIUS = '射手座';
  static const String CAPRICORN = '摩羯座';
  static const String AQUARIUS = '水瓶座';
  static const String PISCES = '雙魚座';

  // 星座陰陽性定義
  // 陽性星座（陽陽陽）：白羊、雙子、獅子、天秤、射手、水瓶
  // 陰性星座（陰陰陰）：金牛、巨蟹、處女、天蠍、摩羯、雙魚
  static const Map<String, bool> SIGN_POLARITY = {
    ARIES: true,      // 牡羊座 - 陽性
    TAURUS: false,    // 金牛座 - 陰性
    GEMINI: true,     // 雙子座 - 陽性
    CANCER: false,    // 巨蟹座 - 陰性
    LEO: true,        // 獅子座 - 陽性
    VIRGO: false,     // 處女座 - 陰性
    LIBRA: true,      // 天秤座 - 陽性
    SCORPIO: false,   // 天蠍座 - 陰性
    SAGITTARIUS: true,// 射手座 - 陽性
    CAPRICORN: false, // 摩羯座 - 陰性
    AQUARIUS: true,   // 水瓶座 - 陽性
    PISCES: false,    // 雙魚座 - 陰性
  };

  // 星座元素定義
  // 火象星座：白羊、獅子、射手
  // 土象星座：金牛、處女、摩羯
  // 風象星座：雙子、天秤、水瓶
  // 水象星座：巨蟹、天蠍、雙魚
  static const Map<String, String> SIGN_ELEMENTS = {
    ARIES: '火',      // 牡羊座 - 火
    TAURUS: '土',     // 金牛座 - 土
    GEMINI: '風',     // 雙子座 - 風
    CANCER: '水',     // 巨蟹座 - 水
    LEO: '火',        // 獅子座 - 火
    VIRGO: '土',      // 處女座 - 土
    LIBRA: '風',      // 天秤座 - 風
    SCORPIO: '水',    // 天蠍座 - 水
    SAGITTARIUS: '火',// 射手座 - 火
    CAPRICORN: '土',  // 摩羯座 - 土
    AQUARIUS: '風',   // 水瓶座 - 風
    PISCES: '水',     // 雙魚座 - 水
  };

  // 星座品質定義
  // 基本/啟動星座：白羊、巨蟹、天秤、摩羯
  // 固定星座：金牛、獅子、天蠍、水瓶
  // 變動星座：雙子、處女、射手、雙魚
  static const Map<String, String> SIGN_MODALITIES = {
    ARIES: 'cardinal',      // 牡羊座 - 啟動
    TAURUS: 'fixed',        // 金牛座 - 固定
    GEMINI: 'mutable',      // 雙子座 - 變動
    CANCER: 'cardinal',     // 巨蟹座 - 啟動
    LEO: 'fixed',           // 獅子座 - 固定
    VIRGO: 'mutable',       // 處女座 - 變動
    LIBRA: 'cardinal',      // 天秤座 - 啟動
    SCORPIO: 'fixed',       // 天蠍座 - 固定
    SAGITTARIUS: 'mutable', // 射手座 - 變動
    CAPRICORN: 'cardinal',  // 摩羯座 - 啟動
    AQUARIUS: 'fixed',      // 水瓶座 - 固定
    PISCES: 'mutable',      // 雙魚座 - 變動
  };

  // 行星日夜屬性定義
  // 日間行星：太陽、木星、土星
  // 夜間行星：月亮、金星、火星
  // 水星則依據其相位與太陽的關係略做區分
  static const Map<int, bool> PLANET_SECT_NATURE = {
    SUN: true,      // 太陽 - 日間行星
    MOON: false,    // 月亮 - 夜間行星
    VENUS: false,   // 金星 - 夜間行星
    MARS: false,    // 火星 - 夜間行星
    JUPITER: true,  // 木星 - 日間行星
    SATURN: true,   // 土星 - 日間行星
    // 水星根據與太陽的關係決定，默認為中性
    MERCURY: true,  // 水星 - 默認為日間行星，但實際上會根據與太陽的關係調整
  };

  // 行星廟旺陷弱定義
  // 行星在各星座的廟旺陷弱狀態定義
  static const Map<int, Map<String, String>> PLANET_DIGNITIES = {
    // 太陽
    SUN: {
      DOMICILE: LEO, // 廟
      EXALTATION: ARIES, // 旺
      DETRIMENT: AQUARIUS, // 陷
      FALL: LIBRA, // 弱
    },
    // 月亮
    MOON: {
      DOMICILE: CANCER, // 廟
      EXALTATION: TAURUS, // 旺
      DETRIMENT: CAPRICORN, // 陷
      FALL: SCORPIO, // 弱
    },
    // 水星
    MERCURY: {
      DOMICILE: '$GEMINI,$VIRGO', // 廟
      EXALTATION: VIRGO, // 旺
      DETRIMENT: '$SAGITTARIUS,$PISCES', // 陷
      FALL: PISCES, // 弱
    },
    // 金星
    VENUS: {
      DOMICILE: '$TAURUS,$LIBRA', // 廟
      EXALTATION: PISCES, // 旺
      DETRIMENT: '$ARIES,$SCORPIO', // 陷
      FALL: VIRGO, // 弱
    },
    // 火星
    MARS: {
      DOMICILE: '$ARIES,$SCORPIO', // 廟
      EXALTATION: CAPRICORN, // 旺
      DETRIMENT: '$TAURUS,$LIBRA', // 陷
      FALL: CANCER, // 弱
    },
    // 木星
    JUPITER: {
      DOMICILE: '$SAGITTARIUS,$PISCES', // 廟
      EXALTATION: CANCER, // 旺
      DETRIMENT: '$GEMINI,$VIRGO', // 陷
      FALL: CAPRICORN, // 弱
    },
    // 土星
    SATURN: {
      DOMICILE: '$CAPRICORN,$AQUARIUS', // 廟
      EXALTATION: LIBRA, // 旺
      DETRIMENT: '$CANCER,$LEO', // 陷
      FALL: ARIES, // 弱
    },
    // 天王星
    URANUS: {
      DOMICILE: AQUARIUS, // 廟
      EXALTATION: SCORPIO, // 旺
      DETRIMENT: LEO, // 陷
      FALL: TAURUS, // 弱
    },
    // 海王星
    NEPTUNE: {
      DOMICILE: PISCES, // 廟
      EXALTATION: CANCER, // 旺
      DETRIMENT: VIRGO, // 陷
      FALL: CAPRICORN, // 弱
    },
    // 冥王星
    PLUTO: {
      DOMICILE: SCORPIO, // 廟
      EXALTATION: ARIES, // 旺
      DETRIMENT: TAURUS, // 陷
      FALL: LIBRA, // 弱
    },
  };

  // 廟旺陷弱定義常量
  static const String DOMICILE = 'domicile'; // 廟
  static const String EXALTATION = 'exaltation'; // 旺
  static const String DETRIMENT = 'detriment'; // 陷
  static const String FALL = 'fall'; // 弱

  // 年月常數定義
  static const double TROPICAL_YEAR = 365.24219; // 熱帶年（Tropical Year）天數
  static const double SIDEREAL_YEAR = 365.25636; // 恆星年（Sidereal Year）天數
  static const double NAIBOD_RATE = 0.9856; // Naibod 修正值（太陽平均每天移動 0°59'08"）
  static const double SIDEREAL_MONTH = 27.321661; // 恆星月（Sidereal Month）天數，月亮繪地球一圈的平均天數

  // 星座界主星定義（Terms/Bounds）
  // 每個星座被分為5個界，每個界由不同的行星主管
  // 格式：{起始度數字符串: 主管行星ID}
  static const Map<String, Map<String, int>> ZODIAC_TERMS = {
    ARIES: {
      '0': JUPITER,   // 0-6度：木星
      '6': VENUS,     // 6-12度：金星
      '12': MERCURY,  // 12-20度：水星
      '20': MARS,     // 20-25度：火星
      '25': SATURN,   // 25-30度：土星
    },
    TAURUS: {
      '0': VENUS,     // 0-8度：金星
      '8': MERCURY,   // 8-14度：水星
      '14': JUPITER,  // 14-22度：木星
      '22': SATURN,   // 22-27度：土星
      '27': MARS,     // 27-30度：火星
    },
    GEMINI: {
      '0': MERCURY,   // 0-6度：水星
      '6': JUPITER,   // 6-12度：木星
      '12': VENUS,    // 12-17度：金星
      '17': MARS,     // 17-24度：火星
      '24': SATURN,   // 24-30度：土星
    },
    CANCER: {
      '0': MARS,      // 0-7度：火星
      '7': VENUS,     // 7-13度：金星
      '13': MERCURY,  // 13-19度：水星
      '19': JUPITER,  // 19-26度：木星
      '26': SATURN,   // 26-30度：土星
    },
    LEO: {
      '0': JUPITER,   // 0-6度：木星
      '6': VENUS,     // 6-11度：金星
      '11': SATURN,   // 11-18度：土星
      '18': MERCURY,  // 18-24度：水星
      '24': MARS,     // 24-30度：火星
    },
    VIRGO: {
      '0': MERCURY,   // 0-7度：水星
      '7': VENUS,     // 7-17度：金星
      '17': JUPITER,  // 17-21度：木星
      '21': MARS,     // 21-28度：火星
      '28': SATURN,   // 28-30度：土星
    },
    LIBRA: {
      '0': SATURN,    // 0-6度：土星
      '6': MERCURY,   // 6-14度：水星
      '14': JUPITER,  // 14-21度：木星
      '21': VENUS,    // 21-28度：金星
      '28': MARS,     // 28-30度：火星
    },
    SCORPIO: {
      '0': MARS,      // 0-7度：火星
      '7': VENUS,     // 7-11度：金星
      '11': MERCURY,  // 11-19度：水星
      '19': JUPITER,  // 19-24度：木星
      '24': SATURN,   // 24-30度：土星
    },
    SAGITTARIUS: {
      '0': JUPITER,   // 0-12度：木星
      '12': VENUS,    // 12-17度：金星
      '17': MERCURY,  // 17-21度：水星
      '21': SATURN,   // 21-26度：土星
      '26': MARS,     // 26-30度：火星
    },
    CAPRICORN: {
      '0': MERCURY,   // 0-7度：水星
      '7': JUPITER,   // 7-14度：木星
      '14': VENUS,    // 14-22度：金星
      '22': SATURN,   // 22-26度：土星
      '26': MARS,     // 26-30度：火星
    },
    AQUARIUS: {
      '0': MERCURY,   // 0-7度：水星
      '7': VENUS,     // 7-13度：金星
      '13': JUPITER,  // 13-20度：木星
      '20': MARS,     // 20-25度：火星
      '25': SATURN,   // 25-30度：土星
    },
    PISCES: {
      '0': VENUS,     // 0-12度：金星
      '12': JUPITER,  // 12-16度：木星
      '16': MERCURY,  // 16-19度：水星
      '19': MARS,     // 19-28度：火星
      '28': SATURN,   // 28-30度：土星
    },
  };

  // 三分主星定義（Triplicity Rulers - Dorothean System）
  // 每個元素（火、土、風、水）的三分主星，分為日間主星、夜間主星和通用主星
  static const Map<String, Map<String, int>> ZODIAC_TRIPLICITIES = {
    // 火象星座：牡羊、獅子、射手
    // 火象三分性：日主星=太陽，夜主星=木星，通用主星=土星
    ARIES: {
      'day': SUN,       // 日間主星：太陽
      'night': JUPITER, // 夜間主星：木星
      'participating': SATURN, // 通用主星：土星
    },
    LEO: {
      'day': SUN,       // 日間主星：太陽
      'night': JUPITER, // 夜間主星：木星
      'participating': SATURN, // 通用主星：土星
    },
    SAGITTARIUS: {
      'day': SUN,       // 日間主星：太陽
      'night': JUPITER, // 夜間主星：木星
      'participating': SATURN, // 通用主星：土星
    },
    // 土象星座：金牛、處女、摩羯
    // 土象三分性：日主星=金星，夜主星=月亮，通用主星=火星
    TAURUS: {
      'day': VENUS,     // 日間主星：金星
      'night': MOON,    // 夜間主星：月亮
      'participating': MARS, // 通用主星：火星
    },
    VIRGO: {
      'day': VENUS,     // 日間主星：金星
      'night': MOON,    // 夜間主星：月亮
      'participating': MARS, // 通用主星：火星
    },
    CAPRICORN: {
      'day': VENUS,     // 日間主星：金星
      'night': MOON,    // 夜間主星：月亮
      'participating': MARS, // 通用主星：火星
    },
    // 風象星座：雙子、天秤、水瓶
    // 風象三分性：日主星=土星，夜主星=水星，通用主星=木星
    GEMINI: {
      'day': SATURN,    // 日間主星：土星
      'night': MERCURY, // 夜間主星：水星
      'participating': JUPITER, // 通用主星：木星
    },
    LIBRA: {
      'day': SATURN,    // 日間主星：土星
      'night': MERCURY, // 夜間主星：水星
      'participating': JUPITER, // 通用主星：木星
    },
    AQUARIUS: {
      'day': SATURN,    // 日間主星：土星
      'night': MERCURY, // 夜間主星：水星
      'participating': JUPITER, // 通用主星：木星
    },
    // 水象星座：巨蟹、天蠍、雙魚
    // 水象三分性：日主星=金星，夜主星=火星，通用主星=月亮
    CANCER: {
      'day': VENUS,     // 日間主星：金星
      'night': MARS,    // 夜間主星：火星
      'participating': MOON, // 通用主星：月亮
    },
    SCORPIO: {
      'day': VENUS,     // 日間主星：金星
      'night': MARS,    // 夜間主星：火星
      'participating': MOON, // 通用主星：月亮
    },
    PISCES: {
      'day': VENUS,     // 日間主星：金星
      'night': MARS,    // 夜間主星：火星
      'participating': MOON, // 通用主星：月亮
    },
  };

  // 十度主星定義（Decan Rulers）
  // 每個星座被分為3個十度，每個十度由不同的行星主管
  static const Map<String, Map<String, int>> ZODIAC_DECANS = {
    ARIES: {
      '0.0': MARS,    // 0-10度：火星
      '10.0': SUN,    // 10-20度：太陽
      '20.0': VENUS,  // 20-30度：金星
    },
    TAURUS: {
      '0.0': MERCURY, // 0-10度：水星
      '10.0': MOON,   // 10-20度：月亮
      '20.0': SATURN, // 20-30度：土星
    },
    GEMINI: {
      '0.0': JUPITER, // 0-10度：木星
      '10.0': MARS,   // 10-20度：火星
      '20.0': SUN,    // 20-30度：太陽
    },
    CANCER: {
      '0.0': VENUS,   // 0-10度：金星
      '10.0': MERCURY, // 10-20度：水星
      '20.0': MOON,   // 20-30度：月亮
    },
    LEO: {
      '0.0': SATURN,  // 0-10度：土星
      '10.0': JUPITER, // 10-20度：木星
      '20.0': MARS,   // 20-30度：火星
    },
    VIRGO: {
      '0.0': SUN,     // 0-10度：太陽
      '10.0': VENUS,  // 10-20度：金星
      '20.0': MERCURY, // 20-30度：水星
    },
    LIBRA: {
      '0.0': MOON,    // 0-10度：月亮
      '10.0': SATURN, // 10-20度：土星
      '20.0': JUPITER, // 20-30度：木星
    },
    SCORPIO: {
      '0.0': MARS,    // 0-10度：火星
      '10.0': SUN,    // 10-20度：太陽
      '20.0': VENUS,  // 20-30度：金星
    },
    SAGITTARIUS: {
      '0.0': MERCURY, // 0-10度：水星
      '10.0': MOON,   // 10-20度：月亮
      '20.0': SATURN, // 20-30度：土星
    },
    CAPRICORN: {
      '0.0': JUPITER, // 0-10度：木星
      '10.0': MARS,   // 10-20度：火星
      '20.0': SUN,    // 20-30度：太陽
    },
    AQUARIUS: {
      '0.0': VENUS,   // 0-10度：金星
      '10.0': MERCURY, // 10-20度：水星
      '20.0': MOON,   // 20-30度：月亮
    },
    PISCES: {
      '0.0': SATURN,  // 0-10度：土星
      '10.0': JUPITER, // 10-20度：木星
      '20.0': MARS,   // 20-30度：火星
    },
  };
}
class AspectMeaning {
  final String base;
  final String advice;
  final String tone;
  final String symbol;

  const AspectMeaning(this.base, this.advice, this.tone, this.symbol);
}

const Map<String, AspectMeaning> aspectDescriptions = {
  '合相': AspectMeaning(
    '能量融合，雙方特質加乘，有可能強化，也可能過度集中。',
    '可以善用這段時間展現你的特質，或與人建立深度連結。',
    '這是一個關鍵相位，會帶來明顯的內在或外在事件。',
    '🔆',
  ),
  '對分相': AspectMeaning(
    '對立能量帶來拉扯感，有助於看見不同觀點或反射他人。',
    '注意人際互動中可能出現的緊張感，學會從中取得平衡。',
    '這段時間你可能需要在兩種極端中找出自己的立場。',
    '🌗',
  ),
  '三分相': AspectMeaning(
    '能量流動順暢，有助於才能發揮與人際合作。',
    '這是適合推進計畫與展現能力的時機。',
    '輕鬆順利的星象氛圍，值得善加利用。',
    '🌈',
  ),
  '四分相': AspectMeaning(
    '帶來摩擦與壓力，是突破與行動的推力來源。',
    '別急著逃避壓力，這正是成長與修正方向的契機。',
    '你可能會感到卡住或焦躁，試著主動面對挑戰。',
    '⛅',
  ),
  '六分相': AspectMeaning(
    '雖不明顯，卻是積極互動與學習的契機。',
    '多與人交流，從細節中捕捉機會與靈感。',
    '這是潛藏支持與轉化的時期，別錯過微小機會。',
    '🌤️',
  ),
};

const Map<int, String> importanceToneMap = {
  5: '⚡ 這是一個強烈且深刻的相位，可能對你的人生節奏造成重要影響。',
  4: '⚡ 這是一個強烈且深刻的相位，可能對你的人生節奏造成重要影響。',
  3: '✨ 這個相位可能帶來實質事件或心理觸發，值得注意。',
  2: '🔍 這個相位雖不劇烈，但會在互動與情緒中留下痕跡。',
  1: '☁️ 整體影響溫和，可以當作一種氛圍來感受。',
};

String _getPlanetDescription(String planetName) {
  const planetDescriptions = {
    '太陽': '核心自我、生命力、意識',
    '月亮': '情感、潛意識、本能反應',
    '水星': '思維、溝通、學習能力',
    '金星': '愛情、美感、價值觀',
    '火星': '行動力、慈望、競爭力',
    '木星': '擴張、幸運、信念',
    '土星': '限制、責任、紀律',
    '天王星': '變革、獨創、突破',
    '海王星': '靈性、幻想、溶解',
    '冥王星': '轉化、權力、重生',
    '北交點': '命運方向、靈魂成長',
    '南交點': '過去業力、舒適區',
    '上升': '自我表達、外在形象、人格面具',
    '中天': '事業、社會地位、公眾形象',
    '下降': '人際關係、伴侶、他人投射',
    '天底': '家庭、根源、內在安全感',
  };
  return planetDescriptions[planetName] ?? '行星特質';
}

String _getHouseDescription(int houseNumber) {
  const houseDescriptions = {
    1: '自我、外表、個性、生命力',
    2: '財產、價值觀、資源、安全感',
    3: '溝通、短途旅行、兄弟姐妹、早期教育',
    4: '家庭、根源、父母、內在情感基礎',
    5: '創造力、娛樂、子女、浪漫關係',
    6: '健康、工作、日常生活、服務',
    7: '關係、婚姻、合作夥伴、公開的敵人',
    8: '共享資源、轉變、性、死亡與重生',
    9: '高等教育、哲學、長途旅行、信仰',
    10: '職業、社會地位、名聲、權威',
    11: '友誼、團體、願望、社會理想',
    12: '潛意識、秘密、限制、靈性成長',
  };
  return houseDescriptions[houseNumber.clamp(1, 12)] ?? '宮位特質';
}

int _getAspectImportance(String aspect, String planet1, String planet2) {
  final majorPersonalPoints = ['太陽', '月亮'];
  final innerPlanets = ['太陽', '月亮', '水星', '金星', '火星'];
  final outerPlanets = ['木星', '土星', '天王星', '海王星', '冥王星'];

  final aspectWeights = {
    '合相': 4,
    '對分相': 3,
    '四分相': 2,
    '三分相': 2,
    '六分相': 1,
  };

  int aspectWeight = aspectWeights[aspect] ?? 1;

  bool involvesMajorPoint =
      majorPersonalPoints.contains(planet1) || majorPersonalPoints.contains(planet2);
  bool isInnerAspect =
      innerPlanets.contains(planet1) && innerPlanets.contains(planet2);
  bool isMixedAspect =
      (innerPlanets.contains(planet1) && outerPlanets.contains(planet2)) ||
          (outerPlanets.contains(planet1) && innerPlanets.contains(planet2));

  int importance = aspectWeight;
  if (involvesMajorPoint) importance += 1;
  if (isMixedAspect) importance += 1;
  if (isInnerAspect) importance += 1;

  return importance.clamp(1, 5);
}
