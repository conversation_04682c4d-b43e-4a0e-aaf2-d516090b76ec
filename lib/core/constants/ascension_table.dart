/// 星座赤經上升時間表
/// 表示在不同緯度下，各星座通過赤經所需的時間
/// 資料來源：https://horoscopes.astro-seek.com/ascensional-rising-times-tables-astrology-calculator
class AscensionTable {
  /// 北緯星座赤經上升時間表 [緯度][星座索引]
  /// 緯度: 0°-66°
  /// 星座索引: 0=♈︎, 1=♉︎, 2=♊︎, 3=♋︎, 4=♌︎, 5=♍︎, 6=♎︎, 7=♏︎, 8=♐︎, 9=♑︎, 10=♒︎, 11=♓︎
  /// 數值為該星座在該緯度的上升時間（分鐘）
  static const List<List<double>> northernTable = [
    // 0°緯度
    [27.91, 29.91, 32.18, 32.18, 29.91, 27.91, 27.91, 29.91, 32.18, 32.18, 29.91, 27.91],
    // 1°緯度
    [27.71, 29.74, 32.11, 32.25, 30.07, 28.11, 28.11, 30.07, 32.25, 32.11, 29.74, 27.71],
    // 2°緯度
    [27.51, 29.58, 32.05, 32.31, 30.24, 28.32, 28.32, 30.24, 32.31, 32.05, 29.58, 27.51],
    // 3°緯度
    [27.30, 29.42, 31.98, 32.38, 30.40, 28.52, 28.52, 30.40, 32.38, 31.98, 29.42, 27.30],
    // 4°緯度
    [27.10, 29.25, 31.91, 32.45, 30.57, 28.72, 28.72, 30.57, 32.45, 31.91, 29.25, 27.10],
    // 5°緯度
    [26.89, 29.09, 31.85, 32.51, 30.73, 28.93, 28.93, 30.73, 32.51, 31.85, 29.09, 26.89],
    // 6°緯度
    [26.69, 28.92, 31.78, 32.58, 30.90, 29.13, 29.13, 30.90, 32.58, 31.78, 28.92, 26.69],
    // 7°緯度
    [26.48, 28.75, 31.71, 32.65, 31.06, 29.34, 29.34, 31.06, 32.65, 31.71, 28.75, 26.48],
    // 8°緯度
    [26.28, 28.59, 31.64, 32.72, 31.23, 29.55, 29.55, 31.23, 32.72, 31.64, 28.59, 26.28],
    // 9°緯度
    [26.07, 28.42, 31.58, 32.79, 31.40, 29.75, 29.75, 31.40, 32.79, 31.58, 28.42, 26.07],
    // 10°緯度
    [25.86, 28.25, 31.51, 32.86, 31.57, 29.96, 29.96, 31.57, 32.86, 31.51, 28.25, 25.86],
    // 11°緯度
    [25.65, 28.08, 31.44, 32.92, 31.74, 30.17, 30.17, 31.74, 32.92, 31.44, 28.08, 25.65],
    // 12°緯度
    [25.44, 27.91, 31.37, 32.99, 31.91, 30.38, 30.38, 31.91, 32.99, 31.37, 27.91, 25.44],
    // 13°緯度
    [25.23, 27.73, 31.30, 33.07, 32.08, 30.60, 30.60, 32.08, 33.07, 31.30, 27.73, 25.23],
    // 14°緯度
    [25.01, 27.56, 31.22, 33.14, 32.26, 30.81, 30.81, 32.26, 33.14, 31.22, 27.56, 25.01],
    // 15°緯度
    [24.79, 27.38, 31.15, 33.21, 32.43, 31.03, 31.03, 32.43, 33.21, 31.15, 27.38, 24.79],
    // 16°緯度
    [24.58, 27.20, 31.08, 33.28, 32.61, 31.25, 31.25, 32.61, 33.28, 31.08, 27.20, 24.58],
    // 17°緯度
    [24.35, 27.02, 31.01, 33.36, 32.79, 31.47, 31.47, 32.79, 33.36, 31.01, 27.02, 24.35],
    // 18°緯度
    [24.13, 26.84, 30.93, 33.43, 32.97, 31.69, 31.69, 32.97, 33.43, 30.93, 26.84, 24.13],
    // 19°緯度
    [23.90, 26.66, 30.85, 33.51, 33.16, 31.92, 31.92, 33.16, 33.51, 30.85, 26.66, 23.90],
    // 20°緯度
    [23.68, 26.47, 30.78, 33.58, 33.35, 32.15, 32.15, 33.35, 33.58, 30.78, 26.47, 23.68],
    // 21°緯度
    [23.44, 26.28, 30.70, 33.66, 33.54, 32.38, 32.38, 33.54, 33.66, 30.70, 26.28, 23.44],
    // 22°緯度
    [23.21, 26.09, 30.62, 33.74, 33.73, 32.61, 32.61, 33.73, 33.74, 30.62, 26.09, 23.21],
    // 23°緯度
    [22.97, 25.89, 30.54, 33.82, 33.93, 32.85, 32.85, 33.93, 33.82, 30.54, 25.89, 22.97],
    // 24°緯度
    [22.73, 25.69, 30.45, 33.91, 34.13, 33.09, 33.09, 34.13, 33.91, 30.45, 25.69, 22.73],
    // 25°緯度
    [22.48, 25.49, 30.37, 33.99, 34.33, 33.34, 33.34, 34.33, 33.99, 30.37, 25.49, 22.48],
    // 26°緯度
    [22.23, 25.28, 30.28, 34.08, 34.54, 33.59, 33.59, 34.54, 34.08, 30.28, 25.28, 22.23],
    // 27°緯度
    [21.98, 25.07, 30.19, 34.17, 34.75, 33.85, 33.85, 34.75, 34.17, 30.19, 25.07, 21.98],
    // 28°緯度
    [21.72, 24.85, 30.10, 34.26, 34.96, 34.10, 34.10, 34.96, 34.26, 30.10, 24.85, 21.72],
    // 29°緯度
    [21.45, 24.63, 30.01, 34.35, 35.18, 34.37, 34.37, 35.18, 34.35, 30.01, 24.63, 21.45],
    // 30°緯度
    [21.18, 24.41, 29.92, 34.45, 35.41, 34.64, 34.64, 35.41, 34.45, 29.92, 24.41, 21.18],
    // 31°緯度
    [20.91, 24.18, 29.82, 34.54, 35.64, 34.91, 34.91, 35.64, 34.54, 29.82, 24.18, 20.91],
    // 32°緯度
    [20.63, 23.94, 29.72, 34.64, 35.88, 35.20, 35.20, 35.88, 34.64, 29.72, 23.94, 20.63],
    // 33°緯度
    [20.34, 23.70, 29.61, 34.75, 36.12, 35.48, 35.48, 36.12, 34.75, 29.61, 23.70, 20.34],
    // 34°緯度
    [20.04, 23.45, 29.51, 34.85, 36.37, 35.78, 35.78, 36.37, 34.85, 29.51, 23.45, 20.04],
    // 35°緯度
    [19.74, 23.19, 29.40, 34.96, 36.63, 36.08, 36.08, 36.63, 34.96, 29.40, 23.19, 19.74],
    // 36°緯度
    [19.43, 22.93, 29.28, 35.08, 36.89, 36.39, 36.39, 36.89, 35.08, 29.28, 22.93, 19.43],
    // 37°緯度
    [19.12, 22.65, 29.16, 35.20, 37.16, 36.71, 36.71, 37.16, 35.20, 29.16, 22.65, 19.12],
    // 38°緯度
    [18.79, 22.37, 29.04, 35.32, 37.44, 37.03, 37.03, 37.44, 35.32, 29.04, 22.37, 18.79],
    // 39°緯度
    [18.45, 22.08, 28.91, 35.45, 37.73, 37.37, 37.37, 37.73, 35.45, 28.91, 22.08, 18.45],
    // 40°緯度
    [18.11, 21.78, 28.78, 35.58, 38.04, 37.71, 37.71, 38.04, 35.58, 28.78, 21.78, 18.11],
    // 41°緯度
    [17.75, 21.47, 28.64, 35.72, 38.35, 38.07, 38.07, 38.35, 35.72, 28.64, 21.47, 17.75],
    // 42°緯度
    [17.38, 21.15, 28.50, 35.86, 38.67, 38.44, 38.44, 38.67, 35.86, 28.50, 21.15, 17.38],
    // 43°緯度
    [17.00, 20.81, 28.34, 36.02, 39.01, 38.82, 38.82, 39.01, 36.02, 28.34, 20.81, 17.00],
    // 44°緯度
    [16.61, 20.46, 28.18, 36.18, 39.36, 39.21, 39.21, 39.36, 36.18, 28.18, 20.46, 16.61],
    // 45°緯度
    [16.20, 20.09, 28.02, 36.35, 39.72, 39.62, 39.62, 39.72, 36.35, 28.02, 20.09, 16.20],
    // 46°緯度
    [15.78, 19.71, 27.84, 36.52, 40.11, 40.04, 40.04, 40.11, 36.52, 27.84, 19.71, 15.78],
    // 47°緯度
    [15.34, 19.31, 27.65, 36.71, 40.51, 40.48, 40.48, 40.51, 36.71, 27.65, 19.31, 15.34],
    // 48°緯度
    [14.89, 18.89, 27.45, 36.91, 40.93, 40.94, 40.94, 40.93, 36.91, 27.45, 18.89, 14.89],
    // 49°緯度
    [14.41, 18.44, 27.23, 37.13, 41.37, 41.41, 41.41, 41.37, 37.13, 27.23, 18.44, 14.41],
    // 50°緯度
    [13.92, 17.97, 27.00, 37.36, 41.84, 41.91, 41.91, 41.84, 37.36, 27.00, 17.97, 13.92],
    // 51°緯度
    [13.40, 17.48, 26.76, 37.60, 42.34, 42.42, 42.42, 42.34, 37.60, 26.76, 17.48, 13.40],
    // 52°緯度
    [12.86, 16.95, 26.49, 37.87, 42.86, 42.96, 42.96, 42.86, 37.87, 26.49, 16.95, 12.86],
    // 53°緯度
    [12.29, 16.39, 26.20, 38.16, 43.42, 43.53, 43.53, 43.42, 38.16, 26.20, 16.39, 12.29],
    // 54°緯度
    [11.69, 15.80, 25.88, 38.48, 44.02, 44.13, 44.13, 44.02, 38.48, 25.88, 15.80, 11.69],
    // 55°緯度
    [11.07, 15.15, 25.53, 38.83, 44.66, 44.76, 44.76, 44.66, 38.83, 25.53, 15.15, 11.07],
    // 56°緯度
    [10.40, 14.46, 25.14, 39.22, 45.35, 45.42, 45.42, 45.35, 39.22, 25.14, 14.46, 10.40],
    // 57°緯度
    [9.70, 13.72, 24.71, 39.66, 46.10, 46.12, 46.12, 46.10, 39.66, 24.71, 13.72, 9.70],
    // 58°緯度
    [8.96, 12.90, 24.21, 40.15, 46.91, 46.86, 46.86, 46.91, 40.15, 24.21, 12.90, 8.96],
    // 59°緯度
    [8.17, 12.01, 23.64, 40.72, 47.80, 47.65, 47.65, 47.80, 40.72, 23.64, 12.01, 8.17],
    // 60°緯度
    [7.33, 11.03, 22.97, 41.39, 48.79, 48.49, 48.49, 48.79, 41.39, 22.97, 11.03, 7.33],
    // 61°緯度
    [6.44, 9.94, 22.18, 42.18, 49.88, 49.38, 49.38, 49.88, 42.18, 22.18, 9.94, 6.44],
    // 62°緯度
    [5.48, 8.71, 21.20, 43.16, 51.11, 50.35, 50.35, 51.11, 43.16, 21.20, 8.71, 5.48],
    // 63°緯度
    [4.44, 7.32, 19.95, 44.42, 52.50, 51.38, 51.38, 52.50, 44.42, 19.95, 7.32, 4.44],
    // 64°緯度
    [3.33, 5.71, 18.25, 46.12, 54.11, 52.50, 52.50, 54.11, 46.12, 18.25, 5.71, 3.33],
    // 65°緯度
    [2.12, 3.82, 15.69, 48.67, 56.00, 53.71, 53.71, 56.00, 48.67, 15.69, 3.82, 2.12],
    // 66°緯度
    [0.80, 1.53, 10.86, 53.50, 58.29, 55.02, 55.02, 58.29, 53.50, 10.86, 1.53, 0.80],
  ];

  /// 南緯星座赤經上升時間表 [緯度][星座索引]
  /// 緯度: 0°-66°（南緯，使用負值表示）
  /// 星座索引: 0=♈︎, 1=♉︎, 2=♊︎, 3=♋︎, 4=♌︎, 5=♍︎, 6=♎︎, 7=♏︎, 8=♐︎, 9=♑︎, 10=♒︎, 11=♓︎
  /// 數值為該星座在該緯度的上升時間（分鐘）
  static const List<List<double>> southernTable = [
    // 0°緯度（赤道）
    [27.91, 29.91, 32.18, 32.18, 29.91, 27.91, 27.91, 29.91, 32.18, 32.18, 29.91, 27.91],
    // -1°緯度
    [28.11, 30.07, 32.25, 32.11, 29.74, 27.71, 27.71, 29.74, 32.11, 32.25, 30.07, 28.11],
    // -2°緯度
    [28.32, 30.24, 32.31, 32.05, 29.58, 27.51, 27.51, 29.58, 32.05, 32.31, 30.24, 28.32],
    // -3°緯度
    [28.52, 30.40, 32.38, 31.98, 29.42, 27.30, 27.30, 29.42, 31.98, 32.38, 30.40, 28.52],
    // -4°緯度
    [28.72, 30.57, 32.45, 31.91, 29.25, 27.10, 27.10, 29.25, 31.91, 32.45, 30.57, 28.72],
    // -5°緯度
    [28.93, 30.73, 32.51, 31.85, 29.09, 26.89, 26.89, 29.09, 31.85, 32.51, 30.73, 28.93],
    // -6°緯度
    [29.13, 30.90, 32.58, 31.78, 28.92, 26.69, 26.69, 28.92, 31.78, 32.58, 30.90, 29.13],
    // -7°緯度
    [29.34, 31.06, 32.65, 31.71, 28.75, 26.48, 26.48, 28.75, 31.71, 32.65, 31.06, 29.34],
    // -8°緯度
    [29.55, 31.23, 32.72, 31.64, 28.59, 26.28, 26.28, 28.59, 31.64, 32.72, 31.23, 29.55],
    // -9°緯度
    [29.75, 31.40, 32.79, 31.58, 28.42, 26.07, 26.07, 28.42, 31.58, 32.79, 31.40, 29.75],
    // -10°緯度
    [29.96, 31.57, 32.86, 31.51, 28.25, 25.86, 25.86, 28.25, 31.51, 32.86, 31.57, 29.96],
    // -11°緯度
    [30.17, 31.74, 32.92, 31.44, 28.08, 25.65, 25.65, 28.08, 31.44, 32.92, 31.74, 30.17],
    // -12°緯度
    [30.38, 31.91, 32.99, 31.37, 27.91, 25.44, 25.44, 27.91, 31.37, 32.99, 31.91, 30.38],
    // -13°緯度
    [30.60, 32.08, 33.07, 31.30, 27.73, 25.23, 25.23, 27.73, 31.30, 33.07, 32.08, 30.60],
    // -14°緯度
    [30.81, 32.26, 33.14, 31.22, 27.56, 25.01, 25.01, 27.56, 31.22, 33.14, 32.26, 30.81],
    // -15°緯度
    [31.03, 32.43, 33.21, 31.15, 27.38, 24.79, 24.79, 27.38, 31.15, 33.21, 32.43, 31.03],
    // -16°緯度
    [31.25, 32.61, 33.28, 31.08, 27.20, 24.58, 24.58, 27.20, 31.08, 33.28, 32.61, 31.25],
    // -17°緯度
    [31.47, 32.79, 33.36, 31.01, 27.02, 24.35, 24.35, 27.02, 31.01, 33.36, 32.79, 31.47],
    // -18°緯度
    [31.69, 32.97, 33.43, 30.93, 26.84, 24.13, 24.13, 26.84, 30.93, 33.43, 32.97, 31.69],
    // -19°緯度
    [31.92, 33.16, 33.51, 30.85, 26.66, 23.90, 23.90, 26.66, 30.85, 33.51, 33.16, 31.92],
    // -20°緯度
    [32.15, 33.35, 33.58, 30.78, 26.47, 23.68, 23.68, 26.47, 30.78, 33.58, 33.35, 32.15],
    // -21°緯度
    [32.38, 33.54, 33.66, 30.70, 26.28, 23.44, 23.44, 26.28, 30.70, 33.66, 33.54, 32.38],
    // -22°緯度
    [32.61, 33.73, 33.74, 30.62, 26.09, 23.21, 23.21, 26.09, 30.62, 33.74, 33.73, 32.61],
    // -23°緯度
    [32.85, 33.93, 33.82, 30.54, 25.89, 22.97, 22.97, 25.89, 30.54, 33.82, 33.93, 32.85],
    // -24°緯度
    [33.09, 34.13, 33.91, 30.45, 25.69, 22.73, 22.73, 25.69, 30.45, 33.91, 34.13, 33.09],
    // -25°緯度
    [33.34, 34.33, 33.99, 30.37, 25.49, 22.48, 22.48, 25.49, 30.37, 33.99, 34.33, 33.34],
    // -26°緯度
    [33.59, 34.54, 34.08, 30.28, 25.28, 22.23, 22.23, 25.28, 30.28, 34.08, 34.54, 33.59],
    // -27°緯度
    [33.85, 34.75, 34.17, 30.19, 25.07, 21.98, 21.98, 25.07, 30.19, 34.17, 34.75, 33.85],
    // -28°緯度
    [34.10, 34.96, 34.26, 30.10, 24.85, 21.72, 21.72, 24.85, 30.10, 34.26, 34.96, 34.10],
    // -29°緯度
    [34.37, 35.18, 34.35, 30.01, 24.63, 21.45, 21.45, 24.63, 30.01, 34.35, 35.18, 34.37],
    // -30°緯度
    [34.64, 35.41, 34.45, 29.92, 24.41, 21.18, 21.18, 24.41, 29.92, 34.45, 35.41, 34.64],
    // -31°緯度
    [34.91, 35.64, 34.54, 29.82, 24.18, 20.91, 20.91, 24.18, 29.82, 34.54, 35.64, 34.91],
    // -32°緯度
    [35.20, 35.88, 34.64, 29.72, 23.94, 20.63, 20.63, 23.94, 29.72, 34.64, 35.88, 35.20],
    // -33°緯度
    [35.48, 36.12, 34.75, 29.61, 23.70, 20.34, 20.34, 23.70, 29.61, 34.75, 36.12, 35.48],
    // -34°緯度
    [35.78, 36.37, 34.85, 29.51, 23.45, 20.04, 20.04, 23.45, 29.51, 34.85, 36.37, 35.78],
    // -35°緯度
    [36.08, 36.63, 34.96, 29.40, 23.19, 19.74, 19.74, 23.19, 29.40, 34.96, 36.63, 36.08],
    // -36°緯度
    [36.39, 36.89, 35.08, 29.28, 22.93, 19.43, 19.43, 22.93, 29.28, 35.08, 36.89, 36.39],
    // -37°緯度
    [36.71, 37.16, 35.20, 29.16, 22.65, 19.12, 19.12, 22.65, 29.16, 35.20, 37.16, 36.71],
    // -38°緯度
    [37.03, 37.44, 35.32, 29.04, 22.37, 18.79, 18.79, 22.37, 29.04, 35.32, 37.44, 37.03],
    // -39°緯度
    [37.37, 37.73, 35.45, 28.91, 22.08, 18.45, 18.45, 22.08, 28.91, 35.45, 37.73, 37.37],
    // -40°緯度
    [37.71, 38.04, 35.58, 28.78, 21.78, 18.11, 18.11, 21.78, 28.78, 35.58, 38.04, 37.71],
    // -41°緯度
    [38.07, 38.35, 35.72, 28.64, 21.47, 17.75, 17.75, 21.47, 28.64, 35.72, 38.35, 38.07],
    // -42°緯度
    [38.44, 38.67, 35.86, 28.50, 21.15, 17.38, 17.38, 21.15, 28.50, 35.86, 38.67, 38.44],
    // -43°緯度
    [38.82, 39.01, 36.02, 28.34, 20.81, 17.00, 17.00, 20.81, 28.34, 36.02, 39.01, 38.82],
    // -44°緯度
    [39.21, 39.36, 36.18, 28.18, 20.46, 16.61, 16.61, 20.46, 28.18, 36.18, 39.36, 39.21],
    // -45°緯度
    [39.62, 39.72, 36.35, 28.02, 20.09, 16.20, 16.20, 20.09, 28.02, 36.35, 39.72, 39.62],
    // -46°緯度
    [40.04, 40.11, 36.52, 27.84, 19.71, 15.78, 15.78, 19.71, 27.84, 36.52, 40.11, 40.04],
    // -47°緯度
    [40.48, 40.51, 36.71, 27.65, 19.31, 15.34, 15.34, 19.31, 27.65, 36.71, 40.51, 40.48],
    // -48°緯度
    [40.94, 40.93, 36.91, 27.45, 18.89, 14.89, 14.89, 18.89, 27.45, 36.91, 40.93, 40.94],
    // -49°緯度
    [41.41, 41.37, 37.13, 27.23, 18.44, 14.41, 14.41, 18.44, 27.23, 37.13, 41.37, 41.41],
    // -50°緯度
    [41.91, 41.84, 37.36, 27.00, 17.97, 13.92, 13.92, 17.97, 27.00, 37.36, 41.84, 41.91],
    // -51°緯度
    [42.42, 42.34, 37.60, 26.76, 17.48, 13.40, 13.40, 17.48, 26.76, 37.60, 42.34, 42.42],
    // -52°緯度
    [42.96, 42.86, 37.87, 26.49, 16.95, 12.86, 12.86, 16.95, 26.49, 37.87, 42.86, 42.96],
    // -53°緯度
    [43.53, 43.42, 38.16, 26.20, 16.39, 12.29, 12.29, 16.39, 26.20, 38.16, 43.42, 43.53],
    // -54°緯度
    [44.13, 44.02, 38.48, 25.88, 15.80, 11.69, 11.69, 15.80, 25.88, 38.48, 44.02, 44.13],
    // -55°緯度
    [44.76, 44.66, 38.83, 25.53, 15.15, 11.07, 11.07, 15.15, 25.53, 38.83, 44.66, 44.76],
    // -56°緯度
    [45.42, 45.35, 39.22, 25.14, 14.46, 10.40, 10.40, 14.46, 25.14, 39.22, 45.35, 45.42],
    // -57°緯度
    [46.12, 46.10, 39.66, 24.71, 13.72, 9.70, 9.70, 13.72, 24.71, 39.66, 46.10, 46.12],
    // -58°緯度
    [46.86, 46.91, 40.15, 24.21, 12.90, 8.96, 8.96, 12.90, 24.21, 40.15, 46.91, 46.86],
    // -59°緯度
    [47.65, 47.80, 40.72, 23.64, 12.01, 8.17, 8.17, 12.01, 23.64, 40.72, 47.80, 47.65],
    // -60°緯度
    [48.49, 48.79, 41.39, 22.97, 11.03, 7.33, 7.33, 11.03, 22.97, 41.39, 48.79, 48.49],
    // -61°緯度
    [49.38, 49.88, 42.18, 22.18, 9.94, 6.44, 6.44, 9.94, 22.18, 42.18, 49.88, 49.38],
    // -62°緯度
    [50.35, 51.11, 43.16, 21.20, 8.71, 5.48, 5.48, 8.71, 21.20, 43.16, 51.11, 50.35],
    // -63°緯度
    [51.38, 52.50, 44.42, 19.95, 7.32, 4.44, 4.44, 7.32, 19.95, 44.42, 52.50, 51.38],
    // -64°緯度
    [52.50, 54.11, 46.12, 18.25, 5.71, 3.33, 3.33, 5.71, 18.25, 46.12, 54.11, 52.50],
    // -65°緯度
    [53.71, 56.00, 48.67, 15.69, 3.82, 2.12, 2.12, 3.82, 15.69, 48.67, 56.00, 53.71],
    // -66°緯度
    [55.02, 58.29, 53.50, 10.86, 1.53, 0.80, 0.80, 1.53, 10.86, 53.50, 58.29, 55.02],
  ];

  /// 星座索引對應表
  static const Map<String, int> signIndex = {
    '牡羊座': 0,  // ♈︎
    '金牛座': 1,  // ♉︎
    '雙子座': 2,  // ♊︎
    '巨蟹座': 3,  // ♋︎
    '獅子座': 4,  // ♌︎
    '處女座': 5,  // ♍︎
    '天秤座': 6,  // ♎︎
    '天蠍座': 7,  // ♏︎
    '射手座': 8,  // ♐︎
    '摩羯座': 9,  // ♑︎
    '水瓶座': 10, // ♒︎
    '雙魚座': 11, // ♓︎
  };

  /// 星座英文名稱對應表
  static const Map<String, int> signIndexEn = {
    'Aries': 0,       // ♈︎
    'Taurus': 1,      // ♉︎
    'Gemini': 2,      // ♊︎
    'Cancer': 3,      // ♋︎
    'Leo': 4,         // ♌︎
    'Virgo': 5,       // ♍︎
    'Libra': 6,       // ♎︎
    'Scorpio': 7,     // ♏︎
    'Sagittarius': 8, // ♐︎
    'Capricorn': 9,   // ♑︎
    'Aquarius': 10,   // ♒︎
    'Pisces': 11,     // ♓︎
  };

  /// 根據緯度和星座獲取上升時間（向後相容的舊方法）
  /// [latitude] 緯度，-66到66度（負值表示南緯）
  /// [sign] 星座名稱
  /// [isNorth] 是否為北半球（此參數已不使用，保留為了向後相容）
  /// 返回 [走完星座所需的赤經上升時間, 換算成一度要走多久]
  /// 支援小數點緯度的線性插值計算
  static List<double> getTimeByLatitudeAndSign(double latitude, String sign, bool isNorth) {
    // 使用新的 getRisingTime 方法，已包含插值計算
    double totalTime = getRisingTime(latitude, sign);
    double timePerDegree = totalTime / 30.0; // 每個星座30度

    return [totalTime, timePerDegree];
  }

  /// 根據緯度和星座獲取上升時間（新方法）
  /// [latitude] 緯度，-66到66度（負值表示南緯）
  /// [sign] 星座名稱（中文或英文）
  /// 返回該星座在該緯度的上升時間（分鐘）
  /// 支援小數點緯度的線性插值計算
  static double getRisingTime(double latitude, String sign) {
    // 確保緯度在有效範圍內
    double clampedLatitude = latitude.clamp(-66.0, 66.0);

    // 根據緯度正負選擇對應的表格
    bool isNorthernHemisphere = clampedLatitude >= 0;
    double absLatitude = clampedLatitude.abs();

    // 獲取星座對應的索引
    int? signIdx = signIndex[sign] ?? signIndexEn[sign];

    if (signIdx == null) {
      throw ArgumentError('無效的星座名稱: $sign');
    }

    // 選擇對應的表格
    List<List<double>> selectedTable = isNorthernHemisphere ? northernTable : southernTable;

    // 如果是整數緯度，直接返回表格值
    if (absLatitude == absLatitude.roundToDouble()) {
      int latIndex = _getLatitudeIndex(absLatitude);
      return selectedTable[latIndex][signIdx];
    }

    // 小數點緯度使用線性插值
    return _interpolateRisingTime(selectedTable, absLatitude, signIdx);
  }

  /// 根據緯度和星座獲取上升時間（每度所需時間）
  /// [latitude] 緯度，-66到66度（負值表示南緯）
  /// [sign] 星座名稱（中文或英文）
  /// 返回該星座在該緯度每度所需的上升時間（分鐘）
  static double getTimePerDegree(double latitude, String sign) {
    double totalTime = getRisingTime(latitude, sign);
    return totalTime / 30.0; // 每個星座30度
  }

  /// 根據緯度獲取表格中的正確索引
  /// 表格結構：0°-66°，每度一個條目
  /// [latitude] 緯度的絕對值（已經處理過正負號）
  static int _getLatitudeIndex(double latitude) {
    // 直接使用緯度的整數部分作為索引
    int index = latitude.round();

    // 確保索引在有效範圍內
    return index.clamp(0, 66);
  }

  /// 使用線性插值計算小數點緯度的上升時間
  /// [table] 要使用的表格（北緯或南緯）
  /// [latitude] 緯度的絕對值
  /// [signIndex] 星座索引
  /// 返回插值計算後的上升時間
  static double _interpolateRisingTime(List<List<double>> table, double latitude, int signIndex) {
    // 獲取下界和上界的整數緯度
    int lowerLatitude = latitude.floor();
    int upperLatitude = latitude.ceil();

    // 確保索引在有效範圍內
    lowerLatitude = lowerLatitude.clamp(0, 66);
    upperLatitude = upperLatitude.clamp(0, 66);

    // 如果上下界相同（整數緯度），直接返回
    if (lowerLatitude == upperLatitude) {
      return table[lowerLatitude][signIndex];
    }

    // 獲取上下界的上升時間
    double lowerTime = table[lowerLatitude][signIndex];
    double upperTime = table[upperLatitude][signIndex];

    // 計算插值比例
    double fraction = latitude - lowerLatitude;

    // 線性插值計算
    double interpolatedTime = lowerTime + (upperTime - lowerTime) * fraction;

    return interpolatedTime;
  }

  /// 調試方法：獲取緯度索引的詳細資訊
  static Map<String, dynamic> getLatitudeIndexDebugInfo(double latitude) {
    final double clampedLatitude = latitude.clamp(-66.0, 66.0);
    final bool isNorthernHemisphere = clampedLatitude >= 0;
    final double absLatitude = clampedLatitude.abs();
    final int index = _getLatitudeIndex(absLatitude);
    final int roundedLat = clampedLatitude.round();

    return {
      'inputLatitude': latitude,
      'clampedLatitude': clampedLatitude,
      'isNorthernHemisphere': isNorthernHemisphere,
      'absLatitude': absLatitude,
      'calculatedIndex': index,
      'description': '${roundedLat >= 0 ? "北緯" : "南緯"}${absLatitude.round()}°',
      'tableStructure': '北緯/南緯 0°-66°，每度一個條目（索引0-66）',
      'maxLatitude': 66,
      'minLatitude': -66,
      'northernTableEntries': northernTable.length,
      'southernTableEntries': southernTable.length,
    };
  }

  /// 獲取所有支援的星座名稱
  static List<String> getSupportedSigns() {
    return signIndex.keys.toList();
  }

  /// 獲取所有支援的英文星座名稱
  static List<String> getSupportedSignsEn() {
    return signIndexEn.keys.toList();
  }

  /// 檢查星座名稱是否有效
  static bool isValidSign(String sign) {
    return signIndex.containsKey(sign) || signIndexEn.containsKey(sign);
  }

  /// 獲取表格的統計資訊
  static Map<String, dynamic> getTableStats() {
    return {
      'latitudeRange': '-66°到66°（南緯到北緯）',
      'northernLatitudes': northernTable.length,
      'southernLatitudes': southernTable.length,
      'totalLatitudes': northernTable.length + southernTable.length,
      'signsPerLatitude': northernTable.isNotEmpty ? northernTable[0].length : 0,
      'northernDataPoints': northernTable.length * (northernTable.isNotEmpty ? northernTable[0].length : 0),
      'southernDataPoints': southernTable.length * (southernTable.isNotEmpty ? southernTable[0].length : 0),
      'totalDataPoints': (northernTable.length + southernTable.length) * (northernTable.isNotEmpty ? northernTable[0].length : 0),
      'supportedSigns': getSupportedSigns(),
      'supportedSignsEn': getSupportedSignsEn(),
    };
  }

  /// 檢查緯度是否在支援範圍內
  static bool isLatitudeSupported(double latitude) {
    return latitude >= -66.0 && latitude <= 66.0;
  }

  /// 獲取指定緯度的半球資訊
  static String getHemisphereInfo(double latitude) {
    if (latitude > 0) {
      return '北緯 ${latitude.abs()}°';
    } else if (latitude < 0) {
      return '南緯 ${latitude.abs()}°';
    } else {
      return '赤道 0°';
    }
  }

  /// 獲取插值計算的詳細資訊（調試用）
  /// [latitude] 緯度，-66到66度
  /// [sign] 星座名稱
  /// 返回插值計算的詳細過程
  static Map<String, dynamic> getInterpolationDebugInfo(double latitude, String sign) {
    // 確保緯度在有效範圍內
    double clampedLatitude = latitude.clamp(-66.0, 66.0);
    bool isNorthernHemisphere = clampedLatitude >= 0;
    double absLatitude = clampedLatitude.abs();

    // 獲取星座索引
    int? signIdx = signIndex[sign] ?? signIndexEn[sign];
    if (signIdx == null) {
      return {'error': '無效的星座名稱: $sign'};
    }

    // 選擇表格
    List<List<double>> selectedTable = isNorthernHemisphere ? northernTable : southernTable;

    // 檢查是否需要插值
    bool needsInterpolation = absLatitude != absLatitude.roundToDouble();

    if (!needsInterpolation) {
      // 整數緯度，直接查表
      int latIndex = _getLatitudeIndex(absLatitude);
      double risingTime = selectedTable[latIndex][signIdx];

      return {
        'inputLatitude': latitude,
        'clampedLatitude': clampedLatitude,
        'hemisphere': getHemisphereInfo(latitude),
        'absLatitude': absLatitude,
        'sign': sign,
        'signIndex': signIdx,
        'needsInterpolation': false,
        'tableIndex': latIndex,
        'risingTime': risingTime,
        'calculationMethod': '直接查表',
      };
    } else {
      // 小數緯度，需要插值
      int lowerLatitude = absLatitude.floor().clamp(0, 66);
      int upperLatitude = absLatitude.ceil().clamp(0, 66);

      double lowerTime = selectedTable[lowerLatitude][signIdx];
      double upperTime = selectedTable[upperLatitude][signIdx];

      double fraction = absLatitude - lowerLatitude;
      double interpolatedTime = lowerTime + (upperTime - lowerTime) * fraction;

      return {
        'inputLatitude': latitude,
        'clampedLatitude': clampedLatitude,
        'hemisphere': getHemisphereInfo(latitude),
        'absLatitude': absLatitude,
        'sign': sign,
        'signIndex': signIdx,
        'needsInterpolation': true,
        'lowerLatitude': lowerLatitude,
        'upperLatitude': upperLatitude,
        'lowerTime': lowerTime,
        'upperTime': upperTime,
        'fraction': fraction,
        'interpolatedTime': interpolatedTime,
        'calculationMethod': '線性插值',
        'formula': 'lowerTime + (upperTime - lowerTime) × fraction',
      };
    }
  }
}