import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'app_theme.dart';

/// Manages the app's theme state
class ThemeProvider extends ChangeNotifier {
  static const String _themePreferenceKey = 'theme_preference';
  static const String _themeModeKey = 'theme_mode';
  static const String _userModeKey = 'user_mode';

  ThemeMode _themeMode = ThemeMode.light;
  String _userMode = 'starmaster'; // 默認為專業模式

  ThemeProvider() {
    _loadThemePreference();
  }

  /// Current theme mode
  ThemeMode get themeMode => _themeMode;

  /// Current user mode
  String get userMode => _userMode;

  /// Current theme data based on user mode
  ThemeData get themeData {
    switch (_themeMode) {
      case ThemeMode.dark:
        // Currently using light theme for both modes
        return _userMode == 'starlight'
            ? AstrealAppTheme.starlightTheme
            : AstrealAppTheme.starmasterTheme;
      case ThemeMode.light:
      default:
        return _userMode == 'starlight'
            ? AstrealAppTheme.starlightTheme
            : AstrealAppTheme.starmasterTheme;
    }
  }

  /// Get AppBar theme based on user mode
  AppBarTheme get appBarTheme {
    return _userMode == 'starlight'
        ? AstrealAppTheme.starlightAppBarTheme
        : AstrealAppTheme.starmasterAppBarTheme;
  }

  /// Toggle between light and dark theme
  void toggleTheme() {
    _themeMode = _themeMode == ThemeMode.light ? ThemeMode.dark : ThemeMode.light;
    _saveThemePreference();
    notifyListeners();
  }

  /// Set specific theme mode
  void setThemeMode(ThemeMode mode) {
    _themeMode = mode;
    _saveThemePreference();
    notifyListeners();
  }

  /// Set user mode
  void setUserMode(String mode) {
    _userMode = mode;
    _saveUserMode();
    notifyListeners();
  }

  /// Load theme preference from SharedPreferences
  Future<void> _loadThemePreference() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final themeModeIndex = prefs.getInt(_themeModeKey) ?? ThemeMode.light.index;
      _themeMode = ThemeMode.values[themeModeIndex];

      // Load user mode
      _userMode = prefs.getString(_userModeKey) ?? 'starmaster';

      notifyListeners();
    } catch (e) {
      // Default to light theme if there's an error
      _themeMode = ThemeMode.light;
    }
  }

  /// Save theme preference to SharedPreferences
  Future<void> _saveThemePreference() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_themeModeKey, _themeMode.index);
    } catch (e) {
      // Ignore errors when saving preferences
    }
  }

  /// Save user mode to SharedPreferences
  Future<void> _saveUserMode() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_userModeKey, _userMode);
    } catch (e) {
      // Ignore errors when saving preferences
    }
  }
}
