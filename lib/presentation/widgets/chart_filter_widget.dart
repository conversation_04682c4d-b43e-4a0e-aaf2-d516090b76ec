import 'package:flutter/material.dart';

import '../../core/constants/astrology_constants.dart';
import '../../features/astrology/models/chart_filter.dart';
import '../../features/astrology/services/chart_filter_service.dart';

/// 篩選器組件
///
/// 提供完整的篩選器UI，支援添加、編輯、刪除篩選條件
class ChartFilterWidget extends StatefulWidget {
  final ChartFilter filter;
  final ValueChanged<ChartFilter> onFilterChanged;
  final VoidCallback? onClose;

  const ChartFilterWidget({
    Key? key,
    required this.filter,
    required this.onFilterChanged,
    this.onClose,
  }) : super(key: key);

  @override
  State<ChartFilterWidget> createState() => _ChartFilterWidgetState();
}

class _ChartFilterWidgetState extends State<ChartFilterWidget> {
  late ChartFilter _currentFilter;
  final TextEditingController _nameController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _currentFilter = widget.filter;
    _nameController.text = _currentFilter.name;
  }

  @override
  void dispose() {
    _nameController.dispose();
    super.dispose();
  }

  void _updateFilter(ChartFilter newFilter) {
    setState(() {
      _currentFilter = newFilter;
    });
    widget.onFilterChanged(_currentFilter);
  }

  void _updateFilterName(String name) {
    _updateFilter(_currentFilter.copyWith(name: name));
  }

  void _addGroup() {
    final newGroup = FilterGroup(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      conditions: [],
    );
    _updateFilter(_currentFilter.addGroup(newGroup));
  }

  void _removeGroup(String groupId) {
    _updateFilter(_currentFilter.removeGroup(groupId));
  }

  void _updateGroup(FilterGroup group) {
    _updateFilter(_currentFilter.updateGroup(group));
  }

  void _toggleGroupLogicalOperator() {
    final newOperator =
        _currentFilter.groupLogicalOperator == LogicalOperator.and
            ? LogicalOperator.or
            : LogicalOperator.and;
    _updateFilter(_currentFilter.copyWith(groupLogicalOperator: newOperator));
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            // const SizedBox(height: 16),
            // _buildFilterName(),
            const SizedBox(height: 16),
            _buildGroupLogicalOperator(),
            const SizedBox(height: 16),
            _buildGroups(),
            const SizedBox(height: 16),
            _buildAddGroupButton(),
            const SizedBox(height: 16),
            _buildFilterSummary(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        const Icon(Icons.filter_list, color: Colors.blue),
        const SizedBox(width: 8),
        const Text(
          '篩選器設定',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const Spacer(),
        if (widget.onClose != null)
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: widget.onClose,
          ),
      ],
    );
  }

  Widget _buildFilterName() {
    return TextField(
      controller: _nameController,
      decoration: const InputDecoration(
        labelText: '篩選器名稱',
        border: OutlineInputBorder(),
        prefixIcon: Icon(Icons.label),
      ),
      onChanged: _updateFilterName,
    );
  }

  Widget _buildGroupLogicalOperator() {
    if (_currentFilter.groups.length <= 1) {
      return const SizedBox.shrink();
    }

    return Column(
      children: [
        const Text('篩選組之間的關係：'),
        const SizedBox(height: 4),
        Row(
          children: [
            const SizedBox(width: 4),
            ChoiceChip(
              label: const Text('且 (AND)'),
              selected:
                  _currentFilter.groupLogicalOperator == LogicalOperator.and,
              onSelected: (_) {
                if (_currentFilter.groupLogicalOperator !=
                    LogicalOperator.and) {
                  _toggleGroupLogicalOperator();
                }
              },
            ),
            const SizedBox(width: 4),
            ChoiceChip(
              label: const Text('或 (OR)'),
              selected:
                  _currentFilter.groupLogicalOperator == LogicalOperator.or,
              onSelected: (_) {
                if (_currentFilter.groupLogicalOperator != LogicalOperator.or) {
                  _toggleGroupLogicalOperator();
                }
              },
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildGroups() {
    if (_currentFilter.groups.isEmpty) {
      return const Center(
        child: Text(
          '尚未設定篩選條件',
          style: TextStyle(
            color: Colors.grey,
            fontSize: 16,
          ),
        ),
      );
    }

    return Column(
      children: _currentFilter.groups.asMap().entries.map((entry) {
        final index = entry.key;
        final group = entry.value;

        return Column(
          children: [
            if (index > 0) _buildGroupSeparator(),
            FilterGroupWidget(
              group: group,
              onGroupChanged: _updateGroup,
              onRemove: () => _removeGroup(group.id),
            ),
          ],
        );
      }).toList(),
    );
  }

  Widget _buildGroupSeparator() {
    final operatorText =
        _currentFilter.groupLogicalOperator == LogicalOperator.and ? '且' : '或';

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          const Expanded(child: Divider()),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Chip(
              label: Text(operatorText),
              backgroundColor: Colors.blue.shade100,
            ),
          ),
          const Expanded(child: Divider()),
        ],
      ),
    );
  }

  Widget _buildAddGroupButton() {
    return Center(
      child: ElevatedButton.icon(
        onPressed: _addGroup,
        icon: const Icon(Icons.add),
        label: const Text('新增篩選組'),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.green,
          foregroundColor: Colors.white,
        ),
      ),
    );
  }

  Widget _buildFilterSummary() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '篩選條件摘要：',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            _currentFilter.getDescription(),
            style: const TextStyle(fontSize: 12),
          ),
        ],
      ),
    );
  }
}

/// 篩選組組件
class FilterGroupWidget extends StatefulWidget {
  final FilterGroup group;
  final ValueChanged<FilterGroup> onGroupChanged;
  final VoidCallback onRemove;

  const FilterGroupWidget({
    Key? key,
    required this.group,
    required this.onGroupChanged,
    required this.onRemove,
  }) : super(key: key);

  @override
  State<FilterGroupWidget> createState() => _FilterGroupWidgetState();
}

class _FilterGroupWidgetState extends State<FilterGroupWidget> {
  void _addCondition() {
    final newCondition = FilterCondition(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      type: FilterType.planetInSign,
      operator: FilterOperator.equals,
    );

    final updatedGroup = widget.group.addCondition(newCondition);
    widget.onGroupChanged(updatedGroup);
  }

  void _removeCondition(String conditionId) {
    final updatedGroup = widget.group.removeCondition(conditionId);
    widget.onGroupChanged(updatedGroup);
  }

  void _updateCondition(FilterCondition condition) {
    final updatedGroup = widget.group.updateCondition(condition);
    widget.onGroupChanged(updatedGroup);
  }

  void _toggleLogicalOperator() {
    final newOperator = widget.group.logicalOperator == LogicalOperator.and
        ? LogicalOperator.or
        : LogicalOperator.and;

    final updatedGroup = widget.group.copyWith(logicalOperator: newOperator);
    widget.onGroupChanged(updatedGroup);
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildGroupHeader(),
            const SizedBox(height: 8),
            if (widget.group.conditions.length > 1) _buildLogicalOperator(),
            if (widget.group.conditions.length > 1) const SizedBox(height: 8),
            _buildConditions(),
            const SizedBox(height: 8),
            _buildAddConditionButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildGroupHeader() {
    return Row(
      children: [
        const Icon(Icons.group_work, size: 16, color: Colors.blue),
        const SizedBox(width: 4),
        const Text(
          '篩選組',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 14,
          ),
        ),
        const Spacer(),
        IconButton(
          icon: const Icon(Icons.delete, size: 16),
          onPressed: widget.onRemove,
          tooltip: '刪除篩選組',
        ),
      ],
    );
  }

  Widget _buildLogicalOperator() {
    return Row(
      children: [
        const Text('條件之間的關係：', style: TextStyle(fontSize: 12)),
        const SizedBox(width: 8),
        ChoiceChip(
          label: const Text('且', style: TextStyle(fontSize: 10)),
          selected: widget.group.logicalOperator == LogicalOperator.and,
          onSelected: (_) {
            if (widget.group.logicalOperator != LogicalOperator.and) {
              _toggleLogicalOperator();
            }
          },
        ),
        const SizedBox(width: 4),
        ChoiceChip(
          label: const Text('或', style: TextStyle(fontSize: 10)),
          selected: widget.group.logicalOperator == LogicalOperator.or,
          onSelected: (_) {
            if (widget.group.logicalOperator != LogicalOperator.or) {
              _toggleLogicalOperator();
            }
          },
        ),
      ],
    );
  }

  Widget _buildConditions() {
    if (widget.group.conditions.isEmpty) {
      return const Center(
        child: Text(
          '尚未設定條件',
          style: TextStyle(
            color: Colors.grey,
            fontSize: 12,
          ),
        ),
      );
    }

    return Column(
      children: widget.group.conditions.asMap().entries.map((entry) {
        final index = entry.key;
        final condition = entry.value;

        return Column(
          children: [
            if (index > 0) _buildConditionSeparator(),
            FilterConditionWidget(
              condition: condition,
              onConditionChanged: _updateCondition,
              onRemove: () => _removeCondition(condition.id),
            ),
          ],
        );
      }).toList(),
    );
  }

  Widget _buildConditionSeparator() {
    final operatorText =
        widget.group.logicalOperator == LogicalOperator.and ? '且' : '或';

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          const Expanded(child: Divider()),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            child: Text(
              operatorText,
              style: const TextStyle(
                fontSize: 10,
                color: Colors.grey,
              ),
            ),
          ),
          const Expanded(child: Divider()),
        ],
      ),
    );
  }

  Widget _buildAddConditionButton() {
    return Center(
      child: TextButton.icon(
        onPressed: _addCondition,
        icon: const Icon(Icons.add, size: 16),
        label: const Text('新增條件', style: TextStyle(fontSize: 12)),
      ),
    );
  }
}

/// 篩選條件組件
class FilterConditionWidget extends StatefulWidget {
  final FilterCondition condition;
  final ValueChanged<FilterCondition> onConditionChanged;
  final VoidCallback onRemove;

  const FilterConditionWidget({
    Key? key,
    required this.condition,
    required this.onConditionChanged,
    required this.onRemove,
  }) : super(key: key);

  @override
  State<FilterConditionWidget> createState() => _FilterConditionWidgetState();
}

class _FilterConditionWidgetState extends State<FilterConditionWidget> {
  late FilterCondition _currentCondition;

  @override
  void initState() {
    super.initState();
    _currentCondition = widget.condition;
  }

  void _updateCondition(FilterCondition newCondition) {
    setState(() {
      _currentCondition = newCondition;
    });
    widget.onConditionChanged(_currentCondition);
  }

  void _updateType(FilterType type) {
    _updateCondition(_currentCondition.copyWith(
      type: type,
      planetName: null,
      signName: null,
      houseNumber: null,
      signNames: null,
      houseNumbers: null,
    ));
  }

  void _updateOperator(FilterOperator operator) {
    _updateCondition(_currentCondition.copyWith(operator: operator));
  }

  void _updatePlanetName(String planetName) {
    _updateCondition(_currentCondition.copyWith(planetName: planetName));
  }

  void _updateSignName(String signName) {
    _updateCondition(_currentCondition.copyWith(signName: signName));
  }

  void _updateHouseNumber(int houseNumber) {
    _updateCondition(_currentCondition.copyWith(houseNumber: houseNumber));
  }

  void _updateSignNames(List<String> signNames) {
    _updateCondition(_currentCondition.copyWith(signNames: signNames));
  }

  void _updateHouseNumbers(List<int> houseNumbers) {
    _updateCondition(_currentCondition.copyWith(houseNumbers: houseNumbers));
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildConditionHeader(),
          const SizedBox(height: 8),
          _buildTypeSelector(),
          const SizedBox(height: 8),
          _buildOperatorSelector(),
          const SizedBox(height: 8),
          _buildValueSelector(),
          const SizedBox(height: 8),
          _buildConditionSummary(),
        ],
      ),
    );
  }

  Widget _buildConditionHeader() {
    return Row(
      children: [
        const Icon(Icons.rule, size: 14, color: Colors.orange),
        const SizedBox(width: 4),
        const Text(
          '篩選條件',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 12,
          ),
        ),
        const Spacer(),
        IconButton(
          icon: const Icon(Icons.delete, size: 14),
          onPressed: widget.onRemove,
          tooltip: '刪除條件',
        ),
      ],
    );
  }

  Widget _buildTypeSelector() {
    return Row(
      children: [
        const Text('類型：', style: TextStyle(fontSize: 11)),
        const SizedBox(width: 8),
        Expanded(
          child: DropdownButton<FilterType>(
            value: _currentCondition.type,
            isExpanded: true,
            style: const TextStyle(fontSize: 11, color: Colors.black),
            items: const [
              DropdownMenuItem(
                value: FilterType.planetInSign,
                child: Text('行星在星座'),
              ),
              DropdownMenuItem(
                value: FilterType.planetInHouse,
                child: Text('行星在宮位'),
              ),
              DropdownMenuItem(
                value: FilterType.houseInSign,
                child: Text('宮位在星座'),
              ),
              DropdownMenuItem(
                value: FilterType.planetAspect,
                child: Text('行星相位'),
              ),
            ],
            onChanged: (type) {
              if (type != null) _updateType(type);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildOperatorSelector() {
    return Row(
      children: [
        const Text('操作：', style: TextStyle(fontSize: 11)),
        const SizedBox(width: 8),
        Expanded(
          child: DropdownButton<FilterOperator>(
            value: _currentCondition.operator,
            isExpanded: true,
            style: const TextStyle(fontSize: 11, color: Colors.black),
            items: const [
              DropdownMenuItem(
                value: FilterOperator.equals,
                child: Text('等於'),
              ),
              DropdownMenuItem(
                value: FilterOperator.notEquals,
                child: Text('不等於'),
              ),
              DropdownMenuItem(
                value: FilterOperator.contains,
                child: Text('包含'),
              ),
              DropdownMenuItem(
                value: FilterOperator.notContains,
                child: Text('不包含'),
              ),
            ],
            onChanged: (operator) {
              if (operator != null) _updateOperator(operator);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildValueSelector() {
    switch (_currentCondition.type) {
      case FilterType.planetInSign:
        return _buildPlanetInSignSelector();
      case FilterType.planetInHouse:
        return _buildPlanetInHouseSelector();
      case FilterType.houseInSign:
        return _buildHouseInSignSelector();
      case FilterType.planetAspect:
        return _buildPlanetAspectSelector();
    }
  }

  Widget _buildPlanetInSignSelector() {
    return Column(
      children: [
        // 行星選擇
        Row(
          children: [
            const Text('行星：', style: TextStyle(fontSize: 11)),
            const SizedBox(width: 8),
            Expanded(
              child: DropdownButton<String>(
                value: _currentCondition.planetName,
                isExpanded: true,
                style: const TextStyle(fontSize: 11, color: Colors.black),
                hint: const Text('選擇行星', style: TextStyle(fontSize: 11)),
                items: AstrologyConstants.PLANETS.map((planet) {
                  return DropdownMenuItem<String>(
                    value: planet['name'] as String,
                    child: Text(planet['name'] as String),
                  );
                }).toList(),
                onChanged: (planetName) {
                  if (planetName != null) _updatePlanetName(planetName);
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        // 星座選擇
        _buildSignSelector(),
      ],
    );
  }

  Widget _buildPlanetInHouseSelector() {
    return Column(
      children: [
        // 行星選擇
        Row(
          children: [
            const Text('行星：', style: TextStyle(fontSize: 11)),
            const SizedBox(width: 8),
            Expanded(
              child: DropdownButton<String>(
                value: _currentCondition.planetName,
                isExpanded: true,
                style: const TextStyle(fontSize: 11, color: Colors.black),
                hint: const Text('選擇行星', style: TextStyle(fontSize: 11)),
                items: AstrologyConstants.PLANETS.map((planet) {
                  return DropdownMenuItem<String>(
                    value: planet['name'] as String,
                    child: Text(planet['name'] as String),
                  );
                }).toList(),
                onChanged: (planetName) {
                  if (planetName != null) _updatePlanetName(planetName);
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        // 宮位選擇
        _buildHouseSelector(),
      ],
    );
  }

  Widget _buildHouseInSignSelector() {
    return Column(
      children: [
        // 宮位選擇
        Row(
          children: [
            const Text('宮位：', style: TextStyle(fontSize: 11)),
            const SizedBox(width: 8),
            Expanded(
              child: DropdownButton<int>(
                value: _currentCondition.houseNumber,
                isExpanded: true,
                style: const TextStyle(fontSize: 11, color: Colors.black),
                hint: const Text('選擇宮位', style: TextStyle(fontSize: 11)),
                items: List.generate(12, (index) {
                  final houseNumber = index + 1;
                  return DropdownMenuItem<int>(
                    value: houseNumber,
                    child: Text('第${houseNumber}宮'),
                  );
                }),
                onChanged: (houseNumber) {
                  if (houseNumber != null) _updateHouseNumber(houseNumber);
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        // 星座選擇
        _buildSignSelector(),
      ],
    );
  }

  Widget _buildSignSelector() {
    final isMultiSelect =
        _currentCondition.operator == FilterOperator.contains ||
            _currentCondition.operator == FilterOperator.notContains;

    if (isMultiSelect) {
      return _buildMultiSignSelector();
    } else {
      return _buildSingleSignSelector();
    }
  }

  Widget _buildSingleSignSelector() {
    return Row(
      children: [
        const Text('星座：', style: TextStyle(fontSize: 11)),
        const SizedBox(width: 8),
        Expanded(
          child: DropdownButton<String>(
            value: _currentCondition.signName,
            isExpanded: true,
            style: const TextStyle(fontSize: 11, color: Colors.black),
            hint: const Text('選擇星座', style: TextStyle(fontSize: 11)),
            items: AstrologyConstants.ZODIAC_SIGNS.map((sign) {
              return DropdownMenuItem<String>(
                value: sign,
                child: Text(sign),
              );
            }).toList(),
            onChanged: (signName) {
              if (signName != null) _updateSignName(signName);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildMultiSignSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('星座：', style: TextStyle(fontSize: 11)),
        const SizedBox(height: 4),
        _buildElementButtons(),
        const SizedBox(height: 4),
        _buildQualityButtons(),
        const SizedBox(height: 4),
        _buildPolarityButtons(),
        const SizedBox(height: 4),
        _buildIndividualSignSelector(),
      ],
    );
  }

  Widget _buildElementButtons() {
    return Row(
      children: [
        const Text('元素：', style: TextStyle(fontSize: 10)),
        const SizedBox(width: 4),
        ...[
          ('火', Colors.red),
          ('土', Colors.brown),
          ('風', Colors.blue),
          ('水', Colors.cyan),
        ].map((element) {
          final elementName = element.$1;
          final color = element.$2;
          final signs = ChartFilterService.getSignsByElement(elementName);
          final isSelected = _currentCondition.signNames != null &&
              signs
                  .every((sign) => _currentCondition.signNames!.contains(sign));

          return Padding(
            padding: const EdgeInsets.only(right: 4),
            child: FilterChip(
              label: Text(elementName, style: const TextStyle(fontSize: 9)),
              selected: isSelected,
              selectedColor: color.withValues(alpha: 0.3),
              onSelected: (selected) {
                if (selected) {
                  _updateSignNames(signs);
                } else {
                  _updateSignNames([]);
                }
              },
            ),
          );
        }).toList(),
      ],
    );
  }

  Widget _buildQualityButtons() {
    return Row(
      children: [
        const Text('性質：', style: TextStyle(fontSize: 10)),
        const SizedBox(width: 4),
        ...[
          '基本',
          '固定',
          '變動',
        ].map((quality) {
          final signs = ChartFilterService.getSignsByQuality(quality);
          final isSelected = _currentCondition.signNames != null &&
              signs
                  .every((sign) => _currentCondition.signNames!.contains(sign));

          return Padding(
            padding: const EdgeInsets.only(right: 4),
            child: FilterChip(
              label: Text(quality, style: const TextStyle(fontSize: 9)),
              selected: isSelected,
              onSelected: (selected) {
                if (selected) {
                  _updateSignNames(signs);
                } else {
                  _updateSignNames([]);
                }
              },
            ),
          );
        }).toList(),
      ],
    );
  }

  Widget _buildPolarityButtons() {
    return Row(
      children: [
        const Text('極性：', style: TextStyle(fontSize: 10)),
        const SizedBox(width: 4),
        ...[
          '陽性',
          '陰性',
        ].map((polarity) {
          final signs = ChartFilterService.getSignsByPolarity(polarity);
          final isSelected = _currentCondition.signNames != null &&
              signs
                  .every((sign) => _currentCondition.signNames!.contains(sign));

          return Padding(
            padding: const EdgeInsets.only(right: 4),
            child: FilterChip(
              label: Text(polarity, style: const TextStyle(fontSize: 9)),
              selected: isSelected,
              onSelected: (selected) {
                if (selected) {
                  _updateSignNames(signs);
                } else {
                  _updateSignNames([]);
                }
              },
            ),
          );
        }).toList(),
      ],
    );
  }

  Widget _buildIndividualSignSelector() {
    return Wrap(
      spacing: 4,
      runSpacing: 4,
      children: AstrologyConstants.ZODIAC_SIGNS.map((sign) {
        final isSelected = _currentCondition.signNames?.contains(sign) ?? false;

        return FilterChip(
          label: Text(sign, style: const TextStyle(fontSize: 9)),
          selected: isSelected,
          onSelected: (selected) {
            final currentSigns = _currentCondition.signNames ?? [];
            List<String> newSigns;

            if (selected) {
              newSigns = [...currentSigns, sign];
            } else {
              newSigns = currentSigns.where((s) => s != sign).toList();
            }

            _updateSignNames(newSigns);
          },
        );
      }).toList(),
    );
  }

  Widget _buildHouseSelector() {
    final isMultiSelect =
        _currentCondition.operator == FilterOperator.contains ||
            _currentCondition.operator == FilterOperator.notContains;

    if (isMultiSelect) {
      return _buildMultiHouseSelector();
    } else {
      return _buildSingleHouseSelector();
    }
  }

  Widget _buildSingleHouseSelector() {
    return Row(
      children: [
        const Text('宮位：', style: TextStyle(fontSize: 11)),
        const SizedBox(width: 8),
        Expanded(
          child: DropdownButton<int>(
            value: _currentCondition.houseNumber,
            isExpanded: true,
            style: const TextStyle(fontSize: 11, color: Colors.black),
            hint: const Text('選擇宮位', style: TextStyle(fontSize: 11)),
            items: List.generate(12, (index) {
              final houseNumber = index + 1;
              return DropdownMenuItem<int>(
                value: houseNumber,
                child: Text('第${houseNumber}宮'),
              );
            }),
            onChanged: (houseNumber) {
              if (houseNumber != null) _updateHouseNumber(houseNumber);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildMultiHouseSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('宮位：', style: TextStyle(fontSize: 11)),
        const SizedBox(height: 4),
        _buildHouseTypeButtons(),
        const SizedBox(height: 4),
        _buildIndividualHouseSelector(),
      ],
    );
  }

  Widget _buildHouseTypeButtons() {
    return Row(
      children: [
        const Text('類型：', style: TextStyle(fontSize: 10)),
        const SizedBox(width: 4),
        ...[
          '始宮',
          '續宮',
          '果宮',
        ].map((houseType) {
          final houses = ChartFilterService.getHousesByType(houseType);
          final isSelected = _currentCondition.houseNumbers != null &&
              houses.every(
                  (house) => _currentCondition.houseNumbers!.contains(house));

          return Padding(
            padding: const EdgeInsets.only(right: 4),
            child: FilterChip(
              label: Text(houseType, style: const TextStyle(fontSize: 9)),
              selected: isSelected,
              onSelected: (selected) {
                if (selected) {
                  _updateHouseNumbers(houses);
                } else {
                  _updateHouseNumbers([]);
                }
              },
            ),
          );
        }).toList(),
      ],
    );
  }

  Widget _buildIndividualHouseSelector() {
    return Wrap(
      spacing: 4,
      runSpacing: 4,
      children: List.generate(12, (index) {
        final houseNumber = index + 1;
        final isSelected =
            _currentCondition.houseNumbers?.contains(houseNumber) ?? false;

        return FilterChip(
          label: Text('第${houseNumber}宮', style: const TextStyle(fontSize: 9)),
          selected: isSelected,
          onSelected: (selected) {
            final currentHouses = _currentCondition.houseNumbers ?? [];
            List<int> newHouses;

            if (selected) {
              newHouses = [...currentHouses, houseNumber];
            } else {
              newHouses = currentHouses.where((h) => h != houseNumber).toList();
            }

            _updateHouseNumbers(newHouses);
          },
        );
      }),
    );
  }

  Widget _buildConditionSummary() {
    return Container(
      padding: const EdgeInsets.all(6),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(4),
      ),
      child: Row(
        children: [
          const Icon(Icons.info_outline, size: 12, color: Colors.blue),
          const SizedBox(width: 4),
          Expanded(
            child: Text(
              _currentCondition.getDescription(),
              style: const TextStyle(
                fontSize: 10,
                color: Colors.blue,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 構建行星相位選擇器
  Widget _buildPlanetAspectSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 第一個行星選擇
        Row(
          children: [
            const Text('第一個行星：', style: TextStyle(fontSize: 10)),
            const SizedBox(width: 8),
            Expanded(
              child: DropdownButton<String>(
                value: _currentCondition.planetName,
                isExpanded: true,
                style: const TextStyle(fontSize: 10, color: Colors.black),
                items: AstrologyConstants.PLANETS.map((planet) {
                  return DropdownMenuItem<String>(
                    value: planet['name'] as String,
                    child: Text(planet['name'] as String),
                  );
                }).toList(),
                onChanged: (planetName) {
                  if (planetName != null) {
                    _updateCondition(_currentCondition.copyWith(planetName: planetName));
                  }
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),

        // 第二個行星選擇
        Row(
          children: [
            const Text('第二個行星：', style: TextStyle(fontSize: 10)),
            const SizedBox(width: 8),
            Expanded(
              child: DropdownButton<String>(
                value: _currentCondition.planet2Name,
                isExpanded: true,
                style: const TextStyle(fontSize: 10, color: Colors.black),
                items: AstrologyConstants.PLANETS.map((planet) {
                  return DropdownMenuItem<String>(
                    value: planet['name'] as String,
                    child: Text(planet['name'] as String),
                  );
                }).toList(),
                onChanged: (planetName) {
                  if (planetName != null) {
                    _updateCondition(_currentCondition.copyWith(planet2Name: planetName));
                  }
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),

        // 相位類型選擇
        const Text('相位類型：', style: TextStyle(fontSize: 10)),
        const SizedBox(height: 4),
        _buildAspectTypeSelector(),
      ],
    );
  }

  /// 構建相位類型選擇器
  Widget _buildAspectTypeSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 主要相位快捷選擇
        // _buildMajorAspectButtons(),
        // const SizedBox(height: 8),

        // 個別相位選擇
        _buildIndividualAspectSelector(),
      ],
    );
  }

  /// 構建主要相位快捷按鈕
  Widget _buildMajorAspectButtons() {
    return Row(
      children: [
        const Text('主要相位：', style: TextStyle(fontSize: 10)),
        const SizedBox(width: 4),
        ...[
          '合相',
          '對分相',
          '三分相',
          '四分相',
          '六分相',
        ].map((aspectType) {
          final isSelected = _currentCondition.aspectTypes?.contains(aspectType) ?? false;

          return Padding(
            padding: const EdgeInsets.only(right: 4),
            child: FilterChip(
              label: Text(aspectType, style: const TextStyle(fontSize: 9)),
              selected: isSelected,
              onSelected: (selected) {
                final currentAspects = _currentCondition.aspectTypes ?? [];
                List<String> newAspects;

                if (selected) {
                  newAspects = [...currentAspects, aspectType];
                } else {
                  newAspects = currentAspects.where((a) => a != aspectType).toList();
                }

                _updateCondition(_currentCondition.copyWith(aspectTypes: newAspects));
              },
            ),
          );
        }).toList(),
      ],
    );
  }

  /// 構建個別相位選擇器
  Widget _buildIndividualAspectSelector() {
    final allAspects = AstrologyConstants.ASPECTS.map((aspect) => aspect['name'] as String).toList();

    return Wrap(
      spacing: 4,
      runSpacing: 4,
      children: allAspects.map((aspectType) {
        final isSelected = _currentCondition.aspectTypes?.contains(aspectType) ?? false;

        return FilterChip(
          label: Text(aspectType, style: const TextStyle(fontSize: 9)),
          selected: isSelected,
          onSelected: (selected) {
            final currentAspects = _currentCondition.aspectTypes ?? [];
            List<String> newAspects;

            if (selected) {
              newAspects = [...currentAspects, aspectType];
            } else {
              newAspects = currentAspects.where((a) => a != aspectType).toList();
            }

            _updateCondition(_currentCondition.copyWith(aspectTypes: newAspects));
          },
        );
      }).toList(),
    );
  }
}
