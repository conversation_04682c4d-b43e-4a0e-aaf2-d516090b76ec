import 'package:flutter/material.dart';

import '../../themes/app_theme.dart';


/// 統一風格的加載狀態組件
class LoadingState extends StatelessWidget {
  final String? message;
  final double size;
  final Color? color;

  const LoadingState({
    super.key,
    this.message,
    this.size = 40,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: size,
            height: size,
            child: CircularProgressIndicator(
              color: color ?? AppColors.royalIndigo,
              strokeWidth: 3,
            ),
          ),
          if (message != null) ...[
            const SizedBox(height: 16),
            Text(
              message!,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                color: AppColors.textMedium,
              ),
            ),
          ],
        ],
      ),
    );
  }
}
