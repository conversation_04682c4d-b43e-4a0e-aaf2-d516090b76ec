import 'package:flutter/material.dart';

import '../../themes/app_theme.dart';

/// 統一風格的頁面標題組件
class PageTitle extends StatelessWidget {
  final String title;
  final IconData icon;
  final List<Widget>? actions;
  final Color? iconColor;
  final Color? textColor;

  const PageTitle({
    super.key,
    required this.title,
    required this.icon,
    this.actions,
    this.iconColor,
    this.textColor,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 4.0, vertical: 12.0),
      child: Row(
        children: [
          Icon(
            icon,
            color: iconColor ?? AppColors.royalIndigo,
            size: 28,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              title,
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: textColor ?? AppColors.royalIndigo,
              ),
            ),
          ),
          if (actions != null) ...actions!,
        ],
      ),
    );
  }
}
