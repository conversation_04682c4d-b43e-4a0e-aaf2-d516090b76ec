import 'dart:io';

import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';

import '../../../core/utils/logger_utils.dart';
import '../../pages/settings/log_viewer_page.dart';

/// 日誌預覽組件
/// 顯示最近的日誌條目，可以快速查看應用程式狀態
class LogPreviewWidget extends StatefulWidget {
  final int maxLines;
  final bool showHeader;
  
  const LogPreviewWidget({
    super.key,
    this.maxLines = 10,
    this.showHeader = true,
  });

  @override
  State<LogPreviewWidget> createState() => _LogPreviewWidgetState();
}

class _LogPreviewWidgetState extends State<LogPreviewWidget> {
  List<String> _recentLogs = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadRecentLogs();
  }

  /// 載入最近的日誌
  Future<void> _loadRecentLogs() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final logFile = File('${directory.path}/logs/astreal_app.log');
      
      if (await logFile.exists()) {
        final content = await logFile.readAsString();
        final lines = content.split('\n')
            .where((line) => line.trim().isNotEmpty)
            .toList();
        
        // 取最後 N 行
        final recentLines = lines.length > widget.maxLines
            ? lines.sublist(lines.length - widget.maxLines)
            : lines;
        
        setState(() {
          _recentLogs = recentLines.reversed.toList(); // 最新的在前面
          _isLoading = false;
        });
      } else {
        setState(() {
          _recentLogs = ['日誌文件尚未創建'];
          _isLoading = false;
        });
      }
    } catch (e) {
      logger.e('載入最近日誌失敗: $e');
      setState(() {
        _recentLogs = ['載入日誌失敗: $e'];
        _isLoading = false;
      });
    }
  }

  /// 獲取日誌行的顏色
  Color _getLogLineColor(String line) {
    if (line.contains('[ERROR]')) return Colors.red.shade700;
    if (line.contains('[WARNING]')) return Colors.orange.shade700;
    if (line.contains('[INFO]')) return Colors.blue.shade700;
    if (line.contains('[DEBUG]')) return Colors.grey.shade600;
    return Colors.black87;
  }

  /// 獲取日誌行的圖標
  IconData _getLogLineIcon(String line) {
    if (line.contains('[ERROR]')) return Icons.error_outline;
    if (line.contains('[WARNING]')) return Icons.warning_amber_outlined;
    if (line.contains('[INFO]')) return Icons.info_outline;
    if (line.contains('[DEBUG]')) return Icons.bug_report_outlined;
    return Icons.notes;
  }

  /// 格式化日誌行顯示
  String _formatLogLine(String line) {
    // 移除時間戳記，只保留重要信息
    final regex = RegExp(r'\[(.*?)\] (.*)');
    final match = regex.firstMatch(line);
    
    if (match != null) {
      final level = match.group(1);
      final message = match.group(2);
      return '[$level] $message';
    }
    
    return line;
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (widget.showHeader) _buildHeader(),
          
          if (_isLoading)
            const Padding(
              padding: EdgeInsets.all(16),
              child: Center(child: CircularProgressIndicator()),
            )
          else if (_recentLogs.isEmpty)
            const Padding(
              padding: EdgeInsets.all(16),
              child: Center(
                child: Text(
                  '暫無日誌記錄',
                  style: TextStyle(color: Colors.grey),
                ),
              ),
            )
          else
            _buildLogList(),
          
          _buildFooter(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
      ),
      child: Row(
        children: [
          Icon(Icons.history, color: Theme.of(context).primaryColor),
          const SizedBox(width: 8),
          Text(
            '最近日誌',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const Spacer(),
          IconButton(
            icon: const Icon(Icons.refresh, size: 20),
            onPressed: _loadRecentLogs,
            tooltip: '刷新',
          ),
        ],
      ),
    );
  }

  Widget _buildLogList() {
    return Container(
      constraints: BoxConstraints(
        maxHeight: widget.maxLines * 40.0, // 每行約 40 像素
      ),
      child: ListView.builder(
        shrinkWrap: true,
        itemCount: _recentLogs.length,
        itemBuilder: (context, index) {
          final line = _recentLogs[index];
          final color = _getLogLineColor(line);
          final icon = _getLogLineIcon(line);
          final formattedLine = _formatLogLine(line);
          
          return Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(color: Colors.grey.shade200),
              ),
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Icon(
                  icon,
                  size: 16,
                  color: color,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    formattedLine,
                    style: TextStyle(
                      fontSize: 12,
                      color: color,
                      fontFamily: 'monospace',
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildFooter() {
    return Container(
      padding: const EdgeInsets.all(12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            '顯示最近 ${_recentLogs.length} 條記錄',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade600,
            ),
          ),
          TextButton.icon(
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const LogViewerPage(),
                ),
              );
            },
            icon: const Icon(Icons.open_in_new, size: 16),
            label: const Text('查看全部'),
            style: TextButton.styleFrom(
              textStyle: const TextStyle(fontSize: 12),
            ),
          ),
        ],
      ),
    );
  }
}

/// 日誌狀態指示器
/// 顯示當前日誌狀態的簡單指示器
class LogStatusIndicator extends StatefulWidget {
  const LogStatusIndicator({super.key});

  @override
  State<LogStatusIndicator> createState() => _LogStatusIndicatorState();
}

class _LogStatusIndicatorState extends State<LogStatusIndicator> {
  int _errorCount = 0;
  int _warningCount = 0;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadLogStatus();
  }

  Future<void> _loadLogStatus() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final logFile = File('${directory.path}/logs/astreal_app.log');
      
      if (await logFile.exists()) {
        final content = await logFile.readAsString();
        final lines = content.split('\n');
        
        int errorCount = 0;
        int warningCount = 0;
        
        for (final line in lines) {
          if (line.contains('[ERROR]')) errorCount++;
          if (line.contains('[WARNING]')) warningCount++;
        }
        
        setState(() {
          _errorCount = errorCount;
          _warningCount = warningCount;
          _isLoading = false;
        });
      } else {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const SizedBox(
        width: 16,
        height: 16,
        child: CircularProgressIndicator(strokeWidth: 2),
      );
    }

    Color indicatorColor = Colors.green;
    IconData indicatorIcon = Icons.check_circle;
    String tooltip = '系統運行正常';

    if (_errorCount > 0) {
      indicatorColor = Colors.red;
      indicatorIcon = Icons.error;
      tooltip = '發現 $_errorCount 個錯誤';
    } else if (_warningCount > 0) {
      indicatorColor = Colors.orange;
      indicatorIcon = Icons.warning;
      tooltip = '發現 $_warningCount 個警告';
    }

    return Tooltip(
      message: tooltip,
      child: Icon(
        indicatorIcon,
        color: indicatorColor,
        size: 16,
      ),
    );
  }
}
