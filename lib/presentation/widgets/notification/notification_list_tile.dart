import 'package:flutter/material.dart';

import '../../../astreal.dart';
import '../../../data/models/notification/notification_model.dart';
import '../../../shared/widgets/unified_card.dart';

/// 通知列表項組件
class NotificationListTile extends StatelessWidget {
  final NotificationModel notification;
  final VoidCallback? onTap;
  final VoidCallback? onDelete;

  const NotificationListTile({
    super.key,
    required this.notification,
    this.onTap,
    this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: UnifiedCard(
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              // 未讀通知使用淺色背景和邊框
              color: notification.isRead
                  ? Colors.transparent
                  : AppColors.royalIndigo.withValues(alpha: 0.03),
              border: notification.isRead
                  ? null
                  : Border.all(
                      color: AppColors.royalIndigo.withValues(alpha: 0.1),
                      width: 1,
                    ),
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 通知圖標
                _buildNotificationIcon(),
                
                const SizedBox(width: 12),
                
                // 通知內容
                Expanded(
                  child: _buildNotificationContent(),
                ),
                
                const SizedBox(width: 8),
                
                // 操作按鈕
                _buildActionButtons(context),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 構建通知圖標或圖片
  Widget _buildNotificationIcon() {
    // 如果有自定義圖片，優先顯示圖片
    if (notification.imageUrl != null && notification.imageUrl!.isNotEmpty) {
      return Container(
        width: 48,
        height: 48,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(24),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(24),
          child: Image.network(
            notification.imageUrl!,
            width: 48,
            height: 48,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) {
              // 圖片載入失敗時顯示預設圖標
              return _buildDefaultIcon();
            },
            loadingBuilder: (context, child, loadingProgress) {
              if (loadingProgress == null) return child;
              return Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: notification.type.color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(24),
                ),
                child: Center(
                  child: SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        notification.type.color,
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      );
    }

    // 沒有圖片時顯示預設圖標
    return _buildDefaultIcon();
  }

  /// 構建預設圖標
  Widget _buildDefaultIcon() {
    return Container(
      width: 48,
      height: 48,
      decoration: BoxDecoration(
        color: notification.type.color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(24),
        border: Border.all(
          color: notification.type.color.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Icon(
        notification.type.icon,
        color: notification.type.color,
        size: 24,
      ),
    );
  }

  /// 構建通知內容
  Widget _buildNotificationContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 標題和優先級
        Row(
          children: [
            Expanded(
              child: Text(
                notification.title,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: notification.isRead ? FontWeight.w500 : FontWeight.bold,
                  color: AppColors.textDark,
                  height: 1.3,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),

            // 優先級標記
            if (notification.isHighPriority)
              Container(
                margin: const EdgeInsets.only(left: 8),
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 3),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: notification.isUrgent
                        ? [Colors.red.shade400, Colors.red.shade600]
                        : [Colors.orange.shade400, Colors.orange.shade600],
                  ),
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: (notification.isUrgent ? Colors.red : Colors.orange)
                          .withValues(alpha: 0.3),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Text(
                  notification.priority.displayName,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
          ],
        ),

        const SizedBox(height: 6),

        // 通知內容
        Text(
          notification.body,
          style: TextStyle(
            fontSize: 14,
            color: notification.isRead ? Colors.grey[600] : AppColors.textDark,
            height: 1.5,
          ),
          maxLines: 3,
          overflow: TextOverflow.ellipsis,
        ),

        // 如果有圖片且不是在圖標位置顯示，可以在這裡顯示大圖
        if (notification.imageUrl != null &&
            notification.imageUrl!.isNotEmpty &&
            _shouldShowLargeImage())
          _buildLargeImage(),

        const SizedBox(height: 12),

        // 底部資訊
        _buildBottomInfo(),
      ],
    );
  }

  /// 是否應該顯示大圖（某些類型的通知可能需要顯示大圖）
  bool _shouldShowLargeImage() {
    // 促銷活動、功能更新等可能需要顯示大圖
    return notification.type == NotificationType.promotion ||
           notification.type == NotificationType.featureUpdate ||
           notification.type == NotificationType.systemAnnouncement;
  }

  /// 構建大圖顯示
  Widget _buildLargeImage() {
    return Container(
      margin: const EdgeInsets.only(top: 8),
      height: 120,
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: Image.network(
          notification.imageUrl!,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            return Container(
              decoration: BoxDecoration(
                color: Colors.grey[200],
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Center(
                child: Icon(
                  Icons.image_not_supported,
                  color: Colors.grey,
                  size: 32,
                ),
              ),
            );
          },
          loadingBuilder: (context, child, loadingProgress) {
            if (loadingProgress == null) return child;
            return Container(
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Center(
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
            );
          },
        ),
      ),
    );
  }

  /// 構建底部資訊
  Widget _buildBottomInfo() {
    return Row(
      children: [
        // 通知類型標籤
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
          decoration: BoxDecoration(
            color: notification.type.color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: notification.type.color.withValues(alpha: 0.3),
              width: 0.5,
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                notification.type.icon,
                size: 12,
                color: notification.type.color,
              ),
              const SizedBox(width: 4),
              Text(
                notification.type.displayName,
                style: TextStyle(
                  color: notification.type.color,
                  fontSize: 11,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),

        // 如果有動作URL，顯示可點擊標記
        if (notification.actionUrl != null && notification.actionUrl!.isNotEmpty) ...[
          const SizedBox(width: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              color: AppColors.royalIndigo.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.touch_app,
                  size: 10,
                  color: AppColors.royalIndigo,
                ),
                const SizedBox(width: 2),
                Text(
                  '可點擊',
                  style: TextStyle(
                    color: AppColors.royalIndigo,
                    fontSize: 9,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],

        const Spacer(),

        // 時間
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
          decoration: BoxDecoration(
            color: Colors.grey.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(10),
          ),
          child: Text(
            notification.formattedCreatedAt,
            style: TextStyle(
              fontSize: 11,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
        ),

        // 未讀標記
        if (!notification.isRead) ...[
          const SizedBox(width: 8),
          Container(
            width: 10,
            height: 10,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  AppColors.royalIndigo,
                  AppColors.royalIndigo.withValues(alpha: 0.8),
                ],
              ),
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: AppColors.royalIndigo.withValues(alpha: 0.4),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  /// 構建操作按鈕
  Widget _buildActionButtons(BuildContext context) {
    return PopupMenuButton<String>(
      icon: Icon(
        Icons.more_vert,
        color: Colors.grey[600],
        size: 20,
      ),
      onSelected: (value) {
        switch (value) {
          case 'delete':
            _showDeleteConfirmation(context);
            break;
        }
      },
      itemBuilder: (context) => [
        const PopupMenuItem(
          value: 'delete',
          child: Row(
            children: [
              Icon(Icons.delete, color: Colors.red, size: 18),
              SizedBox(width: 8),
              Text('刪除'),
            ],
          ),
        ),
      ],
    );
  }

  /// 顯示刪除確認對話框
  void _showDeleteConfirmation(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('確認刪除'),
        content: const Text('確定要刪除這則通知嗎？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              onDelete?.call();
            },
            child: const Text(
              '刪除',
              style: TextStyle(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }
}

/// 通知摘要卡片（用於首頁等地方顯示）
class NotificationSummaryCard extends StatelessWidget {
  final int unreadCount;
  final List<NotificationModel> recentNotifications;
  final VoidCallback? onTap;

  const NotificationSummaryCard({
    super.key,
    required this.unreadCount,
    required this.recentNotifications,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return UnifiedCard(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 標題
              Row(
                children: [
                  const Icon(
                    Icons.notifications,
                    color: AppColors.royalIndigo,
                    size: 24,
                  ),
                  const SizedBox(width: 8),
                  const Text(
                    '通知中心',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textDark,
                    ),
                  ),
                  const Spacer(),
                  if (unreadCount > 0)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.red,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        unreadCount > 99 ? '99+' : unreadCount.toString(),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                ],
              ),
              
              const SizedBox(height: 12),
              
              // 最近通知預覽
              if (recentNotifications.isEmpty)
                const Text(
                  '暫無通知',
                  style: TextStyle(
                    color: Colors.grey,
                    fontSize: 14,
                  ),
                )
              else
                ...recentNotifications.take(3).map((notification) =>
                  Padding(
                    padding: const EdgeInsets.only(bottom: 8),
                    child: Row(
                      children: [
                        Icon(
                          notification.type.icon,
                          color: notification.type.color,
                          size: 16,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            notification.title,
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: notification.isRead 
                                  ? FontWeight.normal 
                                  : FontWeight.w500,
                              color: notification.isRead 
                                  ? Colors.grey[600] 
                                  : AppColors.textDark,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        Text(
                          notification.formattedCreatedAt,
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[500],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              
              // 查看更多
              if (recentNotifications.length > 3)
                Padding(
                  padding: const EdgeInsets.only(top: 8),
                  child: Text(
                    '還有 ${recentNotifications.length - 3} 則通知...',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[500],
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
