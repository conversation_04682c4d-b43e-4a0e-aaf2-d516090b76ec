import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import '../../../astreal.dart';
import '../../../data/services/notification/notification_service.dart';

/// 通知設定組件
/// 提供通知權限檢查和設定功能
class NotificationSettingsWidget extends StatefulWidget {
  final VoidCallback? onPermissionChanged;
  final bool showTitle;
  final EdgeInsetsGeometry? padding;

  const NotificationSettingsWidget({
    super.key,
    this.onPermissionChanged,
    this.showTitle = true,
    this.padding,
  });

  @override
  State<NotificationSettingsWidget> createState() => _NotificationSettingsWidgetState();
}

class _NotificationSettingsWidgetState extends State<NotificationSettingsWidget> {
  bool _isLoading = false;
  bool _notificationsEnabled = false;

  @override
  void initState() {
    super.initState();
    _checkNotificationStatus();
  }

  /// 檢查通知狀態
  Future<void> _checkNotificationStatus() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final enabled = await NotificationService.areNotificationsEnabled();
      setState(() {
        _notificationsEnabled = enabled;
      });
    } catch (e) {
      logger.e('檢查通知狀態失敗: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 打開通知設定
  Future<void> _openNotificationSettings() async {
    if (!NotificationService.canOpenNotificationSettings) {
      _showUnsupportedPlatformDialog();
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final success = await NotificationService.openNotificationSettings();
      
      if (mounted) {
        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('已引導您前往通知設定'),
              backgroundColor: Colors.green,
            ),
          );
          
          // 延遲檢查狀態，給用戶時間修改設定
          Future.delayed(const Duration(seconds: 2), () {
            if (mounted) {
              _checkNotificationStatus();
              widget.onPermissionChanged?.call();
            }
          });
        } else {
          _showManualSettingsDialog();
        }
      }
    } catch (e) {
      logger.e('打開通知設定失敗: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('打開設定失敗，請稍後再試'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 顯示不支援平台對話框
  void _showUnsupportedPlatformDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('不支援的平台'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              kIsWeb 
                  ? '網頁版本不支援自動打開系統設定。'
                  : '當前平台不支援自動打開系統設定。',
            ),
            const SizedBox(height: 12),
            const Text('請手動進行以下操作：'),
            const SizedBox(height: 8),
            if (kIsWeb) ...[
              const Text('• 點擊瀏覽器地址欄左側的鎖頭圖標'),
              const Text('• 選擇「通知」設定'),
              const Text('• 將通知權限設為「允許」'),
            ] else ...[
              const Text('• 前往系統設定'),
              const Text('• 找到「通知」或「應用程式通知」'),
              const Text('• 找到 AstReal 應用程式'),
              const Text('• 開啟通知權限'),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('我知道了'),
          ),
        ],
      ),
    );
  }

  /// 顯示手動設定對話框
  void _showManualSettingsDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('手動開啟通知'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('無法自動打開設定頁面，請手動開啟：'),
            const SizedBox(height: 12),
            if (kIsWeb) ...[
              const Text('🌐 網頁版設定：'),
              const Text('1. 點擊瀏覽器地址欄的通知圖標'),
              const Text('2. 選擇「允許」通知權限'),
            ] else if (defaultTargetPlatform == TargetPlatform.android) ...[
              const Text('📱 Android 設定：'),
              const Text('1. 前往「設定」→「應用程式」'),
              const Text('2. 找到「AstReal」'),
              const Text('3. 點擊「通知」'),
              const Text('4. 開啟「允許通知」'),
            ] else if (defaultTargetPlatform == TargetPlatform.iOS) ...[
              const Text('📱 iOS 設定：'),
              const Text('1. 前往「設定」→「通知」'),
              const Text('2. 找到「AstReal」'),
              const Text('3. 開啟「允許通知」'),
            ] else ...[
              const Text('請前往系統設定中的通知選項，'),
              const Text('找到 AstReal 並開啟通知權限。'),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              // 延遲檢查狀態
              Future.delayed(const Duration(seconds: 1), () {
                if (mounted) {
                  _checkNotificationStatus();
                  widget.onPermissionChanged?.call();
                }
              });
            },
            child: const Text('我已設定完成'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('稍後設定'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: widget.padding ?? const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (widget.showTitle) ...[
            const Text(
              '通知設定',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppColors.textDark,
              ),
            ),
            const SizedBox(height: 16),
          ],
          
          // 通知狀態卡片
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: _notificationsEnabled 
                  ? Colors.green.withValues(alpha: 0.1)
                  : Colors.orange.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: _notificationsEnabled 
                    ? Colors.green.withValues(alpha: 0.3)
                    : Colors.orange.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Icon(
                  _notificationsEnabled 
                      ? Icons.notifications_active
                      : Icons.notifications_off,
                  color: _notificationsEnabled ? Colors.green : Colors.orange,
                  size: 32,
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _notificationsEnabled ? '通知已開啟' : '通知已關閉',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: _notificationsEnabled ? Colors.green : Colors.orange,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _notificationsEnabled 
                            ? '您將收到重要的通知訊息'
                            : '開啟通知以接收重要訊息',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 16),
          
          // 操作按鈕
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _isLoading ? null : _checkNotificationStatus,
                  icon: _isLoading 
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Icon(Icons.refresh),
                  label: const Text('重新檢查'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey[100],
                    foregroundColor: Colors.grey[700],
                    elevation: 0,
                  ),
                ),
              ),
              
              const SizedBox(width: 12),
              
              // 只在支援的平台上顯示設定按鈕
              if (NotificationService.canOpenNotificationSettings)
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isLoading ? null : _openNotificationSettings,
                    icon: const Icon(Icons.settings),
                    label: const Text('打開設定'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.royalIndigo,
                      foregroundColor: Colors.white,
                    ),
                  ),
                )
              else
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _showUnsupportedPlatformDialog,
                    icon: const Icon(Icons.help_outline),
                    label: const Text('設定說明'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }
}
