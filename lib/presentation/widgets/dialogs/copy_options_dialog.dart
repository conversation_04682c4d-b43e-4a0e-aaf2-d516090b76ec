import 'package:flutter/material.dart';

import '../../../astreal.dart';

class CopyOptionsDialog extends StatefulWidget {
  final CopyOptions initialOptions;

  const CopyOptionsDialog({
    Key? key,
    this.initialOptions = const CopyOptions(),
  }) : super(key: key);

  @override
  _CopyOptionsDialogState createState() => _CopyOptionsDialogState();
}

class _CopyOptionsDialogState extends State<CopyOptionsDialog> {
  late CopyOptions _options;
  bool _selectAll = true;

  @override
  void initState() {
    super.initState();
    _options = widget.initialOptions;
    _updateSelectAllState();
  }

  void _updateSelectAllState() {
    _selectAll = _options.includeBasicInfo &&
        _options.includePlanetPositions &&
        _options.includePlanetDignities &&
        _options.includePlanetSectStatus &&
        _options.includeHouseRulers &&
        _options.includeHousePositions &&
        _options.includeAspects &&
        _options.includeReceptions &&
        _options.includeElementStats &&
        _options.includeArabicPoints;
  }

  void _setSelectAll(bool value) {
    setState(() {
      _selectAll = value;
      _options = CopyOptions(
        includeBasicInfo: value,
        includePlanetPositions: value,
        includePlanetDignities: value,
        includePlanetSectStatus: value,
        includeHouseRulers: value,
        includeHousePositions: value,
        includeAspects: value,
        includeReceptions: value,
        includeElementStats: value,
        includeArabicPoints: value,
        usePrettyFormat: _options.usePrettyFormat,
      );
    });
  }

  Widget _buildCheckboxTile(
      String title, String subtitle, bool value, ValueChanged<bool> onChanged,
      {IconData? icon, String? tooltip}) {
    return CheckboxListTile(
      secondary: icon != null
          ? Tooltip(message: tooltip ?? '', child: Icon(icon))
          : null,
      title: Text(title),
      subtitle: Text(subtitle),
      value: value,
      onChanged: (val) {
        if (val != null) {
          setState(() {
            onChanged(val);
            _updateSelectAllState();
          });
        }
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('選擇要複製的信息'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CheckboxListTile(
              title: const Text('全選',
                  style: TextStyle(fontWeight: FontWeight.bold)),
              value: _selectAll,
              onChanged: (value) => value != null ? _setSelectAll(value) : null,
            ),
            // const Divider(),
            ExpansionTile(
              title: const Text('基本資訊'),
              children: [
                _buildCheckboxTile(
                    '基本資料',
                    '姓名、出生日期、地點等',
                    _options.includeBasicInfo,
                    (val) =>
                        _options = _options.copyWith(includeBasicInfo: val),
                    icon: Icons.person,
                    tooltip: '包含使用者基本出生資料'),
              ],
            ),
            ExpansionTile(
              title: const Text('行星資訊'),
              children: [
                _buildCheckboxTile(
                    '行星位置',
                    '星座與宮位',
                    _options.includePlanetPositions,
                    (val) => _options =
                        _options.copyWith(includePlanetPositions: val),
                    icon: Icons.language),
                _buildCheckboxTile(
                    '行星尊貴力量',
                    '廟旺落陷',
                    _options.includePlanetDignities,
                    (val) => _options =
                        _options.copyWith(includePlanetDignities: val),
                    icon: Icons.trending_up),
                _buildCheckboxTile(
                    '行星日夜區分',
                    '日夜盤狀態',
                    _options.includePlanetSectStatus,
                    (val) => _options =
                        _options.copyWith(includePlanetSectStatus: val),
                    icon: Icons.wb_sunny,
                    tooltip: '區分日夜盤中行星的優勢與劣勢'),
              ],
            ),
            ExpansionTile(
              title: const Text('宮位與相位'),
              children: [
                _buildCheckboxTile(
                    '宮主星',
                    '象限制與整宮制',
                    _options.includeHouseRulers,
                    (val) =>
                        _options = _options.copyWith(includeHouseRulers: val),
                    icon: Icons.home),
                _buildCheckboxTile(
                    '宮位位置',
                    '宮位對應星座',
                    _options.includeHousePositions,
                    (val) => _options =
                        _options.copyWith(includeHousePositions: val),
                    icon: Icons.grid_view),
                _buildCheckboxTile('相位', '行星之間的相位關係', _options.includeAspects,
                    (val) => _options = _options.copyWith(includeAspects: val),
                    icon: Icons.share),
                _buildCheckboxTile(
                    '互容接納',
                    '行星互容與接納',
                    _options.includeReceptions,
                    (val) =>
                        _options = _options.copyWith(includeReceptions: val),
                    icon: Icons.sync_alt),
              ],
            ),
            ExpansionTile(
              title: const Text('統計與特殊點'),
              children: [
                _buildCheckboxTile(
                    '元素統計',
                    '四元素、三分性、陰陽',
                    _options.includeElementStats,
                    (val) =>
                        _options = _options.copyWith(includeElementStats: val),
                    icon: Icons.bar_chart),
                _buildCheckboxTile(
                    '特殊點',
                    '阿拉伯點與虛點',
                    _options.includeArabicPoints,
                    (val) =>
                        _options = _options.copyWith(includeArabicPoints: val),
                    icon: Icons.gps_fixed),
              ],
            ),
            // const Divider(),
            CheckboxListTile(
              title: const Text('美化格式'),
              subtitle: const Text('使用縮排與分隔線格式化輸出'),
              value: _options.usePrettyFormat,
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _options = _options.copyWith(usePrettyFormat: value);
                  });
                }
              },
            ),
          ],
        ),
      ),
      actions: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('取消'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(_options),
              style: ElevatedButton.styleFrom(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 24, vertical: 12)),
              child: const Text('複製'),
            ),
          ],
        )
      ],
    );
  }
}

Future<CopyOptions?> showCopyOptionsDialog(BuildContext context,
    {CopyOptions initialOptions = const CopyOptions()}) async {
  return showDialog<CopyOptions>(
    context: context,
    builder: (context) => CopyOptionsDialog(initialOptions: initialOptions),
  );
}
