import 'package:flutter/material.dart';

import '../../../astreal.dart';

/// 占星諮詢分析對話框
class ConsultationAnalysisDialog extends StatefulWidget {
  const ConsultationAnalysisDialog({super.key});

  @override
  State<ConsultationAnalysisDialog> createState() => _ConsultationAnalysisDialogState();
}

class _ConsultationAnalysisDialogState extends State<ConsultationAnalysisDialog> {
  String? _selectedTheme;

  // 定義占星諮詢分析主題
  final Map<String, ConsultationAnalysisTheme> _themes = {
    'personality_talent': const ConsultationAnalysisTheme(
      id: 'personality_talent',
      title: '性格天賦分析',
      subtitle: '基礎結構分析',
      category: '基礎結構分析',
      number: '01',
      description: '請根據以下本命盤資訊，分析當事人的性格傾向與天賦架構',
      icon: Icons.psychology,
      color: Colors.purple,
      analysisPoints: [
        '三大要素（太陽/月亮/上升）的組合意義',
        '元素與三分性統計、行星集中與分布',
        '命主星落點與主題影響',
        '個人行星（金星、水星、火星）的展現方式與表達傾向',
      ],
    ),
    'emotional_pattern': const ConsultationAnalysisTheme(
      id: 'emotional_pattern',
      title: '情感模式分析',
      subtitle: '情感與關係主題',
      category: '情感與關係主題',
      number: '02',
      description: '請分析命盤中與親密關係有關的特徵，包括',
      icon: Icons.favorite,
      color: Colors.pink,
      analysisPoints: [
        '金星與火星落點、相位與特質',
        '月亮與 IC 的情感需求',
        '第五、第七宮主星與宮內行星',
        '土星、凱龍星、南北交點對關係的影響',
      ],
    ),
    'synastry_interaction': const ConsultationAnalysisTheme(
      id: 'synastry_interaction',
      title: '合盤互動分析',
      subtitle: '情感與關係主題',
      category: '情感與關係主題',
      number: '03',
      description: '請根據兩人的本命盤進行基本合盤分析',
      icon: Icons.people,
      color: Colors.red,
      analysisPoints: [
        '太陽、月亮、金星、火星之間的相位與吸引力',
        '內行星是否落入對方第五、第七、第一宮',
        '合盤中的相合、刑剋、對分、相衝位置說明',
      ],
    ),
    'career_direction': const ConsultationAnalysisTheme(
      id: 'career_direction',
      title: '職涯方向與天賦分析',
      subtitle: '職涯與發展主題',
      category: '職涯與發展主題',
      number: '04',
      description: '請根據本命盤，分析個人的職涯傾向與潛在優勢',
      icon: Icons.work,
      color: Colors.blue,
      analysisPoints: [
        '中天（MC）與第十宮主星、行星落點',
        '太陽、火星、水星與天頂的關係',
        '行星集中區域、工作與貢獻主題',
        '適合的工作風格與發展節奏',
      ],
    ),
    'financial_tendency': const ConsultationAnalysisTheme(
      id: 'financial_tendency',
      title: '財務傾向分析',
      subtitle: '職涯與發展主題',
      category: '職涯與發展主題',
      number: '05',
      description: '請分析命主的金錢觀與財務傾向',
      icon: Icons.monetization_on,
      color: Colors.green,
      analysisPoints: [
        '第二宮與第八宮主星、宮內行星與相位',
        '金星與木星的展現方式',
        '有無固定星座強勢（處女、金牛、天蠍等）',
        '財富來源模式與潛在理財風格',
      ],
    ),
    'progression_transit': const ConsultationAnalysisTheme(
      id: 'progression_transit',
      title: '推運與流年建議',
      subtitle: '流年與推運主題',
      category: '流年與推運主題',
      number: '06',
      description: '請分析此盤運勢',
      icon: Icons.timeline,
      color: Colors.orange,
      analysisPoints: [
        // '流年土星、天王、海王、冥王與本命行星的相位',
        // '木星移動與關鍵宮位活化',
        // '若可，加入次限月亮移動與新月觸發',
        // '請整理為「主題 + 時間 + 建議」格式',
      ],
    ),
    'relationship_turning': const ConsultationAnalysisTheme(
      id: 'relationship_turning',
      title: '流年關係轉折點',
      subtitle: '流年與推運主題',
      category: '流年與推運主題',
      number: '07',
      description: '請找出未來一年中關於親密關係或伴侶緣分的可能觸發時間',
      icon: Icons.favorite_border,
      color: Colors.deepOrange,
      analysisPoints: [
        '流年金星、木星與本命金星/第五/第七宮的互動',
        '推運太陽、金星、月亮與合盤的觸發相位',
      ],
    ),
    'self_exploration': const ConsultationAnalysisTheme(
      id: 'self_exploration',
      title: '命主自我探索對話',
      subtitle: '客製化應用指令',
      category: '客製化應用指令',
      number: '08',
      description: '請扮演一位占星師，與使用者展開引導對話，根據命盤提供下列內容',
      icon: Icons.chat,
      color: Colors.indigo,
      analysisPoints: [
        '正在經歷的人生主題',
        '內在衝突與突破方向',
        '一到兩個可以採取的行動建議',
        '語氣請使用鼓勵式、啟發式口吻，幫助對方釐清想法',
      ],
    ),
    'annual_calendar': const ConsultationAnalysisTheme(
      id: 'annual_calendar',
      title: '建立個人年度星象日曆',
      subtitle: '客製化應用指令',
      category: '客製化應用指令',
      number: '09',
      description: '請依據本命盤與流年互動，列出當事人未來一年重要星象日曆',
      icon: Icons.calendar_today,
      color: Colors.teal,
      analysisPoints: [
        '觸發金錢、感情、工作、情緒、療癒等主題之日期',
        '每筆資料包含：日期、主題、觸發原因、建議行動',
      ],
    ),
  };

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppColors.royalIndigo.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.analytics,
              color: AppColors.royalIndigo,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          const Expanded(
            child: Text(
              '占星諮詢分析',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
      content: SizedBox(
        width: double.maxFinite,
        height: MediaQuery.of(context).size.height * 0.7,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              '請選擇您要進行的占星諮詢分析主題，系統將自動複製相關的星盤資訊與分析文案：',
              style: TextStyle(fontSize: 14, color: Colors.grey),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: _buildThemesByCategory(),
                ),
              ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('取消'),
        ),
        ElevatedButton(
          onPressed: _selectedTheme != null
              ? () {
                  final selectedTheme = _themes[_selectedTheme!]!;
                  Navigator.of(context).pop({
                    'theme': _selectedTheme,
                    'themeInfo': selectedTheme,
                  });
                }
              : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.royalIndigo,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          ),
          child: const Text('複製分析資訊'),
        ),
      ],
    );
  }

  List<Widget> _buildThemesByCategory() {
    final categories = <String, List<MapEntry<String, ConsultationAnalysisTheme>>>{};
    
    // 按類別分組
    for (final entry in _themes.entries) {
      final category = entry.value.category;
      categories.putIfAbsent(category, () => []).add(entry);
    }

    final widgets = <Widget>[];
    
    for (final categoryEntry in categories.entries) {
      // 添加類別標題
      widgets.add(
        Padding(
          padding: const EdgeInsets.only(top: 16, bottom: 8),
          child: Align(
            alignment: Alignment.centerLeft,
            child: Text(
              categoryEntry.key,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppColors.royalIndigo,
              ),
            ),
          ),
        ),
      );

      // 添加該類別下的主題
      for (final themeEntry in categoryEntry.value) {
        widgets.add(_buildThemeCard(themeEntry.key, themeEntry.value));
      }
    }

    return widgets;
  }

  Widget _buildThemeCard(String themeKey, ConsultationAnalysisTheme theme) {
    final isSelected = _selectedTheme == themeKey;

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: () {
            setState(() {
              _selectedTheme = themeKey;
            });
          },
          child: Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: isSelected
                    ? theme.color
                    : Colors.grey.withValues(alpha: 0.3),
                width: isSelected ? 2 : 1,
              ),
              color: isSelected
                  ? theme.color.withValues(alpha: 0.1)
                  : Colors.transparent,
            ),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        theme.title,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: isSelected
                              ? theme.color
                              : Colors.black87,
                        ),
                      ),
                      Text(
                        theme.subtitle,
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                if (isSelected)
                  Icon(
                    Icons.check_circle,
                    color: theme.color,
                    size: 20,
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// 顯示占星諮詢分析對話框
Future<Map<String, dynamic>?> showConsultationAnalysisDialog(BuildContext context) async {
  return showDialog<Map<String, dynamic>>(
    context: context,
    builder: (context) => const ConsultationAnalysisDialog(),
  );
}
