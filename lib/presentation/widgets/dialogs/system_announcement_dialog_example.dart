import 'package:flutter/material.dart';

import '../../../astreal.dart';
import '../../../data/models/admin/system_announcement.dart';
import 'system_announcement_dialog.dart';

/// 系統公告對話框使用示例
class SystemAnnouncementDialogExample extends StatelessWidget {
  const SystemAnnouncementDialogExample({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('系統公告對話框示例'),
        backgroundColor: AppColors.royalIndigo,
        foregroundColor: Colors.white,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ElevatedButton(
              onPressed: () => _showSingleAnnouncement(context),
              child: const Text('顯示單個公告'),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => _showMultipleAnnouncements(context),
              child: const Text('顯示多個公告'),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => _showAnnouncementWithImage(context),
              child: const Text('顯示帶圖片的公告'),
            ),
          ],
        ),
      ),
    );
  }

  /// 顯示單個公告
  void _showSingleAnnouncement(BuildContext context) {
    final announcement = SystemAnnouncementWithId(
      id: 'test_1',
      announcement: SystemAnnouncement(
        id: 'test_1',
        title: '系統維護通知',
        content: '親愛的用戶，我們將於今晚 23:00 - 01:00 進行系統維護，期間可能會影響部分功能的使用。維護完成後，系統將更加穩定，感謝您的理解與支持。',
        type: AnnouncementType.maintenance,
        priority: AnnouncementPriority.high,
        isActive: true,
        isSticky: false,
        targetUserTypes: ['all'],
        actionUrl: 'https://astreal.app/maintenance',
        actionText: '了解詳情',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        createdBy: 'system',
        updatedBy: 'system',
      ),
    );

    SystemAnnouncementDialog.show(context, [announcement]);
  }

  /// 顯示多個公告
  void _showMultipleAnnouncements(BuildContext context) {
    final announcements = [
      SystemAnnouncementWithId(
        id: 'test_1',
        announcement: SystemAnnouncement(
          id: 'test_1',
          title: '新功能發布',
          content: '我們很高興地宣布，星真占星應用新增了推播通知功能！現在您可以及時收到重要的占星資訊、系統公告和個人化提醒。',
          type: AnnouncementType.feature,
          priority: AnnouncementPriority.normal,
          isActive: true,
          isSticky: true,
          targetUserTypes: ['all'],
          actionUrl: 'https://astreal.app/features',
          actionText: '探索新功能',
          createdAt: DateTime.now().subtract(const Duration(hours: 2)),
          updatedAt: DateTime.now().subtract(const Duration(hours: 2)),
          createdBy: 'admin',
          updatedBy: 'admin',
        ),
      ),
      SystemAnnouncementWithId(
        id: 'test_2',
        announcement: SystemAnnouncement(
          id: 'test_2',
          title: '限時優惠活動',
          content: '慶祝推播通知功能上線，我們推出限時優惠活動！所有解讀服務享受 8 折優惠，活動期間至本月底。',
          type: AnnouncementType.promotion,
          priority: AnnouncementPriority.high,
          isActive: true,
          isSticky: false,
          targetUserTypes: ['all'],
          actionUrl: 'https://astreal.app/promotion',
          actionText: '立即參與',
          createdAt: DateTime.now().subtract(const Duration(hours: 1)),
          updatedAt: DateTime.now().subtract(const Duration(hours: 1)),
          createdBy: 'admin',
          updatedBy: 'admin',
        ),
      ),
      SystemAnnouncementWithId(
        id: 'test_3',
        announcement: SystemAnnouncement(
          id: 'test_3',
          title: '重要占星事件提醒',
          content: '本週將迎來重要的占星事件 - 滿月發生在天蠍座。這是一個進行深度占星分析和自我反思的絕佳時機。建議您查看個人星盤，了解這次滿月對您的影響。',
          type: AnnouncementType.feature,
          priority: AnnouncementPriority.normal,
          isActive: true,
          isSticky: false,
          targetUserTypes: ['all'],
          createdAt: DateTime.now().subtract(const Duration(minutes: 30)),
          updatedAt: DateTime.now().subtract(const Duration(minutes: 30)),
          createdBy: 'system',
          updatedBy: 'system',
        ),
      ),
    ];

    SystemAnnouncementDialog.show(context, announcements);
  }

  /// 顯示帶圖片的公告
  void _showAnnouncementWithImage(BuildContext context) {
    final announcement = SystemAnnouncementWithId(
      id: 'test_image',
      announcement: SystemAnnouncement(
        id: 'test_image',
        title: '星真占星全新界面',
        content: '我們為您帶來了全新設計的用戶界面！更加簡潔美觀的設計，更流暢的操作體驗，讓您的占星之旅更加愉快。',
        type: AnnouncementType.feature,
        priority: AnnouncementPriority.normal,
        isActive: true,
        isSticky: false,
        targetUserTypes: ['all'],
        imageUrl: 'https://picsum.photos/400/200?random=1',
        actionUrl: 'https://astreal.app/ui-update',
        actionText: '查看更新',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        createdBy: 'admin',
        updatedBy: 'admin',
      ),
    );

    SystemAnnouncementDialog.show(context, [announcement]);
  }
}
