import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import '../../../presentation/themes/app_theme.dart';
import '../../data/services/api/payment_service.dart';
import '../../data/services/api/unified_payment_service.dart';

class PaymentPage extends StatefulWidget {
  final String? selectedPlanId;
  final VoidCallback? onPaymentSuccess;

  const PaymentPage({
    super.key,
    this.selectedPlanId,
    this.onPaymentSuccess,
  });

  @override
  State<PaymentPage> createState() => _PaymentPageState();
}

class _PaymentPageState extends State<PaymentPage> {
  String? _selectedPlanId;
  bool _isProcessing = false;
  Map<String, dynamic>? _subscriptionSummary;
  List<Map<String, dynamic>> _availableProducts = [];
  bool _isLoadingProducts = true;

  @override
  void initState() {
    super.initState();
    _selectedPlanId = widget.selectedPlanId;
    _initializePaymentServices();
  }

  /// 初始化支付服務
  Future<void> _initializePaymentServices() async {
    try {
      // 初始化統一支付服務
      await UnifiedPaymentService.initialize();

      // 載入訂閱摘要
      await _loadSubscriptionSummary();

      // 載入可用商品
      await _loadAvailableProducts();
    } catch (e) {
      if (kDebugMode) {
        print('初始化支付服務失敗: $e');
      }
    }
  }

  /// 載入可用商品
  Future<void> _loadAvailableProducts() async {
    try {
      final products = await UnifiedPaymentService.getAvailableProducts();
      setState(() {
        _availableProducts = products;
        _isLoadingProducts = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingProducts = false;
      });
      if (kDebugMode) {
        print('載入可用商品失敗: $e');
      }
    }
  }

  Future<void> _loadSubscriptionSummary() async {
    final summary = await PaymentService.getSubscriptionSummary();
    setState(() {
      _subscriptionSummary = summary;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('訂閱方案'),
        backgroundColor: AppColors.royalIndigo,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 當前狀態卡片
            _buildCurrentStatusCard(),
            const SizedBox(height: 24),

            // 方案選擇
            _buildPlanSelection(),
            const SizedBox(height: 24),

            // 支付按鈕
            if (_selectedPlanId != null) _buildPaymentButton(),
            const SizedBox(height: 16),

            // 免費試用說明
            _buildFreeTrialInfo(),
            const SizedBox(height: 16),

            // 功能說明
            _buildFeatureDescription(),
          ],
        ),
      ),
    );
  }

  Widget _buildCurrentStatusCard() {
    if (_subscriptionSummary == null) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Center(child: CircularProgressIndicator()),
        ),
      );
    }

    final isPremium = _subscriptionSummary!['isPremium'] as bool;
    final remainingTrials = _subscriptionSummary!['remainingTrials'] as int;
    final remainingSinglePurchases = _subscriptionSummary!['remainingSinglePurchases'] as int;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  isPremium ? Icons.star : Icons.free_breakfast,
                  color:
                      isPremium ? AppColors.solarAmber : AppColors.indigoLight,
                ),
                const SizedBox(width: 8),
                Text(
                  isPremium ? '付費會員' : '免費用戶',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            if (isPremium) ...[
              const Text('您目前是付費會員，可以無限次使用解讀功能。'),
            ] else ...[
              if (remainingSinglePurchases > 0) ...[
                Row(
                  children: [
                    const Icon(Icons.payment, color: AppColors.solarAmber, size: 16),
                    const SizedBox(width: 4),
                    Text('單次購買剩餘：$remainingSinglePurchases 次'),
                  ],
                ),
                const SizedBox(height: 4),
              ],
              Row(
                children: [
                  const Icon(Icons.free_breakfast, color: AppColors.indigoLight, size: 16),
                  const SizedBox(width: 4),
                  Text('免費試用剩餘：$remainingTrials 次'),
                ],
              ),
              const SizedBox(height: 8),
              const Text(
                '可購買單次解讀或升級為付費會員享受無限次解讀服務。',
                style: TextStyle(color: Colors.grey),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildPlanSelection() {
    final plans = PaymentService.getPaymentPlans();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '選擇訂閱方案',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        ...plans.map((plan) => _buildPlanCard(plan)),
      ],
    );
  }

  Widget _buildPlanCard(Map<String, dynamic> plan) {
    final isSelected = _selectedPlanId == plan['id'];
    final features = plan['features'] as List<String>;

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () {
          setState(() {
            _selectedPlanId = plan['id'];
          });
        },
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: isSelected ? AppColors.royalIndigo : Colors.transparent,
              width: 2,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Radio<String>(
                    value: plan['id'],
                    groupValue: _selectedPlanId,
                    onChanged: (value) {
                      setState(() {
                        _selectedPlanId = value;
                      });
                    },
                    activeColor: AppColors.royalIndigo,
                  ),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          plan['name'],
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          plan['description'],
                          style: const TextStyle(color: Colors.grey),
                        ),
                      ],
                    ),
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        'NT\$ ${plan['price'].toInt()}',
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: AppColors.royalIndigo,
                        ),
                      ),
                      Text(
                        plan['type'] == 'single'
                            ? '/ 單次'
                            : '/ ${plan['duration']} 個月',
                        style: const TextStyle(color: Colors.grey),
                      ),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Wrap(
                spacing: 8,
                runSpacing: 4,
                children: features
                    .map((feature) => Chip(
                          label: Text(
                            feature,
                            style: const TextStyle(fontSize: 12),
                          ),
                          backgroundColor:
                              AppColors.indigoLight.withOpacity(0.1),
                          side: BorderSide.none,
                        ))
                    .toList(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPaymentButton() {
    final plans = PaymentService.getPaymentPlans();
    final selectedPlan =
        plans.firstWhere((plan) => plan['id'] == _selectedPlanId);

    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _isProcessing ? null : () => _processPayment(selectedPlan),
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.royalIndigo,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: _isProcessing
            ? const Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  ),
                  SizedBox(width: 12),
                  Text('處理中...'),
                ],
              )
            : Text(
                selectedPlan['type'] == 'single'
                    ? '立即購買 - NT\$ ${selectedPlan['price'].toInt()}'
                    : '立即訂閱 - NT\$ ${selectedPlan['price'].toInt()}',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
      ),
    );
  }

  Widget _buildFreeTrialInfo() {
    return Card(
      color: AppColors.indigoLight.withOpacity(0.1),
      child: const Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info_outline, color: AppColors.indigoLight),
                SizedBox(width: 8),
                Text(
                  '免費試用說明',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: AppColors.indigoLight,
                  ),
                ),
              ],
            ),
            SizedBox(height: 8),
            Text('• 每位用戶可免費試用解讀功能 3 次'),
            Text('• 試用次數用完後需要訂閱才能繼續使用'),
            Text('• 訂閱後可享受無限次解讀服務'),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureDescription() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '解讀功能包含',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            _buildFeatureItem('專業星盤分析', '基於傳統占星學的深度解讀'),
            _buildFeatureItem('個人化建議', '針對您的星盤配置提供專屬建議'),
            _buildFeatureItem('多種解讀類型', '本命盤、行運盤、小限法等多種分析'),
            _buildFeatureItem('即時解答', '隨時可用的占星師'),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureItem(String title, String description) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Icon(
            Icons.check_circle,
            color: AppColors.royalIndigo,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                Text(
                  description,
                  style: const TextStyle(
                    color: Colors.grey,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _processPayment(Map<String, dynamic> plan) async {
    setState(() {
      _isProcessing = true;
    });

    try {
      bool success = false;

      if (kDebugMode) {
        // 開發模式：使用模擬支付
        final paymentRecord = await PaymentService.simulatePayment(
          planType: plan['id'],
          amount: plan['price'].toDouble(),
          durationMonths: plan['duration'],
        );
        success = paymentRecord != null;
      } else {
        // 生產模式：使用實際支付
        success = await UnifiedPaymentService.purchaseProduct(plan['id']);
      }

      if (success) {
        // 支付成功
        if (mounted) {
          final successMessage = plan['type'] == 'single'
              ? '購買成功！您獲得了 1 次解讀機會。'
              : '訂閱成功！您現在可以無限次使用解讀功能。';

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(successMessage),
              backgroundColor: Colors.green,
            ),
          );

          // 調用成功回調
          widget.onPaymentSuccess?.call();

          // 返回上一頁
          Navigator.pop(context, true);
        }
      } else {
        // 支付失敗
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('訂閱失敗，請稍後再試。'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('訂閱過程中發生錯誤：$e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }
}
