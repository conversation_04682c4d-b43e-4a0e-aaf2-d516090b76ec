import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../astreal.dart';
import '../../shared/widgets/recent_persons_section.dart';
import '../viewmodels/files_viewmodel.dart';
import '../viewmodels/recent_persons_viewmodel.dart';
import 'birth_data_form_page.dart';
import 'sort_selector_page.dart';

/// 人物選擇器頁面 - 使用 showModalBottomSheet
class PersonSelectorPage extends StatelessWidget {
  final List<BirthData> birthDataList;
  final String title;
  final Color buttonColor;
  final List<String>? excludeIds; // 要排除的人物ID列表

  const PersonSelectorPage({
    super.key,
    required this.birthDataList,
    required this.title,
    required this.buttonColor,
    this.excludeIds,
  });

  @override
  Widget build(BuildContext context) {
    // 這個類主要用於向後兼容，實際使用 show() 靜態方法
    return Scaffold(
      appBar: AppBar(title: Text(title)),
      body: const Center(
        child: Text('請使用 PersonSelectorPage.show() 方法'),
      ),
    );
  }

  /// 顯示人物選擇器的靜態方法
  static Future<BirthData?> show({
    required BuildContext context,
    required List<BirthData> birthDataList,
    required String title,
    required Color buttonColor,
    List<String>? excludeIds,
  }) {
    return showModalBottomSheet<BirthData>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      barrierColor: Colors.transparent,
      builder: (context) => PersonSelectorBottomSheet(
        birthDataList: birthDataList,
        title: title,
        buttonColor: buttonColor,
        excludeIds: excludeIds,
      ),
    );
  }
}

/// 人物選擇器底部彈出組件
class PersonSelectorBottomSheet extends StatefulWidget {
  final List<BirthData> birthDataList;
  final String title;
  final Color buttonColor;
  final List<String>? excludeIds;

  const PersonSelectorBottomSheet({
    super.key,
    required this.birthDataList,
    required this.title,
    required this.buttonColor,
    this.excludeIds,
  });

  @override
  State<PersonSelectorBottomSheet> createState() => _PersonSelectorBottomSheetState();
}

class _PersonSelectorBottomSheetState extends State<PersonSelectorBottomSheet> {
  // 排序方式 - 使用統一的 SortOption
  SortOption _currentSortOption = SortOption.nameAsc;

  // 搜尋關鍵字
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();

  // 過濾後的人物列表
  List<BirthData> _filteredList = [];

  @override
  void initState() {
    super.initState();
    _loadSortSettings();
    _initializeFilteredList();
  }

  /// 載入排序設置
  Future<void> _loadSortSettings() async {
    final prefs = await SharedPreferences.getInstance();
    final sortOptionIndex =
        prefs.getInt('personSelectionSortOption') ?? SortOption.nameAsc.index;

    setState(() {
      _currentSortOption = SortOption.values[sortOptionIndex];
    });

    _sortAndFilterList();
  }

  /// 初始化過濾列表
  void _initializeFilteredList() {
    _filteredList = widget.excludeIds != null
        ? widget.birthDataList
            .where((person) => !widget.excludeIds!.contains(person.id))
            .toList()
        : List.from(widget.birthDataList);
    _sortAndFilterList();
  }

  /// 排序和過濾列表
  void _sortAndFilterList() {
    // 先過濾
    _filteredList = widget.excludeIds != null
        ? widget.birthDataList
            .where((person) => !widget.excludeIds!.contains(person.id))
            .toList()
        : List.from(widget.birthDataList);

    // 再根據搜尋關鍵字過濾
    if (_searchQuery.isNotEmpty) {
      _filteredList = _filteredList.where((person) {
        return person.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
            person.birthPlace
                .toLowerCase()
                .contains(_searchQuery.toLowerCase()) ||
            person.notes?.toLowerCase().contains(_searchQuery.toLowerCase()) == true ||
            _formatDateTime(person.dateTime).contains(_searchQuery);
      }).toList();
    }

    // 最後排序
    _sortList();
  }

  /// 排序函數 - 使用統一的排序邏輯
  void _sortList() {
    switch (_currentSortOption) {
      case SortOption.nameAsc:
        _filteredList.sort((a, b) => a.name.compareTo(b.name));
        break;
      case SortOption.nameDesc:
        _filteredList.sort((a, b) => b.name.compareTo(a.name));
        break;
      case SortOption.dateNewest:
        _filteredList.sort((a, b) => b.dateTime.compareTo(a.dateTime));
        break;
      case SortOption.dateOldest:
        _filteredList.sort((a, b) => a.dateTime.compareTo(b.dateTime));
        break;
      case SortOption.createdNewest:
        _filteredList.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        break;
      case SortOption.createdOldest:
        _filteredList.sort((a, b) => a.createdAt.compareTo(b.createdAt));
        break;
    }
  }

  /// 更新排序設置
  Future<void> _updateSortOption(SortOption newOption) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt('personSelectionSortOption', newOption.index);

    setState(() {
      _currentSortOption = newOption;
      _sortAndFilterList();
    });
  }

  /// 顯示排序選項選擇器 - 使用統一的 SortSelectorPage
  void _showSortOptionsDialog() async {
    final result = await SortSelectorPage.show(
      context: context,
      currentSortOption: _currentSortOption,
      currentCategoryFilter: Set.from(ChartCategory.values), // 預設顯示所有類別
    );

    if (result != null && result.sortOption != null) {
      await _updateSortOption(result.sortOption!);
    }
  }

  /// 更新搜尋關鍵字
  void _updateSearchQuery(String query) {
    setState(() {
      _searchQuery = query;
      _sortAndFilterList();
    });
  }

  /// 格式化日期時間
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
        '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        children: [
          // 頂部拖拽指示器和標題
          _buildHeader(),

          // 搜尋欄和排序按鈕
          _buildSearchAndSortBar(),

          // 搜尋結果計數
          _buildResultsCount(),

          // 近期選中人物區域
          _buildRecentPersonsSection(),

          // 人物列表
          _buildPersonList(),
        ],
      ),
    );
  }

  /// 構建頂部標題區域
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // 拖拽指示器
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey.shade300,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 16),
          // 標題和關閉按鈕
          Row(
            children: [
              Expanded(
                child: Text(
                  widget.title,
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textDark,
                  ),
                ),
              ),
              IconButton(
                onPressed: () => Navigator.of(context).pop(),
                icon: const Icon(Icons.close),
                style: IconButton.styleFrom(
                  foregroundColor: AppColors.textMedium,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 構建搜尋欄和排序按鈕組合 - 排序按鈕在搜尋框右邊
  Widget _buildSearchAndSortBar() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
      child: Row(
        children: [
          // 搜尋框
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                border: Border.all(color: Colors.grey.shade200),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: '搜尋姓名、地點、備註、標籤...',
                  hintStyle: const TextStyle(color: AppColors.textMedium),
                  prefixIcon: Icon(Icons.search, color: widget.buttonColor),
                  suffixIcon: _searchQuery.isNotEmpty
                      ? IconButton(
                          icon: Icon(Icons.clear, color: widget.buttonColor),
                          onPressed: () {
                            _searchController.clear();
                            _updateSearchQuery('');
                          },
                        )
                      : null,
                  border: InputBorder.none,
                  contentPadding:
                      const EdgeInsets.symmetric(vertical: 14, horizontal: 16),
                ),
                onChanged: _updateSearchQuery,
              ),
            ),
          ),
          const SizedBox(width: 12),
          // 排序按鈕
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: IconButton(
              icon: Icon(Icons.sort, color: widget.buttonColor),
              tooltip: '排序',
              onPressed: _showSortOptionsDialog,
            ),
          ),
        ],
      ),
    );
  }

  /// 構建搜尋結果計數
  Widget _buildResultsCount() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 4),
      child: Align(
        alignment: Alignment.centerLeft,
        child: Text(
          '共 ${_filteredList.length} 個結果',
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey.shade600,
          ),
        ),
      ),
    );
  }

  /// 構建近期選中人物區域
  Widget _buildRecentPersonsSection() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
      child: RecentPersonsSection(
        allPersons: widget.birthDataList,
        onPersonSelected: _selectPerson,
        maxCount: 5,
        showClearButton: true,
        hideWhenSearching: true,
        searchText: _searchQuery,
        title: '近期選中',
        icon: Icons.history,
        themeColor: widget.buttonColor,
        showSelectedState: false,
      ),
    );
  }

  /// 構建人物列表
  Widget _buildPersonList() {
    if (_filteredList.isEmpty) {
      return Expanded(child: _buildEmptyState());
    }

    return Expanded(
      child: ListView.builder(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
        itemCount: _filteredList.length,
        itemBuilder: (context, index) {
          final person = _filteredList[index];
          return _buildPersonCard(person, index);
        },
      ),
    );
  }

  /// 構建空狀態
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.person_search,
            size: 64,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            _searchQuery.isNotEmpty ? '沒有找到符合條件的人物' : '沒有可用的人物資料',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey.shade600,
            ),
          ),
          if (_searchQuery.isNotEmpty) ...[
            const SizedBox(height: 8),
            Text(
              '嘗試調整搜尋條件',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade500,
              ),
            ),
          ] else ...[
            const SizedBox(height: 8),
            Text(
              '點擊下方按鈕新增第一筆出生資料',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade500,
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _navigateToAddBirthData,
              icon: const Icon(Icons.add),
              label: const Text('新增出生資料'),
              style: ElevatedButton.styleFrom(
                backgroundColor: widget.buttonColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// 構建人物卡片
  Widget _buildPersonCard(BirthData person, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: InkWell(
        onTap: () => _selectPerson(person),
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey.shade200),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              // 頭像
              CircleAvatar(
                radius: 24,
                backgroundColor: widget.buttonColor.withValues(alpha: 0.2),
                child: Text(
                  person.name.isNotEmpty ? person.name[0] : '?',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: widget.buttonColor,
                  ),
                ),
              ),
              const SizedBox(width: 16),
              // 人物信息
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      person.name,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textDark,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      person.birthPlace,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey.shade600,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Row(
                      children: [
                        Text(
                          _formatDateTime(person.dateTime),
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey.shade500,
                          ),
                        ),
                        const SizedBox(width: 8),
                        _buildCategoryChip(person.category),
                      ],
                    ),
                  ],
                ),
              ),
              // 選擇指示器
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: Colors.grey.shade400,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 構建類別標籤
  Widget _buildCategoryChip(ChartCategory category) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: category.color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: category.color.withValues(alpha: 0.3),
          width: 0.5,
        ),
      ),
      child: Text(
        category.displayName,
        style: TextStyle(
          fontSize: 10,
          fontWeight: FontWeight.w500,
          color: category.color,
        ),
      ),
    );
  }

  /// 選擇人物
  void _selectPerson(BirthData person) async {
    // 記錄到近期選中
    try {
      final recentPersonsViewModel = Provider.of<RecentPersonsViewModel>(
        context,
        listen: false,
      );
      await recentPersonsViewModel.recordSelectedPerson(person);
    } catch (e) {
      // 如果記錄失敗，不影響選擇功能
      debugPrint('記錄近期選中人物失敗: $e');
    }

    // 返回選中的人物
    if (mounted) {
      Navigator.of(context).pop(person);
    }
  }

  /// 導航到新增出生資料頁面
  Future<void> _navigateToAddBirthData() async {
    if (!mounted) return;

    try {
      // 先關閉當前的 bottom sheet
      Navigator.of(context).pop();

      // 導航到新增出生資料頁面
      final result = await Navigator.push<BirthData>(
        context,
        MaterialPageRoute(
          builder: (context) => const BirthDataFormPage(),
        ),
      );

      // 如果用戶新增了資料，返回新增的資料
      if (result != null && mounted) {
        // 返回新增的出生資料
        if (mounted) {
          Navigator.of(context).pop(result);
        }
      }
    } catch (e) {
      // 錯誤處理
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('新增出生資料時出錯：$e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
