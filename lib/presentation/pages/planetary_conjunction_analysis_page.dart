import 'package:flutter/material.dart';

import '../../../data/models/astrology/chart_data.dart';
import '../../../data/models/astrology/chart_type.dart';
import '../../../data/models/user/birth_data.dart';
import '../../../presentation/themes/app_theme.dart';
import '../../data/services/api/astrology_service.dart';
import '../../data/services/api/conjunction_cache_service.dart';
import '../../data/services/api/planetary_conjunction_service.dart';
import 'ai_interpretation_result_page.dart';

/// 行星會合分析頁面（使用 AIInterpretationResultPage）
class PlanetaryConjunctionAnalysisPage extends StatefulWidget {
  final BirthData person;
  final DateTime? startDate;
  final DateTime? endDate;

  const PlanetaryConjunctionAnalysisPage({
    super.key,
    required this.person,
    this.startDate,
    this.endDate,
  });

  @override
  State<PlanetaryConjunctionAnalysisPage> createState() => _PlanetaryConjunctionAnalysisPageState();
}

class _PlanetaryConjunctionAnalysisPageState extends State<PlanetaryConjunctionAnalysisPage> {
  bool _isLoading = true;
  String? _errorMessage;
  ChartData? _chartData;

  @override
  void initState() {
    super.initState();
    _initializeAnalysis();
  }

  /// 初始化分析
  Future<void> _initializeAnalysis() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      // 創建當前時間的星盤數據
      await _createChartData();

      // 執行會合計算（用於緩存）
      await _performConjunctionCalculation();

      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = '初始化分析失敗: $e';
          _isLoading = false;
        });
      }
    }
  }

  /// 創建星盤數據
  Future<void> _createChartData() async {
    final astrologyService = AstrologyService();
    await astrologyService.initialize();

    final now = DateTime.now();
    
    // 使用當前時間創建星盤，但保持原有的出生地點
    final currentPerson = BirthData(
      id: widget.person.id,
      name: widget.person.name,
      dateTime: now,
      latitude: widget.person.latitude,
      longitude: widget.person.longitude,
      birthPlace: widget.person.birthPlace,
    );

    // 創建星盤數據結構
    final chartData = ChartData(
      chartType: ChartType.natal,
      primaryPerson: currentPerson,
      specificDate: now,
    );

    // 使用 AstrologyService 計算星盤數據
    final calculatedChartData = await astrologyService.calculateChartData(
      chartData,
      latitude: currentPerson.latitude,
      longitude: currentPerson.longitude,
    );

    _chartData = calculatedChartData;
  }

  /// 執行會合計算
  Future<void> _performConjunctionCalculation() async {
    final now = DateTime.now();
    final effectiveStartDate = widget.startDate ?? now.subtract(const Duration(days: 1825)); // 5年前
    final effectiveEndDate = widget.endDate ?? now.add(const Duration(days: 1825)); // 5年後

    // 執行會合計算（這會自動使用緩存或創建新的計算結果）
    await PlanetaryConjunctionService.calculateConjunctions(
      latitude: widget.person.latitude,
      longitude: widget.person.longitude,
      startDate: effectiveStartDate,
      endDate: effectiveEndDate,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('選擇會合分析類型'),
        backgroundColor: AppColors.royalIndigo,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _showCacheManagement,
            icon: const Icon(Icons.storage),
            tooltip: '緩存管理',
          ),
        ],
      ),
      body: _isLoading
          ? _buildLoadingWidget()
          : _errorMessage != null
              ? _buildErrorWidget()
              : _buildAnalysisOptions(),
    );
  }

  /// 構建載入中顯示
  Widget _buildLoadingWidget() {
    return const Center(
      child: Padding(
        padding: EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(AppColors.royalIndigo),
            ),
            SizedBox(height: 16),
            Text(
              '正在初始化行星會合分析',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppColors.royalIndigo,
              ),
            ),
            SizedBox(height: 8),
            Text(
              '正在準備星盤數據和會合計算...',
              style: TextStyle(fontSize: 12, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// 構建錯誤顯示
  Widget _buildErrorWidget() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage!,
              style: const TextStyle(fontSize: 16),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _initializeAnalysis,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.royalIndigo,
                foregroundColor: Colors.white,
              ),
              child: const Text('重新初始化'),
            ),
          ],
        ),
      ),
    );
  }

  /// 構建分析選項
  Widget _buildAnalysisOptions() {
    if (_chartData == null) {
      return const Center(child: Text('星盤數據未準備好'));
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 分析位置信息
          _buildLocationCard(),
          const SizedBox(height: 16),

          // 會合分析說明
          _buildExplanationCard(),
          const SizedBox(height: 16),

          // 分析選項
          _buildAnalysisOptionCard(
            title: '木土會合投資分析',
            subtitle: '20年經濟週期的長期投資策略',
            icon: Icons.public,
            color: AppColors.royalIndigo,
            // interpretationType: InterpretationType.jupiterSaturnConjunction,
            questions: [
              '根據我的星盤，木土會合週期對我的財務有什麼影響？',
              '在20年經濟週期中，我應該如何進行長期投資規劃？',
              '木土會合期間，哪些投資產品最適合我？',
              '如何利用木土會合週期進行財富積累？',
              '我需要避免哪些長期投資風險？',
            ],
          ),
          const SizedBox(height: 16),

          _buildAnalysisOptionCard(
            title: '火土會合投資分析',
            subtitle: '2年調整週期的投資組合優化',
            icon: Icons.flash_on,
            color: AppColors.solarAmber,
            // interpretationType: InterpretationType.marsSaturnConjunction,
            questions: [
              '根據我的星盤，火土會合週期如何影響我的投資決策？',
              '在2年調整週期中，我應該如何平衡風險和收益？',
              '火土會合期間，適合進行哪些投資調整？',
              '如何在火土會合週期中控制投資風險？',
              '什麼時候是最佳的獲利了結時機？',
            ],
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  /// 構建位置卡片
  Widget _buildLocationCard() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.royalIndigo.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Icon(
                Icons.location_on,
                color: AppColors.royalIndigo,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.person.name,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: AppColors.royalIndigo,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '分析位置：${widget.person.birthPlace}',
                    style: const TextStyle(fontSize: 12, color: Colors.grey),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 構建會合分析說明卡片
  Widget _buildExplanationCard() {
    return Card(
      elevation: 2,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              AppColors.royalIndigo.withValues(alpha: 0.05),
              AppColors.solarAmber.withValues(alpha: 0.05),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: AppColors.royalIndigo,
                  size: 20,
                ),
                SizedBox(width: 8),
                Text(
                  '行星會合投資分析',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppColors.royalIndigo,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            const Text(
              '請選擇您想要分析的會合類型：',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            _buildExplanationRow(
              icon: Icons.public,
              color: AppColors.royalIndigo,
              title: '木土會合',
              description: '約20年週期，適合長期投資規劃和財富積累策略',
            ),
            const SizedBox(height: 8),
            _buildExplanationRow(
              icon: Icons.flash_on,
              color: AppColors.solarAmber,
              title: '火土會合',
              description: '約2年週期，適合投資組合調整和風險控制',
            ),
          ],
        ),
      ),
    );
  }

  /// 構建說明行
  Widget _buildExplanationRow({
    required IconData icon,
    required Color color,
    required String title,
    required String description,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.all(6),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Icon(icon, color: color, size: 16),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: TextStyle(
                  fontSize: 13,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                description,
                style: const TextStyle(
                  fontSize: 11,
                  color: Colors.grey,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 構建分析選項卡片
  Widget _buildAnalysisOptionCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    // required InterpretationType interpretationType,
    required List<String> questions,
  }) {
    return Card(
      elevation: 4,
      child: InkWell(
        onTap: () => _navigateToAIAnalysis(
          title: title,
          // interpretationType: interpretationType,
          questions: questions,
        ),
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            gradient: LinearGradient(
              colors: [
                color.withValues(alpha: 0.05),
                color.withValues(alpha: 0.02),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            border: Border.all(
              color: color.withValues(alpha: 0.2),
              width: 1,
            ),
          ),
          child: Column(
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: color.withValues(alpha: 0.15),
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: color.withValues(alpha: 0.2),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Icon(icon, color: color, size: 32),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          title,
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: color,
                          ),
                        ),
                        const SizedBox(height: 6),
                        Text(
                          subtitle,
                          style: const TextStyle(
                            fontSize: 13,
                            color: Colors.grey,
                            height: 1.3,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.08),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.psychology,
                      color: color,
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '點擊開始AI分析',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: color,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Icon(
                      Icons.arrow_forward,
                      color: color,
                      size: 16,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 導航到AI分析
  void _navigateToAIAnalysis({
    required String title,
    // required InterpretationType interpretationType,
    required List<String> questions,
  }) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AIInterpretationResultPage(
          chartData: _chartData!,
          interpretationTitle: title,
          subtitle: '行星會合投資分析',
          suggestedQuestions: questions,
        ),
      ),
    );
  }

  /// 顯示緩存管理
  void _showCacheManagement() async {
    showDialog(
      context: context,
      builder: (context) => _CacheManagementDialog(),
    );
  }
}

/// 緩存管理對話框（簡化版）
class _CacheManagementDialog extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Row(
        children: [
          Icon(Icons.storage, color: AppColors.royalIndigo),
          SizedBox(width: 8),
          Text('緩存管理'),
        ],
      ),
      content: const Text('會合分析結果已自動緩存，避免重複計算。'),
      actions: [
        TextButton(
          onPressed: () async {
            await ConjunctionCacheService.cleanExpiredCache();
            if (context.mounted) {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('過期緩存已清理')),
              );
            }
          },
          child: const Text('清理過期緩存'),
        ),
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('關閉'),
        ),
      ],
    );
  }
}
