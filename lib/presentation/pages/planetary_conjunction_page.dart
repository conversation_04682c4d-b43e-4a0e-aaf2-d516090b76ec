import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../astreal.dart';
import '../../data/services/api/ai_api_service.dart';
import '../../data/services/api/astrology_service.dart';
import '../../data/services/api/chart_interpretation_service.dart';
import '../../data/services/api/conjunction_cache_service.dart';
import '../../data/services/api/planetary_conjunction_service.dart';
import 'ai_interpretation_result_page.dart';


/// 行星會合分析頁面
class PlanetaryConjunctionPage extends StatefulWidget {
  final BirthData person;
  final DateTime? startDate;
  final DateTime? endDate;

  const PlanetaryConjunctionPage({
    super.key,
    required this.person,
    this.startDate,
    this.endDate,
  });

  @override
  State<PlanetaryConjunctionPage> createState() =>
      _PlanetaryConjunctionPageState();
}

class _PlanetaryConjunctionPageState extends State<PlanetaryConjunctionPage> {
  ConjunctionAnalysisResult? _analysisResult;
  bool _isLoading = true;
  String? _errorMessage;
  String _loadingMessage = '正在初始化計算引擎...';

  // 時間範圍設定
  late DateTime _startDate;
  late DateTime _endDate;

  @override
  void initState() {
    super.initState();
    // 使用傳入的時間範圍，或默認時間範圍（當前時間前後各5年）
    final now = DateTime.now();
    _startDate = widget.startDate ?? DateTime(now.year - 5, now.month, now.day);
    _endDate = widget.endDate ?? DateTime(now.year + 5, now.month, now.day);
    _loadConjunctionData();
  }

  /// 載入行星會合數據
  Future<void> _loadConjunctionData() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
        _loadingMessage = '正在初始化計算引擎...';
      });

      // 模擬初始化過程
      await Future.delayed(const Duration(milliseconds: 500));

      if (mounted) {
        setState(() {
          _loadingMessage = '正在計算木土會合...';
        });
      }

      // 模擬計算過程
      await Future.delayed(const Duration(milliseconds: 800));

      if (mounted) {
        setState(() {
          _loadingMessage = '正在計算火土會合...';
        });
      }

      // 模擬計算過程
      await Future.delayed(const Duration(milliseconds: 600));

      if (mounted) {
        setState(() {
          _loadingMessage = '正在分析財務週期...';
        });
      }

      final result = await PlanetaryConjunctionService.calculateConjunctions(
        latitude: widget.person.latitude,
        longitude: widget.person.longitude,
        startDate: _startDate,
        endDate: _endDate,
      );

      if (mounted) {
        setState(() {
          _loadingMessage = '分析完成！';
        });
      }

      // 短暫顯示完成消息
      await Future.delayed(const Duration(milliseconds: 300));

      if (mounted) {
        setState(() {
          _analysisResult = result;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = '載入行星會合數據失敗: $e';
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('行星會合分析'),
        backgroundColor: AppColors.royalIndigo,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _showDateRangeSettings,
            icon: const Icon(Icons.date_range),
            tooltip: '設定時間範圍',
          ),
          IconButton(
            onPressed: _showCacheManagement,
            icon: const Icon(Icons.storage),
            tooltip: '緩存管理',
          ),
        ],
      ),
      body: _isLoading
          ? _buildLoadingWidget()
          : _errorMessage != null
              ? _buildErrorWidget()
              : _buildContent(),
    );
  }

  /// 構建載入中顯示
  Widget _buildLoadingWidget() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 動畫圓圈
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: AppColors.royalIndigo.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(40),
              ),
              child: const Center(
                child: CircularProgressIndicator(
                  valueColor:
                      AlwaysStoppedAnimation<Color>(AppColors.royalIndigo),
                  strokeWidth: 3,
                ),
              ),
            ),
            const SizedBox(height: 24),

            // 載入標題
            const Text(
              '正在計算行星會合',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AppColors.royalIndigo,
              ),
            ),
            const SizedBox(height: 12),

            // 動態載入消息
            AnimatedSwitcher(
              duration: const Duration(milliseconds: 300),
              child: Text(
                _loadingMessage,
                key: ValueKey(_loadingMessage),
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(height: 24),

            // 載入提示
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppColors.solarAmber.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Column(
                children: [
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.info_outline,
                        color: AppColors.solarAmber,
                        size: 16,
                      ),
                      SizedBox(width: 8),
                      Text(
                        '計算說明',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: AppColors.solarAmber,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 8),
                  Text(
                    '正在精確計算\n木土會合（20年週期）和火土會合（2年週期）\n請稍候片刻...',
                    style: TextStyle(
                      fontSize: 12,
                      color: AppColors.solarAmber,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 構建錯誤顯示
  Widget _buildErrorWidget() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage!,
              style: const TextStyle(fontSize: 16),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadConjunctionData,
              child: const Text('重新載入'),
            ),
          ],
        ),
      ),
    );
  }

  /// 構建主要內容
  Widget _buildContent() {
    if (_analysisResult == null) {
      return const Center(child: Text('沒有數據'));
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 分析位置信息
          _buildPersonCard(),
          const SizedBox(height: 16),

          // 時間範圍設定
          _buildDateRangeCard(),
          const SizedBox(height: 16),

          // 分析摘要
          _buildAnalysisSummary(),
          const SizedBox(height: 16),

          // 最近會合
          if (_analysisResult!.nearestConjunction != null) ...[
            _buildNearestConjunction(),
            const SizedBox(height: 16),
          ],

          // 木土會合列表
          _buildConjunctionSection(
            '木土會合 (約20年週期)',
            ConjunctionType.jupiterSaturn,
            AppColors.royalIndigo,
          ),
          const SizedBox(height: 16),

          // 火土會合列表
          _buildConjunctionSection(
            '火土會合 (約2年週期)',
            ConjunctionType.marsSaturn,
            AppColors.solarAmber,
          ),
        ],
      ),
    );
  }

  /// 構建分析位置卡片
  Widget _buildPersonCard() {
    return StyledCard(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.royalIndigo.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Icon(
                Icons.public,
                color: AppColors.royalIndigo,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.person.name,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: AppColors.royalIndigo,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '分析位置：${widget.person.birthPlace}',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                  Text(
                    '經緯度：${widget.person.latitude.toStringAsFixed(2)}°, ${widget.person.longitude.toStringAsFixed(2)}°',
                    style: TextStyle(
                      fontSize: 11,
                      color: Colors.grey[500],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 構建時間範圍卡片
  Widget _buildDateRangeCard() {
    return StyledCard(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.date_range,
                  color: AppColors.solarAmber,
                  size: 20,
                ),
                const SizedBox(width: 8),
                const Text(
                  '分析時間範圍',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppColors.solarAmber,
                  ),
                ),
                const Spacer(),
                TextButton.icon(
                  onPressed: _showDateRangeSettings,
                  icon: const Icon(Icons.edit, size: 16),
                  label: const Text('修改'),
                  style: TextButton.styleFrom(
                    foregroundColor: AppColors.solarAmber,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.grey[50],
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey[300]!),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          '開始時間',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '${_startDate.year}年${_startDate.month}月${_startDate.day}日',
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                const Icon(Icons.arrow_forward, color: Colors.grey),
                const SizedBox(width: 12),
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.grey[50],
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey[300]!),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          '結束時間',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '${_endDate.year}年${_endDate.month}月${_endDate.day}日',
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppColors.royalIndigo.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.info_outline,
                    color: AppColors.royalIndigo,
                    size: 14,
                  ),
                  const SizedBox(width: 6),
                  Expanded(
                    child: Text(
                      '分析範圍：${_endDate.difference(_startDate).inDays}天 (${(_endDate.difference(_startDate).inDays / 365.25).toStringAsFixed(1)}年)',
                      style: const TextStyle(
                        fontSize: 11,
                        color: AppColors.royalIndigo,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 顯示時間範圍設定對話框
  void _showDateRangeSettings() async {
    final result = await showDialog<Map<String, DateTime>>(
      context: context,
      builder: (context) => _DateRangeSettingsDialog(
        initialStartDate: _startDate,
        initialEndDate: _endDate,
      ),
    );

    if (result != null) {
      setState(() {
        _startDate = result['startDate']!;
        _endDate = result['endDate']!;
      });

      // 重新載入數據
      _loadConjunctionData();
    }
  }

  /// 顯示緩存管理對話框
  void _showCacheManagement() async {
    showDialog(
      context: context,
      builder: (context) => _CacheManagementDialog(),
    );
  }

  /// 構建分析摘要
  Widget _buildAnalysisSummary() {
    return StyledCard(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(
                  Icons.analytics,
                  color: AppColors.royalIndigo,
                  size: 20,
                ),
                SizedBox(width: 8),
                Text(
                  '分析摘要',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppColors.royalIndigo,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              _analysisResult!.analysisSummary,
              style: const TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.solarAmber.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Row(
                    children: [
                      Icon(
                        Icons.lightbulb,
                        color: AppColors.solarAmber,
                        size: 16,
                      ),
                      SizedBox(width: 6),
                      Text(
                        '投資建議',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: AppColors.solarAmber,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _analysisResult!.investmentSummary,
                    style: const TextStyle(fontSize: 13),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 構建最近會合
  Widget _buildNearestConjunction() {
    final conjunction = _analysisResult!.nearestConjunction!;

    return StyledCard(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.schedule,
                  color: conjunction.type == ConjunctionType.jupiterSaturn
                      ? AppColors.royalIndigo
                      : AppColors.solarAmber,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  '最近會合',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: conjunction.type == ConjunctionType.jupiterSaturn
                        ? AppColors.royalIndigo
                        : AppColors.solarAmber,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            _buildConjunctionItem(conjunction),
          ],
        ),
      ),
    );
  }

  /// 構建會合區段
  Widget _buildConjunctionSection(
      String title, ConjunctionType type, Color color) {
    final conjunctions = _analysisResult!.getConjunctionsByType(type);

    return GestureDetector(
      onTap: () => _showInvestmentAdviceDialog(type),
      child: StyledCard(
        elevation: 2,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    type == ConjunctionType.jupiterSaturn
                        ? Icons.public
                        : Icons.flash_on,
                    color: color,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      title,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: color,
                      ),
                    ),
                  ),
                  Icon(
                    Icons.info_outline,
                    color: color.withValues(alpha: 0.7),
                    size: 18,
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.touch_app,
                      color: color,
                      size: 14,
                    ),
                    const SizedBox(width: 6),
                    Text(
                      '點擊查看適合的投資方式',
                      style: TextStyle(
                        fontSize: 12,
                        color: color,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 12),
              if (conjunctions.isEmpty)
                const Text('在分析期間內沒有發現此類會合')
              else
                ...conjunctions.map((conjunction) => Padding(
                      padding: const EdgeInsets.only(bottom: 12),
                      child: _buildConjunctionItem(conjunction),
                    )),
            ],
          ),
        ),
      ),
    );
  }

  /// 構建會合項目
  Widget _buildConjunctionItem(PlanetaryConjunction conjunction) {
    final dateFormat = DateFormat('yyyy年MM月dd日 HH:mm');

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: conjunction.type == ConjunctionType.jupiterSaturn
                      ? AppColors.royalIndigo.withValues(alpha: 0.1)
                      : AppColors.solarAmber.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  conjunction.typeName,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: conjunction.type == ConjunctionType.jupiterSaturn
                        ? AppColors.royalIndigo
                        : AppColors.solarAmber,
                  ),
                ),
              ),
              const Spacer(),
              Text(
                conjunction.getTimeDescription(),
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            dateFormat.format(conjunction.dateTime),
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            '位置：${conjunction.zodiacSign} ${conjunction.longitude.toStringAsFixed(1)}°',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            conjunction.financialMeaning,
            style: const TextStyle(fontSize: 13),
          ),
          const SizedBox(height: 6),
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.blue[50],
              borderRadius: BorderRadius.circular(6),
            ),
            child: Text(
              conjunction.investmentAdvice,
              style: const TextStyle(
                fontSize: 12,
                color: Colors.blue,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 顯示投資建議對話框
  void _showInvestmentAdviceDialog(ConjunctionType type) async {
    try {
      // 顯示載入指示器
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => Dialog(
          child: Container(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const CircularProgressIndicator(
                  valueColor:
                      AlwaysStoppedAnimation<Color>(AppColors.royalIndigo),
                ),
                const SizedBox(height: 16),
                Text(
                  '正在分析${type == ConjunctionType.jupiterSaturn ? "木土會合" : "火土會合"}投資建議',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppColors.royalIndigo,
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  '正在結合當前星盤進行分析...',
                  style: TextStyle(fontSize: 12, color: Colors.grey),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      );

      // 創建當前時間的星盤數據
      final chartData = await _createCurrentChartData(type);

      // 關閉載入指示器
      if (mounted) {
        Navigator.of(context).pop();
      }

      // 顯示AI分析對話框
      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => _AIInvestmentAdviceDialog(
            conjunctionType: type,
            conjunctions: _analysisResult!.getConjunctionsByType(type),
            chartData: chartData,
            person: widget.person,
          ),
        );
      }
    } catch (e) {
      // 關閉載入指示器
      if (mounted) {
        Navigator.of(context).pop();
      }

      // 顯示錯誤信息
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('無法進行分析: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// 創建當前時間的星盤數據
  Future<ChartData> _createCurrentChartData(ConjunctionType type) async {
    final astrologyService = AstrologyService();
    await astrologyService.initialize();

    final now = DateTime.now();

    // 使用當前時間創建星盤，但保持原有的出生地點
    final currentPerson = BirthData(
      id: widget.person.id,
      name: widget.person.name,
      dateTime: now,
      latitude: widget.person.latitude,
      longitude: widget.person.longitude,
      birthPlace: widget.person.birthPlace,
    );
    // 創建星盤數據結構
    ChartData chartData = ChartData(
      chartType: ChartType.conjunctionMarsSaturn,
      primaryPerson: currentPerson,
      specificDate: now,
    );
    if (type == ConjunctionType.jupiterSaturn) {
      chartData = ChartData(
        chartType: ChartType.conjunctionJupiterSaturn,
        primaryPerson: currentPerson,
        specificDate: now,
      );
    }

    // 使用 AstrologyService 計算星盤數據
    final calculatedChartData = await astrologyService.calculateChartData(
      chartData,
      latitude: currentPerson.latitude,
      longitude: currentPerson.longitude,
    );

    return calculatedChartData;
  }
}

/// AI投資建議對話框
class _AIInvestmentAdviceDialog extends StatefulWidget {
  final ConjunctionType conjunctionType;
  final List<PlanetaryConjunction> conjunctions;
  final ChartData chartData;
  final BirthData person;

  const _AIInvestmentAdviceDialog({
    required this.conjunctionType,
    required this.conjunctions,
    required this.chartData,
    required this.person,
  });

  @override
  State<_AIInvestmentAdviceDialog> createState() =>
      _AIInvestmentAdviceDialogState();
}

class _AIInvestmentAdviceDialogState extends State<_AIInvestmentAdviceDialog> {
  String? _aiAnalysis;
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _performAIAnalysis();
  }

  /// 執行AI分析
  Future<void> _performAIAnalysis() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      // 創建專門的投資分析提示詞
      final analysisResult = await _getConjunctionInvestmentAnalysis();

      if (mounted) {
        setState(() {
          _aiAnalysis = analysisResult.content;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = '分析失敗: ${e.toString()}';
          _isLoading = false;
        });
      }
    }
  }

  /// 獲取會合投資分析
  Future<AIApiResponse> _getConjunctionInvestmentAnalysis() async {
    final isJupiterSaturn =
        widget.conjunctionType == ConjunctionType.jupiterSaturn;
    final conjunctionName = isJupiterSaturn ? '木土會合' : '火土會合';
    final cycle = isJupiterSaturn ? '20年' : '2年';

    // 獲取最近的會合事件信息
    String conjunctionInfo = '';
    if (widget.conjunctions.isNotEmpty) {
      final recentConjunction = widget.conjunctions.first;
      conjunctionInfo = '''

**當前$conjunctionName狀況：**
- 最近會合時間：${recentConjunction.dateTime.year}年${recentConjunction.dateTime.month}月${recentConjunction.dateTime.day}日
- 會合位置：${recentConjunction.zodiacSign}
- 時間關係：${recentConjunction.getTimeDescription()}
''';
    }

    final prompt = '''
請根據以下星盤數據和$conjunctionName週期，提供專業的投資建議分析：

分析重點：
1. $conjunctionName投資特性
   - $conjunctionName約$cycle發生一次，代表${isJupiterSaturn ? '重要的經濟週期轉換點和長期投資機會' : '行動與謹慎的平衡點，適合投資組合調整'}

2. 具體投資方向
   - 推薦適合的投資產品和資產配置
   - 投資時機的把握和風險控制
   - ${isJupiterSaturn ? '長期投資規劃（20年視野）' : '短期調整策略（2年週期）'}

3. 行動建議
   - 具體的投資行動步驟
   - 最佳的投資時機選擇
   - 需要避免的投資陷阱

$conjunctionInfo

請提供詳細、實用且個人化的投資建議，幫助此人在$conjunctionName週期中做出明智的投資決策。
''';

    return await ChartInterpretationService.getCustomInterpretation(
      chartData: widget.chartData,
      customPrompt: prompt,
    );
  }

  @override
  Widget build(BuildContext context) {
    final isJupiterSaturn =
        widget.conjunctionType == ConjunctionType.jupiterSaturn;
    final color =
        isJupiterSaturn ? AppColors.royalIndigo : AppColors.solarAmber;
    final title = isJupiterSaturn ? '木土會合AI投資分析' : '火土會合AI投資分析';

    return Dialog(
      child: Container(
        constraints: const BoxConstraints(maxWidth: 500, maxHeight: 600),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 標題欄
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    isJupiterSaturn ? Icons.public : Icons.flash_on,
                    color: color,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      title,
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: color,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                    color: color,
                  ),
                ],
              ),
            ),

            // 內容區域
            Expanded(
              child: _isLoading
                  ? _buildLoadingContent(color)
                  : _errorMessage != null
                      ? _buildErrorContent(color)
                      : _buildAIAnalysisContent(color),
            ),
          ],
        ),
      ),
    );
  }

  /// 構建載入中內容
  Widget _buildLoadingContent(Color color) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(color),
            ),
            const SizedBox(height: 16),
            Text(
              'AI正在分析中...',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              '正在結合您的星盤進行個人化投資分析',
              style: TextStyle(fontSize: 12, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// 構建錯誤內容
  Widget _buildErrorContent(Color color) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage!,
              style: const TextStyle(fontSize: 16),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _performAIAnalysis,
              style: ElevatedButton.styleFrom(
                backgroundColor: color,
                foregroundColor: Colors.white,
              ),
              child: const Text('重新分析'),
            ),
          ],
        ),
      ),
    );
  }

  /// 構建AI分析內容
  Widget _buildAIAnalysisContent(Color color) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 個人信息
          _buildPersonInfo(color),
          const SizedBox(height: 16),

          // 會合信息
          if (widget.conjunctions.isNotEmpty) ...[
            _buildConjunctionInfo(color),
            const SizedBox(height: 16),
          ],

          // AI分析結果
          _buildAIAnalysisSection(color),

          const SizedBox(height: 16),

          // 操作按鈕
          _buildActionButtons(color),
        ],
      ),
    );
  }

  /// 構建個人信息
  Widget _buildPersonInfo(Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(Icons.person, color: color, size: 20),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.person.name,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
                Text(
                  '${widget.person.dateTime.year}年${widget.person.dateTime.month}月${widget.person.dateTime.day}日',
                  style: const TextStyle(fontSize: 12, color: Colors.grey),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 構建會合信息
  Widget _buildConjunctionInfo(Color color) {
    final conjunction = widget.conjunctions.first;
    final isJupiterSaturn =
        widget.conjunctionType == ConjunctionType.jupiterSaturn;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: color.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                isJupiterSaturn ? Icons.public : Icons.flash_on,
                color: color,
                size: 18,
              ),
              const SizedBox(width: 8),
              Text(
                '最近${conjunction.typeName}',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            '時間：${conjunction.dateTime.year}年${conjunction.dateTime.month}月${conjunction.dateTime.day}日',
            style: const TextStyle(fontSize: 12),
          ),
          Text(
            '位置：${conjunction.zodiacSign}',
            style: const TextStyle(fontSize: 12),
          ),
          Text(
            '狀態：${conjunction.getTimeDescription()}',
            style: const TextStyle(fontSize: 12),
          ),
        ],
      ),
    );
  }

  /// 構建AI分析區段
  Widget _buildAIAnalysisSection(Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: color.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.psychology, color: color, size: 18),
              const SizedBox(width: 8),
              Text(
                'AI個人化投資分析',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            _aiAnalysis ?? '分析中...',
            style: const TextStyle(fontSize: 13, height: 1.5),
          ),
        ],
      ),
    );
  }

  /// 構建操作按鈕
  Widget _buildActionButtons(Color color) {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton.icon(
            onPressed: _performAIAnalysis,
            icon: const Icon(Icons.refresh),
            label: const Text('重新分析'),
            style: OutlinedButton.styleFrom(
              foregroundColor: color,
              side: BorderSide(color: color),
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: ElevatedButton.icon(
            onPressed: () => _navigateToDetailedAnalysis(),
            icon: const Icon(Icons.analytics),
            label: const Text('詳細分析'),
            style: ElevatedButton.styleFrom(
              backgroundColor: color,
              foregroundColor: Colors.white,
            ),
          ),
        ),
      ],
    );
  }

  /// 導航到詳細分析頁面
  void _navigateToDetailedAnalysis() {
    Navigator.of(context).pop(); // 關閉當前對話框

    // 導航到深入剖析結果頁面
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AIInterpretationResultPage(
          chartData: widget.chartData,
          interpretationTitle:
              '${widget.conjunctionType == ConjunctionType.jupiterSaturn ? "木土會合" : "火土會合"}投資分析',
          subtitle: '分析重點：${widget.conjunctionType == ConjunctionType.jupiterSaturn ? "木土會合" : "火土會合"}投資特性',
          suggestedQuestions: [
            '根據當前${widget.conjunctionType == ConjunctionType.jupiterSaturn ? "木土會合" : "火土會合"}週期，我應該如何調整投資策略？',
            '我的星盤配置適合哪些類型的投資？',
            '在這個會合週期中，我需要注意哪些投資風險？',
            '什麼時候是最佳的投資時機？',
            '我應該如何進行資產配置？',
          ],
        ),
      ),
    );
  }

  /// 構建信息區段
  Widget _buildInfoSection({
    required String title,
    required String content,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: color.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 18),
              const SizedBox(width: 8),
              Text(
                title,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            content,
            style: const TextStyle(fontSize: 13),
          ),
        ],
      ),
    );
  }

  /// 構建投資策略
  Widget _buildInvestmentStrategies(bool isJupiterSaturn, Color color) {
    final strategies = isJupiterSaturn
        ? [
            '制定20年長期投資規劃',
            '關注經濟結構轉型機會',
            '投資基礎建設和新興產業',
            '建立多元化投資組合',
            '重視價值投資原則',
          ]
        : [
            '調整投資組合配置',
            '平衡風險與收益',
            '適時進行獲利了結',
            '加強風險控制措施',
            '保持投資紀律',
          ];

    return _buildListSection(
      title: '投資策略',
      icon: Icons.trending_up,
      items: strategies,
      color: color,
    );
  }

  /// 構建投資產品
  Widget _buildInvestmentProducts(bool isJupiterSaturn, Color color) {
    final products = isJupiterSaturn
        ? [
            '指數型基金 (ETF)',
            '藍籌股和價值股',
            '房地產投資信託 (REITs)',
            '政府債券和公司債',
            '基礎建設相關投資',
          ]
        : [
            '平衡型基金',
            '防禦性股票',
            '短期債券',
            '貨幣市場基金',
            '避險工具和選擇權',
          ];

    return _buildListSection(
      title: '適合的投資產品',
      icon: Icons.account_balance,
      items: products,
      color: color,
    );
  }

  /// 構建風險提醒
  Widget _buildRiskWarnings(bool isJupiterSaturn, Color color) {
    final warnings = isJupiterSaturn
        ? [
            '避免過度槓桿操作',
            '注意經濟週期轉換風險',
            '不要忽視通膨影響',
            '避免追高殺低',
            '保持長期投資視野',
          ]
        : [
            '避免衝動投資決策',
            '注意市場波動風險',
            '控制投資部位大小',
            '避免過度交易',
            '保持資金流動性',
          ];

    return _buildListSection(
      title: '風險提醒',
      icon: Icons.warning,
      items: warnings,
      color: Colors.orange,
    );
  }

  /// 構建列表區段
  Widget _buildListSection({
    required String title,
    required IconData icon,
    required List<String> items,
    required Color color,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, color: color, size: 18),
            const SizedBox(width: 8),
            Text(
              title,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        ...items.map((item) => Padding(
              padding: const EdgeInsets.only(bottom: 6),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    margin: const EdgeInsets.only(top: 6),
                    width: 4,
                    height: 4,
                    decoration: BoxDecoration(
                      color: color,
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      item,
                      style: const TextStyle(fontSize: 13),
                    ),
                  ),
                ],
              ),
            )),
      ],
    );
  }

  /// 構建最近會合
  Widget _buildRecentConjunctions(
      List<PlanetaryConjunction> conjunctions, Color color) {
    final recentConjunctions = conjunctions.take(3).toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.history, color: color, size: 18),
            const SizedBox(width: 8),
            Text(
              '相關會合事件',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        ...recentConjunctions.map((conjunction) => Container(
              margin: const EdgeInsets.only(bottom: 8),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(6),
                border: Border.all(color: color.withValues(alpha: 0.2)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '${conjunction.dateTime.year}年${conjunction.dateTime.month}月${conjunction.dateTime.day}日',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: color,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '位置：${conjunction.zodiacSign}',
                    style: const TextStyle(fontSize: 11),
                  ),
                ],
              ),
            )),
      ],
    );
  }
}

/// 時間範圍設定對話框
class _DateRangeSettingsDialog extends StatefulWidget {
  final DateTime initialStartDate;
  final DateTime initialEndDate;

  const _DateRangeSettingsDialog({
    required this.initialStartDate,
    required this.initialEndDate,
  });

  @override
  State<_DateRangeSettingsDialog> createState() =>
      _DateRangeSettingsDialogState();
}

class _DateRangeSettingsDialogState extends State<_DateRangeSettingsDialog> {
  late DateTime _startDate;
  late DateTime _endDate;

  @override
  void initState() {
    super.initState();
    _startDate = widget.initialStartDate;
    _endDate = widget.initialEndDate;
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '設定分析時間範圍',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppColors.royalIndigo,
              ),
            ),
            const SizedBox(height: 16),

            // 快速選項
            const Text(
              '快速選項',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              children: [
                _buildQuickOption('過去5年', -5, 0),
                _buildQuickOption('未來5年', 0, 5),
                _buildQuickOption('前後5年', -5, 5),
                _buildQuickOption('前後10年', -10, 10),
                _buildQuickOption('前後20年', -20, 20),
              ],
            ),
            const SizedBox(height: 16),

            // 自定義日期
            const Text(
              '自定義日期',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 8),

            // 開始日期
            ListTile(
              leading:
                  const Icon(Icons.calendar_today, color: AppColors.solarAmber),
              title: const Text('開始日期'),
              subtitle: Text(
                  '${_startDate.year}年${_startDate.month}月${_startDate.day}日'),
              onTap: () => _selectDate(true),
              contentPadding: EdgeInsets.zero,
            ),

            // 結束日期
            ListTile(
              leading: const Icon(Icons.event, color: AppColors.solarAmber),
              title: const Text('結束日期'),
              subtitle:
                  Text('${_endDate.year}年${_endDate.month}月${_endDate.day}日'),
              onTap: () => _selectDate(false),
              contentPadding: EdgeInsets.zero,
            ),

            const SizedBox(height: 16),

            // 範圍信息
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.royalIndigo.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.info_outline,
                    color: AppColors.royalIndigo,
                    size: 16,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      '分析範圍：${_endDate.difference(_startDate).inDays}天 (${(_endDate.difference(_startDate).inDays / 365.25).toStringAsFixed(1)}年)',
                      style: const TextStyle(
                        fontSize: 12,
                        color: AppColors.royalIndigo,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // 按鈕
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('取消'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: _startDate.isBefore(_endDate)
                      ? () {
                          Navigator.of(context).pop({
                            'startDate': _startDate,
                            'endDate': _endDate,
                          });
                        }
                      : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.royalIndigo,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('確定'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 構建快速選項按鈕
  Widget _buildQuickOption(String label, int startYears, int endYears) {
    return OutlinedButton(
      onPressed: () {
        final now = DateTime.now();
        setState(() {
          _startDate = DateTime(now.year + startYears, now.month, now.day);
          _endDate = DateTime(now.year + endYears, now.month, now.day);
        });
      },
      style: OutlinedButton.styleFrom(
        foregroundColor: AppColors.solarAmber,
        side: const BorderSide(color: AppColors.solarAmber),
      ),
      child: Text(label, style: const TextStyle(fontSize: 12)),
    );
  }

  /// 選擇日期
  void _selectDate(bool isStartDate) async {
    final initialDate = isStartDate ? _startDate : _endDate;
    final firstDate = DateTime(1900);
    final lastDate = DateTime(2100);

    final selectedDate = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: firstDate,
      lastDate: lastDate,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
                  primary: AppColors.royalIndigo,
                ),
          ),
          child: child!,
        );
      },
    );

    if (selectedDate != null) {
      setState(() {
        if (isStartDate) {
          _startDate = selectedDate;
        } else {
          _endDate = selectedDate;
        }
      });
    }
  }
}

/// 緩存管理對話框
class _CacheManagementDialog extends StatefulWidget {
  @override
  State<_CacheManagementDialog> createState() => _CacheManagementDialogState();
}

class _CacheManagementDialogState extends State<_CacheManagementDialog> {
  ConjunctionCacheStats? _cacheStats;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadCacheStats();
  }

  Future<void> _loadCacheStats() async {
    setState(() => _isLoading = true);
    final stats = await ConjunctionCacheService.getCacheStats();
    if (mounted) {
      setState(() {
        _cacheStats = stats;
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        padding: const EdgeInsets.all(24),
        constraints: const BoxConstraints(maxWidth: 400),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 標題
            const Row(
              children: [
                Icon(Icons.storage, color: AppColors.royalIndigo),
                SizedBox(width: 8),
                Text(
                  '緩存管理',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.royalIndigo,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // 緩存統計
            if (_isLoading)
              const Center(child: CircularProgressIndicator())
            else if (_cacheStats != null) ...[
              _buildCacheStatsCard(),
              const SizedBox(height: 16),
              _buildCacheActions(),
            ],

            const SizedBox(height: 16),

            // 關閉按鈕
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('關閉'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCacheStatsCard() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.royalIndigo.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '緩存統計',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: AppColors.royalIndigo,
            ),
          ),
          const SizedBox(height: 8),
          _buildStatRow('總緩存條目', '${_cacheStats!.totalEntries}'),
          _buildStatRow('有效緩存', '${_cacheStats!.validEntries}'),
          _buildStatRow('過期緩存', '${_cacheStats!.expiredEntries}'),
        ],
      ),
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: const TextStyle(fontSize: 12)),
          Text(
            value,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCacheActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '緩存操作',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: AppColors.royalIndigo,
          ),
        ),
        const SizedBox(height: 8),

        // 清理過期緩存
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: _cleanExpiredCache,
            icon: const Icon(Icons.cleaning_services),
            label: const Text('清理過期緩存'),
            style: OutlinedButton.styleFrom(
              foregroundColor: AppColors.solarAmber,
              side: const BorderSide(color: AppColors.solarAmber),
            ),
          ),
        ),

        const SizedBox(height: 8),

        // 清除所有緩存
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: _clearAllCache,
            icon: const Icon(Icons.delete_sweep),
            label: const Text('清除所有緩存'),
            style: OutlinedButton.styleFrom(
              foregroundColor: Colors.red,
              side: const BorderSide(color: Colors.red),
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _cleanExpiredCache() async {
    try {
      await ConjunctionCacheService.cleanExpiredCache();
      await _loadCacheStats();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('過期緩存清理完成')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('清理失敗: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _clearAllCache() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('確認清除'),
        content: const Text('確定要清除所有緩存嗎？這將刪除所有已保存的分析結果。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('確定'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await ConjunctionCacheService.clearAllCache();
        await _loadCacheStats();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('所有緩存已清除')),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('清除失敗: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }
}
