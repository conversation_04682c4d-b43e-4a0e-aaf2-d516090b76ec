import 'package:flutter/material.dart';

import '../../astreal.dart';
import '../viewmodels/files_viewmodel.dart';
/// 排序和篩選結果
class SortFilterResult {
  final SortOption? sortOption;
  final Set<ChartCategory>? categoryFilter;

  const SortFilterResult({
    this.sortOption,
    this.categoryFilter,
  });
}

/// 排序選擇器頁面 - 由下往上滑動的UI
class SortSelectorPage extends StatefulWidget {
  final SortOption currentSortOption;
  final Set<ChartCategory> currentCategoryFilter;

  const SortSelectorPage({
    super.key,
    required this.currentSortOption,
    required this.currentCategoryFilter,
  });

  @override
  State<SortSelectorPage> createState() => _SortSelectorPageState();

  /// 顯示排序選擇器的靜態方法
  static Future<SortFilterResult?> show({
    required BuildContext context,
    required SortOption currentSortOption,
    required Set<ChartCategory> currentCategoryFilter,
  }) {
    return Navigator.of(context).push(
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            SortSelectorPage(
          currentSortOption: currentSortOption,
          currentCategoryFilter: currentCategoryFilter,
        ),
        transitionDuration: Duration.zero,
        reverseTransitionDuration: const Duration(milliseconds: 300),
        opaque: false,
        barrierDismissible: true,
        barrierColor: Colors.transparent,
      ),
    );
  }
}

class _SortSelectorPageState extends State<SortSelectorPage>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _slideAnimation;
  late Animation<double> _fadeAnimation;

  // 篩選狀態
  late Set<ChartCategory> _selectedCategories;
  bool _hasChanges = false;

  // 當前顯示模式：'sort' 或 'filter'
  String _currentMode = 'sort';

  @override
  void initState() {
    super.initState();
    _selectedCategories = Set.from(widget.currentCategoryFilter);
    _initializeAnimations();
  }

  /// 初始化動畫
  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _slideAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    // 開始動畫
    _animationController.forward();
  }

  /// 關閉選擇器
  void _closeSelector([SortOption? selectedOption]) {
    _animationController.reverse().then((_) {
      if (mounted) {
        SortFilterResult? result;
        if (selectedOption != null || _hasChanges) {
          result = SortFilterResult(
            sortOption: selectedOption,
            categoryFilter: _selectedCategories,
          );
        }
        Navigator.of(context).pop(result);
      }
    });
  }

  /// 應用篩選設定
  void _applyFilter() {
    _hasChanges = true;
    _closeSelector();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black.withValues(alpha: 0.5),
      body: GestureDetector(
        onTap: () => _closeSelector(),
        child: AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            return Stack(
              children: [
                // 背景遮罩
                Opacity(
                  opacity: _fadeAnimation.value,
                  child: Container(
                    color: Colors.black.withValues(alpha: 0.5),
                  ),
                ),
                // 滑動面板
                Align(
                  alignment: Alignment.bottomCenter,
                  child: Transform.translate(
                    offset: Offset(
                      0,
                      MediaQuery.of(context).size.height * _slideAnimation.value,
                    ),
                    child: _buildSelectorPanel(),
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  /// 構建選擇器面板
  Widget _buildSelectorPanel() {
    return Container(
      height: MediaQuery.of(context).size.height * 0.85,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        children: [
          // 頂部拖拽指示器和標題
          _buildHeader(),

          // 模式切換器
          _buildModeSelector(),

          // 內容區域
          Expanded(
            child: AnimatedSwitcher(
              duration: const Duration(milliseconds: 300),
              transitionBuilder: (Widget child, Animation<double> animation) {
                return SlideTransition(
                  position: Tween<Offset>(
                    begin: const Offset(0.3, 0),
                    end: Offset.zero,
                  ).animate(CurvedAnimation(
                    parent: animation,
                    curve: Curves.easeOutCubic,
                  )),
                  child: FadeTransition(
                    opacity: animation,
                    child: child,
                  ),
                );
              },
              child: _currentMode == 'sort'
                ? _buildSortSection()
                : _buildFilterSection(),
            ),
          ),
        ],
      ),
    );
  }

  /// 構建頂部標題區域
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // 拖拽指示器
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey.shade300,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 16),
          // 標題和關閉按鈕
          Row(
            children: [
              const Expanded(
                child: Text(
                  '排序與篩選',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textDark,
                  ),
                ),
              ),
              IconButton(
                onPressed: () => _closeSelector(),
                icon: const Icon(Icons.close),
                style: IconButton.styleFrom(
                  foregroundColor: AppColors.textMedium,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 構建模式切換器
  Widget _buildModeSelector() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildModeButton(
              mode: 'sort',
              icon: Icons.sort,
              title: '排序',
              subtitle: '調整顯示順序',
            ),
          ),
          const SizedBox(width: 4),
          Expanded(
            child: _buildModeButton(
              mode: 'filter',
              icon: Icons.filter_list,
              title: '篩選',
              subtitle: '選擇顯示類型',
            ),
          ),
        ],
      ),
    );
  }

  /// 構建模式按鈕
  Widget _buildModeButton({
    required String mode,
    required IconData icon,
    required String title,
    required String subtitle,
  }) {
    final isSelected = _currentMode == mode;

    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      curve: Curves.easeOutCubic,
      decoration: BoxDecoration(
        color: isSelected ? AppColors.royalIndigo : Colors.transparent,
        borderRadius: BorderRadius.circular(8),
        boxShadow: isSelected ? [
          BoxShadow(
            color: AppColors.royalIndigo.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ] : null,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(8),
          onTap: () {
            setState(() {
              _currentMode = mode;
            });
          },
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            child: Column(
              children: [
                Icon(
                  icon,
                  color: isSelected ? Colors.white : AppColors.textMedium,
                  size: 24,
                ),
                const SizedBox(height: 4),
                Text(
                  title,
                  style: TextStyle(
                    color: isSelected ? Colors.white : AppColors.textDark,
                    fontWeight: isSelected ? FontWeight.bold : FontWeight.w600,
                    fontSize: 14,
                  ),
                ),
                Text(
                  subtitle,
                  style: TextStyle(
                    color: isSelected ? Colors.white.withValues(alpha: 0.8) : AppColors.textMedium,
                    fontSize: 11,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 構建排序區域
  Widget _buildSortSection() {
    return Container(
      key: const ValueKey('sort'),
      child: SingleChildScrollView(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 頂部說明
            Container(
              padding: const EdgeInsets.all(16),
              margin: const EdgeInsets.only(bottom: 20),
              decoration: BoxDecoration(
                color: AppColors.royalIndigo.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: AppColors.royalIndigo.withValues(alpha: 0.2),
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: AppColors.royalIndigo,
                    size: 20,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      '選擇一種排序方式來調整出生資料的顯示順序',
                      style: TextStyle(
                        color: AppColors.royalIndigo,
                        fontSize: 14,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // 姓名分組
            _buildSortGroupTitle('姓名'),
            _buildSortOption(
              title: '升序 (A-Z)',
              icon: Icons.arrow_upward,
              sortOption: SortOption.nameAsc,
            ),
            _buildSortOption(
              title: '降序 (Z-A)',
              icon: Icons.arrow_downward,
              sortOption: SortOption.nameDesc,
            ),

            const SizedBox(height: 20),

            // 出生日期分組
            _buildSortGroupTitle('出生日期'),
            _buildSortOption(
              title: '最新在前',
              icon: Icons.calendar_today,
              sortOption: SortOption.dateNewest,
            ),
            _buildSortOption(
              title: '最舊在前',
              icon: Icons.calendar_today,
              sortOption: SortOption.dateOldest,
            ),

            const SizedBox(height: 20),

            // 建立時間分組
            _buildSortGroupTitle('建立時間'),
            _buildSortOption(
              title: '最新建立在前',
              icon: Icons.access_time,
              sortOption: SortOption.createdNewest,
            ),
            _buildSortOption(
              title: '最舊建立在前',
              icon: Icons.access_time,
              sortOption: SortOption.createdOldest,
            ),
          ],
        ),
      ),
    );
  }

  /// 構建篩選區域
  Widget _buildFilterSection() {
    return Container(
      key: const ValueKey('filter'),
      child: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 頂部說明和統計
                  Container(
                    padding: const EdgeInsets.all(16),
                    margin: const EdgeInsets.only(bottom: 20),
                    decoration: BoxDecoration(
                      color: AppColors.royalIndigo.withValues(alpha: 0.05),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: AppColors.royalIndigo.withValues(alpha: 0.2),
                        width: 1,
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.filter_list,
                              color: AppColors.royalIndigo,
                              size: 20,
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Text(
                                '選擇要顯示的星盤類型',
                                style: TextStyle(
                                  color: AppColors.royalIndigo,
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          '已選擇 ${_selectedCategories.length} / ${ChartCategory.values.length} 個類型',
                          style: TextStyle(
                            color: AppColors.textMedium,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // 個人相關類別
                  _buildCategoryGroup('個人相關', ChartCategory.personalCategories),
                  const SizedBox(height: 20),

                  // 專業相關類別
                  _buildCategoryGroup('專業相關', ChartCategory.professionalCategories),
                  const SizedBox(height: 20),

                  // 特殊類別
                  _buildCategoryGroup('特殊類別', ChartCategory.specialCategories),

                  // 底部間距
                  const SizedBox(height: 20),
                ],
              ),
            ),
          ),

          // 底部按鈕
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              border: Border(
                top: BorderSide(color: Colors.grey.shade200),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 8,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    icon: const Icon(Icons.select_all, size: 18),
                    label: const Text('全選'),
                    onPressed: () {
                      setState(() {
                        _selectedCategories = Set.from(ChartCategory.values);
                      });
                    },
                    style: OutlinedButton.styleFrom(
                      foregroundColor: AppColors.royalIndigo,
                      side: BorderSide(color: AppColors.royalIndigo),
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: OutlinedButton.icon(
                    icon: const Icon(Icons.clear_all, size: 18),
                    label: const Text('清除'),
                    onPressed: () {
                      setState(() {
                        _selectedCategories.clear();
                      });
                    },
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Colors.grey.shade600,
                      side: BorderSide(color: Colors.grey.shade400),
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  flex: 2,
                  child: ElevatedButton.icon(
                    icon: const Icon(Icons.check, size: 18),
                    label: const Text('套用篩選'),
                    onPressed: _applyFilter,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.royalIndigo,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      elevation: 2,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 建立排序分組標題
  Widget _buildSortGroupTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(left: 8.0, bottom: 8.0),
      child: Text(
        title,
        style: const TextStyle(
          fontWeight: FontWeight.bold,
          color: AppColors.royalIndigo,
          fontSize: 16,
        ),
      ),
    );
  }

  /// 建立排序選項
  Widget _buildSortOption({
    required String title,
    required IconData icon,
    required SortOption sortOption,
  }) {
    final isSelected = widget.currentSortOption == sortOption;

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: () => _closeSelector(sortOption),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: isSelected ? AppColors.royalIndigo.withValues(alpha: 0.1) : Colors.transparent,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: isSelected ? AppColors.royalIndigo : Colors.grey.shade300,
                width: isSelected ? 2 : 1,
              ),
            ),
            child: Row(
              children: [
                Icon(
                  icon,
                  color: isSelected ? AppColors.royalIndigo : AppColors.textMedium,
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    title,
                    style: TextStyle(
                      fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                      color: isSelected ? AppColors.royalIndigo : AppColors.textDark,
                      fontSize: 16,
                    ),
                  ),
                ),
                if (isSelected)
                  const Icon(
                    Icons.check_circle,
                    color: AppColors.royalIndigo,
                    size: 20,
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 構建類別組
  Widget _buildCategoryGroup(String title, List<ChartCategory> categories) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            color: AppColors.royalIndigo,
            fontSize: 16,
          ),
        ),
        const SizedBox(height: 8),
        ...categories.map((category) => _buildCategoryOption(category)),
      ],
    );
  }

  /// 構建類別選項
  Widget _buildCategoryOption(ChartCategory category) {
    final isSelected = _selectedCategories.contains(category);

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: () {
            setState(() {
              if (isSelected) {
                _selectedCategories.remove(category);
              } else {
                _selectedCategories.add(category);
              }
            });
          },
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: isSelected ? category.color.withValues(alpha: 0.1) : Colors.transparent,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: isSelected ? category.color : Colors.grey.shade300,
                width: isSelected ? 2 : 1,
              ),
            ),
            child: Row(
              children: [
                Icon(
                  category.icon,
                  color: isSelected ? category.color : AppColors.textMedium,
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    category.displayName,
                    style: TextStyle(
                      fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                      color: isSelected ? category.color : AppColors.textDark,
                      fontSize: 16,
                    ),
                  ),
                ),
                if (isSelected)
                  Icon(
                    Icons.check_circle,
                    color: category.color,
                    size: 20,
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
