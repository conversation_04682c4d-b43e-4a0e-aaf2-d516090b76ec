import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../astreal.dart';
import '../../data/services/api/booking_service.dart';
import '../../shared/utils/utils.dart';
import 'birth_data_form_page.dart';
import 'feedback_page.dart';
class BookingPage extends StatefulWidget {
  const BookingPage({super.key});

  @override
  State<BookingPage> createState() => _BookingPageState();
}

class _BookingPageState extends State<BookingPage> {
  final _formKey = GlobalKey<FormState>();

  // 表單控制器
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _contactMethodController = TextEditingController();
  final TextEditingController _notesController = TextEditingController();

  // 預約資料
  DateTime _selectedDate = DateTime.now().add(const Duration(days: 1));
  TimeOfDay _selectedTime = const TimeOfDay(hour: 14, minute: 0);
  final String _selectedConsultationType = ConsultationType.natal;

  // 可用時段
  List<TimeOfDay> _availableTimeSlots = [];
  List<TimeOfDay> _bookedTimeSlots = [];

  // 載入狀態
  bool _isLoading = false;
  bool _isSaving = false;

  // 星盤資料
  BirthData? _selectedBirthData;
  List<BirthData> _birthDataList = [];

  @override
  void initState() {
    super.initState();
    _loadUserInfo();
    _loadBirthDataList();
    _updateAvailableTimeSlots();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _contactMethodController.dispose();
    _phoneController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  // 載入用戶資訊
  Future<void> _loadUserInfo() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      setState(() {
        _nameController.text = prefs.getString('user_name') ?? '';
        _emailController.text = prefs.getString('user_email') ?? '';
        _contactMethodController.text = prefs.getString('user_contact_method') ?? '';
        _phoneController.text = prefs.getString('user_phone') ?? '';
      });
    } catch (e) {
      logger.e('載入用戶資訊時出錯: $e');
    }
  }

  // 儲存用戶資訊
  Future<void> _saveUserInfo() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('user_name', _nameController.text);
      await prefs.setString('user_email', _emailController.text);
      await prefs.setString('user_contact_method', _contactMethodController.text);
      await prefs.setString('user_phone', _phoneController.text);
    } catch (e) {
      logger.e('儲存用戶資訊時出錯: $e');
    }
  }

  // 載入星盤資料列表
  Future<void> _loadBirthDataList() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final String? birthDataJson = prefs.getString('birthDataList');

      if (birthDataJson != null) {
        final List<dynamic> decodedData = jsonDecode(birthDataJson);
        setState(() {
          _birthDataList = decodedData.map((item) => BirthData.fromJson(item)).toList();

          // 如果有已儲存的選擇的星盤資料 ID，則載入它
          final String? selectedBirthDataId = prefs.getString('selectedBirthDataId');
          if (selectedBirthDataId != null && _birthDataList.isNotEmpty) {
            // 先檢查列表中是否有匹配的 ID
            final int index = _birthDataList.indexWhere((data) => data.id == selectedBirthDataId);
            if (index != -1) {
              _selectedBirthData = _birthDataList[index];
            } else {
              // 如果沒有匹配的 ID，則選擇第一個
              _selectedBirthData = _birthDataList.first;
            }
          } else if (_birthDataList.isNotEmpty) {
            // 如果沒有已儲存的選擇，但有星盤資料，則選擇第一個
            _selectedBirthData = _birthDataList.first;
          }
        });
      }
    } catch (e) {
      logger.e('載入星盤資料時出錯: $e');
    }
  }

  // 更新可用時段
  Future<void> _updateAvailableTimeSlots() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // 獲取已預約的時段
      _bookedTimeSlots = await BookingService.getBookedTimeSlots(_selectedDate);

      // 生成所有可能的時段（上午 9 點到下午 5 點，每小時一個時段）
      final allTimeSlots = List.generate(9, (index) {
        final hour = index + 9; // 9 AM to 5 PM
        return TimeOfDay(hour: hour, minute: 0);
      });

      // 過濾出可用時段
      _availableTimeSlots = allTimeSlots.where((timeSlot) {
        // 檢查是否已被預約
        final isBooked = _bookedTimeSlots.any((bookedSlot) =>
          bookedSlot.hour == timeSlot.hour &&
          bookedSlot.minute == timeSlot.minute
        );

        // 如果是今天，則過濾掉已過去的時段
        if (_selectedDate.year == DateTime.now().year &&
            _selectedDate.month == DateTime.now().month &&
            _selectedDate.day == DateTime.now().day) {
          final now = TimeOfDay.now();
          if (timeSlot.hour < now.hour ||
              (timeSlot.hour == now.hour && timeSlot.minute <= now.minute)) {
            return false;
          }
        }

        return !isBooked;
      }).toList();

      // 如果沒有選擇時段或選擇的時段不可用，則選擇第一個可用時段
      if (_availableTimeSlots.isNotEmpty &&
          (!_availableTimeSlots.any((slot) =>
            slot.hour == _selectedTime.hour &&
            slot.minute == _selectedTime.minute
          ))) {
        _selectedTime = _availableTimeSlots.first;
      }
    } catch (e) {
      logger.e('更新可用時段時出錯: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // 選擇星盤資料
  Future<void> _selectBirthData() async {
    try {
      if (_birthDataList.isEmpty) {
        // 如果沒有星盤資料，則創建新的
        _createNewBirthData();
        return;
      }

      // 顯示選擇對話框
      final dynamic result = await showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('選擇星盤資料'),
          content: SizedBox(
            width: double.maxFinite,
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: _birthDataList.length + 1, // +1 為「新增」選項
              itemBuilder: (context, index) {
                if (index == _birthDataList.length) {
                  // 新增選項
                  return ListTile(
                    leading: const Icon(Icons.add_circle, color: AppColors.royalIndigo),
                    title: const Text('新增星盤資料'),
                    onTap: () {
                      Navigator.of(context).pop();
                      _createNewBirthData();
                    },
                  );
                }

                final data = _birthDataList[index];
                return ListTile(
                  leading: CircleAvatar(
                    backgroundColor: AppColors.royalIndigo,
                    child: Text(
                      data.name.isNotEmpty ? data.name.substring(0, 1) : '?',
                      style: const TextStyle(color: Colors.white),
                    ),
                  ),
                  title: Text(data.name),
                  subtitle: Text(
                    '${data.dateTime.year}-${data.dateTime.month.toString().padLeft(2, '0')}-${data.dateTime.day.toString().padLeft(2, '0')} '
                    '${data.dateTime.hour.toString().padLeft(2, '0')}:${data.dateTime.minute.toString().padLeft(2, '0')}',
                  ),
                  onTap: () {
                    Navigator.of(context).pop(data);
                  },
                );
              },
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('取消'),
            ),
          ],
        ),
      );

      // 如果結果是 BirthData 類型，則更新選擇
      if (result is BirthData) {
        setState(() {
          _selectedBirthData = result;
        });

        // 儲存選擇的星盤資料 ID
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('selectedBirthDataId', result.id);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('選擇星盤資料時出錯: $e')),
        );
      }
    }
  }

  // 創建新的星盤資料
  Future<void> _createNewBirthData() async {
    try {
      final dynamic result = await Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => const BirthDataFormPage(),
        ),
      );

      if (result is BirthData) {
        // 將新創建的星盤資料添加到列表中並儲存
        setState(() {
          _selectedBirthData = result;
          if (!_birthDataList.any((data) => data.id == result.id)) {
            _birthDataList.add(result);
          }
        });

        // 儲存星盤資料列表
        final prefs = await SharedPreferences.getInstance();
        final List<Map<String, dynamic>> encodedData = _birthDataList.map((data) => data.toJson()).toList();
        await prefs.setString('birthDataList', jsonEncode(encodedData));

        // 儲存選擇的星盤資料 ID
        await prefs.setString('selectedBirthDataId', result.id);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('創建星盤資料時出錯: $e')),
        );
      }
    }
  }

  // 提交預約
  Future<void> _submitBooking() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // 如果沒有選擇星盤資料，顯示提示
    if (_selectedBirthData == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('請選擇或新增星盤資料')),
      );
      return;
    }

    setState(() {
      _isSaving = true;
    });

    try {
      // 檢查時段是否可用
      final isAvailable = await BookingService.isTimeSlotAvailable(
        _selectedDate,
        _selectedTime
      );

      if (!isAvailable) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('此時段已被預約，請選擇其他時段')),
          );
        }
        return;
      }

      // 獲取裝置唯一識別碼
      final deviceId = await getOrCreateDeviceId();

      // 創建預約
      final booking = BookingModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        name: _nameController.text,
        email: _emailController.text,
        contactMethod: _contactMethodController.text,
        phone: _phoneController.text,
        bookingDate: _selectedDate,
        bookingTime: _selectedTime,
        consultationType: _selectedConsultationType,
        notes: _notesController.text.isEmpty ? null : _notesController.text,
        birthData: _selectedBirthData!.toJson(), // 包含星盤資料
        deviceId: deviceId, // 添加裝置識別碼
      );

      // 儲存預約
      final success = await BookingService.saveBooking(booking);

      if (success) {
        // 儲存用戶資訊以便下次使用
        await _saveUserInfo();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('預約成功！我們將盡快與您聯繫確認')),
          );
          Navigator.pop(context);
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('預約失敗，請稍後再試')),
          );
        }
      }
    } catch (e) {
      logger.e('提交預約時出錯: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('發生錯誤，請稍後再試')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('報名占星諮詢'),
        actions: [
          // 意見回饋按鈕
          TextButton.icon(
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const FeedbackPage(),
                ),
              );
            },
            icon: const Icon(Icons.feedback, color: AppColors.cardBackground),
            label: const Text('意見回饋', style:TextStyle(color: Colors.white)),
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 星盤資料選擇
                    const Text(
                      '出生資料',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Container(
                      width: double.infinity, // 寬度滿版
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.1),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            if (_selectedBirthData != null) ...[
                              // 已選擇的星盤資料
                              Row(
                                children: [
                                  CircleAvatar(
                                    backgroundColor: AppColors.royalIndigo,
                                    radius: 24,
                                    child: Text(
                                      _selectedBirthData!.name.isNotEmpty ? _selectedBirthData!.name.substring(0, 1) : '?',
                                      style: const TextStyle(color: Colors.white, fontSize: 18),
                                    ),
                                  ),
                                  const SizedBox(width: 16),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          _selectedBirthData!.name,
                                          style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                                        ),
                                        const SizedBox(height: 4),
                                        Text(
                                          '${_selectedBirthData!.dateTime.year}-${_selectedBirthData!.dateTime.month.toString().padLeft(2, '0')}-${_selectedBirthData!.dateTime.day.toString().padLeft(2, '0')} '
                                              '${_selectedBirthData!.dateTime.hour.toString().padLeft(2, '0')}:${_selectedBirthData!.dateTime.minute.toString().padLeft(2, '0')}',
                                          style: const TextStyle(fontSize: 14, color: Colors.grey),
                                        ),
                                        const SizedBox(height: 4),
                                        Text(
                                          _selectedBirthData!.birthPlace,
                                          style: const TextStyle(fontSize: 14, color: Colors.grey),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 16),
                            ],
                            // 選擇或新增按鈕
                            SizedBox(
                              width: double.infinity,
                              child: ElevatedButton.icon(
                                icon: Icon(_selectedBirthData == null ? Icons.add : Icons.edit),
                                label: Text(_selectedBirthData == null ? '新增星盤資料' : '更改星盤資料'),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: AppColors.royalIndigo,
                                  foregroundColor: Colors.white,
                                  padding: const EdgeInsets.symmetric(vertical: 12),
                                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                                ),
                                onPressed: _selectBirthData,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 24),
                    // 諮詢類型選擇
                    // const Text(
                    //   '選擇諮詢類型',
                    //   style: TextStyle(
                    //     fontSize: 18,
                    //     fontWeight: FontWeight.bold,
                    //   ),
                    // ),
                    // const SizedBox(height: 8),
                    // DropdownButtonFormField<String>(
                    //   decoration: const InputDecoration(
                    //     border: OutlineInputBorder(),
                    //     contentPadding: EdgeInsets.symmetric(
                    //       horizontal: 16,
                    //       vertical: 8,
                    //     ),
                    //   ),
                    //   value: _selectedConsultationType,
                    //   items: ConsultationType.all.map((type) {
                    //     return DropdownMenuItem<String>(
                    //       value: type,
                    //       child: Text(type),
                    //     );
                    //   }).toList(),
                    //   onChanged: (value) {
                    //     if (value != null) {
                    //       setState(() {
                    //         _selectedConsultationType = value;
                    //       });
                    //     }
                    //   },
                    //   validator: (value) {
                    //     if (value == null || value.isEmpty) {
                    //       return '請選擇諮詢類型';
                    //     }
                    //     return null;
                    //   },
                    // ),
                    // const SizedBox(height: 16),

                    // 日期和時間選擇
                    // const Text(
                    //   '選擇預約日期和時間',
                    //   style: TextStyle(
                    //     fontSize: 18,
                    //     fontWeight: FontWeight.bold,
                    //   ),
                    // ),
                    // const SizedBox(height: 8),
                    // Row(
                    //   children: [
                    //     Expanded(
                    //       child: InkWell(
                    //         onTap: _selectDate,
                    //         child: InputDecorator(
                    //           decoration: const InputDecoration(
                    //             labelText: '日期',
                    //             border: OutlineInputBorder(),
                    //             contentPadding: EdgeInsets.symmetric(
                    //               horizontal: 16,
                    //               vertical: 8,
                    //             ),
                    //           ),
                    //           child: Text(
                    //             '${_selectedDate.year}/${_selectedDate.month}/${_selectedDate.day}',
                    //           ),
                    //         ),
                    //       ),
                    //     ),
                    //     const SizedBox(width: 16),
                    //     Expanded(
                    //       child: DropdownButtonFormField<TimeOfDay>(
                    //         decoration: const InputDecoration(
                    //           labelText: '時間',
                    //           border: OutlineInputBorder(),
                    //           contentPadding: EdgeInsets.symmetric(
                    //             horizontal: 16,
                    //             vertical: 8,
                    //           ),
                    //         ),
                    //         value: _availableTimeSlots.contains(_selectedTime)
                    //             ? _selectedTime
                    //             : (_availableTimeSlots.isNotEmpty
                    //                 ? _availableTimeSlots.first
                    //                 : _selectedTime),
                    //         items: _availableTimeSlots.isEmpty
                    //             ? [
                    //                 DropdownMenuItem<TimeOfDay>(
                    //                   value: _selectedTime,
                    //                   child: const Text('無可用時段'),
                    //                 )
                    //               ]
                    //             : _availableTimeSlots.map((timeSlot) {
                    //                 return DropdownMenuItem<TimeOfDay>(
                    //                   value: timeSlot,
                    //                   child: Text(
                    //                     '${timeSlot.hour}:${timeSlot.minute.toString().padLeft(2, '0')}',
                    //                   ),
                    //                 );
                    //               }).toList(),
                    //         onChanged: (value) {
                    //           if (value != null) {
                    //             setState(() {
                    //               _selectedTime = value;
                    //             });
                    //           }
                    //         },
                    //         validator: (value) {
                    //           if (_availableTimeSlots.isEmpty) {
                    //             return '此日期無可用時段';
                    //           }
                    //           return null;
                    //         },
                    //       ),
                    //     ),
                    //   ],
                    // ),
                    // if (_availableTimeSlots.isEmpty) ...[
                    //   const SizedBox(height: 8),
                    //   const Text(
                    //     '此日期無可用時段，請選擇其他日期',
                    //     style: TextStyle(
                    //       color: Colors.red,
                    //       fontSize: 12,
                    //     ),
                    //   ),
                    // ],
                    // const SizedBox(height: 16),

                    // 個人資訊
                    const Text(
                      '聯絡資訊',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    // TextFormField(
                    //   controller: _nameController,
                    //   decoration: const InputDecoration(
                    //     labelText: '姓名',
                    //     border: OutlineInputBorder(),
                    //   ),
                    //   validator: (value) {
                    //     if (value == null || value.isEmpty) {
                    //       return '請輸入姓名(暱稱)';
                    //     }
                    //     return null;
                    //   },
                    // ),
                    // const SizedBox(height: 16),
                    TextFormField(
                      controller: _emailController,
                      decoration: const InputDecoration(
                        labelText: '電子郵件',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.emailAddress,
                      // validator: (value) {
                      //   if (value == null || value.isEmpty) {
                      //     return '請輸入電子郵件';
                      //   }
                      //   if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$')
                      //       .hasMatch(value)) {
                      //     return '請輸入有效的電子郵件地址';
                      //   }
                      //   return null;
                      // },
                    ),
                    // const SizedBox(height: 16),
                    // TextFormField(
                    //   controller: _phoneController,
                    //   decoration: const InputDecoration(
                    //     labelText: '電話號碼',
                    //     border: OutlineInputBorder(),
                    //   ),
                    //   keyboardType: TextInputType.phone,
                    //   validator: (value) {
                    //     if (value == null || value.isEmpty) {
                    //       return '請輸入您的電話號碼';
                    //     }
                    //     return null;
                    //   },
                    // ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _contactMethodController,
                      decoration: const InputDecoration(
                        labelText: '聯絡方式',
                        hintText: '例如：Dcard ID、Line ID 等',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.text,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return '請輸入您的聯絡方式';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),

                    // 備註
                    TextFormField(
                      controller: _notesController,
                      decoration: const InputDecoration(
                        labelText: '備註',
                        border: OutlineInputBorder(),
                        hintText: '請詳述您想諮詢的問題或特別需求\n簡單自我介紹\n生命中的重要經歷或趣事',
                      ),
                      maxLines: 5,
                    ),
                    const SizedBox(height: 24),

                    // 提交按鈕
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _isSaving || _availableTimeSlots.isEmpty
                            ? null
                            : _submitBooking,
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          backgroundColor: AppColors.royalIndigo,
                          foregroundColor: Colors.white,
                        ),
                        child: _isSaving
                            ? const SizedBox(
                                width: 24,
                                height: 24,
                                child: CircularProgressIndicator(
                                  color: Colors.white,
                                  strokeWidth: 2,
                                ),
                              )
                            : const Text(
                                '提交',
                                style: TextStyle(fontSize: 16),
                              ),
                      ),
                    ),
                    const SizedBox(height: 16),

                    // 預約說明
                    Container(
                      width: double.infinity, // 寬度滿版
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.1),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: const Padding(
                        padding: EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '須知',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            SizedBox(height: 8),
                            // Text('• 諮詢時間為 60 分鐘'),
                            Text('• 報名成功後，將透過聯絡方式與您確認'),
                            Text('• 願意提供精準出生資訊（日期、時間、地點）'),
                            Text('• 願意分享自己的人生故事（特別事件、轉折點、奇妙經歷都可以）'),
                            Text('• 完成諮商後，提供簡短回饋，回饋內容將用於優化未來服務流程'),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }
}
