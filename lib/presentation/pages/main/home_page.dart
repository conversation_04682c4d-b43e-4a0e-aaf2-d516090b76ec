import 'package:astreal/presentation/pages/ai_interpretation_result_page.dart';
import 'package:astreal/presentation/viewmodels/files_viewmodel.dart';
import 'package:astreal/presentation/viewmodels/recent_charts_viewmodel.dart';
import 'package:astreal/shared/widgets/person_info_card.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../astreal.dart';
import '../../../data/services/api/astro_weather_service.dart';
import '../../../data/services/api/equinox_solstice_service.dart';
import '../../../shared/widgets/common/responsive_wrapper.dart';
import '../analysis/divination_analysis_page.dart';
import '../astro_calendar_page.dart';
import '../birth_data_form_page.dart';
import '../chart_page.dart';
import '../chart_selection_page.dart';
import '../eclipse_setup_page.dart';
import '../equinox_solstice_page.dart';
import '../location_picker_page.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  // 添加 PageController 用於控制滑動
  late PageController _pageController;

  @override
  void initState() {
    super.initState();
    // 初始化 PageController（保留以避免其他地方可能使用）
    _pageController = PageController();
  }

  @override
  void dispose() {
    // 釋放 PageController
    _pageController.dispose();
    super.dispose();
  }

  // 導航到今日星相星盤頁面
  void _navigateToTodayChart(HomeViewModel viewModel) {
    try {
      // 創建 ChartData 對象
      final chartData = viewModel.createTodayChartData();

      // 導航到星盤頁面
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => ChangeNotifierProvider(
            create: (_) =>
                ChartViewModel.withChartData(initialChartData: chartData),
            child: ChartPage(chartData: chartData),
          ),
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('無法載入今日星相: $e')),
      );
    }
  }

  // 選擇位置
  Future<void> _selectLocation(HomeViewModel viewModel) async {
    final result = await Navigator.push<Map<String, dynamic>>(
      context,
      MaterialPageRoute(
        builder: (context) => const LocationPickerPage(),
      ),
    );

    if (result != null) {
      viewModel.updateLocation(
        result['name'],
        result['latitude'],
        result['longitude'],
      );
    }
  }

  // 編輯選中的人物資訊
  Future<void> _editSelectedPerson(HomeViewModel viewModel) async {
    if (viewModel.selectedPerson == null) return;

    // 使用 BirthDataFormPage 來編輯出生資料
    final result = await Navigator.push<BirthData>(
      context,
      MaterialPageRoute(
        builder: (context) => BirthDataFormPage(initialData: viewModel.selectedPerson!),
      ),
    );

    // 如果用戶返回了更新後的出生資料，則更新數據
    if (result != null) {
      await viewModel.updateBirthData(result);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('出生資料已更新')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => HomeViewModel(),
      child: Consumer<HomeViewModel>(builder: (context, viewModel, child) {
        return Scaffold(
            appBar: AppBar(
              title: const Text('首頁'),
              actions: [
                IconButton(
                  icon: const Icon(Icons.location_on, color: Colors.white),
                  onPressed: () {
                    _selectLocation(viewModel);
                  },
                  tooltip: '選擇地點',
                ),
                IconButton(
                  icon: const Icon(Icons.refresh, color: Colors.white),
                  onPressed: () {
                    viewModel.loadRecentAspects();
                  },
                  tooltip: '更新星象',
                ),
              ],
            ),
            body: Stack(children: [
              ResponsivePageWrapper(
                maxWidth: 800.0, // 主頁面適合中等寬度
                child: ListView(
                    padding: ResponsiveUtils.getResponsivePadding(context),
                    children: [
                    // 預約占星諮詢卡片
                    // StyledCard(
                    //   elevation: 2,
                    //   padding: const EdgeInsets.all(16),
                    //   onTap: () {
                    //     Navigator.push(
                    //       context,
                    //       MaterialPageRoute(
                    //         builder: (context) => const BookingPage(),
                    //       ),
                    //     );
                    //   },
                    //   child: const Column(
                    //     crossAxisAlignment: CrossAxisAlignment.start,
                    //     children: [
                    //       CardTitle(
                    //         title: '報名占星諮詢',
                    //         icon: Icons.event_available,
                    //         iconColor: Colors.green,
                    //         trailing: Icon(Icons.arrow_forward_ios, size: 16),
                    //       ),
                    //       SizedBox(height: 8),
                    //       Text(
                    //         '報名占星個人化諮詢服務，幫助你更深入地了解自己的星盤和生命旅程。',
                    //         style: TextStyle(fontSize: 14, height: 1.4),
                    //       ),
                    //     ],
                    //   ),
                    // ),

                    // 個人資訊卡片 - 使用新的共用元件
                    PersonInfoCard(
                      selectedPerson: viewModel.selectedPerson,
                      personList: viewModel.birthDataList,
                      onPersonSelected: (person) {
                        viewModel.setSelectedPerson(person);
                      },
                      onEditPerson: (person) {
                        _editSelectedPerson(viewModel);
                      },
                      formatDateTime: viewModel.formatDateTime,
                      isLoading: viewModel.isLoadingBirthData,
                    ),

                    const SizedBox(height: 16),

                    // 占星天氣預報卡片
                    if (viewModel.selectedPerson != null)
                      _buildWeatherForecastCard(viewModel),

                    const SizedBox(height: 16),

                    // 快捷功能按鈕區域
                    _buildQuickActionButtons(viewModel),

                    // 下一個節氣卡片
                    if (viewModel.nextSeason != null) ...[
                      _buildNextSeasonCard(viewModel),
                    ],

                    // 顯示行運盤資訊
                    // if (viewModel.selectedPerson != null) ...[
                    //   const TransitAspectsSection(),
                    // ],

                    // const SizedBox(height: 12),

                    // 今日星相卡片
                    // StyledCard(
                    //   elevation: 2,
                    //   margin: const EdgeInsets.only(bottom: 12),
                    //   onTap: viewModel.recentAspects.isNotEmpty
                    //       ? () {
                    //           _navigateToTodayChart(viewModel);
                    //         }
                    //       : null,
                    //   child: Column(
                    //     crossAxisAlignment: CrossAxisAlignment.start,
                    //     children: [
                    //       // 標題列
                    //       CardTitle(
                    //         title: '今日星相',
                    //         icon: Icons.stars,
                    //         iconColor: AppColors.solarAmber,
                    //         trailing: Row(
                    //           mainAxisSize: MainAxisSize.min,
                    //           children: [
                    //             TextButton.icon(
                    //               onPressed: () {
                    //                 _selectLocation(viewModel);
                    //               },
                    //               icon: const Icon(Icons.location_on, size: 18),
                    //               label: Text(viewModel.selectedLocation,
                    //                   style: const TextStyle(fontSize: 14)),
                    //               style: TextButton.styleFrom(
                    //                 foregroundColor: AppColors.royalIndigo,
                    //                 padding: const EdgeInsets.symmetric(
                    //                     horizontal: 8, vertical: 4),
                    //               ),
                    //             ),
                    //             if (viewModel.recentAspects.isNotEmpty)
                    //               IconButton(
                    //                 icon: const Icon(Icons.arrow_forward,
                    //                     size: 18),
                    //                 onPressed: () {
                    //                   _navigateToTodayChart(viewModel);
                    //                 },
                    //                 tooltip: '查看星盤',
                    //                 color: AppColors.royalIndigo,
                    //               ),
                    //           ],
                    //         ),
                    //       ),
                    //
                    //       // 內容區域
                    //       if (viewModel.recentAspects.isEmpty)
                    //         const Padding(
                    //           padding: EdgeInsets.all(16.0),
                    //           child: Center(
                    //             child: Text(
                    //               '今日沒有重要星相',
                    //               style: TextStyle(
                    //                 fontSize: 16,
                    //                 color: Colors.grey,
                    //               ),
                    //             ),
                    //           ),
                    //         )
                    //       else
                    //         // 顯示今日星相
                    //         Padding(
                    //           padding: const EdgeInsets.all(0.0),
                    //           child: Builder(builder: (context) {
                    //             final dayAspects =
                    //                 viewModel.recentAspects[0]; // 只有一天的數據
                    //             final aspects = dayAspects['aspects'] as List;
                    //             final planets = dayAspects['planets'] as List;
                    //             final date = dayAspects['date'] as DateTime;
                    //
                    //             return SingleChildScrollView(
                    //               child: Column(
                    //                   crossAxisAlignment:
                    //                       CrossAxisAlignment.start,
                    //                   children: [
                    //                     Center(
                    //                       child: Container(
                    //                         padding: const EdgeInsets.symmetric(
                    //                             horizontal: 16, vertical: 8),
                    //                         decoration: BoxDecoration(
                    //                           color: AppColors.royalIndigo
                    //                               .withValues(alpha: 0.1),
                    //                           borderRadius:
                    //                               BorderRadius.circular(20),
                    //                         ),
                    //                         child: Text(
                    //                           viewModel.formatDate(date),
                    //                           style: const TextStyle(
                    //                             fontSize: 18,
                    //                             fontWeight: FontWeight.bold,
                    //                             color: AppColors.royalIndigo,
                    //                           ),
                    //                         ),
                    //                       ),
                    //                     ),
                    //                     const SizedBox(height: 8),
                    //                     // 顯示各行星的星座位置
                    //                     const Row(
                    //                       children: [
                    //                         Icon(Icons.public,
                    //                             size: 18,
                    //                             color: AppColors.royalIndigo),
                    //                         SizedBox(width: 8),
                    //                         Text(
                    //                           '行星位置',
                    //                           style: TextStyle(
                    //                             fontSize: 16,
                    //                             fontWeight: FontWeight.bold,
                    //                             color: AppColors.royalIndigo,
                    //                           ),
                    //                         ),
                    //                       ],
                    //                     ),
                    //                     const SizedBox(height: 8),
                    //                     Wrap(
                    //                       spacing: 8,
                    //                       runSpacing: 8,
                    //                       children: planets.map((planet) {
                    //                         return Container(
                    //                           padding:
                    //                               const EdgeInsets.symmetric(
                    //                             horizontal: 10,
                    //                             vertical: 6,
                    //                           ),
                    //                           decoration: BoxDecoration(
                    //                             color: AppColors.royalIndigo
                    //                                 .withValues(alpha: 0.08),
                    //                             borderRadius:
                    //                                 BorderRadius.circular(12),
                    //                             border: Border.all(
                    //                               color: AppColors.royalIndigo
                    //                                   .withValues(alpha: 0.2),
                    //                               width: 1,
                    //                             ),
                    //                           ),
                    //                           child: Text(
                    //                             '${planet.name} ${planet.sign}',
                    //                             style: const TextStyle(
                    //                               fontSize: 14,
                    //                               color: AppColors.royalIndigo,
                    //                             ),
                    //                           ),
                    //                         );
                    //                       }).toList(),
                    //                     ),
                    //                     // 相位部分
                    //                     if (aspects.isNotEmpty) ...[
                    //                       const SizedBox(height: 8),
                    //                       const Row(
                    //                         children: [
                    //                           Icon(Icons.compare_arrows,
                    //                               size: 18,
                    //                               color: AppColors.royalIndigo),
                    //                           SizedBox(width: 8),
                    //                           Text(
                    //                             '今日相位',
                    //                             style: TextStyle(
                    //                               fontSize: 16,
                    //                               fontWeight: FontWeight.bold,
                    //                               color: AppColors.royalIndigo,
                    //                             ),
                    //                           ),
                    //                         ],
                    //                       ),
                    //                       const SizedBox(height: 8),
                    //                       // 按重要性排序相位
                    //                       ...(() {
                    //                         final aspectsWithImportance =
                    //                             aspects
                    //                                 .map((aspect) => {
                    //                                       'aspect': aspect,
                    //                                       'importance': viewModel
                    //                                           .getAspectImportance(
                    //                                               aspect.aspect,
                    //                                               aspect.planet1
                    //                                                   .name,
                    //                                               aspect.planet2
                    //                                                   .name)
                    //                                     })
                    //                                 .toList();
                    //
                    //                         aspectsWithImportance.sort((a, b) =>
                    //                             (b['importance'] as int)
                    //                                 .compareTo(a['importance']
                    //                                     as int));
                    //
                    //                         return aspectsWithImportance;
                    //                       })()
                    //                           .map((item) {
                    //                         final aspect =
                    //                             item['aspect'] as AspectInfo;
                    //                         final importance =
                    //                             item['importance'] as int;
                    //                         final aspectColor =
                    //                             AstrologyCalculator
                    //                                 .getAspectColor(
                    //                                     aspect.aspect);
                    //                         final meaning = viewModel
                    //                             .getAspectForecast(aspect);
                    //
                    //                         return Container(
                    //                           margin: const EdgeInsets.only(
                    //                               bottom: 10),
                    //                           padding: const EdgeInsets.all(12),
                    //                           decoration: BoxDecoration(
                    //                             color: aspectColor
                    //                                 .withValues(alpha: 0.05),
                    //                             borderRadius:
                    //                                 BorderRadius.circular(12),
                    //                             border: Border.all(
                    //                               color: aspectColor
                    //                                   .withValues(alpha: 0.2),
                    //                               width: 1,
                    //                             ),
                    //                           ),
                    //                           child: Column(
                    //                             crossAxisAlignment:
                    //                                 CrossAxisAlignment.start,
                    //                             children: [
                    //                               Row(
                    //                                 children: [
                    //                                   Container(
                    //                                     padding:
                    //                                         const EdgeInsets
                    //                                             .symmetric(
                    //                                       horizontal: 8,
                    //                                       vertical: 2,
                    //                                     ),
                    //                                     decoration:
                    //                                         BoxDecoration(
                    //                                       color: aspectColor
                    //                                           .withValues(alpha: 0.1),
                    //                                       borderRadius:
                    //                                           BorderRadius
                    //                                               .circular(8),
                    //                                     ),
                    //                                     child: Text(
                    //                                       aspect.symbol,
                    //                                       style: TextStyle(
                    //                                         fontFamily:
                    //                                             "astro_one_font",
                    //                                         fontSize: 16,
                    //                                         fontWeight:
                    //                                             FontWeight.bold,
                    //                                         color: aspectColor,
                    //                                       ),
                    //                                     ),
                    //                                   ),
                    //                                   const SizedBox(width: 8),
                    //                                   Expanded(
                    //                                     child: Text(
                    //                                       '${aspect.planet1.name} ${aspect.shortZh} ${aspect.planet2.name}${aspect.direction != null ? ' (${aspect.getDirectionText()} ${aspect.orb.toStringAsFixed(2)}°)' : ''}',
                    //                                       style:
                    //                                           const TextStyle(
                    //                                               fontSize: 12),
                    //                                     ),
                    //                                   ),
                    //                                   if (importance >= 3)
                    //                                     Container(
                    //                                       padding:
                    //                                           const EdgeInsets
                    //                                               .symmetric(
                    //                                               horizontal: 6,
                    //                                               vertical: 2),
                    //                                       decoration:
                    //                                           BoxDecoration(
                    //                                         color: AppColors
                    //                                             .warning
                    //                                             .withValues(alpha: 
                    //                                                 0.2),
                    //                                         borderRadius:
                    //                                             BorderRadius
                    //                                                 .circular(
                    //                                                     4),
                    //                                       ),
                    //                                       child: const Text(
                    //                                         '重要',
                    //                                         style: TextStyle(
                    //                                           fontSize: 10,
                    //                                           fontWeight:
                    //                                               FontWeight
                    //                                                   .bold,
                    //                                           color: AppColors
                    //                                               .warning,
                    //                                         ),
                    //                                       ),
                    //                                     ),
                    //                                 ],
                    //                               ),
                    //                             ],
                    //                           ),
                    //                         );
                    //                       }),
                    //                     ],
                    //                   ]),
                    //             );
                    //           }),
                    //         ),
                    //     ],
                    //   ),
                    // )
                  ]),
                ),
              // if (viewModel.isLoading)
              //   const Center(child: CircularProgressIndicator()),
            ]));
      }),
    );
  }

  /// 構建快捷功能按鈕區域
  Widget _buildQuickActionButtons(HomeViewModel viewModel) {
    return StyledCard(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '快捷功能',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppColors.royalIndigo,
              ),
            ),
            const SizedBox(height: 8),
            // 第一行按鈕
            Row(
              children: [
                // 卜卦分析按鈕
                Expanded(
                  child: _buildQuickActionButton(
                    icon: Icons.auto_awesome,
                    title: '卜卦分析',
                    subtitle: '占星、周易',
                    color: AppColors.solarAmber,
                    onTap: _navigateToDivination,
                  ),
                ),
                const SizedBox(width: 8),
                // 星盤按鈕
                Expanded(
                  child: _buildQuickActionButton(
                    icon: Icons.star,
                    title: '星盤',
                    subtitle: '各種星盤類型',
                    color: AppColors.success,
                    onTap: () => _navigateToChartSelection(viewModel),
                  ),
                ),
                const SizedBox(width: 8),
                // 星象日曆按鈕
                Expanded(
                  child: _buildQuickActionButton(
                    icon: Icons.calendar_month,
                    title: '星象日曆',
                    subtitle: '月相、節氣、相位',
                    color: AppColors.royalIndigo,
                    onTap: () => _navigateToAstroCalendarWithLoading(),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            // 第二行按鈕
            Row(
              children: [
                // 二分二至盤按鈕
                Expanded(
                  child: _buildQuickActionButton(
                    icon: Icons.wb_sunny,
                    title: '二分二至盤',
                    subtitle: '春分、夏至、秋分、冬至',
                    color: Colors.orange,
                    onTap: () => _navigateToEquinoxSolsticeQuick(viewModel),
                  ),
                ),
                const SizedBox(width: 8),
                // 日月蝕盤按鈕
                Expanded(
                  child: _buildQuickActionButton(
                    icon: Icons.brightness_2,
                    title: '日月蝕盤',
                    subtitle: '日蝕、月蝕分析',
                    color: Colors.deepPurple,
                    onTap: () => _navigateToEclipseChart(viewModel),
                  ),
                ),
                const SizedBox(width: 8),
                // 佔位符，保持對齊
                Expanded(
                  child: Container(),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 構建單個快捷按鈕
  Widget _buildQuickActionButton({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(10),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.08),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: color.withValues(alpha: 0.25),
            width: 1,
          ),
        ),
        child: Column(
          children: [
            Container(
              width: 30,
              height: 30,
              decoration: BoxDecoration(
                color: color,
                borderRadius: BorderRadius.circular(18),
                boxShadow: [
                  BoxShadow(
                    color: color.withValues(alpha: 0.3),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Icon(
                icon,
                color: Colors.white,
                size: 16,
              ),
            ),
            const SizedBox(height: 6),
            Text(
              title,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: color,
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            // const SizedBox(height: 2),
            // Text(
            //   subtitle,
            //   style: TextStyle(
            //     fontSize: 10,
            //     color: Colors.grey[600],
            //     height: 1.2,
            //   ),
            //   textAlign: TextAlign.center,
            //   maxLines: 2,
            //   overflow: TextOverflow.ellipsis,
            // ),
          ],
        ),
      ),
    );
  }

  /// 構建下一個節氣卡片
  Widget _buildNextSeasonCard(HomeViewModel viewModel) {
    final nextSeason = viewModel.nextSeason!;
    final now = DateTime.now();
    final daysUntil = nextSeason.dateTime.difference(now).inDays;

    // 獲取季節圖標和顏色
    final seasonIcon = _getSeasonIcon(nextSeason.seasonType);
    final seasonColor = _getSeasonColor(nextSeason.seasonType);

    return StyledCard(
      elevation: 2,
      onTap: () {
        _navigateToEquinoxSolstice(viewModel);
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              seasonColor.withValues(alpha: 0.1),
              seasonColor.withValues(alpha: 0.05),
            ],
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 標題列
              Row(
                children: [
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: seasonColor.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Icon(
                      seasonIcon,
                      color: seasonColor,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '下一個節氣：${nextSeason.seasonType.displayName}',
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          _formatSeasonDateTime(nextSeason.dateTime),
                          style: TextStyle(
                            fontSize: 13,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  Column(
                    children: [
                      Text(
                        '$daysUntil',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: seasonColor,
                        ),
                      ),
                      Text(
                        '天後',
                        style: TextStyle(
                          fontSize: 12,
                          color: seasonColor,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(width: 8),
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 16,
                    color: Colors.grey[400],
                  ),
                ],
              ),
              const SizedBox(height: 12),

              // 節氣描述
              Text(
                _getSeasonDescription(nextSeason.seasonType),
                style: TextStyle(
                  fontSize: 13,
                  color: Colors.grey[700],
                  height: 1.3,
                ),
              ),

              // 如果有本命盤人物，顯示個人影響提示
              if (viewModel.selectedPerson != null) ...[
                const SizedBox(height: 8),
                GestureDetector(
                  onTap: () =>
                      _navigateToSeasonComparison(viewModel, nextSeason),
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AppColors.royalIndigo.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(6),
                      border: Border.all(
                        color: AppColors.royalIndigo.withValues(alpha: 0.3),
                        width: 1,
                      ),
                    ),
                    child: Row(
                      children: [
                        const Icon(
                          Icons.person,
                          size: 16,
                          color: AppColors.royalIndigo,
                        ),
                        const SizedBox(width: 6),
                        Expanded(
                          child: Text(
                            '點擊查看與 ${viewModel.selectedPerson!.name} 的比較盤',
                            style: const TextStyle(
                              fontSize: 12,
                              color: AppColors.royalIndigo,
                            ),
                          ),
                        ),
                        Icon(
                          Icons.arrow_forward_ios,
                          size: 12,
                          color: AppColors.royalIndigo.withValues(alpha: 0.7),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  /// 獲取季節圖標
  IconData _getSeasonIcon(SeasonType seasonType) {
    switch (seasonType) {
      case SeasonType.springEquinox:
        return Icons.local_florist; // 春分 - 花朵
      case SeasonType.summerSolstice:
        return Icons.wb_sunny; // 夏至 - 太陽
      case SeasonType.autumnEquinox:
        return Icons.eco; // 秋分 - 葉子
      case SeasonType.winterSolstice:
        return Icons.ac_unit; // 冬至 - 雪花
    }
  }

  /// 獲取季節顏色
  Color _getSeasonColor(SeasonType seasonType) {
    switch (seasonType) {
      case SeasonType.springEquinox:
        return Colors.green; // 春分 - 綠色
      case SeasonType.summerSolstice:
        return AppColors.solarAmber; // 夏至 - 金黃色
      case SeasonType.autumnEquinox:
        return Colors.orange; // 秋分 - 橙色
      case SeasonType.winterSolstice:
        return Colors.blue; // 冬至 - 藍色
    }
  }

  /// 獲取季節描述
  String _getSeasonDescription(SeasonType seasonType) {
    switch (seasonType) {
      case SeasonType.springEquinox:
        return '太陽進入牡羊座，晝夜等長，象徵新的開始與成長。';
      case SeasonType.summerSolstice:
        return '太陽進入巨蟹座，北半球白晝最長，陽氣最盛。';
      case SeasonType.autumnEquinox:
        return '太陽進入天秤座，晝夜等長，象徵平衡與收穫。';
      case SeasonType.winterSolstice:
        return '太陽進入摩羯座，北半球白晝最短，陰氣最盛。';
    }
  }

  /// 格式化季節日期時間
  String _formatSeasonDateTime(DateTime dateTime) {
    return '${dateTime.month}月${dateTime.day}日 '
        '${dateTime.hour.toString().padLeft(2, '0')}:'
        '${dateTime.minute.toString().padLeft(2, '0')}';
  }

  /// 導航到二分二至圖頁面
  void _navigateToEquinoxSolstice(HomeViewModel viewModel) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => EquinoxSolsticePage(
          natalPerson: viewModel.selectedPerson,
          location: BirthData(
            id: 'current_location',
            name: viewModel.selectedLocation,
            dateTime: DateTime.now(),
            latitude: viewModel.latitude,
            longitude: viewModel.longitude,
            birthPlace: viewModel.selectedLocation,
          ),
        ),
      ),
    );
  }

  /// 導航到節氣比較盤
  void _navigateToSeasonComparison(
      HomeViewModel viewModel, SeasonData nextSeason) {
    if (viewModel.selectedPerson == null) return;

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ChartSelectionPage(
          primaryPerson: viewModel.selectedPerson!,
          secondaryPerson: BirthData(
            id: 'season_${nextSeason.seasonType.name}',
            name:
                '${nextSeason.seasonType.displayName} (${DateTime.now().year}年)',
            dateTime: nextSeason.dateTime,
            latitude: viewModel.latitude,
            longitude: viewModel.longitude,
            birthPlace: viewModel.selectedLocation,
          ),
          initialChartType: ChartType.synastry, // 設定為合盤
          isChangingChartType: false,
        ),
      ),
    );
  }

  /// 導航到卜卦分析頁面
  void _navigateToDivination() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const DivinationAnalysisPage(
          title: '卜卦分析',
          description: '占星卜卦、周易卜卦和塔羅牌占卜',
        ),
      ),
    );
  }

  /// 導航到星盤選擇頁面
  void _navigateToChartSelection(HomeViewModel viewModel) {
    // 直接導航到星盤選擇頁面，不需要預先選擇人物
    // 如果有選擇的人物，作為預設值傳入；如果沒有，傳入 null
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => MultiProvider(
          providers: [
            // 確保 RecentChartsViewModel 在新的路由中可用
            ChangeNotifierProvider.value(
              value: Provider.of<RecentChartsViewModel>(context, listen: false),
            ),
            // 確保 FilesViewModel 在新的路由中可用
            ChangeNotifierProvider.value(
              value: Provider.of<FilesViewModel>(context, listen: false),
            ),
          ],
          child: ChartSelectionPage(
            primaryPerson: viewModel.selectedPerson, // 可以為 null
            allPeople: viewModel.birthDataList,
          ),
        ),
      ),
    );
  }

  /// 帶loading的星象日曆導航
  void _navigateToAstroCalendarWithLoading() async {
    // 導航到星象日曆頁面
    if (mounted) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => const AstroCalendarPage(),
        ),
      );
    }
  }

  /// 導航到二分二至盤快捷功能
  void _navigateToEquinoxSolsticeQuick(HomeViewModel viewModel) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => EquinoxSolsticePage(
          natalPerson: viewModel.selectedPerson,
          location: BirthData(
            id: 'current_location',
            name: viewModel.selectedLocation,
            dateTime: DateTime.now(),
            latitude: viewModel.latitude,
            longitude: viewModel.longitude,
            birthPlace: viewModel.selectedLocation,
          ),
        ),
      ),
    );
  }

  /// 導航到日月蝕盤設定頁面
  void _navigateToEclipseChart(HomeViewModel viewModel) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => EclipseSetupPage(
          natalPerson: viewModel.selectedPerson,
        ),
      ),
    );
  }

  /// 構建占星天氣預報卡片
  Widget _buildWeatherForecastCard(HomeViewModel viewModel) {
    return Card(
      margin: EdgeInsets.zero,
      child: InkWell(
        onTap: () => _navigateToTransitChart(viewModel),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  const Icon(
                    Icons.wb_sunny,
                    color: Colors.orange,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    '今日占星天氣',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  if (viewModel.isLoadingWeatherForecast)
                    const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  else
                    Icon(
                      Icons.arrow_forward_ios,
                      size: 16,
                      color: Colors.grey[600],
                    ),
                ],
              ),
              const SizedBox(height: 12),
              if (viewModel.todayWeatherForecast != null)
                _buildWeatherForecastContent(viewModel.todayWeatherForecast!, viewModel)
              else if (!viewModel.isLoadingWeatherForecast)
                const Text('暫無天氣預報資料'),
            ],
          ),
        ),
      ),
    );
  }

  /// 構建天氣預報內容
  Widget _buildWeatherForecastContent(AstroWeatherForecast forecast, HomeViewModel viewModel) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 天氣等級和摘要
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            color: _getWeatherLevelColor(forecast.level).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: _getWeatherLevelColor(forecast.level).withValues(alpha: 0.3),
            ),
          ),
          child: Row(
            children: [
              Text(
                forecast.level.emoji,
                style: const TextStyle(fontSize: 24),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      forecast.level.name,
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: _getWeatherLevelColor(forecast.level),
                      ),
                    ),
                    Text(
                      forecast.summary,
                      style: const TextStyle(fontSize: 13),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),

        // 重點星象
        if (forecast.highlights.isNotEmpty) ...[
          const SizedBox(height: 12),
          Text(
            '重點星象',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          ...forecast.highlights.map((highlight) => Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('• ', style: TextStyle(color: Colors.grey)),
                Expanded(
                  child: Text(
                    highlight,
                    style: const TextStyle(fontSize: 13),
                  ),
                ),
              ],
            ),
          )),
        ],

        // 強度指示器
        const SizedBox(height: 12),
        Row(
          children: [
            const Text('影響強度：', style: TextStyle(fontSize: 12)),
            Expanded(
              child: LinearProgressIndicator(
                value: forecast.intensity,
                backgroundColor: Colors.grey[300],
                valueColor: AlwaysStoppedAnimation<Color>(
                  _getWeatherLevelColor(forecast.level),
                ),
              ),
            ),
            const SizedBox(width: 8),
            Text(
              '${(forecast.intensity * 100).round()}%',
              style: const TextStyle(fontSize: 12),
            ),
          ],
        ),

        // AI分析按鈕
        const SizedBox(height: 12),
        _buildAIAnalysisButton(viewModel),
      ],
    );
  }

  /// 獲取天氣等級對應的顏色
  Color _getWeatherLevelColor(AstroWeatherLevel level) {
    switch (level) {
      case AstroWeatherLevel.stable:
        return Colors.green;
      case AstroWeatherLevel.lightFluctuation:
        return Colors.blue;
      case AstroWeatherLevel.emotionalDisturbance:
        return Colors.orange;
      case AstroWeatherLevel.dramaticChange:
        return Colors.red;
      case AstroWeatherLevel.destinyStorm:
        return Colors.purple;
    }
  }

  /// 導航到行運盤頁面
  void _navigateToTransitChart(HomeViewModel viewModel) {
    if (viewModel.selectedPerson == null) return;

    final chartData = ChartData(
      chartType: ChartType.transit,
      primaryPerson: viewModel.selectedPerson!,
      specificDate: DateTime.now(),
    );

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ChangeNotifierProvider(
          create: (_) => ChartViewModel.withChartData(initialChartData: chartData),
          child: ChartPage(chartData: chartData),
        ),
      ),
    );
  }

  /// 構建天氣預報專用的AI提示詞
  String _buildWeatherForecastPrompt() {
    return '''
請基於這個今日行運盤為用戶提供詳細的運勢分析，重點關注今天的能量狀態和生活指導：

🌟 **今日運勢重點分析：**

1. **整體能量狀態**
   - 今天的主要星象能量特質
   - 情緒和心理狀態的預期
   - 整體運勢的基調和趨勢

2. **重要行運相位影響**
   - 分析最重要的3-5個行運相位
   - 每個相位對不同生活領域的具體影響
   - 相位能量的發揮時機和注意事項

3. **各生活領域運勢**
   - **感情關係**：今日感情運勢，人際互動建議
   - **事業工作**：工作效率，重要決策，職場人際
   - **財運狀況**：消費投資建議，財務決策提醒
   - **健康狀態**：身心健康注意事項，養生建議

4. **今日行動指南**
   - **適合做的事**：把握今日有利能量的具體行動
   - **需要避免的事**：可能帶來負面影響的行為
   - **重要時機**：今日特別有利或需要謹慎的時段

5. **情緒管理建議**
   - 今日可能出現的情緒波動
   - 壓力釋放和情緒調節的方法
   - 保持內心平衡的具體建議

6. **人際關係指導**
   - 今日人際互動的重點
   - 溝通交流的注意事項
   - 處理人際關係的策略

7. **幸運提醒**
   - 今日的幸運色彩、數字、方位
   - 有利的活動類型和環境
   - 提升運勢的小貼士

**請以溫暖、實用、正面的語調提供分析，給出具體可行的生活指導建議，幫助用戶更好地度過今天。**
''';
  }

  /// 構建AI分析按鈕
  Widget _buildAIAnalysisButton(HomeViewModel viewModel) {
    return SizedBox(
      width: double.infinity,
      child: OutlinedButton.icon(
        onPressed: () => _showAIAnalysisDialog(viewModel),
        icon: const Icon(Icons.psychology, size: 18),
        label: const Text('分析運勢'),
        style: OutlinedButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
          side: BorderSide(color: Theme.of(context).primaryColor.withValues(alpha: 0.5)),
          foregroundColor: Theme.of(context).primaryColor,
        ),
      ),
    );
  }

  /// 顯示AI分析頁面
  void _showAIAnalysisDialog(HomeViewModel viewModel) async {
    if (viewModel.selectedPerson == null) return;

    // 創建行運盤數據
    final chartData = ChartData(
      chartType: ChartType.transit,
      primaryPerson: viewModel.selectedPerson!,
      specificDate: DateTime.now(),
    );

    // 導航到 AI 解讀結果頁面
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AIInterpretationResultPage(
          chartData: chartData,
          interpretationTitle: '今日運勢分析',
          subtitle: '基於 ${viewModel.selectedPerson!.name} 的行運盤',
          suggestedQuestions: [
            _buildWeatherForecastPrompt(),
          ],
          autoExecuteFirstQuestion: true,
        ),
      ),
    );
  }
}
