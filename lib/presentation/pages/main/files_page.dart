import 'dart:convert';

import 'package:crypto/crypto.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:provider/provider.dart';

import '../../../astreal.dart';
import '../../../data/services/api/astrology_service.dart';
import '../../../data/services/api/firebase_storage_backup_service.dart';
import '../../../features/astrology/models/chart_filter.dart';
import '../../../shared/utils/csv_helper.dart';
import '../../../shared/widgets/birth_data_card.dart';
import '../../../shared/widgets/common/responsive_wrapper.dart';
import '../../../shared/widgets/csv_import_loading_dialog.dart';
import '../../../shared/widgets/recent_persons_section.dart';
import '../../viewmodels/chart_filter_viewmodel.dart';
import '../../viewmodels/files_viewmodel.dart';
import '../../viewmodels/recent_persons_viewmodel.dart';
import '../birth_data_form_page.dart';
import '../chart_filter_page.dart';
import '../chart_page.dart';
import '../chart_selection_page.dart';
import '../sort_selector_page.dart';


class FilesPage extends StatefulWidget {
  final bool isSelectionMode;

  const FilesPage({
    super.key,
    this.isSelectionMode = false,
  });

  @override
  State<FilesPage> createState() => _FilesPageState();
}

class _FilesPageState extends State<FilesPage> {
  late FilesViewModel _viewModel;
  bool _importDialogShown = false;
  late ScrollController _scrollController;
  bool _isFabVisible = true;

  // 星盤資料快取機制
  List<ChartData>? _cachedChartData;
  String? _cachedDataHash;
  DateTime? _lastCacheTime;
  final Map<String, ChartData> _chartDataMap = {}; // 按ID快取單個星盤資料
  final Map<String, String> _birthDataHashMap = {}; // 按ID快取出生資料雜湊值
  static const Duration _cacheValidDuration = Duration(minutes: 30);
  bool _isNavigatingToChartFilter = false; // 標記是否正在導航到篩選器

  @override
  void initState() {
    super.initState();
    // 在 initState 中獲取 ViewModel
    _viewModel = Provider.of<FilesViewModel>(context, listen: false);
    // 初始化滑動控制器
    _scrollController = ScrollController();
    _scrollController.addListener(_onScroll);
    // 延遲載入出生資料，避免在 build 階段調用 notifyListeners
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadData();
    });
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _clearCache(); // 清除快取
    super.dispose();
  }

  @override
  void didChangeDependencies() {
    print('🔍 FilesPage: didChangeDependencies 被調用');
    super.didChangeDependencies();
  }

  @override
  void didUpdateWidget(FilesPage oldWidget) {
    print('🔍 FilesPage: didUpdateWidget 被調用');
    super.didUpdateWidget(oldWidget);
  }

  /// 滑動監聽
  void _onScroll() {
    if (_scrollController.position.userScrollDirection ==
        ScrollDirection.reverse) {
      // 向上滑動，隱藏 FAB
      if (_isFabVisible) {
        setState(() {
          _isFabVisible = false;
        });
      }
    } else if (_scrollController.position.userScrollDirection ==
        ScrollDirection.forward) {
      // 向下滑動，顯示 FAB
      if (!_isFabVisible) {
        setState(() {
          _isFabVisible = true;
        });
      }
    }
  }

  /// 載入資料
  Future<void> _loadData() async {
    await _viewModel.loadBirthData();
    // 只有在初始化時才清除快取，避免不必要的快取清除
    // _clearCache(); // 移除自動清除快取
  }

  // 顯示排序和過濾選項選擇器
  void _showSortOptionsDialog() async {
    final result = await SortSelectorPage.show(
      context: context,
      currentSortOption: _viewModel.currentSortOption,
      currentCategoryFilter: _viewModel.categoryFilter,
    );

    if (result != null) {
      // 如果有排序選項變更
      if (result.sortOption != null) {
        await _viewModel.setSortOption(result.sortOption!);
      }

      // 如果有類別過濾變更
      if (result.categoryFilter != null) {
        _viewModel.setCategoryFilter(result.categoryFilter!);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // 使用 AnimatedBuilder 監聽 ViewModel 的變化
    return AnimatedBuilder(
      animation: _viewModel,
      builder: (context, child) {
        // 管理匯入對話框的顯示和隱藏
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _manageImportDialog();
        });

        if (_viewModel.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        return Consumer<ThemeProvider>(
          builder: (context, themeProvider, child) {
            final isStarlight = themeProvider.userMode == 'starlight';
            final primaryColor = isStarlight ? AppColors.solarAmber : AppColors.royalIndigo;
            final backgroundColor = isStarlight ? AppColors.lightCornsilk : AppColors.pastelSkyBlue;

            return Scaffold(
              appBar: AppBar(
                backgroundColor: backgroundColor,
                title: Row(
                  children: [
                    Expanded(
                      child: Text(
                        _viewModel.isMultiSelectMode
                            ? _viewModel.selectedItems.length == 2
                                ? '已選擇 2 個人'
                                : '已選擇 ${_viewModel.selectedItems.length} 個'
                            : '出生資料',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: primaryColor,
                        ),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                    ),
                  ],
                ),
                actions: [
                  if (!_viewModel.isMultiSelectMode) ...[
                    // 星盤篩選器按鈕
                    IconButton(
                      icon: Icon(Icons.filter_list, color: primaryColor),
                      tooltip: '星盤篩選器',
                      onPressed: _openChartFilter,
                    ),
                    // 多選模式按鈕
                    IconButton(
                      icon: Icon(Icons.checklist, color: primaryColor),
                      tooltip: '多選模式',
                      onPressed: () {
                        _viewModel.toggleMultiSelectMode();
                      },
                    ),
                    // 更多選項按鈕
                    PopupMenuButton<String>(
                      icon: Icon(Icons.more_vert, color: primaryColor),
                      tooltip: '更多選項',
                      onSelected: (value) {
                        switch (value) {
                          case 'import':
                            _importFromCsv();
                            break;
                          case 'export':
                            _exportToCsv();
                            break;
                          case 'backup_cloud':
                            _backupToCloud();
                            break;
                          case 'restore_cloud':
                            _restoreFromCloud();
                            break;
                        }
                      },
                      itemBuilder: (context) => [
                        PopupMenuItem(
                          value: 'import',
                          child: Row(
                            children: [
                              Icon(Icons.file_download, color: primaryColor),
                              const SizedBox(width: 12),
                              const Text('從 CSV 匯入'),
                            ],
                          ),
                        ),
                        PopupMenuItem(
                          value: 'export',
                          child: Row(
                            children: [
                              Icon(Icons.file_upload, color: primaryColor),
                              const SizedBox(width: 12),
                              const Text('匯出為 CSV'),
                            ],
                          ),
                        ),
                        const PopupMenuDivider(),
                        PopupMenuItem(
                          value: 'backup_cloud',
                          child: Row(
                            children: [
                              Icon(Icons.cloud_upload, color: primaryColor),
                              const SizedBox(width: 12),
                              const Text('備份到雲端'),
                            ],
                          ),
                        ),
                        PopupMenuItem(
                          value: 'restore_cloud',
                          child: Row(
                            children: [
                              Icon(Icons.cloud_download, color: primaryColor),
                              const SizedBox(width: 12),
                              const Text('從雲端還原'),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ] else ...[
                    // 全選按鈕
                    IconButton(
                      icon: Icon(Icons.select_all, color: primaryColor),
                      tooltip: '全選',
                      onPressed: () {
                        _viewModel.toggleSelectAll();
                      },
                    ),
                    // 刪除按鈕
                    IconButton(
                      icon: Icon(Icons.delete, color: primaryColor),
                      tooltip: '刪除所選項目',
                  onPressed: _viewModel.selectedItems.isEmpty
                      ? null
                      : () {
                          _confirmDeleteSelected();
                        },
                ),
                // 退出多選按鈕
                IconButton(
                  icon: const Icon(Icons.close, color: AppColors.royalIndigo),
                  tooltip: '退出多選',
                  onPressed: () {
                    _viewModel.toggleMultiSelectMode();
                  },
                ),
              ],
            ],
          ),
          body: ResponsivePageWrapper(
            maxWidth: 800.0, // 檔案管理頁面適合中等寬度
            child: Stack(
              children: [
                _buildContentView(),
                // 底部總計人數顯示
                Positioned(
                  bottom: 0,
                  left: 0,
                  right: 0,
                  child: _buildBottomTotalCountSection(),
                ),
              ],
            ),
          ),
          floatingActionButton: AnimatedSlide(
            duration: const Duration(milliseconds: 200),
            offset: _isFabVisible ? Offset.zero : const Offset(0, 2),
            child: AnimatedOpacity(
              duration: const Duration(milliseconds: 200),
              opacity: _isFabVisible ? 1.0 : 0.0,
              child: _viewModel.isMultiSelectMode &&
                      _viewModel.selectedItems.length == 2
                  ? FloatingActionButton.extended(
                      onPressed: () {
                        _navigateToChartSelection();
                      },
                      tooltip: '選擇星盤類型',
                      icon: const Icon(Icons.auto_graph),
                      label: const Text(
                        '選擇星盤',
                        overflow: TextOverflow.ellipsis,
                      ),
                      backgroundColor: AppColors.royalIndigo,
                    )
                  : FloatingActionButton(
                      onPressed: () {
                        _showAddBirthDataDialog();
                      },
                      tooltip: '新增出生資料',
                      child: const Icon(Icons.add),
                    ),
            ),
          ));
        },
      );
    });
  }

  /// 構建內容視圖
  Widget _buildContentView() {
    // 如果有搜尋結果但為空，顯示無搜尋結果狀態
    if (_viewModel.searchController.text.isNotEmpty &&
        _viewModel.filteredList.isEmpty) {
      return _buildNoSearchResultsState();
    }

    // 如果沒有任何資料，顯示空狀態
    if (_viewModel.birthDataList.isEmpty) {
      return _buildEmptyState();
    }

    return Column(
      children: [
        // 搜尋和篩選區域
        _buildSearchAndFilterSection(),

        // 近期選中人物區域
        _buildRecentPersonsSection(),

        // 資料列表
        Expanded(
          child: RefreshIndicator(
            onRefresh: () async {
              logger.d('用戶手動刷新資料');
              await _viewModel.loadBirthData();
              // 只有在不是導航期間才清除快取
              if (!_isNavigatingToChartFilter) {
                print('🔍 手動刷新，清除快取');
                _clearCache();
              } else {
                print('🔍 導航期間，跳過清除快取');
              }
            },
            // 增加觸發距離，避免意外觸發
            displacement: 60.0,
            strokeWidth: 2.0,
            child: ListView.builder(
              controller: _scrollController,
              padding: const EdgeInsets.fromLTRB(16, 8, 16, 60), // 底部增加空間給總計人數
              itemCount: _viewModel.filteredList.length,
              itemBuilder: (context, index) {
                final data = _viewModel.filteredList[index];
                return BirthDataCard(
                  data: data,
                  index: index,
                  isSelected: _viewModel.selectedItems.contains(data.id),
                  isMultiSelectMode: _viewModel.isMultiSelectMode,
                  isSelectionMode: widget.isSelectionMode,
                  onToggleSelection: (id) => _viewModel.toggleItemSelection(id),
                  onLongPress: () {
                    if (!_viewModel.isMultiSelectMode) {
                      _viewModel.toggleMultiSelectMode();
                      _viewModel.toggleItemSelection(data.id);
                    }
                  },
                  onEdit: (data) => _showEditBirthDataDialog(data, index),
                  onDelete: (id) => _confirmDelete(id),
                );
              },
            ),
          ),
        ),
      ],
    );
  }

  /// 構建搜尋和篩選區域
  Widget _buildSearchAndFilterSection() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          // 搜尋框
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16.0),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: TextField(
                controller: _viewModel.searchController,
                decoration: InputDecoration(
                  hintText: '搜尋出生資料...',
                  hintStyle: const TextStyle(color: AppColors.textMedium),
                  prefixIcon:
                      const Icon(Icons.search, color: AppColors.royalIndigo),
                  suffixIcon: _viewModel.searchController.text.isNotEmpty
                      ? IconButton(
                          icon: const Icon(Icons.clear,
                              color: AppColors.royalIndigo),
                          onPressed: () {
                            _viewModel.clearSearch();
                          },
                        )
                      : null,
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(
                      vertical: 14.0, horizontal: 16.0),
                ),
                onChanged: (_) {
                  // 搜尋時重新篩選資料夾數據
                  _filterCurrentFolderData();
                },
              ),
            ),
          ),
          // 排序按鈕
          if (!_viewModel.isMultiSelectMode) ...[
            const SizedBox(width: 8),
            Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16.0),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: IconButton(
                icon: const Icon(Icons.sort, color: AppColors.royalIndigo),
                tooltip: '排序',
                onPressed: _showSortOptionsDialog,
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// 構建底部總計人數區域
  Widget _buildBottomTotalCountSection() {
    final totalCount = _viewModel.filteredList.length;
    final isFiltered = _viewModel.searchController.text.isNotEmpty ||
        _viewModel.categoryFilter.length != ChartCategory.values.length;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Center(
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: AppColors.softGray.withValues(alpha: 0.3),
            borderRadius: BorderRadius.circular(20),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.people,
                size: 16,
                color: AppColors.textMedium,
              ),
              const SizedBox(width: 8),
              Flexible(
                child: Text(
                  isFiltered
                      ? '篩選結果：$totalCount 筆 (總計 ${_viewModel.birthDataList.length} 筆)'
                      : '總計 $totalCount 筆',
                  style: const TextStyle(
                    fontSize: 14,
                    color: AppColors.textMedium,
                    fontWeight: FontWeight.w500,
                  ),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 構建近期選中人物區域
  Widget _buildRecentPersonsSection() {
    return Container(
      margin: const EdgeInsets.fromLTRB(16, 0, 16, 0),
      child: RecentPersonsSection(
        allPersons: _viewModel.birthDataList,
        onPersonSelected: _selectRecentPerson,
        maxCount: 5,
        showClearButton: true,
        hideWhenSearching: true,
        searchText: _viewModel.searchController.text,
        title: '近期選中',
        icon: Icons.history,
        themeColor: AppColors.royalIndigo,
        showSelectedState: false,
      ),
    );
  }

  /// 篩選當前資料夾數據
  void _filterCurrentFolderData() {
    setState(() {
      // 觸發重建以更新顯示的資料
    });
  }

  /// 選擇近期人物
  Future<void> _selectRecentPerson(BirthData person) async {
    // 記錄選中的人物
    try {
      final recentPersonsViewModel =
          Provider.of<RecentPersonsViewModel>(context, listen: false);
      recentPersonsViewModel.recordSelectedPerson(person);
    } catch (e) {
      // 如果記錄失敗，不影響選擇功能
    }

    // 更新最後訪問時間（暫時移除，因為 FilesViewModel 中沒有此方法）
    // await _viewModel.updateLastAccessedTime(person.id);

    if (widget.isSelectionMode) {
      // 選擇模式：返回選中的出生資料
      Navigator.pop(context, person);
    } else {
      // 直接進入基本星盤
      final chartData = ChartData(
        chartType: ChartType.natal,
        primaryPerson: person,
      );

      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => ChangeNotifierProvider(
            create: (_) =>
                ChartViewModel.withChartData(initialChartData: chartData),
            child: ChartPage(chartData: chartData),
          ),
        ),
      );
    }
  }

  /// 切換最愛狀態
  Future<void> _toggleFavorite(BirthData data) async {
    try {
      // 暫時移除最愛功能，因為 FilesViewModel 中沒有此方法
      // await _viewModel.toggleFavorite(data.id);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('最愛功能暫時不可用'),
            duration: Duration(seconds: 1),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('操作失敗: $e')),
        );
      }
    }
  }

  /// 構建空狀態
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 120,
            height: 120,
            decoration: const BoxDecoration(
              color: AppColors.softGray,
              shape: BoxShape.circle,
            ),
            child: const Icon(Icons.folder_open,
                size: 60, color: AppColors.textMedium),
          ),
          const SizedBox(height: 24),
          const Text(
            '沒有出生資料',
            style: TextStyle(
                fontSize: 22,
                fontWeight: FontWeight.bold,
                color: AppColors.textDark),
          ),
          const SizedBox(height: 12),
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 32),
            child: Text(
              '點擊下方按鈕新增第一筆出生資料',
              style: TextStyle(fontSize: 16, color: AppColors.textMedium),
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(height: 32),
          ElevatedButton.icon(
            icon: const Icon(Icons.add),
            label: const Text('新增出生資料'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.royalIndigo,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
            ),
            onPressed: () {
              _showAddBirthDataDialog();
            },
          ),
        ],
      ),
    );
  }

  /// 構建無搜尋結果狀態
  Widget _buildNoSearchResultsState() {
    return Column(
      children: [
        // 搜尋和篩選區域
        _buildSearchAndFilterSection(),

        // 無搜索結果提示
        Expanded(
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                Container(
                  width: 120,
                  height: 120,
                  decoration: const BoxDecoration(
                    color: AppColors.softGray,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(Icons.search_off,
                      size: 60, color: AppColors.textMedium),
                ),
                const SizedBox(height: 24),
                const Text(
                  '沒有符合的搜索結果',
                  style: TextStyle(
                      fontSize: 22,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textDark),
                ),
                const SizedBox(height: 12),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 32.0),
                  child: Text(
                    '沒有找到包含 "${_viewModel.searchController.text}" 的出生資料',
                    style: const TextStyle(
                        fontSize: 16, color: AppColors.textMedium),
                    textAlign: TextAlign.center,
                  ),
                ),
                const SizedBox(height: 32),
                ElevatedButton.icon(
                  icon: const Icon(Icons.clear),
                  label: const Text('清除搜索'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.royalIndigo,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                        horizontal: 24, vertical: 12),
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12)),
                  ),
                  onPressed: () {
                    _viewModel.clearSearch();
                  },
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  void _confirmDelete(String id) {
    // 找到要刪除的項目名稱，用於顯示確認訊息
    final data = _viewModel.filteredList.firstWhere((data) => data.id == id,
        orElse: () => BirthData(
              id: '',
              name: '',
              dateTime: DateTime.now(),
              birthPlace: '',
              latitude: 0,
              longitude: 0,
            ));

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          title: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.error.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: const Icon(Icons.delete, color: AppColors.error),
              ),
              const SizedBox(width: 12),
              const Text('確認刪除'),
            ],
          ),
          content: Text(
            '確定要刪除 ${data.name} 的出生資料嗎？此操作無法撤銷。',
            style: const TextStyle(fontSize: 16),
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('取消'),
              style:
                  TextButton.styleFrom(foregroundColor: AppColors.textMedium),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.error,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8)),
              ),
              child: const Text('刪除'),
              onPressed: () async {
                await _viewModel.deleteBirthData(id);

                Navigator.of(context).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('${data.name} 的出生資料已刪除'),
                    backgroundColor: AppColors.royalIndigo,
                  ),
                );
              },
            ),
          ],
        );
      },
    );
  }

  // 導航到星盤選擇頁面
  void _navigateToChartSelection() {
    if (_viewModel.selectedItems.length != 2) return;

    // 獲取選中的兩個人
    final selectedPeople = _viewModel.birthDataList
        .where((data) => _viewModel.selectedItems.contains(data.id))
        .toList();

    if (selectedPeople.length != 2) return;

    // 將第一個人設為主要人物，第二個人設為次要人物
    final primaryPerson = selectedPeople[0];
    final secondaryPerson = selectedPeople[1];

    // 導航到星盤選擇頁面，並傳遞兩個人的資料
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ChartSelectionPage(
          primaryPerson: primaryPerson,
          secondaryPerson: secondaryPerson,
        ),
      ),
    );

    // 退出多選模式
    _viewModel.toggleMultiSelectMode();
  }

  void _confirmDeleteSelected() {
    if (_viewModel.selectedItems.isEmpty) return;

    final selectedCount = _viewModel.selectedItems.length;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          title: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.error.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: const Icon(Icons.delete, color: AppColors.error),
              ),
              const SizedBox(width: 12),
              const Text('確認刪除'),
            ],
          ),
          content: Text(
            '確定要刪除所選的 $selectedCount 條出生資料嗎？此操作無法撤銷。',
            style: const TextStyle(fontSize: 16),
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('取消'),
              style:
                  TextButton.styleFrom(foregroundColor: AppColors.textMedium),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.error,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8)),
              ),
              child: const Text('刪除'),
              onPressed: () async {
                final deletedCount = await _viewModel.deleteSelectedBirthData();

                Navigator.of(context).pop();

                // 顯示成功訊息
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('已刪除 $deletedCount 條出生資料'),
                    backgroundColor: AppColors.royalIndigo,
                  ),
                );
              },
            ),
          ],
        );
      },
    );
  }

  void _showAddBirthDataDialog() async {
    // 使用 BirthDataFormPage 頁面來新增出生資料
    final result = await Navigator.push<BirthData>(
      context,
      MaterialPageRoute(
        builder: (context) => const BirthDataFormPage(),
      ),
    );

    // 如果用戶返回了出生資料，則添加到列表中並進入星盤頁面
    if (result != null) {
      await _viewModel.addBirthData(result);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('出生資料已儲存')),
        );

        // 自動進入星盤頁面
        _selectRecentPerson(result);
      }
    }
  }

  void _showEditBirthDataDialog(BirthData data, int displayIndex) async {
    // 使用 BirthDataFormPage 頁面來編輯出生資料
    final result = await Navigator.push<BirthData>(
      context,
      MaterialPageRoute(
        builder: (context) => BirthDataFormPage(initialData: data),
      ),
    );

    // 如果用戶返回了更新後的出生資料，則更新列表
    if (result != null) {
      await _viewModel.updateBirthData(data.id, result);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('出生資料已更新')),
        );
      }
    }
  }

  // 匯出出生資料到 CSV 文件
  Future<void> _exportToCsv() async {
    if (_viewModel.birthDataList.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('沒有可匯出的出生資料')),
      );
      return;
    }

    try {
      // 顯示加載指示器
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return const AlertDialog(
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text('正在匯出資料...'),
              ],
            ),
          );
        },
      );

      // 匯出數據
      final filePath = await _viewModel.exportToCsv();

      // 關閉加載對話框
      if (mounted) Navigator.of(context).pop();

      if (filePath != null && filePath.isNotEmpty) {
        // 顯示分享選項
        if (mounted) {
          showDialog(
            context: context,
            builder: (BuildContext context) {
              return AlertDialog(
                title: const Text('匯出成功'),
                content: const Text('出生資料已成功匯出為 CSV 文件。您想要分享這個文件嗎？'),
                actions: [
                  TextButton(
                    child: const Text('取消'),
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                  ),
                  TextButton(
                    child: const Text('分享'),
                    onPressed: () async {
                      Navigator.of(context).pop();
                      await CsvHelper.shareCsvFile(filePath);
                    },
                  ),
                ],
              );
            },
          );
        }
      }
    } catch (e) {
      // 關閉加載對話框
      if (mounted) Navigator.of(context).pop();

      // 顯示錯誤訊息
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('匯出資料時出錯: $e')),
        );
      }
      logger.e('匯出資料時出錯: $e');
    }
  }

  // 管理匯入對話框的顯示和隱藏
  void _manageImportDialog() {
    if (_viewModel.isImporting && !_importDialogShown) {
      _showImportDialog();
    } else if (!_viewModel.isImporting && _importDialogShown) {
      _hideImportDialog();
    }
  }

  // 顯示匯入對話框
  void _showImportDialog() {
    if (!_importDialogShown && mounted) {
      _importDialogShown = true;
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AnimatedBuilder(
          animation: _viewModel,
          builder: (context, child) => CsvImportLoadingDialog(
            status: _viewModel.importStatus,
            progress: _viewModel.importProgress,
            total: _viewModel.importTotal,
            progressPercent: _viewModel.importProgressPercent,
          ),
        ),
      );
    }
  }

  // 隱藏匯入對話框
  void _hideImportDialog() {
    if (_importDialogShown && mounted) {
      _importDialogShown = false;
      Navigator.of(context).pop();
    }
  }

  /// 開啟星盤篩選器（簡化版本 - 計算邏輯移到 ChartFilterPage）
  void _openChartFilter() async {
    try {
      print('🔍 FilesPage: _openChartFilter 被調用');
      logger.i('=== 開始開啟星盤篩選器 ===');

      // 檢查是否有資料
      if (_viewModel.birthDataList.isEmpty) {
        logger.w('沒有出生資料可以篩選');
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('沒有出生資料可以篩選'),
            backgroundColor: Colors.orange,
          ),
        );
        return;
      }

      // 限制處理的數量，避免計算過多導致性能問題
      final maxCharts = 50;
      final birthDataToProcess = _viewModel.birthDataList.length > maxCharts
          ? _viewModel.birthDataList.sublist(0, maxCharts)
          : _viewModel.birthDataList;

      logger.i('準備處理 ${birthDataToProcess.length} 個出生資料');

      // 直接導航到星盤篩選器頁面，將出生資料和快取資訊傳遞過去
      final result = await Navigator.push<ChartFilter>(
        context,
        MaterialPageRoute(
          builder: (context) => ChangeNotifierProvider(
            create: (_) => ChartFilterViewModel(),
            child: ChartFilterPage(
              birthDataToProcess: birthDataToProcess,
              cachedChartData: _cachedChartData,
              cachedDataHash: _cachedDataHash,
              chartDataMap: Map.from(_chartDataMap),
              birthDataHashMap: Map.from(_birthDataHashMap),
            ),
          ),
        ),
      );

      // 如果用戶選擇了篩選器，可以在這裡處理結果
      if (result != null && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('已應用篩選器：${result.name ?? '未命名篩選器'}'),
            backgroundColor: AppColors.royalIndigo,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('開啟星盤篩選器時出錯: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
      logger.e('開啟星盤篩選器時出錯: $e');
    }
  }

  /// 導航到星盤篩選器頁面
  Future<void> _navigateToChartFilter(List<ChartData> chartData) async {
    logger.d('導航到星盤篩選器，傳入 ${chartData.length} 個星盤');

    // 設置導航標記，保護快取
    _isNavigatingToChartFilter = true;
    print('🔍 設置導航標記，保護快取');

    try {
      final result = await Navigator.push<ChartFilter>(
        context,
        MaterialPageRoute(
          builder: (context) => ChangeNotifierProvider(
            create: (_) => ChartFilterViewModel(),
            child: ChartFilterPage(
              initialCharts: chartData,
            ),
          ),
        ),
      );

      // 處理篩選結果
      if (result != null && mounted) {
        logger.d('用戶應用了篩選器：${result.name}');
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('已應用篩選器：${result.name ?? '未命名篩選器'}'),
            backgroundColor: AppColors.royalIndigo,
          ),
        );
      } else {
        logger.d('用戶從星盤篩選器返回，沒有應用篩選器');
        print('🔍 用戶正常返回，快取應該保持有效');
      }
    } finally {
      // 清除導航標記
      _isNavigatingToChartFilter = false;
      print('🔍 清除導航標記');
    }
  }

  /// 生成單個出生資料的雜湊值
  String _generateSingleBirthDataHash(BirthData data) {
    final dataString = '${data.id}_${data.name}_${data.dateTime.millisecondsSinceEpoch}_'
                      '${data.birthPlace}_${data.latitude}_${data.longitude}_'
                      '${data.notes ?? ''}_${data.createdAt.millisecondsSinceEpoch}';

    final bytes = utf8.encode(dataString);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// 生成出生資料列表的雜湊值，用於檢測整體資料是否有變動
  String _generateBirthDataHash(List<BirthData> birthDataList) {
    final dataString = birthDataList.map((data) {
      return '${data.id}_${data.name}_${data.dateTime.millisecondsSinceEpoch}_'
             '${data.birthPlace}_${data.latitude}_${data.longitude}_'
             '${data.notes ?? ''}_${data.createdAt.millisecondsSinceEpoch}';
    }).join('|');

    final bytes = utf8.encode(dataString);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// 檢查快取是否有效
  bool _isCacheValid(String currentHash) {
    print('🔍 _isCacheValid 被調用');
    print('🔍 _cachedChartData == null: ${_cachedChartData == null}');
    print('🔍 _cachedDataHash == null: ${_cachedDataHash == null}');
    print('🔍 _lastCacheTime == null: ${_lastCacheTime == null}');

    if (_cachedChartData == null || _cachedDataHash == null || _lastCacheTime == null) {
      print('🔍 快取檢查失敗：快取資料不存在');
      logger.i('快取檢查失敗：快取資料不存在');
      return false;
    }

    // 檢查資料是否有變動
    if (_cachedDataHash != currentHash) {
      logger.i('快取檢查失敗：資料雜湊值不匹配');
      logger.i('快取雜湊值: $_cachedDataHash');
      logger.i('當前雜湊值: $currentHash');
      print('🔍 快取失效：雜湊值不匹配');
      print('🔍 快取: $_cachedDataHash');
      print('🔍 當前: $currentHash');
      return false;
    }

    // 檢查快取是否過期
    final now = DateTime.now();
    final timeDiff = now.difference(_lastCacheTime!);
    if (timeDiff > _cacheValidDuration) {
      logger.i('快取檢查失敗：快取已過期 (${timeDiff.inMinutes} 分鐘)');
      return false;
    }

    logger.i('快取檢查成功：使用有效快取 (${_cachedChartData!.length} 個星盤)');
    return true;
  }

  /// 更新快取
  void _updateCache(List<ChartData> chartData, String dataHash) {
    print('🔍 _updateCache 被調用，更新 ${chartData.length} 個星盤');
    print('🔍 新的雜湊值: $dataHash');
    _cachedChartData = List.from(chartData); // 創建副本避免引用問題
    _cachedDataHash = dataHash;
    _lastCacheTime = DateTime.now();
    print('🔍 快取更新完成');
    logger.i('星盤資料快取已更新，共 ${chartData.length} 個星盤');
  }

  /// 檢測變更的出生資料
  List<BirthData> _getChangedBirthData(List<BirthData> currentBirthDataList) {
    final changedData = <BirthData>[];

    for (final birthData in currentBirthDataList) {
      final currentHash = _generateSingleBirthDataHash(birthData);
      final cachedHash = _birthDataHashMap[birthData.id];

      // 如果沒有快取或雜湊值不同，表示資料有變更
      if (cachedHash == null || cachedHash != currentHash) {
        changedData.add(birthData);
      }
    }

    return changedData;
  }

  /// 檢測已刪除的出生資料ID
  List<String> _getDeletedBirthDataIds(List<BirthData> currentBirthDataList) {
    final currentIds = currentBirthDataList.map((data) => data.id).toSet();
    final cachedIds = _birthDataHashMap.keys.toSet();

    // 找出在快取中但不在當前列表中的ID（已刪除的資料）
    return cachedIds.difference(currentIds).toList();
  }

  /// 執行增量更新
  Future<void> _performIncrementalUpdate(
    List<BirthData> allBirthData,
    List<BirthData> changedBirthData,
    List<String> deletedIds,
    String newHash,
  ) async {
    print('🔍 FilesPage: _performIncrementalUpdate 被調用 - 這會重新計算部分星盤！');
    // 顯示增量更新的進度對話框
    final progressNotifier = ValueNotifier<double>(0.0);
    final statusNotifier = ValueNotifier<String>('正在增量更新星盤資料...');

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const CircularProgressIndicator(),
              const SizedBox(height: 16),
              ValueListenableBuilder<String>(
                valueListenable: statusNotifier,
                builder: (context, status, child) => Text(status),
              ),
              const SizedBox(height: 8),
              ValueListenableBuilder<double>(
                valueListenable: progressNotifier,
                builder: (context, progress, child) => LinearProgressIndicator(value: progress),
              ),
            ],
          ),
        );
      },
    );

    try {
      // 移除已刪除的資料
      for (final deletedId in deletedIds) {
        _chartDataMap.remove(deletedId);
        _birthDataHashMap.remove(deletedId);
      }

      // 計算變更的星盤資料
      if (changedBirthData.isNotEmpty) {
        final updatedCharts = await _calculateChartsWithProgress(
          changedBirthData,
          progressNotifier,
          statusNotifier,
        );

        // 更新快取
        for (final chart in updatedCharts) {
          _chartDataMap[chart.primaryPerson.id] = chart;
          _birthDataHashMap[chart.primaryPerson.id] = _generateSingleBirthDataHash(chart.primaryPerson);
        }
      }

      // 重建完整的星盤列表
      final allCharts = <ChartData>[];
      for (final birthData in allBirthData) {
        final chart = _chartDataMap[birthData.id];
        if (chart != null) {
          allCharts.add(chart);
        }
      }

      // 更新整體快取
      _updateCache(allCharts, newHash);

      // 關閉進度對話框
      if (mounted && Navigator.canPop(context)) {
        Navigator.of(context).pop();
      }

      logger.i('增量更新完成：更新 ${changedBirthData.length} 個，刪除 ${deletedIds.length} 個，總計 ${allCharts.length} 個星盤');

      // 導航到篩選器頁面
      await _navigateToChartFilter(allCharts);

    } catch (e) {
      if (mounted && Navigator.canPop(context)) {
        Navigator.of(context).pop();
      }
      logger.e('增量更新失敗: $e');
      // 降級到完整計算
      await _performFullCalculation(allBirthData, newHash);
    }
  }

  /// 執行完整計算
  Future<void> _performFullCalculation(List<BirthData> birthDataList, String newHash) async {
    print('🔍 FilesPage: _performFullCalculation 被調用 - 這會重新計算星盤！');
    // 顯示完整計算的進度對話框
    final progressNotifier = ValueNotifier<double>(0.0);
    final statusNotifier = ValueNotifier<String>('正在計算星盤資料...');

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const CircularProgressIndicator(),
              const SizedBox(height: 16),
              ValueListenableBuilder<String>(
                valueListenable: statusNotifier,
                builder: (context, status, child) => Text(status),
              ),
              const SizedBox(height: 8),
              ValueListenableBuilder<double>(
                valueListenable: progressNotifier,
                builder: (context, progress, child) => Column(
                  children: [
                    LinearProgressIndicator(value: progress),
                    const SizedBox(height: 4),
                    Text('${(progress * 100).toInt()}%'),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );

    try {
      // 計算所有星盤資料
      final charts = await _calculateChartsWithProgress(
        birthDataList,
        progressNotifier,
        statusNotifier,
      );

      // 清空舊快取
      _chartDataMap.clear();
      _birthDataHashMap.clear();

      // 建立新的快取
      for (final chart in charts) {
        _chartDataMap[chart.primaryPerson.id] = chart;
        _birthDataHashMap[chart.primaryPerson.id] = _generateSingleBirthDataHash(chart.primaryPerson);
      }

      // 更新整體快取
      _updateCache(charts, newHash);

      // 關閉進度對話框
      if (mounted && Navigator.canPop(context)) {
        Navigator.of(context).pop();
      }

      logger.i('完整計算完成，共 ${charts.length} 個星盤');

      // 導航到篩選器頁面
      await _navigateToChartFilter(charts);

    } catch (e) {
      if (mounted && Navigator.canPop(context)) {
        Navigator.of(context).pop();
      }
      rethrow; // 重新拋出異常讓上層處理
    }
  }

  /// 清除快取
  void _clearCache() {
    print('🔍 _clearCache 被調用 - 這會清除所有快取！');
    print('🔍 調用堆疊: ${StackTrace.current}');
    _cachedChartData = null;
    _cachedDataHash = null;
    _lastCacheTime = null;
    _chartDataMap.clear();
    _birthDataHashMap.clear();
    logger.i('星盤資料快取已清除');
  }

  /// 帶進度的星盤計算方法
  Future<List<ChartData>> _calculateChartsWithProgress(
    List<BirthData> birthDataList,
    ValueNotifier<double> progressNotifier,
    ValueNotifier<String> statusNotifier,
  ) async {
    final charts = <ChartData>[];
    final total = birthDataList.length;

    // 載入圖表設定
    statusNotifier.value = '載入設定...';
    final chartSettings = await ChartSettings.loadFromPrefs();
    final astrologyService = AstrologyService();

    // 批量處理，每批處理5個
    const batchSize = 5;
    final batches = <List<BirthData>>[];

    for (int i = 0; i < birthDataList.length; i += batchSize) {
      final end = (i + batchSize < birthDataList.length)
          ? i + batchSize
          : birthDataList.length;
      batches.add(birthDataList.sublist(i, end));
    }

    int processedCount = 0;

    for (int batchIndex = 0; batchIndex < batches.length; batchIndex++) {
      final batch = batches[batchIndex];

      // 並行處理當前批次
      final futures = batch.map((birthData) async {
        try {
          // 創建基本ChartData
          final chartData = ChartData(
            chartType: ChartType.natal,
            primaryPerson: birthData,
          );

          // 計算星盤資料
          final calculatedChart = await astrologyService.calculateChartData(
            chartData,
            chartSettings: chartSettings,
          );

          return calculatedChart;
        } catch (e) {
          logger.w('計算星盤資料失敗: ${birthData.name}, 錯誤: $e');
          return null;
        }
      }).toList();

      // 等待當前批次完成
      statusNotifier.value = '正在處理第 ${batchIndex + 1}/${batches.length} 批資料...';
      final results = await Future.wait(futures);

      // 添加成功的結果
      for (final result in results) {
        if (result != null) {
          charts.add(result);
        }
        processedCount++;

        // 更新進度
        progressNotifier.value = processedCount / total;
      }

      // 批次間短暫延遲，讓UI更新
      if (batchIndex < batches.length - 1) {
        await Future.delayed(const Duration(milliseconds: 50));
      }
    }

    statusNotifier.value = '計算完成！成功處理 ${charts.length}/$total 個星盤';
    await Future.delayed(const Duration(milliseconds: 500)); // 讓用戶看到完成狀態

    return charts;
  }

  // 從 CSV 文件匯入出生資料
  Future<void> _importFromCsv() async {
    try {
      // 匯入數據
      final importedData = await _viewModel.importFromCsv();

      // 等待 Loading 對話框完全關閉
      await Future.delayed(const Duration(milliseconds: 300));

      if (importedData.isEmpty) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('沒有匯入任何資料')),
          );
        }
        return;
      }

      // 確認匯入
      if (mounted) {
        showDialog(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              title: Row(
                children: [
                  Icon(Icons.file_download, color: Colors.blue.shade600),
                  const SizedBox(width: 8),
                  const Text('確認匯入'),
                ],
              ),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('找到 ${importedData.length} 條出生資料'),
                  const SizedBox(height: 8),
                  const Text(
                    '請選擇匯入方式：',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
              actions: [
                TextButton(
                  child: const Text('取消'),
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                ),
                TextButton(
                  child: const Text('替換現有資料'),
                  onPressed: () async {
                    await _viewModel.replaceExistingData(importedData);

                    if (mounted) {
                      Navigator.of(context).pop();
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Row(
                            children: [
                              Icon(Icons.check_circle, color: Colors.white),
                              const SizedBox(width: 8),
                              Text('已匯入 ${importedData.length} 條出生資料並替換現有資料'),
                            ],
                          ),
                          backgroundColor: Colors.green,
                        ),
                      );
                    }
                  },
                ),
                TextButton(
                  child: const Text('合併到現有資料'),
                  onPressed: () async {
                    final newDataCount =
                        await _viewModel.mergeWithExistingData(importedData);

                    if (mounted) {
                      Navigator.of(context).pop();
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Row(
                            children: [
                              Icon(Icons.check_circle, color: Colors.white),
                              const SizedBox(width: 8),
                              Text('已合併 $newDataCount 條新出生資料'),
                            ],
                          ),
                          backgroundColor: Colors.green,
                        ),
                      );
                    }
                  },
                ),
              ],
            );
          },
        );
      }
    } catch (e) {
      // 等待 Loading 對話框完全關閉
      await Future.delayed(const Duration(milliseconds: 300));

      // 顯示錯誤訊息
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('匯入資料時出錯: $e')),
        );
      }
      logger.e('匯入資料時出錯: $e');
    }
  }

  /// 備份到雲端
  Future<void> _backupToCloud() async {
    if (_viewModel.birthDataList.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('沒有可備份的出生資料')),
      );
      return;
    }

    try {
      // 顯示備份進度對話框
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return const AlertDialog(
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text('正在備份到雲端...'),
              ],
            ),
          );
        },
      );

      // 執行備份
      final success = await FirebaseStorageBackupService.backupBirthData();

      // 關閉進度對話框
      if (mounted) Navigator.of(context).pop();

      if (success) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.check_circle, color: Colors.white),
                  const SizedBox(width: 8),
                  Text('已成功備份 ${_viewModel.birthDataList.length} 筆出生資料到雲端'),
                ],
              ),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('雲端備份失敗，請檢查網路連接和登入狀態'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      // 關閉進度對話框
      if (mounted) Navigator.of(context).pop();

      // 顯示錯誤訊息
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('備份時出錯: $e')),
        );
      }
      logger.e('雲端備份時出錯: $e');
    }
  }

  /// 從雲端還原
  Future<void> _restoreFromCloud() async {
    try {
      // 先檢查雲端備份狀態
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return const AlertDialog(
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text('正在檢查雲端備份...'),
              ],
            ),
          );
        },
      );

      final backupInfo = await FirebaseStorageBackupService.getBackupInfo();

      // 關閉檢查對話框
      if (mounted) Navigator.of(context).pop();

      if (backupInfo == null) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('沒有找到雲端備份，請先進行備份'),
              backgroundColor: Colors.orange,
            ),
          );
        }
        return;
      }

      // 顯示備份資訊和還原選項
      if (mounted) {
        final backupDate = DateTime.parse(backupInfo['lastModified']);
        final backupSize = backupInfo['size'] as int;

        showDialog(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              title: Row(
                children: [
                  Icon(Icons.cloud_download, color: Colors.blue.shade600),
                  const SizedBox(width: 8),
                  const Text('從雲端還原'),
                ],
              ),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('找到雲端備份：'),
                  const SizedBox(height: 8),
                  Text('備份時間：${_formatDateTime(backupDate)}'),
                  Text('檔案大小：${(backupSize / 1024).toStringAsFixed(1)} KB'),
                  const SizedBox(height: 16),
                  const Text(
                    '請選擇還原方式：',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
              actions: [
                TextButton(
                  child: const Text('取消'),
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                ),
                TextButton(
                  child: const Text('替換現有資料'),
                  onPressed: () async {
                    Navigator.of(context).pop();
                    await _performCloudRestore(replaceExisting: true);
                  },
                ),
                TextButton(
                  child: const Text('合併到現有資料'),
                  onPressed: () async {
                    Navigator.of(context).pop();
                    await _performCloudRestore(replaceExisting: false);
                  },
                ),
              ],
            );
          },
        );
      }
    } catch (e) {
      // 關閉檢查對話框
      if (mounted) Navigator.of(context).pop();

      // 顯示錯誤訊息
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('檢查雲端備份時出錯: $e')),
        );
      }
      logger.e('檢查雲端備份時出錯: $e');
    }
  }

  /// 執行雲端還原
  Future<void> _performCloudRestore({required bool replaceExisting}) async {
    try {
      // 顯示還原進度對話框
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return const AlertDialog(
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text('正在從雲端還原...'),
              ],
            ),
          );
        },
      );

      // 執行還原
      final success = await FirebaseStorageBackupService.restoreBirthData();

      // 關閉進度對話框
      if (mounted) Navigator.of(context).pop();

      if (success) {
        // 重新載入資料以確保 UI 更新
        await _viewModel.loadBirthData();

        // 獲取還原報告以顯示詳細資訊
        final restoreReport = FirebaseStorageBackupService.getLastRestoreReport();
        final actualAddedCount = restoreReport?['actualAddedCount'] ?? 0;
        final totalCount = _viewModel.birthDataList.length;

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.check_circle, color: Colors.white),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      actualAddedCount > 0
                        ? '已成功從雲端還原 $actualAddedCount 筆新資料，目前共 $totalCount 筆出生資料'
                        : '雲端還原完成，目前共 $totalCount 筆出生資料',
                    ),
                  ),
                ],
              ),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 4),
            ),
          );
        }

        logger.i('雲端還原完成，UI 已更新 - 新增: $actualAddedCount 筆，總計: $totalCount 筆');
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('雲端還原失敗，請檢查網路連接和登入狀態'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      // 關閉進度對話框
      if (mounted) Navigator.of(context).pop();

      // 顯示錯誤訊息
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('還原時出錯: $e')),
        );
      }
      logger.e('雲端還原時出錯: $e');
    }
  }

  /// 格式化日期時間
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}/${dateTime.month.toString().padLeft(2, '0')}/${dateTime.day.toString().padLeft(2, '0')} '
           '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
