import 'package:flutter/material.dart';

import '../../../astreal.dart';
import '../ai_interpretation_selection_page.dart';
import '../chart_selection_page.dart';
import '../equinox_solstice_page.dart';
import '../main/files_page.dart';

class StarMasterAnalysisPage extends StatefulWidget {
  const StarMasterAnalysisPage({super.key});

  @override
  State<StarMasterAnalysisPage> createState() => _StarMasterAnalysisPageState();
}

class _StarMasterAnalysisPageState extends State<StarMasterAnalysisPage> with SingleTickerProviderStateMixin {
  late TabController _tabController;

  // 分析類別定義
  final List<AnalysisCategory> _categories = [
    AnalysisCategory(
      id: 'professional',
      name: '專業星盤分析',
      description: '完整的專業星盤分析服務，包含各種星盤類型',
      icon: Icons.auto_awesome,
      color: AppColors.royalIndigo,
      analysisPoints: ['本命盤分析', '合盤分析', '推運分析', '返照盤分析'],
      relatedPlanets: ['所有行星'],
      relatedHouses: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
      relatedAspects: ['所有相位'],
    ),
    AnalysisCategory(
      id: 'emotion',
      name: '感情分析',
      description: '探索愛情、人際關係與情感世界',
      icon: Icons.favorite,
      color: Colors.pink,
      analysisPoints: ['感情運勢', '人際關係', '家庭和諧'],
      relatedPlanets: ['金星', '火星', '月亮'],
      relatedHouses: [5, 7, 11],
      relatedAspects: ['金星相位', '火星相位'],
    ),
    AnalysisCategory(
      id: 'finance',
      name: '理財分析',
      description: '分析財運、投資理財與財富累積',
      icon: Icons.monetization_on,
      color: Colors.green,
      analysisPoints: ['財運發展', '理財能力', '投資方向'],
      relatedPlanets: ['金星', '木星', '土星'],
      relatedHouses: [2, 8, 11],
      relatedAspects: ['第二宮相位', '木星相位'],
    ),
    AnalysisCategory(
      id: 'career',
      name: '職涯分析',
      description: '探討事業發展、職業方向與成就潛力',
      icon: Icons.work,
      color: Colors.indigo,
      analysisPoints: ['職業天賦', '事業發展', '成就潛力'],
      relatedPlanets: ['太陽', '火星', '土星'],
      relatedHouses: [6, 10],
      relatedAspects: ['第十宮相位', '火星相位'],
    ),
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _categories.length, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          '星盤分析',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: AppColors.indigoSurface,
          ),
        ),
        backgroundColor: AppColors.pastelSkyBlue,
        foregroundColor: Colors.white,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          labelColor: AppColors.royalIndigo,
          unselectedLabelColor: AppColors.royalIndigo.withOpacity(0.6),
          indicatorColor: AppColors.solarAmber,
          indicatorWeight: 3,
          indicatorSize: TabBarIndicatorSize.tab,
          labelStyle: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
          unselectedLabelStyle: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.normal,
          ),
          isScrollable: false,
          tabs: _categories.map((category) => Tab(
            icon: Icon(category.icon, size: 20),
            text: category.name,
          )).toList(),
        ),
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppColors.royalIndigo.withValues(alpha: 0.1),
              Colors.white,
            ],
            stops: const [0.0, 0.3],
          ),
        ),
        child: TabBarView(
          controller: _tabController,
          children: _categories.map((category) => _buildCategoryContent(category)).toList(),
        ),
      ),
    );
  }

  /// 構建分類內容
  Widget _buildCategoryContent(AnalysisCategory category) {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        // 分類介紹卡片
        _buildCategoryIntroCard(category),
        const SizedBox(height: 24),

        // 根據分類顯示相應的星盤分析選項
        ..._buildCategoryAnalysisOptions(category),
      ],
    );
  }

  /// 構建分類介紹卡片
  Widget _buildCategoryIntroCard(AnalysisCategory category) {
    return StyledCard(
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              category.color.withValues(alpha: 0.1),
              category.color.withValues(alpha: 0.05),
            ],
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: category.color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    category.icon,
                    color: category.color,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        category.name,
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: category.color,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '專業${category.name}服務',
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              category.description,
              style: const TextStyle(
                fontSize: 14,
                color: AppColors.textDark,
                height: 1.5,
              ),
            ),
            const SizedBox(height: 12),
            // 分析要點
            Text(
              '分析重點：',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: category.color,
              ),
            ),
            const SizedBox(height: 8),
            ...category.analysisPoints.map((point) => Padding(
              padding: const EdgeInsets.only(bottom: 4),
              child: Row(
                children: [
                  Icon(
                    Icons.check_circle,
                    size: 16,
                    color: category.color,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    point,
                    style: const TextStyle(
                      fontSize: 13,
                      color: AppColors.textDark,
                    ),
                  ),
                ],
              ),
            )),
          ],
        ),
      ),
    );
  }

  /// 根據分類構建分析選項
  List<Widget> _buildCategoryAnalysisOptions(AnalysisCategory category) {
    switch (category.id) {
      case 'professional':
        return [
          // 個人星盤分析
          _buildSectionHeader('個人星盤分析', Icons.person),
          _buildPersonalChartSection(),
          const SizedBox(height: 24),

          // 關係合盤分析
          _buildSectionHeader('關係合盤分析', Icons.favorite),
          _buildRelationshipChartSection(),
          const SizedBox(height: 24),

          // 預測推運分析
          _buildSectionHeader('預測推運分析', Icons.timeline),
          _buildPredictiveChartSection(),
          const SizedBox(height: 24),

          // 關係合盤推運分析
          _buildSectionHeader('關係合盤推運分析', Icons.trending_up),
          _buildRelationshipProgressionSection(),
          const SizedBox(height: 24),

          // 特殊星盤分析
          _buildSectionHeader('特殊星盤分析', Icons.auto_awesome),
          _buildSpecialChartSection(),
          const SizedBox(height: 32),
        ];
      case 'emotion':
        return [
          _buildSectionHeader('感情運勢分析', Icons.favorite),
          _buildEmotionAnalysisSection(),
          const SizedBox(height: 24),
          _buildSectionHeader('人際關係分析', Icons.people),
          _buildRelationshipAnalysisSection(),
        ];
      case 'finance':
        return [
          _buildSectionHeader('財運分析', Icons.monetization_on),
          _buildFinanceAnalysisSection(),
          const SizedBox(height: 24),
          _buildSectionHeader('投資理財', Icons.trending_up),
          _buildInvestmentAnalysisSection(),
        ];
      case 'career':
        return [
          _buildSectionHeader('職業天賦', Icons.work),
          _buildCareerTalentSection(),
          const SizedBox(height: 24),
          _buildSectionHeader('事業發展', Icons.business_center),
          _buildCareerDevelopmentSection(),
        ];
      default:
        return [];
    }
  }

  /// 構建介紹卡片
  Widget _buildIntroCard() {
    return StyledCard(
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              AppColors.royalIndigo.withValues(alpha: 0.1),
              AppColors.indigoLight.withValues(alpha: 0.05),
            ],
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppColors.royalIndigo.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.analytics,
                    color: AppColors.royalIndigo,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                const Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '專業星盤分析',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: AppColors.textDark,
                        ),
                      ),
                      SizedBox(height: 4),
                      Text(
                        '選擇星盤類型，深入解讀占星奧秘',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            const Text(
              '我們提供全方位的星盤分析服務，涵蓋個人成長、關係探索、未來預測等各個面向。選擇您感興趣的星盤類型，開始您的占星探索之旅。',
              style: TextStyle(
                fontSize: 14,
                color: AppColors.textDark,
                height: 1.5,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 構建章節標題
  Widget _buildSectionHeader(String title, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        children: [
          Icon(icon, color: AppColors.royalIndigo, size: 28),
          const SizedBox(width: 12),
          Text(
            title,
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AppColors.royalIndigo,
            ),
          ),
        ],
      ),
    );
  }

  /// 顯示功能尚未開放的提示
  void _showFeatureNotAvailable(String feature) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('$feature功能即將推出，敬請期待！'),
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  /// 導航到二分二至圖頁面
  void _navigateToEquinoxSolstice() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const EquinoxSolsticePage(),
      ),
    );
  }

  /// 構建個人星盤分析區域
  Widget _buildPersonalChartSection() {
    return Column(
      children: [
        _buildChartAnalysisCard(
          '本命盤分析',
          '深度解析個人性格、天賦、人生課題',
          Icons.person,
          AppColors.royalIndigo,
          ChartType.natal,
        ),
      ],
    );
  }

  /// 構建關係合盤分析區域
  Widget _buildRelationshipChartSection() {
    return Column(
      children: [
        _buildChartAnalysisCard(
          '合盤分析',
          '兩人星盤比較，關係相容性深度解析',
          Icons.favorite,
          Colors.pink,
          ChartType.synastry,
          requiresTwoPeople: true,
        ),
        _buildChartAnalysisCard(
          '組合盤分析',
          '關係中點盤，兩人共同創造的能量',
          Icons.merge_type,
          Colors.purple,
          ChartType.composite,
          requiresTwoPeople: true,
        ),
        _buildChartAnalysisCard(
          '戴維森盤',
          '關係的時空中點，共同命運分析',
          Icons.people,
          Colors.teal,
          ChartType.davison,
          requiresTwoPeople: true,
        ),
        _buildChartAnalysisCard(
          '馬克思盤',
          '體現關係中盤主內心的主觀感受和心理層面解讀',
          Icons.psychology,
          Colors.deepPurple,
          ChartType.marks,
          requiresTwoPeople: true,
        ),
      ],
    );
  }

  /// 構建預測推運分析區域
  Widget _buildPredictiveChartSection() {
    return Column(
      children: [
        _buildChartAnalysisCard(
          '流年盤分析',
          '當前行星位置對個人的影響',
          Icons.schedule,
          Colors.blue,
          ChartType.transit,
          requiresTimeSelection: true,
        ),
        _buildChartAnalysisCard(
          '次限推運盤',
          '次限推運的人生發展趨勢',
          Icons.timeline,
          Colors.purple,
          ChartType.secondaryProgression,
          requiresTimeSelection: true,
        ),
        _buildChartAnalysisCard(
          '三限推運盤',
          '一天代表一個月，反映短期心理變化',
          Icons.calendar_view_month,
          Colors.indigo,
          ChartType.tertiaryProgression,
          requiresTimeSelection: true,
        ),
        _buildChartAnalysisCard(
          '太陽返照盤',
          '年度運勢預測，生日當天的星象配置',
          Icons.wb_sunny,
          AppColors.solarAmber,
          ChartType.solarReturn,
          requiresTimeSelection: true,
        ),
        _buildChartAnalysisCard(
          '月亮返照盤',
          '月度情緒週期與內在需求分析',
          Icons.nightlight_round,
          Colors.indigo,
          ChartType.lunarReturn,
          requiresTimeSelection: true,
        ),
        _buildChartAnalysisCard(
          '太陽弧推運盤',
          '所有行星以太陽推進度數前進，觀察重大人生變化',
          Icons.wb_sunny,
          Colors.deepOrange,
          ChartType.solarArcDirection,
          requiresTimeSelection: true,
        ),
        _buildChartAnalysisCard(
          '法達盤分析',
          '古典占星的時期系統分析',
          Icons.access_time,
          Colors.orange,
          ChartType.firdaria,
          requiresTimeSelection: true,
        ),
        _buildChartAnalysisCard(
          '小限盤分析',
          '月度推運，短期發展趨勢',
          Icons.calendar_month,
          Colors.cyan,
          ChartType.profection,
          requiresTimeSelection: true,
        ),
      ],
    );
  }

  /// 構建關係合盤推運分析區域
  Widget _buildRelationshipProgressionSection() {
    return Column(
      children: [
        // 比較推運盤
        _buildChartAnalysisCard(
          '比較次限盤',
          '分析一方次限推運對另一方本命盤的影響',
          Icons.compare_arrows,
          Colors.blue,
          ChartType.synastrySecondary,
          requiresTwoPeople: true,
          requiresTimeSelection: true,
        ),
        _buildChartAnalysisCard(
          '比較三限盤',
          '分析一方三限推運對另一方本命盤的影響',
          Icons.compare,
          Colors.lightBlue,
          ChartType.synastryTertiary,
          requiresTwoPeople: true,
          requiresTimeSelection: true,
        ),

        // 組合推運盤
        _buildChartAnalysisCard(
          '組合次限盤',
          '分析組合盤的次限推運，觀察關係長期發展',
          Icons.merge_type,
          Colors.purple,
          ChartType.compositeSecondary,
          requiresTwoPeople: true,
          requiresTimeSelection: true,
        ),
        _buildChartAnalysisCard(
          '組合三限盤',
          '分析組合盤的三限推運，觀察關係短期變化',
          Icons.merge,
          Colors.deepPurple,
          ChartType.compositeTertiary,
          requiresTwoPeople: true,
          requiresTimeSelection: true,
        ),

        // 時空推運盤
        _buildChartAnalysisCard(
          '時空次限盤',
          '分析時空中點盤的次限推運，觀察關係長期發展',
          Icons.schedule,
          Colors.teal,
          ChartType.davisonSecondary,
          requiresTwoPeople: true,
          requiresTimeSelection: true,
        ),
        _buildChartAnalysisCard(
          '時空三限盤',
          '分析時空中點盤的三限推運，觀察關係短期變化',
          Icons.access_time,
          Colors.cyan,
          ChartType.davisonTertiary,
          requiresTwoPeople: true,
          requiresTimeSelection: true,
        ),

        // 馬克思推運盤
        _buildChartAnalysisCard(
          '馬克思次限盤',
          '分析馬克思盤的次限推運，觀察關係深層發展',
          Icons.psychology,
          Colors.deepPurple,
          ChartType.marksSecondary,
          requiresTwoPeople: true,
          requiresTimeSelection: true,
        ),
        _buildChartAnalysisCard(
          '馬克思三限盤',
          '分析馬克思盤的三限推運，觀察關係短期變化',
          Icons.psychology_outlined,
          Colors.purple,
          ChartType.marksTertiary,
          requiresTwoPeople: true,
          requiresTimeSelection: true,
        ),
      ],
    );
  }

  /// 構建特殊星盤分析區域
  Widget _buildSpecialChartSection() {
    return Column(
      children: [
        _buildChartAnalysisCard(
          '二分二至盤',
          '春分、夏至、秋分、冬至的季節能量',
          Icons.wb_sunny,
          AppColors.solarAmber,
          ChartType.equinoxSolstice,
          isSpecial: true,
        ),
        _buildChartAnalysisCard(
          '日月蝕盤',
          '蝕相對個人命運的深層影響',
          Icons.brightness_1,
          Colors.purple,
          ChartType.eclipse,
          requiresTwoPeople: true,
        ),
      ],
    );
  }

  /// 構建星盤分析卡片
  Widget _buildChartAnalysisCard(
    String title,
    String description,
    IconData icon,
    Color color,
    ChartType chartType, {
    bool requiresTwoPeople = false,
    bool requiresDateSelection = false,
    bool requiresTimeSelection = false,
    bool isSpecial = false,
  }) {
    return StyledCard(
      elevation: 2,
      margin: const EdgeInsets.only(bottom: 12),
      borderRadius: BorderRadius.circular(12),
      onTap: () => _handleChartAnalysis(
        chartType,
        title,
        requiresTwoPeople: requiresTwoPeople,
        requiresDateSelection: requiresDateSelection,
        requiresTimeSelection: requiresTimeSelection,
        isSpecial: isSpecial,
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(25),
              ),
              child: Icon(
                icon,
                color: color,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textDark,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    description,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Colors.grey,
                      height: 1.3,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      if (requiresTwoPeople && requiresTimeSelection) ...[
                        Icon(
                          Icons.people,
                          size: 14,
                          color: color,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '雙人+時間',
                          style: TextStyle(
                            fontSize: 12,
                            color: color,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ] else if (requiresTwoPeople) ...[
                        Icon(
                          Icons.people,
                          size: 14,
                          color: color,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '需要兩人資料',
                          style: TextStyle(
                            fontSize: 12,
                            color: color,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ] else if (requiresDateSelection) ...[
                        Icon(
                          Icons.schedule,
                          size: 14,
                          color: color,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '需要選擇時間',
                          style: TextStyle(
                            fontSize: 12,
                            color: color,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ] else if (requiresTimeSelection) ...[
                        Icon(
                          Icons.access_time,
                          size: 14,
                          color: color,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '需要時間參數',
                          style: TextStyle(
                            fontSize: 12,
                            color: color,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ] else if (isSpecial) ...[
                        Icon(
                          Icons.auto_awesome,
                          size: 14,
                          color: color,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '特殊星盤',
                          style: TextStyle(
                            fontSize: 12,
                            color: color,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ] else ...[
                        Icon(
                          Icons.person,
                          size: 14,
                          color: color,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '個人星盤',
                          style: TextStyle(
                            fontSize: 12,
                            color: color,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ],
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: color,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  /// 處理星盤分析
  Future<void> _handleChartAnalysis(
    ChartType chartType,
    String title, {
    bool requiresTwoPeople = false,
    bool requiresDateSelection = false,
    bool requiresTimeSelection = false,
    bool isSpecial = false,
  }) async {
    if (isSpecial) {
      _handleSpecialChart(chartType);
      return;
    }

    if (requiresTwoPeople && requiresTimeSelection) {
      await _handleTwoPeopleTimeSelectionChart(chartType, title);
    } else if (requiresTwoPeople) {
      await _handleTwoPeopleChart(chartType, title);
    } else if (requiresDateSelection) {
      await _handleDateSelectionChart(chartType, title);
    } else if (requiresTimeSelection) {
      await _handleTimeSelectionChart(chartType, title);
    } else {
      await _handleSinglePersonChart(chartType, title);
    }
  }

  /// 處理單人星盤（使用 ChartSelectionPage）
  Future<void> _handleSinglePersonChart(
      ChartType chartType, String title) async {
    final selectedPerson = await _selectBirthData(title);
    if (selectedPerson != null && mounted) {
      final chartData = ChartData(
        chartType: ChartType.natal,
        primaryPerson: selectedPerson,
      );

      _navigateToAIInterpretation(chartData);
    }
  }

  /// 導航到 AI 解讀選擇頁面
  Future<void> _navigateToAIInterpretation(ChartData chartData) async {
    chartData = await ChartViewModel.calculateChartData(chartData);
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AIInterpretationSelectionPage(
          chartData: chartData,
        ),
      ),
    );
  }

  /// 處理雙人星盤
  Future<void> _handleTwoPeopleChart(ChartType chartType, String title) async {
    if (mounted) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => ChartSelectionPage(
            initialChartType: chartType,
            fromAnalysisPage: true,
          ),
        ),
      );
    }
  }

  /// 處理需要日期選擇的星盤
  Future<void> _handleDateSelectionChart(
      ChartType chartType, String title) async {
    final selectedPerson = await _selectBirthData(title);
    if (selectedPerson != null && mounted) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => ChartSelectionPage(
            primaryPerson: selectedPerson,
            initialChartType: chartType,
            fromAnalysisPage: true,
          ),
        ),
      );
    }
  }

  /// 處理需要時間選擇的星盤（使用 ChartSelectionPage）
  Future<void> _handleTimeSelectionChart(
      ChartType chartType, String title) async {
    final selectedPerson = await _selectBirthData(title);
    if (selectedPerson != null && mounted) {
      // 使用 ChartSelectionPage 來處理時間選擇和星盤設定
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => ChartSelectionPage(
            primaryPerson: selectedPerson,
            initialChartType: chartType,
            fromAnalysisPage: true,
          ),
        ),
      );
    }
  }

  /// 處理需要兩人資料和時間選擇的星盤（關係合盤推運）
  Future<void> _handleTwoPeopleTimeSelectionChart(
      ChartType chartType, String title) async {
    if (mounted) {
      // 直接使用 ChartSelectionPage 來處理雙人選擇和時間設定
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => ChartSelectionPage(
            initialChartType: chartType,
            fromAnalysisPage: true,
          ),
        ),
      );
    }
  }

  /// 處理特殊星盤
  void _handleSpecialChart(ChartType chartType) {
    switch (chartType) {
      case ChartType.equinoxSolstice:
        _navigateToEquinoxSolstice();
        break;
      default:
        _showFeatureNotAvailable('特殊星盤');
    }
  }

  /// 選擇出生資料
  Future<BirthData?> _selectBirthData(String title) async {
    return await Navigator.push<BirthData>(
      context,
      MaterialPageRoute(
        builder: (context) => const FilesPage(
          isSelectionMode: true,
        ),
      ),
    );
  }

  /// 構建感情分析區域
  Widget _buildEmotionAnalysisSection() {
    return Column(
      children: [
        _buildChartAnalysisCard(
          '本命感情分析',
          '深度解析個人感情模式、愛情觀念',
          Icons.favorite,
          Colors.pink,
          ChartType.natal,
        ),
        _buildChartAnalysisCard(
          '合盤感情分析',
          '兩人感情相容性、關係發展潛力',
          Icons.favorite_border,
          Colors.pinkAccent,
          ChartType.synastry,
          requiresTwoPeople: true,
        ),
      ],
    );
  }

  /// 構建人際關係分析區域
  Widget _buildRelationshipAnalysisSection() {
    return Column(
      children: [
        _buildChartAnalysisCard(
          '人際關係分析',
          '社交能力、人際互動模式分析',
          Icons.people,
          Colors.blue,
          ChartType.natal,
        ),
        _buildChartAnalysisCard(
          '家庭關係分析',
          '與家人的關係、家庭和諧度',
          Icons.home,
          Colors.blueAccent,
          ChartType.natal,
        ),
      ],
    );
  }

  /// 構建財運分析區域
  Widget _buildFinanceAnalysisSection() {
    return Column(
      children: [
        _buildChartAnalysisCard(
          '財運格局分析',
          '個人財運、賺錢能力、財富累積',
          Icons.monetization_on,
          Colors.green,
          ChartType.natal,
        ),
        _buildChartAnalysisCard(
          '流年財運',
          '當前財運趨勢、投資時機分析',
          Icons.trending_up,
          Colors.greenAccent,
          ChartType.transit,
          requiresTimeSelection: true,
        ),
      ],
    );
  }

  /// 構建投資理財分析區域
  Widget _buildInvestmentAnalysisSection() {
    return Column(
      children: [
        _buildChartAnalysisCard(
          '投資偏好分析',
          '適合的投資類型、理財方式',
          Icons.account_balance_wallet,
          Colors.amber,
          ChartType.natal,
        ),
        _buildChartAnalysisCard(
          '財富管理建議',
          '理財策略、風險控制建議',
          Icons.savings,
          Colors.orange,
          ChartType.natal,
        ),
      ],
    );
  }

  /// 構建職業天賦分析區域
  Widget _buildCareerTalentSection() {
    return Column(
      children: [
        _buildChartAnalysisCard(
          '職業天賦分析',
          '個人天賦、適合的職業方向',
          Icons.work,
          Colors.indigo,
          ChartType.natal,
        ),
        _buildChartAnalysisCard(
          '工作風格分析',
          '工作方式、團隊合作能力',
          Icons.group_work,
          Colors.indigoAccent,
          ChartType.natal,
        ),
      ],
    );
  }

  /// 構建事業發展分析區域
  Widget _buildCareerDevelopmentSection() {
    return Column(
      children: [
        _buildChartAnalysisCard(
          '事業發展趨勢',
          '事業運勢、發展機會分析',
          Icons.business_center,
          Colors.deepPurple,
          ChartType.natal,
        ),
        _buildChartAnalysisCard(
          '流年事業運',
          '當前事業運勢、轉職時機',
          Icons.timeline,
          Colors.purple,
          ChartType.transit,
          requiresTimeSelection: true,
        ),
      ],
    );
  }
}
