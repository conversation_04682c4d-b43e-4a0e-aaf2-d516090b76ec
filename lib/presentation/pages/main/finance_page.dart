import 'dart:convert';

import 'package:astreal/presentation/pages/ai_interpretation_result_page.dart';
import 'package:astreal/presentation/pages/conjunction_selection_page.dart';
import 'package:astreal/presentation/pages/person_selector_page.dart';
import 'package:astreal/presentation/viewmodels/finance_viewmodel.dart';
import 'package:astreal/presentation/viewmodels/recent_persons_viewmodel.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../astreal.dart';
import '../../../data/services/api/astrology_service.dart';

/// 理財主頁面
class FinancePage extends StatefulWidget {
  const FinancePage({super.key});

  @override
  State<FinancePage> createState() => _FinancePageState();
}

class _FinancePageState extends State<FinancePage> {
  // 行星會合分析的時間範圍設定
  late DateTime _conjunctionStartDate;
  late DateTime _conjunctionEndDate;

  @override
  void initState() {
    super.initState();
    // 初始化默認時間範圍（當前時間前後各5年）
    final now = DateTime.now();
    _conjunctionStartDate = DateTime(now.year - 5, now.month, now.day);
    _conjunctionEndDate = DateTime(now.year + 5, now.month, now.day);
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => FinanceViewModel(),
      child: Consumer<FinanceViewModel>(builder: (context, viewModel, child) {
        return Consumer<ThemeProvider>(
          builder: (context, themeProvider, child) {
            final isStarlight = themeProvider.userMode == 'starlight';
            final primaryColor =
                isStarlight ? AppColors.solarAmber : AppColors.royalIndigo;
            final backgroundColor =
                isStarlight ? AppColors.lightCornsilk : AppColors.pastelSkyBlue;

            return Scaffold(
              appBar: AppBar(
                backgroundColor: backgroundColor,
                title: Text(
                  '理財',
                  style: TextStyle(
                    color: primaryColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                iconTheme: IconThemeData(color: primaryColor),
              ),
              body: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    // 木土會合分析 - 不需要選擇人物
                    // _buildJupiterSaturnConjunctionCard(),
                    // const SizedBox(height: 16),

                    // 火土會合分析 - 不需要選擇人物
                    // _buildMarsSaturnConjunctionCard(),
                    // const SizedBox(height: 16),

                    // 客製化問題分析
                    // _buildCustomQuestionCard(),
                    // const SizedBox(height: 16),

                    // 其他功能需要選擇人物
                    viewModel.selectedPerson == null
                        ? _buildPersonSelectionPrompt(viewModel)
                        : _buildFinanceContent(viewModel),
                  ],
                ),
              ),
            );
          },
        );
      }),
    );
  }

  /// 構建木土會合分析卡片（不需要選擇人物）
  Widget _buildJupiterSaturnConjunctionCard() {
    return StyledCard(
      elevation: 3,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppColors.royalIndigo.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.public,
                    color: AppColors.royalIndigo,
                    size: 28,
                  ),
                ),
                const SizedBox(width: 16),
                const Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '木土會合分析',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: AppColors.royalIndigo,
                        ),
                      ),
                      SizedBox(height: 4),
                      Text(
                        '20年經濟週期分析，長期投資規劃',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // 時間範圍設定
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Icon(
                        Icons.date_range,
                        color: AppColors.royalIndigo,
                        size: 16,
                      ),
                      const SizedBox(width: 6),
                      const Text(
                        '分析時間範圍',
                        style: TextStyle(
                          fontSize: 13,
                          fontWeight: FontWeight.bold,
                          color: AppColors.royalIndigo,
                        ),
                      ),
                      const Spacer(),
                      TextButton(
                        onPressed: _showConjunctionDateRangeSettings,
                        style: TextButton.styleFrom(
                          foregroundColor: AppColors.royalIndigo,
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 4),
                          minimumSize: Size.zero,
                        ),
                        child: const Text('設定', style: TextStyle(fontSize: 12)),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          '${_conjunctionStartDate.year}/${_conjunctionStartDate.month}/${_conjunctionStartDate.day}',
                          style: const TextStyle(fontSize: 12),
                        ),
                      ),
                      const Icon(Icons.arrow_forward,
                          size: 14, color: Colors.grey),
                      Expanded(
                        child: Text(
                          '${_conjunctionEndDate.year}/${_conjunctionEndDate.month}/${_conjunctionEndDate.day}',
                          style: const TextStyle(fontSize: 12),
                          textAlign: TextAlign.right,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '範圍：${(_conjunctionEndDate.difference(_conjunctionStartDate).inDays / 365.25).toStringAsFixed(1)}年',
                    style: TextStyle(
                      fontSize: 11,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 12),

            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.royalIndigo.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: AppColors.royalIndigo,
                    size: 16,
                  ),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      '木土會合約20年發生一次，代表重要的經濟結構轉換點，適合長期投資規劃',
                      style: TextStyle(
                        fontSize: 12,
                        color: AppColors.royalIndigo,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => _navigateToJupiterSaturnConjunction(),
                icon: const Icon(Icons.analytics),
                label: const Text('開始木土會合分析'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.royalIndigo,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 構建火土會合分析卡片（不需要選擇人物）
  Widget _buildMarsSaturnConjunctionCard() {
    return StyledCard(
      elevation: 3,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppColors.solarAmber.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.flash_on,
                    color: AppColors.solarAmber,
                    size: 28,
                  ),
                ),
                const SizedBox(width: 16),
                const Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '火土會合分析',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: AppColors.solarAmber,
                        ),
                      ),
                      SizedBox(height: 4),
                      Text(
                        '2年調整週期分析，投資組合優化',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),

            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.solarAmber.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: AppColors.solarAmber,
                    size: 16,
                  ),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      '火土會合約2年發生一次，代表行動與謹慎的平衡點，適合投資組合調整',
                      style: TextStyle(
                        fontSize: 12,
                        color: AppColors.solarAmber,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),

            // 開始分析按鈕
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => _navigateToMarsSaturnConjunction(),
                icon: const Icon(Icons.analytics),
                label: const Text('開始火土會合分析'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.solarAmber,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 構建人物選擇提示
  Widget _buildPersonSelectionPrompt(FinanceViewModel viewModel) {
    return Consumer<RecentPersonsViewModel>(
      builder: (context, recentPersonsViewModel, child) {
        final recentPersons =
            recentPersonsViewModel.getRecentPersons([], maxCount: 5);

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // 選擇人物提示
              StyledCard(
                elevation: 2,
                // margin: const EdgeInsets.only(bottom: 16),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: [
                      const Icon(
                        Icons.account_balance_wallet,
                        size: 64,
                        color: AppColors.solarAmber,
                      ),
                      // const SizedBox(height: 8),
                      const Text(
                        '開始您的財運分析',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: AppColors.royalIndigo,
                        ),
                      ),
                      // const SizedBox(height: 8),
                      Text(
                        '選擇一個人物來分析其財務星盤、投資風格和財運趨勢',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                        textAlign: TextAlign.center,
                      ),
                      // const SizedBox(height: 8),
                      ElevatedButton.icon(
                        onPressed: () => _selectPerson(viewModel),
                        icon: const Icon(Icons.person_search),
                        label: const Text('選擇人物'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.royalIndigo,
                          foregroundColor: Colors.white,
                          // padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // 近期人物快速選擇
              if (recentPersons.isNotEmpty) ...[
                _buildRecentPersonsSection(viewModel, recentPersons),
                // const SizedBox(height: 16),
              ],

              // 功能介紹
              _buildFeatureIntroduction(),
            ],
          ),
        );
      },
    );
  }

  /// 構建近期人物區域
  Widget _buildRecentPersonsSection(
      FinanceViewModel viewModel, List<BirthData> recentPersons) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Row(
          children: [
            Icon(
              Icons.history,
              size: 16,
              color: AppColors.royalIndigo,
            ),
            SizedBox(width: 6),
            Text(
              '近期分析',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: AppColors.royalIndigo,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        SizedBox(
          height: 80,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: recentPersons.length,
            itemBuilder: (context, index) {
              final person = recentPersons[index];
              return Container(
                width: 120,
                margin: const EdgeInsets.only(right: 12),
                child: InkWell(
                  onTap: () => viewModel.selectPerson(person),
                  borderRadius: BorderRadius.circular(12),
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: AppColors.textLight),
                    ),
                    padding: const EdgeInsets.all(8),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CircleAvatar(
                          radius: 16,
                          backgroundColor:
                              AppColors.solarAmber.withValues(alpha: 0.2),
                          child: Text(
                            person.name.isNotEmpty ? person.name[0] : '?',
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              color: AppColors.solarAmber,
                            ),
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          person.name,
                          style: const TextStyle(fontSize: 12),
                          textAlign: TextAlign.center,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  /// 構建功能介紹
  Widget _buildFeatureIntroduction() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '功能介紹',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: AppColors.royalIndigo,
          ),
        ),
        const SizedBox(height: 8),
        _buildFeatureCard(
          '投資屬性判斷',
          '深度分析您的投資性格，判斷您屬於保守、穩健或積極投資類型',
          Icons.psychology,
          AppColors.solarAmber,
        ),
        _buildFeatureCard(
          '個人財務星盤分析',
          '分析財帛宮、投資宮、合夥財等宮位，了解您的金錢吸引力和理財偏好',
          Icons.pie_chart,
          AppColors.royalIndigo,
        ),
        _buildFeatureCard(
          '流年財運指標',
          '追蹤木星、土星等行星對財運的影響，顯示年度財運曲線和吉凶月份',
          Icons.trending_up,
          AppColors.solarAmber,
        ),
        _buildFeatureCard(
          '占星投資建議',
          '結合個人財務風格和當前行運，提供保守型、積極型、靈活型投資建議',
          Icons.lightbulb,
          AppColors.indigoLight,
        ),
        _buildFeatureCard(
          '行星會合分析',
          '計算木土會合與火土會合，掌握重要的財務週期和投資時機',
          Icons.timeline_outlined,
          AppColors.royalIndigo,
        ),
      ],
    );
  }

  /// 構建功能卡片
  Widget _buildFeatureCard(
      String title, String description, IconData icon, Color color) {
    return StyledCard(
      elevation: 1,
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: color, size: 24),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: AppColors.royalIndigo,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    description,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 構建理財內容
  Widget _buildFinanceContent(FinanceViewModel viewModel) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // 當前選中人物信息
          _buildSelectedPersonCard(viewModel),
          // const SizedBox(height: 16),

          // 主要功能區域
          _buildMainFunctions(viewModel),
        ],
      ),
    );
  }

  /// 構建選中人物卡片
  Widget _buildSelectedPersonCard(FinanceViewModel viewModel) {
    final person = viewModel.selectedPerson!;
    return StyledCard(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            CircleAvatar(
              radius: 24,
              backgroundColor: AppColors.solarAmber.withValues(alpha: 0.2),
              child: Text(
                person.name.isNotEmpty ? person.name[0] : '?',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppColors.solarAmber,
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    person.name,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: AppColors.royalIndigo,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${person.dateTime.year}年${person.dateTime.month}月${person.dateTime.day}日',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            IconButton(
              onPressed: () => _selectPerson(viewModel),
              icon: const Icon(Icons.edit, color: AppColors.royalIndigo),
              tooltip: '更換人物',
            ),
          ],
        ),
      ),
    );
  }

  /// 構建主要功能
  Widget _buildMainFunctions(FinanceViewModel viewModel) {
    return Column(
      children: [
        _buildFunctionCard(
          '個人財務星盤分析',
          '深度分析您的財帛宮、投資宮、合夥財等宮位配置',
          Icons.pie_chart_outline,
          AppColors.royalIndigo,
          () => _navigateToFinancialAnalysis(viewModel),
        ),
        // const SizedBox(height: 12),
        _buildFunctionCard(
          '流年財運預測',
          '查看年度財運曲線、吉凶月份和行星觸發提示',
          Icons.timeline,
          AppColors.solarAmber,
          () => _navigateToFinancialForecast(viewModel),
        ),
        // const SizedBox(height: 12),
        _buildFunctionCard(
          '投資建議分析',
          '根據星盤配置和當前行運提供個性化投資建議',
          Icons.trending_up,
          AppColors.indigoLight,
          () => _navigateToInvestmentAdvice(viewModel),
        ),
        // const SizedBox(height: 12),
        _buildFunctionCard(
          '個人投資屬性判斷',
          '使用AI分析判斷您屬於保守、穩健或積極的投資類型',
          Icons.psychology,
          AppColors.solarAmber,
          () => _navigateToInvestmentPersonality(viewModel),
        ),
        // const SizedBox(height: 12),
        _buildFunctionCard(
          '行星會合分析',
          '計算木土會合與火土會合，掌握重要的財務週期時機',
          Icons.timeline_outlined,
          AppColors.royalIndigo,
          () => _navigateToPlanetaryConjunction(viewModel),
        ),
      ],
    );
  }

  /// 構建功能卡片
  Widget _buildFunctionCard(
    String title,
    String description,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return StyledCard(
      elevation: 2,
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: color, size: 24),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: AppColors.royalIndigo,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    description,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: Colors.grey[400],
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  /// 選擇人物
  Future<void> _selectPerson(FinanceViewModel viewModel) async {
    try {
      // 從 SharedPreferences 加載所有人物列表
      final prefs = await SharedPreferences.getInstance();
      final String? birthDataJson = prefs.getString('birthDataList');

      List<BirthData> allPersons = [];
      if (birthDataJson != null) {
        final List<dynamic> decodedData = jsonDecode(birthDataJson);
        allPersons =
            decodedData.map((item) => BirthData.fromJson(item)).toList();
      }

      if (allPersons.isEmpty) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('沒有可用的人物資料，請先新增人物資料')),
          );
        }
        return;
      }

      if (!mounted) return;

      // 使用模態頁面顯示人物選擇器
      final selectedPerson = await PersonSelectorPage.show(
        context: context,
        birthDataList: allPersons,
        title: '選擇分析對象',
        buttonColor: AppColors.royalIndigo,
      );

      if (selectedPerson != null && mounted) {
        viewModel.selectPerson(selectedPerson);

        // 記錄選中的人物到最近選中列表
        final recentPersonsViewModel =
            Provider.of<RecentPersonsViewModel>(context, listen: false);
        await recentPersonsViewModel.recordSelectedPerson(selectedPerson);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('選擇人物時出錯：$e')),
        );
      }
    }
  }

  /// 刷新數據
  void _refreshData(FinanceViewModel viewModel) {
    if (viewModel.selectedPerson != null) {
      viewModel.refreshAnalysis();
    }
  }

  /// 導航到財務分析頁面 - 直接進行 AI 分析
  void _navigateToFinancialAnalysis(FinanceViewModel viewModel) async {
    await _performAIAnalysisAndNavigate(
      viewModel,
      // InterpretationType.careerFinance,
      '個人財務星盤分析',
      [
        '我的財務性格和金錢觀是什麼？',
        '我的主要賺錢能力和方式有哪些？',
        '適合我的投資類型和策略是什麼？',
        '我應該如何進行財務規劃？',
        '我的財運最佳時期是什麼時候？',
      ],
    );
  }

  /// 導航到財運預測頁面 - 直接進行 AI 分析
  void _navigateToFinancialForecast(FinanceViewModel viewModel) async {
    await _performAIAnalysisAndNavigate(
      viewModel,
      // InterpretationType.currentTrends,
      '流年財運預測',
      [
        '我今年的整體財運如何？',
        '哪些月份是我的財運高峰期？',
        '我需要在什麼時候謹慎理財？',
        '今年有哪些重要的財務機會？',
        '我應該如何把握財運時機？',
      ],
    );
  }

  /// 導航到投資建議頁面 - 直接進行 AI 分析
  void _navigateToInvestmentAdvice(FinanceViewModel viewModel) async {
    await _performAIAnalysisAndNavigate(
      viewModel,
      // InterpretationType.careerFinance,
      '投資建議分析',
      [
        '根據我的星盤，適合什麼類型的投資？',
        '我的投資風險承受能力如何？',
        '什麼時候是我的投資最佳時機？',
        '我應該避免哪些投資陷阱？',
        '如何制定適合我的投資策略？',
      ],
    );
  }

  /// 導航到投資屬性判斷頁面 - 直接進行 AI 分析
  void _navigateToInvestmentPersonality(FinanceViewModel viewModel) async {
    await _performAIAnalysisAndNavigate(
      viewModel,
      // InterpretationType.investmentPersonality,
      '個人投資屬性判斷',
      [
        '根據我的星盤分析，我屬於保守型、穩健型還是積極型投資者？',
        '我的投資性格有哪些特點？',
        '我的風險承受能力如何？應該如何評估？',
        '什麼樣的投資策略最適合我的個性？',
        '我在投資決策時容易出現哪些心理偏誤？',
        '如何根據我的投資屬性制定資產配置？',
        '我應該避免哪些不適合我性格的投資方式？',
      ],
    );
  }

  /// 構建客製化問題分析卡片
  Widget _buildCustomQuestionCard() {
    return StyledCard(
      elevation: 3,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppColors.indigoLight.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.psychology,
                    color: AppColors.indigoLight,
                    size: 28,
                  ),
                ),
                const SizedBox(width: 16),
                const Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '客製化問題分析',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: AppColors.indigoLight,
                        ),
                      ),
                      SizedBox(height: 4),
                      Text(
                        '輸入您的財務投資問題，AI為您提供專業分析',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),

            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.indigoLight.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: AppColors.indigoLight,
                    size: 16,
                  ),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      '您可以詢問任何關於投資、理財、市場分析的問題，AI將結合占星學知識為您解答',
                      style: TextStyle(
                        fontSize: 12,
                        color: AppColors.indigoLight,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),

            // 開始分析按鈕
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => _showCustomQuestionDialog(),
                icon: const Icon(Icons.edit),
                label: const Text('輸入問題並分析'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.indigoLight,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 導航到木土會合分析
  void _navigateToJupiterSaturnConjunction() async {
    try {
      // 顯示詳細的載入UI
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => _JupiterSaturnLoadingDialog(
          startDate: _conjunctionStartDate,
          endDate: _conjunctionEndDate,
        ),
      );

      // 使用默認位置（台北）
      final defaultPerson = BirthData(
        id: 'jupiter_saturn_analysis',
        name: '木土會合分析',
        dateTime: DateTime.now(),
        latitude: 25.0330,
        // 台北緯度
        longitude: 121.5654,
        // 台北經度
        birthPlace: '台北',
      );

      // 模擬載入過程
      await Future.delayed(const Duration(milliseconds: 1500));

      // 關閉載入對話框
      if (mounted) {
        Navigator.of(context).pop();
      }

      // 導航到木土會合選擇頁面
      if (mounted) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => ConjunctionSelectionPage(
              conjunctionType: ConjunctionType.jupiterSaturn,
              person: defaultPerson,
              startDate: _conjunctionStartDate,
              endDate: _conjunctionEndDate,
            ),
          ),
        );
      }
    } catch (e) {
      // 關閉載入對話框
      if (mounted) {
        Navigator.of(context).pop();
      }

      // 顯示錯誤信息
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('無法開啟木土會合分析: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// 導航到火土會合分析
  void _navigateToMarsSaturnConjunction() async {
    try {
      // 顯示詳細的載入UI
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => _MarsSaturnLoadingDialog(
          startDate: _conjunctionStartDate,
          endDate: _conjunctionEndDate,
        ),
      );

      // 使用默認位置（台北）
      final defaultPerson = BirthData(
        id: 'mars_saturn_analysis',
        name: '火土會合分析',
        dateTime: DateTime.now(),
        latitude: 25.0330,
        // 台北緯度
        longitude: 121.5654,
        // 台北經度
        birthPlace: '台北',
      );

      // 模擬載入過程
      await Future.delayed(const Duration(milliseconds: 1500));

      // 關閉載入對話框
      if (mounted) {
        Navigator.of(context).pop();
      }

      // 導航到火土會合選擇頁面
      if (mounted) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => ConjunctionSelectionPage(
              conjunctionType: ConjunctionType.marsSaturn,
              person: defaultPerson,
              startDate: _conjunctionStartDate,
              endDate: _conjunctionEndDate,
            ),
          ),
        );
      }
    } catch (e) {
      // 關閉載入對話框
      if (mounted) {
        Navigator.of(context).pop();
      }

      // 顯示錯誤信息
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('無法開啟火土會合分析: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// 顯示客製化問題對話框
  void _showCustomQuestionDialog() async {
    final result = await showDialog<String>(
      context: context,
      builder: (context) => _CustomQuestionDialog(),
    );

    if (result != null && result.isNotEmpty) {
      _analyzeCustomQuestion(result);
    }
  }

  /// 分析客製化問題
  void _analyzeCustomQuestion(String question) async {
    try {
      // 顯示載入指示器
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => _CustomQuestionLoadingDialog(question: question),
      );

      // 創建默認星盤數據
      final defaultPerson = BirthData(
        id: 'custom_question_analysis',
        name: '客製化問題分析',
        dateTime: DateTime.now(),
        latitude: 25.0330,
        // 台北緯度
        longitude: 121.5654,
        // 台北經度
        birthPlace: '台北',
      );

      final chartData = await _createChartData(defaultPerson) ??
          ChartData(
            chartType: ChartType.natal,
            primaryPerson: defaultPerson,
          );

      // 模擬載入過程
      await Future.delayed(const Duration(milliseconds: 2000));

      // 關閉載入對話框
      if (mounted) {
        Navigator.of(context).pop();
      }

      // 導航到AI分析結果頁面
      if (mounted) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => AIInterpretationResultPage(
              chartData: chartData,
              interpretationTitle: '客製化問題分析',
              subtitle: question,
              autoExecuteFirstQuestion: true,
              // 自動執行第一個問題
              suggestedQuestions: [
                question, // 用戶的原始問題會自動被分析
                '這個問題在占星學上有什麼特殊意義？',
                '根據當前的星象配置，我應該如何應對？',
                '有什麼需要特別注意的時機或風險？',
                '您還有其他相關的建議嗎？',
              ],
            ),
          ),
        );
      }
    } catch (e) {
      // 關閉載入對話框
      if (mounted) {
        Navigator.of(context).pop();
      }

      // 顯示錯誤信息
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('無法進行分析: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// 導航到行星會合分析頁面（不需要選擇人物）
  void _navigateToPlanetaryConjunctionWithoutPerson() async {
    try {
      // 顯示載入指示器
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => Dialog(
          backgroundColor: Colors.transparent,
          child: Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const CircularProgressIndicator(
                  valueColor:
                      AlwaysStoppedAnimation<Color>(AppColors.royalIndigo),
                ),
                const SizedBox(height: 16),
                const Text(
                  '正在準備分析',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppColors.royalIndigo,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  '正在初始化行星會合計算引擎...',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      );

      // 短暫延遲以顯示loading效果
      await Future.delayed(const Duration(milliseconds: 800));

      // 使用默認位置（台北）
      final defaultPerson = BirthData(
        id: 'default_location',
        name: '全球財務週期',
        dateTime: DateTime.now(),
        latitude: 25.0330,
        // 台北緯度
        longitude: 121.5654,
        // 台北經度
        birthPlace: '台北',
      );

      // 導航到行星會合分析頁面
      if (mounted) {
        Navigator.of(context).pop(); // 關閉載入指示器

        // 這個方法已經被新的木土會合和火土會合分析取代
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('請使用上方的木土會合分析或火土會合分析功能'),
            backgroundColor: AppColors.royalIndigo,
          ),
        );
      }
    } catch (e) {
      // 關閉載入指示器
      if (mounted) {
        Navigator.of(context).pop();
      }

      // 顯示錯誤信息
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('無法開啟行星會合分析: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// 導航到行星會合分析頁面（需要選擇人物）
  void _navigateToPlanetaryConjunction(FinanceViewModel viewModel) async {
    if (viewModel.selectedPerson == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('請先選擇分析對象')),
      );
      return;
    }

    try {
      // 顯示載入指示器
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );

      // 導航到行星會合分析頁面
      if (mounted) {
        Navigator.of(context).pop(); // 關閉載入指示器

        // 這個方法已經被新的木土會合和火土會合分析取代
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('請使用上方的木土會合分析或火土會合分析功能'),
            backgroundColor: AppColors.royalIndigo,
          ),
        );
      }
    } catch (e) {
      // 關閉載入指示器
      if (mounted) {
        Navigator.of(context).pop();
      }

      // 顯示錯誤信息
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('無法開啟行星會合分析: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// 執行 AI 分析並導航到結果頁面
  Future<void> _performAIAnalysisAndNavigate(
    FinanceViewModel viewModel,
    // InterpretationType interpretationType,
    String analysisTitle,
    List<String> suggestedQuestions,
  ) async {
    try {
      // 顯示載入指示器
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );

      // 創建星盤數據
      final chartData = await _createChartData(viewModel.selectedPerson!);

      // 關閉載入指示器
      if (mounted) {
        Navigator.of(context).pop();
      }

      if (chartData != null && mounted) {
        // 直接導航到 AI 解讀結果頁面
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => AIInterpretationResultPage(
              chartData: chartData,
              interpretationTitle: analysisTitle,
              subtitle: viewModel.selectedPerson!.name,
              suggestedQuestions: suggestedQuestions,
            ),
          ),
        );
      } else {
        // 顯示錯誤信息
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('無法創建星盤數據，請稍後再試'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      // 關閉載入指示器
      if (mounted) {
        Navigator.of(context).pop();
      }

      // 顯示錯誤信息
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('分析失敗: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// 顯示行星會合分析時間範圍設定
  void _showConjunctionDateRangeSettings() async {
    final result = await showDialog<Map<String, DateTime>>(
      context: context,
      builder: (context) => _ConjunctionDateRangeDialog(
        initialStartDate: _conjunctionStartDate,
        initialEndDate: _conjunctionEndDate,
      ),
    );

    if (result != null) {
      setState(() {
        _conjunctionStartDate = result['startDate']!;
        _conjunctionEndDate = result['endDate']!;
      });
    }
  }

  /// 創建星盤數據
  Future<ChartData?> _createChartData(BirthData person) async {
    try {
      // 創建本命盤數據結構
      final chartData = ChartData(
        chartType: ChartType.natal,
        primaryPerson: person,
      );

      // 使用 AstrologyService 計算星盤數據
      final calculatedChartData = await AstrologyService().calculateChartData(
        chartData,
        latitude: person.latitude,
        longitude: person.longitude,
      );

      return calculatedChartData;
    } catch (e) {
      print('創建星盤數據失敗: $e');
      return null;
    }
  }
}

/// 行星會合分析時間範圍設定對話框
class _ConjunctionDateRangeDialog extends StatefulWidget {
  final DateTime initialStartDate;
  final DateTime initialEndDate;

  const _ConjunctionDateRangeDialog({
    required this.initialStartDate,
    required this.initialEndDate,
  });

  @override
  State<_ConjunctionDateRangeDialog> createState() =>
      _ConjunctionDateRangeDialogState();
}

class _ConjunctionDateRangeDialogState
    extends State<_ConjunctionDateRangeDialog> {
  late DateTime _startDate;
  late DateTime _endDate;

  @override
  void initState() {
    super.initState();
    _startDate = widget.initialStartDate;
    _endDate = widget.initialEndDate;
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.timeline_outlined, color: AppColors.royalIndigo),
                SizedBox(width: 8),
                Text(
                  '行星會合分析時間範圍',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.royalIndigo,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // 快速選項
            const Text(
              '快速選項',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              runSpacing: 4,
              children: [
                _buildQuickOption('過去5年', -5, 0),
                _buildQuickOption('未來5年', 0, 5),
                _buildQuickOption('前後5年', -5, 5),
                _buildQuickOption('前後10年', -10, 10),
                _buildQuickOption('前後20年', -20, 20),
              ],
            ),
            const SizedBox(height: 16),

            // 自定義日期
            const Text(
              '自定義日期',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 8),

            // 開始日期
            ListTile(
              leading:
                  const Icon(Icons.calendar_today, color: AppColors.solarAmber),
              title: const Text('開始日期'),
              subtitle: Text(
                  '${_startDate.year}年${_startDate.month}月${_startDate.day}日'),
              onTap: () => _selectDate(true),
              contentPadding: EdgeInsets.zero,
            ),

            // 結束日期
            ListTile(
              leading: const Icon(Icons.event, color: AppColors.solarAmber),
              title: const Text('結束日期'),
              subtitle:
                  Text('${_endDate.year}年${_endDate.month}月${_endDate.day}日'),
              onTap: () => _selectDate(false),
              contentPadding: EdgeInsets.zero,
            ),

            const SizedBox(height: 16),

            // 範圍信息
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.royalIndigo.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.info_outline,
                    color: AppColors.royalIndigo,
                    size: 16,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      '分析範圍：${_endDate.difference(_startDate).inDays}天 (${(_endDate.difference(_startDate).inDays / 365.25).toStringAsFixed(1)}年)',
                      style: const TextStyle(
                        fontSize: 12,
                        color: AppColors.royalIndigo,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // 按鈕
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('取消'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: _startDate.isBefore(_endDate)
                      ? () {
                          Navigator.of(context).pop({
                            'startDate': _startDate,
                            'endDate': _endDate,
                          });
                        }
                      : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.royalIndigo,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('確定'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 構建快速選項按鈕
  Widget _buildQuickOption(String label, int startYears, int endYears) {
    return OutlinedButton(
      onPressed: () {
        final now = DateTime.now();
        setState(() {
          _startDate = DateTime(now.year + startYears, now.month, now.day);
          _endDate = DateTime(now.year + endYears, now.month, now.day);
        });
      },
      style: OutlinedButton.styleFrom(
        foregroundColor: AppColors.solarAmber,
        side: const BorderSide(color: AppColors.solarAmber),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      ),
      child: Text(label, style: const TextStyle(fontSize: 12)),
    );
  }

  /// 選擇日期
  void _selectDate(bool isStartDate) async {
    final initialDate = isStartDate ? _startDate : _endDate;
    final firstDate = DateTime(1900);
    final lastDate = DateTime(2100);

    final selectedDate = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: firstDate,
      lastDate: lastDate,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
                  primary: AppColors.royalIndigo,
                ),
          ),
          child: child!,
        );
      },
    );

    if (selectedDate != null) {
      setState(() {
        if (isStartDate) {
          _startDate = selectedDate;
        } else {
          _endDate = selectedDate;
        }
      });
    }
  }
}

/// 木土會合載入對話框
class _JupiterSaturnLoadingDialog extends StatefulWidget {
  final DateTime startDate;
  final DateTime endDate;

  const _JupiterSaturnLoadingDialog({
    required this.startDate,
    required this.endDate,
  });

  @override
  State<_JupiterSaturnLoadingDialog> createState() =>
      _JupiterSaturnLoadingDialogState();
}

class _JupiterSaturnLoadingDialogState
    extends State<_JupiterSaturnLoadingDialog> with TickerProviderStateMixin {
  late AnimationController _rotationController;
  late AnimationController _scaleController;
  late Animation<double> _scaleAnimation;

  int _currentStep = 0;
  final List<String> _loadingMessages = [
    '正在初始化木土會合計算引擎...',
    '正在搜索20年經濟週期中的木土會合時點...',
    '正在分析木星與土星的軌道交會...',
    '正在計算會合的精確時間和位置...',
    '準備進入會合時間選擇頁面...',
  ];

  @override
  void initState() {
    super.initState();

    _rotationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();

    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.easeInOut,
    ));

    _startLoadingSequence();
  }

  void _startLoadingSequence() async {
    for (int i = 0; i < _loadingMessages.length; i++) {
      if (mounted) {
        setState(() {
          _currentStep = i;
        });
        _scaleController.forward().then((_) {
          _scaleController.reverse();
        });
        await Future.delayed(const Duration(milliseconds: 300));
      }
    }
  }

  @override
  void dispose() {
    _rotationController.dispose();
    _scaleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final yearRange =
        (widget.endDate.difference(widget.startDate).inDays / 365.25)
            .toStringAsFixed(1);

    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: AppColors.royalIndigo.withValues(alpha: 0.2),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 動畫圖標
            AnimatedBuilder(
              animation: _rotationController,
              builder: (context, child) {
                return Transform.rotate(
                  angle: _rotationController.value * 2 * 3.14159,
                  child: AnimatedBuilder(
                    animation: _scaleAnimation,
                    builder: (context, child) {
                      return Transform.scale(
                        scale: _scaleAnimation.value,
                        child: Container(
                          width: 80,
                          height: 80,
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                AppColors.royalIndigo,
                                AppColors.royalIndigo.withValues(alpha: 0.6),
                              ],
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            ),
                            shape: BoxShape.circle,
                            boxShadow: [
                              BoxShadow(
                                color: AppColors.royalIndigo
                                    .withValues(alpha: 0.3),
                                blurRadius: 15,
                                offset: const Offset(0, 5),
                              ),
                            ],
                          ),
                          child: const Icon(
                            Icons.public,
                            color: Colors.white,
                            size: 40,
                          ),
                        ),
                      );
                    },
                  ),
                );
              },
            ),

            const SizedBox(height: 24),

            // 標題
            const Text(
              '木土會合分析',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AppColors.royalIndigo,
              ),
            ),

            const SizedBox(height: 8),

            // 副標題
            Text(
              '20年經濟週期 • $yearRange年範圍',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),

            const SizedBox(height: 24),

            // 進度指示器
            LinearProgressIndicator(
              value: (_currentStep + 1) / _loadingMessages.length,
              backgroundColor: AppColors.royalIndigo.withValues(alpha: 0.2),
              valueColor:
                  const AlwaysStoppedAnimation<Color>(AppColors.royalIndigo),
            ),

            const SizedBox(height: 16),

            // 載入訊息
            AnimatedSwitcher(
              duration: const Duration(milliseconds: 300),
              child: Text(
                _currentStep < _loadingMessages.length
                    ? _loadingMessages[_currentStep]
                    : '載入完成',
                key: ValueKey(_currentStep),
                style: const TextStyle(
                  fontSize: 13,
                  color: AppColors.royalIndigo,
                ),
                textAlign: TextAlign.center,
              ),
            ),

            const SizedBox(height: 16),

            // 時間範圍信息
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.royalIndigo.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(
                    Icons.date_range,
                    color: AppColors.royalIndigo,
                    size: 16,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    '${widget.startDate.year}/${widget.startDate.month}/${widget.startDate.day} - ${widget.endDate.year}/${widget.endDate.month}/${widget.endDate.day}',
                    style: const TextStyle(
                      fontSize: 12,
                      color: AppColors.royalIndigo,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// 火土會合載入對話框
class _MarsSaturnLoadingDialog extends StatefulWidget {
  final DateTime startDate;
  final DateTime endDate;

  const _MarsSaturnLoadingDialog({
    required this.startDate,
    required this.endDate,
  });

  @override
  State<_MarsSaturnLoadingDialog> createState() =>
      _MarsSaturnLoadingDialogState();
}

class _MarsSaturnLoadingDialogState extends State<_MarsSaturnLoadingDialog>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _rotationController;
  late Animation<double> _pulseAnimation;

  int _currentStep = 0;
  final List<String> _loadingMessages = [
    '正在初始化火土會合計算引擎...',
    '正在搜索2年調整週期中的火土會合時點...',
    '正在分析火星與土星的動態交會...',
    '正在計算會合的投資時機和影響...',
    '準備進入會合時間選擇頁面...',
  ];

  @override
  void initState() {
    super.initState();

    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    )..repeat(reverse: true);

    _rotationController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    )..repeat();

    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _startLoadingSequence();
  }

  void _startLoadingSequence() async {
    for (int i = 0; i < _loadingMessages.length; i++) {
      if (mounted) {
        setState(() {
          _currentStep = i;
        });
        await Future.delayed(const Duration(milliseconds: 300));
      }
    }
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _rotationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final yearRange =
        (widget.endDate.difference(widget.startDate).inDays / 365.25)
            .toStringAsFixed(1);

    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: AppColors.solarAmber.withValues(alpha: 0.2),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 動畫圖標
            AnimatedBuilder(
              animation: _rotationController,
              builder: (context, child) {
                return Transform.rotate(
                  angle: _rotationController.value * 2 * 3.14159,
                  child: AnimatedBuilder(
                    animation: _pulseAnimation,
                    builder: (context, child) {
                      return Transform.scale(
                        scale: _pulseAnimation.value,
                        child: Container(
                          width: 80,
                          height: 80,
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                AppColors.solarAmber,
                                AppColors.solarAmber.withValues(alpha: 0.6),
                              ],
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            ),
                            shape: BoxShape.circle,
                            boxShadow: [
                              BoxShadow(
                                color:
                                    AppColors.solarAmber.withValues(alpha: 0.3),
                                blurRadius: 15,
                                offset: const Offset(0, 5),
                              ),
                            ],
                          ),
                          child: const Icon(
                            Icons.flash_on,
                            color: Colors.white,
                            size: 40,
                          ),
                        ),
                      );
                    },
                  ),
                );
              },
            ),

            const SizedBox(height: 24),

            // 標題
            const Text(
              '火土會合分析',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AppColors.solarAmber,
              ),
            ),

            const SizedBox(height: 8),

            // 副標題
            Text(
              '2年調整週期 • $yearRange年範圍',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),

            const SizedBox(height: 24),

            // 進度指示器
            LinearProgressIndicator(
              value: (_currentStep + 1) / _loadingMessages.length,
              backgroundColor: AppColors.solarAmber.withValues(alpha: 0.2),
              valueColor:
                  const AlwaysStoppedAnimation<Color>(AppColors.solarAmber),
            ),

            const SizedBox(height: 16),

            // 載入訊息
            AnimatedSwitcher(
              duration: const Duration(milliseconds: 300),
              child: Text(
                _currentStep < _loadingMessages.length
                    ? _loadingMessages[_currentStep]
                    : '載入完成',
                key: ValueKey(_currentStep),
                style: const TextStyle(
                  fontSize: 13,
                  color: AppColors.solarAmber,
                ),
                textAlign: TextAlign.center,
              ),
            ),

            const SizedBox(height: 16),

            // 時間範圍信息
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.solarAmber.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(
                    Icons.date_range,
                    color: AppColors.solarAmber,
                    size: 16,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    '${widget.startDate.year}/${widget.startDate.month}/${widget.startDate.day} - ${widget.endDate.year}/${widget.endDate.month}/${widget.endDate.day}',
                    style: const TextStyle(
                      fontSize: 12,
                      color: AppColors.solarAmber,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// 客製化問題對話框
class _CustomQuestionDialog extends StatefulWidget {
  @override
  State<_CustomQuestionDialog> createState() => _CustomQuestionDialogState();
}

class _CustomQuestionDialogState extends State<_CustomQuestionDialog> {
  final TextEditingController _questionController = TextEditingController();
  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    // 延遲聚焦，確保對話框完全顯示後再聚焦
    Future.delayed(const Duration(milliseconds: 300), () {
      if (mounted) {
        _focusNode.requestFocus();
      }
    });
  }

  @override
  void dispose() {
    _questionController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        padding: const EdgeInsets.all(24),
        constraints: const BoxConstraints(maxWidth: 500),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 標題
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppColors.indigoLight.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.psychology,
                    color: AppColors.indigoLight,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                const Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '客製化問題分析',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: AppColors.indigoLight,
                        ),
                      ),
                      SizedBox(height: 4),
                      Text(
                        '請輸入您想詢問的財務投資問題',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),

            // 問題輸入框
            TextField(
              controller: _questionController,
              focusNode: _focusNode,
              maxLines: 4,
              decoration: InputDecoration(
                hintText: '例如：我應該在什麼時候投資股票？\n目前的市場適合買房嗎？\n我的投資組合需要調整嗎？',
                hintStyle: TextStyle(
                  color: Colors.grey[400],
                  fontSize: 13,
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.grey[300]!),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide:
                      const BorderSide(color: AppColors.indigoLight, width: 2),
                ),
                contentPadding: const EdgeInsets.all(16),
                filled: true,
                fillColor: Colors.grey[50],
              ),
              style: const TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 16),

            // 建議問題
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.indigoLight.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: AppColors.indigoLight.withValues(alpha: 0.2),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Row(
                    children: [
                      Icon(
                        Icons.lightbulb_outline,
                        color: AppColors.indigoLight,
                        size: 16,
                      ),
                      SizedBox(width: 6),
                      Text(
                        '建議問題類型',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: AppColors.indigoLight,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 8,
                    runSpacing: 4,
                    children: [
                      _buildSuggestionChip('投資時機'),
                      _buildSuggestionChip('風險評估'),
                      _buildSuggestionChip('資產配置'),
                      _buildSuggestionChip('市場分析'),
                      _buildSuggestionChip('理財規劃'),
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),

            // 按鈕
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Colors.grey,
                      side: BorderSide(color: Colors.grey[300]!),
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                    child: const Text('取消'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  flex: 2,
                  child: ElevatedButton.icon(
                    onPressed: () {
                      final question = _questionController.text.trim();
                      if (question.isNotEmpty) {
                        Navigator.of(context).pop(question);
                      } else {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('請輸入您的問題'),
                            backgroundColor: Colors.orange,
                          ),
                        );
                      }
                    },
                    icon: const Icon(Icons.analytics),
                    label: const Text('開始分析'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.indigoLight,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 構建建議標籤
  Widget _buildSuggestionChip(String label) {
    return GestureDetector(
      onTap: () {
        // 點擊建議標籤時，在輸入框中添加相關提示
        final currentText = _questionController.text;
        if (currentText.isEmpty) {
          _questionController.text = '關於$label，我想知道...';
        }
        _questionController.selection = TextSelection.fromPosition(
          TextPosition(offset: _questionController.text.length),
        );
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: AppColors.indigoLight.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: AppColors.indigoLight.withValues(alpha: 0.3),
          ),
        ),
        child: Text(
          label,
          style: const TextStyle(
            fontSize: 11,
            color: AppColors.indigoLight,
          ),
        ),
      ),
    );
  }
}

/// 客製化問題載入對話框
class _CustomQuestionLoadingDialog extends StatefulWidget {
  final String question;

  const _CustomQuestionLoadingDialog({
    required this.question,
  });

  @override
  State<_CustomQuestionLoadingDialog> createState() =>
      _CustomQuestionLoadingDialogState();
}

class _CustomQuestionLoadingDialogState
    extends State<_CustomQuestionLoadingDialog> with TickerProviderStateMixin {
  late AnimationController _rotationController;
  late AnimationController _scaleController;
  late Animation<double> _scaleAnimation;

  int _currentStep = 0;
  final List<String> _loadingMessages = [
    '正在分析您的問題...',
    '正在結合占星學知識...',
    '正在生成個人化建議...',
    '正在準備分析結果...',
  ];

  @override
  void initState() {
    super.initState();

    _rotationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();

    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.9,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.easeInOut,
    ));

    _startLoadingSequence();
  }

  void _startLoadingSequence() async {
    for (int i = 0; i < _loadingMessages.length; i++) {
      if (mounted) {
        setState(() {
          _currentStep = i;
        });
        _scaleController.forward().then((_) {
          _scaleController.reverse();
        });
        await Future.delayed(const Duration(milliseconds: 500));
      }
    }
  }

  @override
  void dispose() {
    _rotationController.dispose();
    _scaleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: AppColors.indigoLight.withValues(alpha: 0.2),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 動畫圖標
            AnimatedBuilder(
              animation: _rotationController,
              builder: (context, child) {
                return Transform.rotate(
                  angle: _rotationController.value * 2 * 3.14159,
                  child: AnimatedBuilder(
                    animation: _scaleAnimation,
                    builder: (context, child) {
                      return Transform.scale(
                        scale: _scaleAnimation.value,
                        child: Container(
                          width: 80,
                          height: 80,
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                AppColors.indigoLight,
                                AppColors.indigoLight.withValues(alpha: 0.6),
                              ],
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            ),
                            shape: BoxShape.circle,
                            boxShadow: [
                              BoxShadow(
                                color: AppColors.indigoLight
                                    .withValues(alpha: 0.3),
                                blurRadius: 15,
                                offset: const Offset(0, 5),
                              ),
                            ],
                          ),
                          child: const Icon(
                            Icons.psychology,
                            color: Colors.white,
                            size: 40,
                          ),
                        ),
                      );
                    },
                  ),
                );
              },
            ),

            const SizedBox(height: 24),

            // 標題
            const Text(
              '正在分析您的問題',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppColors.indigoLight,
              ),
            ),

            const SizedBox(height: 16),

            // 用戶問題
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.indigoLight.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                '"${widget.question}"',
                style: const TextStyle(
                  fontSize: 13,
                  color: AppColors.indigoLight,
                  fontStyle: FontStyle.italic,
                ),
                textAlign: TextAlign.center,
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
            ),

            const SizedBox(height: 20),

            // 進度指示器
            LinearProgressIndicator(
              value: (_currentStep + 1) / _loadingMessages.length,
              backgroundColor: AppColors.indigoLight.withValues(alpha: 0.2),
              valueColor:
                  const AlwaysStoppedAnimation<Color>(AppColors.indigoLight),
            ),

            const SizedBox(height: 16),

            // 載入訊息
            AnimatedSwitcher(
              duration: const Duration(milliseconds: 300),
              child: Text(
                _currentStep < _loadingMessages.length
                    ? _loadingMessages[_currentStep]
                    : '分析完成',
                key: ValueKey(_currentStep),
                style: const TextStyle(
                  fontSize: 14,
                  color: AppColors.indigoLight,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
