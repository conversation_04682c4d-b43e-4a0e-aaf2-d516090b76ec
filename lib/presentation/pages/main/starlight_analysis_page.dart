import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../astreal.dart';
import '../ai_interpretation_selection_page.dart';
import '../equinox_solstice_page.dart';
import '../main/files_page.dart';
import '../starlight_chart_selection_page.dart';

/// 初心者模式分析頁面 - 使用通俗易懂的語言
class StarlightAnalysisPage extends StatefulWidget {
  const StarlightAnalysisPage({super.key});

  @override
  State<StarlightAnalysisPage> createState() => _StarlightAnalysisPageState();
}

class _StarlightAnalysisPageState extends State<StarlightAnalysisPage> {
  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        final isStarlight = themeProvider.userMode == 'starlight';
        final primaryColor = isStarlight ? AppColors.solarAmber : AppColors.royalIndigo;
        final backgroundColor = isStarlight ? AppColors.lightCornsilk : AppColors.pastelSkyBlue;

        return Scaffold(
          appBar: AppBar(
            title: Text(
              '個人分析',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: primaryColor,
              ),
            ),
            backgroundColor: backgroundColor,
            iconTheme: IconThemeData(color: primaryColor),
            elevation: 0,
          ),
          body: ListView(
            padding: const EdgeInsets.all(16),
            children: [
            // 介紹卡片
            _buildIntroCard(),
            const SizedBox(height: 24),

            // 了解自己
            _buildSectionHeader('了解自己', Icons.person_outline),
            _buildPersonalSection(),
            const SizedBox(height: 24),

            // 關係分析
            _buildSectionHeader('關係分析', Icons.favorite_outline),
            _buildRelationshipSection(),
            const SizedBox(height: 24),

            // 未來預測
            _buildSectionHeader('未來預測', Icons.trending_up),
            _buildPredictiveSection(),
            const SizedBox(height: 24),

            // 特殊時刻
            _buildSectionHeader('特殊時刻', Icons.auto_awesome),
            _buildSpecialSection(),
            const SizedBox(height: 32),
            ],
          ),
        );
      },
    );
  }

  /// 構建介紹卡片
  Widget _buildIntroCard() {
    return StyledCard(
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              AppColors.solarAmber.withValues(alpha: 0.1),
              AppColors.paleAmber.withValues(alpha: 0.05),
            ],
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppColors.solarAmber.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.psychology,
                    color: AppColors.solarAmber,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                const Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '個人分析工具',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: AppColors.textDark,
                        ),
                      ),
                      SizedBox(height: 4),
                      Text(
                        '透過出生時間了解自己和他人',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            const Text(
              '根據您的出生時間和地點，我們可以分析您的性格特質、人際關係、未來趨勢等。選擇您想了解的內容，開始探索自己的內心世界。',
              style: TextStyle(
                fontSize: 14,
                color: AppColors.textDark,
                height: 1.5,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 構建章節標題
  Widget _buildSectionHeader(String title, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        children: [
          Icon(icon, color: AppColors.solarAmber, size: 28),
          const SizedBox(width: 12),
          Text(
            title,
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AppColors.solarAmber,
            ),
          ),
        ],
      ),
    );
  }

  /// 顯示功能尚未開放的提示
  void _showFeatureNotAvailable(String feature) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('$feature功能即將推出，敬請期待！'),
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  /// 導航到二分二至圖頁面
  void _navigateToEquinoxSolstice() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const EquinoxSolsticePage(),
      ),
    );
  }

  /// 構建個人分析區域
  Widget _buildPersonalSection() {
    return Column(
      children: [
        _buildAnalysisCard(
          '我的性格分析',
          '了解您的個性特質、優缺點和天賦能力',
          Icons.psychology,
          AppColors.indigoLight,
          ChartType.natal,
        ),
      ],
    );
  }

  /// 構建關係分析區域
  Widget _buildRelationshipSection() {
    return Column(
      children: [
        _buildAnalysisCard(
          '配對分析',
          '分析兩人的相處模式和關係相容性',
          Icons.favorite,
          Colors.pink,
          ChartType.synastry,
          requiresTwoPeople: true,
        ),
        _buildAnalysisCard(
          '關係深度分析',
          '了解兩人在一起會產生什麼樣的化學反應',
          Icons.merge_type,
          Colors.purple,
          ChartType.composite,
          requiresTwoPeople: true,
        ),
        _buildAnalysisCard(
          '關係時空分析',
          '分析兩人相遇的時空意義和共同命運',
          Icons.people,
          Colors.teal,
          ChartType.davison,
          requiresTwoPeople: true,
        ),
        _buildAnalysisCard(
          '內心感受分析',
          '了解在這段關係中您內心的真實感受',
          Icons.psychology_outlined,
          Colors.deepPurple,
          ChartType.marks,
          requiresTwoPeople: true,
        ),
      ],
    );
  }

  /// 構建預測分析區域
  Widget _buildPredictiveSection() {
    return Column(
      children: [
        _buildAnalysisCard(
          '當前運勢',
          '分析目前的運勢和需要注意的事項',
          Icons.schedule,
          Colors.blue,
          ChartType.transit,
          requiresTimeSelection: true,
        ),
        _buildAnalysisCard(
          '人生發展趨勢',
          '了解您未來的人生發展方向和重要轉折',
          Icons.timeline,
          Colors.purple,
          ChartType.secondaryProgression,
          requiresTimeSelection: true,
        ),
        _buildAnalysisCard(
          '短期心理變化',
          '分析近期的心理狀態和情緒變化',
          Icons.calendar_view_month,
          Colors.indigo,
          ChartType.tertiaryProgression,
          requiresTimeSelection: true,
        ),
        _buildAnalysisCard(
          '年度運勢',
          '分析這一年的整體運勢和發展重點',
          Icons.wb_sunny,
          AppColors.solarAmber,
          ChartType.solarReturn,
          requiresTimeSelection: true,
        ),
        _buildAnalysisCard(
          '月度情緒週期',
          '了解每個月的情緒變化和內在需求',
          Icons.nightlight_round,
          Colors.indigo,
          ChartType.lunarReturn,
          requiresTimeSelection: true,
        ),
      ],
    );
  }

  /// 構建特殊時刻分析區域
  Widget _buildSpecialSection() {
    return Column(
      children: [
        _buildAnalysisCard(
          '季節能量分析',
          '了解春夏秋冬四季對您的影響',
          Icons.wb_sunny,
          AppColors.solarAmber,
          ChartType.equinoxSolstice,
          isSpecial: true,
        ),
        _buildAnalysisCard(
          '重要天象影響',
          '分析日蝕月蝕等特殊天象對您的影響',
          Icons.brightness_1,
          Colors.purple,
          ChartType.eclipse,
          requiresTwoPeople: true,
        ),
      ],
    );
  }

  /// 構建分析卡片
  Widget _buildAnalysisCard(
    String title,
    String description,
    IconData icon,
    Color color,
    ChartType chartType, {
    bool requiresTwoPeople = false,
    bool requiresDateSelection = false,
    bool requiresTimeSelection = false,
    bool isSpecial = false,
  }) {
    return StyledCard(
      elevation: 2,
      margin: const EdgeInsets.only(bottom: 12),
      borderRadius: BorderRadius.circular(12),
      onTap: () => _handleAnalysis(
        chartType,
        title,
        requiresTwoPeople: requiresTwoPeople,
        requiresDateSelection: requiresDateSelection,
        requiresTimeSelection: requiresTimeSelection,
        isSpecial: isSpecial,
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(25),
              ),
              child: Icon(
                icon,
                color: color,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textDark,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    description,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Colors.grey,
                      height: 1.3,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      if (requiresTwoPeople && requiresTimeSelection) ...[
                        Icon(
                          Icons.people,
                          size: 14,
                          color: color,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '需要兩人+時間',
                          style: TextStyle(
                            fontSize: 12,
                            color: color,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ] else if (requiresTwoPeople) ...[
                        Icon(
                          Icons.people,
                          size: 14,
                          color: color,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '需要兩人資料',
                          style: TextStyle(
                            fontSize: 12,
                            color: color,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ] else if (requiresDateSelection) ...[
                        Icon(
                          Icons.schedule,
                          size: 14,
                          color: color,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '需要選擇時間',
                          style: TextStyle(
                            fontSize: 12,
                            color: color,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ] else if (requiresTimeSelection) ...[
                        Icon(
                          Icons.access_time,
                          size: 14,
                          color: color,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '需要時間參數',
                          style: TextStyle(
                            fontSize: 12,
                            color: color,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ] else if (isSpecial) ...[
                        Icon(
                          Icons.auto_awesome,
                          size: 14,
                          color: color,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '特殊分析',
                          style: TextStyle(
                            fontSize: 12,
                            color: color,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ] else ...[
                        Icon(
                          Icons.person,
                          size: 14,
                          color: color,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '個人分析',
                          style: TextStyle(
                            fontSize: 12,
                            color: color,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ],
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: color,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  /// 處理分析
  Future<void> _handleAnalysis(
    ChartType chartType,
    String title, {
    bool requiresTwoPeople = false,
    bool requiresDateSelection = false,
    bool requiresTimeSelection = false,
    bool isSpecial = false,
  }) async {
    if (isSpecial) {
      _handleSpecialChart(chartType);
      return;
    }

    if (requiresTwoPeople && requiresTimeSelection) {
      await _handleTwoPeopleTimeSelectionChart(chartType, title);
    } else if (requiresTwoPeople) {
      await _handleTwoPeopleChart(chartType, title);
    } else if (requiresDateSelection) {
      await _handleDateSelectionChart(chartType, title);
    } else if (requiresTimeSelection) {
      await _handleTimeSelectionChart(chartType, title);
    } else {
      await _handleSinglePersonChart(chartType, title);
    }
  }

  /// 處理單人分析
  Future<void> _handleSinglePersonChart(
      ChartType chartType, String title) async {
    final selectedPerson = await _selectBirthData(title);
    if (selectedPerson != null && mounted) {
      final chartData = ChartData(
        chartType: ChartType.natal,
        primaryPerson: selectedPerson,
      );

      _navigateToAIInterpretation(chartData);
    }
  }

  /// 導航到 AI 解讀選擇頁面
  Future<void> _navigateToAIInterpretation(ChartData chartData) async {
    chartData = await ChartViewModel.calculateChartData(chartData);
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AIInterpretationSelectionPage(
          chartData: chartData,
        ),
      ),
    );
  }

  /// 處理雙人分析
  Future<void> _handleTwoPeopleChart(ChartType chartType, String title) async {
    if (mounted) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => StarlightChartSelectionPage(
            chartType: chartType,
            analysisTitle: title,
            fromAnalysisPage: true,
          ),
        ),
      );
    }
  }

  /// 處理需要日期選擇的分析
  Future<void> _handleDateSelectionChart(
      ChartType chartType, String title) async {
    final selectedPerson = await _selectBirthData(title);
    if (selectedPerson != null && mounted) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => StarlightChartSelectionPage(
            primaryPerson: selectedPerson,
            chartType: chartType,
            analysisTitle: title,
            fromAnalysisPage: true,
          ),
        ),
      );
    }
  }

  /// 處理需要時間選擇的分析
  Future<void> _handleTimeSelectionChart(
      ChartType chartType, String title) async {
    final selectedPerson = await _selectBirthData(title);
    if (selectedPerson != null && mounted) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => StarlightChartSelectionPage(
            primaryPerson: selectedPerson,
            chartType: chartType,
            analysisTitle: title,
            fromAnalysisPage: true,
          ),
        ),
      );
    }
  }

  /// 處理需要兩人資料和時間選擇的分析
  Future<void> _handleTwoPeopleTimeSelectionChart(
      ChartType chartType, String title) async {
    if (mounted) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => StarlightChartSelectionPage(
            chartType: chartType,
            analysisTitle: title,
            fromAnalysisPage: true,
          ),
        ),
      );
    }
  }

  /// 處理特殊分析
  void _handleSpecialChart(ChartType chartType) {
    switch (chartType) {
      case ChartType.equinoxSolstice:
        _navigateToEquinoxSolstice();
        break;
      default:
        _showFeatureNotAvailable('特殊分析');
    }
  }

  /// 選擇出生資料
  Future<BirthData?> _selectBirthData(String title) async {
    return await Navigator.push<BirthData>(
      context,
      MaterialPageRoute(
        builder: (context) => const FilesPage(
          isSelectionMode: true,
        ),
      ),
    );
  }
}
