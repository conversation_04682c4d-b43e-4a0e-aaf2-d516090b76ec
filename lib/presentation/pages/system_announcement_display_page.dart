import 'package:flutter/material.dart';

import '../../data/models/admin/system_announcement.dart';
import '../widgets/dialogs/system_announcement_dialog.dart';

/// 系統公告顯示頁面（啟動時顯示）
/// 現在使用 Dialog 形式顯示，提供更好的用戶體驗
class SystemAnnouncementDisplayPage extends StatelessWidget {
  final List<SystemAnnouncementWithId> announcements;

  const SystemAnnouncementDisplayPage({
    super.key,
    required this.announcements,
  });

  /// 顯示系統公告
  static Future<bool?> show(
    BuildContext context,
    List<SystemAnnouncementWithId> announcements,
  ) {
    return SystemAnnouncementDialog.show(context, announcements);
  }

  @override
  Widget build(BuildContext context) {
    // 直接顯示 Dialog，不需要頁面
    WidgetsBinding.instance.addPostFrameCallback((_) {
      SystemAnnouncementDialog.show(context, announcements).then((result) {
        // Dialog 關閉後，關閉這個頁面
        if (context.mounted) {
          Navigator.of(context).pop(result);
        }
      });
    });

    // 返回透明的容器，因為實際內容在 Dialog 中
    return const Scaffold(
      backgroundColor: Colors.transparent,
      body: SizedBox.shrink(),
    );
  }
}


