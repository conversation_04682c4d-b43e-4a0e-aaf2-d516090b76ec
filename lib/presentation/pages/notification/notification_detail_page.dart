import 'package:flutter/material.dart';

import '../../../astreal.dart';
import '../../../data/models/notification/notification_model.dart';
import '../../../data/services/notification/notification_service.dart';
import '../../../shared/widgets/unified_card.dart';

/// 通知詳情頁面
class NotificationDetailPage extends StatefulWidget {
  final NotificationModel notification;

  const NotificationDetailPage({
    super.key,
    required this.notification,
  });

  @override
  State<NotificationDetailPage> createState() => _NotificationDetailPageState();
}

class _NotificationDetailPageState extends State<NotificationDetailPage> {
  late NotificationModel _notification;

  @override
  void initState() {
    super.initState();
    _notification = widget.notification;
    _markAsReadIfNeeded();
  }

  /// 如果通知未讀，標記為已讀
  Future<void> _markAsReadIfNeeded() async {
    if (!_notification.isRead) {
      try {
        await NotificationService.markNotificationAsRead(_notification.id);
        setState(() {
          _notification = _notification.copyWith(isRead: true);
        });
      } catch (e) {
        logger.e('標記通知為已讀失敗: $e');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.scaffoldBackground,
      appBar: _buildAppBar(),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
            // 通知標題卡片
            _buildTitleCard(),
            
            const SizedBox(height: 16),
            
            // 通知內容卡片
            _buildContentCard(),
            
            const SizedBox(height: 16),
            
            // 通知圖片（如果有）
            if (_notification.imageUrl != null && _notification.imageUrl!.isNotEmpty)
              _buildImageCard(),
            
            const SizedBox(height: 16),
            
            // 通知詳細資訊
            _buildDetailsCard(),
            
            const SizedBox(height: 16),
            
            // 操作按鈕
            _buildActionButtons(),
          ],
        ),
        ),
      ),
    );
  }

  /// 構建應用欄
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text('通知詳情'),
      backgroundColor: AppColors.royalIndigo,
      foregroundColor: Colors.white,
      elevation: 0,
      flexibleSpace: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppColors.royalIndigo,
              AppColors.royalIndigo.withValues(alpha: 0.8),
            ],
          ),
        ),
      ),
      actions: [
        PopupMenuButton<String>(
          onSelected: (value) {
            switch (value) {
              case 'share':
                _shareNotification();
                break;
              case 'delete':
                _deleteNotification();
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'share',
              child: Row(
                children: [
                  Icon(Icons.share),
                  SizedBox(width: 12),
                  Text('分享'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete, color: Colors.red),
                  SizedBox(width: 12),
                  Text('刪除'),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 構建標題卡片
  Widget _buildTitleCard() {
    return UnifiedCard(
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(20),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 通知圖標
            Container(
              width: 56,
              height: 56,
              decoration: BoxDecoration(
                color: _notification.type.color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(28),
                border: Border.all(
                  color: _notification.type.color.withValues(alpha: 0.3),
                  width: 2,
                ),
              ),
              child: Icon(
                _notification.type.icon,
                color: _notification.type.color,
                size: 28,
              ),
            ),
            
            const SizedBox(width: 16),
            
            // 標題和類型
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _notification.title,
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textDark,
                      height: 1.3,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                        decoration: BoxDecoration(
                          color: _notification.type.color.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: _notification.type.color.withValues(alpha: 0.3),
                            width: 1,
                          ),
                        ),
                        child: Text(
                          _notification.type.displayName,
                          style: TextStyle(
                            color: _notification.type.color,
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                      if (_notification.isHighPriority) ...[
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: _notification.isUrgent 
                                  ? [Colors.red.shade400, Colors.red.shade600]
                                  : [Colors.orange.shade400, Colors.orange.shade600],
                            ),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            _notification.priority.displayName,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 構建內容卡片
  Widget _buildContentCard() {
    return UnifiedCard(
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '通知內容',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppColors.textDark,
              ),
            ),
            const SizedBox(height: 12),
            SizedBox(
              width: double.infinity,
              child: Text(
                _notification.body,
                style: const TextStyle(
                  fontSize: 15,
                  color: AppColors.textDark,
                  height: 1.6,
                ),
                textAlign: TextAlign.start,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 構建圖片卡片
  Widget _buildImageCard() {
    return UnifiedCard(
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '相關圖片',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppColors.textDark,
              ),
            ),
            const SizedBox(height: 12),
            ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: Image.network(
                _notification.imageUrl!,
                width: double.infinity,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    height: 200,
                    decoration: BoxDecoration(
                      color: Colors.grey[200],
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.image_not_supported,
                            color: Colors.grey,
                            size: 48,
                          ),
                          SizedBox(height: 8),
                          Text(
                            '圖片載入失敗',
                            style: TextStyle(color: Colors.grey),
                          ),
                        ],
                      ),
                    ),
                  );
                },
                loadingBuilder: (context, child, loadingProgress) {
                  if (loadingProgress == null) return child;
                  return Container(
                    height: 200,
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Center(
                      child: CircularProgressIndicator(),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 構建詳細資訊卡片
  Widget _buildDetailsCard() {
    return UnifiedCard(
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '詳細資訊',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppColors.textDark,
              ),
            ),
            const SizedBox(height: 16),
            _buildDetailRow('通知ID', _notification.id),
            _buildDetailRow('創建時間', _formatDateTime(_notification.createdAt)),
            if (_notification.scheduledAt != null)
              _buildDetailRow('預定時間', _formatDateTime(_notification.scheduledAt!)),
            _buildDetailRow('讀取狀態', _notification.isRead ? '已讀' : '未讀'),
            if (_notification.actionUrl != null && _notification.actionUrl!.isNotEmpty)
              _buildDetailRow('動作連結', _notification.actionUrl!, isUrl: true),
          ],
        ),
      ),
    );
  }

  /// 構建詳細資訊行
  Widget _buildDetailRow(String label, String value, {bool isUrl = false}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: isUrl
                ? GestureDetector(
                    onTap: () {
                      // TODO: 處理 URL 點擊
                      logger.i('點擊 URL: $value');
                    },
                    child: Text(
                      value,
                      style: const TextStyle(
                        fontSize: 14,
                        color: AppColors.royalIndigo,
                        decoration: TextDecoration.underline,
                      ),
                    ),
                  )
                : Text(
                    value,
                    style: const TextStyle(
                      fontSize: 14,
                      color: AppColors.textDark,
                    ),
                  ),
          ),
        ],
      ),
    );
  }

  /// 構建操作按鈕
  Widget _buildActionButtons() {
    return SizedBox(
      width: double.infinity,
      child: Row(
        children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: _shareNotification,
            icon: const Icon(Icons.share),
            label: const Text('分享通知'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.royalIndigo,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: OutlinedButton.icon(
            onPressed: _deleteNotification,
            icon: const Icon(Icons.delete, color: Colors.red),
            label: const Text('刪除通知'),
            style: OutlinedButton.styleFrom(
              foregroundColor: Colors.red,
              side: const BorderSide(color: Colors.red),
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),
      ],
      ),
    );
  }

  /// 格式化日期時間
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}/${dateTime.month.toString().padLeft(2, '0')}/${dateTime.day.toString().padLeft(2, '0')} '
           '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  /// 分享通知
  void _shareNotification() {
    final shareText = '${_notification.title}\n\n${_notification.body}';
    // TODO: 實現分享功能
    logger.i('分享通知: $shareText');
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('分享功能開發中')),
    );
  }

  /// 刪除通知
  void _deleteNotification() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('確認刪除'),
        content: const Text('確定要刪除這則通知嗎？此操作無法復原。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              
              try {
                await NotificationService.deleteNotification(_notification.id);
                
                if (mounted) {
                  Navigator.of(context).pop(); // 返回通知列表
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('通知已刪除')),
                  );
                }
              } catch (e) {
                logger.e('刪除通知失敗: $e');
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('刪除失敗，請稍後再試')),
                  );
                }
              }
            },
            child: const Text(
              '刪除',
              style: TextStyle(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }
}
