import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import '../../../astreal.dart';
import '../../../data/models/notification/notification_model.dart';
import '../../../data/services/notification/notification_service.dart';
import '../../../shared/widgets/common/responsive_wrapper.dart';
import '../../widgets/notification/notification_list_tile.dart';
import 'notification_detail_page.dart';

/// 通知頁面
class NotificationPage extends StatefulWidget {
  const NotificationPage({super.key});

  @override
  State<NotificationPage> createState() => _NotificationPageState();
}

class _NotificationPageState extends State<NotificationPage> {
  List<NotificationModel> _notifications = [];
  bool _isLoading = true;
  bool _isRefreshing = false;
  NotificationType? _selectedType;
  bool _showUnreadOnly = false;

  @override
  void initState() {
    super.initState();
    _loadNotifications();
  }

  /// 載入通知
  Future<void> _loadNotifications() async {
    if (!mounted) return;
    
    setState(() {
      _isLoading = true;
    });

    try {
      final notifications = await NotificationService.getAllNotifications(
        forceRefresh: true,
      );
      
      if (mounted) {
        setState(() {
          _notifications = notifications;
          _isLoading = false;
        });
      }
    } catch (e) {
      logger.e('載入通知失敗: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('載入通知失敗，請稍後再試')),
        );
      }
    }
  }

  /// 刷新通知
  Future<void> _refreshNotifications() async {
    if (_isRefreshing) return;
    
    setState(() {
      _isRefreshing = true;
    });

    try {
      final notifications = await NotificationService.getAllNotifications(
        forceRefresh: true,
      );
      
      if (mounted) {
        setState(() {
          _notifications = notifications;
          _isRefreshing = false;
        });
      }
    } catch (e) {
      logger.e('刷新通知失敗: $e');
      if (mounted) {
        setState(() {
          _isRefreshing = false;
        });
      }
    }
  }

  /// 標記所有通知為已讀
  Future<void> _markAllAsRead() async {
    try {
      await NotificationService.markAllNotificationsAsRead();
      
      setState(() {
        _notifications = _notifications.map((n) => n.copyWith(isRead: true)).toList();
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('所有通知已標記為已讀')),
        );
      }
    } catch (e) {
      logger.e('標記所有通知為已讀失敗: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('操作失敗，請稍後再試')),
        );
      }
    }
  }

  /// 清除所有通知
  Future<void> _clearAllNotifications() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('確認清除'),
        content: const Text('確定要清除所有通知嗎？此操作無法復原。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('確定'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await NotificationService.clearAllNotifications();
        
        setState(() {
          _notifications.clear();
        });
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('所有通知已清除')),
          );
        }
      } catch (e) {
        logger.e('清除所有通知失敗: $e');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('清除失敗，請稍後再試')),
          );
        }
      }
    }
  }

  /// 獲取過濾後的通知
  List<NotificationModel> get _filteredNotifications {
    var filtered = _notifications;
    
    // 按類型過濾
    if (_selectedType != null) {
      filtered = filtered.where((n) => n.type == _selectedType).toList();
    }
    
    // 只顯示未讀
    if (_showUnreadOnly) {
      filtered = filtered.where((n) => !n.isRead).toList();
    }
    
    return filtered;
  }

  /// 獲取未讀通知數量
  int get _unreadCount => _notifications.where((n) => !n.isRead).length;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.scaffoldBackground,
      appBar: _buildAppBar(),
      body: ResponsivePageWrapper(
        maxWidth: 800.0, // 通知中心適合中等寬度
        child: Column(
          children: [
            // 篩選欄
            _buildFilterBar(),

            // 通知列表
            Expanded(
              child: _buildNotificationList(),
            ),
          ],
        ),
      ),
    );
  }

  /// 構建應用欄
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      elevation: 0,
      title: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '通知中心',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          if (_unreadCount > 0)
            Container(
              margin: const EdgeInsets.only(top: 2),
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                '$_unreadCount 則未讀',
                style: const TextStyle(
                  fontSize: 11,
                  color: Colors.white,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
        ],
      ),
      backgroundColor: AppColors.royalIndigo,
      foregroundColor: Colors.white,
      flexibleSpace: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppColors.royalIndigo,
              AppColors.royalIndigo.withValues(alpha: 0.8),
            ],
          ),
        ),
      ),
      actions: [
        // 標記全部已讀
        if (_unreadCount > 0)
          Container(
            margin: const EdgeInsets.only(right: 8),
            child: IconButton(
              icon: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: const Icon(Icons.done_all, size: 20),
              ),
              onPressed: _markAllAsRead,
              tooltip: '標記全部已讀',
            ),
          ),

        // 更多選項
        PopupMenuButton<String>(
          icon: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(20),
            ),
            child: const Icon(Icons.more_vert, size: 20),
          ),
          onSelected: (value) async {
            switch (value) {
              case 'clear_all':
                _clearAllNotifications();
                break;
              case 'settings':
                await _openNotificationSettings();
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'clear_all',
              child: Row(
                children: [
                  Icon(Icons.clear_all, color: Colors.red),
                  SizedBox(width: 12),
                  Text('清除所有通知'),
                ],
              ),
            ),
            // 只在支援的平台上顯示通知設定選項
            if (NotificationService.canOpenNotificationSettings)
              const PopupMenuItem(
                value: 'settings',
                child: Row(
                  children: [
                    Icon(Icons.settings, color: AppColors.royalIndigo),
                    SizedBox(width: 12),
                    Text('通知設定'),
                  ],
                ),
              ),
          ],
        ),
        const SizedBox(width: 8),
      ],
    );
  }

  /// 構建篩選欄
  Widget _buildFilterBar() {
    return Container(
      margin: const EdgeInsets.fromLTRB(16, 8, 16, 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // 篩選標題
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppColors.royalIndigo.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.tune,
                    size: 20,
                    color: AppColors.royalIndigo,
                  ),
                ),
                const SizedBox(width: 12),
                const Text(
                  '篩選條件',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textDark,
                  ),
                ),
                const Spacer(),
                // 重置篩選按鈕
                if (_selectedType != null || _showUnreadOnly)
                  TextButton.icon(
                    onPressed: () {
                      setState(() {
                        _selectedType = null;
                        _showUnreadOnly = false;
                      });
                    },
                    icon: const Icon(Icons.refresh, size: 16),
                    label: const Text('重置'),
                    style: TextButton.styleFrom(
                      foregroundColor: Colors.grey[600],
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    ),
                  ),
              ],
            ),

            const SizedBox(height: 16),

            // 類型篩選
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.category, size: 18, color: Colors.grey[600]),
                    const SizedBox(width: 8),
                    Text(
                      '通知類型',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: Colors.grey[700],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: [
                      _buildFilterChip('全部', _selectedType == null, () {
                        setState(() {
                          _selectedType = null;
                        });
                      }),
                      ...NotificationType.values.map((type) =>
                        _buildFilterChip(
                          type.displayName,
                          _selectedType == type,
                          () {
                            setState(() {
                              _selectedType = type;
                            });
                          },
                          icon: type.icon,
                          color: type.color,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // 讀取狀態篩選
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.visibility, size: 18, color: Colors.grey[600]),
                    const SizedBox(width: 8),
                    Text(
                      '讀取狀態',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: Colors.grey[700],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    _buildFilterChip('全部', !_showUnreadOnly, () {
                      setState(() {
                        _showUnreadOnly = false;
                      });
                    }),
                    _buildFilterChip('僅未讀', _showUnreadOnly, () {
                      setState(() {
                        _showUnreadOnly = true;
                      });
                    }, icon: Icons.mark_email_unread),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 構建篩選標籤
  Widget _buildFilterChip(
    String label,
    bool isSelected,
    VoidCallback onTap, {
    IconData? icon,
    Color? color,
  }) {
    return Padding(
      padding: const EdgeInsets.only(right: 8),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(20),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            color: isSelected
                ? (color ?? AppColors.royalIndigo).withValues(alpha: 0.15)
                : Colors.grey.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: isSelected
                  ? (color ?? AppColors.royalIndigo).withValues(alpha: 0.5)
                  : Colors.grey.withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (icon != null) ...[
                Icon(
                  icon,
                  size: 14,
                  color: isSelected
                      ? (color ?? AppColors.royalIndigo)
                      : Colors.grey[600],
                ),
                const SizedBox(width: 4),
              ],
              Text(
                label,
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                  color: isSelected
                      ? (color ?? AppColors.royalIndigo)
                      : Colors.grey[700],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 構建通知列表
  Widget _buildNotificationList() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    final filteredNotifications = _filteredNotifications;

    if (filteredNotifications.isEmpty) {
      return _buildEmptyState();
    }

    return RefreshIndicator(
      onRefresh: _refreshNotifications,
      child: ListView.builder(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: filteredNotifications.length,
        itemBuilder: (context, index) {
          final notification = filteredNotifications[index];
          return NotificationListTile(
            notification: notification,
            onTap: () => _handleNotificationTap(notification),
            onDelete: () => _deleteNotification(notification.id),
          );
        },
      ),
    );
  }

  /// 構建空狀態
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: Colors.grey.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              _showUnreadOnly ? Icons.mark_email_read : Icons.notifications_none,
              size: 60,
              color: Colors.grey[400],
            ),
          ),
          const SizedBox(height: 24),
          Text(
            _showUnreadOnly ? '沒有未讀通知' : '暫無通知',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32),
            child: Text(
              _showUnreadOnly
                  ? '所有通知都已讀取，保持關注最新動態！'
                  : '當有新通知時會顯示在這裡\n我們會及時通知您重要訊息',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[500],
                height: 1.5,
              ),
            ),
          ),
          const SizedBox(height: 24),
          if (!_showUnreadOnly)
            ElevatedButton.icon(
              onPressed: _refreshNotifications,
              icon: const Icon(Icons.refresh, size: 18),
              label: const Text('重新整理'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.royalIndigo,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(24),
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// 處理通知點擊
  void _handleNotificationTap(NotificationModel notification) {
    // 標記為已讀
    if (!notification.isRead) {
      NotificationService.markNotificationAsRead(notification.id);
      setState(() {
        final index = _notifications.indexWhere((n) => n.id == notification.id);
        if (index != -1) {
          _notifications[index] = notification.copyWith(isRead: true);
        }
      });
    }

    // 跳轉到通知詳情頁面
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => NotificationDetailPage(
          notification: notification.copyWith(isRead: true),
        ),
      ),
    );
  }

  /// 打開通知設定
  Future<void> _openNotificationSettings() async {
    try {
      final success = await NotificationService.openNotificationSettings();

      if (mounted) {
        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('已引導您前往通知設定'),
              backgroundColor: Colors.green,
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text('無法自動打開設定頁面'),
                  const SizedBox(height: 4),
                  Text(
                    kIsWeb
                        ? '請在瀏覽器設定中允許通知權限'
                        : '請手動前往系統設定 > 通知 > AstReal 開啟通知權限',
                    style: const TextStyle(fontSize: 12),
                  ),
                ],
              ),
              backgroundColor: Colors.orange,
              duration: const Duration(seconds: 5),
            ),
          );
        }
      }
    } catch (e) {
      logger.e('打開通知設定失敗: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('打開設定失敗，請稍後再試'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// 刪除通知
  Future<void> _deleteNotification(String notificationId) async {
    try {
      await NotificationService.deleteNotification(notificationId);
      
      setState(() {
        _notifications.removeWhere((n) => n.id == notificationId);
      });
    } catch (e) {
      logger.e('刪除通知失敗: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('刪除失敗，請稍後再試')),
        );
      }
    }
  }
}
