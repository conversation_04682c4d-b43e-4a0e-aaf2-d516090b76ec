import 'package:flutter/material.dart';

import '../../astreal.dart';
import '../../data/services/notification/daily_astrology_notification_service.dart';
import '../../shared/utils/user_preferences.dart';

/// 每日星相推播設定頁面
class DailyAstrologySettingsPage extends StatefulWidget {
  const DailyAstrologySettingsPage({super.key});

  @override
  State<DailyAstrologySettingsPage> createState() => _DailyAstrologySettingsPageState();
}

class _DailyAstrologySettingsPageState extends State<DailyAstrologySettingsPage> {
  bool _notificationEnabled = true;
  bool _personalizedEnabled = true;
  String _notificationTime = '10:00';
  bool _hasPermission = false;
  bool _isLoading = true;

  final List<String> _timeOptions = [
    '08:00',
    '09:00',
    '10:00',
    '11:00',
    '12:00',
    '18:00',
    '19:00',
    '20:00',
    '21:00',
  ];

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  /// 載入設定
  Future<void> _loadSettings() async {
    try {
      final notificationEnabled = await UserPreferences.getDailyAstrologyNotificationEnabled();
      final personalizedEnabled = await UserPreferences.getDailyAstrologyPersonalizedEnabled();
      final notificationTime = await UserPreferences.getDailyAstrologyNotificationTime();
      final hasPermission = await DailyAstrologyNotificationService.checkPermissionStatus();

      if (mounted) {
        setState(() {
          _notificationEnabled = notificationEnabled;
          _personalizedEnabled = personalizedEnabled;
          _notificationTime = notificationTime;
          _hasPermission = hasPermission;
          _isLoading = false;
        });
      }
    } catch (e) {
      logger.e('載入推播設定失敗: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// 切換推播啟用狀態
  Future<void> _toggleNotification(bool enabled) async {
    try {
      if (enabled && !_hasPermission) {
        // 請求權限
        final granted = await DailyAstrologyNotificationService.requestPermissions();
        if (!granted) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('需要推播權限才能啟用每日星相提醒'),
                backgroundColor: Colors.orange,
              ),
            );
          }
          return;
        }
        setState(() {
          _hasPermission = true;
        });
      }

      await UserPreferences.saveDailyAstrologyNotificationEnabled(enabled);
      await DailyAstrologyNotificationService.updateNotificationSettings();

      setState(() {
        _notificationEnabled = enabled;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(enabled ? '每日星相推播已啟用' : '每日星相推播已停用'),
            backgroundColor: AppColors.successGreen,
          ),
        );
      }
    } catch (e) {
      logger.e('切換推播設定失敗: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('設定失敗: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// 切換個人化推播
  Future<void> _togglePersonalized(bool enabled) async {
    try {
      await UserPreferences.saveDailyAstrologyPersonalizedEnabled(enabled);
      
      setState(() {
        _personalizedEnabled = enabled;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(enabled ? '個人化推播已啟用' : '個人化推播已停用'),
            backgroundColor: AppColors.successGreen,
          ),
        );
      }
    } catch (e) {
      logger.e('切換個人化設定失敗: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('設定失敗: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// 更改推播時間
  Future<void> _changeNotificationTime(String time) async {
    try {
      await UserPreferences.saveDailyAstrologyNotificationTime(time);
      
      if (_notificationEnabled) {
        await DailyAstrologyNotificationService.updateNotificationSettings();
      }

      setState(() {
        _notificationTime = time;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('推播時間已設定為 $time'),
            backgroundColor: AppColors.successGreen,
          ),
        );
      }
    } catch (e) {
      logger.e('更改推播時間失敗: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('設定失敗: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// 發送測試推播
  Future<void> _sendTestNotification() async {
    try {
      await DailyAstrologyNotificationService.sendTestNotification();
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('測試推播已發送'),
            backgroundColor: AppColors.successGreen,
          ),
        );
      }
    } catch (e) {
      logger.e('發送測試推播失敗: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('發送失敗: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('每日星相推播設定'),
        backgroundColor: AppColors.solarAmber,
        foregroundColor: Colors.white,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 推播啟用設定
                  _buildSettingCard(
                    title: '每日星相推播',
                    subtitle: '每天定時接收星相提醒',
                    child: Switch(
                      value: _notificationEnabled,
                      onChanged: _toggleNotification,
                      activeColor: AppColors.solarAmber,
                    ),
                  ),

                  const SizedBox(height: 16),

                  // 推播時間設定
                  if (_notificationEnabled) ...[
                    _buildSettingCard(
                      title: '推播時間',
                      subtitle: '選擇接收推播的時間',
                      child: DropdownButton<String>(
                        value: _notificationTime,
                        onChanged: (String? newValue) {
                          if (newValue != null) {
                            _changeNotificationTime(newValue);
                          }
                        },
                        items: _timeOptions.map<DropdownMenuItem<String>>((String value) {
                          return DropdownMenuItem<String>(
                            value: value,
                            child: Text(value),
                          );
                        }).toList(),
                        underline: Container(),
                      ),
                    ),

                    const SizedBox(height: 16),

                    // 個人化推播設定
                    _buildSettingCard(
                      title: '個人化推播',
                      subtitle: '根據你的出生資料提供個人化星相資訊',
                      child: Switch(
                        value: _personalizedEnabled,
                        onChanged: _togglePersonalized,
                        activeColor: AppColors.royalIndigo,
                      ),
                    ),

                    const SizedBox(height: 16),

                    // 測試推播按鈕
                    _buildTestButton(),

                    const SizedBox(height: 24),
                  ],

                  // 權限狀態提示
                  if (!_hasPermission) ...[
                    _buildPermissionWarning(),
                    const SizedBox(height: 16),
                  ],

                  // 說明區塊
                  _buildInfoSection(),
                ],
              ),
            ),
    );
  }

  /// 構建設定卡片
  Widget _buildSettingCard({
    required String title,
    required String subtitle,
    required Widget child,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey[200]!),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textDark,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          child,
        ],
      ),
    );
  }

  /// 構建測試按鈕
  Widget _buildTestButton() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.solarAmber.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: AppColors.solarAmber.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '測試推播',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: AppColors.textDark,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '立即發送一則測試推播，確認設定是否正常運作',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 16),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _sendTestNotification,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.solarAmber,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: const Text('發送測試推播'),
            ),
          ),
        ],
      ),
    );
  }

  /// 構建權限警告
  Widget _buildPermissionWarning() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.orange.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.warning_outlined,
                color: Colors.orange[700],
                size: 24,
              ),
              const SizedBox(width: 12),
              const Text(
                '需要推播權限',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textDark,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            '請在系統設定中允許本應用發送推播通知，才能接收每日星相提醒。',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[700],
            ),
          ),
          const SizedBox(height: 16),
          SizedBox(
            width: double.infinity,
            child: OutlinedButton(
              onPressed: () async {
                final granted = await DailyAstrologyNotificationService.requestPermissions();
                if (granted) {
                  setState(() {
                    _hasPermission = true;
                  });
                }
              },
              style: OutlinedButton.styleFrom(
                foregroundColor: Colors.orange[700],
                side: BorderSide(color: Colors.orange[700]!),
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: const Text('請求權限'),
            ),
          ),
        ],
      ),
    );
  }

  /// 構建說明區塊
  Widget _buildInfoSection() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.info_outline,
                color: Colors.grey[600],
                size: 24,
              ),
              const SizedBox(width: 12),
              const Text(
                '關於每日星相推播',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textDark,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          _buildInfoItem('📅', '每日定時推播重要的星象事件和能量變化'),
          _buildInfoItem('🌟', '包含新月、滿月、行星相位、逆行等重要天象'),
          _buildInfoItem('👤', '個人化推播會根據你的出生資料提供專屬建議'),
          _buildInfoItem('⏰', '可自訂推播時間，配合你的作息習慣'),
          _buildInfoItem('🔕', '隨時可以在此頁面調整或關閉推播'),
        ],
      ),
    );
  }

  /// 構建說明項目
  Widget _buildInfoItem(String emoji, String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            emoji,
            style: const TextStyle(fontSize: 16),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[700],
                height: 1.4,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
