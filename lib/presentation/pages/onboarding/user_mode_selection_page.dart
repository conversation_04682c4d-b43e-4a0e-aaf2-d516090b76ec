import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../astreal.dart';
import '../../../shared/widgets/common/responsive_wrapper.dart';

/// 用戶模式選擇頁面
class UserModeSelectionPage extends StatefulWidget {
  /// 是否為模式切換（而非初次選擇）
  final bool isModeSwitch;

  const UserModeSelectionPage({
    super.key,
    this.isModeSwitch = false,
  });

  @override
  State<UserModeSelectionPage> createState() => _UserModeSelectionPageState();
}

class _UserModeSelectionPageState extends State<UserModeSelectionPage>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.0, 0.6, curve: Curves.easeOut),
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.3, 1.0, curve: Curves.easeOut),
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// 保存用戶模式選擇
  Future<void> _saveUserMode(String mode) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('user_mode', mode);
    await prefs.setBool('has_selected_mode', true);

    logger.i('用戶模式已保存: $mode, 是否為模式切換: ${widget.isModeSwitch}');

    if (mounted) {
      if (widget.isModeSwitch) {
        // 如果是模式切換，使用最徹底的方式重置應用
        logger.i('執行模式切換導航，完全重置路由堆疊');

        // 獲取根導航器
        final navigator = Navigator.of(context, rootNavigator: true);

        // 方法1：強制清除所有路由到根
        navigator.pushNamedAndRemoveUntil(
          '/main',
          (Route<dynamic> route) {
            logger.d('檢查路由: ${route.settings.name}, 是否保留: false');
            return false; // 不保留任何路由
          },
        );
      } else {
        // 如果是初次選擇，導航到主頁面
        Navigator.pushReplacementNamed(context, '/main');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            AppColors.royalIndigo,
            AppColors.indigoSurface,
          ],
        ),
      ),
      child: SafeArea(
        child: ResponsivePageWrapper(
          maxWidth: 600.0, // 模式選擇頁面適合中等寬度
          child: Padding(
            padding: ResponsiveUtils.getResponsivePadding(context),
            child: Column(
              children: [
                const SizedBox(height: 0),

                // 標題區域
                FadeTransition(
                  opacity: _fadeAnimation,
                  child: Column(
                    children: [
                      Container(
                        width: 80,
                        height: 80,
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(40),
                        ),
                        child: Image.asset(
                          'assets/images/flutter_launcher_icons.png',
                          width: 40,
                          height: 40,
                        ),
                      ),
                      const SizedBox(height: 24),
                      const Text(
                        '歡迎來到 AstReal',
                        style: TextStyle(
                          fontSize: 28,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(height: 12),
                      const Text(
                        '請選擇適合您的使用模式',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.white70,
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 34),

                // 模式選擇卡片
                Expanded(
                  child: SlideTransition(
                    position: _slideAnimation,
                    child: FadeTransition(
                      opacity: _fadeAnimation,
                      child: SingleChildScrollView(
                        child: Column(
                          children: [
                            // 初心者模式
                            _buildModeCard(
                              title: 'Starlight',
                              subtitle: '初心者模式',
                              description: '剛接觸占星學，想要輕鬆學習和探索',
                              icon: Icons.star_outline,
                              features: [
                                '簡化的星盤解讀',
                                '基礎占星知識',
                                '引導式學習',
                                '個人化建議',
                              ],
                              color: AppColors.solarAmber,
                              onTap: () => _saveUserMode('starlight'),
                            ),

                            const SizedBox(height: 24),

                            // 專業模式
                            _buildModeCard(
                              title: 'Starmaster',
                              subtitle: '占星師模式',
                              description: '熟悉占星學，需要專業工具和深度分析',
                              icon: Icons.star,
                              features: [
                                '完整星盤功能',
                                '專業分析工具',
                                '多種星盤類型',
                                '高級解讀選項',
                              ],
                              color: AppColors.royalIndigo,
                              onTap: () => _saveUserMode('starmaster'),
                            ),

                            const SizedBox(height: 40),

                            // 底部提示（移到滾動區域內）
                            const Text(
                              '您可以隨時在設定中更改模式',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.white60,
                              ),
                              textAlign: TextAlign.center,
                            ),

                            const SizedBox(height: 0),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    ));
  }

  /// 構建模式選擇卡片
  Widget _buildModeCard({
    required String title,
    required String subtitle,
    required String description,
    required IconData icon,
    required List<String> features,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(20),
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      width: 60,
                      height: 60,
                      decoration: BoxDecoration(
                        color: color.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(30),
                      ),
                      child: Icon(
                        icon,
                        size: 30,
                        color: color,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            title,
                            style: TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              color: color,
                            ),
                          ),
                          Text(
                            subtitle,
                            style: const TextStyle(
                              fontSize: 16,
                              color: Colors.grey,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Icon(
                      Icons.arrow_forward_ios,
                      color: color,
                      size: 20,
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                Text(
                  description,
                  style: const TextStyle(
                    fontSize: 16,
                    color: AppColors.textDark,
                    height: 1.4,
                  ),
                ),

                const SizedBox(height: 16),

                // 功能列表
                ...features.map((feature) => Padding(
                      padding: const EdgeInsets.only(bottom: 8),
                      child: Row(
                        children: [
                          Container(
                            width: 6,
                            height: 6,
                            decoration: BoxDecoration(
                              color: color,
                              borderRadius: BorderRadius.circular(3),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Text(
                            feature,
                            style: const TextStyle(
                              fontSize: 14,
                              color: Colors.grey,
                            ),
                          ),
                        ],
                      ),
                    )),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
