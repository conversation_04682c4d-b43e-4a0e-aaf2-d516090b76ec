import 'package:flutter/material.dart';

import '../../core/utils/build_info_utils.dart';
import '../../core/utils/logger_utils.dart';
import '../../data/services/api/remote_config_version_service.dart';
import '../../shared/utils/url_launcher_service.dart';
import '../../shared/widgets/common/responsive_wrapper.dart';
import '../../shared/widgets/update_dialog.dart';
import '../themes/app_theme.dart';
import '../widgets/common/styled_card.dart';

/// 關於我們頁面
class AboutUsPage extends StatelessWidget {
  const AboutUsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.grey.shade50,
        appBar: AppBar(
          title: const Text(
            '關於我們',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 20,
            ),
          ),
          backgroundColor: AppColors.royalIndigo,
          foregroundColor: Colors.white,
          elevation: 0,
          centerTitle: true,
        ),
        body: FutureBuilder<BuildInfo>(
            future: BuildInfoUtils.getBuildInfo(),
            builder: (context, snapshot) {
              return ResponsivePageWrapper(
                maxWidth: 800.0, // 關於我們頁面適合中等寬度
                child: ListView(
                  padding: ResponsiveUtils.getResponsivePadding(context),
                  children: [
                    // 應用介紹卡片
                    _buildAppIntroCard(),
                    const SizedBox(height: 20),

                    // 官網與服務卡片
                    _buildWebsiteCard(),
                    const SizedBox(height: 20),

                    // 社群媒體連結卡片
                    _buildSocialMediaCard(),
                    // const SizedBox(height: 20),

                    // 應用資訊卡片
                    // _buildAppInfoCard(),
                    const SizedBox(height: 20),

                    // 關於應用
                    _buildAboutSection(snapshot, context),

                    // const SizedBox(height: 20),

                    // 版權資訊
                    // _buildCopyrightInfo(),
                    const SizedBox(height: 40),
                  ],
                ),
              );
            }));
  }

  /// 構建應用介紹卡片
  Widget _buildAppIntroCard() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 16,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // 應用 Logo
            Container(
              width: 100,
              height: 100,
              decoration: BoxDecoration(
                color: AppColors.royalIndigo.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(24),
                border: Border.all(
                  color: AppColors.royalIndigo.withValues(alpha: 0.2),
                  width: 2,
                ),
              ),
              child: Center(
                child: Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(16),
                    child: Padding(
                      padding: const EdgeInsets.all(12),
                      child: Image.asset(
                        'assets/images/flutter_launcher_icons.png',
                        width: 36,
                        height: 36,
                        fit: BoxFit.contain,
                      ),
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(height: 24),

            // 應用名稱
            Text(
              'AstReal',
              style: TextStyle(
                fontSize: 26,
                fontWeight: FontWeight.bold,
                color: AppColors.royalIndigo,
                letterSpacing: 0.5,
              ),
            ),
            const SizedBox(height: 12),

            // 應用描述
            Text(
              '專業的占星分析應用程式\n結合傳統占星學與現代科技\n為您提供精準的星盤解讀與人生指引',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey.shade600,
                height: 1.6,
                fontWeight: FontWeight.w400,
              ),
            ),
            const SizedBox(height: 24),

            // 特色標籤
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                _buildFeatureTag(
                    '專業占星', Icons.auto_awesome, AppColors.royalIndigo),
                _buildFeatureTag('深入解讀', Icons.psychology, Colors.purple),
                _buildFeatureTag('多平台', Icons.devices, Colors.blue),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 構建社群媒體卡片
  Widget _buildSocialMediaCard() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 16,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 標題區域
            Row(
              children: [
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: Colors.pink.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Colors.pink.withValues(alpha: 0.2),
                      width: 1.5,
                    ),
                  ),
                  child: const Icon(
                    Icons.favorite,
                    color: Colors.pink,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '追蹤我們',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textDark,
                      ),
                    ),
                    Text(
                      '獲得最新占星資訊',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 20),

            // YouTube
            _buildEnhancedSocialMediaItem(
              icon: Icons.camera_alt,
              iconColor: const Color(0xFFE4405F),
              title: 'YouTube 頻道',
              subtitle: '@AstReal-Astrology',
              description: '每週星象解析・占星觀點深談・主題企劃系列',
              onTap: () => UrlLauncherService.launchExternal(
                'https://www.youtube.com/@AstReal-Astrology',
                showErrorSnackBar: false,
              ),
            ),
            const SizedBox(height: 16),

            // Instagram
            _buildEnhancedSocialMediaItem(
              icon: Icons.camera_alt,
              iconColor: const Color(0xFFE4405F),
              title: 'Instagram',
              subtitle: '@astreal.astrology',
              description: '圖文版星座運勢・靈感日籤・占星生活觀察',
              onTap: () => UrlLauncherService.launchExternal(
                'https://instagram.com/astreal.astrology',
                showErrorSnackBar: false,
              ),
            ),
            const SizedBox(height: 16),

            // Facebook
            _buildEnhancedSocialMediaItem(
              icon: Icons.facebook,
              iconColor: const Color(0xFF1877F2),
              title: 'Facebook 粉絲團',
              subtitle: '@astreal.astrology',
              description: '即時公告・專欄文章・社群互動與討論',
              onTap: () => UrlLauncherService.launchExternal(
                'https://www.facebook.com/astreal.astrology',
                showErrorSnackBar: false,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 構建官網與服務卡片
  Widget _buildWebsiteCard() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 16,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 標題區域
            Row(
              children: [
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: AppColors.royalIndigo.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: AppColors.royalIndigo.withValues(alpha: 0.2),
                      width: 1.5,
                    ),
                  ),
                  child: const Icon(
                    Icons.language,
                    color: AppColors.royalIndigo,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '官方網站',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textDark,
                      ),
                    ),
                    Text(
                      '探索更多專業內容',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 24),

            // 官網連結 - 簡潔設計
            _buildEnhancedSocialMediaItem(
              icon: Icons.public,
              iconColor: AppColors.royalIndigo,
              title: '官方網站',
              subtitle: '🌐 astreal-website.web.app',
              description: '🪐 深入分析與 VIP 內容',
              onTap: () => UrlLauncherService.launchURL(
                'https://astreal-website.web.app/',
                showErrorSnackBar: false,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 構建應用資訊卡片
  Widget _buildAppInfoCard() {
    return StyledCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: Colors.blue.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.info_outline,
                    color: Colors.blue,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                const Text(
                  '應用資訊',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textDark,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildInfoRow('應用名稱', 'AstReal'),
            _buildInfoRow('版本', '1.0.0'),
            _buildInfoRow('開發者', 'AstReal Team'),
            _buildInfoRow('支援平台', 'iOS, Android, macOS, Windows, Web'),
          ],
        ),
      ),
    );
  }

  /// 構建版權資訊
  Widget _buildCopyrightInfo() {
    return Container(
      alignment: Alignment.center, // 讓整個內容在容器內置中
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center, // 垂直置中
        crossAxisAlignment: CrossAxisAlignment.center, // 水平置中
        children: [
          Text(
            '© 2025 AstReal',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade600,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            '版權所有，保留一切權利',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildAboutSection(
      AsyncSnapshot<BuildInfo> snapshot, BuildContext context) {
    final BuildInfo? buildInfo = snapshot.data;
    final String version = buildInfo?.version ?? '未知';
    final String buildNumber = buildInfo?.buildNumber ?? '未知';
    final String buildDate = buildInfo?.buildDate ?? '未知';
    String formattedVersion =
        buildInfo?.formattedVersion ?? '$version ($buildNumber)';

    return StyledCard(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            /// 標題區
            Row(
              children: [
                Container(
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: AppColors.royalIndigo.withValues(alpha: 0.1),
                  ),
                  padding: const EdgeInsets.all(8),
                  child: const Icon(Icons.info_outline,
                      color: AppColors.royalIndigo, size: 24),
                ),
                const SizedBox(width: 12),
                const Text(
                  '關於應用',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textDark,
                  ),
                ),
                if (buildInfo != null) ...[
                  const SizedBox(width: 8),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                    decoration: BoxDecoration(
                      color: buildInfo.versionTagColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: buildInfo.versionTagColor.withValues(alpha: 0.3),
                        width: 1,
                      ),
                    ),
                    child: Text(
                      buildInfo.versionTag,
                      style: TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.w600,
                        color: buildInfo.versionTagColor,
                      ),
                    ),
                  ),
                ],
              ],
            ),
            const SizedBox(height: 20),

            /// App icon 與基本資訊
            Row(
              children: [
                Container(
                  width: 64,
                  height: 64,
                  decoration: BoxDecoration(
                    color: AppColors.royalIndigo.withValues(alpha: 0.05),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Center(
                    child: Image.asset(
                      'assets/images/flutter_launcher_icons.png',
                      width: 36,
                      height: 36,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          const Text(
                            'AstReal 占星應用',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: AppColors.textDark,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 6),
                      Text(
                        '版本: $formattedVersion',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade700,
                        ),
                      ),
                      Text(
                        '時間: $buildDate',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade700,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            const SizedBox(height: 20),

            /// 系統與平台資訊卡片
            Container(
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(12),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildInfoRow('開發者', 'AstReal Team'),
                  const SizedBox(height: 4),
                  _buildInfoRow('支援平台', 'iOS, Android, macOS, Web'),
                ],
              ),
            ),

            const SizedBox(height: 20),

            /// 應用描述
            const Text(
              '這是一款專業的占星應用，提供全面的占星功能，包括本命盤、合盤、推運盤等多種星盤類型，'
              '以及詳細的占星分析與深入剖析。',
              style: TextStyle(
                fontSize: 14,
                color: AppColors.textDark,
                fontStyle: FontStyle.normal,
                height: 1.5,
              ),
            ),

            const SizedBox(height: 24),

            /// 檢查更新按鈕
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => _checkForUpdates(context),
                icon: const Icon(Icons.system_update, size: 20),
                label: const Text('檢查更新'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.royalIndigo,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 14),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
              ),
            ),

            const SizedBox(height: 16),

            /// 版權標語
            Center(
              child: Text(
                '© 2025 AstReal. 保留所有權利。',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade500,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 檢查版本更新（使用 Remote Config）
  Future<void> _checkForUpdates(BuildContext context) async {
    // 顯示載入指示器
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(
        child: CircularProgressIndicator(),
      ),
    );

    try {
      logger.i('手動檢查版本更新（使用 Remote Config）...');

      // 使用新的 Remote Config 版本服務
      final status = await RemoteConfigVersionService.checkForUpdates();

      // 關閉載入指示器
      if (context.mounted) {
        Navigator.of(context).pop();
      }

      if (!context.mounted) return;

      if (status.isSuccess) {
        if (status.needsUpdate && status.latestVersion != null) {
          // 有更新可用
          await UpdateDialog.show(
            context,
            versionInfo: status.latestVersion!,
            isForceUpdate: status.isForceUpdate,
            onLater: () {
              logger.i('用戶選擇稍後更新');
            },
            onUpdate: () {
              logger.i('用戶選擇立即更新');
            },
          );
        } else {
          // 已是最新版本
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('您的應用已是最新版本'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        // 檢查失敗，嘗試使用舊的版本檢查服務作為降級方案
        logger.w('Remote Config 版本檢查失敗，嘗試使用舊服務: ${status.errorMessage}');
      }
    } catch (e) {
      logger.e('檢查版本更新時發生錯誤: $e');

      // 關閉載入指示器
      if (context.mounted) {
        Navigator.of(context).pop();
      }

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('版本檢查失敗：$e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// 構建資訊行
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade600,
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: AppColors.textDark,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 構建增強版社群媒體項目
  Widget _buildEnhancedSocialMediaItem({
    required IconData icon,
    required Color iconColor,
    required String title,
    required String subtitle,
    required String description,
    required VoidCallback onTap,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: iconColor.withValues(alpha: 0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: iconColor.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        iconColor.withValues(alpha: 0.2),
                        iconColor.withValues(alpha: 0.1),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    icon,
                    color: iconColor,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: AppColors.textDark,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        subtitle,
                        style: TextStyle(
                          fontSize: 14,
                          color: iconColor,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        description,
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    color: iconColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.arrow_forward_ios,
                    size: 16,
                    color: iconColor,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 構建特色標籤
  Widget _buildFeatureTag(String text, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(24),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
          width: 1.5,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 18,
            color: color,
          ),
          const SizedBox(width: 8),
          Text(
            text,
            style: TextStyle(
              fontSize: 13,
              fontWeight: FontWeight.w600,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

// 移除舊的 _launchURL 方法，現在使用 UrlLauncherService
}
