import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../core/utils/logger_utils.dart';
import '../../data/models/video/video_data.dart';
import '../../presentation/themes/app_theme.dart';
import '../../shared/widgets/common/responsive_wrapper.dart';
import '../../shared/widgets/web_aware_app_bar.dart';
import '../../shared/widgets/web_aware_pop_scope.dart';
import '../viewmodels/video_list_viewmodel.dart';
import 'video_player_page.dart';

/// YouTube 影片列表頁面
/// 顯示分類的影片列表，支援搜尋和篩選功能
class VideoListPage extends StatefulWidget {
  const VideoListPage({super.key});

  @override
  State<VideoListPage> createState() => _VideoListPageState();
}

class _VideoListPageState extends State<VideoListPage>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    // 初始化時先設置一個默認的 TabController
    _tabController = TabController(length: 1, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return WebAwarePopScope(
      routeName: '/video_list',
      child: ChangeNotifierProvider(
        create: (_) => VideoListViewModel(),
        child: Consumer<VideoListViewModel>(
          builder: (context, viewModel, child) {
            // 當分類載入完成後，重新初始化 TabController
            if (viewModel.categories.isNotEmpty &&
                _tabController.length != viewModel.categories.length + 1) {
              // 先保存當前選中的索引
              final currentIndex = _tabController.index;
              _tabController.dispose();
              _tabController = TabController(
                length: viewModel.categories.length + 1, // +1 for "全部" tab
                vsync: this,
                initialIndex: currentIndex < viewModel.categories.length + 1 ? currentIndex : 0,
              );
            }

            return Scaffold(
              appBar: WebAwareAppBarHelper.simple(
                title: '影片教學',
                actions: [
                  IconButton(
                    icon: const Icon(Icons.refresh),
                    onPressed: viewModel.isLoading ? null : () {
                      viewModel.refreshVideos();
                    },
                    tooltip: '重新整理',
                  ),
                ],
              ),
              body: ResponsivePageWrapper(
                maxWidth: 900.0, // 影片教學頁面適合較大寬度
                child: Column(
                  children: [
                    // 搜尋欄
                    _buildSearchBar(viewModel),

                    // 篩選選項
                    _buildFilterOptions(viewModel),

                    // 分類標籤
                    if (viewModel.categories.isNotEmpty)
                      _buildCategoryTabs(viewModel),

                    // 內容區域
                    Expanded(
                      child: _buildContent(viewModel),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  /// 構建搜尋欄
  Widget _buildSearchBar(VideoListViewModel viewModel) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: TextField(
        controller: viewModel.searchController,
        decoration: InputDecoration(
          hintText: '搜尋影片...',
          prefixIcon: const Icon(Icons.search),
          suffixIcon: viewModel.searchQuery.isNotEmpty
              ? IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    viewModel.searchController.clear();
                    viewModel.searchVideos('');
                  },
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          filled: true,
          fillColor: Colors.grey[50],
        ),
      ),
    );
  }

  /// 構建篩選選項
  Widget _buildFilterOptions(VideoListViewModel viewModel) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          // 熱門篩選
          FilterChip(
            label: const Text('熱門'),
            selected: viewModel.showOnlyPopular,
            onSelected: (_) => viewModel.toggleShowOnlyPopular(),
            selectedColor: AppColors.royalIndigo.withValues(alpha: 0.2),
          ),
          const SizedBox(width: 8),
          
          // 標籤篩選
          if (viewModel.selectedTag.isNotEmpty)
            FilterChip(
              label: Text(viewModel.selectedTag),
              selected: true,
              onSelected: (_) {}, // 空實現，因為已選中的標籤不需要再次選擇
              onDeleted: () => viewModel.clearTagSelection(),
              selectedColor: AppColors.solarAmber.withValues(alpha: 0.2),
            ),
          
          const Spacer(),
          
          // 清除篩選
          if (viewModel.selectedCategoryId.isNotEmpty ||
              viewModel.selectedTag.isNotEmpty ||
              viewModel.showOnlyPopular ||
              viewModel.searchQuery.isNotEmpty)
            TextButton(
              onPressed: () => viewModel.clearAllFilters(),
              child: const Text('清除篩選'),
            ),
        ],
      ),
    );
  }

  /// 構建分類標籤
  Widget _buildCategoryTabs(VideoListViewModel viewModel) {
    return SizedBox(
      height: 48,
      child: TabBar(
        controller: _tabController,
        isScrollable: true,
        labelColor: AppColors.royalIndigo,
        unselectedLabelColor: Colors.grey[600],
        indicatorColor: AppColors.royalIndigo,
        onTap: (index) {
          if (index == 0) {
            viewModel.clearCategorySelection();
          } else {
            final category = viewModel.categories[index - 1];
            viewModel.selectCategory(category.id);
          }
        },
        tabs: [
          const Tab(text: '全部'),
          ...viewModel.categories.map((category) => Tab(text: category.name)),
        ],
      ),
    );
  }

  /// 構建內容區域
  Widget _buildContent(VideoListViewModel viewModel) {
    if (viewModel.isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (viewModel.hasError) {
      return _buildErrorState(viewModel);
    }

    if (viewModel.filteredVideos.isEmpty) {
      return _buildEmptyState(viewModel);
    }

    return _buildVideoList(viewModel);
  }

  /// 構建錯誤狀態
  Widget _buildErrorState(VideoListViewModel viewModel) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            '載入失敗',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 8),
          Text(
            viewModel.errorMessage,
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () => viewModel.loadVideos(),
            child: const Text('重試'),
          ),
        ],
      ),
    );
  }

  /// 構建空狀態
  Widget _buildEmptyState(VideoListViewModel viewModel) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.video_library_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            '沒有找到影片',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 8),
          Text(
            '嘗試調整搜尋條件或篩選選項',
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () => viewModel.clearAllFilters(),
            child: const Text('清除篩選'),
          ),
        ],
      ),
    );
  }

  /// 構建影片列表
  Widget _buildVideoList(VideoListViewModel viewModel) {
    return RefreshIndicator(
      onRefresh: () => viewModel.refreshVideos(),
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: viewModel.filteredVideos.length,
        itemBuilder: (context, index) {
          final video = viewModel.filteredVideos[index];
          return _buildVideoCard(video);
        },
      ),
    );
  }

  /// 構建影片卡片
  Widget _buildVideoCard(VideoData video) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () => _openVideoPlayer(video),
        borderRadius: BorderRadius.circular(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 縮圖
            ClipRRect(
              borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
              child: AspectRatio(
                aspectRatio: 16 / 9,
                child: Stack(
                  children: [
                    Image.network(
                      video.thumbnailUrl,
                      width: double.infinity,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          color: Colors.grey[300],
                          child: const Center(
                            child: Icon(
                              Icons.video_library_outlined,
                              size: 48,
                              color: Colors.grey,
                            ),
                          ),
                        );
                      },
                    ),
                    // 播放按鈕
                    const Center(
                      child: CircleAvatar(
                        radius: 24,
                        backgroundColor: Colors.black54,
                        child: Icon(
                          Icons.play_arrow,
                          color: Colors.white,
                          size: 32,
                        ),
                      ),
                    ),
                    // 時長
                    Positioned(
                      bottom: 8,
                      right: 8,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.black87,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          video.duration,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ),
                    // 熱門標籤
                    if (video.isPopular)
                      Positioned(
                        top: 8,
                        left: 8,
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 6,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: AppColors.solarAmber,
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: const Text(
                            '熱門',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
            
            // 內容
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 標題
                  Text(
                    video.title,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 8),
                  
                  // 描述
                  Text(
                    video.description,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 12),
                  
                  // 標籤
                  if (video.tags.isNotEmpty)
                    Wrap(
                      spacing: 4,
                      runSpacing: 4,
                      children: video.tags.take(3).map((tag) {
                        return Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: AppColors.royalIndigo.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            tag,
                            style: TextStyle(
                              fontSize: 10,
                              color: AppColors.royalIndigo,
                            ),
                          ),
                        );
                      }).toList(),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 開啟影片播放器
  void _openVideoPlayer(VideoData video) {
    logger.d('開啟影片播放器：${video.title}');

    // 在 web 平台上，直接在新標籤頁中開啟 YouTube 影片
    if (kIsWeb) {
      _openVideoInNewTab(video);
    } else {
      // 在移動平台上使用 WebView 播放器
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => VideoPlayerPage(video: video),
        ),
      );
    }
  }

  /// 在新標籤頁中開啟影片（Web 平台專用）
  Future<void> _openVideoInNewTab(VideoData video) async {
    try {
      final url = video.youtubeUrl;
      logger.d('在新標籤頁中開啟 YouTube 影片：$url');

      final uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(
          uri,
          mode: LaunchMode.externalApplication,
          webOnlyWindowName: '_blank',
        );
      } else {
        // 如果無法開啟，顯示錯誤訊息並提供手動複製連結的選項
        if (mounted) {
          _showVideoUrlDialog(video);
        }
      }
    } catch (e) {
      logger.e('開啟 YouTube 影片失敗：$e');
      if (mounted) {
        _showVideoUrlDialog(video);
      }
    }
  }

  /// 顯示影片連結對話框
  void _showVideoUrlDialog(VideoData video) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(video.title),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('無法自動開啟影片，請手動複製以下連結：'),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: SelectableText(
                video.youtubeUrl,
                style: const TextStyle(
                  fontFamily: 'monospace',
                  fontSize: 12,
                ),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('關閉'),
          ),
          ElevatedButton(
            onPressed: () async {
              // 嘗試複製到剪貼簿
              try {
                await Clipboard.setData(ClipboardData(text: video.youtubeUrl));
                if (mounted) {
                  Navigator.of(context).pop();
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('連結已複製到剪貼簿')),
                  );
                }
              } catch (e) {
                logger.e('複製連結失敗：$e');
              }
            },
            child: const Text('複製連結'),
          ),
        ],
      ),
    );
  }
}
