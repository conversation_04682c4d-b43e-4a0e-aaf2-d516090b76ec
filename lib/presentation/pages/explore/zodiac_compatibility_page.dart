import 'package:flutter/material.dart';

import '../../../astreal.dart';

/// 星座配對頁面
class ZodiacCompatibilityPage extends StatefulWidget {
  const ZodiacCompatibilityPage({super.key});

  @override
  State<ZodiacCompatibilityPage> createState() => _ZodiacCompatibilityPageState();
}

class _ZodiacCompatibilityPageState extends State<ZodiacCompatibilityPage> {
  String? _selectedSign1;
  String? _selectedSign2;

  /// 十二星座列表
  final List<Map<String, dynamic>> _zodiacSigns = [
    {'name': '牡羊座', 'symbol': '♈', 'color': Colors.red},
    {'name': '金牛座', 'symbol': '♉', 'color': Colors.green},
    {'name': '雙子座', 'symbol': '♊', 'color': Colors.yellow},
    {'name': '巨蟹座', 'symbol': '♋', 'color': Colors.blue},
    {'name': '獅子座', 'symbol': '♌', 'color': Colors.orange},
    {'name': '處女座', 'symbol': '♍', 'color': Colors.brown},
    {'name': '天秤座', 'symbol': '♎', 'color': Colors.pink},
    {'name': '天蠍座', 'symbol': '♏', 'color': Colors.deepPurple},
    {'name': '射手座', 'symbol': '♐', 'color': Colors.purple},
    {'name': '摩羯座', 'symbol': '♑', 'color': Colors.grey},
    {'name': '水瓶座', 'symbol': '♒', 'color': Colors.cyan},
    {'name': '雙魚座', 'symbol': '♓', 'color': Colors.teal},
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          '星座配對',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppColors.warning,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppColors.warning.withValues(alpha: 0.1),
              Colors.white,
            ],
            stops: const [0.0, 0.3],
          ),
        ),
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            // 介紹卡片
            StyledCard(
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          width: 50,
                          height: 50,
                          decoration: BoxDecoration(
                            color: AppColors.warning.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(25),
                          ),
                          child: const Icon(
                            Icons.favorite,
                            color: AppColors.warning,
                            size: 24,
                          ),
                        ),
                        const SizedBox(width: 16),
                        const Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                '星座配對分析',
                                style: TextStyle(
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.textDark,
                                ),
                              ),
                              SizedBox(height: 4),
                              Text(
                                '探索不同星座間的關係和諧度',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      '選擇兩個星座來查看它們的配對分析。了解不同星座在愛情、友情和工作上的相容性。',
                      style: TextStyle(
                        fontSize: 14,
                        color: AppColors.textDark,
                        height: 1.5,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 20),
            
            // 星座選擇
            StyledCard(
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '選擇星座',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textDark,
                      ),
                    ),
                    const SizedBox(height: 16),
                    
                    // 第一個星座選擇
                    const Text(
                      '第一個星座：',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: AppColors.textDark,
                      ),
                    ),
                    const SizedBox(height: 8),
                    _buildZodiacSelector(1),
                    
                    const SizedBox(height: 20),
                    
                    // 第二個星座選擇
                    const Text(
                      '第二個星座：',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: AppColors.textDark,
                      ),
                    ),
                    const SizedBox(height: 8),
                    _buildZodiacSelector(2),
                    
                    const SizedBox(height: 24),
                    
                    // 分析按鈕
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _selectedSign1 != null && _selectedSign2 != null
                            ? _analyzeCompatibility
                            : null,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.warning,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: const Text(
                          '分析配對',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 20),
            
            // 功能說明
            StyledCard(
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '配對分析包含：',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textDark,
                      ),
                    ),
                    const SizedBox(height: 12),
                    _buildFeatureItem('💕', '愛情配對', '分析兩個星座在愛情關係中的相容性'),
                    _buildFeatureItem('👫', '友情配對', '探索友誼關係中的和諧度'),
                    _buildFeatureItem('💼', '工作配對', '了解在工作合作中的默契'),
                    _buildFeatureItem('⭐', '整體評分', '綜合評估配對指數'),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 構建星座選擇器
  Widget _buildZodiacSelector(int selectorNumber) {
    final selectedSign = selectorNumber == 1 ? _selectedSign1 : _selectedSign2;
    
    return SizedBox(
      height: 80,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: _zodiacSigns.length,
        itemBuilder: (context, index) {
          final sign = _zodiacSigns[index];
          final isSelected = selectedSign == sign['name'];
          
          return Padding(
            padding: const EdgeInsets.only(right: 12),
            child: GestureDetector(
              onTap: () {
                setState(() {
                  if (selectorNumber == 1) {
                    _selectedSign1 = sign['name'];
                  } else {
                    _selectedSign2 = sign['name'];
                  }
                });
              },
              child: Container(
                width: 60,
                decoration: BoxDecoration(
                  color: isSelected 
                      ? (sign['color'] as Color).withValues(alpha: 0.2)
                      : Colors.grey.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: isSelected 
                        ? sign['color'] as Color
                        : Colors.grey.withValues(alpha: 0.3),
                    width: isSelected ? 2 : 1,
                  ),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      sign['symbol'],
                      style: TextStyle(
                        fontSize: 24,
                        color: isSelected 
                            ? sign['color'] as Color
                            : Colors.grey,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      sign['name'],
                      style: TextStyle(
                        fontSize: 10,
                        color: isSelected 
                            ? sign['color'] as Color
                            : Colors.grey,
                        fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  /// 構建功能項目
  Widget _buildFeatureItem(String emoji, String title, String description) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Text(
            emoji,
            style: const TextStyle(fontSize: 20),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textDark,
                  ),
                ),
                Text(
                  description,
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 分析配對
  void _analyzeCompatibility() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('配對分析'),
        content: Text(
          '$_selectedSign1 和 $_selectedSign2 的配對分析\n\n'
          '這個功能正在開發中，敬請期待！\n\n'
          '未來將提供詳細的配對分析，包括愛情、友情、工作等各方面的相容性評估。',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('確定'),
          ),
        ],
      ),
    );
  }
}
