import 'package:flutter/material.dart';

import '../../../astreal.dart';

/// 占星日曆頁面
class AstrologyCalendarPage extends StatefulWidget {
  const AstrologyCalendarPage({super.key});

  @override
  State<AstrologyCalendarPage> createState() => _AstrologyCalendarPageState();
}

class _AstrologyCalendarPageState extends State<AstrologyCalendarPage> {

  /// 模擬的占星事件數據
  final List<Map<String, dynamic>> _astrologyEvents = [
    {
      'date': DateTime.now(),
      'title': '新月在射手座',
      'type': '月相',
      'description': '適合設定新目標，特別是與學習和旅行相關的計劃。',
      'color': Colors.blue,
      'icon': Icons.brightness_2,
    },
    {
      'date': DateTime.now().add(const Duration(days: 3)),
      'title': '水星進入摩羯座',
      'type': '行星移位',
      'description': '思維變得更加實際和有條理，適合制定長期計劃。',
      'color': Colors.orange,
      'icon': Icons.swap_horiz,
    },
    {
      'date': DateTime.now().add(const Duration(days: 7)),
      'title': '金星與木星合相',
      'type': '行星相位',
      'description': '愛情和財運都有提升的機會，是表達愛意的好時機。',
      'color': Colors.pink,
      'icon': Icons.favorite,
    },
    {
      'date': DateTime.now().add(const Duration(days: 14)),
      'title': '滿月在雙子座',
      'type': '月相',
      'description': '溝通和學習能力達到高峰，適合重要的對話和決定。',
      'color': Colors.yellow,
      'icon': Icons.brightness_1,
    },
    {
      'date': DateTime.now().add(const Duration(days: 21)),
      'title': '火星逆行開始',
      'type': '逆行',
      'description': '行動力可能受到影響，建議重新檢視目標和計劃。',
      'color': Colors.red,
      'icon': Icons.refresh,
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          '占星日曆',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppColors.indigoLight,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppColors.indigoLight.withValues(alpha: 0.1),
              Colors.white,
            ],
            stops: const [0.0, 0.3],
          ),
        ),
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            // 介紹卡片
            StyledCard(
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          width: 50,
                          height: 50,
                          decoration: BoxDecoration(
                            color: AppColors.indigoLight.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(25),
                          ),
                          child: const Icon(
                            Icons.calendar_today,
                            color: AppColors.indigoLight,
                            size: 24,
                          ),
                        ),
                        const SizedBox(width: 16),
                        const Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                '占星日曆',
                                style: TextStyle(
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.textDark,
                                ),
                              ),
                              SizedBox(height: 4),
                              Text(
                                '追蹤重要的占星事件和天象',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      '了解即將到來的重要占星事件，包括新月滿月、行星移位、逆行等，幫助您更好地規劃生活。',
                      style: TextStyle(
                        fontSize: 14,
                        color: AppColors.textDark,
                        height: 1.5,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 20),
            
            // 即將到來的事件
            const Text(
              '即將到來的占星事件',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppColors.textDark,
              ),
            ),
            const SizedBox(height: 12),
            
            // 事件列表
            ..._astrologyEvents.map((event) => Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: _buildEventCard(event),
            )),
            
            const SizedBox(height: 20),
            
            // 事件類型說明
            StyledCard(
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '事件類型說明',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textDark,
                      ),
                    ),
                    const SizedBox(height: 12),
                    _buildEventTypeItem(
                      Icons.brightness_2,
                      '月相',
                      '新月和滿月對情緒和直覺有重要影響',
                      Colors.blue,
                    ),
                    _buildEventTypeItem(
                      Icons.swap_horiz,
                      '行星移位',
                      '行星進入新星座，帶來能量轉換',
                      Colors.orange,
                    ),
                    _buildEventTypeItem(
                      Icons.favorite,
                      '行星相位',
                      '行星間的角度關係，影響各個生活領域',
                      Colors.pink,
                    ),
                    _buildEventTypeItem(
                      Icons.refresh,
                      '逆行',
                      '行星逆行期間，相關領域需要特別注意',
                      Colors.red,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 構建事件卡片
  Widget _buildEventCard(Map<String, dynamic> event) {
    final DateTime eventDate = event['date'];
    final int daysUntil = eventDate.difference(DateTime.now()).inDays;
    
    return StyledCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            // 日期和圖標
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: (event['color'] as Color).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    event['icon'],
                    color: event['color'],
                    size: 20,
                  ),
                  const SizedBox(height: 2),
                  Text(
                    daysUntil == 0 
                        ? '今天'
                        : daysUntil > 0 
                            ? '$daysUntil天後'
                            : '${-daysUntil}天前',
                    style: TextStyle(
                      fontSize: 10,
                      color: event['color'],
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
            
            const SizedBox(width: 16),
            
            // 事件詳情
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          event['title'],
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: AppColors.textDark,
                          ),
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: (event['color'] as Color).withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          event['type'],
                          style: TextStyle(
                            fontSize: 10,
                            color: event['color'],
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${eventDate.month}/${eventDate.day}',
                    style: const TextStyle(
                      fontSize: 12,
                      color: Colors.grey,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    event['description'],
                    style: const TextStyle(
                      fontSize: 14,
                      color: AppColors.textDark,
                      height: 1.3,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 構建事件類型項目
  Widget _buildEventTypeItem(IconData icon, String title, String description, Color color) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Icon(
              icon,
              color: color,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
                Text(
                  description,
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
