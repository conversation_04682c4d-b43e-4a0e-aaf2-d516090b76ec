import 'package:flutter/material.dart';

import '../../../astreal.dart';
import '../../../data/models/admin/system_announcement.dart';
import '../../../data/services/api/firebase_auth_service.dart';
import '../../../data/services/api/system_announcement_service.dart';
import '../../../shared/widgets/unified_card.dart';

/// 系統公告編輯頁面
class SystemAnnouncementEditPage extends StatefulWidget {
  final SystemAnnouncement? announcement;
  final String? announcementId;

  const SystemAnnouncementEditPage({
    super.key,
    this.announcement,
    this.announcementId,
  });

  bool get isEditing => announcement != null;

  @override
  State<SystemAnnouncementEditPage> createState() => _SystemAnnouncementEditPageState();
}

class _SystemAnnouncementEditPageState extends State<SystemAnnouncementEditPage> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _contentController = TextEditingController();
  final _actionTextController = TextEditingController();
  final _actionUrlController = TextEditingController();
  final _imageUrlController = TextEditingController();

  AnnouncementType _selectedType = AnnouncementType.info;
  AnnouncementPriority _selectedPriority = AnnouncementPriority.normal;
  bool _isActive = true;
  bool _isSticky = false;
  DateTime? _startDate;
  DateTime? _endDate;
  List<String> _targetUserTypes = ['all'];

  bool _isSaving = false;

  @override
  void initState() {
    super.initState();
    _initializeForm();
  }

  @override
  void dispose() {
    _titleController.dispose();
    _contentController.dispose();
    _actionTextController.dispose();
    _actionUrlController.dispose();
    _imageUrlController.dispose();
    super.dispose();
  }

  /// 初始化表單
  void _initializeForm() {
    if (widget.isEditing) {
      final announcement = widget.announcement!;
      _titleController.text = announcement.title;
      _contentController.text = announcement.content;
      _actionTextController.text = announcement.actionText ?? '';
      _actionUrlController.text = announcement.actionUrl ?? '';
      _imageUrlController.text = announcement.imageUrl ?? '';
      
      _selectedType = announcement.type;
      _selectedPriority = announcement.priority;
      _isActive = announcement.isActive;
      _isSticky = announcement.isSticky;
      _startDate = announcement.startDate;
      _endDate = announcement.endDate;
      _targetUserTypes = List.from(announcement.targetUserTypes);
    }
  }

  /// 獲取當前管理員 UID
  String _getCurrentAdminUid() {
    final currentUser = FirebaseAuthService.getCurrentUser();
    if (currentUser == null) {
      logger.w('無法獲取當前用戶，使用預設管理員 UID');
      return 'system_admin';
    }
    return currentUser.uid;
  }

  /// 保存系統公告
  Future<void> _saveAnnouncement() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isSaving = true;
    });

    try {
      final adminUid = _getCurrentAdminUid();
      final now = DateTime.now();

      final announcement = SystemAnnouncement(
        id: widget.announcementId ?? '',
        title: _titleController.text.trim(),
        content: _contentController.text.trim(),
        type: _selectedType,
        priority: _selectedPriority,
        isActive: _isActive,
        isSticky: _isSticky,
        startDate: _startDate,
        endDate: _endDate,
        targetUserTypes: _targetUserTypes,
        imageUrl: _imageUrlController.text.trim().isEmpty 
            ? null 
            : _imageUrlController.text.trim(),
        actionUrl: _actionUrlController.text.trim().isEmpty 
            ? null 
            : _actionUrlController.text.trim(),
        actionText: _actionTextController.text.trim().isEmpty 
            ? null 
            : _actionTextController.text.trim(),
        metadata: null,
        createdAt: widget.announcement?.createdAt ?? now,
        updatedAt: now,
        createdBy: widget.announcement?.createdBy ?? adminUid,
        updatedBy: adminUid,
      );

      bool success;
      if (widget.isEditing) {
        success = await SystemAnnouncementService.updateAnnouncement(
          widget.announcementId!,
          announcement,
          adminUid,
        );
      } else {
        final id = await SystemAnnouncementService.addAnnouncement(
          announcement,
          adminUid,
        );
        success = id != null;
      }

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(widget.isEditing ? '系統公告已更新' : '系統公告已新增'),
            backgroundColor: AppColors.successGreen,
          ),
        );
        Navigator.pop(context, true);
      } else {
        throw Exception('保存失敗');
      }
    } catch (e) {
      logger.e('保存系統公告失敗: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('保存失敗: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  /// 選擇日期時間
  Future<void> _selectDateTime(bool isStartDate) async {
    final currentDate = isStartDate ? _startDate : _endDate;
    
    final date = await showDatePicker(
      context: context,
      initialDate: currentDate ?? DateTime.now(),
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (date != null && mounted) {
      final time = await showTimePicker(
        context: context,
        initialTime: TimeOfDay.fromDateTime(currentDate ?? DateTime.now()),
      );

      if (time != null) {
        final dateTime = DateTime(
          date.year,
          date.month,
          date.day,
          time.hour,
          time.minute,
        );

        setState(() {
          if (isStartDate) {
            _startDate = dateTime;
          } else {
            _endDate = dateTime;
          }
        });
      }
    }
  }

  /// 清除日期時間
  void _clearDateTime(bool isStartDate) {
    setState(() {
      if (isStartDate) {
        _startDate = null;
      } else {
        _endDate = null;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.isEditing ? '編輯系統公告' : '新增系統公告'),
        backgroundColor: AppColors.royalIndigo,
        foregroundColor: Colors.white,
        actions: [
          if (_isSaving)
            const Padding(
              padding: EdgeInsets.all(16),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
            )
          else
            TextButton(
              onPressed: _saveAnnouncement,
              child: const Text(
                '保存',
                style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
              ),
            ),
        ],
      ),
      backgroundColor: AppColors.scaffoldBackground,
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            // 基本資訊
            _buildBasicInfoSection(),

            const SizedBox(height: 16),

            // 類型和優先級
            _buildTypeAndPrioritySection(),

            const SizedBox(height: 16),

            // 狀態設定
            _buildStatusSection(),

            const SizedBox(height: 16),

            // 時間設定
            _buildTimeSection(),

            const SizedBox(height: 16),

            // 目標用戶
            _buildTargetUsersSection(),

            const SizedBox(height: 16),

            // 進階設定
            _buildAdvancedSection(),
          ],
        ),
      ),
    );
  }

  /// 構建基本資訊區段
  Widget _buildBasicInfoSection() {
    return UnifiedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '基本資訊',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.textDark,
            ),
          ),
          const SizedBox(height: 16),

          // 標題
          TextFormField(
            controller: _titleController,
            decoration: const InputDecoration(
              labelText: '公告標題 *',
              hintText: '請輸入公告標題',
              border: OutlineInputBorder(),
            ),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return '請輸入公告標題';
              }
              if (value.trim().length > 100) {
                return '標題長度不能超過 100 個字元';
              }
              return null;
            },
            maxLength: 100,
          ),

          const SizedBox(height: 16),

          // 內容
          TextFormField(
            controller: _contentController,
            decoration: const InputDecoration(
              labelText: '公告內容 *',
              hintText: '請輸入公告內容',
              border: OutlineInputBorder(),
              alignLabelWithHint: true,
            ),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return '請輸入公告內容';
              }
              if (value.trim().length > 1000) {
                return '內容長度不能超過 1000 個字元';
              }
              return null;
            },
            maxLines: 5,
            maxLength: 1000,
          ),
        ],
      ),
    );
  }

  /// 構建類型和優先級區段
  Widget _buildTypeAndPrioritySection() {
    return UnifiedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '類型和優先級',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.textDark,
            ),
          ),
          const SizedBox(height: 16),

          // 公告類型
          const Text(
            '公告類型',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: AppColors.textDark,
            ),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: AnnouncementType.values.map((type) {
              final isSelected = _selectedType == type;
              return ChoiceChip(
                label: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(type.icon, size: 16, color: isSelected ? Colors.white : type.color),
                    const SizedBox(width: 4),
                    Text(type.displayName),
                  ],
                ),
                selected: isSelected,
                onSelected: (selected) {
                  if (selected) {
                    setState(() {
                      _selectedType = type;
                    });
                  }
                },
                selectedColor: type.color,
                backgroundColor: type.color.withValues(alpha: 0.1),
                labelStyle: TextStyle(
                  color: isSelected ? Colors.white : type.color,
                  fontWeight: FontWeight.w500,
                ),
              );
            }).toList(),
          ),

          const SizedBox(height: 16),

          // 優先級
          const Text(
            '優先級',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: AppColors.textDark,
            ),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: AnnouncementPriority.values.map((priority) {
              final isSelected = _selectedPriority == priority;
              return ChoiceChip(
                label: Text(priority.displayName),
                selected: isSelected,
                onSelected: (selected) {
                  if (selected) {
                    setState(() {
                      _selectedPriority = priority;
                    });
                  }
                },
                selectedColor: priority.color,
                backgroundColor: priority.color.withValues(alpha: 0.1),
                labelStyle: TextStyle(
                  color: isSelected ? Colors.white : priority.color,
                  fontWeight: FontWeight.w500,
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  /// 構建狀態區段
  Widget _buildStatusSection() {
    return UnifiedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '狀態設定',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.textDark,
            ),
          ),
          const SizedBox(height: 16),

          SwitchListTile(
            title: const Text('啟用公告'),
            subtitle: const Text('關閉後用戶將看不到此公告'),
            value: _isActive,
            onChanged: (value) {
              setState(() {
                _isActive = value;
              });
            },
            activeColor: AppColors.successGreen,
          ),

          SwitchListTile(
            title: const Text('置頂顯示'),
            subtitle: const Text('置頂的公告會優先顯示在列表頂部'),
            value: _isSticky,
            onChanged: (value) {
              setState(() {
                _isSticky = value;
              });
            },
            activeColor: AppColors.solarAmber,
          ),
        ],
      ),
    );
  }

  /// 構建時間區段
  Widget _buildTimeSection() {
    return UnifiedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '顯示時間',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.textDark,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            '設定公告的顯示時間範圍（可選）',
            style: TextStyle(
              fontSize: 12,
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: 16),

          // 開始時間
          _buildDateTimeField(
            label: '開始時間',
            dateTime: _startDate,
            onTap: () => _selectDateTime(true),
            onClear: () => _clearDateTime(true),
          ),

          const SizedBox(height: 12),

          // 結束時間
          _buildDateTimeField(
            label: '結束時間',
            dateTime: _endDate,
            onTap: () => _selectDateTime(false),
            onClear: () => _clearDateTime(false),
          ),
        ],
      ),
    );
  }

  /// 構建日期時間欄位
  Widget _buildDateTimeField({
    required String label,
    required DateTime? dateTime,
    required VoidCallback onTap,
    required VoidCallback onClear,
  }) {
    return Row(
      children: [
        Expanded(
          child: InkWell(
            onTap: onTap,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey[400]!),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Row(
                children: [
                  Icon(Icons.schedule, color: Colors.grey[600], size: 20),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      dateTime != null
                          ? '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}'
                          : '選擇$label',
                      style: TextStyle(
                        color: dateTime != null ? AppColors.textDark : Colors.grey[600],
                        fontSize: 14,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        if (dateTime != null) ...[
          const SizedBox(width: 8),
          IconButton(
            onPressed: onClear,
            icon: const Icon(Icons.clear, color: Colors.grey),
            tooltip: '清除',
          ),
        ],
      ],
    );
  }

  /// 構建目標用戶區段
  Widget _buildTargetUsersSection() {
    return UnifiedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '目標用戶',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.textDark,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            '選擇要顯示此公告的用戶類型',
            style: TextStyle(
              fontSize: 12,
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: 16),

          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              _buildUserTypeChip('all', '所有用戶'),
              _buildUserTypeChip('starmaster', '專業用戶'),
              _buildUserTypeChip('starlight', '初心者'),
              _buildUserTypeChip('admin', '管理員'),
            ],
          ),
        ],
      ),
    );
  }

  /// 構建用戶類型晶片
  Widget _buildUserTypeChip(String userType, String label) {
    final isSelected = _targetUserTypes.contains(userType);

    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          if (userType == 'all') {
            if (selected) {
              _targetUserTypes = ['all'];
            } else {
              _targetUserTypes.remove('all');
            }
          } else {
            if (selected) {
              _targetUserTypes.remove('all');
              _targetUserTypes.add(userType);
            } else {
              _targetUserTypes.remove(userType);
            }
          }

          // 如果沒有選擇任何類型，預設選擇所有用戶
          if (_targetUserTypes.isEmpty) {
            _targetUserTypes = ['all'];
          }
        });
      },
      backgroundColor: Colors.grey[200],
      selectedColor: AppColors.royalIndigo.withValues(alpha: 0.2),
      checkmarkColor: AppColors.royalIndigo,
    );
  }

  /// 構建進階設定區段
  Widget _buildAdvancedSection() {
    return UnifiedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '進階設定',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.textDark,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            '可選的進階功能設定',
            style: TextStyle(
              fontSize: 12,
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: 16),

          // 圖片 URL
          TextFormField(
            controller: _imageUrlController,
            decoration: const InputDecoration(
              labelText: '圖片 URL',
              hintText: '請輸入圖片連結（可選）',
              border: OutlineInputBorder(),
              prefixIcon: Icon(Icons.image),
            ),
            validator: (value) {
              if (value != null && value.trim().isNotEmpty) {
                final uri = Uri.tryParse(value.trim());
                if (uri == null || !uri.hasScheme) {
                  return '請輸入有效的 URL';
                }
              }
              return null;
            },
          ),

          const SizedBox(height: 16),

          // 行動按鈕文字
          TextFormField(
            controller: _actionTextController,
            decoration: const InputDecoration(
              labelText: '行動按鈕文字',
              hintText: '例如：立即查看、了解更多（可選）',
              border: OutlineInputBorder(),
              prefixIcon: Icon(Icons.touch_app),
            ),
            maxLength: 20,
          ),

          const SizedBox(height: 16),

          // 行動按鈕連結
          TextFormField(
            controller: _actionUrlController,
            decoration: const InputDecoration(
              labelText: '行動按鈕連結',
              hintText: '點擊按鈕後的跳轉連結（可選）',
              border: OutlineInputBorder(),
              prefixIcon: Icon(Icons.link),
            ),
            validator: (value) {
              if (value != null && value.trim().isNotEmpty) {
                final uri = Uri.tryParse(value.trim());
                if (uri == null || !uri.hasScheme) {
                  return '請輸入有效的 URL';
                }
              }
              return null;
            },
          ),
        ],
      ),
    );
  }
}
