import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';

import '../../../astreal.dart';
import '../../../data/services/api/admin_service.dart';
import '../../../data/services/api/user_profile_service.dart';
import '../../../shared/widgets/common/responsive_wrapper.dart';
import '../../../shared/widgets/unified_card.dart';

/// 用戶編輯頁面
class UserEditPage extends StatefulWidget {
  final UserProfile userData;

  const UserEditPage({
    super.key,
    required this.userData,
  });

  @override
  State<UserEditPage> createState() => _UserEditPageState();
}

class _UserEditPageState extends State<UserEditPage> {
  final _formKey = GlobalKey<FormState>();
  
  late TextEditingController _displayNameController;
  late TextEditingController _emailController;
  late TextEditingController _photoURLController;
  late TextEditingController _interpretationCreditsController;

  bool _isLoading = false;
  bool _emailVerified = false;
  bool _isAnonymous = false;
  bool _isAdmin = false;
  bool _hasChanges = false;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
  }

  @override
  void dispose() {
    _displayNameController.dispose();
    _emailController.dispose();
    _photoURLController.dispose();
    _interpretationCreditsController.dispose();
    super.dispose();
  }

  /// 初始化控制器
  void _initializeControllers() {
    _displayNameController = TextEditingController(
      text: widget.userData.displayName,
    );
    _emailController = TextEditingController(
      text: widget.userData.email ?? '',
    );
    _photoURLController = TextEditingController(
      text: widget.userData.photoURL ?? '',
    );
    _interpretationCreditsController = TextEditingController(
      text: widget.userData.interpretationCredits.toString(),
    );

    _emailVerified = widget.userData.emailVerified ?? false;
    _isAnonymous = widget.userData.isAnonymous;
    _isAdmin = widget.userData.isAdmin ?? false;

    // 監聽變更
    _displayNameController.addListener(_onFieldChanged);
    _emailController.addListener(_onFieldChanged);
    _photoURLController.addListener(_onFieldChanged);
    _interpretationCreditsController.addListener(_onFieldChanged);
  }

  /// 欄位變更處理
  void _onFieldChanged() {
    setState(() {
      _hasChanges = true;
    });
  }

  /// 保存用戶資料
  Future<void> _saveUser() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final uid = widget.userData.userId;

      // 從現有資料創建 UserProfile 物件
      final currentProfile = widget.userData;

      // 解析解讀次數
      int interpretationCredits;
      try {
        interpretationCredits = int.parse(_interpretationCreditsController.text.trim());
        if (interpretationCredits < 0) {
          throw FormatException('解讀次數不能為負數');
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('解讀次數格式錯誤: ${e.toString()}'),
              backgroundColor: AppColors.error,
            ),
          );
        }
        setState(() {
          _isLoading = false;
        });
        return;
      }

      // 創建更新後的 UserProfile
      final updatedProfile = currentProfile.copyWith(
        displayName: _displayNameController.text.trim(),
        email: _emailController.text.trim(),
        photoURL: _photoURLController.text.trim().isEmpty
            ? null
            : _photoURLController.text.trim(),
        emailVerified: _emailVerified,
        isAnonymous: _isAnonymous,
        isAdmin: _isAdmin,
        interpretationCredits: interpretationCredits,
        creditsLastUpdated: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // 使用 UserProfileService 更新資料
      await UserProfileService.updateUser(uid, updatedProfile);

      // 如果管理者狀態有變更，同時更新 admin_users 集合
      if (_isAdmin != (widget.userData.isAdmin ?? false)) {
        await AdminService.setUserAdmin(uid, _isAdmin);
      }

      // 記錄管理者操作
      await AdminService.logAdminAction(
        adminUid: 'current_admin', // 這裡應該傳入當前管理者的 UID
        action: 'edit_user',
        description: '編輯用戶資料: $uid',
        metadata: {
          'targetUserId': uid,
          'changes': updatedProfile.toJson(),
        },
      );

      logger.i('用戶資料更新成功: $uid');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('用戶資料更新成功'),
            backgroundColor: AppColors.successGreen,
          ),
        );
        
        Navigator.pop(context, true);
      }
    } catch (e) {
      logger.e('更新用戶資料失敗: $e');
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('更新失敗: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// 複製 UID
  void _copyUID() {
    final uid = widget.userData.userId;
    Clipboard.setData(ClipboardData(text: uid));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('UID 已複製到剪貼簿'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          '編輯用戶',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: AppColors.textDark,
          ),
        ),
        backgroundColor: AppColors.pastelSkyBlue,
        elevation: 0,
        iconTheme: const IconThemeData(color: AppColors.textDark),
        actions: [
          if (_hasChanges)
            TextButton(
              onPressed: _isLoading ? null : _saveUser,
              child: _isLoading
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Text(
                      '保存',
                      style: TextStyle(
                        color: AppColors.royalIndigo,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
            ),
        ],
      ),
      backgroundColor: AppColors.scaffoldBackground,
      body: ResponsivePageWrapper(
        maxWidth: 800.0, // 用戶編輯頁面適合中等寬度
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            padding: ResponsiveUtils.getResponsivePadding(context),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
              // 用戶基本資訊卡片
              _buildBasicInfoCard(),
              const SizedBox(height: 16),
              
              // 編輯表單卡片
              _buildEditFormCard(),
              const SizedBox(height: 16),
              
              // 狀態設定卡片
              _buildStatusCard(),
              const SizedBox(height: 16),
              
              // 時間資訊卡片
              _buildTimeInfoCard(),
              const SizedBox(height: 16),

              // 統計資訊卡片
              _buildStatisticsCard(),
              const SizedBox(height: 16),

                // 設備資訊卡片
                _buildDeviceInfoCard(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 構建基本資訊卡片
  Widget _buildBasicInfoCard() {
    final uid = widget.userData.userId;
    
    return UnifiedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '用戶資訊',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.textDark,
            ),
          ),
          const SizedBox(height: 12),
          
          Row(
            children: [
              CircleAvatar(
                radius: 32,
                backgroundColor: AppColors.royalIndigo.withValues(alpha: 0.1),
                backgroundImage: _photoURLController.text.isNotEmpty
                    ? NetworkImage(_photoURLController.text)
                    : null,
                child: _photoURLController.text.isEmpty
                    ? Text(
                        _displayNameController.text.isNotEmpty
                            ? _displayNameController.text[0].toUpperCase()
                            : 'U',
                        style: const TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: AppColors.royalIndigo,
                        ),
                      )
                    : null,
              ),
              const SizedBox(width: 16),
              
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _displayNameController.text.isNotEmpty
                          ? _displayNameController.text
                          : '未設置名稱',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textDark,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _emailController.text.isNotEmpty
                          ? _emailController.text
                          : '未設置郵件',
                      style: const TextStyle(
                        fontSize: 14,
                        color: AppColors.textSecondary,
                      ),
                    ),
                    const SizedBox(height: 4),
                    // 用戶類型標籤
                    Row(
                      children: [
                        _buildUserTypeChip(),
                        const SizedBox(width: 8),
                        if (widget.userData.emailVerified == true)
                          _buildStatusChip('已驗證', AppColors.successGreen),
                        if (widget.userData.isAdmin == true) ...[
                          const SizedBox(width: 8),
                          _buildStatusChip('管理員', AppColors.royalIndigo),
                        ],
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          // UID 資訊
          Row(
            children: [
              const Text(
                'UID: ',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textSecondary,
                ),
              ),
              Expanded(
                child: Text(
                  uid,
                  style: const TextStyle(
                    fontSize: 12,
                    color: AppColors.textDark,
                    fontFamily: 'monospace',
                  ),
                ),
              ),
              IconButton(
                icon: const Icon(Icons.copy, size: 16),
                onPressed: _copyUID,
                tooltip: '複製 UID',
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 構建編輯表單卡片
  Widget _buildEditFormCard() {
    return UnifiedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '編輯資料',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.textDark,
            ),
          ),
          const SizedBox(height: 16),
          
          // 顯示名稱
          TextFormField(
            controller: _displayNameController,
            decoration: const InputDecoration(
              labelText: '顯示名稱',
              border: OutlineInputBorder(),
              prefixIcon: Icon(Icons.person),
            ),
            validator: (value) {
              // if (value == null || value.trim().isEmpty) {
              //   return '請輸入顯示名稱';
              // }
              return null;
            },
          ),
          const SizedBox(height: 16),
          
          // 電子郵件
          TextFormField(
            controller: _emailController,
            decoration: const InputDecoration(
              labelText: '電子郵件',
              border: OutlineInputBorder(),
              prefixIcon: Icon(Icons.email),
            ),
            keyboardType: TextInputType.emailAddress,
            validator: (value) {
              // if (value == null || value.trim().isEmpty) {
              //   return '請輸入電子郵件';
              // }
              // if (value != null && !RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
              //   return '請輸入有效的電子郵件格式';
              // }
              return null;
            },
          ),
          const SizedBox(height: 16),
          
          // 頭像 URL
          TextFormField(
            controller: _photoURLController,
            decoration: const InputDecoration(
              labelText: '頭像 URL (選填)',
              border: OutlineInputBorder(),
              prefixIcon: Icon(Icons.image),
            ),
            keyboardType: TextInputType.url,
          ),
          const SizedBox(height: 16),

          // 解讀次數
          TextFormField(
            controller: _interpretationCreditsController,
            decoration: const InputDecoration(
              labelText: '可用解讀次數',
              border: OutlineInputBorder(),
              prefixIcon: Icon(Icons.psychology),
              helperText: '用戶可使用的深入剖析次數',
            ),
            keyboardType: TextInputType.number,
            inputFormatters: [
              FilteringTextInputFormatter.digitsOnly,
            ],
            validator: (value) {
              if (value == null || value.isEmpty) {
                return '請輸入解讀次數';
              }
              final credits = int.tryParse(value);
              if (credits == null) {
                return '請輸入有效的數字';
              }
              if (credits < 0) {
                return '解讀次數不能為負數';
              }
              return null;
            },
          ),
        ],
      ),
    );
  }

  /// 構建狀態設定卡片
  Widget _buildStatusCard() {
    return UnifiedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '狀態設定',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.textDark,
            ),
          ),
          const SizedBox(height: 16),
          
          // 電子郵件驗證狀態
          SwitchListTile(
            title: const Text('電子郵件已驗證'),
            subtitle: const Text('用戶的電子郵件驗證狀態'),
            value: _emailVerified,
            onChanged: (value) {
              setState(() {
                _emailVerified = value;
                _hasChanges = true;
              });
            },
            activeColor: AppColors.successGreen,
          ),
          
          // 匿名用戶狀態
          SwitchListTile(
            title: const Text('匿名用戶'),
            subtitle: const Text('是否為匿名註冊用戶'),
            value: _isAnonymous,
            onChanged: (value) {
              setState(() {
                _isAnonymous = value;
                _hasChanges = true;
              });
            },
            activeColor: AppColors.warning,
          ),
          
          // 管理者狀態
          SwitchListTile(
            title: const Text('管理者權限'),
            subtitle: const Text('是否具有管理者權限'),
            value: _isAdmin,
            onChanged: (value) {
              setState(() {
                _isAdmin = value;
                _hasChanges = true;
              });
            },
            activeColor: AppColors.royalIndigo,
          ),
        ],
      ),
    );
  }

  /// 構建時間資訊卡片
  Widget _buildTimeInfoCard() {
    final createdAt = widget.userData.createdAt;
    final lastSignInAt = widget.userData.lastLoginAt;
    final updatedAt = widget.userData.updatedAt;
    
    return UnifiedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '時間資訊',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.textDark,
            ),
          ),
          const SizedBox(height: 12),
          
          _buildTimeInfoRow('註冊時間', createdAt),
          _buildTimeInfoRow('最後登入', lastSignInAt),
          _buildTimeInfoRow('最後更新', updatedAt),
          _buildTimeInfoRow('解讀次數更新', widget.userData.creditsLastUpdated),
        ],
      ),
    );
  }

  /// 構建時間資訊行
  Widget _buildTimeInfoRow(String label, DateTime dateTime) {
    String formatted = DateFormat('yyyy-MM-dd HH:mm:ss').format(dateTime);
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              label,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: AppColors.textSecondary,
              ),
            ),
          ),
          Expanded(
            child: Text(
              formatted,
              style: const TextStyle(
                fontSize: 14,
                color: AppColors.textDark,
                fontFamily: 'monospace',
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 構建用戶類型標籤
  Widget _buildUserTypeChip() {
    final userData = widget.userData;
    String label;
    Color color;

    if (userData.isAnonymous) {
      label = '匿名用戶';
      color = AppColors.warning;
    } else if (userData.isAdmin == true) {
      label = '管理員';
      color = AppColors.royalIndigo;
    } else {
      label = '註冊用戶';
      color = AppColors.successGreen;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Text(
        label,
        style: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w500,
          color: color,
        ),
      ),
    );
  }

  /// 構建狀態標籤
  Widget _buildStatusChip(String label, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Text(
        label,
        style: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w500,
          color: color,
        ),
      ),
    );
  }

  /// 構建統計資訊卡片
  Widget _buildStatisticsCard() {
    final userData = widget.userData;

    return UnifiedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '統計資訊',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.textDark,
            ),
          ),
          const SizedBox(height: 12),

          _buildStatRow('登入次數', '${userData.loginCount} 次'),
          _buildStatRow('可用解讀次數', '${userData.interpretationCredits} 次'),
          _buildStatRow('檔案完成度', userData.profileCompleted ? '已完成' : '未完成'),
          _buildStatRow('新用戶', userData.isNewUser ? '是' : '否'),
          _buildStatRow('有可用解讀', userData.hasInterpretationCredits ? '是' : '否'),
        ],
      ),
    );
  }

  /// 構建設備資訊卡片
  Widget _buildDeviceInfoCard() {
    final userData = widget.userData;

    return UnifiedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '設備資訊',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.textDark,
            ),
          ),
          const SizedBox(height: 12),

          _buildStatRow(
            '最後登入 IP',
            userData.lastLoginIp ?? '未記錄',
            isMonospace: true,
          ),
          _buildStatRow(
            '使用平台',
            userData.platform ?? '未記錄',
          ),
        ],
      ),
    );
  }

  /// 構建統計資訊行
  Widget _buildStatRow(String label, String value, {bool isMonospace = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: AppColors.textSecondary,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 14,
                color: AppColors.textDark,
                fontFamily: isMonospace ? 'monospace' : null,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
