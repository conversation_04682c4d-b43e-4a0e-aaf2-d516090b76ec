import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../../astreal.dart';
import '../../../data/services/api/celebrity_management_service.dart';
import '../../../data/services/api/firebase_auth_service.dart';
import '../../../shared/widgets/unified_card.dart';
import 'celebrity_edit_page.dart';

/// 名人資料管理頁面
class CelebrityManagementPage extends StatefulWidget {
  const CelebrityManagementPage({super.key});

  @override
  State<CelebrityManagementPage> createState() => _CelebrityManagementPageState();
}

class _CelebrityManagementPageState extends State<CelebrityManagementPage> {
  List<CelebrityWithId> _celebrities = [];
  List<CelebrityWithId> _filteredCelebrities = [];
  bool _isLoading = true;
  bool _isSyncing = false;
  String _searchQuery = '';
  CelebrityCategory? _selectedCategory;

  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _searchController.addListener(_onSearchChanged);
    _initializePage();
  }

  /// 初始化頁面
  Future<void> _initializePage() async {
    // 先載入現有的名人資料
    await _loadCelebrities();

    // 如果沒有資料，自動從 RemoteConfig 導入
    if (_celebrities.isEmpty) {
      logger.i('沒有找到名人資料，自動從 RemoteConfig 導入');
      await _autoImportFromRemoteConfig();
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  /// 載入名人資料
  Future<void> _loadCelebrities() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final celebrities = await CelebrityManagementService.getAllCelebrities();
      setState(() {
        _celebrities = celebrities;
        _filteredCelebrities = celebrities;
        _isLoading = false;
      });

      logger.i('載入了 ${celebrities.length} 個名人資料');
    } catch (e) {
      logger.e('載入名人資料失敗: $e');
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('載入名人資料失敗: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  /// 搜尋變更處理
  void _onSearchChanged() {
    setState(() {
      _searchQuery = _searchController.text.toLowerCase();
      _filterCelebrities();
    });
  }

  /// 篩選名人資料
  void _filterCelebrities() {
    _filteredCelebrities = _celebrities.where((celebrityWithId) {
      final celebrity = celebrityWithId.celebrity;
      final matchesSearch = _searchQuery.isEmpty ||
          celebrity.name.toLowerCase().contains(_searchQuery) ||
          celebrity.description.toLowerCase().contains(_searchQuery);

      final matchesCategory = _selectedCategory == null ||
          celebrity.category == _selectedCategory;

      return matchesSearch && matchesCategory;
    }).toList();
  }

  /// 獲取當前管理員 UID
  String _getCurrentAdminUid() {
    final currentUser = FirebaseAuthService.getCurrentUser();
    if (currentUser == null) {
      logger.w('無法獲取當前用戶，使用預設管理員 UID');
      return 'system_admin';
    }
    return currentUser.uid;
  }

  /// 自動從 RemoteConfig 導入（靜默導入，不顯示確認對話框）
  Future<void> _autoImportFromRemoteConfig() async {
    try {
      logger.i('開始自動從 RemoteConfig 導入名人資料');

      final adminUid = _getCurrentAdminUid();
      final success = await CelebrityManagementService.importFromRemoteConfig(adminUid);

      if (success) {
        logger.i('自動導入 RemoteConfig 名人資料成功');
        // 重新載入資料
        await _loadCelebrities();

        if (mounted && _celebrities.isNotEmpty) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('已自動導入 ${_celebrities.length} 個名人資料'),
              backgroundColor: AppColors.successGreen,
            ),
          );
        }
      } else {
        logger.w('自動導入 RemoteConfig 名人資料失敗');
      }
    } catch (e) {
      logger.e('自動導入 RemoteConfig 名人資料時發生錯誤: $e');
      // 靜默失敗，不顯示錯誤訊息給用戶
    }
  }

  /// 刷新資料
  Future<void> _refreshCelebrities() async {
    await _loadCelebrities();
  }

  /// 新增名人資料
  void _addCelebrity() async {
    final result = await Navigator.push<bool>(
      context,
      MaterialPageRoute(
        builder: (context) => const CelebrityEditPage(),
      ),
    );

    if (result == true) {
      _refreshCelebrities();
    }
  }

  /// 編輯名人資料
  void _editCelebrity(CelebrityWithId celebrityWithId) async {
    final result = await Navigator.push<bool>(
      context,
      MaterialPageRoute(
        builder: (context) =>
            CelebrityEditPage(
              celebrity: celebrityWithId.celebrity,
              celebrityId: celebrityWithId.id,
            ),
      ),
    );

    if (result == true) {
      _refreshCelebrities();
    }
  }

  /// 刪除名人資料
  void _deleteCelebrity(CelebrityWithId celebrityWithId) async {
    final celebrity = celebrityWithId.celebrity;
    final id = celebrityWithId.id;

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) =>
          AlertDialog(
            title: const Text('確認刪除'),
            content: Text(
                '確定要刪除「${celebrity.name}」的資料嗎？此操作無法復原。'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: const Text('取消'),
              ),
              TextButton(
                onPressed: () => Navigator.pop(context, true),
                style: TextButton.styleFrom(foregroundColor: AppColors.error),
                child: const Text('刪除'),
              ),
            ],
          ),
    );

    if (confirmed == true) {
      try {
        final adminUid = _getCurrentAdminUid();
        final success = await CelebrityManagementService.deleteCelebrity(
          id,
          adminUid,
        );

        if (success && mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('名人資料已刪除'),
              backgroundColor: AppColors.successGreen,
            ),
          );
          _refreshCelebrities();
        } else {
          throw Exception('刪除失敗');
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('刪除失敗: $e'),
              backgroundColor: AppColors.error,
            ),
          );
        }
      }
    }
  }

  /// 同步到 RemoteConfig
  void _syncToRemoteConfig() async {
    setState(() {
      _isSyncing = true;
    });

    try {
      final adminUid = _getCurrentAdminUid();
      final success = await CelebrityManagementService.syncToRemoteConfig(
        adminUid,
      );

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
                '已準備同步到 RemoteConfig\n請聯繫技術人員完成最終同步'),
            backgroundColor: AppColors.successGreen,
            duration: Duration(seconds: 4),
          ),
        );
      } else {
        throw Exception('同步準備失敗');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('同步失敗: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSyncing = false;
        });
      }
    }
  }

  /// 從 RemoteConfig 導入
  void _importFromRemoteConfig() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) =>
          AlertDialog(
            title: const Text('確認導入'),
            content: const Text(
                '確定要從 RemoteConfig 導入名人資料嗎？\n重複的資料將被跳過。'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: const Text('取消'),
              ),
              TextButton(
                onPressed: () => Navigator.pop(context, true),
                child: const Text('導入'),
              ),
            ],
          ),
    );

    if (confirmed == true) {
      try {
        final adminUid = _getCurrentAdminUid();
        final success = await CelebrityManagementService.importFromRemoteConfig(
          adminUid,
        );

        if (success && mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('從 RemoteConfig 導入完成'),
              backgroundColor: AppColors.successGreen,
            ),
          );
          _refreshCelebrities();
        } else {
          throw Exception('導入失敗');
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('導入失敗: $e'),
              backgroundColor: AppColors.error,
            ),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('名人資料管理'),
        backgroundColor: AppColors.royalIndigo,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _addCelebrity,
            tooltip: '新增名人資料',
          ),
          if (_isSyncing)
            const Padding(
              padding: EdgeInsets.all(16),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
            )
          else
            PopupMenuButton<String>(
              onSelected: (value) {
                switch (value) {
                  case 'sync':
                    _syncToRemoteConfig();
                    break;
                  case 'import':
                    _importFromRemoteConfig();
                    break;
                }
              },
              itemBuilder: (context) =>
              [
                const PopupMenuItem(
                  value: 'sync',
                  child: ListTile(
                    leading: Icon(Icons.cloud_upload),
                    title: Text('同步到 RemoteConfig'),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
                const PopupMenuItem(
                  value: 'import',
                  child: ListTile(
                    leading: Icon(Icons.cloud_download),
                    title: Text('從 RemoteConfig 導入'),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
              ],
            ),
        ],
      ),
      backgroundColor: AppColors.scaffoldBackground,
      body: Column(
        children: [
          // 搜尋和篩選欄
          _buildSearchAndFilterBar(),

          // 統計資訊
          _buildStatsBar(),

          // 名人資料列表
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _filteredCelebrities.isEmpty
                ? _buildEmptyState()
                : _buildCelebrityList(),
          ),
        ],
      ),
    );
  }

  /// 構建搜尋和篩選欄
  Widget _buildSearchAndFilterBar() {
    return UnifiedCard(
      margin: const EdgeInsets.all(16),
      child: Column(
        children: [
          // 搜尋欄
          TextField(
            controller: _searchController,
            decoration: const InputDecoration(
              hintText: '搜尋名人姓名或描述...',
              prefixIcon: Icon(Icons.search),
              border: OutlineInputBorder(),
              contentPadding: EdgeInsets.symmetric(
                  horizontal: 16, vertical: 12),
            ),
          ),

          const SizedBox(height: 12),

          // 類別篩選
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                _buildCategoryChip(null, '全部'),
                const SizedBox(width: 8),
                ...CelebrityCategory.values.map((category) =>
                    Padding(
                      padding: const EdgeInsets.only(right: 8),
                      child: _buildCategoryChip(category, category.displayName),
                    ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 構建類別篩選晶片
  Widget _buildCategoryChip(CelebrityCategory? category, String label) {
    final isSelected = _selectedCategory == category;

    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          _selectedCategory = selected ? category : null;
          _filterCelebrities();
        });
      },
      backgroundColor: Colors.grey[200],
      selectedColor: AppColors.royalIndigo.withValues(alpha: 0.2),
      checkmarkColor: AppColors.royalIndigo,
    );
  }

  /// 構建統計資訊欄
  Widget _buildStatsBar() {
    final totalCount = _celebrities.length;
    final filteredCount = _filteredCelebrities.length;
    final popularCount = _celebrities
        .where((c) => c.celebrity.isPopular)
        .length;

    return UnifiedCard(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildStatItem('總計', totalCount.toString(), Icons.people),
          _buildStatItem('顯示', filteredCount.toString(), Icons.visibility),
          _buildStatItem('熱門', popularCount.toString(), Icons.star),
        ],
      ),
    );
  }

  /// 構建統計項目
  Widget _buildStatItem(String label, String value, IconData icon) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, color: AppColors.royalIndigo, size: 20),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppColors.textDark,
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }

  /// 構建空狀態
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.star_outline,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            _searchQuery.isNotEmpty || _selectedCategory != null
                ? '沒有符合條件的名人資料'
                : '還沒有名人資料',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _searchQuery.isNotEmpty || _selectedCategory != null
                ? '請嘗試調整搜尋條件'
                : '點擊右上角的 + 按鈕新增第一個名人資料',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  /// 構建名人資料列表
  Widget _buildCelebrityList() {
    return RefreshIndicator(
      onRefresh: _refreshCelebrities,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _filteredCelebrities.length,
        itemBuilder: (context, index) {
          final celebrityWithId = _filteredCelebrities[index];
          return _buildCelebrityCard(celebrityWithId);
        },
      ),
    );
  }

  /// 構建名人資料卡片
  Widget _buildCelebrityCard(CelebrityWithId celebrityWithId) {
    final celebrity = celebrityWithId.celebrity;
    return UnifiedCard(
      margin: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 標題行
          Row(
            children: [
              // 類別圖示
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: celebrity.category.color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  celebrity.category.icon,
                  color: celebrity.category.color,
                  size: 20,
                ),
              ),

              const SizedBox(width: 12),

              // 名稱和類別
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            celebrity.name,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: AppColors.textDark,
                            ),
                          ),
                        ),
                        if (celebrity.isPopular)
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: AppColors.solarAmber.withValues(
                                  alpha: 0.2),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: const Text(
                              '熱門',
                              style: TextStyle(
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                                color: AppColors.solarAmber,
                              ),
                            ),
                          ),
                      ],
                    ),
                    Text(
                      celebrity.category.displayName,
                      style: const TextStyle(
                        fontSize: 12,
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),

              // 操作按鈕
              PopupMenuButton<String>(
                onSelected: (value) {
                  switch (value) {
                    case 'edit':
                      _editCelebrity(celebrityWithId);
                      break;
                    case 'delete':
                      _deleteCelebrity(celebrityWithId);
                      break;
                  }
                },
                itemBuilder: (context) =>
                [
                  const PopupMenuItem(
                    value: 'edit',
                    child: ListTile(
                      leading: Icon(Icons.edit),
                      title: Text('編輯'),
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'delete',
                    child: ListTile(
                      leading: Icon(Icons.delete, color: AppColors.error),
                      title: Text(
                          '刪除', style: TextStyle(color: AppColors.error)),
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                ],
              ),
            ],
          ),

          const SizedBox(height: 12),

          // 出生資料
          Row(
            children: [
              const Icon(Icons.cake, size: 16, color: AppColors.textSecondary),
              const SizedBox(width: 6),
              Expanded(
                child: Text(
                  '${DateFormat('yyyy-MM-dd HH:mm').format(
                      celebrity.birthData.dateTime)} • ${celebrity.birthData
                      .birthPlace}',
                  style: const TextStyle(
                    fontSize: 12,
                    color: AppColors.textSecondary,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 8),

          // 描述
          Text(
            celebrity.description,
            style: const TextStyle(
              fontSize: 14,
              color: AppColors.textDark,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),

          const SizedBox(height: 12),

          // 推薦主題標籤
          if (celebrity.recommendedTopics.isNotEmpty) ...[
            Wrap(
              spacing: 6,
              runSpacing: 4,
              children: celebrity.recommendedTopics.take(3).map((topic) =>
                  Container(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: AppColors.royalIndigo.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      topic,
                      style: const TextStyle(
                        fontSize: 10,
                        color: AppColors.royalIndigo,
                      ),
                    ),
                  ),
              ).toList(),
            ),
            if (celebrity.recommendedTopics.length > 3)
              Padding(
                padding: const EdgeInsets.only(top: 4),
                child: Text(
                  '還有 ${celebrity.recommendedTopics.length - 3} 個主題...',
                  style: const TextStyle(
                    fontSize: 10,
                    color: AppColors.textSecondary,
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ),
          ],
        ],
      ),
    );
  }
}