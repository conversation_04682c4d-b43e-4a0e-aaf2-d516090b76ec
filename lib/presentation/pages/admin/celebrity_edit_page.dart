import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../../astreal.dart';
import '../../../data/services/api/celebrity_management_service.dart';
import '../../../shared/widgets/unified_card.dart';
import '../birth_data_form_page.dart';

/// 名人資料編輯頁面
class CelebrityEditPage extends StatefulWidget {
  final CelebrityExample? celebrity;
  final String? celebrityId;

  const CelebrityEditPage({
    super.key,
    this.celebrity,
    this.celebrityId,
  });

  bool get isEditing => celebrity != null && celebrityId != null;

  @override
  State<CelebrityEditPage> createState() => _CelebrityEditPageState();
}

class _CelebrityEditPageState extends State<CelebrityEditPage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _imageUrlController = TextEditingController();
  final _topicsController = TextEditingController();
  
  BirthData? _birthData;
  CelebrityCategory _selectedCategory = CelebrityCategory.entertainment;
  bool _isPopular = false;
  bool _isSaving = false;

  @override
  void initState() {
    super.initState();
    _initializeForm();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _imageUrlController.dispose();
    _topicsController.dispose();
    super.dispose();
  }

  /// 初始化表單
  void _initializeForm() {
    if (widget.celebrity != null) {
      final celebrity = widget.celebrity!;
      _nameController.text = celebrity.name;
      _descriptionController.text = celebrity.description;
      _imageUrlController.text = celebrity.imageUrl ?? '';
      _topicsController.text = celebrity.recommendedTopics.join(', ');
      _birthData = celebrity.birthData;
      _selectedCategory = celebrity.category;
      _isPopular = celebrity.isPopular;
    }
  }

  /// 編輯出生資料
  void _editBirthData() async {
    final result = await Navigator.push<BirthData>(
      context,
      MaterialPageRoute(
        builder: (context) => BirthDataFormPage(
          initialData: _birthData,
        ),
      ),
    );

    if (result != null) {
      setState(() {
        _birthData = result;
      });
    }
  }

  /// 儲存名人資料
  void _saveCelebrity() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_birthData == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('請設定出生資料'),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    setState(() {
      _isSaving = true;
    });

    try {
      // 解析推薦主題
      final topics = _topicsController.text
          .split(',')
          .map((topic) => topic.trim())
          .where((topic) => topic.isNotEmpty)
          .toList();

      final celebrity = CelebrityExample(
        name: _nameController.text.trim(),
        birthData: _birthData!,
        description: _descriptionController.text.trim(),
        category: _selectedCategory,
        recommendedTopics: topics,
        isPopular: _isPopular,
        imageUrl: _imageUrlController.text.trim().isEmpty 
            ? null 
            : _imageUrlController.text.trim(),
      );

      bool success;
      if (widget.isEditing) {
        success = await CelebrityManagementService.updateCelebrity(
          widget.celebrityId!,
          celebrity,
          'current_admin_uid', // TODO: 從認證服務獲取當前管理員 UID
        );
      } else {
        final id = await CelebrityManagementService.addCelebrity(
          celebrity,
          'current_admin_uid', // TODO: 從認證服務獲取當前管理員 UID
        );
        success = id != null;
      }

      if (success) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(widget.isEditing ? '名人資料已更新' : '名人資料已新增'),
              backgroundColor: AppColors.successGreen,
            ),
          );
          Navigator.pop(context, true);
        }
      } else {
        throw Exception(widget.isEditing ? '更新失敗' : '新增失敗');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('儲存失敗: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      setState(() {
        _isSaving = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.isEditing ? '編輯名人資料' : '新增名人資料'),
        backgroundColor: AppColors.royalIndigo,
        foregroundColor: Colors.white,
        actions: [
          if (_isSaving)
            const Padding(
              padding: EdgeInsets.all(16),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
            )
          else
            TextButton(
              onPressed: _saveCelebrity,
              child: const Text(
                '儲存',
                style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
              ),
            ),
        ],
      ),
      backgroundColor: AppColors.scaffoldBackground,
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 基本資訊卡片
              _buildBasicInfoCard(),
              const SizedBox(height: 16),
              
              // 出生資料卡片
              _buildBirthDataCard(),
              const SizedBox(height: 16),
              
              // 分類和設定卡片
              _buildCategoryAndSettingsCard(),
              const SizedBox(height: 16),
              
              // 推薦主題卡片
              _buildTopicsCard(),
              const SizedBox(height: 16),
              
              // 圖片設定卡片
              _buildImageCard(),
            ],
          ),
        ),
      ),
    );
  }

  /// 構建基本資訊卡片
  Widget _buildBasicInfoCard() {
    return UnifiedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '基本資訊',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.textDark,
            ),
          ),
          const SizedBox(height: 16),
          
          // 姓名
          TextFormField(
            controller: _nameController,
            decoration: const InputDecoration(
              labelText: '姓名 *',
              hintText: '請輸入名人姓名',
              border: OutlineInputBorder(),
            ),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return '請輸入姓名';
              }
              return null;
            },
          ),
          
          const SizedBox(height: 16),
          
          // 描述
          TextFormField(
            controller: _descriptionController,
            decoration: const InputDecoration(
              labelText: '特點描述 *',
              hintText: '請輸入名人的特點描述',
              border: OutlineInputBorder(),
            ),
            maxLines: 3,
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return '請輸入特點描述';
              }
              return null;
            },
          ),
        ],
      ),
    );
  }

  /// 構建出生資料卡片
  Widget _buildBirthDataCard() {
    return UnifiedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                '出生資料',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textDark,
                ),
              ),
              TextButton.icon(
                onPressed: _editBirthData,
                icon: Icon(_birthData == null ? Icons.add : Icons.edit),
                label: Text(_birthData == null ? '設定' : '編輯'),
              ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          if (_birthData != null) ...[
            _buildBirthDataInfo(),
          ] else ...[
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.cake_outlined,
                    size: 48,
                    color: Colors.grey[400],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '尚未設定出生資料',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '點擊上方的「設定」按鈕來新增出生資料',
                    style: TextStyle(
                      color: Colors.grey[500],
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// 構建出生資料資訊
  Widget _buildBirthDataInfo() {
    if (_birthData == null) return const SizedBox.shrink();
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.royalIndigo.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.royalIndigo.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.person, size: 16, color: AppColors.royalIndigo),
              const SizedBox(width: 6),
              Expanded(
                child: Text(
                  _birthData!.name,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    color: AppColors.textDark,
                  ),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 8),
          
          Row(
            children: [
              const Icon(Icons.cake, size: 16, color: AppColors.royalIndigo),
              const SizedBox(width: 6),
              Text(
                DateFormat('yyyy年MM月dd日 HH:mm').format(_birthData!.dateTime),
                style: const TextStyle(color: AppColors.textDark),
              ),
            ],
          ),
          
          const SizedBox(height: 4),
          
          Row(
            children: [
              const Icon(Icons.location_on, size: 16, color: AppColors.royalIndigo),
              const SizedBox(width: 6),
              Expanded(
                child: Text(
                  _birthData!.birthPlace,
                  style: const TextStyle(color: AppColors.textDark),
                ),
              ),
            ],
          ),
          
          if (_birthData!.notes?.isNotEmpty == true) ...[
            const SizedBox(height: 4),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Icon(Icons.note, size: 16, color: AppColors.royalIndigo),
                const SizedBox(width: 6),
                Expanded(
                  child: Text(
                    _birthData!.notes!,
                    style: const TextStyle(
                      color: AppColors.textSecondary,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  /// 構建分類和設定卡片
  Widget _buildCategoryAndSettingsCard() {
    return UnifiedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '分類和設定',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.textDark,
            ),
          ),
          const SizedBox(height: 16),

          // 類別選擇
          const Text(
            '類別 *',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: AppColors.textDark,
            ),
          ),
          const SizedBox(height: 8),

          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: CelebrityCategory.values.map((category) {
              final isSelected = _selectedCategory == category;
              return GestureDetector(
                onTap: () {
                  setState(() {
                    _selectedCategory = category;
                  });
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  decoration: BoxDecoration(
                    color: isSelected
                        ? category.color.withValues(alpha: 0.2)
                        : Colors.grey[100],
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: isSelected
                          ? category.color
                          : Colors.grey[300]!,
                      width: isSelected ? 2 : 1,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        category.icon,
                        size: 16,
                        color: isSelected ? category.color : Colors.grey[600],
                      ),
                      const SizedBox(width: 6),
                      Text(
                        category.displayName,
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                          color: isSelected ? category.color : Colors.grey[700],
                        ),
                      ),
                    ],
                  ),
                ),
              );
            }).toList(),
          ),

          const SizedBox(height: 20),

          // 熱門設定
          Row(
            children: [
              Switch(
                value: _isPopular,
                onChanged: (value) {
                  setState(() {
                    _isPopular = value;
                  });
                },
                activeColor: AppColors.solarAmber,
              ),
              const SizedBox(width: 12),
              const Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '設為熱門',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: AppColors.textDark,
                      ),
                    ),
                    Text(
                      '熱門的名人資料會優先顯示在範例頁面',
                      style: TextStyle(
                        fontSize: 12,
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 構建推薦主題卡片
  Widget _buildTopicsCard() {
    return UnifiedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '推薦主題',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.textDark,
            ),
          ),
          const SizedBox(height: 16),

          TextFormField(
            controller: _topicsController,
            decoration: const InputDecoration(
              labelText: '推薦解讀主題',
              hintText: '請輸入推薦的解讀主題，用逗號分隔',
              helperText: '例如：領導力分析, 政治天賦, 公眾魅力',
              border: OutlineInputBorder(),
            ),
            maxLines: 2,
          ),
        ],
      ),
    );
  }

  /// 構建圖片設定卡片
  Widget _buildImageCard() {
    return UnifiedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '圖片設定',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.textDark,
            ),
          ),
          const SizedBox(height: 16),

          TextFormField(
            controller: _imageUrlController,
            decoration: const InputDecoration(
              labelText: '圖片 URL（選填）',
              hintText: 'https://example.com/image.jpg',
              border: OutlineInputBorder(),
              helperText: '可以留空，系統會使用預設圖示',
            ),
            validator: (value) {
              if (value != null && value.isNotEmpty) {
                final uri = Uri.tryParse(value);
                if (uri == null || !uri.hasScheme) {
                  return '請輸入有效的 URL';
                }
              }
              return null;
            },
          ),
        ],
      ),
    );
  }
}
