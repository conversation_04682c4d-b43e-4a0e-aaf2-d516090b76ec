import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../../astreal.dart';
import '../../../data/models/admin/system_announcement.dart';
import '../../../data/services/api/firebase_auth_service.dart';
import '../../../data/services/api/system_announcement_service.dart';
import '../../../shared/widgets/unified_card.dart';
import 'system_announcement_edit_page.dart';

/// 系統公告管理頁面
class SystemAnnouncementManagementPage extends StatefulWidget {
  const SystemAnnouncementManagementPage({super.key});

  @override
  State<SystemAnnouncementManagementPage> createState() => _SystemAnnouncementManagementPageState();
}

class _SystemAnnouncementManagementPageState extends State<SystemAnnouncementManagementPage> {
  List<SystemAnnouncementWithId> _announcements = [];
  List<SystemAnnouncementWithId> _filteredAnnouncements = [];
  bool _isLoading = true;
  String _searchQuery = '';
  AnnouncementType? _selectedType;
  bool? _selectedStatus; // null: 全部, true: 啟用, false: 停用

  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadAnnouncements();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  /// 載入系統公告
  Future<void> _loadAnnouncements() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final announcements = await SystemAnnouncementService.getAllAnnouncements();
      setState(() {
        _announcements = announcements;
        _filteredAnnouncements = announcements;
        _isLoading = false;
      });

      logger.i('載入了 ${announcements.length} 個系統公告');
    } catch (e) {
      logger.e('載入系統公告失敗: $e');
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('載入系統公告失敗: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  /// 搜尋變更處理
  void _onSearchChanged() {
    setState(() {
      _searchQuery = _searchController.text.toLowerCase();
      _filterAnnouncements();
    });
  }

  /// 篩選系統公告
  void _filterAnnouncements() {
    _filteredAnnouncements = _announcements.where((announcementWithId) {
      final announcement = announcementWithId.announcement;
      
      final matchesSearch = _searchQuery.isEmpty ||
          announcement.title.toLowerCase().contains(_searchQuery) ||
          announcement.content.toLowerCase().contains(_searchQuery);

      final matchesType = _selectedType == null ||
          announcement.type == _selectedType;

      final matchesStatus = _selectedStatus == null ||
          announcement.isActive == _selectedStatus;

      return matchesSearch && matchesType && matchesStatus;
    }).toList();
  }

  /// 刷新資料
  Future<void> _refreshAnnouncements() async {
    await _loadAnnouncements();
  }

  /// 獲取當前管理員 UID
  String _getCurrentAdminUid() {
    final currentUser = FirebaseAuthService.getCurrentUser();
    if (currentUser == null) {
      logger.w('無法獲取當前用戶，使用預設管理員 UID');
      return 'system_admin';
    }
    return currentUser.uid;
  }

  /// 新增系統公告
  void _addAnnouncement() async {
    final result = await Navigator.push<bool>(
      context,
      MaterialPageRoute(
        builder: (context) => const SystemAnnouncementEditPage(),
      ),
    );

    if (result == true) {
      _refreshAnnouncements();
    }
  }

  /// 編輯系統公告
  void _editAnnouncement(SystemAnnouncementWithId announcementWithId) async {
    final result = await Navigator.push<bool>(
      context,
      MaterialPageRoute(
        builder: (context) => SystemAnnouncementEditPage(
          announcement: announcementWithId.announcement,
          announcementId: announcementWithId.id,
        ),
      ),
    );

    if (result == true) {
      _refreshAnnouncements();
    }
  }

  /// 切換公告狀態
  void _toggleAnnouncementStatus(SystemAnnouncementWithId announcementWithId) async {
    final announcement = announcementWithId.announcement;
    final newStatus = !announcement.isActive;

    try {
      final adminUid = _getCurrentAdminUid();
      final success = await SystemAnnouncementService.toggleAnnouncementStatus(
        announcementWithId.id,
        newStatus,
        adminUid,
      );

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('公告已${newStatus ? '啟用' : '停用'}'),
            backgroundColor: AppColors.successGreen,
          ),
        );
        _refreshAnnouncements();
      } else {
        throw Exception('狀態切換失敗');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('狀態切換失敗: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  /// 刪除系統公告
  void _deleteAnnouncement(SystemAnnouncementWithId announcementWithId) async {
    final announcement = announcementWithId.announcement;

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('確認刪除'),
        content: Text('確定要刪除公告「${announcement.title}」嗎？此操作無法復原。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: AppColors.error),
            child: const Text('刪除'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        final adminUid = _getCurrentAdminUid();
        final success = await SystemAnnouncementService.deleteAnnouncement(
          announcementWithId.id,
          adminUid,
        );

        if (success && mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('系統公告已刪除'),
              backgroundColor: AppColors.successGreen,
            ),
          );
          _refreshAnnouncements();
        } else {
          throw Exception('刪除失敗');
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('刪除失敗: $e'),
              backgroundColor: AppColors.error,
            ),
          );
        }
      }
    }
  }

  /// 同步到 Remote Config
  void _syncToRemoteConfig() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.sync, color: AppColors.royalIndigo),
            SizedBox(width: 8),
            Text('同步到 Remote Config'),
          ],
        ),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('確定要將系統公告同步到 Remote Config 嗎？'),
            SizedBox(height: 8),
            Text(
              '注意：這會將當前的公告資料保存到 Firestore，等待管理員手動更新到 Remote Config。',
              style: TextStyle(fontSize: 12, color: AppColors.textSecondary),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.royalIndigo,
              foregroundColor: Colors.white,
            ),
            child: const Text('確認同步'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        // 顯示進度指示器
        if (mounted) {
          showDialog(
            context: context,
            barrierDismissible: false,
            builder: (context) => const AlertDialog(
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('正在同步到 Remote Config...'),
                ],
              ),
            ),
          );
        }

        final adminUid = _getCurrentAdminUid();
        final success = await SystemAnnouncementService.syncAnnouncementsToRemoteConfig(adminUid);

        // 關閉進度指示器
        if (mounted) Navigator.of(context).pop();

        if (success && mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('系統公告已同步到 Remote Config'),
              backgroundColor: AppColors.successGreen,
            ),
          );
        } else {
          throw Exception('同步失敗');
        }
      } catch (e) {
        // 關閉進度指示器
        if (mounted) Navigator.of(context).pop();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('同步失敗: $e'),
              backgroundColor: AppColors.error,
            ),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('系統公告管理'),
        backgroundColor: AppColors.royalIndigo,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.sync),
            onPressed: _syncToRemoteConfig,
            tooltip: '同步到 Remote Config',
          ),
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _addAnnouncement,
            tooltip: '新增系統公告',
          ),
        ],
      ),
      backgroundColor: AppColors.scaffoldBackground,
      body: Column(
        children: [
          // 搜尋和篩選欄
          _buildSearchAndFilterBar(),

          // 統計資訊
          _buildStatsBar(),

          // 系統公告列表
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _filteredAnnouncements.isEmpty
                ? _buildEmptyState()
                : _buildAnnouncementList(),
          ),
        ],
      ),
    );
  }

  /// 構建搜尋和篩選欄
  Widget _buildSearchAndFilterBar() {
    return UnifiedCard(
      margin: const EdgeInsets.all(16),
      child: Column(
        children: [
          // 搜尋欄
          TextField(
            controller: _searchController,
            decoration: const InputDecoration(
              hintText: '搜尋公告標題或內容...',
              prefixIcon: Icon(Icons.search),
              border: OutlineInputBorder(),
              contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            ),
          ),

          const SizedBox(height: 12),

          // 篩選選項
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                // 狀態篩選
                _buildStatusChip(null, '全部狀態'),
                const SizedBox(width: 8),
                _buildStatusChip(true, '已啟用'),
                const SizedBox(width: 8),
                _buildStatusChip(false, '已停用'),
                const SizedBox(width: 16),
                
                // 類型篩選
                _buildTypeChip(null, '全部類型'),
                const SizedBox(width: 8),
                ...AnnouncementType.values.map((type) =>
                    Padding(
                      padding: const EdgeInsets.only(right: 8),
                      child: _buildTypeChip(type, type.displayName),
                    ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 構建狀態篩選晶片
  Widget _buildStatusChip(bool? status, String label) {
    final isSelected = _selectedStatus == status;

    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          _selectedStatus = selected ? status : null;
          _filterAnnouncements();
        });
      },
      backgroundColor: Colors.grey[200],
      selectedColor: AppColors.royalIndigo.withValues(alpha: 0.2),
      checkmarkColor: AppColors.royalIndigo,
    );
  }

  /// 構建類型篩選晶片
  Widget _buildTypeChip(AnnouncementType? type, String label) {
    final isSelected = _selectedType == type;

    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          _selectedType = selected ? type : null;
          _filterAnnouncements();
        });
      },
      backgroundColor: Colors.grey[200],
      selectedColor: type?.color.withValues(alpha: 0.2) ?? AppColors.royalIndigo.withValues(alpha: 0.2),
      checkmarkColor: type?.color ?? AppColors.royalIndigo,
    );
  }

  /// 構建統計資訊欄
  Widget _buildStatsBar() {
    final totalCount = _announcements.length;
    final filteredCount = _filteredAnnouncements.length;
    final activeCount = _announcements.where((a) => a.announcement.isActive).length;
    final stickyCount = _announcements.where((a) => a.announcement.isSticky).length;

    return UnifiedCard(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildStatItem('總計', totalCount.toString(), Icons.announcement),
          _buildStatItem('顯示', filteredCount.toString(), Icons.visibility),
          _buildStatItem('啟用', activeCount.toString(), Icons.check_circle),
          _buildStatItem('置頂', stickyCount.toString(), Icons.push_pin),
        ],
      ),
    );
  }

  /// 構建統計項目
  Widget _buildStatItem(String label, String value, IconData icon) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, color: AppColors.royalIndigo, size: 20),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppColors.textDark,
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }

  /// 構建空狀態
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.announcement_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            _searchQuery.isNotEmpty || _selectedType != null || _selectedStatus != null
                ? '沒有符合條件的系統公告'
                : '還沒有系統公告',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _searchQuery.isNotEmpty || _selectedType != null || _selectedStatus != null
                ? '請嘗試調整搜尋條件'
                : '點擊右上角的 + 按鈕新增第一個系統公告',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  /// 構建系統公告列表
  Widget _buildAnnouncementList() {
    return RefreshIndicator(
      onRefresh: _refreshAnnouncements,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _filteredAnnouncements.length,
        itemBuilder: (context, index) {
          final announcementWithId = _filteredAnnouncements[index];
          return _buildAnnouncementCard(announcementWithId);
        },
      ),
    );
  }

  /// 構建系統公告卡片
  Widget _buildAnnouncementCard(SystemAnnouncementWithId announcementWithId) {
    final announcement = announcementWithId.announcement;
    final dateFormat = DateFormat('yyyy-MM-dd HH:mm');

    return UnifiedCard(
      margin: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 標題行
          Row(
            children: [
              // 類型圖示
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: announcement.type.color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  announcement.type.icon,
                  color: announcement.type.color,
                  size: 20,
                ),
              ),

              const SizedBox(width: 12),

              // 標題和狀態
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            announcement.title,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: AppColors.textDark,
                            ),
                          ),
                        ),
                        // 狀態標籤
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            if (announcement.isSticky)
                              Container(
                                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                margin: const EdgeInsets.only(right: 4),
                                decoration: BoxDecoration(
                                  color: AppColors.solarAmber.withValues(alpha: 0.2),
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: const Text(
                                  '置頂',
                                  style: TextStyle(
                                    fontSize: 10,
                                    fontWeight: FontWeight.bold,
                                    color: AppColors.solarAmber,
                                  ),
                                ),
                              ),
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                              decoration: BoxDecoration(
                                color: announcement.isActive
                                    ? AppColors.successGreen.withValues(alpha: 0.2)
                                    : Colors.grey.withValues(alpha: 0.2),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Text(
                                announcement.isActive ? '啟用' : '停用',
                                style: TextStyle(
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                  color: announcement.isActive
                                      ? AppColors.successGreen
                                      : Colors.grey,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                    Row(
                      children: [
                        Text(
                          announcement.type.displayName,
                          style: const TextStyle(
                            fontSize: 12,
                            color: AppColors.textSecondary,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 1),
                          decoration: BoxDecoration(
                            color: announcement.priority.color.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(3),
                          ),
                          child: Text(
                            announcement.priority.displayName,
                            style: TextStyle(
                              fontSize: 10,
                              color: announcement.priority.color,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // 操作按鈕
              PopupMenuButton<String>(
                onSelected: (value) {
                  switch (value) {
                    case 'edit':
                      _editAnnouncement(announcementWithId);
                      break;
                    case 'toggle':
                      _toggleAnnouncementStatus(announcementWithId);
                      break;
                    case 'delete':
                      _deleteAnnouncement(announcementWithId);
                      break;
                  }
                },
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'edit',
                    child: ListTile(
                      leading: Icon(Icons.edit),
                      title: Text('編輯'),
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                  PopupMenuItem(
                    value: 'toggle',
                    child: ListTile(
                      leading: Icon(
                        announcement.isActive ? Icons.visibility_off : Icons.visibility,
                        color: announcement.isActive ? Colors.orange : AppColors.successGreen,
                      ),
                      title: Text(
                        announcement.isActive ? '停用' : '啟用',
                        style: TextStyle(
                          color: announcement.isActive ? Colors.orange : AppColors.successGreen,
                        ),
                      ),
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'delete',
                    child: ListTile(
                      leading: Icon(Icons.delete, color: AppColors.error),
                      title: Text('刪除', style: TextStyle(color: AppColors.error)),
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                ],
              ),
            ],
          ),

          const SizedBox(height: 12),

          // 內容預覽
          Text(
            announcement.content,
            style: const TextStyle(
              fontSize: 14,
              color: AppColors.textDark,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),

          const SizedBox(height: 12),

          // 時間和目標用戶
          Row(
            children: [
              const Icon(Icons.schedule, size: 16, color: AppColors.textSecondary),
              const SizedBox(width: 6),
              Expanded(
                child: Text(
                  '建立: ${dateFormat.format(announcement.createdAt)}',
                  style: const TextStyle(
                    fontSize: 12,
                    color: AppColors.textSecondary,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 4),

          Row(
            children: [
              const Icon(Icons.group, size: 16, color: AppColors.textSecondary),
              const SizedBox(width: 6),
              Expanded(
                child: Text(
                  '目標: ${announcement.targetUserTypes.join(', ')}',
                  style: const TextStyle(
                    fontSize: 12,
                    color: AppColors.textSecondary,
                  ),
                ),
              ),
            ],
          ),

          // 時間範圍（如果有設定）
          if (announcement.startDate != null || announcement.endDate != null) ...[
            const SizedBox(height: 4),
            Row(
              children: [
                const Icon(Icons.date_range, size: 16, color: AppColors.textSecondary),
                const SizedBox(width: 6),
                Expanded(
                  child: Text(
                    '顯示期間: ${announcement.startDate != null ? dateFormat.format(announcement.startDate!) : '不限'} ~ ${announcement.endDate != null ? dateFormat.format(announcement.endDate!) : '不限'}',
                    style: const TextStyle(
                      fontSize: 12,
                      color: AppColors.textSecondary,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }
}
