import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../astreal.dart';
import '../../data/services/api/firebase_service.dart';
import '../../shared/utils/utils.dart';
/// 意見回饋頁面
class FeedbackPage extends StatefulWidget {
  const FeedbackPage({super.key});

  @override
  State<FeedbackPage> createState() => _FeedbackPageState();
}

class _FeedbackPageState extends State<FeedbackPage> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _contentController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();

  String _selectedCategory = '諮商回饋';
  bool _isSubmitting = false;

  // 回饋類別選項
  final List<String> _categories = [
    '諮商回饋',
    '功能建議',
    '問題回報',
    '使用體驗',
    '其他',
  ];

  @override
  void initState() {
    super.initState();
    _loadUserEmail();
  }

  @override
  void dispose() {
    _contentController.dispose();
    _emailController.dispose();
    super.dispose();
  }

  // 載入用戶郵箱
  Future<void> _loadUserEmail() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      setState(() {
        _emailController.text = prefs.getString('user_email') ?? '';
      });
    } catch (e) {
      logger.e('載入用戶郵箱時出錯: $e');
    }
  }

  // 提交意見回饋
  Future<void> _submitFeedback() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isSubmitting = true;
    });

    try {
      // 獲取裝置唯一識別碼
      final deviceId = await getOrCreateDeviceId();

      // 創建意見回饋
      final feedback = FeedbackModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        content: _contentController.text,
        email: _emailController.text.isEmpty ? null : _emailController.text,
        deviceId: deviceId,
        category: _selectedCategory,
      );

      // 保存到 Firebase
      final success = await FirebaseService.saveFeedback(feedback);

      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });

        if (success) {
          // 顯示成功訊息
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('感謝您的回饋！我們會認真考慮您的意見。'),
              backgroundColor: Colors.green,
            ),
          );

          // 清空表單
          _contentController.clear();

          // 返回上一頁
          Navigator.pop(context);
        } else {
          // 顯示錯誤訊息
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('提交回饋時出錯，請稍後再試。'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      logger.e('提交意見回饋時出錯: $e');

      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('提交回饋時出錯: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('意見回饋'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        '您的意見對我們很重要',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        '請告訴我們您的想法、建議或問題，我們會認真考慮每一條回饋。',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey,
                        ),
                      ),
                      const SizedBox(height: 16),

                      // 回饋類別
                      const Text(
                        '回饋類別',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      DropdownButtonFormField<String>(
                        value: _selectedCategory,
                        decoration: const InputDecoration(
                          border: OutlineInputBorder(),
                          contentPadding: EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 12,
                          ),
                        ),
                        items: _categories.map((category) {
                          return DropdownMenuItem<String>(
                            value: category,
                            child: Text(category),
                          );
                        }).toList(),
                        onChanged: (value) {
                          if (value != null) {
                            setState(() {
                              _selectedCategory = value;
                            });
                          }
                        },
                      ),
                      const SizedBox(height: 16),

                      // 回饋內容
                      const Text(
                        '回饋內容',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      TextFormField(
                        controller: _contentController,
                        decoration: const InputDecoration(
                          border: OutlineInputBorder(),
                          hintText: '請輸入您的意見或建議...',
                        ),
                        maxLines: 5,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return '請輸入回饋內容';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // 電子郵件（選填）
                      const Text(
                        '電子郵件（選填）',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      TextFormField(
                        controller: _emailController,
                        decoration: const InputDecoration(
                          border: OutlineInputBorder(),
                          hintText: '如需回覆，請留下您的電子郵件',
                        ),
                        keyboardType: TextInputType.emailAddress,
                        validator: (value) {
                          if (value != null && value.isNotEmpty) {
                            // 簡單的電子郵件格式驗證
                            if (!value.contains('@') || !value.contains('.')) {
                              return '請輸入有效的電子郵件地址';
                            }
                          }
                          return null;
                        },
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 24),

              // 提交按鈕
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _isSubmitting ? null : _submitFeedback,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.royalIndigo,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                  child: _isSubmitting
                      ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            color: Colors.white,
                            strokeWidth: 2,
                          ),
                        )
                      : const Text('提交回饋'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
