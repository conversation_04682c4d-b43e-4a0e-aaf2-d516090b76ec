import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../core/utils/logger_utils.dart';
import '../../data/models/astrology/chart_data.dart';
import '../../data/models/astrology/chart_settings.dart';
import '../../data/services/api/ai_api_service.dart';
import '../../data/services/api/astrology_service.dart';
import '../../data/services/api/chart_interpretation_service.dart';
import '../../data/services/api/interpretation_guidance_service.dart';
import '../themes/app_theme.dart';

/// 原始內容顯示頁面
/// 顯示AI解讀的原始輸入內容，包含星盤摘要、提示詞和指導內容
class OriginalContentPage extends StatefulWidget {
  final ChartData chartData;
  final String interpretationTitle;
  final String subtitle;
  final String? keyPoint;

  const OriginalContentPage({
    super.key,
    required this.chartData,
    required this.interpretationTitle,
    required this.subtitle,
    this.keyPoint,
  });

  @override
  State<OriginalContentPage> createState() => _OriginalContentPageState();
}

class _OriginalContentPageState extends State<OriginalContentPage> {
  bool _isLoading = true;
  String _originalContent = '';
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();
    _loadOriginalContent();
  }

  /// 載入原始內容
  Future<void> _loadOriginalContent() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = '';
      });

      // 檢查星盤數據是否完整，如果不完整則重新計算
      ChartData chartData = widget.chartData;
      if (chartData.planets == null || chartData.houses == null) {
        logger.w('數據為空，重新計算');
        // 重新計算
        ChartSettings chartSettings = await ChartSettings.loadFromPrefs();
        chartData = await AstrologyService()
            .calculateChartData(chartData, chartSettings: chartSettings);
      }

      // 構建星盤數據摘要
      String chartSummary = await AIApiService.buildChartSummary(chartData);
      String guidance = await InterpretationGuidanceService.getGuidance();
      String prompt = await ChartInterpretationService.getPrompt(
        chartData,
        widget.interpretationTitle,
        widget.subtitle,
        keyPoint: widget.keyPoint,
      );

      // 構建完整的原始內容
      final buffer = StringBuffer();
      buffer.writeln(chartSummary);
      buffer.writeln();
      buffer.writeln(prompt);
      buffer.writeln();
      buffer.writeln(guidance);

      setState(() {
        _originalContent = buffer.toString();
        _isLoading = false;
      });

      logger.d('原始內容載入成功，長度: ${_originalContent.length}');
    } catch (e) {
      logger.e('載入原始內容時出錯: $e');
      setState(() {
        _errorMessage = '載入原始內容時出錯: $e';
        _isLoading = false;
      });
    }
  }

  /// 複製原始內容
  Future<void> _copyOriginalContent() async {
    if (_originalContent.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('沒有內容可複製'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    try {
      await Clipboard.setData(ClipboardData(text: _originalContent));
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('原始內容已複製到剪貼板'),
            backgroundColor: AppColors.royalIndigo,
          ),
        );
      }
      logger.d('原始內容複製成功');
    } catch (e) {
      logger.e('複製原始內容時出錯: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('複製失敗: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('原始內容'),
        backgroundColor: AppColors.royalIndigo,
        foregroundColor: Colors.white,
        actions: [
          if (!_isLoading && _originalContent.isNotEmpty)
            IconButton(
              icon: const Icon(Icons.copy),
              onPressed: _copyOriginalContent,
              tooltip: '複製內容',
            ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(AppColors.royalIndigo),
            ),
            SizedBox(height: 16),
            Text('正在載入原始內容...'),
          ],
        ),
      );
    }

    if (_errorMessage.isNotEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage,
              style: const TextStyle(color: Colors.red),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadOriginalContent,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.royalIndigo,
                foregroundColor: Colors.white,
              ),
              child: const Text('重新載入'),
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        // 頂部操作區域
        Container(
          padding: const EdgeInsets.all(16),
          decoration: const BoxDecoration(
            color: AppColors.lightCornsilk,
            border: Border(
              bottom: BorderSide(color: Colors.grey, width: 0.5),
            ),
          ),
          child: Row(
            children: [
              const Icon(Icons.info_outline, color: AppColors.royalIndigo),
              const SizedBox(width: 8),
              const Expanded(
                child: Text(
                  '這是發送給AI的原始內容，包含星盤摘要、提示詞和解讀指導',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.black87,
                  ),
                ),
              ),
            ],
          ),
        ),
        
        // 內容顯示區域
        Expanded(
          child: SelectableRegion(
            focusNode: FocusNode(),
            selectionControls: MaterialTextSelectionControls(),
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: Text(
                  _originalContent,
                  style: const TextStyle(
                    fontSize: 14,
                    height: 1.5,
                    fontFamily: 'monospace',
                    color: Colors.black87,
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
