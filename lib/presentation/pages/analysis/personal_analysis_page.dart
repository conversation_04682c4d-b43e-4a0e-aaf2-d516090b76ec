import 'package:flutter/material.dart';

import '../../../astreal.dart';
import '../../../shared/widgets/common/responsive_wrapper.dart';
import '../ai_interpretation_result_page.dart';
import '../chart_selection_page.dart';
import '../interpretation_records_page.dart';

/// 個人分析頁面
class PersonalAnalysisPage extends StatefulWidget {
  final HomeViewModel? homeViewModel;

  const PersonalAnalysisPage({
    super.key,
    this.homeViewModel,
  });

  @override
  State<PersonalAnalysisPage> createState() => _PersonalAnalysisPageState();
}

class _PersonalAnalysisPageState extends State<PersonalAnalysisPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text(
          '我的分析',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w500,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppColors.royalIndigo,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          // 查看分析記錄按鈕
          IconButton(
            onPressed: _viewAnalysisHistory,
            icon: const Icon(
              Icons.history,
              color: Colors.white,
            ),
            tooltip: '查看分析記錄',
          ),
        ],
      ),
      body: ResponsivePageWrapper(
        maxWidth: 800.0, // 分析頁面適合中等寬度
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 用戶資訊區域
              ResponsiveCardWrapper(
                maxWidth: 700.0,
                child: _buildUserInfoSection(),
              ),

              // 分隔線
              Divider(
                height: 1,
                color: Colors.grey[200],
              ),

              // 分析類別列表
              ResponsiveCardWrapper(
                maxWidth: 700.0,
                child: _buildAnalysisCategoriesSection(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 構建用戶資訊區域
  Widget _buildUserInfoSection() {
    final viewModel = widget.homeViewModel;
    return Container(
      padding: const EdgeInsets.all(20),
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppColors.royalIndigo.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.analytics_outlined,
                  color: AppColors.royalIndigo,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '個人星盤分析',
                      style: TextStyle(
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textDark,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      viewModel?.selectedPerson != null
                          ? '基於 ${viewModel!.selectedPerson!.name} 的星盤'
                          : '請先選擇或創建星盤',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                        height: 1.3,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          if (viewModel?.selectedPerson == null) ...[
            const SizedBox(height: 24),
            Center(
              child: Column(
                children: [
                  Icon(
                    Icons.analytics_outlined,
                    color: AppColors.indigoLight,
                    size: 48,
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    '需要星盤資料才能進行分析',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: AppColors.textDark,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '創建或選擇星盤後即可開始個人分析',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const ChartSelectionPage(),
                        ),
                      );
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.royalIndigo,
                      foregroundColor: Colors.white,
                      elevation: 0,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 32,
                        vertical: 16,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text(
                      '創建或選擇星盤',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ] else ...[
            const SizedBox(height: 16),
            Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: AppColors.indigoLight.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Center(
                    child: Text(
                      viewModel?.selectedPerson?.name.isNotEmpty == true
                          ? viewModel!.selectedPerson!.name[0].toUpperCase()
                          : '✨',
                      style: const TextStyle(
                        color: AppColors.royalIndigo,
                        fontWeight: FontWeight.w500,
                        fontSize: 16,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        viewModel?.selectedPerson?.name ?? '',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: AppColors.textDark,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        viewModel?.selectedPerson != null
                            ? '${viewModel!.selectedPerson!.dateTime.year}年${viewModel.selectedPerson!.dateTime.month}月${viewModel.selectedPerson!.dateTime.day}日 • ${viewModel.selectedPerson!.birthPlace}'
                            : '',
                        style: TextStyle(
                          fontSize: 13,
                          color: Colors.grey[600],
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  /// 構建分析類別區域
  Widget _buildAnalysisCategoriesSection() {
    final viewModel = widget.homeViewModel;

    if (viewModel?.selectedPerson == null) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '分析類別',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppColors.textDark,
            ),
          ),
          const SizedBox(height: 16),

          // 分析類別列表
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: AnalysisCategory.categories.length,
            separatorBuilder: (context, index) => const SizedBox(height: 12),
            itemBuilder: (context, index) {
              final category = AnalysisCategory.categories[index];
              return _buildAnalysisCategoryItem(
                  category, viewModel!.selectedPerson!);
            },
          ),
        ],
      ),
    );
  }

  /// 構建分析類別項目
  Widget _buildAnalysisCategoryItem(
      AnalysisCategory category, BirthData birthData) {
    return GestureDetector(
      onTap: () => _navigateToAnalysisDetail(category, birthData),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: Colors.grey[200]!,
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.solarAmber.withOpacity(0.1),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Icon(
                category.icon,
                color: AppColors.solarAmber,
                size: 20,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    category.name,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: AppColors.textDark,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    category.description,
                    style: TextStyle(
                      fontSize: 13,
                      color: Colors.grey[600],
                      height: 1.3,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: AppColors.indigoLight,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  /// 導航到分析詳情
  void _navigateToAnalysisDetail(AnalysisCategory category, BirthData birthData) {
    final chartData = ChartData(
      chartType: ChartType.natal,
      primaryPerson: birthData,
    );

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AIInterpretationResultPage(
          chartData: chartData,
          interpretationTitle: category.name,
          subtitle: category.description,
          suggestedQuestions: category.analysisPoints,
          autoExecuteFirstQuestion: false,
        ),
      ),
    );
  }

  /// 查看分析記錄
  void _viewAnalysisHistory() {
    print('📚 查看分析記錄');
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const InterpretationRecordsPage(),
      ),
    );
  }
}
