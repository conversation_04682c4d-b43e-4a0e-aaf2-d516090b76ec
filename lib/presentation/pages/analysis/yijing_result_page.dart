import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../../astreal.dart';
import '../../../data/services/api/ai_api_service.dart';
import '../../../data/services/api/chart_interpretation_service.dart';

class YijingResultPage extends StatefulWidget {
  final YijingDivinationResult result;

  const YijingResultPage({
    super.key,
    required this.result,
  });

  @override
  State<YijingResultPage> createState() => _YijingResultPageState();
}

class _YijingResultPageState extends State<YijingResultPage> {
  String? _aiInterpretation;
  bool _isLoadingInterpretation = false;

  @override
  void initState() {
    super.initState();
    _loadAIInterpretation();
  }

  /// 載入 AI 解讀
  Future<void> _loadAIInterpretation() async {
    if (widget.result.aiInterpretation != null) {
      setState(() {
        _aiInterpretation = widget.result.aiInterpretation;
      });
      return;
    }

    setState(() {
      _isLoadingInterpretation = true;
    });

    try {
      final interpretation = await _generateAIInterpretation();
      setState(() {
        _aiInterpretation = interpretation.content;
        _isLoadingInterpretation = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingInterpretation = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('解讀載入失敗：$e')),
        );
      }
    }
  }

  /// 生成 AI 解讀
  Future<AIApiResponse> _generateAIInterpretation() async {
    final prompt = _buildInterpretationPrompt();
    
    try {
      // 創建一個虛擬的 ChartData 用於 AI 解讀
      final dummyChartData = ChartData(
        chartType: ChartType.natal,
        primaryPerson: BirthData(
          id: 'yijing_dummy',
          name: '卜卦者',
          dateTime: widget.result.divinationTime,
          birthPlace: '未指定',
          latitude: 0.0,
          longitude: 0.0,
        ),
      );

      final interpretation = await ChartInterpretationService.getCustomInterpretation(
        chartData: dummyChartData,
        customPrompt: prompt,
      );
      return interpretation;
    } catch (e) {
      throw Exception('解讀服務暫時不可用：$e');
    }
  }

  /// 構建解讀提示詞
  String _buildInterpretationPrompt() {
    final result = widget.result;
    final primary = result.primaryHexagram;
    
    String prompt = '''
請為以下周易卜卦結果提供專業的解讀分析：

問題：${result.question}
卜卦時間：${result.divinationTime.toString().substring(0, 19)}

本卦：${primary.name}卦（第${primary.number}卦）
卦象：${primary.symbol}
上卦：${primary.upperTrigram}
下卦：${primary.lowerTrigram}
卦辭：${primary.judgment}
象辭：${primary.image}

六爻：
${primary.lineTexts.asMap().entries.map((entry) => '${entry.key + 1}. ${entry.value}').join('\n')}
''';

    if (result.hasChangingHexagram) {
      final changing = result.changingHexagram!;
      prompt += '''

變卦：${changing.name}卦（第${changing.number}卦）
變爻位置：${result.changingLines.map((i) => '第${i + 1}爻').join('、')}
變卦卦辭：${changing.judgment}
變卦象辭：${changing.image}
''';
    }

    prompt += '''

請提供以下內容的解讀：
1. 卦象總體含義
2. 針對所問問題的具體分析
3. 當前情況的判斷
4. 未來發展趨勢
5. 建議的行動方針
${result.hasChangingHexagram ? '6. 變卦的意義和影響' : ''}

請用繁體中文回答，語言要通俗易懂，避免過於艱澀的古文。
''';

    return prompt;
  }

  /// 複製解讀內容
  void _copyInterpretation() {
    if (_aiInterpretation != null) {
      // 移除 Markdown 格式，方便分享給其他人
      final cleanContent = _removeMarkdownFormatting(_aiInterpretation!);
      Clipboard.setData(ClipboardData(text: cleanContent));
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('解讀內容已複製到剪貼板（已移除格式）')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('周易卜卦結果'),
        backgroundColor: AppColors.royalIndigo,
        foregroundColor: Colors.white,
        actions: [
          if (_aiInterpretation != null)
            IconButton(
              icon: const Icon(Icons.copy),
              onPressed: _copyInterpretation,
              tooltip: '複製解讀',
            ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildQuestionCard(),
            const SizedBox(height: 16),
            _buildHexagramCard(),
            if (widget.result.hasChangingHexagram) ...[
              const SizedBox(height: 16),
              _buildChangingHexagramCard(),
            ],
            const SizedBox(height: 16),
            _buildAIInterpretationCard(),
          ],
        ),
      ),
    );
  }

  /// 構建問題卡片
  Widget _buildQuestionCard() {
    return StyledCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.help_outline,
                  color: AppColors.royalIndigo,
                  size: 24,
                ),
                const SizedBox(width: 8),
                const Text(
                  '卜卦問題',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              widget.result.question,
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 8),
            Text(
              '卜卦時間：${widget.result.divinationTime.toString().substring(0, 19)}',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 構建卦象卡片
  Widget _buildHexagramCard() {
    final hexagram = widget.result.primaryHexagram;
    
    return StyledCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  hexagram.symbol,
                  style: const TextStyle(fontSize: 32),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${hexagram.name}卦（第${hexagram.number}卦）',
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        '上${hexagram.upperTrigram} 下${hexagram.lowerTrigram}',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildHexagramLines(hexagram.lines, widget.result.changingLines),
            const SizedBox(height: 16),
            _buildHexagramTexts(hexagram),
          ],
        ),
      ),
    );
  }

  /// 構建變卦卡片
  Widget _buildChangingHexagramCard() {
    final hexagram = widget.result.changingHexagram!;
    
    return StyledCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.change_circle,
                  color: AppColors.solarAmber,
                  size: 24,
                ),
                const SizedBox(width: 8),
                const Text(
                  '變卦',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Text(
                  hexagram.symbol,
                  style: const TextStyle(fontSize: 32),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${hexagram.name}卦（第${hexagram.number}卦）',
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        '變爻：${widget.result.changingLines.map((i) => '第${i + 1}爻').join('、')}',
                        style: TextStyle(
                          fontSize: 14,
                          color: AppColors.solarAmber,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildHexagramLines(hexagram.lines, []),
            const SizedBox(height: 16),
            _buildHexagramTexts(hexagram),
          ],
        ),
      ),
    );
  }

  /// 構建卦象爻線
  Widget _buildHexagramLines(List<bool> lines, List<int> changingLines) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        children: List.generate(6, (index) {
          final lineIndex = 5 - index; // 從上到下顯示
          final isYang = lines[lineIndex];
          final isChanging = changingLines.contains(lineIndex);

          return Padding(
            padding: const EdgeInsets.symmetric(vertical: 4),
            child: Row(
              children: [
                SizedBox(
                  width: 30,
                  child: Text(
                    '${lineIndex + 1}',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ),
                Expanded(
                  child: Container(
                    height: 8,
                    decoration: BoxDecoration(
                      color: isChanging ? AppColors.solarAmber : AppColors.royalIndigo,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: isYang
                        ? Container() // 陽爻：實線
                        : Row(
                            children: [
                              Expanded(flex: 2, child: Container()),
                              Expanded(flex: 1, child: Container(color: Colors.transparent)),
                              Expanded(flex: 2, child: Container()),
                            ],
                          ), // 陰爻：斷線
                  ),
                ),
                if (isChanging)
                  Padding(
                    padding: const EdgeInsets.only(left: 8),
                    child: Icon(
                      Icons.change_circle,
                      size: 16,
                      color: AppColors.solarAmber,
                    ),
                  ),
              ],
            ),
          );
        }),
      ),
    );
  }

  /// 構建卦辭和象辭
  Widget _buildHexagramTexts(YijingHexagram hexagram) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildTextSection('卦辭', hexagram.judgment),
        const SizedBox(height: 12),
        _buildTextSection('象辭', hexagram.image),
        const SizedBox(height: 12),
        _buildTextSection('爻辭', hexagram.lineTexts.join('\n')),
      ],
    );
  }

  /// 構建文字區段
  Widget _buildTextSection(String title, String content) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: AppColors.royalIndigo,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          content,
          style: const TextStyle(fontSize: 14, height: 1.5),
        ),
      ],
    );
  }

  /// 構建 AI 解讀卡片
  Widget _buildAIInterpretationCard() {
    return StyledCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.psychology,
                  color: AppColors.royalIndigo,
                  size: 24,
                ),
                const SizedBox(width: 8),
                const Text(
                  '深入剖析',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                if (_isLoadingInterpretation)
                  const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
              ],
            ),
            const SizedBox(height: 16),
            if (_isLoadingInterpretation)
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(32),
                  child: Column(
                    children: [
                      CircularProgressIndicator(),
                      SizedBox(height: 16),
                      Text('正在分析卦象，請稍候...'),
                    ],
                  ),
                ),
              )
            else if (_aiInterpretation != null)
              Text(
                _aiInterpretation!,
                style: const TextStyle(fontSize: 14, height: 1.6),
              )
            else
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.orange.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.orange.shade200),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.warning_rounded,
                      color: Colors.orange.shade600,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    const Expanded(
                      child: Text(
                        '解讀載入失敗，請檢查網路連接後重試',
                        style: TextStyle(fontSize: 14),
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// 移除 Markdown 格式，方便分享給其他人
  String _removeMarkdownFormatting(String content) {
    // 移除 Markdown 標記，但保留內容
    String cleaned = content;

    // 先移除代碼塊（包含內容）
    final lines = cleaned.split('\n');
    final filteredLines = <String>[];
    bool inCodeBlock = false;

    for (final line in lines) {
      if (line.trim().startsWith('```')) {
        inCodeBlock = !inCodeBlock;
        continue; // 跳過代碼塊標記行
      }
      if (!inCodeBlock) {
        filteredLines.add(line);
      }
    }

    cleaned = filteredLines.join('\n');

    // 處理粗體：**text** -> text
    cleaned = cleaned.replaceAllMapped(RegExp(r'\*\*(.*?)\*\*'), (match) => match.group(1) ?? '');

    // 處理斜體：*text* -> text
    cleaned = cleaned.replaceAllMapped(RegExp(r'\*(.*?)\*'), (match) => match.group(1) ?? '');

    // 移除標題：# -> 空
    cleaned = cleaned.replaceAll(RegExp(r'#{1,6}\s*'), '');

    // 處理連結：[text](url) -> text
    cleaned = cleaned.replaceAllMapped(RegExp(r'\[([^\]]+)\]\([^)]+\)'), (match) => match.group(1) ?? '');

    // 處理行內代碼：`code` -> code
    cleaned = cleaned.replaceAllMapped(RegExp(r'`([^`]+)`'), (match) => match.group(1) ?? '');

    // 列表項目：- item -> • item
    cleaned = cleaned.replaceAll(RegExp(r'^\s*[-*+]\s+', multiLine: true), '• ');

    // 數字列表：1. item -> item
    cleaned = cleaned.replaceAll(RegExp(r'^\s*\d+\.\s+', multiLine: true), '');

    // 多餘的換行：最多保留兩個
    cleaned = cleaned.replaceAll(RegExp(r'\n{3,}'), '\n\n');

    return cleaned.trim();
  }
}
