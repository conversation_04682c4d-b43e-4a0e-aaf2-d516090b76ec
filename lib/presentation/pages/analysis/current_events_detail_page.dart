import 'package:flutter/material.dart';

import '../../../core/utils/logger_utils.dart';
import '../../../data/models/current_events/current_events_data.dart';
import '../../../data/services/api/current_events_service.dart';
import '../../../shared/widgets/web_aware_app_bar.dart';
import '../../../shared/widgets/web_aware_pop_scope.dart';

/// 時事分析詳情頁面
/// 顯示特定時事分析事件的詳細資訊和說明
class CurrentEventsDetailPage extends StatefulWidget {
  final CurrentEventsData event;

  const CurrentEventsDetailPage({
    super.key,
    required this.event,
  });

  @override
  State<CurrentEventsDetailPage> createState() => _CurrentEventsDetailPageState();
}

class _CurrentEventsDetailPageState extends State<CurrentEventsDetailPage> {
  String _typeExplanation = '';
  bool _isLoadingExplanation = true;

  @override
  void initState() {
    super.initState();
    _loadTypeExplanation();
  }

  /// 載入類型說明
  void _loadTypeExplanation() {
    setState(() {
      _isLoadingExplanation = true;
    });

    try {
      _typeExplanation = CurrentEventsService.getTypeExplanation(widget.event.type);
      setState(() {
        _isLoadingExplanation = false;
      });
    } catch (e) {
      logger.e('載入類型說明失敗: $e');
      setState(() {
        _isLoadingExplanation = false;
        _typeExplanation = '載入說明失敗，請稍後再試。';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return WebAwarePopScope(
      routeName: '/current_events_detail',
      child: Scaffold(
        appBar: WebAwareAppBarHelper.simple(
          title: widget.event.title,
          actions: [
            IconButton(
              icon: const Icon(Icons.share),
              onPressed: _shareEvent,
              tooltip: '分享',
            ),
          ],
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 事件標題卡片
              _buildEventHeader(),
              
              const SizedBox(height: 20),
              
              // 事件詳情
              _buildEventDetails(),
              
              const SizedBox(height: 20),
              
              // 類型說明
              _buildTypeExplanation(),
              
              const SizedBox(height: 20),
              
              // 關鍵字
              if (widget.event.keywords.isNotEmpty)
                _buildKeywords(),
              
              const SizedBox(height: 20),
              
              // 操作按鈕
              _buildActionButtons(),
            ],
          ),
        ),
      ),
    );
  }

  /// 構建事件標題
  Widget _buildEventHeader() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: double.infinity,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              widget.event.themeColor.withValues(alpha: 0.1),
              widget.event.themeColor.withValues(alpha: 0.05),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(16),
        ),
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                // 類型圖示
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: widget.event.themeColor.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(30),
                  ),
                  child: Icon(
                    _getEventIcon(widget.event.type),
                    color: widget.event.themeColor,
                    size: 30,
                  ),
                ),
                const SizedBox(width: 16),
                
                // 標題和狀態
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.event.title,
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: widget.event.themeColor,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        widget.event.beginnerTypeDescription,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey[700],
                        ),
                      ),
                    ],
                  ),
                ),
                
                // 活躍狀態
                if (widget.event.isActive)
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.green,
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: const Text(
                      '活躍中',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // 事件日期
            Row(
              children: [
                Icon(
                  Icons.schedule,
                  size: 18,
                  color: widget.event.themeColor,
                ),
                const SizedBox(width: 8),
                Text(
                  '事件時間：${_formatDateTime(widget.event.eventDate)}',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 構建事件詳情
  Widget _buildEventDetails() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '事件說明',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              widget.event.beginnerDescription,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                height: 1.6,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              '詳細描述',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              widget.event.description,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                height: 1.5,
                color: Colors.grey[700],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 構建類型說明
  Widget _buildTypeExplanation() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: widget.event.themeColor,
                ),
                const SizedBox(width: 8),
                Text(
                  '${widget.event.typeDisplayName}詳細說明',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: widget.event.themeColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_isLoadingExplanation)
              const Center(
                child: CircularProgressIndicator(),
              )
            else
              Text(
                _typeExplanation,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  height: 1.6,
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// 構建關鍵字
  Widget _buildKeywords() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '相關關鍵字',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: widget.event.keywords.map((keyword) {
                return Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: widget.event.themeColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: widget.event.themeColor.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Text(
                    keyword,
                    style: TextStyle(
                      color: widget.event.themeColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  /// 構建操作按鈕
  Widget _buildActionButtons() {
    return Column(
      children: [
        // 主要操作按鈕
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: _startAnalysis,
            icon: const Icon(Icons.analytics),
            label: Text('開始${widget.event.typeDisplayName}'),
            style: ElevatedButton.styleFrom(
              backgroundColor: widget.event.themeColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),
        
        const SizedBox(height: 12),
        
        // 次要操作按鈕
        Row(
          children: [
            Expanded(
              child: OutlinedButton.icon(
                onPressed: _shareEvent,
                icon: const Icon(Icons.share),
                label: const Text('分享'),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: OutlinedButton.icon(
                onPressed: _addToCalendar,
                icon: const Icon(Icons.calendar_today),
                label: const Text('加入行事曆'),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 獲取事件類型圖示
  IconData _getEventIcon(CurrentEventsType type) {
    switch (type) {
      case CurrentEventsType.horary:
        return Icons.help_center;
      case CurrentEventsType.solstice:
        return Icons.wb_sunny;
      case CurrentEventsType.eclipse:
        return Icons.brightness_2;
    }
  }

  /// 格式化日期時間
  String _formatDateTime(DateTime date) {
    return '${date.year}年${date.month}月${date.day}日 ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
  }

  /// 開始分析
  void _startAnalysis() {
    logger.d('開始${widget.event.typeDisplayName}：${widget.event.title}');
    
    // TODO: 實現具體的分析功能
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('${widget.event.typeDisplayName}功能開發中...'),
        backgroundColor: widget.event.themeColor,
      ),
    );
  }

  /// 分享事件
  void _shareEvent() {
    logger.d('分享事件：${widget.event.title}');
    
    // TODO: 實現分享功能
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('分享功能開發中...')),
    );
  }

  /// 加入行事曆
  void _addToCalendar() {
    logger.d('加入行事曆：${widget.event.title}');
    
    // TODO: 實現加入行事曆功能
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('行事曆功能開發中...')),
    );
  }
}
