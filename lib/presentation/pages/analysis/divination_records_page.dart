import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:uuid/uuid.dart';

import '../../../core/utils/logger_utils.dart';
import '../../../data/models/astrology/chart_data.dart';
import '../../../data/models/astrology/chart_type.dart';
import '../../../data/models/interpretation/divination_record.dart';
import '../../../data/models/user/birth_data.dart';
import '../../../data/services/api/astrology_service.dart';
import '../../../data/services/api/divination_record_service.dart';
import '../../themes/app_theme.dart';
import '../../viewmodels/chart_viewmodel.dart';
import '../chart_page.dart';

class DivinationRecordsPage extends StatefulWidget {
  const DivinationRecordsPage({Key? key}) : super(key: key);

  @override
  State<DivinationRecordsPage> createState() => _DivinationRecordsPageState();
}

class _DivinationRecordsPageState extends State<DivinationRecordsPage> {
  List<DivinationRecord> _records = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadRecords();
  }

  /// 加載卜卦記錄
  Future<void> _loadRecords() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final records = await DivinationRecordService.getAllRecords();
      setState(() {
        _records = records;
        _isLoading = false;
      });
    } catch (e) {
      logger.e('加載卜卦記錄時出錯: $e');
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('加載卜卦記錄時出錯: $e')),
        );
      }
    }
  }

  /// 刪除卜卦記錄
  Future<void> _deleteRecord(DivinationRecord record) async {
    try {
      await DivinationRecordService.deleteRecord(record.id);
      await _loadRecords();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('卜卦記錄已刪除')),
        );
      }
    } catch (e) {
      logger.e('刪除卜卦記錄時出錯: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('刪除卜卦記錄時出錯: $e')),
        );
      }
    }
  }

  /// 從記錄中查看星盤
  Future<void> _viewChartFromRecord(DivinationRecord record) async {
    try {
      if (record.chartData == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('此記錄沒有星盤數據')),
        );
        return;
      }

      // 顯示加載指示器
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );

      try {
        // 從記錄中重建 ChartData 物件
        final chartData = await _reconstructChartDataFromRecord(record);

        // 關閉加載指示器
        if (mounted) {
          Navigator.of(context).pop();
        }

        if (chartData != null && mounted) {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => ChangeNotifierProvider(
                create: (_) =>
                    ChartViewModel.withChartData(initialChartData: chartData),
                child: ChartPage(chartData: chartData),
              ),
            ),
          );
        } else if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('無法重建星盤數據')),
          );
        }
      } catch (e) {
        // 關閉加載指示器
        if (mounted) {
          Navigator.of(context).pop();
        }
        throw e;
      }
    } catch (e) {
      logger.e('查看記錄星盤時出錯: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('查看星盤時出錯: $e')),
        );
      }
    }
  }

  /// 從記錄中重建 ChartData 物件
  Future<ChartData?> _reconstructChartDataFromRecord(
      DivinationRecord record) async {
    try {
      if (record.chartData == null) {
        logger.w('記錄中沒有星盤數據');
        return null;
      }

      logger.d('開始重建星盤數據...');

      // 從記錄中獲取基本信息來重新創建星盤
      // 嘗試從 chartData 中獲取座標信息
      double latitude = 25.0330; // 預設台北座標
      double longitude = 121.5654;

      if (record.chartData!.containsKey('primaryPerson')) {
        final primaryPersonData =
            record.chartData!['primaryPerson'] as Map<String, dynamic>?;
        if (primaryPersonData != null) {
          latitude =
              (primaryPersonData['latitude'] as num?)?.toDouble() ?? latitude;
          longitude =
              (primaryPersonData['longitude'] as num?)?.toDouble() ?? longitude;
          logger.d('從記錄中獲取座標: $latitude, $longitude');
        }
      }

      // 創建一個簡化的 BirthData 物件
      final birthData = BirthData(
        id: 'horary_${record.id}',
        name: '占星',
        dateTime: record.timestamp,
        birthPlace: record.location,
        latitude: latitude,
        longitude: longitude,
        notes: record.question,
      );

      // 創建 ChartData 物件
      final chartData = ChartData(
        chartType: ChartType.horary,
        primaryPerson: birthData,
        specificDate: record.timestamp,
      );

      // 重新計算星盤數據
      logger.d('重新計算星盤數據...');
      final calculatedChartData = await AstrologyService().calculateChartData(chartData);
      logger.d('星盤數據重建完成');
      return calculatedChartData;
    } catch (e) {
      logger.e('重建 ChartData 時出錯: $e');
      return null;
    }
  }

  /// 測試添加記錄（僅用於調試）
  Future<void> _addTestRecord() async {
    try {
      final testRecord = DivinationRecord(
        id: const Uuid().v4(),
        question: '測試問題：今天的運勢如何？',
        location: '台北市',
        timestamp: DateTime.now(),
        type: 'horary',
        result: '測試結果：今天的運勢非常好，適合進行新的嘗試。',
        chartData: {
          'test': true,
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      await DivinationRecordService.saveRecord(testRecord);
      await _loadRecords();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('測試記錄已添加')),
        );
      }
    } catch (e) {
      logger.e('添加測試記錄時出錯: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('添加測試記錄時出錯: $e')),
        );
      }
    }
  }

  /// 清除所有卜卦記錄
  Future<void> _clearAllRecords() async {
    // 顯示確認對話框
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('確認清除'),
        content: const Text('確定要清除所有卜卦記錄嗎？此操作無法撤銷。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('確定'),
          ),
        ],
      ),
    );

    if (confirmed != true) {
      return;
    }

    try {
      await DivinationRecordService.clearAllRecords();
      await _loadRecords();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('所有卜卦記錄已清除')),
        );
      }
    } catch (e) {
      logger.e('清除卜卦記錄時出錯: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('清除卜卦記錄時出錯: $e')),
        );
      }
    }
  }

  /// 顯示卜卦記錄詳情
  void _showRecordDetails(DivinationRecord record) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) {
        return Container(
          padding: const EdgeInsets.all(16),
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.7,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    _getRecordTypeTitle(record.type),
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                ],
              ),
              const Divider(),
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 8),
                      _buildDetailItem('問題', record.question),
                      _buildDetailItem(
                          '時間',
                          DateFormat('yyyy-MM-dd HH:mm')
                              .format(record.timestamp)),
                      _buildDetailItem('地點', record.location),
                      const SizedBox(height: 16),
                      Row(
                        children: [
                          // 左側標題
                          const Expanded(
                            child: Align(
                              alignment: Alignment.centerLeft,
                              child: Text(
                                '卜卦結果',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),
                          // 右側按鈕（如果有星盤資料）
                          if (record.chartData != null && record.type == 'horary')
                            Align(
                              alignment: Alignment.centerRight,
                              child: ElevatedButton.icon(
                                icon: const Icon(Icons.auto_awesome),
                                label: const Text('查看星盤'),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: AppColors.royalIndigo,
                                  foregroundColor: Colors.white,
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 24,
                                    vertical: 12,
                                  ),
                                ),
                                onPressed: () {
                                  Navigator.of(context).pop(); // 關閉當前對話框
                                  _viewChartFromRecord(record);
                                },
                              ),
                            ),
                        ],
                      ),

                      const SizedBox(height: 8),
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.grey.shade100,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          record.result,
                          style: const TextStyle(fontSize: 14),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// 構建詳情項目
  Widget _buildDetailItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade700,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  /// 獲取卜卦類型標題
  String _getRecordTypeTitle(String type) {
    switch (type) {
      case 'horary':
        return '占星卜卦';
      case 'yijing':
        return '周易卜卦';
      case 'tarot':
        return '塔羅牌占卜';
      default:
        return '卜卦記錄';
    }
  }

  /// 獲取卜卦類型圖標
  IconData _getRecordTypeIcon(String type) {
    switch (type) {
      case 'horary':
        return Icons.auto_awesome;
      case 'yijing':
        return Icons.change_history;
      case 'tarot':
        return Icons.style;
      default:
        return Icons.question_answer;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('卜卦記錄'),
        actions: [
          // 測試按鈕（僅用於調試）
          IconButton(
            icon: const Icon(Icons.add),
            tooltip: '測試添加記錄',
            onPressed: _addTestRecord,
          ),
          if (_records.isNotEmpty)
            IconButton(
              icon: const Icon(Icons.delete_sweep),
              tooltip: '清除所有記錄',
              onPressed: _clearAllRecords,
            ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _records.isEmpty
              ? _buildEmptyState()
              : _buildRecordsList(),
    );
  }

  /// 構建空狀態
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.history,
            size: 64,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          const Text(
            '暫無卜卦記錄',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            '進行卜卦後，記錄將顯示在這裡',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  /// 構建記錄列表
  Widget _buildRecordsList() {
    return RefreshIndicator(
      onRefresh: _loadRecords,
      child: ListView.builder(
        padding: const EdgeInsets.all(8),
        itemCount: _records.length,
        itemBuilder: (context, index) {
          final record = _records[index];
          return Card(
            margin: const EdgeInsets.only(bottom: 8),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: ListTile(
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 8,
              ),
              leading: CircleAvatar(
                backgroundColor: AppColors.royalIndigo.withValues(alpha: 0.1),
                child: Icon(
                  _getRecordTypeIcon(record.type),
                  color: AppColors.royalIndigo,
                ),
              ),
              title: Text(
                record.question,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                ),
              ),
              subtitle: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 4),
                  Text(
                    '${DateFormat('yyyy-MM-dd HH:mm').format(record.timestamp)} · ${_getRecordTypeTitle(record.type)}',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
              trailing: IconButton(
                icon: const Icon(Icons.delete_outline),
                onPressed: () => _deleteRecord(record),
              ),
              onTap: () => _showRecordDetails(record),
            ),
          );
        },
      ),
    );
  }
}
