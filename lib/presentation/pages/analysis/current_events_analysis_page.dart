import 'package:flutter/material.dart';

import '../../../core/utils/logger_utils.dart';
import '../../../data/models/current_events/current_events_data.dart';
import '../../../data/services/api/current_events_service.dart';
import '../../../presentation/themes/app_theme.dart';
import '../../../shared/widgets/web_aware_pop_scope.dart';
import 'current_events_detail_page.dart';

/// 時事分析頁面
/// 顯示卜卦分析、二分二至盤、日月蝕盤等時事占星功能
class CurrentEventsAnalysisPage extends StatefulWidget {
  const CurrentEventsAnalysisPage({super.key});

  @override
  State<CurrentEventsAnalysisPage> createState() => _CurrentEventsAnalysisPageState();
}

class _CurrentEventsAnalysisPageState extends State<CurrentEventsAnalysisPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  
  List<CurrentEventsData> _allEvents = [];
  List<CurrentEventsData> _activeEvents = [];
  List<CurrentEventsData> _horaryEvents = [];
  List<CurrentEventsData> _solsticeEvents = [];
  List<CurrentEventsData> _eclipseEvents = [];
  
  bool _isLoading = true;
  bool _hasError = false;
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadEvents();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// 載入時事分析事件
  Future<void> _loadEvents() async {
    logger.d('載入時事分析事件');
    setState(() {
      _isLoading = true;
      _hasError = false;
    });

    try {
      final results = await Future.wait([
        CurrentEventsService.getAllEvents(),
        CurrentEventsService.getActiveEvents(),
        CurrentEventsService.getEventsByType(CurrentEventsType.horary),
        CurrentEventsService.getEventsByType(CurrentEventsType.solstice),
        CurrentEventsService.getEventsByType(CurrentEventsType.eclipse),
      ]);

      setState(() {
        _allEvents = results[0];
        _activeEvents = results[1];
        _horaryEvents = results[2];
        _solsticeEvents = results[3];
        _eclipseEvents = results[4];
        _isLoading = false;
      });

      logger.i('成功載入時事分析事件：${_allEvents.length} 個事件');
    } catch (e) {
      logger.e('載入時事分析事件失敗: $e');
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = '載入失敗：$e';
      });
    }
  }

  /// 刷新事件
  Future<void> _refreshEvents() async {
    try {
      await CurrentEventsService.refreshConfig();
      await _loadEvents();
    } catch (e) {
      logger.e('刷新時事分析事件失敗: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('刷新失敗：$e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return WebAwarePopScope(
      routeName: '/current_events_analysis',
      child: Scaffold(
        appBar: AppBar(
          title: const Text('時事分析'),
          actions: [
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: _isLoading ? null : _refreshEvents,
              tooltip: '重新整理',
            ),
            IconButton(
              icon: const Icon(Icons.help_outline),
              onPressed: _showHelpDialog,
              tooltip: '說明',
            ),
          ],
          bottom: TabBar(
            controller: _tabController,
            labelColor: AppColors.royalIndigo,
            unselectedLabelColor: Colors.grey[600],
            indicatorColor: AppColors.royalIndigo,
            tabs: const [
              Tab(text: '活躍事件'),
              Tab(text: '卜卦分析'),
              Tab(text: '二分二至'),
              Tab(text: '日月蝕'),
            ],
          ),
        ),
        body: _buildBody(),
      ),
    );
  }

  /// 構建主體內容
  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_hasError) {
      return _buildErrorState();
    }

    return TabBarView(
      controller: _tabController,
      children: [
        _buildEventsList(_activeEvents, '目前沒有活躍的時事分析事件'),
        _buildEventsList(_horaryEvents, '目前沒有卜卦分析事件'),
        _buildEventsList(_solsticeEvents, '目前沒有二分二至盤事件'),
        _buildEventsList(_eclipseEvents, '目前沒有日月蝕盤事件'),
      ],
    );
  }

  /// 構建錯誤狀態
  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            '載入失敗',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 8),
          Text(
            _errorMessage,
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _loadEvents,
            child: const Text('重試'),
          ),
        ],
      ),
    );
  }

  /// 構建事件列表
  Widget _buildEventsList(List<CurrentEventsData> events, String emptyMessage) {
    if (events.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.event_note,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              emptyMessage,
              style: Theme.of(context).textTheme.bodyLarge,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _refreshEvents,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: events.length,
        itemBuilder: (context, index) {
          final event = events[index];
          return _buildEventCard(event);
        },
      ),
    );
  }

  /// 構建事件卡片
  Widget _buildEventCard(CurrentEventsData event) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () => _openEventDetail(event),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
            Row(
              children: [
                // 類型圖示
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: event.themeColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(24),
                  ),
                  child: Icon(
                    _getEventIcon(event.type),
                    color: event.themeColor,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                
                // 事件資訊
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 標題和活躍狀態
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              event.title,
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          if (event.isActive)
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 4,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.green,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: const Text(
                                '活躍',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      
                      // 類型描述
                      Text(
                        event.beginnerTypeDescription,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: event.themeColor,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 8),
                      
                      // 初心者描述
                      Text(
                        event.beginnerDescription,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey[600],
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 8),
                      
                      // 日期和關鍵字
                      Row(
                        children: [
                          Icon(
                            Icons.schedule,
                            size: 14,
                            color: Colors.grey[500],
                          ),
                          const SizedBox(width: 4),
                          Text(
                            _formatDate(event.eventDate),
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                          ),
                          const Spacer(),
                          if (event.keywords.isNotEmpty)
                            Wrap(
                              spacing: 4,
                              children: event.keywords.take(2).map((keyword) {
                                return Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 6,
                                    vertical: 2,
                                  ),
                                  decoration: BoxDecoration(
                                    color: Colors.grey[200],
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Text(
                                    keyword,
                                    style: const TextStyle(
                                      fontSize: 10,
                                      color: Colors.black87,
                                    ),
                                  ),
                                );
                              }).toList(),
                            ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
            ],
          ),
        ),
      ),
    );
  }

  /// 獲取事件類型圖示
  IconData _getEventIcon(CurrentEventsType type) {
    switch (type) {
      case CurrentEventsType.horary:
        return Icons.help_center;
      case CurrentEventsType.solstice:
        return Icons.wb_sunny;
      case CurrentEventsType.eclipse:
        return Icons.brightness_2;
    }
  }

  /// 格式化日期
  String _formatDate(DateTime date) {
    return '${date.year}/${date.month.toString().padLeft(2, '0')}/${date.day.toString().padLeft(2, '0')}';
  }

  /// 開啟事件詳情
  void _openEventDetail(CurrentEventsData event) {
    logger.d('開啟事件詳情：${event.title}');
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CurrentEventsDetailPage(event: event),
      ),
    );
  }

  /// 顯示說明對話框
  void _showHelpDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('時事分析說明'),
        content: const SingleChildScrollView(
          child: Text(
            '時事分析是占星學中用來分析當前宇宙能量和重要天文事件的方法：\n\n'
            '🔮 卜卦分析：針對特定問題的占星指引\n'
            '• 適合問感情、工作、決策等具體問題\n'
            '• 需要明確和真誠的問題\n\n'
            '🌸 二分二至盤：季節轉換的能量分析\n'
            '• 春分、夏至、秋分、冬至四個重要時刻\n'
            '• 了解自然節律對個人的影響\n\n'
            '🌑 日月蝕盤：重要的宇宙轉換時刻\n'
            '• 日蝕代表新開始和機會\n'
            '• 月蝕代表結束、釋放和轉化\n\n'
            '點擊任何事件可以查看詳細說明和分析。',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('了解'),
          ),
        ],
      ),
    );
  }
}
