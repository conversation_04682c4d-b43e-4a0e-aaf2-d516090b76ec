import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:geocoding/geocoding.dart';
import 'package:geolocator/geolocator.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';

import '../../../astreal.dart';
import '../../../data/services/api/astrology_divination_service.dart';
import '../../../data/services/api/yijing_divination_service.dart';
import '../../../shared/widgets/common/date_time_picker_bottom_sheet.dart';
import '../../../shared/widgets/common/responsive_wrapper.dart';
import '../../viewmodels/horary_chart_viewmodel.dart';
import '../ai_interpretation_result_page.dart';
import 'divination_records_page.dart';
import 'yijing_result_page.dart';

class DivinationAnalysisPage extends StatefulWidget {
  final String title;
  final String? description;

  const DivinationAnalysisPage(
      {super.key, required this.title, this.description});

  @override
  State<DivinationAnalysisPage> createState() => _DivinationAnalysisPageState();
}

class _DivinationAnalysisPageState extends State<DivinationAnalysisPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final _questionController = TextEditingController();
  final _locationController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  DateTime _selectedDateTime = DateTime.now();
  bool _isPerformingDivination = false;
  bool _showChartResult = false;

  // 占星卜卦 ViewModel
  late HoraryChartViewModel _horaryViewModel;

  @override
  void initState() {
    super.initState();
    // 根據發佈模式決定標籤數量：發佈模式只顯示占星卜卦，開發模式顯示兩個
    final tabCount = kDebugMode ? 2 : 1;
    _tabController = TabController(length: tabCount, vsync: this);
    _locationController.text = '台北市'; // 預設地點
    _horaryViewModel = HoraryChartViewModel();
  }

  /// 選擇日期時間
  Future<void> _selectDateTime() async {
    final selectedDateTime = await DateTimePickerBottomSheet.show(
        context: context,
        initialDateTime: _selectedDateTime,
        title: '選擇提問時間',
        minDate: DateTime(1900, 1, 1),
        maxDate: DateTime(2100, 12, 31),
        defaultDateTime: _selectedDateTime);

    if (selectedDateTime != null) {
      setState(() {
        _selectedDateTime = selectedDateTime;
      });
    }
  }

  /// 提交占星卜卦
  Future<void> _submitHoraryChart() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // 驗證參數
    if (!AstrologyDivinationService.validateDivinationParams(
      question: _questionController.text,
      location: _locationController.text,
      questionTime: _selectedDateTime,
    )) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('請檢查輸入的資訊是否正確')),
      );
      return;
    }

    setState(() {
      _isPerformingDivination = true;
    });

    // 顯示加載指示器
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return const Center(
          child: CircularProgressIndicator(),
        );
      },
    );

    try {
      // 使用新的占星卜卦服務
      final chartData = await AstrologyDivinationService.performDivination(
        questionTime: _selectedDateTime,
        location: _locationController.text,
        question: _questionController.text,
      );

      // 更新 ViewModel 的數據
      _horaryViewModel.setChartData(chartData);

      // 關閉加載指示器
      if (mounted) {
        Navigator.of(context).pop();
      }

      // 顯示星盤結果
      if (mounted) {
        setState(() {
          _showChartResult = true;
        });
      }
    } catch (e) {
      logger.e('占星卜卦時出錯: $e');
      // 關閉加載指示器
      if (mounted) {
        Navigator.of(context).pop();
      }

      // 顯示錯誤訊息
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('卜卦時出錯: $e')),
        );
      }
    } finally {
      setState(() {
        _isPerformingDivination = false;
      });
    }
  }

  /// 執行周易卜卦
  Future<void> _performDivination() async {
    final question = _questionController.text.trim();
    if (question.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('請輸入您的問題')),
      );
      return;
    }

    setState(() {
      _isPerformingDivination = true;
    });

    // 顯示載入對話框
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('正在進行周易卜卦...'),
            ],
          ),
        );
      },
    );

    try {
      // 執行周易卜卦
      final result = YijingDivinationService.performDivination(question);

      // 關閉載入對話框
      if (mounted) {
        Navigator.of(context).pop();
      }

      // 導航到結果頁面
      if (mounted) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => YijingResultPage(result: result),
          ),
        );
      }
    } catch (e) {
      // 關閉載入對話框
      logger.e('占星卜卦時出錯: $e');
      if (mounted) {
        Navigator.of(context).pop();
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('卜卦時出錯: $e')),
        );
      }
    } finally {
      setState(() {
        _isPerformingDivination = false;
      });
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    _questionController.dispose();
    _locationController.dispose();
    super.dispose();
  }

  /// 格式化日期時間
  String _formatDateTime(DateTime dateTime) {
    return DateFormat('yyyy-MM-dd HH:mm').format(dateTime);
  }

  /// 獲取當前位置
  Future<void> _getCurrentLocation() async {
    try {
      // 顯示加載指示器
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        },
      );

      // 檢查位置權限
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          // 如果用戶拒絕了權限請求，則顯示提示
          if (mounted) {
            Navigator.of(context).pop(); // 關閉加載指示器
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('無法獲取位置權限')),
            );
          }
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        // 如果用戶永久拒絕了權限請求，則顯示提示
        if (mounted) {
          Navigator.of(context).pop(); // 關閉加載指示器
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('位置權限被永久拒絕，請在設定中開啟')),
          );
        }
        return;
      }

      // 獲取當前位置
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      // 使用 geocoding 套件將經緯度轉換為地址
      List<Placemark> placemarks = await placemarkFromCoordinates(
        position.latitude,
        position.longitude,
      );

      // 關閉加載指示器
      if (mounted) {
        Navigator.of(context).pop();
      }

      // 如果有地址信息，則更新地點輸入欄位
      if (placemarks.isNotEmpty) {
        Placemark place = placemarks.first;
        String address = '';

        // 根據不同的地區格式化地址
        if (place.locality != null && place.locality!.isNotEmpty) {
          address = place.locality!; // 城市
        } else if (place.administrativeArea != null &&
            place.administrativeArea!.isNotEmpty) {
          address = place.administrativeArea!; // 省/州
        }

        // 如果地址為空，則使用經緯度
        if (address.isEmpty) {
          address = '${position.latitude}, ${position.longitude}';
        }

        // 更新地點輸入欄位
        setState(() {
          _locationController.text = address;
        });
      } else {
        // 如果沒有地址信息，則使用經緯度
        setState(() {
          _locationController.text =
              '${position.latitude}, ${position.longitude}';
        });
      }
    } catch (e) {
      print('獲取位置時出錯: $e');
      // 關閉加載指示器
      if (mounted) {
        Navigator.of(context).pop();
      }

      // 顯示錯誤訊息
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('獲取位置時出錯: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title),
        actions: [
          // 查看卜卦記錄按鈕
          IconButton(
            icon: const Icon(Icons.history),
            tooltip: '查看卜卦記錄',
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const DivinationRecordsPage(),
                ),
              );
            },
          ),
        ],
        // bottom: TabBar(
        //   controller: _tabController,
        //   labelColor: Colors.white,
        //   unselectedLabelColor: Colors.white.withOpacity(0.7),
        //   indicatorColor: AppColors.solarAmber,
        //   indicatorWeight: 3,
        //   indicatorSize: TabBarIndicatorSize.tab,
        //   labelStyle: const TextStyle(
        //     fontSize: 14,
        //     fontWeight: FontWeight.bold,
        //   ),
        //   unselectedLabelStyle: const TextStyle(
        //     fontSize: 14,
        //     fontWeight: FontWeight.normal,
        //   ),
        //   isScrollable: true,
        //   tabs: [
        //     const Tab(text: '占星卜卦'),
        //     // 只在開發模式下顯示周易卜卦
        //     if (kDebugMode) const Tab(text: '周易卜卦'),
        //   ],
        // ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildAstrologyDivination(),
          // 只在開發模式下顯示周易卜卦
          if (kDebugMode) _buildYijingDivination(),
        ],
      ),
    );
  }

  Widget _buildYijingDivination() {
    return SingleChildScrollView(
      child: ResponsivePageWrapper(
        maxWidth: 800.0,
        child: Padding(
          padding: ResponsiveUtils.getResponsivePadding(context),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ResponsiveCardWrapper(
                maxWidth: 700.0,
                child: _buildDivinationCard(
                  '周易卜卦',
                  '通過周易六十四卦解讀您的問題',
                  Icons.change_history_outlined,
                  content: '周易卜卦是中國古代的一種占卜方式，通過生成六爻卦象來解讀問題。\n'
                      '請在心中默念您的問題，然後點擊「開始卜卦」按鈕。系統將隨機生成一個卦象及其變卦，並提供相應的解釋。\n'
                      '卜卦結果僅供參考，最終決定權仍在您手中。',
                ),
              ),
              // const SizedBox(height: 16),
              ResponsiveCardWrapper(
                maxWidth: 700.0,
                child: Card(
                  elevation: 3,
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12)),
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          '您的問題',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 12),
                        TextField(
                          controller: _questionController,
                          decoration: const InputDecoration(
                            hintText: '請輸入您想要解答的問題...',
                            border: OutlineInputBorder(),
                          ),
                          maxLines: 3,
                        ),
                        const SizedBox(height: 16),
                        Center(
                          child: ElevatedButton.icon(
                            onPressed: _isPerformingDivination
                                ? null
                                : _performDivination,
                            icon: const Icon(Icons.auto_awesome),
                            label: Text(
                                _isPerformingDivination ? '卜卦中...' : '開始卜卦'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppColors.royalIndigo,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 24, vertical: 12),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              // const SizedBox(height: 16),
              ResponsiveCardWrapper(
                maxWidth: 700.0,
                child: _buildDivinationTips(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 構建占星卜卦頁面
  Widget _buildAstrologyDivination() {
    return ChangeNotifierProvider.value(
      value: _horaryViewModel,
      child: _showChartResult
          ? _buildChartResultView()
          : _buildAstrologyInputForm(),
    );
  }

  /// 構建占星卜卦輸入表單
  Widget _buildAstrologyInputForm() {
    return SingleChildScrollView(
      child: ResponsivePageWrapper(
        maxWidth: 800.0, // 限制最大寬度
        child: Padding(
          padding: ResponsiveUtils.getResponsivePadding(context),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ResponsiveCardWrapper(
                  maxWidth: 700.0,
                  child: _buildDivinationCard(
                    '占星卜卦',
                    '通過時刻盤解讀您的問題',
                    Icons.auto_awesome,
                    content:
                        AstrologyDivinationService.getDivinationDescription(),
                  ),
                ),
                const SizedBox(height: 16),
                ResponsiveCardWrapper(
                  maxWidth: 700.0,
                  child: StyledCard(
                    elevation: 3,
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            '卜卦資訊',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),

                          // 問題輸入
                          TextFormField(
                            controller: _questionController,
                            decoration: const InputDecoration(
                              labelText: '問題',
                              hintText: '輸入想要解答的問題...',
                              border: OutlineInputBorder(),
                            ),
                            maxLines: 3,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return '輸入問題';
                              }
                              return null;
                            },
                          ),
                          const SizedBox(height: 16),

                          // 日期時間選擇
                          InkWell(
                            onTap: _selectDateTime,
                            child: InputDecorator(
                              decoration: const InputDecoration(
                                labelText: '提問時間',
                                border: OutlineInputBorder(),
                                suffixIcon: Icon(Icons.calendar_today),
                              ),
                              child: Text(
                                _formatDateTime(_selectedDateTime),
                              ),
                            ),
                          ),
                          const SizedBox(height: 16),

                          // 地點輸入
                          TextFormField(
                            controller: _locationController,
                            decoration: InputDecoration(
                              labelText: '提問地點',
                              hintText: '輸入提問時所在的地點',
                              border: const OutlineInputBorder(),
                              suffixIcon: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  // 清除按鈕
                                  IconButton(
                                    icon: const Icon(Icons.clear),
                                    onPressed: () {
                                      setState(() {
                                        _locationController.clear();
                                      });
                                    },
                                    tooltip: '清除',
                                  ),
                                  // 獲取當前位置按鈕
                                  IconButton(
                                    icon: const Icon(Icons.my_location),
                                    onPressed: _getCurrentLocation,
                                    tooltip: '獲取當前位置',
                                  ),
                                ],
                              ),
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return '請輸入地點';
                              }
                              return null;
                            },
                          ),
                          const SizedBox(height: 16),

                          // 提交按鈕
                          Center(
                            child: ElevatedButton.icon(
                              onPressed: _isPerformingDivination
                                  ? null
                                  : _submitHoraryChart,
                              icon: const Icon(Icons.auto_awesome),
                              label: Text(
                                  _isPerformingDivination ? '卜卦中...' : '開始卜卦'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: AppColors.royalIndigo,
                                foregroundColor: Colors.white,
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 24, vertical: 12),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                // const SizedBox(height: 16),
                ResponsiveCardWrapper(
                  maxWidth: 700.0,
                  child: _buildAstrologyDivinationTips(),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildChartResultView() {
    if (_horaryViewModel.chartData == null) {
      return const Center(child: Text('無卜卦數據'));
    }

    // 導航到 AI 解讀結果頁面
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => AIInterpretationResultPage(
            chartData: _horaryViewModel.chartData!,
            interpretationTitle: '占星卜卦結果',
            subtitle: '問題：${_questionController.text}',
            suggestedQuestions: [
              _buildHoraryInterpretationPrompt(),
            ],
            autoExecuteFirstQuestion: true,
          ),
        ),
      ).then((_) {
        // 當從結果頁面返回時，重置狀態
        setState(() {
          _showChartResult = false;
        });
      });
    });

    // 顯示加載指示器，直到導航完成
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text('正在準備卜卦結果...'),
        ],
      ),
    );
  }

  Widget _buildDivinationCard(String title, String subtitle, IconData icon,
      {required String content, Color? color}) {
    return Card(
      elevation: 3,
      margin: const EdgeInsets.only(bottom: 16),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CircleAvatar(
                  backgroundColor: color ?? AppColors.solarAmber,
                  radius: 24,
                  child: Icon(icon, color: Colors.white, size: 24),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        subtitle,
                        style: const TextStyle(
                            fontSize: 14, color: Colors.black87),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 4),
            const Divider(),
            const SizedBox(height: 4),
            Text(
              content,
              style: const TextStyle(fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }

  /// 構建占星卜卦解讀提示詞
  String _buildHoraryInterpretationPrompt() {
    final question = _questionController.text.trim();
    final location = _locationController.text.trim();
    final dateTime = _formatDateTime(_selectedDateTime);

    return '''
請為以下占星卜卦提供專業的解讀分析：

卜卦問題：${question.isNotEmpty ? question : '未記錄問題'}
卜卦時間：$dateTime
卜卦地點：$location

請根據占星卜卦的傳統原則，提供以下內容的解讀：

1. 問題分析
- 分析問題的性質和類型
- 確定相關的宮位和行星
- 評估問題的可解答性

2. 星盤解讀
- 上升點和宮位系統分析
- 主要行星位置和相位
- 關鍵的占星因素和配置

3. 卜卦判斷
- 根據傳統卜卦規則進行分析
- 考慮行星的力量和狀態
- 評估相位的影響和意義
- 分析月亮的狀態和相位

4. 結論與建議
- 對問題的明確答案
- 時間預測（如適用）
- 具體的行動建議
- 需要注意的事項

5. 注意事項
- 可能的變數和影響因素
- 需要關注的時間點
- 卜卦的有效期限

請提供詳細、實用的解讀，並結合傳統占星卜卦的技術來分析。
''';
  }

  /// 構建占星卜卦提示
  Widget _buildAstrologyDivinationTips() {
    final tips = AstrologyDivinationService.getDivinationTips();

    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.lightbulb, color: AppColors.solarAmber),
                SizedBox(width: 8),
                Text(
                  '占星卜卦小貼士',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            ...tips.asMap().entries.map((entry) {
              final index = entry.key;
              final tip = entry.value;
              return Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: Text(
                  '${index + 1}. $tip',
                  style: const TextStyle(fontSize: 14),
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  /// 構建周易卜卦提示（保留原有的）
  Widget _buildDivinationTips() {
    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: const Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.lightbulb, color: AppColors.solarAmber),
                SizedBox(width: 8),
                Text(
                  '周易卜卦小貼士',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            SizedBox(height: 12),
            Text(
              '1. 卜卦前保持平靜的心態，專注於您的問題。',
              style: TextStyle(fontSize: 14),
            ),
            SizedBox(height: 8),
            Text(
              '2. 問題應該明確具體，避免過於籠統或包含多個問題。',
              style: TextStyle(fontSize: 14),
            ),
            SizedBox(height: 8),
            Text(
              '3. 同一個問題不宜在短時間內重複卜問，以免干擾結果。',
              style: TextStyle(fontSize: 14),
            ),
            SizedBox(height: 8),
            Text(
              '4. 卜卦結果提供的是參考和啟示，最終決定仍應基於理性思考。',
              style: TextStyle(fontSize: 14),
            ),
            SizedBox(height: 8),
            Text(
              '5. 變爻代表事物的變化趨勢，無變爻則表示情況相對穩定。',
              style: TextStyle(fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }
}
