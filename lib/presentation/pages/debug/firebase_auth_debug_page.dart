import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../../core/utils/logger_utils.dart';
import '../../../data/services/api/ai_api_service.dart';
import '../../../data/services/api/remote_config_service.dart';
import '../../../data/services/api/remote_config_version_service.dart';
import '../../../shared/utils/apple_signin_diagnostic.dart';
import '../../../shared/utils/firebase_auth_debug.dart';
import '../../../shared/utils/firebase_config_checker.dart';
import '../../../shared/utils/firebase_init_diagnostic.dart';
import '../../../shared/utils/google_oauth_config_checker.dart';
import '../../../shared/utils/google_signin_diagnostic.dart';
import '../../../shared/utils/remote_config_debug_helper.dart';
import '../../../shared/utils/remote_config_diagnostic.dart';
import '../../themes/app_theme.dart';

/// Firebase 認證調試頁面
class FirebaseAuthDebugPage extends StatefulWidget {
  const FirebaseAuthDebugPage({super.key});

  @override
  State<FirebaseAuthDebugPage> createState() => _FirebaseAuthDebugPageState();
}

class _FirebaseAuthDebugPageState extends State<FirebaseAuthDebugPage> {
  bool _isLoading = false;
  Map<String, dynamic>? _diagnosticReport;
  Map<String, dynamic>? _googleTestResult;
  Map<String, dynamic>? _appleTestResult;
  Map<String, dynamic>? _googleDiagnostic;
  Map<String, dynamic>? _firebaseConfigCheck;
  Map<String, dynamic>? _googleOAuthCheck;
  Map<String, dynamic>? _firebaseInitDiagnostic;
  Map<String, dynamic>? _appleSignInDiagnostic;
  Map<String, dynamic>? _remoteConfigDiagnostic;

  @override
  void initState() {
    super.initState();
    _generateDiagnosticReport();
  }

  /// 生成診斷報告
  Future<void> _generateDiagnosticReport() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final report = await FirebaseAuthDebug.generateDiagnosticReport();
      setState(() {
        _diagnosticReport = report;
      });
      
      // 打印到控制台
      FirebaseAuthDebug.printDiagnosticReport(report);
    } catch (e) {
      logger.e('生成診斷報告失敗: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('生成診斷報告失敗：$e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 測試 Google 登入
  Future<void> _testGoogleSignIn() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final result = await FirebaseAuthDebug.safeGoogleSignInTest();
      setState(() {
        _googleTestResult = result;
      });
      
      logger.i('Google 登入測試結果: $result');
      
      if (mounted) {
        final message = result['result'] == 'success' 
            ? 'Google 登入測試成功'
            : result['result'] == 'canceled'
                ? 'Google 登入被取消'
                : 'Google 登入測試失敗：${result['error'] ?? 'unknown'}';
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(message),
            backgroundColor: result['result'] == 'success' 
                ? AppColors.successGreen 
                : result['result'] == 'canceled'
                    ? Colors.orange
                    : Colors.red,
          ),
        );
      }
    } catch (e) {
      logger.e('Google 登入測試失敗: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Google 登入測試失敗：$e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 測試 Apple 登入
  Future<void> _testAppleSignIn() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final result = await FirebaseAuthDebug.safeAppleSignInTest();
      setState(() {
        _appleTestResult = result;
      });

      logger.i('Apple 登入測試結果: $result');

      if (mounted) {
        final message = result['result'] == 'success'
            ? 'Apple 登入測試成功'
            : result['result'] == 'unsupported'
                ? 'Apple 登入不支援此平台'
                : result['result'] == 'unavailable'
                    ? 'Apple 登入在此設備不可用'
                    : 'Apple 登入測試失敗：${result['error'] ?? 'unknown'}';

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(message),
            backgroundColor: result['result'] == 'success'
                ? AppColors.successGreen
                : result['result'] == 'unsupported' || result['result'] == 'unavailable'
                    ? Colors.orange
                    : Colors.red,
          ),
        );
      }
    } catch (e) {
      logger.e('Apple 登入測試失敗: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Apple 登入測試失敗：$e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// Google Sign-In 完整診斷
  Future<void> _runGoogleDiagnostic() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final result = await GoogleSignInDiagnostic.runFullDiagnostic();
      setState(() {
        _googleDiagnostic = result;
      });

      // 打印到控制台
      GoogleSignInDiagnostic.printDiagnostic(result);

      if (mounted) {
        final authTest = result['authenticationTest'] as Map<String, dynamic>?;
        final message = authTest?['result'] == 'success'
            ? 'Google 診斷完成，請查看詳細結果'
            : authTest?['result'] == 'canceled'
                ? 'Google 診斷完成（用戶取消登入）'
                : 'Google 診斷完成，發現問題';

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(message),
            backgroundColor: authTest?['result'] == 'success'
                ? AppColors.successGreen
                : authTest?['result'] == 'canceled'
                    ? Colors.orange
                    : Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      logger.e('Google 診斷失敗: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Google 診斷失敗：$e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// Firebase 配置檢查
  Future<void> _checkFirebaseConfig() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final result = await FirebaseConfigChecker.checkFirebaseConfig();
      setState(() {
        _firebaseConfigCheck = result;
      });

      // 打印到控制台
      FirebaseConfigChecker.printDiagnostic(result);

      if (mounted) {
        final init = result['initialization'] as Map<String, dynamic>?;
        final message = init?['status'] == 'initialized'
            ? 'Firebase 配置檢查完成，狀態正常'
            : 'Firebase 配置檢查完成，發現問題';

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(message),
            backgroundColor: init?['status'] == 'initialized'
                ? AppColors.successGreen
                : Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      logger.e('Firebase 配置檢查失敗: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Firebase 配置檢查失敗：$e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// Google OAuth 配置檢查
  Future<void> _checkGoogleOAuthConfig() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final result = await GoogleOAuthConfigChecker.checkGoogleOAuthConfig();
      setState(() {
        _googleOAuthCheck = result;
      });

      // 打印到控制台
      GoogleOAuthConfigChecker.printDiagnostic(result);

      if (mounted) {
        final analysis = result['clientIdAnalysis'] as Map<String, dynamic>?;
        final message = analysis?['status'] == 'correct'
            ? 'Google OAuth 配置檢查完成，配置正確'
            : 'Google OAuth 配置檢查完成，發現問題';

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(message),
            backgroundColor: analysis?['status'] == 'correct'
                ? AppColors.successGreen
                : Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      logger.e('Google OAuth 配置檢查失敗: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Google OAuth 配置檢查失敗：$e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// Firebase 初始化診斷
  Future<void> _runFirebaseInitDiagnostic() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final result = await FirebaseInitDiagnostic.runFullDiagnostic();
      setState(() {
        _firebaseInitDiagnostic = result;
      });

      // 打印到控制台
      FirebaseInitDiagnostic.printDiagnostic(result);

      if (mounted) {
        final initStatus = result['initializationStatus'] as Map<String, dynamic>?;
        final manualInit = result['manualInitialization'] as Map<String, dynamic>?;

        String message;
        Color backgroundColor;

        if (initStatus?['status'] == 'initialized') {
          message = 'Firebase 初始化診斷完成，狀態正常';
          backgroundColor = AppColors.successGreen;
        } else if (manualInit?['result'] == 'success') {
          message = 'Firebase 初始化診斷完成，手動初始化成功';
          backgroundColor = AppColors.successGreen;
        } else {
          message = 'Firebase 初始化診斷完成，發現問題';
          backgroundColor = Colors.red;
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(message),
            backgroundColor: backgroundColor,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      logger.e('Firebase 初始化診斷失敗: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Firebase 初始化診斷失敗：$e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// Apple Sign-In 診斷
  Future<void> _runAppleSignInDiagnostic() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final result = await AppleSignInDiagnostic.runFullDiagnostic();
      setState(() {
        _appleSignInDiagnostic = result;
      });

      // 打印到控制台
      AppleSignInDiagnostic.printDiagnostic(result);

      if (mounted) {
        final signInTest = result['signInTest'] as Map<String, dynamic>?;
        final platformSupport = result['platformSupport'] as Map<String, dynamic>?;

        String message;
        Color backgroundColor;

        if (platformSupport?['appleSignInSupported'] != true) {
          message = 'Apple Sign-In 診斷完成，當前平台不支援';
          backgroundColor = Colors.orange;
        } else if (signInTest?['result'] == 'success') {
          message = 'Apple Sign-In 診斷完成，功能正常';
          backgroundColor = AppColors.successGreen;
        } else if (signInTest?['isError1000'] == true) {
          message = 'Apple Sign-In 診斷完成，檢測到錯誤 1000';
          backgroundColor = Colors.red;
        } else {
          message = 'Apple Sign-In 診斷完成，發現問題';
          backgroundColor = Colors.red;
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(message),
            backgroundColor: backgroundColor,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      logger.e('Apple Sign-In 診斷失敗: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Apple Sign-In 診斷失敗：$e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// Remote Config 診斷
  Future<void> _runRemoteConfigDiagnostic() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final result = await RemoteConfigDiagnostic.runFullDiagnostic();
      setState(() {
        _remoteConfigDiagnostic = result;
      });

      // 打印到控制台
      RemoteConfigDiagnostic.printDiagnostic(result);

      if (mounted) {
        final serviceStatus = result['serviceStatus'] as Map<String, dynamic>?;
        final platformKeys = result['platformKeys'] as Map<String, dynamic>?;

        String message;
        Color backgroundColor;

        if (serviceStatus?['status'] == 'initialized') {
          final validation = platformKeys?['validation'] as Map<String, dynamic>?;
          final hasValidKeys = validation?.values.any((v) =>
              (v as Map<String, dynamic>)['hasValue'] == true) == true;

          if (hasValidKeys) {
            message = 'Remote Config 診斷完成，配置正常';
            backgroundColor = AppColors.successGreen;
          } else {
            message = 'Remote Config 診斷完成，需要設定 API Keys';
            backgroundColor = Colors.orange;
          }
        } else {
          message = 'Remote Config 診斷完成，發現問題';
          backgroundColor = Colors.red;
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(message),
            backgroundColor: backgroundColor,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      logger.e('Remote Config 診斷失敗: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Remote Config 診斷失敗：$e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// OpenAI Key 詳細調試
  Future<void> _debugOpenAIKey() async {
    setState(() {
      _isLoading = true;
    });

    try {
      logger.i('開始 OpenAI Key 詳細調試...');

      // 打印完整調試報告到控制台
      RemoteConfigDebugHelper.printFullDebugReport();

      // 執行詳細調試
      final debugResult = RemoteConfigDebugHelper.debugOpenAIKeyRetrieval();

      // 比較配置
      final comparison = RemoteConfigDebugHelper.compareConfigurations();

      // 強制刷新並重新檢查
      await RemoteConfigDebugHelper.forceRefreshAndCheck();

      if (mounted) {
        // 顯示調試結果對話框
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('OpenAI Key 調試結果'),
            content: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text('平台: ${debugResult['platform']}'),
                  const SizedBox(height: 8),
                  Text('Remote Config 狀態: ${debugResult['remoteConfigStatus']?['isInitialized']}'),
                  const SizedBox(height: 8),
                  Text('實際獲取的 Key: ${debugResult['actualKey']?['isEmpty'] == true ? '空' : '有值'}'),
                  const SizedBox(height: 8),
                  Text('Key 長度: ${debugResult['actualKey']?['length'] ?? 0}'),
                  const SizedBox(height: 8),
                  Text('配置匹配: ${comparison['configMatch'] ?? false}'),
                  const SizedBox(height: 8),
                  Text('iOS Key 匹配: ${comparison['iosKeyComparison']?['match'] ?? false}'),
                  const SizedBox(height: 16),
                  const Text('詳細信息已輸出到控制台，請查看日誌。'),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('確定'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      logger.e('OpenAI Key 調試失敗: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('OpenAI Key 調試失敗：$e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// Remote Config 快速診斷
  void _quickDiagnosis() {
    try {
      logger.i('執行 Remote Config 快速診斷...');
      RemoteConfigService.quickDiagnosis();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('快速診斷已完成，請查看控制台輸出'),
            backgroundColor: Colors.amber,
          ),
        );
      }
    } catch (e) {
      logger.e('快速診斷失敗: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('快速診斷失敗：$e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// 強制刷新 Remote Config
  Future<void> _forceRefreshRemoteConfig() async {
    setState(() {
      _isLoading = true;
    });

    try {
      logger.i('執行強制刷新 Remote Config...');
      final success = await RemoteConfigService.forceRefresh();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(success ? '強制刷新成功，已獲取最新配置' : '強制刷新完成，但配置可能仍為空'),
            backgroundColor: success ? AppColors.successGreen : Colors.orange,
          ),
        );
      }

      // 刷新後重新執行診斷
      RemoteConfigService.quickDiagnosis();

    } catch (e) {
      logger.e('強制刷新失敗: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('強制刷新失敗：$e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 版本檢查診斷
  Future<void> _debugVersionCheck() async {
    setState(() {
      _isLoading = true;
    });

    try {
      logger.i('執行版本檢查診斷...');

      // 獲取版本配置摘要
      final versionSummary = RemoteConfigVersionService.getVersionSummary();
      logger.i('版本配置摘要: ${versionSummary.toString()}');

      // 執行版本檢查
      final checkResult = await RemoteConfigVersionService.checkForUpdates();
      logger.i('版本檢查結果: ${checkResult.toString()}');

      if (mounted) {
        // 顯示診斷結果對話框
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('版本檢查診斷結果'),
            content: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text('當前平台: ${versionSummary['currentPlatform']}'),
                  const SizedBox(height: 8),
                  Text('有版本配置: ${versionSummary['hasVersionConfig']}'),
                  const SizedBox(height: 8),
                  Text('有平台配置: ${versionSummary['hasPlatformConfig']}'),
                  const SizedBox(height: 8),
                  Text('可用平台: ${versionSummary['availablePlatforms']}'),
                  const SizedBox(height: 8),
                  Text('檢查成功: ${checkResult.isSuccess}'),
                  const SizedBox(height: 8),
                  Text('需要更新: ${checkResult.needsUpdate}'),
                  const SizedBox(height: 8),
                  Text('強制更新: ${checkResult.isForceUpdate}'),
                  if (checkResult.errorMessage != null) ...[
                    const SizedBox(height: 8),
                    Text('錯誤訊息: ${checkResult.errorMessage}'),
                  ],
                  const SizedBox(height: 16),
                  const Text('詳細信息已輸出到控制台，請查看日誌。'),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('確定'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      logger.e('版本檢查診斷失敗: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('版本檢查診斷失敗：$e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// AI API Keys 診斷
  Future<void> _debugAIApiKeys() async {
    setState(() {
      _isLoading = true;
    });

    try {
      logger.i('執行 AI API Keys 診斷...');

      // 從 Remote Config 獲取 API Keys 和模型配置
      final remoteOpenAI = RemoteConfigService.getOpenAIKey();
      final remoteGroq = RemoteConfigService.getGroqAIKey();
      final remoteGemini = RemoteConfigService.getGoogleGeminiKey();
      final remoteSelectedModelId = RemoteConfigService.getSelectedModelId();

      // 檢查 AIApiService 中的配置狀態
      final openAIConfigured = await AIApiService.isApiKeyConfigured(AIProvider.openai);
      final groqConfigured = await AIApiService.isApiKeyConfigured(AIProvider.groq);
      final geminiConfigured = await AIApiService.isApiKeyConfigured(AIProvider.gemini);

      // 獲取當前選擇的模型
      final currentSelectedModel = await AIApiService.getSelectedModel();

      logger.i('AI API Keys 診斷結果:');
      logger.i('Remote Config - OpenAI: ${remoteOpenAI.isNotEmpty ? "有(${remoteOpenAI.length}字符)" : "無"}');
      logger.i('Remote Config - Groq: ${remoteGroq.isNotEmpty ? "有(${remoteGroq.length}字符)" : "無"}');
      logger.i('Remote Config - Gemini: ${remoteGemini.isNotEmpty ? "有(${remoteGemini.length}字符)" : "無"}');
      logger.i('Remote Config - 選擇模型: $remoteSelectedModelId');
      logger.i('AIApiService - OpenAI: ${openAIConfigured ? "已配置" : "未配置"}');
      logger.i('AIApiService - Groq: ${groqConfigured ? "已配置" : "未配置"}');
      logger.i('AIApiService - Gemini: ${geminiConfigured ? "已配置" : "未配置"}');
      logger.i('AIApiService - 當前模型: $currentSelectedModel');
      logger.i('模型配置一致性: ${remoteSelectedModelId == currentSelectedModel ? "✅ 一致" : "❌ 不一致"}');

      if (mounted) {
        // 顯示診斷結果對話框
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('AI API Keys 診斷結果'),
            content: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Text('Remote Config 狀態:', style: TextStyle(fontWeight: FontWeight.bold)),
                  const SizedBox(height: 8),
                  _buildKeyStatusRow('OpenAI', remoteOpenAI.isNotEmpty, remoteOpenAI.length),
                  _buildKeyStatusRow('Groq', remoteGroq.isNotEmpty, remoteGroq.length),
                  _buildKeyStatusRow('Gemini', remoteGemini.isNotEmpty, remoteGemini.length),
                  _buildModelStatusRow('選擇模型', remoteSelectedModelId),
                  const SizedBox(height: 16),

                  const Text('AIApiService 配置狀態:', style: TextStyle(fontWeight: FontWeight.bold)),
                  const SizedBox(height: 8),
                  _buildConfigStatusRow('OpenAI', openAIConfigured),
                  _buildConfigStatusRow('Groq', groqConfigured),
                  _buildConfigStatusRow('Gemini', geminiConfigured),
                  _buildModelStatusRow('當前模型', currentSelectedModel),
                  const SizedBox(height: 16),

                  _buildModelConsistencyRow(remoteSelectedModelId, currentSelectedModel),
                  const SizedBox(height: 16),

                  const Text('詳細信息已輸出到控制台，請查看日誌。'),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('確定'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      logger.e('AI API Keys 診斷失敗: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('AI API Keys 診斷失敗：$e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 構建 API Key 狀態行
  Widget _buildKeyStatusRow(String provider, bool hasKey, int keyLength) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Icon(
            hasKey ? Icons.check_circle : Icons.cancel,
            color: hasKey ? Colors.green : Colors.red,
            size: 16,
          ),
          const SizedBox(width: 8),
          Text('$provider: ${hasKey ? "有($keyLength字符)" : "無"}'),
        ],
      ),
    );
  }

  /// 構建配置狀態行
  Widget _buildConfigStatusRow(String provider, bool isConfigured) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Icon(
            isConfigured ? Icons.check_circle : Icons.cancel,
            color: isConfigured ? Colors.green : Colors.red,
            size: 16,
          ),
          const SizedBox(width: 8),
          Text('$provider: ${isConfigured ? "已配置" : "未配置"}'),
        ],
      ),
    );
  }

  /// 構建模型狀態行
  Widget _buildModelStatusRow(String label, String modelId) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          const Icon(
            Icons.smart_toy,
            color: Colors.blue,
            size: 16,
          ),
          const SizedBox(width: 8),
          Text('$label: $modelId'),
        ],
      ),
    );
  }

  /// 構建模型一致性狀態行
  Widget _buildModelConsistencyRow(String remoteModel, String currentModel) {
    final isConsistent = remoteModel == currentModel;
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: isConsistent ? Colors.green.withValues(alpha: 0.1) : Colors.orange.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(4),
        border: Border.all(
          color: isConsistent ? Colors.green : Colors.orange,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            isConsistent ? Icons.check_circle : Icons.warning,
            color: isConsistent ? Colors.green : Colors.orange,
            size: 16,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              isConsistent
                  ? '模型配置一致'
                  : '模型配置不一致 (Remote: $remoteModel, Current: $currentModel)',
              style: TextStyle(
                color: isConsistent ? Colors.green : Colors.orange,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 複製診斷報告到剪貼板
  Future<void> _copyDiagnosticReport() async {
    if (_diagnosticReport == null) return;

    try {
      final jsonString = const JsonEncoder.withIndent('  ').convert(_diagnosticReport);
      await Clipboard.setData(ClipboardData(text: jsonString));
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('診斷報告已複製到剪貼板'),
            backgroundColor: AppColors.successGreen,
          ),
        );
      }
    } catch (e) {
      logger.e('複製診斷報告失敗: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('複製失敗：$e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Firebase 認證調試'),
        backgroundColor: AppColors.royalIndigo,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _isLoading ? null : _generateDiagnosticReport,
            tooltip: '重新生成診斷報告',
          ),
          IconButton(
            icon: const Icon(Icons.copy),
            onPressed: _diagnosticReport == null ? null : _copyDiagnosticReport,
            tooltip: '複製診斷報告',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 測試按鈕
                  _buildTestButtons(),
                  const SizedBox(height: 16),
                  
                  // 診斷報告
                  if (_diagnosticReport != null) ...[
                    _buildDiagnosticReport(),
                    const SizedBox(height: 16),
                  ],
                  
                  // Google 測試結果
                  if (_googleTestResult != null) ...[
                    _buildTestResult('Google 登入測試結果', _googleTestResult!),
                    const SizedBox(height: 16),
                  ],
                  
                  // Apple 測試結果
                  if (_appleTestResult != null) ...[
                    _buildTestResult('Apple 登入測試結果', _appleTestResult!),
                    const SizedBox(height: 16),
                  ],

                  // Google 診斷結果
                  if (_googleDiagnostic != null) ...[
                    _buildTestResult('Google Sign-In 完整診斷', _googleDiagnostic!),
                    const SizedBox(height: 16),
                  ],

                  // Firebase 配置檢查結果
                  if (_firebaseConfigCheck != null) ...[
                    _buildTestResult('Firebase 配置檢查', _firebaseConfigCheck!),
                    const SizedBox(height: 16),
                  ],

                  // Google OAuth 配置檢查結果
                  if (_googleOAuthCheck != null) ...[
                    _buildTestResult('Google OAuth 配置檢查', _googleOAuthCheck!),
                    const SizedBox(height: 16),
                  ],

                  // Firebase 初始化診斷結果
                  if (_firebaseInitDiagnostic != null) ...[
                    _buildTestResult('Firebase 初始化診斷', _firebaseInitDiagnostic!),
                    const SizedBox(height: 16),
                  ],

                  // Apple Sign-In 診斷結果
                  if (_appleSignInDiagnostic != null) ...[
                    _buildTestResult('Apple Sign-In 診斷', _appleSignInDiagnostic!),
                    const SizedBox(height: 16),
                  ],

                  // Remote Config 診斷結果
                  if (_remoteConfigDiagnostic != null) ...[
                    _buildTestResult('Remote Config 診斷', _remoteConfigDiagnostic!),
                    const SizedBox(height: 16),
                  ],
                ],
              ),
            ),
    );
  }

  /// 構建測試按鈕
  Widget _buildTestButtons() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '認證測試',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isLoading ? null : _testGoogleSignIn,
                    icon: const Icon(Icons.g_mobiledata),
                    label: const Text('測試 Google'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isLoading ? null : _testAppleSignIn,
                    icon: const Icon(Icons.apple),
                    label: const Text('測試 Apple'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.black,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),

            // Google 專門診斷
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isLoading ? null : _runGoogleDiagnostic,
                icon: const Icon(Icons.medical_services),
                label: const Text('Google 完整診斷'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.royalIndigo,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
            const SizedBox(height: 12),

            // Firebase 配置檢查
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isLoading ? null : _checkFirebaseConfig,
                icon: const Icon(Icons.settings_applications),
                label: const Text('Firebase 配置檢查'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
            const SizedBox(height: 12),

            // Google OAuth 配置檢查
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isLoading ? null : _checkGoogleOAuthConfig,
                icon: const Icon(Icons.verified_user),
                label: const Text('Google OAuth 配置檢查'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.purple,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
            const SizedBox(height: 12),

            // Firebase 初始化診斷
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isLoading ? null : _runFirebaseInitDiagnostic,
                icon: const Icon(Icons.rocket_launch),
                label: const Text('Firebase 初始化診斷'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.deepOrange,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
            const SizedBox(height: 12),

            // Apple Sign-In 診斷
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isLoading ? null : _runAppleSignInDiagnostic,
                icon: const Icon(Icons.apple),
                label: const Text('Apple Sign-In 診斷'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.black,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
            const SizedBox(height: 12),

            // Remote Config 診斷
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isLoading ? null : _runRemoteConfigDiagnostic,
                icon: const Icon(Icons.cloud_download),
                label: const Text('Remote Config 診斷'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.teal,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
            const SizedBox(height: 12),

            // OpenAI Key 詳細調試
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isLoading ? null : _debugOpenAIKey,
                icon: const Icon(Icons.bug_report),
                label: const Text('OpenAI Key 詳細調試'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.deepPurple,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
            const SizedBox(height: 12),

            // Remote Config 快速診斷
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isLoading ? null : _quickDiagnosis,
                icon: const Icon(Icons.flash_on),
                label: const Text('Remote Config 快速診斷'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.amber,
                  foregroundColor: Colors.black,
                ),
              ),
            ),
            const SizedBox(height: 12),

            // 強制刷新 Remote Config
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isLoading ? null : _forceRefreshRemoteConfig,
                icon: const Icon(Icons.refresh_outlined),
                label: const Text('強制刷新 Remote Config'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
            const SizedBox(height: 12),

            // 版本檢查診斷
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isLoading ? null : _debugVersionCheck,
                icon: const Icon(Icons.system_update_alt),
                label: const Text('版本檢查診斷'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.indigo,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
            const SizedBox(height: 12),

            // AI API Keys 診斷
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isLoading ? null : _debugAIApiKeys,
                icon: const Icon(Icons.smart_toy),
                label: const Text('AI API Keys 診斷'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.purple,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 構建診斷報告
  Widget _buildDiagnosticReport() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '診斷報告',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            _buildJsonDisplay(_diagnosticReport!),
          ],
        ),
      ),
    );
  }

  /// 構建測試結果
  Widget _buildTestResult(String title, Map<String, dynamic> result) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            _buildJsonDisplay(result),
          ],
        ),
      ),
    );
  }

  /// 構建 JSON 顯示
  Widget _buildJsonDisplay(Map<String, dynamic> data) {
    final jsonString = const JsonEncoder.withIndent('  ').convert(data);
    
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: SelectableText(
        jsonString,
        style: const TextStyle(
          fontFamily: 'monospace',
          fontSize: 12,
        ),
      ),
    );
  }
}
