import 'package:flutter/material.dart';

import '../../../astreal.dart';
import '../../../data/models/notification/notification_model.dart';
import '../../../data/services/notification/ios_notification_diagnostic.dart';
import '../../../data/services/notification/notification_diagnostic_service.dart';
import '../../../data/services/notification/notification_management_service.dart';
import '../../../data/services/notification/notification_service.dart';
import '../../../data/services/notification/test_notification_service.dart';
import '../../../shared/widgets/unified_card.dart';

/// 通知測試頁面（僅用於開發和測試）
class NotificationTestPage extends StatefulWidget {
  const NotificationTestPage({super.key});

  @override
  State<NotificationTestPage> createState() => _NotificationTestPageState();
}

class _NotificationTestPageState extends State<NotificationTestPage> {
  bool _isLoading = false;
  String _statusMessage = '';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.scaffoldBackground,
      appBar: AppBar(
        title: const Text('通知系統測試'),
        backgroundColor: AppColors.royalIndigo,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // 狀態顯示
            if (_statusMessage.isNotEmpty)
              UnifiedCard(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Text(
                    _statusMessage,
                    style: const TextStyle(
                      fontSize: 14,
                      color: AppColors.textDark,
                    ),
                  ),
                ),
              ),
            
            const SizedBox(height: 16),
            
            // 系統診斷
            _buildSectionTitle('系統診斷'),
            UnifiedCard(
              child: Column(
                children: [
                  _buildTestButton(
                    '執行完整診斷',
                    Icons.health_and_safety,
                    _runFullDiagnostic,
                  ),
                  const Divider(),
                  _buildTestButton(
                    '嘗試自動修復',
                    Icons.build,
                    _attemptAutoFix,
                  ),
                  const Divider(),
                  _buildTestButton(
                    'iOS 專用診斷',
                    Icons.phone_iphone,
                    _runIOSDiagnostic,
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // 系統狀態檢查
            _buildSectionTitle('系統狀態'),
            UnifiedCard(
              child: Column(
                children: [
                  _buildTestButton(
                    '檢查通知服務狀態',
                    Icons.info,
                    _checkNotificationServiceStatus,
                  ),
                  const Divider(),
                  _buildTestButton(
                    '檢查通知權限',
                    Icons.security,
                    _checkNotificationPermissions,
                  ),
                  const Divider(),
                  _buildTestButton(
                    '獲取 FCM Token',
                    Icons.token,
                    _getFCMToken,
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 16),
            
            // 推播通知測試
            _buildSectionTitle('推播通知測試'),
            UnifiedCard(
              child: Column(
                children: [
                  _buildTestButton(
                    '驗證通知配置',
                    Icons.verified,
                    _validateNotificationSetup,
                  ),
                  const Divider(),
                  _buildTestButton(
                    '獲取發送信息',
                    Icons.info_outline,
                    _getNotificationSendInfo,
                  ),
                  const Divider(),
                  _buildTestButton(
                    '模擬發送通知',
                    Icons.send,
                    _simulateSendNotification,
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // 本地通知測試
            _buildSectionTitle('本地通知測試'),
            UnifiedCard(
              child: Column(
                children: [
                  _buildTestButton(
                    '發送測試通知',
                    Icons.notifications,
                    _sendTestNotification,
                  ),
                  const Divider(),
                  _buildTestButton(
                    '發送高優先級通知',
                    Icons.priority_high,
                    _sendHighPriorityNotification,
                  ),
                  const Divider(),
                  _buildTestButton(
                    '發送緊急通知',
                    Icons.warning,
                    _sendUrgentNotification,
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 16),
            
            // 通知管理測試
            _buildSectionTitle('通知管理測試'),
            UnifiedCard(
              child: Column(
                children: [
                  _buildTestButton(
                    '獲取所有通知',
                    Icons.list,
                    _getAllNotifications,
                  ),
                  const Divider(),
                  _buildTestButton(
                    '獲取未讀通知數量',
                    Icons.mark_email_unread,
                    _getUnreadCount,
                  ),
                  const Divider(),
                  _buildTestButton(
                    '標記所有通知為已讀',
                    Icons.done_all,
                    _markAllAsRead,
                  ),
                  const Divider(),
                  _buildTestButton(
                    '清除所有通知',
                    Icons.clear_all,
                    _clearAllNotifications,
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 16),
            
            // 系統公告測試
            _buildSectionTitle('系統公告測試'),
            UnifiedCard(
              child: Column(
                children: [
                  _buildTestButton(
                    '發送功能更新通知',
                    Icons.new_releases,
                    _sendFeatureUpdateNotification,
                  ),
                  const Divider(),
                  _buildTestButton(
                    '發送占星事件通知',
                    Icons.stars,
                    _sendAstroEventNotification,
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  /// 構建區段標題
  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: AppColors.textDark,
        ),
      ),
    );
  }

  /// 構建測試按鈕
  Widget _buildTestButton(String title, IconData icon, VoidCallback onPressed) {
    return ListTile(
      leading: Icon(icon, color: AppColors.royalIndigo),
      title: Text(title),
      trailing: _isLoading 
          ? const SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(strokeWidth: 2),
            )
          : const Icon(Icons.arrow_forward_ios, size: 16),
      onTap: _isLoading ? null : onPressed,
    );
  }

  /// 設置狀態消息
  void _setStatus(String message) {
    setState(() {
      _statusMessage = message;
      _isLoading = false;
    });
    logger.i('通知測試: $message');
  }

  /// 執行完整診斷
  void _runFullDiagnostic() async {
    _setLoading(true);

    try {
      final results = await NotificationDiagnosticService.runFullDiagnostic();
      final summary = results['summary'] as Map<String, dynamic>?;

      String statusText = '=== 通知系統診斷報告 ===\n\n';

      // 整體狀態
      final overallStatus = summary?['overallStatus'] ?? 'unknown';
      statusText += '整體狀態: ${overallStatus == 'healthy' ? '✅ 正常' : '⚠️ 發現問題'}\n\n';

      // 問題列表
      final issues = summary?['issues'] as List<dynamic>? ?? [];
      if (issues.isNotEmpty) {
        statusText += '發現的問題:\n';
        for (int i = 0; i < issues.length; i++) {
          statusText += '${i + 1}. ${issues[i]}\n';
        }
        statusText += '\n';
      }

      // 建議
      final recommendations = summary?['recommendations'] as List<dynamic>? ?? [];
      if (recommendations.isNotEmpty) {
        statusText += '修復建議:\n';
        for (int i = 0; i < recommendations.length; i++) {
          statusText += '${i + 1}. ${recommendations[i]}\n';
        }
        statusText += '\n';
      }

      // 詳細信息
      final platform = results['platform'] as Map<String, dynamic>?;
      statusText += '平台信息:\n';
      statusText += '- 平台: ${platform?['platform'] ?? 'unknown'}\n';
      statusText += '- 是否為 Web: ${platform?['isWeb'] ?? false}\n';
      statusText += '- 支援背景通知: ${platform?['supportsBackgroundNotifications'] ?? false}\n\n';

      final fcmToken = results['fcmToken'] as Map<String, dynamic>?;
      statusText += 'FCM Token:\n';
      statusText += '- 已獲取: ${fcmToken?['hasToken'] ?? false}\n';
      statusText += '- Token 預覽: ${fcmToken?['tokenPreview'] ?? 'N/A'}\n';

      // iOS 特定信息
      if (fcmToken?['apnsToken'] != null) {
        statusText += '- APNs Token: ${fcmToken!['apnsToken']}\n';
        statusText += '- 已獲取 APNs Token: ${fcmToken['hasApnsToken'] ?? false}\n';
      }

      _setStatus(statusText);
    } catch (e) {
      _setStatus('執行診斷失敗: $e');
    }
  }

  /// 嘗試自動修復
  void _attemptAutoFix() async {
    _setLoading(true);

    try {
      final results = await NotificationDiagnosticService.attemptAutoFix();

      String statusText = '=== 自動修復結果 ===\n\n';

      final permissions = results['permissions'] as Map<String, dynamic>?;
      statusText += '權限修復: ${permissions?['success'] == true ? '✅ 成功' : '❌ 失敗'}\n';
      if (permissions?['status'] != null) {
        statusText += '- 權限狀態: ${permissions!['status']}\n';
      }

      final fcmToken = results['fcmToken'] as Map<String, dynamic>?;
      statusText += 'FCM Token 修復: ${fcmToken?['success'] == true ? '✅ 成功' : '❌ 失敗'}\n';
      if (fcmToken?['hasNewToken'] == true) {
        statusText += '- 新 Token: ${fcmToken!['tokenPreview']}\n';
      }

      final services = results['services'] as Map<String, dynamic>?;
      statusText += '服務修復: ${services?['success'] == true ? '✅ 成功' : '❌ 失敗'}\n';

      _setStatus(statusText);
    } catch (e) {
      _setStatus('自動修復失敗: $e');
    }
  }

  /// 設置加載狀態
  void _setLoading(bool loading) {
    setState(() {
      _isLoading = loading;
    });
  }

  /// 檢查通知服務狀態
  void _checkNotificationServiceStatus() {
    _setLoading(true);
    
    final isInitialized = NotificationService.isInitialized;
    final cachedCount = NotificationService.cachedNotificationCount;
    
    _setStatus('通知服務狀態:\n'
        '- 已初始化: ${isInitialized ? "是" : "否"}\n'
        '- 快取通知數量: $cachedCount');
  }

  /// 檢查通知權限
  void _checkNotificationPermissions() async {
    _setLoading(true);
    
    try {
      final hasPermission = await NotificationService.areNotificationsEnabled();
      _setStatus('通知權限狀態: ${hasPermission ? "已授權" : "未授權"}');
    } catch (e) {
      _setStatus('檢查通知權限失敗: $e');
    }
  }

  /// 獲取 FCM Token
  void _getFCMToken() {
    _setLoading(true);
    
    final token = NotificationService.fcmToken;
    if (token != null) {
      _setStatus('FCM Token: ${token.substring(0, 20)}...');
    } else {
      _setStatus('FCM Token: 未獲取');
    }
  }

  /// 發送測試通知
  void _sendTestNotification() async {
    _setLoading(true);
    
    try {
      final notification = NotificationModel(
        id: 'test_${DateTime.now().millisecondsSinceEpoch}',
        title: '測試通知',
        body: '這是一個測試通知，用於驗證通知系統是否正常工作。',
        type: NotificationType.general,
        priority: NotificationPriority.normal,
        createdAt: DateTime.now(),
      );
      
      // 這裡應該調用本地通知服務來顯示通知
      // 由於我們在測試環境中，直接添加到快取
      await NotificationService.getAllNotifications();
      
      _setStatus('測試通知已發送');
    } catch (e) {
      _setStatus('發送測試通知失敗: $e');
    }
  }

  /// 發送高優先級通知
  void _sendHighPriorityNotification() async {
    _setLoading(true);
    
    try {
      final notification = NotificationModel(
        id: 'high_priority_${DateTime.now().millisecondsSinceEpoch}',
        title: '高優先級通知',
        body: '這是一個高優先級通知，應該會更顯眼地顯示。',
        type: NotificationType.important,
        priority: NotificationPriority.high,
        createdAt: DateTime.now(),
      );
      
      _setStatus('高優先級通知已發送');
    } catch (e) {
      _setStatus('發送高優先級通知失敗: $e');
    }
  }

  /// 發送緊急通知
  void _sendUrgentNotification() async {
    _setLoading(true);
    
    try {
      final notification = NotificationModel(
        id: 'urgent_${DateTime.now().millisecondsSinceEpoch}',
        title: '緊急通知',
        body: '這是一個緊急通知，需要立即關注！',
        type: NotificationType.important,
        priority: NotificationPriority.urgent,
        createdAt: DateTime.now(),
      );
      
      _setStatus('緊急通知已發送');
    } catch (e) {
      _setStatus('發送緊急通知失敗: $e');
    }
  }

  /// 獲取所有通知
  void _getAllNotifications() async {
    _setLoading(true);
    
    try {
      final notifications = await NotificationService.getAllNotifications();
      _setStatus('獲取到 ${notifications.length} 個通知');
    } catch (e) {
      _setStatus('獲取通知失敗: $e');
    }
  }

  /// 獲取未讀通知數量
  void _getUnreadCount() async {
    _setLoading(true);
    
    try {
      final count = await NotificationService.getUnreadNotificationCount();
      _setStatus('未讀通知數量: $count');
    } catch (e) {
      _setStatus('獲取未讀通知數量失敗: $e');
    }
  }

  /// 標記所有通知為已讀
  void _markAllAsRead() async {
    _setLoading(true);
    
    try {
      await NotificationService.markAllNotificationsAsRead();
      _setStatus('所有通知已標記為已讀');
    } catch (e) {
      _setStatus('標記通知為已讀失敗: $e');
    }
  }

  /// 清除所有通知
  void _clearAllNotifications() async {
    _setLoading(true);
    
    try {
      await NotificationService.clearAllNotifications();
      _setStatus('所有通知已清除');
    } catch (e) {
      _setStatus('清除通知失敗: $e');
    }
  }

  /// 發送功能更新通知
  void _sendFeatureUpdateNotification() async {
    _setLoading(true);
    
    try {
      await NotificationManagementService.sendFeatureUpdateNotification(
        title: '新功能發布',
        description: '我們剛剛發布了全新的推播通知系統，現在您可以及時收到重要的占星資訊！',
        version: '1.0.0',
      );
      _setStatus('功能更新通知已發送');
    } catch (e) {
      _setStatus('發送功能更新通知失敗: $e');
    }
  }

  /// 發送占星事件通知
  void _sendAstroEventNotification() async {
    _setLoading(true);

    try {
      await NotificationManagementService.sendAstroEventNotification(
        title: '重要占星事件',
        description: '今晚將有滿月發生，這是一個進行占星分析的絕佳時機。',
        eventDate: DateTime.now().add(const Duration(hours: 2)),
      );
      _setStatus('占星事件通知已發送');
    } catch (e) {
      _setStatus('發送占星事件通知失敗: $e');
    }
  }

  /// 驗證通知配置
  void _validateNotificationSetup() async {
    _setLoading(true);

    try {
      final results = await TestNotificationService.validateNotificationSetup();

      String statusText = '=== 通知配置驗證 ===\n\n';

      final isReady = results['isReady'] as bool? ?? false;
      statusText += '配置狀態: ${isReady ? '✅ 就緒' : '❌ 未就緒'}\n\n';

      final fcmToken = results['fcmToken'] as Map<String, dynamic>?;
      statusText += 'FCM Token:\n';
      statusText += '- 可用: ${fcmToken?['available'] == true ? '是' : '否'}\n';
      if (fcmToken?['preview'] != null) {
        statusText += '- 預覽: ${fcmToken!['preview']}\n';
      }
      statusText += '\n';

      final platform = results['platform'] as Map<String, dynamic>?;
      statusText += '平台信息:\n';
      statusText += '- Android: ${platform?['isAndroid'] == true ? '是' : '否'}\n';
      statusText += '- iOS: ${platform?['isIOS'] == true ? '是' : '否'}\n';
      statusText += '- Web: ${platform?['isWeb'] == true ? '是' : '否'}\n\n';

      final suggestions = results['suggestions'] as List<dynamic>? ?? [];
      if (suggestions.isNotEmpty) {
        statusText += '建議:\n';
        for (int i = 0; i < suggestions.length; i++) {
          statusText += '${i + 1}. ${suggestions[i]}\n';
        }
      }

      _setStatus(statusText);
    } catch (e) {
      _setStatus('驗證通知配置失敗: $e');
    }
  }

  /// 獲取通知發送信息
  void _getNotificationSendInfo() async {
    _setLoading(true);

    try {
      final info = TestNotificationService.getNotificationSendInfo();

      String statusText = '=== 通知發送信息 ===\n\n';

      statusText += 'FCM Token:\n';
      statusText += '- 已獲取: ${info['hasToken'] == true ? '是' : '否'}\n';
      if (info['token'] != null) {
        statusText += '- 預覽: ${info['token']}\n';
      }
      statusText += '\n';

      statusText += '使用 Firebase 控制台測試:\n';
      final instructions = info['instructions'] as List<dynamic>? ?? [];
      for (final instruction in instructions) {
        statusText += '$instruction\n';
      }
      statusText += '\n';

      if (info['fullToken'] != null) {
        statusText += '完整 Token（用於測試）:\n${info['fullToken']}\n\n';
      }

      statusText += 'Firebase 控制台:\n${info['firebaseConsoleUrl']}';

      _setStatus(statusText);
    } catch (e) {
      _setStatus('獲取通知發送信息失敗: $e');
    }
  }

  /// 模擬發送通知
  void _simulateSendNotification() async {
    _setLoading(true);

    try {
      final result = await TestNotificationService.sendTestNotificationToCurrentDevice(
        title: '星真占星測試',
        body: '這是一個測試通知，用於驗證推播通知功能是否正常工作。點擊此通知應該會導航到通知中心頁面。',
        data: {
          'type': 'test',
          'actionUrl': '/notifications',
        },
      );

      String statusText = '=== 模擬發送結果 ===\n\n';
      statusText += '發送狀態: ${result['success'] == true ? '✅ 成功' : '❌ 失敗'}\n';

      if (result['success'] == true) {
        statusText += '消息: ${result['message']}\n';
        statusText += '時間: ${result['timestamp']}\n';
        if (result['note'] != null) {
          statusText += '注意: ${result['note']}\n';
        }
      } else {
        statusText += '錯誤: ${result['error']}\n';
      }

      _setStatus(statusText);
    } catch (e) {
      _setStatus('模擬發送通知失敗: $e');
    }
  }

  /// 運行 iOS 專用診斷
  void _runIOSDiagnostic() async {
    _setLoading(true);

    try {
      final results = await IOSNotificationDiagnostic.runIOSDiagnostic();

      String statusText = '=== iOS 通知診斷報告 ===\n\n';

      if (results['error'] != null) {
        statusText += '錯誤: ${results['error']}\n';
        _setStatus(statusText);
        return;
      }

      final summary = results['summary'] as Map<String, dynamic>?;
      final overallStatus = summary?['overallStatus'] ?? 'unknown';
      statusText += '整體狀態: ${overallStatus == 'healthy' ? '✅ 正常' : '⚠️ 發現問題'}\n\n';

      // 權限狀態
      final permissions = results['permissions'] as Map<String, dynamic>?;
      statusText += '通知權限:\n';
      statusText += '- 授權狀態: ${permissions?['authorizationStatus'] ?? 'unknown'}\n';
      statusText += '- 已授權: ${permissions?['isAuthorized'] == true ? '是' : '否'}\n';
      statusText += '- Alert: ${permissions?['alert'] ?? 'unknown'}\n';
      statusText += '- Badge: ${permissions?['badge'] ?? 'unknown'}\n';
      statusText += '- Sound: ${permissions?['sound'] ?? 'unknown'}\n\n';

      // APNs Token
      final apnsToken = results['apnsToken'] as Map<String, dynamic>?;
      statusText += 'APNs Token:\n';
      statusText += '- 已獲取: ${apnsToken?['hasToken'] == true ? '是' : '否'}\n';
      if (apnsToken?['tokenPreview'] != null) {
        statusText += '- Token 預覽: ${apnsToken!['tokenPreview']}\n';
      }
      statusText += '\n';

      // FCM Token
      final fcmToken = results['fcmToken'] as Map<String, dynamic>?;
      statusText += 'FCM Token:\n';
      statusText += '- 已獲取: ${fcmToken?['hasToken'] == true ? '是' : '否'}\n';
      if (fcmToken?['tokenPreview'] != null) {
        statusText += '- Token 預覽: ${fcmToken!['tokenPreview']}\n';
      }
      statusText += '\n';

      // 問題和建議
      final issues = summary?['issues'] as List<dynamic>? ?? [];
      if (issues.isNotEmpty) {
        statusText += '發現的問題:\n';
        for (int i = 0; i < issues.length; i++) {
          statusText += '${i + 1}. ${issues[i]}\n';
        }
        statusText += '\n';
      }

      final recommendations = summary?['recommendations'] as List<dynamic>? ?? [];
      if (recommendations.isNotEmpty) {
        statusText += '修復建議:\n';
        for (int i = 0; i < recommendations.length; i++) {
          statusText += '${i + 1}. ${recommendations[i]}\n';
        }
        statusText += '\n';
      }

      final criticalIssues = summary?['criticalIssues'] as List<dynamic>? ?? [];
      if (criticalIssues.isNotEmpty) {
        statusText += '⚠️ 關鍵問題:\n';
        for (int i = 0; i < criticalIssues.length; i++) {
          statusText += '${i + 1}. ${criticalIssues[i]}\n';
        }
      }

      _setStatus(statusText);
    } catch (e) {
      _setStatus('iOS 診斷失敗: $e');
    }
  }
}
