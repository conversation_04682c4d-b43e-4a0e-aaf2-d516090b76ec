import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';

import '../../astreal.dart';
import '../../data/models/user/user_analysis.dart';
import '../../data/models/user/user_birth_data.dart';
import '../../data/services/api/account_deletion_service.dart';
import '../../data/services/api/birth_data_service.dart';
import '../../data/services/api/chart_interpretation_service.dart';
import '../../data/services/api/firebase_auth_service.dart';
import '../../data/services/api/payment_service.dart';
import '../../data/services/user_analysis_service.dart';
import '../../data/services/user_birth_data_service.dart';
import '../../features/astrology/astrology_service.dart';
import 'admin/admin_dashboard_page.dart';
import 'birth_data_form_page.dart';
import 'firebase_login_page.dart';
import 'payment_management_page.dart';
import 'person_selector_page.dart';

/// 用戶資料頁面
class UserProfilePage extends StatefulWidget {
  const UserProfilePage({super.key});

  @override
  State<UserProfilePage> createState() => _UserProfilePageState();
}

class _UserProfilePageState extends State<UserProfilePage> {
  AppUser? _currentUser;
  UserProfile? _userProfile;
  bool _isLoading = false;
  Map<String, dynamic>? _subscriptionSummary;

  // 我的分析相關狀態
  UserBirthData? _selectedBirthData;
  UserAnalysis? _userAnalysis;
  bool _isAnalysisLoading = false;

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  /// 載入用戶數據
  Future<void> _loadUserData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final user = FirebaseAuthService.getCurrentUser();
      final summary = await PaymentService.getSubscriptionSummary();

      // 載入 UserProfile 資料
      UserProfile? userProfile;
      UserBirthData? selectedBirthData;
      UserAnalysis? userAnalysis;

      if (user?.uid != null) {
        userProfile = await UserProfileUnifiedService.getUserById(user!.uid);
        // 載入用戶選中的出生資料
        selectedBirthData = await UserBirthDataService.getSelectedBirthData(user!.uid);
        // 如果有選中的出生資料，載入對應的分析結果
        if (selectedBirthData != null) {
          userAnalysis = await UserAnalysisService.getUserAnalysis(user!.uid, selectedBirthData.id);
        }
      }

      if (mounted) {
        setState(() {
          _currentUser = user;
          _userProfile = userProfile;
          _selectedBirthData = selectedBirthData;
          _userAnalysis = userAnalysis;
          _subscriptionSummary = summary;
          _isLoading = false;
        });
      }
    } catch (e) {
      logger.e('載入用戶數據失敗: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// 登入
  Future<void> _login() async {
    final result = await Navigator.of(context).push<AppUser>(
      MaterialPageRoute(
        builder: (context) => const FirebaseLoginPage(),
      ),
    );

    if (result != null) {
      await _loadUserData();
    }
  }

  /// 登出
  Future<void> _logout() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('確認登出'),
        content: const Text('您確定要登出嗎？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('登出'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await FirebaseAuthService.signOut();
        await _loadUserData();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('已成功登出'),
              backgroundColor: AppColors.successGreen,
            ),
          );
        }
      } catch (e) {
        logger.e('登出失敗: $e');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('登出失敗：$e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  /// 發送電子郵件驗證
  Future<void> _sendEmailVerification() async {
    try {
      await FirebaseAuthService.sendEmailVerification();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('驗證郵件已發送'),
            backgroundColor: AppColors.successGreen,
          ),
        );
      }
    } catch (e) {
      logger.e('發送驗證郵件失敗: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('發送失敗：$e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// 更新用戶資料
  Future<void> _updateProfile() async {
    final displayNameController = TextEditingController(
      text: _currentUser?.displayName ?? '',
    );

    final result = await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('更新資料'),
        content: TextField(
          controller: displayNameController,
          decoration: const InputDecoration(
            labelText: '顯示名稱',
            border: OutlineInputBorder(),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () =>
                Navigator.of(context).pop(displayNameController.text),
            child: const Text('更新'),
          ),
        ],
      ),
    );

    if (result != null && result != _currentUser?.displayName) {
      try {
        await FirebaseAuthService.updateUserProfile(displayName: result);
        await _loadUserData();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('資料更新成功'),
              backgroundColor: AppColors.successGreen,
            ),
          );
        }
      } catch (e) {
        logger.e('更新資料失敗: $e');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('更新失敗：$e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  /// 複製用戶ID到剪貼簿
  Future<void> _copyUserId() async {
    if (_currentUser?.uid != null) {
      try {
        await Clipboard.setData(ClipboardData(text: _currentUser!.uid));
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('用戶 ID 已複製到剪貼簿'),
              backgroundColor: AppColors.successGreen,
              duration: const Duration(seconds: 2),
            ),
          );
        }
      } catch (e) {
        logger.e('複製用戶ID失敗: $e');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('複製失敗：$e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  /// 刪除帳戶
  Future<void> _deleteAccount() async {
    // 第一次確認
    final firstConfirmed = await _showDeleteAccountWarning();
    if (!firstConfirmed) return;

    // 第二次確認（更詳細的警告）
    final secondConfirmed = await _showFinalDeleteConfirmation();
    if (!secondConfirmed) return;

    // 顯示進度對話框
    if (!mounted) return;
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('正在刪除帳戶...'),
            SizedBox(height: 8),
            Text(
              '這可能需要一些時間，請勿關閉應用程式',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
      ),
    );

    try {
      // 使用新的完整刪除服務
      await AccountDeletionService.deleteUserAccount(_currentUser?.uid ?? '');

      // 關閉進度對話框
      if (mounted) {
        Navigator.of(context).pop();

        // 先回到設定頁面，然後推送登入頁面
        Navigator.of(context).popUntil(
            (route) => route.settings.name == '/main' || route.isFirst);

        // 推送登入頁面，這樣用戶登入後可以回到設定頁面
        Navigator.of(context).push(
          MaterialPageRoute(builder: (context) => const FirebaseLoginPage()),
        );

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('帳戶已完全刪除'),
            backgroundColor: AppColors.successGreen,
          ),
        );
      }
    } catch (e) {
      // 關閉進度對話框
      if (mounted) Navigator.of(context).pop();

      logger.e('刪除帳戶失敗: $e');
      if (mounted) {
        _showDeleteErrorDialog(e.toString());
      }
    }
  }

  /// 顯示刪除帳戶警告
  Future<bool> _showDeleteAccountWarning() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.warning, color: Colors.orange),
            SizedBox(width: 8),
            Text('刪除帳戶'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '此操作將會永久刪除：',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            const Text('• 您的帳戶資訊'),
            const Text('• 所有出生資料'),
            const Text('• 雲端備份檔案'),
            const Text('• 購買記錄'),
            const Text('• 使用記錄'),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.red.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Text(
                '⚠️ 此操作無法撤銷！',
                style: TextStyle(
                  color: Colors.red,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.orange),
            child: const Text('繼續'),
          ),
        ],
      ),
    );

    return confirmed ?? false;
  }

  /// 顯示最終刪除確認
  Future<bool> _showFinalDeleteConfirmation() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.delete_forever, color: Colors.red),
            SizedBox(width: 8),
            Text('最終確認'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              '您即將永久刪除您的帳戶。',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.red.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
              ),
              child: const Column(
                children: [
                  Icon(Icons.warning, color: Colors.red, size: 32),
                  SizedBox(height: 8),
                  Text(
                    '這個操作無法撤銷！',
                    style: TextStyle(
                      color: Colors.red,
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  SizedBox(height: 4),
                  Text(
                    '所有資料將被永久刪除',
                    style: TextStyle(color: Colors.red),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('確定刪除'),
          ),
        ],
      ),
    );

    return confirmed ?? false;
  }

  /// 顯示刪除錯誤對話框
  void _showDeleteErrorDialog(String error) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.error, color: Colors.red),
            SizedBox(width: 8),
            Text('刪除失敗'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('刪除帳戶時發生錯誤：'),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                error,
                style: const TextStyle(fontFamily: 'monospace'),
              ),
            ),
            const SizedBox(height: 16),
            Text(
              '請稍後再試，或聯繫客服協助。',
              style: TextStyle(color: Colors.grey.shade600),
            ),
          ],
        ),
        actions: [
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('確定'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('用戶資料'),
        backgroundColor: AppColors.royalIndigo,
        foregroundColor: Colors.white,
        actions: [
          if (_currentUser != null)
            IconButton(
              icon: const Icon(Icons.logout),
              onPressed: _logout,
              tooltip: '登出',
            ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _currentUser == null
              ? _buildLoginPrompt()
              : _buildUserProfile(),
    );
  }

  /// 構建登入提示
  Widget _buildLoginPrompt() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.person_outline,
              size: 80,
              color: Colors.grey,
            ),
            const SizedBox(height: 16),
            const Text(
              '請先登入',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            const Text(
              '登入後可以同步您的支付記錄和設定',
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.grey),
            ),
            const SizedBox(height: 32),
            ElevatedButton(
              onPressed: _login,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.royalIndigo,
                foregroundColor: Colors.white,
                padding:
                    const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
              ),
              child: const Text('立即登入'),
            ),
          ],
        ),
      ),
    );
  }

  /// 構建用戶資料
  Widget _buildUserProfile() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildUserInfoCard(),
          const SizedBox(height: 16),
          _buildMyAnalysisCard(),
          const SizedBox(height: 16),
          if (_currentUser?.isAdmin == true) ...[
            _buildSubscriptionCard(),
            const SizedBox(height: 16),
          ],
          _buildActionButtons(),
        ],
      ),
    );
  }

  /// 構建用戶信息卡片
  Widget _buildUserInfoCard() {
    final profile = _userProfile;
    final user = _currentUser;
    final isAdmin = profile?.isAdmin == true || user?.isAdmin == true;

    return Card(
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            color: Colors.white,
          ),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 標題列
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: AppColors.royalIndigo.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Icon(
                        Icons.person_outline,
                        color: AppColors.royalIndigo,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 12),
                    const Text(
                      '個人資料',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textDark,
                      ),
                    ),
                    const Spacer(),
                    if (isAdmin) ...[
                      _buildActionButton(
                        icon: Icons.edit_outlined,
                        onPressed: _showEditProfileDialog,
                        tooltip: '編輯資料',
                        color: AppColors.royalIndigo,
                      ),
                      const SizedBox(width: 8),
                    ],
                    _buildActionButton(
                      icon: Icons.refresh_outlined,
                      onPressed: _loadUserData,
                      tooltip: '重新載入',
                      color: Colors.grey[600]!,
                    ),
                  ],
                ),
                const SizedBox(height: 20),

                // 用戶頭像和基本資訊
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.grey[50],
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.grey[200]!),
                  ),
                  child: Row(
                    children: [
                      Container(
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color:
                                  AppColors.royalIndigo.withValues(alpha: 0.2),
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: CircleAvatar(
                          radius: 35,
                          backgroundColor:
                              AppColors.royalIndigo.withValues(alpha: 0.1),
                          backgroundImage: user?.photoURL != null
                              ? NetworkImage(user!.photoURL!)
                              : null,
                          child: user?.photoURL == null
                              ? Text(
                                  _getInitialLetter(),
                                  style: const TextStyle(
                                    fontSize: 28,
                                    fontWeight: FontWeight.bold,
                                    color: AppColors.royalIndigo,
                                  ),
                                )
                              : null,
                        ),
                      ),
                      const SizedBox(width: 20),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              profile?.displayName ??
                                  user?.displayName ??
                                  '未設置名稱',
                              style: const TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                                color: AppColors.textDark,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              profile?.email ?? user?.email ?? '未設置郵件',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey[600],
                              ),
                            ),
                            const SizedBox(height: 8),
                            // 用戶類型標籤
                            Wrap(
                              spacing: 6,
                              runSpacing: 4,
                              children: [
                                if (profile?.isAnonymous == true ||
                                    user?.isAnonymous == true)
                                  _buildModernStatusChip(
                                      '匿名用戶', Colors.orange, Icons.person_off),
                                if (profile?.isAdmin == true ||
                                    user?.isAdmin == true)
                                  _buildModernStatusChip(
                                      '管理者',
                                      AppColors.royalIndigo,
                                      Icons.admin_panel_settings),
                                if (profile?.emailVerified == true ||
                                    user?.emailVerified == true)
                                  _buildModernStatusChip(
                                      '已驗證', Colors.green, Icons.verified),
                                if (profile?.profileCompleted == true)
                                  _buildModernStatusChip(
                                      '資料完整', Colors.blue, Icons.check_circle),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 24),

                // 詳細資訊
                if (profile != null) ...[
                  // 一般用戶資訊
                  _buildModernInfoSection(
                    '基本資訊',
                    Icons.person_outline,
                    [
                      _buildModernInfoRow(
                          '顯示名稱', profile.displayName, Icons.badge),
                      _buildModernInfoRow(
                          '電子郵件', profile.email ?? '未設置', Icons.email_outlined),
                      _buildModernInfoRow(
                          '可用解讀次數',
                          '${profile.interpretationCredits} 次',
                          Icons.stars_outlined),
                      _buildCopyableInfoRow(
                          '用戶 ID', profile.userId, _copyUserId),
                      if (isAdmin) ...[
                        _buildModernInfoRow('用戶類型', profile.userTypeDescription,
                            Icons.category_outlined),
                        _buildModernInfoRow(
                            '資料完整度',
                            profile.profileCompleted ? '已完成' : '未完成',
                            Icons.checklist),
                      ],
                    ],
                    isExpanded: true,
                  ),

                  if (isAdmin) ...[
                    const SizedBox(height: 16),
                    _buildModernInfoSection(
                      '使用統計',
                      Icons.analytics_outlined,
                      [
                        _buildModernInfoRow(
                            '登入次數', '${profile.loginCount} 次', Icons.login),
                        _buildModernInfoRow(
                            '是否新用戶',
                            profile.isNewUser ? '是' : '否',
                            Icons.new_releases_outlined),
                        _buildModernInfoRow('使用平台', profile.platform ?? '未知',
                            Icons.devices_outlined),
                        _buildModernInfoRow(
                            '最後登入 IP',
                            profile.lastLoginIp ?? '未記錄',
                            Icons.location_on_outlined),
                      ],
                      isExpanded: true,
                    ),
                  ],
                  const SizedBox(height: 16),
                  _buildModernInfoSection(
                    '時間記錄',
                    Icons.schedule_outlined,
                    [
                      _buildModernInfoRow(
                          '註冊時間',
                          _formatDateTime(profile.createdAt),
                          Icons.person_add_outlined),
                      _buildModernInfoRow(
                          '最後更新',
                          _formatDateTime(profile.updatedAt),
                          Icons.update_outlined),
                      // _buildModernInfoRow(
                      //     '最後登入',
                      //     _formatDateTime(profile.lastLoginAt),
                      //     Icons.access_time_outlined),
                      // if (isAdmin) ...[
                      //   _buildModernInfoRow(
                      //       '點數更新時間',
                      //       _formatDateTime(profile.creditsLastUpdated),
                      //       Icons.schedule_outlined),
                      // ],
                    ],
                    isExpanded: true,
                  ),
                ] else ...[
                  // 如果沒有 UserProfile 資料，顯示基本的 AppUser 資料
                  _buildInfoRow('用戶 ID', user?.uid ?? ''),
                  _buildInfoRow('顯示名稱', user?.displayName ?? '未設置'),
                  _buildInfoRow('電子郵件', user?.email ?? '未設置'),
                  _buildInfoRow(
                      '電子郵件驗證', user?.emailVerified == true ? '已驗證' : '未驗證'),
                  _buildInfoRow(
                      '註冊時間',
                      user?.createdAt != null
                          ? _formatDateTime(user!.createdAt!)
                          : '未知'),
                  const SizedBox(height: 8),
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.orange.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                          color: Colors.orange.withValues(alpha: 0.3)),
                    ),
                    child: Row(
                      children: [
                        const Icon(Icons.warning, color: Colors.orange),
                        const SizedBox(width: 8),
                        const Expanded(
                          child: Text(
                            '未找到完整的用戶資料，可能需要重新登入或聯繫管理員',
                            style: TextStyle(color: Colors.orange),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],

                // 操作按鈕
                if (user?.emailVerified == false && user?.email != null) ...[
                  const SizedBox(height: 16),
                  TextButton.icon(
                    onPressed: _sendEmailVerification,
                    icon: const Icon(Icons.email),
                    label: const Text('發送驗證郵件'),
                  ),
                ],
              ],
            ),
          ),
        ));
  }

  /// 構建訂閱信息卡片
  Widget _buildSubscriptionCard() {
    if (_subscriptionSummary == null) return const SizedBox.shrink();

    final isPremium = _subscriptionSummary!['isPremium'] as bool;
    final remainingTrials = _subscriptionSummary!['remainingTrials'] as int;
    final remainingSinglePurchases =
        _subscriptionSummary!['remainingSinglePurchases'] as int;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  isPremium ? Icons.star : Icons.star_border,
                  color: isPremium ? AppColors.solarAmber : Colors.grey,
                ),
                const SizedBox(width: 8),
                const Text(
                  '訂閱狀態',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () {
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => const PaymentManagementPage(),
                      ),
                    );
                  },
                  child: const Text('管理'),
                ),
              ],
            ),
            const SizedBox(height: 8),
            _buildInfoRow('付費會員', isPremium ? '是' : '否'),
            _buildInfoRow('免費試用', '$remainingTrials 次'),
            _buildInfoRow('單次購買', '$remainingSinglePurchases 次'),
          ],
        ),
      ),
    );
  }

  /// 構建操作按鈕
  Widget _buildActionButtons() {
    return Column(
      children: [
        // 管理者專用功能
        if (_currentUser?.isAdmin == true) ...[
          ListTile(
            leading: const Icon(Icons.admin_panel_settings,
                color: AppColors.royalIndigo),
            title: const Text(
              '管理後台',
              style: TextStyle(
                color: AppColors.royalIndigo,
                fontWeight: FontWeight.bold,
              ),
            ),
            trailing:
                const Icon(Icons.chevron_right, color: AppColors.royalIndigo),
            onTap: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const AdminDashboardPage(),
                ),
              );
            },
          ),
          const Divider(),
          ListTile(
            leading: const Icon(Icons.payment),
            title: const Text('支付管理'),
            trailing: const Icon(Icons.chevron_right),
            onTap: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const PaymentManagementPage(),
                ),
              );
            },
          ),
          const Divider(),
        ],

        ListTile(
          leading: const Icon(Icons.delete_forever, color: Colors.red),
          title: const Text('刪除帳戶', style: TextStyle(color: Colors.red)),
          trailing: const Icon(Icons.chevron_right),
          onTap: _deleteAccount,
        ),
      ],
    );
  }

  /// 構建信息行
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(color: Colors.grey),
            ),
          ),
        ],
      ),
    );
  }

  /// 構建可複製的信息行
  Widget _buildCopyableInfoRow(
      String label, String value, VoidCallback onCopy) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: GestureDetector(
              onTap: value.isNotEmpty ? onCopy : null,
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 2, horizontal: 4),
                // decoration: value.isNotEmpty
                //     ? BoxDecoration(
                //         color: Colors.grey.withValues(alpha: 0.1),
                //         borderRadius: BorderRadius.circular(4),
                //         border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
                //       )
                //     : null,
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        value.isNotEmpty ? value : '未設置',
                        style: TextStyle(
                          color: value.isNotEmpty
                              ? AppColors.royalIndigo
                              : Colors.grey,
                          fontSize: 12,
                          fontFamily: 'monospace', // 使用等寬字體顯示ID
                        ),
                      ),
                    ),
                    if (value.isNotEmpty) ...[
                      const SizedBox(width: 8),
                      Icon(
                        Icons.copy,
                        size: 16,
                        color: AppColors.royalIndigo.withValues(alpha: 0.7),
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 安全地獲取用戶初始字母
  String _getInitialLetter() {
    // 優先從 UserProfile 獲取
    final profileDisplayName = _userProfile?.displayName;
    if (profileDisplayName != null && profileDisplayName.isNotEmpty) {
      return profileDisplayName.substring(0, 1).toUpperCase();
    }

    // 嘗試從 AppUser displayName 獲取
    final displayName = _currentUser?.displayName;
    if (displayName != null && displayName.isNotEmpty) {
      return displayName.substring(0, 1).toUpperCase();
    }

    // 嘗試從 email 獲取
    final email = _userProfile?.email ?? _currentUser?.email;
    if (email != null && email.isNotEmpty) {
      return email.substring(0, 1).toUpperCase();
    }

    // 如果是匿名用戶，返回 'A'
    if (_userProfile?.isAnonymous == true ||
        _currentUser?.isAnonymous == true) {
      return 'A';
    }

    // 默認返回 'U'
    return 'U';
  }

  /// 構建現代化狀態標籤
  Widget _buildModernStatusChip(String label, Color color, IconData icon) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 14,
            color: color,
          ),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              color: color,
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  /// 構建操作按鈕
  Widget _buildActionButton({
    required IconData icon,
    required VoidCallback onPressed,
    required String tooltip,
    required Color color,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(10),
      ),
      child: IconButton(
        icon: Icon(icon, color: color, size: 20),
        onPressed: onPressed,
        tooltip: tooltip,
        padding: const EdgeInsets.all(8),
        constraints: const BoxConstraints(minWidth: 36, minHeight: 36),
      ),
    );
  }

  /// 構建現代化資訊區塊
  Widget _buildModernInfoSection(
    String title,
    IconData icon,
    List<Widget> children, {
    bool isExpanded = true,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Theme(
        data: Theme.of(context).copyWith(dividerColor: Colors.transparent),
        child: ExpansionTile(
          initiallyExpanded: isExpanded,
          leading: Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: AppColors.royalIndigo.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: AppColors.royalIndigo,
              size: 20,
            ),
          ),
          title: Text(
            title,
            style: const TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 16,
              color: AppColors.textDark,
            ),
          ),
          children: [
            Container(
              padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
              child: Column(
                children: children,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 構建現代化資訊行
  Widget _buildModernInfoRow(String label, String value, IconData icon) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Row(
        children: [
          Icon(
            icon,
            size: 18,
            color: AppColors.royalIndigo,
          ),
          const SizedBox(width: 12),
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.grey[700],
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: AppColors.textDark,
              ),
              textAlign: TextAlign.end,
            ),
          ),
        ],
      ),
    );
  }

  /// 格式化日期時間
  String _formatDateTime(DateTime dateTime) {
    return DateFormat('yyyy-MM-dd HH:mm:ss').format(dateTime);
  }

  /// 顯示編輯用戶資料對話框
  Future<void> _showEditProfileDialog() async {
    final profile = _userProfile;
    if (profile == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('無法載入用戶資料')),
      );
      return;
    }

    final displayNameController =
        TextEditingController(text: profile.displayName);
    final platformController =
        TextEditingController(text: profile.platform ?? '');
    final interpretationCreditsController =
        TextEditingController(text: profile.interpretationCredits.toString());
    bool profileCompleted = profile.profileCompleted;
    int interpretationCredits = profile.interpretationCredits;

    final result = await showDialog<bool>(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('編輯用戶資料'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: displayNameController,
                  decoration: const InputDecoration(
                    labelText: '顯示名稱',
                    border: OutlineInputBorder(),
                  ),
                ),
                // const SizedBox(height: 16),
                // TextField(
                //   controller: platformController,
                //   decoration: const InputDecoration(
                //     labelText: '平台',
                //     border: OutlineInputBorder(),
                //     hintText: 'web, android, ios, etc.',
                //   ),
                // ),
                // const SizedBox(height: 16),
                // TextField(
                //   controller: interpretationCreditsController,
                //   decoration: const InputDecoration(
                //     labelText: '可用解讀次數',
                //     border: OutlineInputBorder(),
                //   ),
                //   keyboardType: TextInputType.number,
                //   onChanged: (value) {
                //     interpretationCredits =
                //         int.tryParse(value) ?? interpretationCredits;
                //   },
                // ),
                // const SizedBox(height: 16),
                // CheckboxListTile(
                //   title: const Text('資料完整'),
                //   value: profileCompleted,
                //   onChanged: (value) {
                //     setState(() {
                //       profileCompleted = value ?? false;
                //     });
                //   },
                // ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('取消'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('保存'),
            ),
          ],
        ),
      ),
    );

    if (result == true) {
      await _updateUserProfile(
        displayName: displayNameController.text,
        platform:
            platformController.text.isEmpty ? null : platformController.text,
        profileCompleted: profileCompleted,
        interpretationCredits: interpretationCredits,
      );
    }
  }

  /// 更新用戶資料
  Future<void> _updateUserProfile({
    String? displayName,
    String? platform,
    bool? profileCompleted,
    int? interpretationCredits,
  }) async {
    final profile = _userProfile;
    if (profile == null) return;

    try {
      final updatedProfile = profile.copyWith(
        displayName: displayName,
        platform: platform,
        profileCompleted: profileCompleted,
        interpretationCredits: interpretationCredits,
        updatedAt: DateTime.now(),
      );

      await UserProfileUnifiedService.updateUser(
          profile.userId, updatedProfile);

      // 重新載入資料
      await _loadUserData();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('用戶資料更新成功')),
        );
      }
    } catch (e) {
      logger.e('更新用戶資料失敗: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('更新失敗: $e')),
        );
      }
    }
  }



  /// 構建我的分析卡片
  Widget _buildMyAnalysisCard() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          color: Colors.white,
          border: Border.all(
            color: AppColors.solarAmber.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 優化的標題列
              _buildAnalysisTitleRow(),
              const SizedBox(height: 24),

              if (_selectedBirthData == null) ...[
                // 沒有出生資料時的提示
                _buildNoBirthDataPrompt(),
              ] else if (_userAnalysis == null && !_isAnalysisLoading) ...[
                // 有出生資料但沒有分析時的提示
                _buildGenerateAnalysisPrompt(),
              ] else if (_isAnalysisLoading) ...[
                // 分析生成中
                _buildAnalysisLoading(),
              ] else ...[
                // 顯示分析結果
                _buildAnalysisResults(),
              ],
            ],
          ),
        ),
      ),
    );
  }

  /// 構建分析標題列
  Widget _buildAnalysisTitleRow() {
    return Row(
      children: [
        // 漸變圖標背景
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            gradient: LinearGradient(
              colors: [
                AppColors.solarAmber.withValues(alpha: 0.2),
                AppColors.solarAmber.withValues(alpha: 0.1),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            border: Border.all(
              color: AppColors.solarAmber.withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          child: const Icon(
            Icons.psychology_outlined,
            color: AppColors.solarAmber,
            size: 28,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                '我的分析',
                style: TextStyle(
                  fontSize: 22,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textDark,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                _selectedBirthData != null
                    ? '基於 ${_selectedBirthData!.birthData.name} 的星盤分析'
                    : '個人化的占星分析',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
        // 操作按鈕組
        _buildModernActionButtons(),
      ],
    );
  }

  /// 構建現代化操作按鈕組
  Widget _buildModernActionButtons() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // 重新選擇出生資料按鈕
        _buildModernActionButton(
          icon: Icons.person_search_outlined,
          onPressed: _selectBirthData,
          tooltip: '重新選擇出生資料',
          color: Colors.grey[600]!,
          isPrimary: false,
        ),
        const SizedBox(width: 8),

        if (_selectedBirthData != null) ...[
          if (_userAnalysis != null) ...[
            _buildModernActionButton(
              icon: Icons.edit_outlined,
              onPressed: _showEditAnalysisDialog,
              tooltip: '編輯分析',
              color: AppColors.royalIndigo,
              isPrimary: false,
            ),
            const SizedBox(width: 8),
          ],
          _buildModernActionButton(
            icon: _userAnalysis != null ? Icons.refresh_outlined : Icons.auto_awesome_outlined,
            onPressed: _generatePersonalAnalysis,
            tooltip: _userAnalysis != null ? '重新分析' : '開始分析',
            color: AppColors.solarAmber,
            isPrimary: true,
          ),
        ],
      ],
    );
  }

  /// 構建現代化操作按鈕
  Widget _buildModernActionButton({
    required IconData icon,
    required VoidCallback onPressed,
    required String tooltip,
    required Color color,
    bool isPrimary = false,
  }) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: isPrimary ? color : Colors.transparent,
        border: Border.all(
          color: color.withValues(alpha: isPrimary ? 0 : 0.3),
          width: 1.5,
        ),
        boxShadow: isPrimary ? [
          BoxShadow(
            color: color.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ] : null,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: onPressed,
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Icon(
              icon,
              color: isPrimary ? Colors.white : color,
              size: 20,
            ),
          ),
        ),
      ),
    );
  }

  /// 構建沒有出生資料時的提示
  Widget _buildNoBirthDataPrompt() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.orange.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          const Icon(
            Icons.person_search_outlined,
            size: 48,
            color: Colors.orange,
          ),
          const SizedBox(height: 12),
          const Text(
            '還沒有出生資料',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppColors.textDark,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '需要出生資料才能進行個人分析\n生成自我介紹、性格分析等內容',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          Center(
            child: Text(
              '請使用右上角的「👤」按鈕選擇或新增出生資料',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
                fontStyle: FontStyle.italic,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  /// 構建生成分析提示
  Widget _buildGenerateAnalysisPrompt() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.solarAmber.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.solarAmber.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          const Icon(
            Icons.auto_awesome_outlined,
            size: 48,
            color: AppColors.solarAmber,
          ),
          const SizedBox(height: 12),
          const Text(
            '準備好了！',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppColors.textDark,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '基於您的出生資料：${_selectedBirthData?.birthData.name}\n生成個人化的性格分析和自我介紹',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: _generatePersonalAnalysis,
            icon: const Icon(Icons.psychology_outlined),
            label: const Text('開始分析'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.solarAmber,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
          ),
        ],
      ),
    );
  }

  /// 構建分析載入中
  Widget _buildAnalysisLoading() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          const CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AppColors.solarAmber),
          ),
          const SizedBox(height: 16),
          const Text(
            '正在分析您的星盤...',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: AppColors.textDark,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '這可能需要幾秒鐘時間',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  /// 構建分析結果
  Widget _buildAnalysisResults() {
    if (_userAnalysis == null) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 基本資訊
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.grey[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey[200]!),
          ),
          child: Row(
            children: [
              const Icon(Icons.person_outline, color: AppColors.royalIndigo),
              const SizedBox(width: 8),
              Text(
                '基於：${_selectedBirthData?.birthData.name}',
                style: const TextStyle(
                  fontWeight: FontWeight.w500,
                  color: AppColors.textDark,
                ),
              ),
              const Spacer(),
              Text(
                _formatDateTime(DateTime.now()),
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),

        // 分析內容
        _buildAnalysisSection('自我介紹', Icons.badge_outlined, _userAnalysis!.selfIntroduction),
        _buildAnalysisSection('性格優點', Icons.thumb_up_outlined, _userAnalysis!.strengths),
        _buildAnalysisSection('性格缺點', Icons.warning_outlined, _userAnalysis!.weaknesses),
        _buildAnalysisSection('喜歡話題', Icons.chat_outlined, _userAnalysis!.favoriteTopics),
        _buildAnalysisSection('人際偏好', Icons.people_outlined, _userAnalysis!.socialPreferences),
      ],
    );
  }

  /// 構建分析區塊
  Widget _buildAnalysisSection(String title, IconData icon, String? content) {
    if (content == null || content.isEmpty) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: AppColors.solarAmber.withValues(alpha: 0.1)),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.solarAmber.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(
                  icon,
                  size: 18,
                  color: AppColors.solarAmber,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textDark,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            content,
            style: TextStyle(
              fontSize: 15,
              color: Colors.grey[700],
              height: 1.5,
              letterSpacing: 0.2,
            ),
          ),
        ],
      ),
    );
  }

  /// 選擇出生資料
  Future<void> _selectBirthData() async {
    try {
      // 載入本地出生資料列表
      final birthDataService = BirthDataService();
      final birthDataList = await birthDataService.getAllBirthData();

      if (birthDataList.isEmpty) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('沒有可用的出生資料，請先新增出生資料')),
          );
        }
        // 如果沒有資料，導航到新增頁面
        await _addNewBirthData();
        return;
      }

      // 使用 PersonSelectorPage 顯示選擇器
      final BirthData? result = await PersonSelectorPage.show(
        context: context,
        birthDataList: birthDataList,
        title: '選擇出生資料',
        buttonColor: AppColors.solarAmber,
      );

      if (result != null && _currentUser?.uid != null) {
        // 將選擇的出生資料保存到 Firebase
        final birthDataId = await UserBirthDataService.createFromLocalBirthData(
          _currentUser!.uid,
          result,
        );

        if (birthDataId != null) {
          // 更新 UserProfile 中的選中出生資料 ID
          await _updateSelectedBirthDataId(birthDataId);

          // 重新載入資料
          await _loadUserData();

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('已選擇出生資料：${result.name}'),
                backgroundColor: AppColors.successGreen,
              ),
            );
          }
        }
      }
    } catch (e) {
      logger.e('選擇出生資料失敗: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('選擇出生資料失敗: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// 新增出生資料
  Future<void> _addNewBirthData() async {
    final result = await Navigator.push<BirthData>(
      context,
      MaterialPageRoute(
        builder: (context) => const BirthDataFormPage(),
      ),
    );

    if (result != null && _currentUser?.uid != null) {
      // 將新增的出生資料保存到 Firebase
      final birthDataId = await UserBirthDataService.createFromLocalBirthData(
        _currentUser!.uid,
        result,
      );

      if (birthDataId != null) {
        // 更新 UserProfile 中的選中出生資料 ID
        await _updateSelectedBirthDataId(birthDataId);

        // 重新載入資料
        await _loadUserData();
      }
    }
  }

  /// 更新 UserProfile 中的選中出生資料 ID
  Future<void> _updateSelectedBirthDataId(String birthDataId) async {
    try {
      final user = _currentUser;
      if (user?.uid == null) return;

      // 更新 UserProfile 中的選中出生資料 ID
      final profile = _userProfile;
      if (profile != null) {
        final updatedProfile = profile.copyWith(
          selectedBirthDataId: birthDataId,
          updatedAt: DateTime.now(),
        );

        await UserProfileUnifiedService.updateUser(user!.uid, updatedProfile);

        setState(() {
          _userProfile = updatedProfile;
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('出生資料已更新到雲端'),
              backgroundColor: AppColors.successGreen,
            ),
          );
        }
      }
    } catch (e) {
      logger.e('更新選中出生資料 ID 失敗: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('更新失敗: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// 生成個人分析
  Future<void> _generatePersonalAnalysis() async {
    if (_selectedBirthData == null || _currentUser?.uid == null) return;

    setState(() {
      _isAnalysisLoading = true;
    });

    try {
      // 這裡調用 AI 分析服務
      final analysisResult = await _performPersonalAnalysis(_selectedBirthData!.birthData);

      // 保存分析結果到 Firebase
      final analysisId = await UserAnalysisService.createAnalysis(
        userId: _currentUser!.uid,
        birthDataId: _selectedBirthData!.id,
        selfIntroduction: analysisResult['selfIntroduction'] as String,
        strengths: analysisResult['strengths'] as String,
        weaknesses: analysisResult['weaknesses'] as String,
        favoriteTopics: analysisResult['favoriteTopics'] as String,
        socialPreferences: analysisResult['socialPreferences'] as String,
      );

      if (analysisId != null) {
        // 重新載入分析結果
        final userAnalysis = await UserAnalysisService.getUserAnalysis(
          _currentUser!.uid,
          _selectedBirthData!.id,
        );

        setState(() {
          _userAnalysis = userAnalysis;
          _isAnalysisLoading = false;
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('個人分析生成完成！'),
              backgroundColor: AppColors.successGreen,
            ),
          );
        }
      } else {
        throw Exception('保存分析結果失敗');
      }
    } catch (e) {
      logger.e('生成個人分析失敗: $e');
      setState(() {
        _isAnalysisLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('分析失敗: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// 執行個人分析
  Future<Map<String, dynamic>> _performPersonalAnalysis(BirthData birthData) async {
    try {
      // 創建星盤數據
      final chartData = ChartData(
        chartType: ChartType.natal,
        primaryPerson: birthData,
      );

      // 計算完整星盤數據
      final calculatedChartData = await AstrologyService.calculateChartData(chartData);

      // 構建個人分析的提示詞
      final customPrompt = _buildPersonalAnalysisPrompt(calculatedChartData);

      // 調用 AI 分析服務
      final aiResponse = await ChartInterpretationService.getCustomInterpretation(
        chartData: calculatedChartData,
        customPrompt: customPrompt,
      );

      if (aiResponse.success && aiResponse.content.isNotEmpty) {
        // 解析 AI 回應，提取各個維度的內容
        return _parseAnalysisResponse(aiResponse.content);
      } else {
        throw Exception('AI 分析失敗: ${aiResponse.content}');
      }
    } catch (e) {
      logger.e('執行個人分析失敗: $e');
      // 如果 AI 分析失敗，返回基於星盤的基本分析
      return _generateBasicAnalysis(birthData);
    }
  }

  /// 構建個人分析提示詞
  String _buildPersonalAnalysisPrompt(ChartData chartData) {
    return '''
請基於以下星盤資料，為用戶生成個人化的分析內容：

請按照以下格式提供分析結果：

【自我介紹】
生成一段適合社交場合的自我介紹，突出個人特色和魅力

【性格優點】
列出主要的性格優點，用逗號分隔

【性格缺點】
列出需要注意的性格弱點，用逗號分隔

【喜歡話題】
列出喜歡興趣與話題，用逗號分隔

【人際偏好】
描述社交風格和人際關係偏好

不用給建議，當作正式交友的自我介紹。
''';
    // 請確保內容積極正面，適合用於交友和社交場合。
  }

  /// 解析 AI 分析回應
  Map<String, dynamic> _parseAnalysisResponse(String response) {
    final result = <String, dynamic>{};

    try {
      // 使用正則表達式提取各個部分
      final selfIntroMatch = RegExp(r'【自我介紹】\s*\n(.*?)(?=\n【|$)', dotAll: true).firstMatch(response);
      final strengthsMatch = RegExp(r'【性格優點】\s*\n(.*?)(?=\n【|$)', dotAll: true).firstMatch(response);
      final weaknessesMatch = RegExp(r'【性格缺點】\s*\n(.*?)(?=\n【|$)', dotAll: true).firstMatch(response);
      final topicsMatch = RegExp(r'【喜歡話題】\s*\n(.*?)(?=\n【|$)', dotAll: true).firstMatch(response);
      final socialMatch = RegExp(r'【人際偏好】\s*\n(.*?)(?=\n【|$)', dotAll: true).firstMatch(response);

      result['selfIntroduction'] = selfIntroMatch?.group(1)?.trim() ?? '我是一個獨特的個體，正在探索自己的人生道路。';
      result['strengths'] = strengthsMatch?.group(1)?.trim() ?? '善於思考、有責任心、樂於助人';
      result['weaknesses'] = weaknessesMatch?.group(1)?.trim() ?? '有時過於謹慎、需要更多自信';
      result['favoriteTopics'] = topicsMatch?.group(1)?.trim() ?? '生活分享、興趣愛好、個人成長';
      result['socialPreferences'] = socialMatch?.group(1)?.trim() ?? '喜歡真誠的交流，重視深度的人際關係。';

    } catch (e) {
      logger.w('解析 AI 回應失敗，使用預設內容: $e');
      return _generateBasicAnalysis(null);
    }

    return result;
  }

  /// 生成基本分析（當 AI 分析失敗時使用）
  Map<String, dynamic> _generateBasicAnalysis(BirthData? birthData) {
    return {
      'selfIntroduction': '我是一個充滿好奇心的人，喜歡探索生活中的美好事物，相信每個人都有自己獨特的故事和價值。',
      'strengths': '善於思考、有責任心、樂於助人、適應能力強、重視友誼',
      'weaknesses': '有時過於謹慎、可能過度思考、需要更多自信、偶爾會猶豫不決',
      'favoriteTopics': '生活分享、興趣愛好、個人成長、文化藝術、科技趨勢、美食旅行',
      'socialPreferences': '喜歡真誠的交流，重視深度的人際關係，欣賞有趣且有內涵的對話。',
    };
  }

  /// 顯示編輯分析結果對話框
  Future<void> _showEditAnalysisDialog() async {
    if (_userAnalysis == null) return;

    final analysis = _userAnalysis!;

    // 使用 Navigator.push 而不是 showDialog 來避免控制器釋放問題
    final result = await Navigator.push<Map<String, String>>(
      context,
      MaterialPageRoute(
        builder: (context) => _EditAnalysisPage(
          initialAnalysis: {
            'selfIntroduction': analysis.selfIntroduction,
            'strengths': analysis.strengths,
            'weaknesses': analysis.weaknesses,
            'favoriteTopics': analysis.favoriteTopics,
            'socialPreferences': analysis.socialPreferences,
          },
        ),
      ),
    );

    if (result != null) {
      await _updateAnalysisResult(
        selfIntroduction: result['selfIntroduction'] ?? '',
        strengths: result['strengths'] ?? '',
        weaknesses: result['weaknesses'] ?? '',
        favoriteTopics: result['favoriteTopics'] ?? '',
        socialPreferences: result['socialPreferences'] ?? '',
      );
    }
  }



  /// 更新分析結果
  Future<void> _updateAnalysisResult({
    required String selfIntroduction,
    required String strengths,
    required String weaknesses,
    required String favoriteTopics,
    required String socialPreferences,
  }) async {
    if (_userAnalysis == null || _currentUser?.uid == null) return;

    try {
      // 更新分析結果
      final updatedAnalysis = _userAnalysis!.copyWith(
        selfIntroduction: selfIntroduction,
        strengths: strengths,
        weaknesses: weaknesses,
        favoriteTopics: favoriteTopics,
        socialPreferences: socialPreferences,
        updatedAt: DateTime.now(),
      );

      // 保存到 Firebase
      final success = await UserAnalysisService.updateAnalysis(
        _currentUser!.uid,
        _userAnalysis!.id,
        updatedAnalysis,
      );

      if (success) {
        setState(() {
          _userAnalysis = updatedAnalysis;
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('分析結果更新成功！'),
              backgroundColor: AppColors.successGreen,
            ),
          );
        }
      } else {
        throw Exception('更新失敗');
      }
    } catch (e) {
      logger.e('更新分析結果失敗: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('更新失敗: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}

/// 編輯分析結果頁面
class _EditAnalysisPage extends StatefulWidget {
  final Map<String, String> initialAnalysis;

  const _EditAnalysisPage({
    required this.initialAnalysis,
  });

  @override
  State<_EditAnalysisPage> createState() => _EditAnalysisPageState();
}

class _EditAnalysisPageState extends State<_EditAnalysisPage> {
  late final TextEditingController _selfIntroController;
  late final TextEditingController _strengthsController;
  late final TextEditingController _weaknessesController;
  late final TextEditingController _topicsController;
  late final TextEditingController _socialController;

  @override
  void initState() {
    super.initState();
    _selfIntroController = TextEditingController(text: widget.initialAnalysis['selfIntroduction'] ?? '');
    _strengthsController = TextEditingController(text: widget.initialAnalysis['strengths'] ?? '');
    _weaknessesController = TextEditingController(text: widget.initialAnalysis['weaknesses'] ?? '');
    _topicsController = TextEditingController(text: widget.initialAnalysis['favoriteTopics'] ?? '');
    _socialController = TextEditingController(text: widget.initialAnalysis['socialPreferences'] ?? '');
  }

  @override
  void dispose() {
    _selfIntroController.dispose();
    _strengthsController.dispose();
    _weaknessesController.dispose();
    _topicsController.dispose();
    _socialController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('編輯我的分析'),
        backgroundColor: AppColors.royalIndigo,
        foregroundColor: Colors.white,
        actions: [
          TextButton(
            onPressed: _saveChanges,
            child: const Text(
              '保存',
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildEditField('自我介紹', _selfIntroController, maxLines: 7),
            const SizedBox(height: 20),
            _buildEditField('性格優點', _strengthsController, maxLines: 3),
            const SizedBox(height: 20),
            _buildEditField('性格缺點', _weaknessesController, maxLines: 3),
            const SizedBox(height: 20),
            _buildEditField('喜歡話題', _topicsController, maxLines: 3),
            const SizedBox(height: 20),
            _buildEditField('人際偏好', _socialController, maxLines: 7),
            const SizedBox(height: 50),
          ],
        ),
      ),
    );
  }

  /// 構建編輯欄位
  Widget _buildEditField(String label, TextEditingController controller, {int maxLines = 1}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: AppColors.textDark,
          ),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: controller,
          maxLines: maxLines,
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: AppColors.royalIndigo, width: 2),
            ),
            contentPadding: const EdgeInsets.all(16),
            hintText: '請輸入$label',
            filled: true,
            fillColor: Colors.grey[50],
          ),
        ),
      ],
    );
  }

  /// 保存變更
  void _saveChanges() {
    final result = {
      'selfIntroduction': _selfIntroController.text,
      'strengths': _strengthsController.text,
      'weaknesses': _weaknessesController.text,
      'favoriteTopics': _topicsController.text,
      'socialPreferences': _socialController.text,
    };

    Navigator.of(context).pop(result);
  }
}
