import 'package:flutter/material.dart';

import '../../astreal.dart';
import '../../data/services/api/profection_service.dart';
import '../../shared/widgets/common/date_time_picker_bottom_sheet.dart';
import '../pages/ai_interpretation_result_page.dart';

/// 小限法時間軸頁面
class ProfectionTimelinePage extends StatefulWidget {
  final ProfectionTimelineResult result;
  final ChartViewModel viewModel;

  const ProfectionTimelinePage({
    Key? key,
    required this.result,
    required this.viewModel,
  }) : super(key: key);

  @override
  State<ProfectionTimelinePage> createState() => _ProfectionTimelinePageState();
}

class _ProfectionTimelinePageState extends State<ProfectionTimelinePage> {
  late ScrollController _scrollController;
  
  // 時間設定相關狀態
  DateTime? _customDate;
  ProfectionTimelineResult? _customResult;
  bool _isCalculating = false;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text(
          '小限法時間軸',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppColors.royalIndigo,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Column(
        children: [
          // 時間設定區域
          _buildTimeSettingSection(),
          
          // 當前小限法信息
          _buildCurrentProfectionInfo(),
          
          const SizedBox(height: 16),
          
          // 時間軸列表
          Expanded(
            child: _isCalculating 
                ? const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CircularProgressIndicator(
                          valueColor: AlwaysStoppedAnimation<Color>(AppColors.royalIndigo),
                        ),
                        SizedBox(height: 16),
                        Text(
                          '正在計算小限法數據...',
                          style: TextStyle(color: Colors.grey),
                        ),
                      ],
                    ),
                  )
                : _buildTimelineList(),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _navigateToInterpretation,
        backgroundColor: AppColors.solarAmber,
        foregroundColor: Colors.white,
        icon: const Icon(Icons.psychology),
        label: const Text('AI 解讀'),
      ),
    );
  }

  /// 構建時間設定區域
  Widget _buildTimeSettingSection() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: AppColors.royalIndigo.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.royalIndigo.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.access_time,
            color: AppColors.royalIndigo,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  '分析時間設定',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: AppColors.royalIndigo,
                  ),
                ),
                Text(
                  _customDate != null 
                      ? '自訂時間：${_formatDateTime(_customDate!)}'
                      : '使用當前時間',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
          // 時間設定按鈕
          ElevatedButton.icon(
            onPressed: _isCalculating ? null : _showDateTimePicker,
            icon: const Icon(Icons.schedule, size: 18),
            label: const Text('設定時間'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.royalIndigo,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
          const SizedBox(width: 8),
          // 重置按鈕（只在有自訂時間時顯示）
          if (_customDate != null)
            ElevatedButton.icon(
              onPressed: _isCalculating ? null : _resetToCurrentTime,
              icon: const Icon(Icons.refresh, size: 18),
              label: const Text('重置'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange.shade600,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// 構建當前小限法信息
  Widget _buildCurrentProfectionInfo() {
    // 使用自訂結果或原始結果
    final result = _customResult ?? widget.result;
    final current = result.currentProfection;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.royalIndigo,
            AppColors.indigoSurface,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.royalIndigo.withOpacity(0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.star,
                color: Colors.white,
                size: 24,
              ),
              const SizedBox(width: 8),
              const Text(
                '當前小限法',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildInfoItem('年齡', '${current.currentAge}歲'),
              ),
              Expanded(
                child: _buildInfoItem('小限宮位', '第${current.profectionHouse}宮'),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: _buildInfoItem('小限星座', current.profectionSign),
              ),
              Expanded(
                child: _buildInfoItem('時間主星', current.timeLordPlanetName),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 構建信息項目
  Widget _buildInfoItem(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.white.withOpacity(0.8),
          ),
        ),
        const SizedBox(height: 2),
        Text(
          value,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
      ],
    );
  }

  /// 構建時間軸列表
  Widget _buildTimelineList() {
    // 使用自訂結果或原始結果
    final result = _customResult ?? widget.result;
    
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                const Icon(
                  Icons.timeline,
                  color: AppColors.indigoSurface,
                  size: 20,
                ),
                const SizedBox(width: 8),
                const Text(
                  '年度時間軸',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
                if (_isCalculating) ...[
                  const SizedBox(width: 12),
                  const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(AppColors.indigoSurface),
                    ),
                  ),
                ],
              ],
            ),
          ),
          Expanded(
            child: Scrollbar(
              controller: _scrollController,
              thumbVisibility: true,
              child: ListView.builder(
                controller: _scrollController,
                itemCount: result.timeline.length,
                padding: const EdgeInsets.only(bottom: 16),
                itemBuilder: (context, index) {
                  final profection = result.timeline[index];
                  final isCurrent = profection.isCurrent;
                  
                  return Container(
                    margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                    decoration: BoxDecoration(
                      color: isCurrent 
                          ? AppColors.solarAmber.withOpacity(0.1)
                          : Colors.grey.shade50,
                      borderRadius: BorderRadius.circular(8),
                      border: isCurrent 
                          ? Border.all(color: AppColors.solarAmber, width: 2)
                          : Border.all(color: Colors.grey.shade200),
                    ),
                    child: ListTile(
                      leading: CircleAvatar(
                        backgroundColor: isCurrent 
                            ? AppColors.solarAmber
                            : Colors.grey.shade400,
                        child: Text(
                          '${profection.currentAge}',
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                        ),
                      ),
                      title: Text(
                        '第${profection.profectionHouse}宮 - ${profection.profectionSign}',
                        style: TextStyle(
                          fontWeight: isCurrent ? FontWeight.bold : FontWeight.normal,
                          color: isCurrent ? AppColors.royalIndigo : Colors.black87,
                        ),
                      ),
                      subtitle: Text(
                        '時間主星：${profection.timeLordPlanetName}',
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontSize: 12,
                        ),
                      ),
                      trailing: isCurrent 
                          ? const Icon(
                              Icons.star,
                              color: AppColors.solarAmber,
                            )
                          : null,
                    ),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 顯示日期時間選擇器
  Future<void> _showDateTimePicker() async {
    if (_isCalculating) return;

    final initialDate = _customDate ?? DateTime.now();
    
    final selectedDateTime = await DateTimePickerBottomSheet.show(
      context: context,
      initialDateTime: initialDate,
      title: '選擇小限法分析時間',
      minDate: DateTime(1800, 1, 1),
      maxDate: DateTime(2100, 12, 31),
      defaultDateTime: DateTime.now(),
    );

    if (selectedDateTime != null) {
      await _updateCustomTime(selectedDateTime);
    }
  }

  /// 更新自訂時間並重新計算小限法
  Future<void> _updateCustomTime(DateTime customDate) async {
    if (_isCalculating) return;

    setState(() {
      _isCalculating = true;
      _customDate = customDate;
    });

    try {
      // 使用 ProfectionService 重新計算小限法數據
      final profectionService = ProfectionService();
      final newResult = await profectionService.calculateProfectionTimeline(
        widget.viewModel.primaryPerson,
        currentDate: customDate,
        yearsRange: 10, // 計算前後各10年
        housesData: widget.viewModel.chartData.houses,
      );

      if (mounted) {
        setState(() {
          _customResult = newResult;
          _isCalculating = false;
        });
      }
    } catch (e) {
      logger.e('重新計算小限法時出錯: $e');
      if (mounted) {
        setState(() {
          _isCalculating = false;
        });
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('計算小限法時出錯: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// 重置為當前時間
  Future<void> _resetToCurrentTime() async {
    if (_isCalculating) return;

    setState(() {
      _customDate = null;
      _customResult = null;
    });
  }

  /// 格式化日期時間顯示
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}/${dateTime.month.toString().padLeft(2, '0')}/${dateTime.day.toString().padLeft(2, '0')} '
           '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  /// 導航到 AI 解讀頁面
  void _navigateToInterpretation() {
    // 使用自訂結果或原始結果
    final result = _customResult ?? widget.result;
    final current = result.currentProfection;

    // 創建一個基於原始星盤的 ChartData，但添加小限法信息
    final chartData = ChartData(
      chartType: widget.viewModel.chartData.chartType,
      primaryPerson: widget.viewModel.chartData.primaryPerson,
      secondaryPerson: widget.viewModel.chartData.secondaryPerson,
      specificDate: widget.viewModel.chartData.specificDate,
      planets: widget.viewModel.chartData.planets,
      houses: widget.viewModel.chartData.houses,
      aspects: widget.viewModel.chartData.aspects,
    );

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) {
          return AIInterpretationResultPage(
            chartData: chartData,
            interpretationTitle: '小限法解讀',
            subtitle: '${current.currentAge}歲 - 第${current.profectionHouse}宮 ${current.profectionSign}',
            suggestedQuestions: [
              _buildProfectionInterpretationPrompt(current),
            ],
            autoExecuteFirstQuestion: true,
          );
        },
      ),
    );
  }

  /// 構建小限法專用的解讀提示詞
  String _buildProfectionInterpretationPrompt(ProfectionData current) {
    return '''
請基於這個本命盤為用戶提供詳細的小限法（Profection）解讀分析：

**當前小限法狀況：**
- 年齡：${current.currentAge}歲
- 小限宮位：第${current.profectionHouse}宮
- 小限星座：${current.profectionSign}
- 時間主星：${current.timeLordPlanetName}

**請提供以下分析：**

1. **年度主題分析**
   - 根據第${current.profectionHouse}宮的意義，說明這一年的主要生活主題
   - 解釋${current.profectionSign}星座如何影響這個主題的表現方式

2. **時間主星指引**
   - 分析${current.timeLordPlanetName}作為時間主星的重要性
   - 說明應該如何關注${current.timeLordPlanetName}在本命盤中的位置和相位
   - 提供關於${current.timeLordPlanetName}行運的建議

3. **實用建議**
   - 這一年應該重點關注哪些生活領域
   - 如何善用這個小限年的能量
   - 需要注意的挑戰和機會

4. **時機把握**
   - 什麼時候是行動的最佳時機
   - 如何配合${current.timeLordPlanetName}的週期來規劃

請用專業但易懂的語言，提供實用的指導建議。
''';
  }
}
