import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../astreal.dart';
import '../../data/services/api/astrology_service.dart';
import '../../data/services/api/planetary_conjunction_service.dart';
import 'ai_interpretation_result_page.dart';

/// 會合時間選擇頁面
class ConjunctionSelectionPage extends StatefulWidget {
  final ConjunctionType conjunctionType;
  final BirthData person;
  final DateTime startDate;
  final DateTime endDate;

  const ConjunctionSelectionPage({
    super.key,
    required this.conjunctionType,
    required this.person,
    required this.startDate,
    required this.endDate,
  });

  @override
  State<ConjunctionSelectionPage> createState() => _ConjunctionSelectionPageState();
}

class _ConjunctionSelectionPageState extends State<ConjunctionSelectionPage> {
  bool _isLoading = true;
  String? _errorMessage;
  List<PlanetaryConjunction> _conjunctions = [];

  @override
  void initState() {
    super.initState();
    _searchConjunctions();
  }

  /// 搜尋會合時間點
  Future<void> _searchConjunctions() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      // 根據會合類型搜尋對應的會合
      final result = await PlanetaryConjunctionService.calculateConjunctions(
        latitude: widget.person.latitude,
        longitude: widget.person.longitude,
        startDate: widget.startDate,
        endDate: widget.endDate,
      );

      // 篩選出指定類型的會合
      final filteredConjunctions = result.allConjunctions
          .where((c) => c.type == widget.conjunctionType)
          .toList();

      // 按時間排序
      filteredConjunctions.sort((a, b) => a.dateTime.compareTo(b.dateTime));

      if (mounted) {
        setState(() {
          _conjunctions = filteredConjunctions;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = '搜尋會合時間失敗: ${e.toString()}';
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isJupiterSaturn = widget.conjunctionType == ConjunctionType.jupiterSaturn;
    final color = isJupiterSaturn ? AppColors.royalIndigo : AppColors.solarAmber;
    final title = isJupiterSaturn ? '木土會合時間選擇' : '火土會合時間選擇';
    final subtitle = isJupiterSaturn ? '20年經濟週期' : '2年調整週期';

    return Scaffold(
      appBar: AppBar(
        title: Text(title),
        backgroundColor: color,
        foregroundColor: Colors.white,
      ),
      body: _isLoading
          ? _buildLoadingWidget(color)
          : _errorMessage != null
              ? _buildErrorWidget(color)
              : _buildConjunctionList(color, subtitle),
    );
  }

  /// 構建載入中顯示
  Widget _buildLoadingWidget(Color color) {
    final isJupiterSaturn = widget.conjunctionType == ConjunctionType.jupiterSaturn;
    
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(color),
            ),
            const SizedBox(height: 16),
            Text(
              isJupiterSaturn ? '正在搜尋木土會合時間' : '正在搜尋火土會合時間',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '時間範圍：${DateFormat('yyyy/MM/dd').format(widget.startDate)} - ${DateFormat('yyyy/MM/dd').format(widget.endDate)}',
              style: const TextStyle(fontSize: 12, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// 構建錯誤顯示
  Widget _buildErrorWidget(Color color) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage!,
              style: const TextStyle(fontSize: 16),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _searchConjunctions,
              style: ElevatedButton.styleFrom(
                backgroundColor: color,
                foregroundColor: Colors.white,
              ),
              child: const Text('重新搜尋'),
            ),
          ],
        ),
      ),
    );
  }

  /// 構建會合列表
  Widget _buildConjunctionList(Color color, String subtitle) {
    return Column(
      children: [
        // 搜尋結果摘要
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          color: color.withValues(alpha: 0.1),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '搜尋結果',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                '找到 ${_conjunctions.length} 個${subtitle}會合時間點',
                style: const TextStyle(fontSize: 12, color: Colors.grey),
              ),
              if (_conjunctions.isNotEmpty) ...[
                const SizedBox(height: 8),
                Text(
                  '請選擇要分析的會合時間點：',
                  style: TextStyle(
                    fontSize: 13,
                    color: color,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ],
          ),
        ),

        // 會合列表
        Expanded(
          child: _conjunctions.isEmpty
              ? _buildEmptyState(color)
              : ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: _conjunctions.length,
                  itemBuilder: (context, index) {
                    final conjunction = _conjunctions[index];
                    return _buildConjunctionCard(conjunction, color, index);
                  },
                ),
        ),
      ],
    );
  }

  /// 構建空狀態
  Widget _buildEmptyState(Color color) {
    final isJupiterSaturn = widget.conjunctionType == ConjunctionType.jupiterSaturn;
    
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              isJupiterSaturn ? Icons.public : Icons.flash_on,
              size: 64,
              color: Colors.grey,
            ),
            const SizedBox(height: 16),
            Text(
              '在指定時間範圍內沒有找到${isJupiterSaturn ? "木土" : "火土"}會合',
              style: const TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              '請嘗試擴大搜尋時間範圍',
              style: const TextStyle(
                fontSize: 12,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 構建會合卡片
  Widget _buildConjunctionCard(PlanetaryConjunction conjunction, Color color, int index) {
    final isHistorical = conjunction.dateTime.isBefore(DateTime.now());
    final dateStr = DateFormat('yyyy年MM月dd日 HH:mm').format(conjunction.dateTime);
    
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: StyledCard(
        elevation: 2,
        child: InkWell(
          onTap: () => _selectConjunction(conjunction),
          borderRadius: BorderRadius.circular(8),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: color.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Text(
                        '${index + 1}',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: color,
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            dateStr,
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: color,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Row(
                            children: [
                              Icon(
                                isHistorical ? Icons.history : Icons.schedule,
                                size: 14,
                                color: Colors.grey,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                isHistorical ? '歷史會合' : '未來會合',
                                style: const TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    Icon(
                      Icons.arrow_forward_ios,
                      color: color.withValues(alpha: 0.7),
                      size: 16,
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade50,
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.location_on,
                        size: 14,
                        color: Colors.grey.shade600,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '位置：${conjunction.zodiacSign}',
                        style: TextStyle(
                          fontSize: 11,
                          color: Colors.grey.shade600,
                        ),
                      ),
                      const Spacer(),
                      Icon(
                        Icons.psychology,
                        size: 14,
                        color: color,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '點擊進行AI分析',
                        style: TextStyle(
                          fontSize: 11,
                          color: color,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 選擇會合進行分析
  void _selectConjunction(PlanetaryConjunction conjunction) async {
    try {
      // 顯示載入指示器
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => Dialog(
          child: Container(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(
                    widget.conjunctionType == ConjunctionType.jupiterSaturn 
                        ? AppColors.royalIndigo 
                        : AppColors.solarAmber,
                  ),
                ),
                const SizedBox(height: 16),
                const Text(
                  '正在準備AI分析',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  '分析時間：${DateFormat('yyyy年MM月dd日').format(conjunction.dateTime)}',
                  style: const TextStyle(fontSize: 12, color: Colors.grey),
                ),
              ],
            ),
          ),
        ),
      );

      // 創建星盤數據
      final chartData = await _createChartData(conjunction.dateTime);

      // 關閉載入指示器
      if (mounted) {
        Navigator.of(context).pop();
      }

      // 導航到AI分析頁面
      if (mounted) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => AIInterpretationResultPage(
              chartData: chartData,
              interpretationTitle: _getAnalysisTitle(conjunction),
              subtitle: conjunction.zodiacSign,
              suggestedQuestions: _getSuggestedQuestions(conjunction),
            ),
          ),
        );
      }
    } catch (e) {
      // 關閉載入指示器
      if (mounted) {
        Navigator.of(context).pop();
      }

      // 顯示錯誤信息
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('無法進行分析: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// 創建星盤數據
  Future<ChartData> _createChartData(DateTime conjunctionTime) async {
    final astrologyService = AstrologyService();
    await astrologyService.initialize();

    // 使用會合時間創建星盤
    final conjunctionPerson = BirthData(
      id: widget.person.id,
      name: widget.person.name,
      dateTime: conjunctionTime,
      latitude: widget.person.latitude,
      longitude: widget.person.longitude,
      birthPlace: widget.person.birthPlace,
    );

    final chartData = ChartData(
      chartType: ChartType.natal,
      primaryPerson: conjunctionPerson,
      specificDate: conjunctionTime,
    );

    return await astrologyService.calculateChartData(
      chartData,
      latitude: conjunctionPerson.latitude,
      longitude: conjunctionPerson.longitude,
    );
  }

  /// 獲取分析標題
  String _getAnalysisTitle(PlanetaryConjunction conjunction) {
    final dateStr = DateFormat('yyyy年MM月dd日').format(conjunction.dateTime);
    final isJupiterSaturn = widget.conjunctionType == ConjunctionType.jupiterSaturn;
    return '${isJupiterSaturn ? "木土" : "火土"}會合分析 - $dateStr';
  }

  /// 獲取建議問題
  List<String> _getSuggestedQuestions(PlanetaryConjunction conjunction) {
    final dateStr = DateFormat('yyyy年MM月dd日').format(conjunction.dateTime);
    final isJupiterSaturn = widget.conjunctionType == ConjunctionType.jupiterSaturn;
    final isHistorical = conjunction.dateTime.isBefore(DateTime.now());

    if (isJupiterSaturn) {
      return [
        '${dateStr}的木土會合對全球經濟有什麼重大影響？',
        '這次會合在${conjunction.zodiacSign}座的特殊意義是什麼？',
        isHistorical ? '回顧這次會合期間，有哪些重要的經濟事件？' : '未來這次會合可能帶來哪些經濟變化？',
        '個人投資者應該如何應對這次20年週期的轉換？',
        '這次會合對不同行業的影響有何差異？',
      ];
    } else {
      return [
        '${dateStr}的火土會合對投資市場有什麼影響？',
        '這次會合在${conjunction.zodiacSign}座代表什麼投資機會？',
        isHistorical ? '回顧這次會合期間，市場有哪些重要變化？' : '未來這次會合可能帶來哪些市場調整？',
        '投資者應該如何調整投資組合來應對這次會合？',
        '這次2年週期的會合對風險控制有什麼啟示？',
      ];
    }
  }
}
