import 'package:flutter/material.dart';

import '../../../astreal.dart';
import '../../../shared/widgets/common/responsive_wrapper.dart';

/// 占星分析注意事項頁面
class AstrologyGuidancePage extends StatelessWidget {
  const AstrologyGuidancePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('占星分析注意事項'),
        backgroundColor: AppColors.royalIndigo,
        foregroundColor: Colors.white,
      ),
      body: ResponsivePageWrapper(
        maxWidth: 800.0, // 占星分析注意事項適合中等寬度
        child: SingleChildScrollView(
          padding: ResponsiveUtils.getResponsivePadding(context),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 頁面說明
              _buildPageDescription(),

              // 基本原則
              _buildBasicPrinciplesSection(),

              // 解讀準確性
              _buildAccuracySection(),

              // 使用建議
              _buildUsageRecommendationsSection(),

              // 注意事項
              _buildWarningsSection(),

              // 免責聲明
              _buildDisclaimerSection(),
            ],
          ),
        ),
      ),
    );
  }

  /// 構建頁面說明
  Widget _buildPageDescription() {
    return StyledCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: AppColors.royalIndigo.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.info_outline,
                    color: AppColors.royalIndigo,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                const Expanded(
                  child: Text(
                    '占星分析使用指南',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textDark,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              '占星學是一門古老的學問，結合天文觀測與人文解讀。本應用提供的分析僅供參考，請理性看待占星資訊，並結合個人實際情況進行判斷。',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade600,
                height: 1.5,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 構建基本原則區塊
  Widget _buildBasicPrinciplesSection() {
    return _buildSection(
      title: '基本原則',
      icon: Icons.rule,
      color: AppColors.solarAmber,
      items: [
        '占星分析基於出生時間、地點的天體位置計算',
        '準確的出生資料是精確分析的基礎',
        '星盤反映潛能與傾向，非絕對命運',
        '個人意志與努力可以影響人生發展',
        '占星學提供自我認識的工具，非預測未來的絕對方法',
      ],
    );
  }

  /// 構建解讀準確性區塊
  Widget _buildAccuracySection() {
    return _buildSection(
      title: '解讀準確性',
      icon: Icons.precision_manufacturing,
      color: AppColors.cosmicPurple,
      items: [
        '出生時間誤差會影響上升星座和宮位計算',
        '建議使用官方出生證明的精確時間',
        '時間不確定時，可參考太陽星座分析',
        '解讀基於傳統占星理論，但無法完全替代專業占星師',
        '複雜的人生問題建議諮詢專業占星師',
      ],
    );
  }

  /// 構建使用建議區塊
  Widget _buildUsageRecommendationsSection() {
    return _buildSection(
      title: '使用建議',
      icon: Icons.lightbulb_outline,
      color: AppColors.successGreen,
      items: [
        '將占星分析作為自我探索的參考工具',
        '結合多個分析角度，避免單一解讀',
        '定期回顧分析內容，觀察個人成長變化',
        '與親友分享討論，增進相互理解',
        '保持開放心態，理性看待分析結果',
      ],
    );
  }

  /// 構建注意事項區塊
  Widget _buildWarningsSection() {
    return _buildSection(
      title: '重要注意事項',
      icon: Icons.warning_amber,
      color: AppColors.warning,
      items: [
        '不應將占星分析作為重大決策的唯一依據',
        '避免過度依賴占星預測，忽略現實努力',
        '健康、法律、財務問題請諮詢專業人士',
        '占星分析不能替代醫療診斷或心理治療',
        '保持批判思維，避免迷信或過度解讀',
      ],
    );
  }

  /// 構建免責聲明區塊
  Widget _buildDisclaimerSection() {
    return StyledCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: Colors.red.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.gavel,
                    color: Colors.red,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                const Expanded(
                  child: Text(
                    '免責聲明',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textDark,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              '本應用提供的占星分析內容僅供娛樂和參考用途。我們不對分析結果的準確性、完整性或適用性做任何保證。用戶應理性看待占星資訊，不應將其作為人生重大決策的唯一依據。\n\n'
              '對於因使用本應用分析內容而產生的任何直接或間接損失，本應用及其開發者概不負責。用戶在使用本應用時，即表示同意承擔相關風險並理解上述限制。',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade600,
                height: 1.5,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 構建通用區塊
  Widget _buildSection({
    required String title,
    required IconData icon,
    required Color color,
    required List<String> items,
  }) {
    return StyledCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    icon,
                    color: color,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    title,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textDark,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...items.map((item) => Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        width: 6,
                        height: 6,
                        margin: const EdgeInsets.only(top: 6, right: 12),
                        decoration: BoxDecoration(
                          color: color,
                          shape: BoxShape.circle,
                        ),
                      ),
                      Expanded(
                        child: Text(
                          item,
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey.shade700,
                            height: 1.4,
                          ),
                        ),
                      ),
                    ],
                  ),
                )),
          ],
        ),
      ),
    );
  }
}
