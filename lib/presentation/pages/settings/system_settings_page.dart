import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../astreal.dart';
import '../../../data/services/api/auth_service.dart';
import '../../../data/services/api/birth_data_service.dart';
import '../../../data/services/api/firebase_storage_backup_service.dart';
import '../../../data/services/api/remote_config_version_service.dart';
import '../../../shared/utils/csv_helper.dart';
import '../../../shared/widgets/common/responsive_wrapper.dart';
import '../../../shared/widgets/update_dialog.dart';
import '../../viewmodels/files_viewmodel.dart';
import 'log_management_page.dart';
import 'storage_diagnostics_page.dart';

/// 系統設置頁面
class SystemSettingsPage extends StatelessWidget {
  const SystemSettingsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('系統設置'),
        backgroundColor: AppColors.royalIndigo,
        foregroundColor: Colors.white,
      ),
      body: FutureBuilder<PackageInfo>(
        future: PackageInfo.fromPlatform(),
        builder: (context, snapshot) {
          return ResponsivePageWrapper(
            maxWidth: 800.0, // 系統設定頁面適合中等寬度
            child: ListView(
              padding: ResponsiveUtils.getResponsivePadding(context),
              children: [
              // 頁面說明
              _buildPageDescription(),
              const SizedBox(height: 16),

              // 資料管理
              _buildDataManagementSection(context),
              // const SizedBox(height: 24),

              // 應用設置
              // _buildAppSettingsSection(),
              // const SizedBox(height: 24),

              // 關於應用
              // _buildAboutSection(snapshot, context),
              // const SizedBox(height: 24),

              // 開發者選項（僅在開發模式顯示）
              // if (kDebugMode) _buildDeveloperSection(),
              ],
            ),
          );
        },
      ),
    );
  }

  /// 構建頁面說明
  Widget _buildPageDescription() {
    return Container(
      padding: const EdgeInsets.all(16),
      // decoration: BoxDecoration(
      //   gradient: LinearGradient(
      //     colors: [
      //       AppColors.royalIndigo.withValues(alpha: 0.1),
      //       AppColors.solarAmber.withValues(alpha: 0.1),
      //     ],
      //     begin: Alignment.topLeft,
      //     end: Alignment.bottomRight,
      //   ),
      //   borderRadius: BorderRadius.circular(12),
      //   border: Border.all(
      //     color: AppColors.royalIndigo.withValues(alpha: 0.2),
      //   ),
      // ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(
                Icons.settings,
                color: AppColors.royalIndigo,
                size: 24,
              ),
              SizedBox(width: 8),
              Text(
                '系統設置',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppColors.royalIndigo,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            '管理應用的系統設置，包括資料管理、應用偏好設定和關於信息。',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade700,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  /// 構建資料管理區域
  Widget _buildDataManagementSection(BuildContext context) {
    return StyledCard(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(
                  Icons.storage,
                  color: AppColors.solarAmber,
                  size: 20,
                ),
                SizedBox(width: 8),
                Text(
                  '資料管理',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textDark,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // 清除資料選項
            _buildDataManagementItem(
              title: '清除所有資料',
              subtitle: '清除所有儲存的出生資料、設定和偏好',
              icon: Icons.delete_forever,
              color: Colors.red,
              onTap: () => _showClearDataConfirmDialog(context),
              isDestructive: true,
            ),

            const SizedBox(height: 12),

            // 匯出資料選項
            _buildDataManagementItem(
              title: '匯出資料',
              subtitle: '將出生資料匯出為備份檔案',
              icon: Icons.file_upload,
              color: AppColors.indigoLight,
              onTap: () => _exportData(context),
            ),

            const SizedBox(height: 12),

            // 匯入資料選項
            _buildDataManagementItem(
              title: '匯入資料',
              subtitle: '從備份檔案匯入出生資料',
              icon: Icons.file_download,
              color: AppColors.indigoLight,
              onTap: () => _importData(context),
            ),

            const SizedBox(height: 16),

            // 雲端備份分隔線
            const Divider(),
            const SizedBox(height: 8),

            Row(
              children: [
                Icon(
                  Icons.cloud,
                  color: Colors.blue,
                  size: 16,
                ),
                const SizedBox(width: 8),
                Text(
                  '雲端備份',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // 雲端備份選項
            _buildDataManagementItem(
              title: '備份到雲端',
              subtitle: '將出生資料備份到雲端',
              icon: Icons.cloud_upload,
              color: Colors.blue,
              onTap: () => _backupToCloud(context),
            ),

            const SizedBox(height: 12),

            // 雲端還原選項
            _buildDataManagementItem(
              title: '從雲端還原',
              subtitle: '從雲端還原出生資料',
              icon: Icons.cloud_download,
              color: Colors.blue,
              onTap: () => _restoreFromCloud(context),
            ),

            const SizedBox(height: 16),

            if (kDebugMode) ...[
              // 系統診斷分隔線
              const Divider(),
              const SizedBox(height: 8),

              Row(
                children: [
                  Icon(
                    Icons.analytics,
                    color: Colors.green,
                    size: 16,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    '系統診斷',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // 日誌管理選項
              _buildDataManagementItem(
                title: '日誌管理',
                subtitle: '查看應用程式日誌、上傳診斷資料和管理日誌設定',
                icon: Icons.description,
                color: Colors.green,
                onTap: () => _openLogManagement(context),
              ),

              const SizedBox(height: 12),

              // Storage 診斷選項（開發模式）
              _buildDataManagementItem(
                title: 'Storage 診斷',
                subtitle: '診斷和修復 Firebase Storage 問題',
                icon: Icons.bug_report,
                color: Colors.orange,
                onTap: () => _openStorageDiagnostics(context),
              ),
            ]
          ],
        ),
      ),
    );
  }

  /// 構建資料管理項目
  Widget _buildDataManagementItem({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.05),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: color.withValues(alpha: 0.2),
          ),
        ),
        child: Row(
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: isDestructive ? Colors.red : AppColors.textDark,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: color,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  /// 構建設置項目
  Widget _buildSettingItem({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.05),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Icon(icon, color: color, size: 20),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: AppColors.textDark,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: color,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  /// 構建關於區域
  Widget _buildAboutSection(
      AsyncSnapshot<PackageInfo> snapshot, BuildContext context) {
    final String version = snapshot.hasData ? snapshot.data!.version : '未知';
    final String buildNumber =
        snapshot.hasData ? snapshot.data!.buildNumber : '未知';
    final String buildDate =
        DateFormat('yyyy-MM-dd HH:mm').format(DateTime.now());

    return StyledCard(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: AppColors.royalIndigo,
                  size: 20,
                ),
                SizedBox(width: 8),
                Text(
                  '關於應用',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textDark,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // 應用圖標和基本信息
            Row(
              children: [
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: AppColors.royalIndigo.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Image.asset(
                    'assets/images/flutter_launcher_icons.png',
                    width: 36,
                    height: 36,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'AstReal 占星應用',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: AppColors.textDark,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '版本: $version ($buildNumber)',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade700,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        '日期: $buildDate',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade700,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // 應用描述
            const Text(
              '這是一款專業的占星應用，提供全面的占星功能，包括本命盤、合盤、推運盤等多種星盤類型，以及詳細的占星分析和深入剖析。',
              style: TextStyle(
                fontSize: 14,
                color: AppColors.textDark,
                height: 1.4,
              ),
            ),

            const SizedBox(height: 16),

            // 版本檢查按鈕
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => _checkForUpdates(context),
                icon: const Icon(Icons.system_update, size: 20),
                label: const Text('檢查更新'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.royalIndigo,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ),

            const SizedBox(height: 12),

            // 版權信息
            const Text(
              '© 2025 AstReal. 保留所有權利。',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 構建開發者選項
  Widget _buildDeveloperSection() {
    return StyledCard(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.developer_mode,
                  color: Colors.orange,
                  size: 20,
                ),
                const SizedBox(width: 8),
                const Text(
                  '開發者選項',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textDark,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildSettingItem(
              title: '調試信息',
              subtitle: '查看應用調試信息和日誌',
              icon: Icons.bug_report,
              color: Colors.orange,
              onTap: () => _showDebugInfo(),
            ),
            const SizedBox(height: 12),
            _buildSettingItem(
              title: '測試功能',
              subtitle: '訪問實驗性功能和測試選項',
              icon: Icons.science,
              color: Colors.purple,
              onTap: () => _showTestFeatures(),
            ),
          ],
        ),
      ),
    );
  }

  // 以下是各種功能的實現方法

  /// 檢查版本更新（使用 Remote Config）
  Future<void> _checkForUpdates(BuildContext context) async {
    // 顯示載入指示器
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(
        child: CircularProgressIndicator(),
      ),
    );

    try {
      logger.i('手動檢查版本更新（使用 Remote Config）...');

      // 使用新的 Remote Config 版本服務
      final status = await RemoteConfigVersionService.checkForUpdates();

      // 關閉載入指示器
      if (context.mounted) {
        Navigator.of(context).pop();
      }

      if (!context.mounted) return;

      if (status.isSuccess) {
        if (status.needsUpdate && status.latestVersion != null) {
          // 有更新可用
          await UpdateDialog.show(
            context,
            versionInfo: status.latestVersion!,
            isForceUpdate: status.isForceUpdate,
            onLater: () {
              logger.i('用戶選擇稍後更新');
            },
            onUpdate: () {
              logger.i('用戶選擇立即更新');
            },
          );
        } else {
          // 已是最新版本
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('您的應用已是最新版本'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        // 檢查失敗，嘗試使用舊的版本檢查服務作為降級方案
        logger.w('Remote Config 版本檢查失敗，嘗試使用舊服務: ${status.errorMessage}');
      }
    } catch (e) {
      logger.e('檢查版本更新時發生錯誤: $e');

      // 關閉載入指示器
      if (context.mounted) {
        Navigator.of(context).pop();
      }

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('版本檢查失敗：$e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// 顯示清除資料確認對話框
  Future<void> _showClearDataConfirmDialog(BuildContext context) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('確認清除所有資料'),
          content: const SingleChildScrollView(
            child: ListBody(
              children: [
                Text('此操作將清除所有儲存的資料與設定，包含：'),
                SizedBox(height: 8),
                Text('- 所有出生資料'),
                Text('- 星盤設定與偏好'),
                Text('- 用戶資訊'),
                SizedBox(height: 8),
                Text(
                  '此操作不可還原，確定要繼續嗎？',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.red,
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              child: const Text('取消'),
              onPressed: () => Navigator.of(context).pop(),
            ),
            TextButton(
              child: const Text('確認清除', style: TextStyle(color: Colors.red)),
              onPressed: () async {
                Navigator.of(context).pop();
                await _performClearData(context);
              },
            ),
          ],
        );
      },
    );
  }

  /// 執行清除資料
  Future<void> _performClearData(BuildContext context) async {
    // 顯示載入中對話框
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return const AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('正在清除資料...'),
            ],
          ),
        );
      },
    );

    try {
      // 清除所有出生資料
      final birthDataService = BirthDataService();
      await birthDataService.saveAllBirthData([]);

      // 清除 FilesViewModel 中的資料
      final filesViewModel =
          Provider.of<FilesViewModel>(context, listen: false);
      filesViewModel.clearAllData();

      // 清除設定和偏好
      final settingsViewModel =
          Provider.of<SettingsViewModel>(context, listen: false);
      await settingsViewModel.resetToDefaults();

      // 清除其他相關的 SharedPreferences 資料
      await _clearAllSharedPreferences();

      Navigator.of(context).pop(); // 關閉載入對話框

      // 顯示成功訊息
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('所有資料已清除'),
          backgroundColor: AppColors.royalIndigo,
        ),
      );
    } catch (e) {
      Navigator.of(context).pop(); // 關閉載入對話框

      logger.e('清除資料失敗: $e');
      // 顯示錯誤訊息
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('清除資料失敗: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// 清除所有 SharedPreferences 資料
  Future<void> _clearAllSharedPreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // 獲取所有鍵值
      final keys = prefs.getKeys();

      // 清除所有資料，但保留一些系統必要的設定
      final systemKeys = {
        'flutter.first_run', // Flutter 系統標記
        'flutter.inspector.structuredErrors', // Flutter 調試相關
      };

      for (final key in keys) {
        if (!systemKeys.contains(key)) {
          await prefs.remove(key);
        }
      }

      logger.i('已清除 ${keys.length - systemKeys.length} 個 SharedPreferences 項目');
    } catch (e) {
      logger.e('清除 SharedPreferences 失敗: $e');
      rethrow;
    }
  }

  /// 匯出資料
  Future<void> _exportData(BuildContext context) async {
    try {
      // 獲取所有出生資料
      final birthDataService = BirthDataService();
      final allBirthData = await birthDataService.getAllBirthData();

      if (allBirthData.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('沒有可匯出的出生資料'),
            backgroundColor: Colors.orange,
          ),
        );
        return;
      }

      // 顯示載入對話框
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return const AlertDialog(
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text('正在匯出資料...'),
              ],
            ),
          );
        },
      );

      // 匯出數據
      final filePath = await CsvHelper.exportBirthData(allBirthData);

      // 關閉載入對話框
      Navigator.of(context).pop();

      if (filePath.isNotEmpty) {
        // 顯示成功對話框
        showDialog(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              title: const Row(
                children: [
                  Icon(Icons.check_circle, color: Colors.green),
                  SizedBox(width: 8),
                  Text('匯出成功'),
                ],
              ),
              content: Text(
                  '已成功匯出 ${allBirthData.length} 筆出生資料為 CSV 文件。您想要分享這個文件嗎？'),
              actions: [
                TextButton(
                  child: const Text('取消'),
                  onPressed: () => Navigator.of(context).pop(),
                ),
                TextButton(
                  child: const Text('分享'),
                  onPressed: () async {
                    Navigator.of(context).pop();
                    try {
                      await CsvHelper.shareCsvFile(filePath);
                    } catch (e) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('分享失敗: $e'),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  },
                ),
              ],
            );
          },
        );
      } else {
        // Web 平台的成功提示
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('已成功匯出 ${allBirthData.length} 筆出生資料'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      // 關閉載入對話框
      Navigator.of(context).pop();

      logger.e('匯出資料失敗: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('匯出資料失敗: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// 匯入資料
  Future<void> _importData(BuildContext context) async {
    try {
      // 匯入數據
      final importedData = await CsvHelper.importBirthDataFromCsv();

      if (importedData.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('沒有匯入任何資料'),
            backgroundColor: Colors.orange,
          ),
        );
        return;
      }

      // 顯示匯入選項對話框
      final result = await showDialog<String>(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: const Text('匯入資料'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('找到 ${importedData.length} 筆出生資料，請選擇匯入方式：'),
                const SizedBox(height: 16),
                const Text('• 合併：將新資料添加到現有資料中'),
                const Text('• 替換：清除現有資料並匯入新資料'),
              ],
            ),
            actions: [
              TextButton(
                child: const Text('取消'),
                onPressed: () => Navigator.of(context).pop('cancel'),
              ),
              TextButton(
                child: const Text('合併'),
                onPressed: () => Navigator.of(context).pop('merge'),
              ),
              TextButton(
                child: const Text('替換', style: TextStyle(color: Colors.red)),
                onPressed: () => Navigator.of(context).pop('replace'),
              ),
            ],
          );
        },
      );

      if (result == null || result == 'cancel') {
        return;
      }

      // 顯示載入對話框
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return const AlertDialog(
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text('正在匯入資料...'),
              ],
            ),
          );
        },
      );

      final birthDataService = BirthDataService();

      if (result == 'replace') {
        // 替換模式：清除現有資料並匯入新資料
        await birthDataService.saveAllBirthData(importedData);
      } else {
        // 合併模式：將新資料添加到現有資料中
        for (final data in importedData) {
          await birthDataService.addBirthData(data);
        }
      }

      // 更新 FilesViewModel
      final filesViewModel =
          Provider.of<FilesViewModel>(context, listen: false);
      await filesViewModel.loadBirthData();

      // 關閉載入對話框
      Navigator.of(context).pop();

      // 顯示成功訊息
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('成功匯入 ${importedData.length} 筆出生資料'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      // 關閉載入對話框
      Navigator.of(context).pop();

      logger.e('匯入資料失敗: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('匯入資料失敗: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// 顯示主題設置
  void _showThemeSettings() {
    // 實現主題設置
  }

  /// 顯示語言設置
  void _showLanguageSettings() {
    // 實現語言設置
  }

  /// 顯示通知設置
  void _showNotificationSettings() {
    // 實現通知設置
  }

  /// 顯示調試信息
  void _showDebugInfo() {
    // 實現調試信息顯示
  }

  /// 顯示測試功能
  void _showTestFeatures() {
    // 實現測試功能
  }

  /// 備份到雲端
  Future<void> _backupToCloud(BuildContext context) async {
    // 檢查用戶是否已登入
    final currentUser = AuthService.getCurrentUser();
    if (currentUser == null) {
      _showErrorDialog(context, '請先登入', '需要登入才能使用雲端備份功能');
      return;
    }

    // 顯示確認對話框
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('雲端備份'),
        content: const Text('確定要將出生資料備份到雲端嗎？\n\n這將會覆蓋之前的雲端備份。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('確定'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    // 顯示載入對話框
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('正在備份到雲端...'),
          ],
        ),
      ),
    );

    try {
      // 執行備份
      final success = await FirebaseStorageBackupService.backupBirthData();

      // 關閉載入對話框
      Navigator.of(context).pop();

      if (success) {
        _showSuccessDialog(context, '備份成功', '出生資料已成功備份到雲端');
      } else {
        _showErrorDialog(context, '備份失敗', '無法備份到雲端，請稍後再試');
      }
    } catch (e) {
      // 關閉載入對話框
      Navigator.of(context).pop();
      _showErrorDialog(context, '備份失敗', '發生錯誤：$e');
    }
  }

  /// 從雲端還原
  Future<void> _restoreFromCloud(BuildContext context) async {
    // 檢查用戶是否已登入
    final currentUser = AuthService.getCurrentUser();
    if (currentUser == null) {
      _showErrorDialog(context, '請先登入', '需要登入才能使用雲端還原功能');
      return;
    }

    // 檢查是否存在雲端備份
    final backupInfo = await FirebaseStorageBackupService.getBackupInfo();
    if (backupInfo == null) {
      _showNoBackupDialog(context, currentUser);
      return;
    }

    // 檢查是否是連接問題
    final note = backupInfo['note'] as String?;
    if (note == 'firebase_storage_connection_unavailable' ||
        note == 'connection_unavailable_assume_no_backup') {
      _showConnectionErrorDialog(context);
      return;
    }

    // 檢查備份是否真的存在
    final exists = backupInfo['exists'] as bool? ?? false;
    if (!exists) {
      _showNoBackupDialog(context, currentUser);
      return;
    }

    // 顯示備份資訊和確認對話框
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('從雲端還原'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('找到雲端備份：'),
            const SizedBox(height: 8),
            Text(
                '大小：${FirebaseStorageBackupService.formatFileSize(backupInfo['size'] ?? 0)}'),
            Text(
                '建立時間：${FirebaseStorageBackupService.formatDateTime(backupInfo['timeCreated'])}'),
            if (backupInfo['customMetadata'] != null)
              Text(
                  '資料筆數：${backupInfo['customMetadata']['birth_data_count'] ?? '未知'}'),
            const SizedBox(height: 16),
            const Text(
              '確定要從雲端還原出生資料嗎？\n\n這將會覆蓋目前的本地資料。',
              style: TextStyle(color: Colors.orange),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.orange),
            child: const Text('確定還原'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    // 顯示載入對話框
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('正在從雲端還原...'),
          ],
        ),
      ),
    );

    try {
      // 記錄還原前的資料數量
      final prefs = await SharedPreferences.getInstance();
      final String? beforeDataJson = prefs.getString('birthDataList');
      int beforeCount = 0;
      if (beforeDataJson != null) {
        final List<dynamic> beforeData = jsonDecode(beforeDataJson);
        beforeCount = beforeData.length;
      }

      // 執行還原
      final success = await FirebaseStorageBackupService.restoreBirthData();

      // 關閉載入對話框
      Navigator.of(context).pop();

      if (success) {
        // 更新 FilesViewModel
        final filesViewModel =
            Provider.of<FilesViewModel>(context, listen: false);
        await filesViewModel.loadBirthData();

        // 獲取詳細的還原報告
        final report = FirebaseStorageBackupService.getLastRestoreReport();

        String message = '出生資料已成功從雲端還原\n\n';
        if (report != null) {
          message += '還原結果：\n';
          message += '• 總計解析：${report['totalParsed']} 筆\n';
          message += '• 成功處理：${report['processedCount']} 筆\n';
          message += '• 實際新增：${report['actualAddedCount']} 筆\n';
          if (report['failCount'] > 0) {
            message += '• 失敗跳過：${report['failCount']} 筆\n';
          }
          message +=
              '• 備份檔案大小：${(report['csvSize'] / 1024).toStringAsFixed(1)} KB\n';

          final duplicateCount =
              report['processedCount'] - report['actualAddedCount'];
          if (duplicateCount > 0) {
            message += '• 重複資料：$duplicateCount 筆（已更新）\n';
          }

          message += '\n資料庫狀態：\n';
          message += '• 還原前：${report['beforeCount']} 筆\n';
          message += '• 還原後：${report['afterCount']} 筆\n';

          if (report['actualAddedCount'] > 0) {
            message += '\n新增的資料包含：\n';
            final names = report['successfulNames'] as List;
            for (int i = 0; i < names.length && i < 5; i++) {
              message += '• ${names[i]}\n';
            }
            if (names.length > 5) {
              message += '• ... 還有 ${names.length - 5} 筆\n';
            }
          }
        } else {
          // 計算還原後的資料數量（備用方法）
          final String? afterDataJson = prefs.getString('birthDataList');
          int afterCount = 0;
          if (afterDataJson != null) {
            final List<dynamic> afterData = jsonDecode(afterDataJson);
            afterCount = afterData.length;
          }
          int restoredCount = afterCount - beforeCount;
          message += '還原前：$beforeCount 筆\n';
          message += '還原後：$afterCount 筆\n';
          message += '新增：$restoredCount 筆';
        }

        _showRestoreSuccessDialog(context, message);
      } else {
        final report = FirebaseStorageBackupService.getLastRestoreReport();
        String errorMessage = '無法從雲端還原資料。\n\n';

        if (report != null) {
          errorMessage += '詳細資訊：\n';
          errorMessage += '• 解析到 ${report['totalParsed']} 筆資料\n';
          errorMessage += '• 成功 ${report['successCount']} 筆\n';
          errorMessage += '• 失敗 ${report['failCount']} 筆\n';
          if (report['totalParsed'] == 0) {
            errorMessage += '\n可能原因：\n• 備份檔案格式錯誤\n• 備份檔案為空\n';
          }
        } else {
          errorMessage += '可能原因：\n• 雲端備份檔案不存在\n• 備份檔案格式錯誤\n• 網路連接問題\n';
        }

        errorMessage += '\n請檢查日誌以獲取更多詳細資訊';
        _showErrorDialog(context, '還原失敗', errorMessage);
      }
    } catch (e) {
      // 關閉載入對話框
      Navigator.of(context).pop();
      _showErrorDialog(context, '還原失敗', '發生錯誤：$e\n\n請檢查網路連接和登入狀態');
    }
  }

  /// 顯示成功對話框
  void _showSuccessDialog(BuildContext context, String title, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            const Icon(Icons.check_circle, color: Colors.green),
            const SizedBox(width: 8),
            Text(title),
          ],
        ),
        content: Text(message),
        actions: [
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('確定'),
          ),
        ],
      ),
    );
  }

  /// 顯示錯誤對話框
  void _showErrorDialog(BuildContext context, String title, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            const Icon(Icons.error, color: Colors.red),
            const SizedBox(width: 8),
            Text(title),
          ],
        ),
        content: Text(message),
        actions: [
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('確定'),
          ),
        ],
      ),
    );
  }

  /// 顯示還原成功對話框（包含詳細資訊）
  void _showRestoreSuccessDialog(BuildContext context, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.cloud_download, color: Colors.green),
            SizedBox(width: 8),
            Text('還原成功'),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(message),
              SizedBox(height: 16),
              Container(
                padding: EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.green.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.green.shade200),
                ),
                child: Row(
                  children: [
                    Icon(Icons.info_outline,
                        color: Colors.green.shade700, size: 16),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        '資料已成功還原到本地儲存',
                        style: TextStyle(
                          color: Colors.green.shade700,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        actions: [
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('確定'),
          ),
        ],
      ),
    );
  }

  /// 顯示沒有備份的對話框
  void _showNoBackupDialog(BuildContext context, dynamic currentUser) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.cloud_off, color: Colors.orange),
            SizedBox(width: 8),
            Text('沒有找到雲端備份'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('在雲端中沒有找到您的備份檔案。'),
            const SizedBox(height: 16),
            const Text('可能的原因：'),
            const SizedBox(height: 8),
            const Text('• 您還沒有執行過雲端備份'),
            const Text('• 使用了不同的帳戶登入'),
            const Text('• 備份檔案已被刪除'),
            const Text('• 網路連接問題'),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '💡 建議：',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 4),
                  const Text('1. 先執行「備份到雲端」'),
                  const Text('2. 確認使用正確的帳戶'),
                  const Text('3. 檢查網路連接'),
                ],
              ),
            ),
            const SizedBox(height: 12),
            Text(
              '當前帳戶：${currentUser?.email ?? '未知'}',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('確定'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // 導航到備份功能
              _backupToCloud(context);
            },
            child: const Text('立即備份'),
          ),
        ],
      ),
    );
  }

  /// 顯示連接錯誤對話框
  void _showConnectionErrorDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.cloud_off, color: Colors.red),
            SizedBox(width: 8),
            Text('雲端服務連接問題'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('無法連接到 Firebase Storage 雲端服務。'),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '🔧 可能的解決方案：',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 4),
                  Text('1. 檢查網路連接'),
                  Text('2. 稍後再試'),
                  Text('3. 重新啟動應用程式'),
                  Text('4. 清除瀏覽器快取（Web 版）'),
                ],
              ),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '💡 替代方案：',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 4),
                  Text('• 使用「匯出 CSV 檔案」備份資料'),
                  Text('• 使用「匯入 CSV 檔案」還原資料'),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('確定'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // 可以重新嘗試連接
              _restoreFromCloud(context);
            },
            child: const Text('重試'),
          ),
        ],
      ),
    );
  }

  /// 打開日誌管理頁面
  void _openLogManagement(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const LogManagementPage(),
      ),
    );
  }

  /// 打開 Storage 診斷頁面
  void _openStorageDiagnostics(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const StorageDiagnosticsPage(),
      ),
    );
  }
}
