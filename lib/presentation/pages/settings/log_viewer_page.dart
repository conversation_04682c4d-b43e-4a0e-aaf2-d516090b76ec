import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';

import '../../../astreal.dart';

/// 日誌查看器頁面
class LogViewerPage extends StatefulWidget {
  const LogViewerPage({super.key});

  @override
  State<LogViewerPage> createState() => _LogViewerPageState();
}

class _LogViewerPageState extends State<LogViewerPage> {
  String _logContent = '';
  bool _isLoading = true;
  bool _isAutoRefresh = false;
  String _selectedLogLevel = 'ALL';
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  Timer? _refreshTimer;
  DateTime? _lastModified;

  final List<String> _logLevels = ['ALL', 'DEBUG', 'INFO', 'WARNING', 'ERROR'];
  
  @override
  void initState() {
    super.initState();
    _loadLogContent();
  }

  @override
  void dispose() {
    _refreshTimer?.cancel();
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  /// 載入日誌內容
  Future<void> _loadLogContent() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // 嘗試獲取應用程式文檔目錄
      Directory? directory;
      try {
        directory = await getApplicationDocumentsDirectory();
      } catch (e) {
        logger.w('無法獲取應用程式文檔目錄: $e');
        // 如果 path_provider 失敗，嘗試使用替代方案
        setState(() {
          _logContent = '無法訪問日誌文件：path_provider 插件未正確初始化\n\n'
              '這通常發生在以下情況：\n'
              '1. 應用程式在模擬器上運行\n'
              '2. 插件需要重新初始化\n'
              '3. 平台特定的實現缺失\n\n'
              '建議解決方案：\n'
              '• 在真實設備上測試\n'
              '• 重新安裝應用程式\n'
              '• 檢查插件配置\n\n'
              '錯誤詳情：$e';
          _isLoading = false;
        });
        return;
      }

      final logFile = File('${directory.path}/logs/astreal_app.log');

      if (await logFile.exists()) {
        final stat = await logFile.stat();

        // 只有在文件被修改時才重新讀取
        if (_lastModified == null || stat.modified.isAfter(_lastModified!)) {
          try {
            // 嘗試使用 UTF-8 編碼讀取
            final bytes = await logFile.readAsBytes();
            final content = utf8.decode(bytes, allowMalformed: true); // ⬅️ 關鍵！

            // 添加調試信息
            logger.d('成功讀取日誌文件，內容長度: ${content.length}');
            logger.d('文件前100個字符: ${content.length > 100 ? content.substring(0, 100) : content}');

            setState(() {
              _logContent = content;
              _lastModified = stat.modified;
              _isLoading = false;
            });
          } catch (e) {
            // 如果 UTF-8 解碼失敗，嘗試使用字節讀取並處理
            logger.w('UTF-8 解碼失敗，嘗試字節讀取: $e');
            try {
              final bytes = await logFile.readAsBytes();
              final decoded = utf8.decode(bytes, allowMalformed: true); // ⬅️ 關鍵！

              setState(() {
                _logContent = decoded;
                _lastModified = stat.modified;
                _isLoading = false;
              });
            } catch (e2) {
              logger.e('讀取失敗: $e2');
            }
          }
        } else {
          setState(() {
            _isLoading = false;
          });
        }
      } else {
        setState(() {
          _logContent = '日誌文件不存在或尚未創建\n\n'
              '日誌文件路徑：${directory?.path ?? '未知'}/logs/astreal_app.log\n\n'
              '如果這是首次使用，日誌文件會在應用程式產生日誌時自動創建。\n'
              '請嘗試使用應用程式的其他功能，然後重新整理此頁面。';
          _isLoading = false;
        });
      }
    } catch (e) {
      logger.e('載入日誌內容失敗: $e');
      setState(() {
        _logContent = '載入日誌失敗\n\n'
            '錯誤詳情：$e\n\n'
            '可能的原因：\n'
            '• 文件系統權限問題\n'
            '• 日誌文件損壞\n'
            '• 存儲空間不足\n\n'
            '建議嘗試：\n'
            '• 重新啟動應用程式\n'
            '• 清除應用程式快取\n'
            '• 檢查設備存儲空間';
        _isLoading = false;
      });
    }
  }

  /// 開始自動刷新
  void _startAutoRefresh() {
    _refreshTimer?.cancel();
    _refreshTimer = Timer.periodic(const Duration(seconds: 2), (timer) {
      _loadLogContent();
    });
    setState(() {
      _isAutoRefresh = true;
    });
  }

  /// 停止自動刷新
  void _stopAutoRefresh() {
    _refreshTimer?.cancel();
    setState(() {
      _isAutoRefresh = false;
    });
  }

  /// 分享日誌內容
  void _shareLogContent() async {
    final filteredLines = _getFilteredLogLines();
    final content = filteredLines.join('\n');

    if (content.isNotEmpty) {
      await Share.share(
        content,
        subject: 'AstReal 應用程式日誌',
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('沒有日誌內容可分享')),
      );
    }
  }

  /// 過濾日誌內容
  List<String> _getFilteredLogLines() {
    if (_logContent.isEmpty) return [];
    
    final lines = _logContent.split('\n');
    List<String> filteredLines = lines;
    
    // 按日誌級別過濾
    if (_selectedLogLevel != 'ALL') {
      filteredLines = filteredLines.where((line) {
        return line.contains('[$_selectedLogLevel]');
      }).toList();
    }
    
    // 按搜尋查詢過濾
    if (_searchQuery.isNotEmpty) {
      filteredLines = filteredLines.where((line) {
        return line.toLowerCase().contains(_searchQuery.toLowerCase());
      }).toList();
    }
    
    return filteredLines;
  }

  /// 獲取日誌行的顏色
  Color _getLogLineColor(String line) {
    if (line.contains('[ERROR]')) return Colors.red.shade700;
    if (line.contains('[WARNING]')) return Colors.orange.shade700;
    if (line.contains('[INFO]')) return Colors.blue.shade700;
    if (line.contains('[DEBUG]')) return Colors.grey.shade600;
    return Colors.black87;
  }

  /// 獲取日誌行的圖標
  IconData _getLogLineIcon(String line) {
    if (line.contains('[ERROR]')) return Icons.error;
    if (line.contains('[WARNING]')) return Icons.warning;
    if (line.contains('[INFO]')) return Icons.info;
    if (line.contains('[DEBUG]')) return Icons.bug_report;
    return Icons.notes;
  }

  /// 複製日誌內容
  void _copyLogContent() {
    final filteredLines = _getFilteredLogLines();
    final content = filteredLines.join('\n');
    
    Clipboard.setData(ClipboardData(text: content));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('日誌內容已複製到剪貼板')),
    );
  }

  /// 清空日誌
  void _clearLogs() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('確認清空'),
        content: const Text('確定要清空所有日誌嗎？此操作無法復原。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('確認'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        // 嘗試獲取應用程式文檔目錄
        Directory? directory;
        try {
          directory = await getApplicationDocumentsDirectory();
        } catch (e) {
          logger.w('無法獲取應用程式文檔目錄: $e');
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('清空失敗：無法訪問文件系統 ($e)')),
            );
          }
          return;
        }

        final logFile = File('${directory.path}/logs/astreal_app.log');

        if (await logFile.exists()) {
          try {
            // 確保寫入有效的 UTF-8 內容
            final clearContent = '# AstReal App Logs (Cleared)\n# Created: ${DateTime.now().toIso8601String()}\n# Encoding: UTF-8\n\n';
            await logFile.writeAsString(clearContent);
            await _loadLogContent();

            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('日誌已清空')),
              );
            }
          } catch (e) {
            logger.e('寫入清空日誌失敗: $e');
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('清空失敗：寫入錯誤 ($e)')),
              );
            }
          }
        } else {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('日誌文件不存在，無需清空')),
            );
          }
        }
      } catch (e) {
        logger.e('清空日誌失敗: $e');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('清空失敗: $e')),
          );
        }
      }
    }
  }

  /// 滾動到底部
  void _scrollToBottom() {
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        _scrollController.position.maxScrollExtent,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }

  /// 滾動到頂部
  void _scrollToTop() {
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        0,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final filteredLines = _getFilteredLogLines();
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('日誌查看器'),
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadLogContent,
            tooltip: '重新載入',
          ),
          IconButton(
            icon: const Icon(Icons.copy),
            onPressed: _copyLogContent,
            tooltip: '複製日誌',
          ),
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: _shareLogContent,
            tooltip: '分享日誌',
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'clear':
                  _clearLogs();
                  break;
                case 'scroll_top':
                  _scrollToTop();
                  break;
                case 'scroll_bottom':
                  _scrollToBottom();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'scroll_top',
                child: Row(
                  children: [
                    Icon(Icons.vertical_align_top),
                    SizedBox(width: 8),
                    Text('滾動到頂部'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'scroll_bottom',
                child: Row(
                  children: [
                    Icon(Icons.vertical_align_bottom),
                    SizedBox(width: 8),
                    Text('滾動到底部'),
                  ],
                ),
              ),
              const PopupMenuDivider(),
              const PopupMenuItem(
                value: 'clear',
                child: Row(
                  children: [
                    Icon(Icons.clear_all, color: Colors.red),
                    SizedBox(width: 8),
                    Text('清空日誌', style: TextStyle(color: Colors.red)),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          // 過濾器和搜尋欄
          _buildFilterBar(),
          
          // 統計信息
          _buildStatsBar(filteredLines),
          
          // 日誌內容
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _buildLogContent(filteredLines),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _isAutoRefresh ? _stopAutoRefresh : _startAutoRefresh,
        backgroundColor: _isAutoRefresh ? Colors.red : Colors.green,
        tooltip: _isAutoRefresh ? '停止自動刷新' : '開始自動刷新',
        child: Icon(_isAutoRefresh ? Icons.pause : Icons.play_arrow),
      ),
    );
  }

  Widget _buildFilterBar() {
    return StyledCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // 日誌級別選擇
            Row(
              children: [
                const Text('級別: ', style: TextStyle(fontWeight: FontWeight.bold)),
                Expanded(
                  child: Wrap(
                    spacing: 8,
                    children: _logLevels.map((level) {
                      final isSelected = _selectedLogLevel == level;
                      return FilterChip(
                        label: Text(level),
                        selected: isSelected,
                        onSelected: (selected) {
                          setState(() {
                            _selectedLogLevel = level;
                          });
                        },
                        selectedColor: Theme.of(context).primaryColor.withValues(alpha: 0.2),
                      );
                    }).toList(),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            
            // 搜尋欄
            TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: '搜尋日誌內容...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchQuery.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          setState(() {
                            _searchQuery = '';
                          });
                        },
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatsBar(List<String> filteredLines) {
    final totalLines = _logContent.split('\n').length;
    final errorCount = filteredLines.where((line) => line.contains('[ERROR]')).length;
    final warningCount = filteredLines.where((line) => line.contains('[WARNING]')).length;
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      color: Colors.grey.shade100,
      child: Row(
        children: [
          _buildStatChip('總計', totalLines.toString(), Colors.blue),
          const SizedBox(width: 8),
          _buildStatChip('顯示', filteredLines.length.toString(), Colors.green),
          const SizedBox(width: 8),
          _buildStatChip('錯誤', errorCount.toString(), Colors.red),
          const SizedBox(width: 8),
          _buildStatChip('警告', warningCount.toString(), Colors.orange),
        ],
      ),
    );
  }

  Widget _buildStatChip(String label, String count, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(width: 4),
          Text(
            count,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLogContent(List<String> filteredLines) {
    if (filteredLines.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.inbox, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              '沒有找到符合條件的日誌',
              style: TextStyle(fontSize: 16, color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.all(16),
      itemCount: filteredLines.length,
      itemBuilder: (context, index) {
        final line = filteredLines[index];
        if (line.trim().isEmpty) return const SizedBox(height: 4);
        
        return _buildLogLine(line, index);
      },
    );
  }

  Widget _buildLogLine(String line, int index) {
    final color = _getLogLineColor(line);
    final icon = _getLogLineIcon(line);
    
    return Container(
      margin: const EdgeInsets.only(bottom: 2),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 行號
          SizedBox(
            width: 40,
            child: Text(
              '${index + 1}',
              style: TextStyle(
                fontSize: 10,
                color: Colors.grey.shade500,
                fontFamily: 'monospace',
              ),
            ),
          ),
          
          // 日誌級別圖標
          Icon(
            icon,
            size: 16,
            color: color,
          ),
          const SizedBox(width: 8),
          
          // 日誌內容
          Expanded(
            child: SelectableText(
              line,
              style: TextStyle(
                fontSize: 12,
                color: color,
                fontFamily: 'monospace',
                height: 1.4,
              ),
              // 禁用系統上下文菜單以避免 Flutter 框架錯誤
              contextMenuBuilder: (context, editableTextState) {
                return const SizedBox.shrink(); // 返回空組件，禁用上下文菜單
              },
            ),
          ),
        ],
      ),
    );
  }
}
