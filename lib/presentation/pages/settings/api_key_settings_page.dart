import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../../astreal.dart';
import '../../../data/services/api/ai_api_service.dart';
import '../../../data/services/api/remote_config_service.dart';
import '../../../shared/utils/remote_config_diagnostic.dart';
import 'groq_models_info_page.dart';

/// API Key 設置頁面
class ApiKeySettingsPage extends StatefulWidget {
  const ApiKeySettingsPage({super.key});

  @override
  State<ApiKeySettingsPage> createState() => _ApiKeySettingsPageState();
}

class _ApiKeySettingsPageState extends State<ApiKeySettingsPage> {
  final _openaiController = TextEditingController();
  final _anthropicController = TextEditingController();
  final _groqController = TextEditingController();
  final _geminiController = TextEditingController();
  bool _isLoading = true;
  bool _openaiObscured = true;
  bool _anthropicObscured = true;
  bool _groqObscured = true;
  bool _geminiObscured = true;

  // Remote Config 相關狀態
  Map<String, dynamic>? _remoteConfigStatus;
  bool _showRemoteConfigKeys = false;

  @override
  void initState() {
    super.initState();
    _loadApiKeys();
  }

  @override
  void dispose() {
    _openaiController.dispose();
    _anthropicController.dispose();
    _groqController.dispose();
    _geminiController.dispose();
    super.dispose();
  }

  Future<void> _loadApiKeys() async {
    try {
      // 獲取 Remote Config 狀態
      _remoteConfigStatus = RemoteConfigDiagnostic.getConfigSummary();

      // 優先從 Remote Config 獲取 API Keys
      String openaiKey = '';
      String anthropicKey = '';
      String groqKey = '';
      String geminiKey = '';

      if (_remoteConfigStatus?['isInitialized'] == true) {
        // 從 Remote Config 獲取
        openaiKey = RemoteConfigService.getOpenAIKey();
        groqKey = RemoteConfigService.getGroqAIKey();
        geminiKey = RemoteConfigService.getGoogleGeminiKey();

        // Anthropic 暫時從本地獲取（Remote Config 中沒有）
        anthropicKey = await AIApiService.getAnthropicApiKey() ?? '';
      } else {
        // 降級到本地存儲
        openaiKey = await AIApiService.getOpenAIApiKey() ?? '';
        anthropicKey = await AIApiService.getAnthropicApiKey() ?? '';
        groqKey = await AIApiService.getGroqApiKey() ?? '';
        geminiKey = await AIApiService.getGeminiApiKey() ?? '';
      }

      setState(() {
        _openaiController.text = openaiKey;
        _anthropicController.text = anthropicKey;
        _groqController.text = groqKey;
        _geminiController.text = geminiKey;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('載入 API Key 失敗: $e')),
        );
      }
    }
  }

  /// 刷新 Remote Config 並重新載入 API Keys
  Future<void> _refreshRemoteConfig() async {
    try {
      final updated = await RemoteConfigService.refresh();
      await _loadApiKeys();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(updated ? 'Remote Config 已更新' : 'Remote Config 無新更新'),
            backgroundColor: updated ? AppColors.successGreen : AppColors.royalIndigo,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('刷新 Remote Config 失敗: $e')),
        );
      }
    }
  }

  Future<void> _saveOpenAIKey() async {
    try {
      await AIApiService.setOpenAIApiKey(_openaiController.text.trim());
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('OpenAI API Key 已保存'),
            backgroundColor: AppColors.royalIndigo,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('保存失敗: $e')),
        );
      }
    }
  }

  Future<void> _saveAnthropicKey() async {
    try {
      await AIApiService.setAnthropicApiKey(_anthropicController.text.trim());
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Anthropic API Key 已保存'),
            backgroundColor: AppColors.royalIndigo,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('保存失敗: $e')),
        );
      }
    }
  }

  Future<void> _saveGroqKey() async {
    try {
      await AIApiService.setGroqApiKey(_groqController.text.trim());
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Groq API Key 已保存'),
            backgroundColor: AppColors.royalIndigo,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('保存失敗: $e')),
        );
      }
    }
  }

  Future<void> _saveGeminiKey() async {
    try {
      await AIApiService.setGeminiApiKey(_geminiController.text.trim());
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Gemini API Key 已保存'),
            backgroundColor: AppColors.royalIndigo,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('保存失敗: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('API Key 設置'),
        backgroundColor: AppColors.royalIndigo,
        foregroundColor: Colors.white,
        actions: [
          // Remote Config 狀態指示器
          if (_remoteConfigStatus != null) ...[
            IconButton(
              icon: Icon(
                _remoteConfigStatus!['isInitialized'] == true
                    ? Icons.cloud_done
                    : Icons.cloud_off,
                color: _remoteConfigStatus!['isInitialized'] == true
                    ? AppColors.successGreen
                    : Colors.orange,
              ),
              onPressed: () {
                setState(() {
                  _showRemoteConfigKeys = !_showRemoteConfigKeys;
                });
              },
              tooltip: _remoteConfigStatus!['isInitialized'] == true
                  ? 'Remote Config 已連接'
                  : 'Remote Config 未連接',
            ),
            // 刷新按鈕
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: _refreshRemoteConfig,
              tooltip: '刷新 Remote Config',
            ),
          ],
        ],
      ),
      body: ListView(
        padding: const EdgeInsets.all(16.0),
        children: [
          // 頁面說明
          _buildPageDescription(),
          const SizedBox(height: 16),
          
          // OpenAI API Key 設置
          _buildOpenAISection(),
          // const SizedBox(height: 24),
          
          // Anthropic API Key 設置
          _buildAnthropicSection(),
          // const SizedBox(height: 24),

          // Groq API Key 設置
          _buildGroqSection(),
          // const SizedBox(height: 24),

          // Gemini API Key 設置
          _buildGeminiSection(),
          // const SizedBox(height: 24),

          // 使用說明
          _buildUsageInstructions(),
        ],
      ),
    );
  }

  Widget _buildPageDescription() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.royalIndigo.withValues(alpha: 0.1),
            AppColors.solarAmber.withValues(alpha: 0.1),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.royalIndigo.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(
                Icons.key,
                color: AppColors.royalIndigo,
                size: 24,
              ),
              SizedBox(width: 8),
              Text(
                'API Key 管理',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppColors.royalIndigo,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            '設置您的 AI API Key 以啟用星盤解讀功能。',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade700,
              height: 1.5,
            ),
          ),
          const SizedBox(height: 8),
          // Remote Config 狀態信息
          if (_remoteConfigStatus != null) ...[
            Row(
              children: [
                Icon(
                  _remoteConfigStatus!['isInitialized'] == true
                      ? Icons.cloud_done
                      : Icons.cloud_off,
                  size: 16,
                  color: _remoteConfigStatus!['isInitialized'] == true
                      ? AppColors.successGreen
                      : Colors.orange,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    _remoteConfigStatus!['isInitialized'] == true
                        ? 'Remote Config 已連接，優先使用雲端配置'
                        : 'Remote Config 未連接，使用本地存儲',
                    style: TextStyle(
                      fontSize: 12,
                      color: _remoteConfigStatus!['isInitialized'] == true
                          ? AppColors.successGreen
                          : Colors.orange,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildOpenAISection() {
    return StyledCard(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: Colors.green.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.smart_toy,
                    color: Colors.green,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          const Text(
                            'OpenAI API Key',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: AppColors.textDark,
                            ),
                          ),
                          const SizedBox(width: 8),
                          if (_remoteConfigStatus?['isInitialized'] == true &&
                              RemoteConfigService.getOpenAIKey().isNotEmpty) ...[
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                              decoration: BoxDecoration(
                                color: AppColors.successGreen,
                                borderRadius: BorderRadius.circular(4),
                                border: Border.all(color: AppColors.successGreen, width: 0.5),
                              ),
                              child: const Text(
                                'Remote',
                                style: TextStyle(
                                  fontSize: 10,
                                  color: AppColors.successGreen,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ] else ...[
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                              decoration: BoxDecoration(
                                color: Colors.grey,
                                borderRadius: BorderRadius.circular(4),
                                border: Border.all(color: Colors.grey, width: 0.5),
                              ),
                              child: const Text(
                                'Local',
                                style: TextStyle(
                                  fontSize: 10,
                                  color: Colors.grey,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                      const Text(
                        '用於 GPT-4、GPT-3.5 等模型',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _openaiController,
              obscureText: _openaiObscured,
              decoration: InputDecoration(
                labelText: 'OpenAI API Key',
                hintText: 'sk-...',
                border: const OutlineInputBorder(),
                suffixIcon: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    IconButton(
                      icon: Icon(_openaiObscured ? Icons.visibility : Icons.visibility_off),
                      onPressed: () {
                        setState(() {
                          _openaiObscured = !_openaiObscured;
                        });
                      },
                    ),
                    IconButton(
                      icon: const Icon(Icons.content_paste),
                      onPressed: () async {
                        final data = await Clipboard.getData(Clipboard.kTextPlain);
                        if (data?.text != null) {
                          _openaiController.text = data!.text!;
                        }
                      },
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _saveOpenAIKey,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('保存'),
                  ),
                ),
                const SizedBox(width: 12),
                TextButton(
                  onPressed: () => _showApiKeyHelp(AIProvider.openai),
                  child: const Text('如何獲取？'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAnthropicSection() {
    return StyledCard(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: Colors.orange.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.psychology,
                    color: Colors.orange,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                const Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Anthropic API Key',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: AppColors.textDark,
                        ),
                      ),
                      Text(
                        '用於 Claude 3 系列模型',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _anthropicController,
              obscureText: _anthropicObscured,
              decoration: InputDecoration(
                labelText: 'Anthropic API Key',
                hintText: 'sk-ant-...',
                border: const OutlineInputBorder(),
                suffixIcon: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    IconButton(
                      icon: Icon(_anthropicObscured ? Icons.visibility : Icons.visibility_off),
                      onPressed: () {
                        setState(() {
                          _anthropicObscured = !_anthropicObscured;
                        });
                      },
                    ),
                    IconButton(
                      icon: const Icon(Icons.content_paste),
                      onPressed: () async {
                        final data = await Clipboard.getData(Clipboard.kTextPlain);
                        if (data?.text != null) {
                          _anthropicController.text = data!.text!;
                        }
                      },
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _saveAnthropicKey,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('保存'),
                  ),
                ),
                const SizedBox(width: 12),
                TextButton(
                  onPressed: () => _showApiKeyHelp(AIProvider.anthropic),
                  child: const Text('如何獲取？'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGroqSection() {
    return StyledCard(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: Colors.purple.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.flash_on,
                    color: Colors.purple,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            'Groq API Key',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: AppColors.textDark,
                            ),
                          ),
                          const SizedBox(width: 8),
                          if (_remoteConfigStatus?['isInitialized'] == true &&
                              RemoteConfigService.getGroqAIKey().isNotEmpty) ...[
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                              decoration: BoxDecoration(
                                color: AppColors.successGreen.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(4),
                                border: Border.all(color: AppColors.successGreen, width: 0.5),
                              ),
                              child: Text(
                                'Remote',
                                style: TextStyle(
                                  fontSize: 10,
                                  color: AppColors.successGreen,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ] else ...[
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                              decoration: BoxDecoration(
                                color: Colors.grey.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(4),
                                border: Border.all(color: Colors.grey, width: 0.5),
                              ),
                              child: Text(
                                'Local',
                                style: TextStyle(
                                  fontSize: 10,
                                  color: Colors.grey,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                      Text(
                        '用於 Llama、Mixtral 等模型',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _groqController,
              obscureText: _groqObscured,
              decoration: InputDecoration(
                labelText: 'Groq API Key',
                hintText: 'gsk_...',
                border: const OutlineInputBorder(),
                suffixIcon: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    IconButton(
                      icon: Icon(_groqObscured ? Icons.visibility : Icons.visibility_off),
                      onPressed: () {
                        setState(() {
                          _groqObscured = !_groqObscured;
                        });
                      },
                    ),
                    IconButton(
                      icon: const Icon(Icons.content_paste),
                      onPressed: () async {
                        final data = await Clipboard.getData(Clipboard.kTextPlain);
                        if (data?.text != null) {
                          _groqController.text = data!.text!;
                        }
                      },
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _saveGroqKey,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.purple,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('保存'),
                  ),
                ),
                const SizedBox(width: 8),
                TextButton(
                  onPressed: () => _showApiKeyHelp(AIProvider.groq),
                  child: const Text('如何獲取？'),
                ),
              ],
            ),
            const SizedBox(height: 12),
            OutlinedButton.icon(
              onPressed: () => Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const GroqModelsInfoPage(),
                ),
              ),
              icon: const Icon(Icons.info_outline, size: 16),
              label: const Text('查看 Groq 模型介紹'),
              style: OutlinedButton.styleFrom(
                foregroundColor: Colors.purple,
                side: const BorderSide(color: Colors.purple),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGeminiSection() {
    return StyledCard(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: Colors.blue.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.auto_awesome,
                    color: Colors.blue,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            'Google Gemini API Key',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: AppColors.textDark,
                            ),
                          ),
                          const SizedBox(width: 8),
                          if (_remoteConfigStatus?['isInitialized'] == true &&
                              RemoteConfigService.getGoogleGeminiKey().isNotEmpty) ...[
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                              decoration: BoxDecoration(
                                color: AppColors.successGreen.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(4),
                                border: Border.all(color: AppColors.successGreen, width: 0.5),
                              ),
                              child: Text(
                                'Remote',
                                style: TextStyle(
                                  fontSize: 10,
                                  color: AppColors.successGreen,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ] else ...[
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                              decoration: BoxDecoration(
                                color: Colors.grey.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(4),
                                border: Border.all(color: Colors.grey, width: 0.5),
                              ),
                              child: Text(
                                'Local',
                                style: TextStyle(
                                  fontSize: 10,
                                  color: Colors.grey,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                      Text(
                        '用於 Gemini 系列模型',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _geminiController,
              obscureText: _geminiObscured,
              decoration: InputDecoration(
                labelText: 'Gemini API Key',
                hintText: 'AIza...',
                border: const OutlineInputBorder(),
                suffixIcon: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    IconButton(
                      icon: Icon(_geminiObscured ? Icons.visibility : Icons.visibility_off),
                      onPressed: () {
                        setState(() {
                          _geminiObscured = !_geminiObscured;
                        });
                      },
                    ),
                    IconButton(
                      icon: const Icon(Icons.content_paste),
                      onPressed: () async {
                        final data = await Clipboard.getData(Clipboard.kTextPlain);
                        if (data?.text != null) {
                          _geminiController.text = data!.text!;
                        }
                      },
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _saveGeminiKey,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('保存'),
                  ),
                ),
                const SizedBox(width: 12),
                TextButton(
                  onPressed: () => _showApiKeyHelp(AIProvider.gemini),
                  child: const Text('如何獲取？'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUsageInstructions() {
    return StyledCard(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: AppColors.indigoLight,
                  size: 20,
                ),
                const SizedBox(width: 8),
                const Text(
                  '使用說明',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textDark,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            _buildInstructionItem(
              '安全性',
              'API Key 僅儲存在您的設備上，不會上傳到任何伺服器',
              Icons.security,
              Colors.green,
            ),
            _buildInstructionItem(
              '費用',
              '使用 AI 解讀功能會消耗您的 API 配額，請注意使用量',
              Icons.attach_money,
              Colors.orange,
            ),
            _buildInstructionItem(
              '模型選擇',
              '在 AI 模型設置中選擇您要使用的模型',
              Icons.tune,
              AppColors.royalIndigo,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInstructionItem(String title, String description, IconData icon, Color color) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Row(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showApiKeyHelp(AIProvider provider) {
    String title;
    String content;

    switch (provider) {
      case AIProvider.openai:
        title = '如何獲取 OpenAI API Key';
        content = '''
1. 前往 OpenAI 官網 (platform.openai.com)
2. 註冊或登入您的帳戶
3. 進入 API Keys 頁面
4. 點擊 "Create new secret key"
5. 複製生成的 API Key
6. 貼上到上方的輸入框中

注意：API Key 只會顯示一次，請妥善保存。
''';

        break;
      case AIProvider.anthropic:
        title = '如何獲取 Anthropic API Key';
        content = '''
1. 前往 Anthropic Console (console.anthropic.com)
2. 註冊或登入您的帳戶
3. 進入 API Keys 頁面
4. 點擊 "Create Key"
5. 複製生成的 API Key
6. 貼上到上方的輸入框中

注意：API Key 只會顯示一次，請妥善保存。
''';

        break;
      case AIProvider.groq:
        title = '如何獲取 Groq API Key';
        content = '''
1. 前往 Groq Console (console.groq.com)
2. 註冊或登入您的帳戶
3. 進入 API Keys 頁面
4. 點擊 "Create API Key"
5. 複製生成的 API Key
6. 貼上到上方的輸入框中

注意：Groq 提供免費的 API 配額，速度極快。
''';
        break;
      case AIProvider.gemini:
        title = '如何獲取 Gemini API Key';
        content = '''
1. 前往 Google AI Studio (aistudio.google.com)
2. 使用 Google 帳戶登入
3. 點擊 "Get API key" 按鈕
4. 選擇或創建一個 Google Cloud 項目
5. 複製生成的 API Key
6. 貼上到上方的輸入框中

注意：Gemini API 提供免費配額，支援多模態功能。
''';
        break;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: SingleChildScrollView(
          child: Text(content),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('關閉'),
          ),
        ],
      ),
    );
  }
}
