import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../astreal.dart';
import '../../../shared/widgets/chart_type_selector.dart';


/// 相位設置頁面
class AspectSettingsPage extends StatelessWidget {
  const AspectSettingsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('相位設置'),
        backgroundColor: AppColors.royalIndigo,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.restore),
            onPressed: () => _showResetDialog(context),
            tooltip: '重置為預設值',
          ),
        ],
      ),
      body: Consumer<SettingsViewModel>(
        builder: (context, viewModel, child) {
          if (viewModel.multiChartSettings == null) {
            return const Center(child: CircularProgressIndicator());
          }

          return SingleChildScrollView(
            padding: const EdgeInsets.fromLTRB(16.0, 16.0, 16.0, 80.0), // 大幅增加底部間距
            physics: const ClampingScrollPhysics(), // 使用 ClampingScrollPhysics 避免彈回
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
              // 頁面說明
              _buildPageDescription(),
              const SizedBox(height: 24),

              // 星盤類型選擇器
              ChartTypeSelector(
                selectedChartType: viewModel.currentChartType,
                onChartTypeChanged: (chartType) {
                  viewModel.setCurrentChartType(chartType);
                },
              ),
              const SizedBox(height: 24),

              // 相位容許度設置
              _buildAspectOrbsSection(context, viewModel),
              const SizedBox(height: 24),

              // 相位說明
              _buildAspectExplanation(),
              const SizedBox(height: 24),

              // 建議設置
              _buildRecommendedSettings(viewModel, context),
              ],
            ),
          );
        },
      ),
    );
  }

  /// 構建頁面說明
  Widget _buildPageDescription() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.royalIndigo.withValues(alpha: 0.1),
            AppColors.solarAmber.withValues(alpha: 0.1),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.royalIndigo.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.timeline,
                color: AppColors.royalIndigo,
                size: 24,
              ),
              const SizedBox(width: 8),
              const Text(
                '相位容許度設置',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppColors.royalIndigo,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            '調整各種相位的容許度範圍。不同星盤類型可以使用不同的容許度設定，例如流年盤通常使用較小的容許度，而日月蝕盤可能需要較大的容許度。',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade700,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  /// 構建相位容許度設置區域
  Widget _buildAspectOrbsSection(BuildContext context, SettingsViewModel viewModel) {
    return StyledCard(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.tune,
                  color: AppColors.solarAmber,
                  size: 20,
                ),
                const SizedBox(width: 8),
                const Text(
                  '相位容許度調整',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textDark,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...(viewModel.currentChartTypeSettings?.aspectOrbs.entries ?? {}).map((entry) {
              return _buildAspectOrbSlider(
                context,
                viewModel,
                entry.key,
                entry.value,
              );
            }).toList(),
          ],
        ),
      ),
    );
  }

  /// 構建相位容許度滑桿
  Widget _buildAspectOrbSlider(
    BuildContext context,
    SettingsViewModel viewModel,
    String aspectName,
    double currentValue,
  ) {
    final aspectInfo = _getAspectInfo(aspectName);
    
    return Container(
      margin: const EdgeInsets.only(bottom: 20),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: aspectInfo['color'].withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: aspectInfo['color'].withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                aspectInfo['icon'],
                color: aspectInfo['color'],
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  aspectName,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: aspectInfo['color'],
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: aspectInfo['color'].withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${currentValue.toInt()}°',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: aspectInfo['color'],
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            aspectInfo['description'],
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 12),
          SliderTheme(
            data: SliderTheme.of(context).copyWith(
              activeTrackColor: aspectInfo['color'],
              thumbColor: aspectInfo['color'],
              overlayColor: aspectInfo['color'].withValues(alpha: 0.2),
              inactiveTrackColor: aspectInfo['color'].withValues(alpha: 0.3),
            ),
            child: Slider(
              value: currentValue.clamp(0, 30),
              min: 0,
              max: 30,
              divisions: 30,
              label: '${currentValue.toInt()}°',
              onChanged: (newValue) {
                viewModel.updateAspectOrb(aspectName, newValue);
              },
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '0°',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade500,
                ),
              ),
              Text(
                '建議: ${aspectInfo['recommended']}°',
                style: TextStyle(
                  fontSize: 12,
                  color: aspectInfo['color'],
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                '30°',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade500,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 獲取相位信息
  Map<String, dynamic> _getAspectInfo(String aspectName) {
    switch (aspectName) {
      case '合相':
        return {
          'icon': Icons.radio_button_checked,
          'color': Colors.red,
          'description': '0° - 強烈的能量結合，行星力量融合',
          'recommended': 8,
        };
      case '對相':
        return {
          'icon': Icons.compare_arrows,
          'color': Colors.blue,
          'description': '180° - 對立與平衡，需要整合的能量',
          'recommended': 8,
        };
      case '三分相':
        return {
          'icon': Icons.change_history,
          'color': Colors.green,
          'description': '120° - 和諧流暢，天賦與機會',
          'recommended': 6,
        };
      case '四分相':
        return {
          'icon': Icons.crop_square,
          'color': Colors.orange,
          'description': '90° - 挑戰與動力，需要努力克服',
          'recommended': 6,
        };
      case '六分相':
        return {
          'icon': Icons.hexagon,
          'color': Colors.purple,
          'description': '60° - 機會與合作，需要主動把握',
          'recommended': 4,
        };
      default:
        return {
          'icon': Icons.circle,
          'color': Colors.grey,
          'description': '其他相位',
          'recommended': 4,
        };
    }
  }

  /// 構建相位說明
  Widget _buildAspectExplanation() {
    return StyledCard(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.school,
                  color: AppColors.indigoLight,
                  size: 20,
                ),
                const SizedBox(width: 8),
                const Text(
                  '相位說明',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textDark,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildAspectExplanationItem(
              '容許度的作用',
              '容許度決定了相位的精確度要求。例如，如果合相的容許度設為8°，那麼兩顆行星相距0°-8°都會被視為合相。',
              Icons.info_outline,
              AppColors.royalIndigo,
            ),
            _buildAspectExplanationItem(
              '調整建議',
              '• 初學者：使用較大容許度（8-10°）\n• 進階用戶：使用中等容許度（6-8°）\n• 專業占星師：使用較小容許度（4-6°）',
              Icons.lightbulb_outline,
              AppColors.solarAmber,
            ),
            _buildAspectExplanationItem(
              '注意事項',
              '容許度過大會產生過多相位，影響解讀精確度；容許度過小可能會遺漏重要相位。建議根據個人經驗調整。',
              Icons.warning_amber,
              Colors.orange,
            ),
          ],
        ),
      ),
    );
  }

  /// 構建相位說明項目
  Widget _buildAspectExplanationItem(
    String title,
    String content,
    IconData icon,
    Color color,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: color.withValues(alpha: 0.2),
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  content,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade700,
                    height: 1.4,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 構建建議設置
  Widget _buildRecommendedSettings(SettingsViewModel viewModel, BuildContext context) {
    return StyledCard(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.recommend,
                  color: AppColors.solarAmber,
                  size: 20,
                ),
                const SizedBox(width: 8),
                const Text(
                  '建議設置',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textDark,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            // 根據星盤類型顯示不同的預設按鈕
            if (viewModel.currentChartType == ChartType.natal) ...[
              _buildPresetButton(
                '本命盤標準設置',
                '合相: 8°, 對相: 8°, 三分相: 6°, 四分相: 6°, 六分相: 4°',
                Icons.person,
                Colors.blue,
                () => _applyPreset('natal_standard', viewModel, context),
              ),
              _buildPresetButton(
                '本命盤嚴格設置',
                '合相: 6°, 對相: 6°, 三分相: 4°, 四分相: 4°, 六分相: 3°',
                Icons.precision_manufacturing,
                Colors.blue.shade700,
                () => _applyPreset('natal_strict', viewModel, context),
              ),
            ] else if (viewModel.currentChartType == ChartType.transit) ...[
              _buildPresetButton(
                '流年盤標準設置',
                '合相: 6°, 對相: 6°, 三分相: 4°, 四分相: 4°, 六分相: 3°',
                Icons.timeline,
                Colors.green,
                () => _applyPreset('transit_standard', viewModel, context),
              ),
              _buildPresetButton(
                '流年盤精確設置',
                '合相: 4°, 對相: 4°, 三分相: 3°, 四分相: 3°, 六分相: 2°',
                Icons.precision_manufacturing,
                Colors.green.shade700,
                () => _applyPreset('transit_precise', viewModel, context),
              ),
            ] else if (viewModel.currentChartType == ChartType.eclipse) ...[
              _buildPresetButton(
                '日月蝕盤標準設置',
                '合相: 10°, 對相: 10°, 三分相: 8°, 四分相: 8°, 六分相: 5°',
                Icons.brightness_2,
                Colors.indigo,
                () => _applyPreset('eclipse_standard', viewModel, context),
              ),
              _buildPresetButton(
                '日月蝕盤寬鬆設置',
                '合相: 12°, 對相: 12°, 三分相: 10°, 四分相: 10°, 六分相: 6°',
                Icons.brightness_high,
                Colors.indigo.shade700,
                () => _applyPreset('eclipse_wide', viewModel, context),
              ),
            ] else ...[
              _buildPresetButton(
                '標準設置',
                '合相: 8°, 對相: 8°, 三分相: 6°, 四分相: 6°, 六分相: 4°',
                Icons.star,
                Colors.blue,
                () => _applyPreset('standard', viewModel, context),
              ),
              _buildPresetButton(
                '嚴格設置',
                '合相: 6°, 對相: 6°, 三分相: 4°, 四分相: 4°, 六分相: 3°',
                Icons.precision_manufacturing,
                Colors.purple,
                () => _applyPreset('strict', viewModel, context),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// 構建預設按鈕
  Widget _buildPresetButton(
    String title,
    String description,
    IconData icon,
    Color color,
    VoidCallback onPressed,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(12),
          // 確保觸摸事件不會干擾滾動
          excludeFromSemantics: false,
          // 添加觸摸行為配置
          splashFactory: InkRipple.splashFactory,
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: color.withValues(alpha: 0.2),
              ),
            ),
            child: Row(
              children: [
                Icon(icon, color: color, size: 24),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: color,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        description,
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  color: color,
                  size: 16,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 應用預設設置
  void _applyPreset(String presetType, SettingsViewModel viewModel, [BuildContext? context]) {
    Map<String, double> newOrbs = {};

    switch (presetType) {
      case 'natal_standard':
        newOrbs = {
          '合相': 8.0,
          '六分相': 4.0,
          '四分相': 6.0,
          '三分相': 6.0,
          '對分相': 8.0,
        };
        break;
      case 'natal_strict':
        newOrbs = {
          '合相': 6.0,
          '六分相': 3.0,
          '四分相': 4.0,
          '三分相': 4.0,
          '對分相': 6.0,
        };
        break;
      case 'transit_standard':
        newOrbs = {
          '合相': 6.0,
          '六分相': 3.0,
          '四分相': 4.0,
          '三分相': 4.0,
          '對分相': 6.0,
        };
        break;
      case 'transit_precise':
        newOrbs = {
          '合相': 4.0,
          '六分相': 2.0,
          '四分相': 3.0,
          '三分相': 3.0,
          '對分相': 4.0,
        };
        break;
      case 'eclipse_standard':
        newOrbs = {
          '合相': 10.0,
          '六分相': 5.0,
          '四分相': 8.0,
          '三分相': 8.0,
          '對分相': 10.0,
        };
        break;
      case 'eclipse_wide':
        newOrbs = {
          '合相': 12.0,
          '六分相': 6.0,
          '四分相': 10.0,
          '三分相': 10.0,
          '對分相': 12.0,
        };
        break;
      case 'standard':
      default:
        newOrbs = {
          '合相': 8.0,
          '六分相': 4.0,
          '四分相': 6.0,
          '三分相': 6.0,
          '對分相': 8.0,
        };
        break;
    }

    // 應用新的容許度設置
    for (final entry in newOrbs.entries) {
      viewModel.updateAspectOrb(entry.key, entry.value);
    }

    // 顯示成功訊息
    if (context != null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('已應用 ${_getPresetDisplayName(presetType)} 設置'),
          backgroundColor: AppColors.royalIndigo,
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  /// 獲取預設類型的顯示名稱
  String _getPresetDisplayName(String presetType) {
    switch (presetType) {
      case 'natal_standard':
        return '本命盤標準';
      case 'natal_strict':
        return '本命盤嚴格';
      case 'transit_standard':
        return '流年盤標準';
      case 'transit_precise':
        return '流年盤精確';
      case 'eclipse_standard':
        return '日月蝕盤標準';
      case 'eclipse_wide':
        return '日月蝕盤寬鬆';
      case 'standard':
        return '標準';
      case 'strict':
        return '嚴格';
      default:
        return '預設';
    }
  }

  /// 顯示重置對話框
  void _showResetDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('重置相位設置'),
          content: const Text('確定要將所有相位容許度重置為預設值嗎？'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                // 實現重置邏輯
                final viewModel = Provider.of<SettingsViewModel>(context, listen: false);
                _applyPreset('standard', viewModel, context);
              },
              child: const Text('重置'),
            ),
          ],
        );
      },
    );
  }
}
