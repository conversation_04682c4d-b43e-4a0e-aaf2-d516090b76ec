import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';

import '../../../astreal.dart';
import '../../../data/services/api/ai_api_service.dart';
import '../../../data/services/api/firebase_ai_usage_service.dart';

/// 增強版 Firebase AI 使用量管理頁面
/// 支援實時監聽、批量操作和統計分析（非 Windows 平台）
class EnhancedFirebaseAIUsagePage extends StatefulWidget {
  const EnhancedFirebaseAIUsagePage({super.key});

  @override
  State<EnhancedFirebaseAIUsagePage> createState() => _EnhancedFirebaseAIUsagePageState();
}

class _EnhancedFirebaseAIUsagePageState extends State<EnhancedFirebaseAIUsagePage> {
  List<FirebaseAIUsageRecord> _records = [];
  Map<String, dynamic>? _summary;
  bool _isLoading = true;
  String? _errorMessage;
  bool _isRealTimeEnabled = false;
  
  // 實時監聽訂閱
  final Map<AIProvider, StreamSubscription<FirebaseAIUsageRecord>?> _subscriptions = {};

  @override
  void initState() {
    super.initState();
    _loadUsageData();
    
    // 非 Windows 平台才啟用實時監聽
    if (!Platform.isWindows) {
      _setupRealTimeListening();
    }
  }

  @override
  void dispose() {
    _cancelAllSubscriptions();
    super.dispose();
  }

  Future<void> _loadUsageData() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // 並行載入使用記錄和統計摘要
      final futures = await Future.wait([
        FirebaseAIUsageService.getAllTodayUsage(),
        FirebaseAIUsageService.getUsageSummary(),
      ]);

      setState(() {
        _records = futures[0] as List<FirebaseAIUsageRecord>;
        _summary = futures[1] as Map<String, dynamic>;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
        _isLoading = false;
      });
    }
  }

  void _setupRealTimeListening() {
    if (Platform.isWindows) return;

    for (final provider in AIProvider.values) {
      final stream = FirebaseAIUsageService.listenToUsageChanges(provider);
      if (stream != null) {
        _subscriptions[provider] = stream.listen(
          (record) {
            setState(() {
              // 更新對應的記錄
              final index = _records.indexWhere((r) => r.aiProvider == record.aiProvider);
              if (index >= 0) {
                _records[index] = record;
              } else {
                _records.add(record);
              }
            });
          },
          onError: (error) {
            debugPrint('實時監聽錯誤 ${provider.displayName}: $error');
          },
        );
      }
    }

    setState(() {
      _isRealTimeEnabled = true;
    });
  }

  void _cancelAllSubscriptions() {
    for (final subscription in _subscriptions.values) {
      subscription?.cancel();
    }
    _subscriptions.clear();
    _isRealTimeEnabled = false;
  }

  Future<void> _batchResetUsage() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('批量重置使用量'),
        content: const Text('確定要重置所有 AI Provider 的今日使用量嗎？\n\n⚠️ 此操作無法復原。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('確定重置'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        if (Platform.isWindows) {
          await FirebaseAIUsageService.resetAllUsage();
        } else {
          await FirebaseAIUsageService.batchResetUsage(
            providers: AIProvider.values,
          );
        }
        
        await _loadUsageData();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('✅ 所有 AI 使用量已批量重置'),
              backgroundColor: AppColors.royalIndigo,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('❌ 批量重置失敗：$e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  Future<void> _loadHistoricalData() async {
    if (Platform.isWindows) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Windows 平台不支援批量歷史資料查詢'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    try {
      // 獲取過去 7 天的資料
      final dates = List.generate(7, (index) {
        final date = DateTime.now().subtract(Duration(days: index));
        return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
      });

      final historicalRecords = <String, List<FirebaseAIUsageRecord>>{};
      
      for (final provider in AIProvider.values) {
        final records = await FirebaseAIUsageService.getBatchUsageRecords(
          dates: dates,
          provider: provider,
        );
        historicalRecords[provider.displayName] = records;
      }

      // 顯示歷史資料對話框
      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => _buildHistoricalDataDialog(historicalRecords),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('載入歷史資料失敗：$e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            const Text('AI 使用量控管'),
            if (_isRealTimeEnabled) ...[
              const SizedBox(width: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.green,
                  borderRadius: BorderRadius.circular(10),
                ),
                child: const Text(
                  '即時',
                  style: TextStyle(fontSize: 10, color: Colors.white),
                ),
              ),
            ],
          ],
        ),
        backgroundColor: AppColors.royalIndigo,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadUsageData,
            tooltip: '重新載入',
          ),
          if (!Platform.isWindows)
            IconButton(
              icon: const Icon(Icons.history),
              onPressed: _loadHistoricalData,
              tooltip: '歷史資料',
            ),
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'batch_reset':
                  _batchResetUsage();
                  break;
                case 'toggle_realtime':
                  if (_isRealTimeEnabled) {
                    _cancelAllSubscriptions();
                  } else {
                    _setupRealTimeListening();
                  }
                  setState(() {});
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'batch_reset',
                child: Row(
                  children: [
                    Icon(Icons.restore, color: Colors.red),
                    SizedBox(width: 8),
                    Text('批量重置'),
                  ],
                ),
              ),
              if (!Platform.isWindows)
                PopupMenuItem(
                  value: 'toggle_realtime',
                  child: Row(
                    children: [
                      Icon(
                        _isRealTimeEnabled ? Icons.pause : Icons.play_arrow,
                        color: _isRealTimeEnabled ? Colors.orange : Colors.green,
                      ),
                      const SizedBox(width: 8),
                      Text(_isRealTimeEnabled ? '停止即時' : '啟用即時'),
                    ],
                  ),
                ),
            ],
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(color: AppColors.royalIndigo),
            SizedBox(height: 16),
            Text('載入使用量資料中...'),
          ],
        ),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text('載入失敗', style: Theme.of(context).textTheme.headlineSmall),
            const SizedBox(height: 8),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32),
              child: Text(
                _errorMessage!,
                textAlign: TextAlign.center,
                style: TextStyle(color: Colors.grey.shade600),
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _loadUsageData,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.royalIndigo,
                foregroundColor: Colors.white,
              ),
              child: const Text('重試'),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadUsageData,
      color: AppColors.royalIndigo,
      child: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          if (_summary != null) _buildEnhancedSummaryCard(),
          const SizedBox(height: 16),
          _buildUsageList(),
          const SizedBox(height: 16),
          _buildPlatformInfoCard(),
        ],
      ),
    );
  }

  Widget _buildEnhancedSummaryCard() {
    if (_summary == null) return const SizedBox.shrink();

    final totalTokens = _summary!['totalTokens'] as int? ?? 0;
    final totalCalls = _summary!['totalCalls'] as int? ?? 0;
    final limitReachedCount = _summary!['limitReachedCount'] as int? ?? 0;
    final providerCount = _summary!['providerCount'] as int? ?? 0;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.analytics, color: AppColors.royalIndigo),
                const SizedBox(width: 8),
                Text('今日使用量統計', style: Theme.of(context).textTheme.titleLarge),
                const Spacer(),
                if (_isRealTimeEnabled)
                  const Icon(Icons.live_tv, color: Colors.green, size: 16),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(child: _buildSummaryItem('總 Tokens', totalTokens.toString())),
                Expanded(child: _buildSummaryItem('總調用', totalCalls.toString())),
                Expanded(child: _buildSummaryItem('活躍 AI', providerCount.toString())),
                Expanded(
                  child: _buildSummaryItem(
                    '達限制',
                    limitReachedCount.toString(),
                    color: limitReachedCount > 0 ? Colors.red : Colors.green,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryItem(String label, String value, {Color? color}) {
    return Column(
      children: [
        Text(
          value,
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            color: color ?? AppColors.royalIndigo,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
        ),
      ],
    );
  }

  Widget _buildUsageList() {
    return Card(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                const Icon(Icons.list, color: AppColors.royalIndigo),
                const SizedBox(width: 8),
                Text('AI 提供商使用詳情', style: Theme.of(context).textTheme.titleLarge),
                const Spacer(),
                if (_isRealTimeEnabled)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.green.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.green.withOpacity(0.3)),
                    ),
                    child: const Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(Icons.circle, color: Colors.green, size: 8),
                        SizedBox(width: 4),
                        Text('即時更新', style: TextStyle(fontSize: 10, color: Colors.green)),
                      ],
                    ),
                  ),
              ],
            ),
          ),
          const Divider(height: 1),
          ..._records.map((record) => _buildEnhancedUsageItem(record)),
        ],
      ),
    );
  }

  Widget _buildEnhancedUsageItem(FirebaseAIUsageRecord record) {
    final provider = _getAIProvider(record.aiProvider);
    final dailyLimit = FirebaseAIUsageService.getDailyLimits()[provider] ?? 200000;
    final usagePercentage = (record.totalTokens / dailyLimit * 100).clamp(0.0, 100.0);
    final lastUpdated = record.updatedAt;
    final timeDiff = DateTime.now().difference(lastUpdated);

    return ListTile(
      leading: Stack(
        children: [
          CircleAvatar(
            backgroundColor: record.limitReached ? Colors.red : AppColors.royalIndigo,
            child: Text(
              _getProviderIcon(record.aiProvider),
              style: const TextStyle(color: Colors.white),
            ),
          ),
          if (_isRealTimeEnabled && timeDiff.inMinutes < 5)
            Positioned(
              right: 0,
              top: 0,
              child: Container(
                width: 12,
                height: 12,
                decoration: const BoxDecoration(
                  color: Colors.green,
                  shape: BoxShape.circle,
                ),
              ),
            ),
        ],
      ),
      title: Row(
        children: [
          Text(provider.displayName),
          const Spacer(),
          if (timeDiff.inMinutes < 60)
            Text(
              '${timeDiff.inMinutes}分鐘前',
              style: TextStyle(fontSize: 10, color: Colors.grey.shade500),
            ),
        ],
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 4),
          LinearProgressIndicator(
            value: usagePercentage / 100,
            backgroundColor: Colors.grey.shade200,
            valueColor: AlwaysStoppedAnimation<Color>(
              record.limitReached ? Colors.red : AppColors.royalIndigo,
            ),
          ),
          const SizedBox(height: 4),
          Row(
            children: [
              Text(
                '${record.totalTokens.toStringAsFixed(0)} / ${dailyLimit.toStringAsFixed(0)} tokens',
                style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
              ),
              const Spacer(),
              Text(
                '${usagePercentage.toStringAsFixed(1)}%',
                style: TextStyle(
                  fontSize: 12,
                  color: record.limitReached ? Colors.red : Colors.grey.shade600,
                  fontWeight: record.limitReached ? FontWeight.bold : FontWeight.normal,
                ),
              ),
            ],
          ),
        ],
      ),
      trailing: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Text(
            '${record.callCount} 次',
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          if (record.limitReached)
            const Text(
              '已達限制',
              style: TextStyle(
                color: Colors.red,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            )
          else
            Text(
              '剩餘 ${record.remainingTokens}',
              style: TextStyle(
                color: Colors.grey.shade600,
                fontSize: 10,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildPlatformInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Platform.isWindows ? Icons.computer : Icons.smartphone,
                  color: AppColors.royalIndigo,
                ),
                const SizedBox(width: 8),
                Text('平台功能', style: Theme.of(context).textTheme.titleMedium),
              ],
            ),
            const SizedBox(height: 12),
            if (Platform.isWindows) ...[
              const Text('🖥️ Windows 平台功能：'),
              const Text('• 基本使用量記錄和查詢'),
              const Text('• REST API 與 Firebase 通信'),
              const Text('• 本地快取機制'),
              const SizedBox(height: 8),
              const Text('⚠️ 不支援功能：'),
              const Text('• 實時監聽使用量變化'),
              const Text('• 批量操作和歷史資料查詢'),
            ] else ...[
              const Text('📱 行動平台增強功能：'),
              const Text('• 實時監聽使用量變化'),
              const Text('• 批量操作和重置'),
              const Text('• 歷史資料查詢'),
              const Text('• 使用量統計分析'),
              const SizedBox(height: 8),
              Text(
                _isRealTimeEnabled ? '✅ 實時監聽已啟用' : '⏸️ 實時監聽已停用',
                style: TextStyle(
                  color: _isRealTimeEnabled ? Colors.green : Colors.orange,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildHistoricalDataDialog(Map<String, List<FirebaseAIUsageRecord>> historicalRecords) {
    return AlertDialog(
      title: const Text('歷史使用量資料'),
      content: SizedBox(
        width: double.maxFinite,
        height: 400,
        child: ListView(
          children: historicalRecords.entries.map((entry) {
            final providerName = entry.key;
            final records = entry.value;

            return ExpansionTile(
              title: Text(providerName),
              children: records.map((record) {
                return ListTile(
                  title: Text(record.date),
                  subtitle: Text('${record.totalTokens} tokens, ${record.callCount} 次調用'),
                  trailing: record.limitReached
                      ? const Icon(Icons.warning, color: Colors.red)
                      : const Icon(Icons.check_circle, color: Colors.green),
                );
              }).toList(),
            );
          }).toList(),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('關閉'),
        ),
      ],
    );
  }

  AIProvider _getAIProvider(String providerName) {
    switch (providerName) {
      case 'gpt4o':
        return AIProvider.openai;
      case 'claude':
        return AIProvider.anthropic;
      case 'groq':
        return AIProvider.groq;
      case 'gemini':
        return AIProvider.gemini;
      default:
        return AIProvider.openai;
    }
  }

  String _getProviderIcon(String providerName) {
    switch (providerName) {
      case 'gpt4o':
        return '🧠';
      case 'claude':
        return '🤖';
      case 'groq':
        return '⚡';
      case 'gemini':
        return '✨';
      default:
        return '🤖';
    }
  }
}
