import 'package:flutter/material.dart';

import '../../../astreal.dart';
import '../../../data/services/api/interpretation_guidance_service.dart';

/// 解讀指引設定頁面
class InterpretationGuidanceSettingsPage extends StatefulWidget {
  const InterpretationGuidanceSettingsPage({super.key});

  @override
  State<InterpretationGuidanceSettingsPage> createState() => _InterpretationGuidanceSettingsPageState();
}

class _InterpretationGuidanceSettingsPageState extends State<InterpretationGuidanceSettingsPage> {
  final TextEditingController _customGuidanceController = TextEditingController();
  
  bool _isLoading = true;
  bool _useCustomGuidance = false;
  String _remoteGuidance = '';
  String _guidanceSource = '';
  bool _isSaving = false;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  @override
  void dispose() {
    _customGuidanceController.dispose();
    super.dispose();
  }

  /// 載入設定
  Future<void> _loadSettings() async {
    try {
      final useCustom = await InterpretationGuidanceService.getUseCustomGuidance();
      final customGuidance = await InterpretationGuidanceService.getCustomGuidance();
      final remoteGuidance = await InterpretationGuidanceService.getRemoteGuidance();
      final source = await InterpretationGuidanceService.getGuidanceSource();

      if (mounted) {
        setState(() {
          _useCustomGuidance = useCustom;
          _customGuidanceController.text = customGuidance;
          _remoteGuidance = remoteGuidance;
          _guidanceSource = source;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        _showErrorSnackBar('載入設定失敗: $e');
      }
    }
  }

  /// 保存設定
  Future<void> _saveSettings() async {
    if (_isSaving) return;

    setState(() {
      _isSaving = true;
    });

    try {
      // 驗證自定義指引
      if (_useCustomGuidance) {
        final customGuidance = _customGuidanceController.text.trim();
        if (!InterpretationGuidanceService.validateGuidance(customGuidance)) {
          _showErrorSnackBar('自定義指引格式不正確，請至少輸入3行內容');
          return;
        }
        
        await InterpretationGuidanceService.saveCustomGuidance(customGuidance);
      }

      await InterpretationGuidanceService.setUseCustomGuidance(_useCustomGuidance);

      if (mounted) {
        _showSuccessSnackBar('設定已保存');
        // 重新載入來源信息
        final source = await InterpretationGuidanceService.getGuidanceSource();
        setState(() {
          _guidanceSource = source;
        });
      }
    } catch (e) {
      _showErrorSnackBar('保存設定失敗: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  /// 重置為預設
  Future<void> _resetToDefault() async {
    final confirmed = await _showConfirmDialog(
      '重置確認',
      '確定要重置為預設指引嗎？這將清除您的自定義設定。',
    );

    if (!confirmed) return;

    try {
      await InterpretationGuidanceService.resetToDefault();
      await _loadSettings();
      _showSuccessSnackBar('已重置為預設指引');
    } catch (e) {
      _showErrorSnackBar('重置失敗: $e');
    }
  }

  /// 從 Remote Config 同步
  Future<void> _syncFromRemoteConfig() async {
    try {
      setState(() {
        _isLoading = true;
      });

      await InterpretationGuidanceService.syncFromRemoteConfig();
      await _loadSettings();
      _showSuccessSnackBar('已從 Remote Config 同步');
    } catch (e) {
      _showErrorSnackBar('同步失敗: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// 將 Remote Config 指引載入到編輯器
  Future<void> _loadRemoteConfigToEditor() async {
    try {
      // 檢查是否有未保存的自定義內容
      final currentText = _customGuidanceController.text.trim();
      if (currentText.isNotEmpty) {
        final confirmed = await _showConfirmDialog(
          '覆蓋確認',
          '這將覆蓋您當前的自定義指引內容。確定要繼續嗎？',
        );
        if (!confirmed) return;
      }

      // 載入 Remote Config 指引
      final remoteGuidance = await InterpretationGuidanceService.getRemoteGuidance();

      if (mounted) {
        setState(() {
          _customGuidanceController.text = remoteGuidance;
        });
        _showSuccessSnackBar('已載入 Remote Config 指引到編輯器');
      }
    } catch (e) {
      _showErrorSnackBar('載入 Remote Config 指引失敗: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          '解讀指引設定',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppColors.royalIndigo,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _syncFromRemoteConfig,
            tooltip: '從 Remote Config 同步',
          ),
          IconButton(
            icon: const Icon(Icons.restore),
            onPressed: _resetToDefault,
            tooltip: '重置為預設',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 當前狀態卡片
                  _buildStatusCard(),
                  
                  // const SizedBox(height: 20),
                  
                  // 指引來源選擇
                  _buildSourceSelectionCard(),
                  
                  // const SizedBox(height: 20),
                  
                  // 自定義指引編輯
                  if (_useCustomGuidance) _buildCustomGuidanceCard(),
                  
                  // const SizedBox(height: 20),
                  
                  // Remote Config 指引預覽
                  _buildRemoteGuidanceCard(),
                  
                  // const SizedBox(height: 20),
                  
                  // 預設指引預覽
                  _buildDefaultGuidanceCard(),
                  
                  const SizedBox(height: 32),
                ],
              ),
            ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _isSaving ? null : _saveSettings,
        backgroundColor: AppColors.royalIndigo,
        foregroundColor: Colors.white,
        icon: _isSaving 
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : const Icon(Icons.save),
        label: Text(_isSaving ? '保存中...' : '保存設定'),
      ),
    );
  }

  /// 構建狀態卡片
  Widget _buildStatusCard() {
    return StyledCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: AppColors.royalIndigo,
                  size: 20,
                ),
                SizedBox(width: 8),
                Text(
                  '當前狀態',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppColors.royalIndigo,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            _buildStatusRow('指引來源', _guidanceSource),
            _buildStatusRow('使用自定義', _useCustomGuidance ? '是' : '否'),
          ],
        ),
      ),
    );
  }

  /// 構建狀態行
  Widget _buildStatusRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          SizedBox(
            width: 80,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade600,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: AppColors.textDark,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 構建來源選擇卡片
  Widget _buildSourceSelectionCard() {
    return StyledCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(
                  Icons.source,
                  color: AppColors.royalIndigo,
                  size: 20,
                ),
                SizedBox(width: 8),
                Text(
                  '指引來源',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppColors.royalIndigo,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text('使用自定義指引'),
              subtitle: Text(
                _useCustomGuidance
                    ? '使用您自定義的解讀指引'
                    : '使用 Remote Config 或預設指引',
              ),
              value: _useCustomGuidance,
              onChanged: (value) async {
                if (value && _customGuidanceController.text.trim().isEmpty) {
                  // 當開啟自定義指引且編輯器為空時，帶入 Remote Config 內容
                  await _loadRemoteConfigToEditor();
                }
                setState(() {
                  _useCustomGuidance = value;
                });
              },
              activeColor: AppColors.royalIndigo,
            ),

            // 帶入 Remote Config 按鈕
            if (_useCustomGuidance) ...[
              const SizedBox(height: 8),
              TextButton.icon(
                onPressed: _loadRemoteConfigToEditor,
                icon: const Icon(Icons.cloud_download, size: 16),
                label: const Text('帶入 Remote Config 指引'),
                style: TextButton.styleFrom(
                  foregroundColor: AppColors.royalIndigo,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// 構建自定義指引卡片
  Widget _buildCustomGuidanceCard() {
    return StyledCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(
                  Icons.edit,
                  color: AppColors.solarAmber,
                  size: 20,
                ),
                SizedBox(width: 8),
                Text(
                  '自定義指引',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppColors.solarAmber,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _customGuidanceController,
              maxLines: 15,
              decoration: const InputDecoration(
                hintText: '請輸入您的自定義解讀指引...',
                border: OutlineInputBorder(),
                alignLabelWithHint: true,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '提示：請至少輸入3行內容，指引將直接用於解讀。您可以點擊上方的「帶入 Remote Config 指引」按鈕作為起始內容。',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 構建 Remote Config 指引卡片
  Widget _buildRemoteGuidanceCard() {
    return StyledCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(
                  Icons.cloud,
                  color: Colors.blue,
                  size: 20,
                ),
                SizedBox(width: 8),
                Text(
                  'Remote Config 指引',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Colors.blue.withValues(alpha: 0.2),
                ),
              ),
              child: Text(
                InterpretationGuidanceService.getGuidancePreview(_remoteGuidance),
                style: const TextStyle(
                  fontSize: 12,
                  fontFamily: 'monospace',
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 構建預設指引卡片
  Widget _buildDefaultGuidanceCard() {
    return StyledCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(
                  Icons.settings_backup_restore,
                  color: Colors.grey,
                  size: 20,
                ),
                SizedBox(width: 8),
                Text(
                  '預設指引',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Colors.grey.withValues(alpha: 0.2),
                ),
              ),
              child: Text(
                InterpretationGuidanceService.getGuidancePreview(
                  InterpretationGuidanceService.defaultGuidance,
                ),
                style: const TextStyle(
                  fontSize: 12,
                  fontFamily: 'monospace',
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 顯示確認對話框
  Future<bool> _showConfirmDialog(String title, String content) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(content),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('確定'),
          ),
        ],
      ),
    );
    return result ?? false;
  }

  /// 顯示成功訊息
  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }

  /// 顯示錯誤訊息
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }
}
