import 'package:flutter/material.dart';

import '../../../astreal.dart';
import '../../../shared/widgets/common/responsive_wrapper.dart';

/// Groq 模型信息頁面
class GroqModelsInfoPage extends StatelessWidget {
  const GroqModelsInfoPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Groq 模型介紹'),
        backgroundColor: Colors.purple,
        foregroundColor: Colors.white,
      ),
      body: ResponsivePageWrapper(
        maxWidth: 800.0,
        child: ListView(
          padding: ResponsiveUtils.getResponsivePadding(context),
          children: [
          // Groq 平台介紹
          _buildGroqIntroduction(),
          const SizedBox(height: 24),
          
          // 生產模型
          _buildProductionModels(),
          const SizedBox(height: 24),
          
          // 模型比較
          _buildModelComparison(),
          const SizedBox(height: 24),
          
          // 使用建議
          _buildUsageRecommendations(),
          const SizedBox(height: 24),
          
          // 優勢特點
          _buildAdvantages(),
          ],
        ),
      ),
    );
  }

  Widget _buildGroqIntroduction() {
    return StyledCard(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: Colors.purple.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.flash_on,
                    color: Colors.purple,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                const Text(
                  'Groq 平台介紹',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textDark,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            const Text(
              'Groq 是一個專注於極速 AI 推理的平台，使用專門設計的 LPU（Language Processing Unit）硬件，提供比傳統 GPU 快 10-100 倍的推理速度。',
              style: TextStyle(
                fontSize: 14,
                height: 1.5,
                color: AppColors.textDark,
              ),
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.purple.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Row(
                children: [
                  Icon(Icons.star, color: Colors.purple, size: 20),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      '特色：極速推理、免費配額、開源模型、OpenAI 兼容 API',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: Colors.purple,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProductionModels() {
    return StyledCard(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '生產級模型',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppColors.textDark,
              ),
            ),
            const SizedBox(height: 16),
            
            _buildModelCard(
              name: 'Llama 3.3 70B Versatile',
              id: 'llama-3.3-70b-versatile',
              description: '最新的 Meta Llama 模型，提供最佳的性能和準確性',
              features: ['128K 上下文', '32K 輸出', '最新模型', '高準確性'],
              color: Colors.blue,
              icon: Icons.auto_awesome,
            ),
            
            _buildModelCard(
              name: 'Llama 3.1 8B Instant',
              id: 'llama-3.1-8b-instant',
              description: '輕量級模型，提供極速響應，適合快速解讀',
              features: ['128K 上下文', '8K 輸出', '極速響應', '輕量高效'],
              color: Colors.green,
              icon: Icons.speed,
            ),
            
            _buildModelCard(
              name: 'Gemma 2 9B',
              id: 'gemma2-9b-it',
              description: 'Google 開發的高效模型，平衡性能與速度',
              features: ['8K 上下文', '高效能', '平衡性能', 'Google 出品'],
              color: Colors.orange,
              icon: Icons.balance,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildModelCard({
    required String name,
    required String id,
    required String description,
    required List<String> features,
    required Color color,
    required IconData icon,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 24),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      name,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: color,
                      ),
                    ),
                    Text(
                      id,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                        fontFamily: 'monospace',
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            description,
            style: const TextStyle(
              fontSize: 14,
              height: 1.4,
              color: AppColors.textDark,
            ),
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 6,
            runSpacing: 6,
            children: features.map((feature) => Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                feature,
                style: TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.w500,
                  color: color,
                ),
              ),
            )).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildModelComparison() {
    return StyledCard(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Groq 模型比較',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppColors.textDark,
              ),
            ),
            const SizedBox(height: 16),
            Table(
              border: TableBorder.all(color: Colors.grey.shade300),
              columnWidths: const {
                0: FlexColumnWidth(2),
                1: FlexColumnWidth(1),
                2: FlexColumnWidth(1),
                3: FlexColumnWidth(1),
              },
              children: [
                TableRow(
                  decoration: BoxDecoration(color: Colors.grey.shade100),
                  children: const [
                    Padding(
                      padding: EdgeInsets.all(8),
                      child: Text('特性', style: TextStyle(fontWeight: FontWeight.bold)),
                    ),
                    Padding(
                      padding: EdgeInsets.all(8),
                      child: Text('Llama 3.3 70B', style: TextStyle(fontWeight: FontWeight.bold), textAlign: TextAlign.center),
                    ),
                    Padding(
                      padding: EdgeInsets.all(8),
                      child: Text('Llama 3.1 8B', style: TextStyle(fontWeight: FontWeight.bold), textAlign: TextAlign.center),
                    ),
                    Padding(
                      padding: EdgeInsets.all(8),
                      child: Text('Gemma 2 9B', style: TextStyle(fontWeight: FontWeight.bold), textAlign: TextAlign.center),
                    ),
                  ],
                ),
                _buildComparisonRow('模型大小', '70B 參數', '8B 參數', '9B 參數'),
                _buildComparisonRow('上下文長度', '128K tokens', '128K tokens', '8K tokens'),
                _buildComparisonRow('輸出長度', '32K tokens', '8K tokens', '標準'),
                _buildComparisonRow('推理速度', '★★★★☆', '★★★★★', '★★★★★'),
                _buildComparisonRow('準確性', '★★★★★', '★★★★☆', '★★★★☆'),
                _buildComparisonRow('適用場景', '複雜分析', '快速解讀', '平衡使用'),
              ],
            ),
          ],
        ),
      ),
    );
  }

  TableRow _buildComparisonRow(String feature, String llama70b, String llama8b, String gemma) {
    return TableRow(
      children: [
        Padding(padding: const EdgeInsets.all(8), child: Text(feature)),
        Padding(padding: const EdgeInsets.all(8), child: Text(llama70b, textAlign: TextAlign.center)),
        Padding(padding: const EdgeInsets.all(8), child: Text(llama8b, textAlign: TextAlign.center)),
        Padding(padding: const EdgeInsets.all(8), child: Text(gemma, textAlign: TextAlign.center)),
      ],
    );
  }

  Widget _buildUsageRecommendations() {
    return StyledCard(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '使用建議',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppColors.textDark,
              ),
            ),
            const SizedBox(height: 16),
            _buildRecommendationItem(
              '深度分析',
              'Llama 3.3 70B Versatile',
              '適合複雜的星盤解讀，需要深入分析時使用',
              Icons.psychology,
              Colors.blue,
            ),
            _buildRecommendationItem(
              '快速解讀',
              'Llama 3.1 8B Instant',
              '適合日常快速解讀，響應速度最快',
              Icons.flash_on,
              Colors.green,
            ),
            _buildRecommendationItem(
              '平衡使用',
              'Gemma 2 9B',
              '平衡性能與速度，適合一般用途',
              Icons.balance,
              Colors.orange,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecommendationItem(String title, String model, String description, IconData icon, Color color) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Row(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '$title → $model',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAdvantages() {
    return StyledCard(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Groq 平台優勢',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppColors.textDark,
              ),
            ),
            const SizedBox(height: 16),
            _buildAdvantageItem(
              '極速推理',
              '使用專門的 LPU 硬件，推理速度比傳統 GPU 快 10-100 倍',
              Icons.speed,
              Colors.purple,
            ),
            _buildAdvantageItem(
              '免費配額',
              '提供慷慨的免費使用額度，適合個人用戶和小型項目',
              Icons.free_breakfast,
              Colors.green,
            ),
            _buildAdvantageItem(
              '開源模型',
              '使用 Llama、Gemma 等開源模型，透明度高，社群支持強',
              Icons.open_in_browser,
              Colors.blue,
            ),
            _buildAdvantageItem(
              'API 兼容',
              '完全兼容 OpenAI API 格式，易於整合和遷移',
              Icons.api,
              Colors.orange,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdvantageItem(String title, String description, IconData icon, Color color) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: color, size: 20),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                    height: 1.4,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
