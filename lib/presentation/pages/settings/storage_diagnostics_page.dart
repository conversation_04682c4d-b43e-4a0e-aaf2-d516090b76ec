import 'package:flutter/material.dart';

import '../../../core/utils/logger_utils.dart';
import '../../../shared/utils/firebase_storage_diagnostics.dart';
import '../../themes/app_theme.dart';
/// Firebase Storage 診斷頁面
class StorageDiagnosticsPage extends StatefulWidget {
  const StorageDiagnosticsPage({super.key});

  @override
  State<StorageDiagnosticsPage> createState() => _StorageDiagnosticsPageState();
}

class _StorageDiagnosticsPageState extends State<StorageDiagnosticsPage> {
  bool _isRunningDiagnostics = false;
  bool _isRunningFix = false;
  bool _isRunningTest = false;
  Map<String, dynamic>? _diagnosticsResult;
  String? _diagnosticsReport;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Storage 診斷'),
        backgroundColor: AppColors.royalIndigo,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 說明卡片
            _buildInfoCard(),
            
            const SizedBox(height: 20),
            
            // 診斷按鈕
            _buildDiagnosticsSection(),
            
            const SizedBox(height: 20),
            
            // 修復按鈕
            _buildFixSection(),
            
            const SizedBox(height: 20),
            
            // 測試按鈕
            _buildTestSection(),
            
            const SizedBox(height: 20),
            
            // 診斷結果
            if (_diagnosticsReport != null) _buildResultsSection(),
          ],
        ),
      ),
    );
  }

  /// 構建說明卡片
  Widget _buildInfoCard() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: AppColors.royalIndigo,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  'Firebase Storage 診斷工具',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textDark,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              '此工具可以幫助診斷和解決 Firebase Storage 連接問題，包括：\n'
              '• 檢查 Firebase 初始化狀態\n'
              '• 驗證用戶認證狀態\n'
              '• 測試 Storage 連接和權限\n'
              '• 嘗試自動修復常見問題',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade700,
                height: 1.4,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 構建診斷區塊
  Widget _buildDiagnosticsSection() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '🔍 系統診斷',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppColors.textDark,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              '執行完整的 Firebase Storage 診斷，檢查所有相關組件的狀態。',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade700,
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isRunningDiagnostics ? null : _runDiagnostics,
                icon: _isRunningDiagnostics
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : const Icon(Icons.search),
                label: Text(_isRunningDiagnostics ? '診斷中...' : '開始診斷'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.royalIndigo,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 構建修復區塊
  Widget _buildFixSection() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '🔧 自動修復',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppColors.textDark,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              '嘗試自動修復常見的 Firebase Storage 問題。',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade700,
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isRunningFix ? null : _runAutoFix,
                icon: _isRunningFix
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : const Icon(Icons.build),
                label: Text(_isRunningFix ? '修復中...' : '嘗試修復'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange.shade600,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 構建測試區塊
  Widget _buildTestSection() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '🧪 功能測試',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppColors.textDark,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              '測試基本的 Storage 操作（上傳、下載、刪除）。',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade700,
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isRunningTest ? null : _runBasicTest,
                icon: _isRunningTest
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : const Icon(Icons.science),
                label: Text(_isRunningTest ? '測試中...' : '執行測試'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green.shade600,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 構建結果區塊
  Widget _buildResultsSection() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.analytics,
                  color: AppColors.royalIndigo,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  '診斷結果',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textDark,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: _copyResults,
                  icon: const Icon(Icons.copy),
                  tooltip: '複製結果',
                ),
              ],
            ),
            const SizedBox(height: 12),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Text(
                  _diagnosticsReport!,
                  style: const TextStyle(
                    fontSize: 12,
                    fontFamily: 'monospace',
                    height: 1.4,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 執行診斷
  Future<void> _runDiagnostics() async {
    setState(() {
      _isRunningDiagnostics = true;
      _diagnosticsResult = null;
      _diagnosticsReport = null;
    });

    try {
      final diagnostics = FirebaseStorageDiagnostics();
      final result = await diagnostics.runFullDiagnostics();
      
      setState(() {
        _diagnosticsResult = result;
        _diagnosticsReport = '診斷完成，請查看日誌獲取詳細報告';
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('診斷完成，請查看結果'),
            backgroundColor: Colors.green,
          ),
        );
      }

    } catch (e) {
      logger.e('診斷失敗: $e');
      setState(() {
        _diagnosticsReport = '診斷失敗: $e';
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('診斷失敗: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isRunningDiagnostics = false;
        });
      }
    }
  }

  /// 執行自動修復
  Future<void> _runAutoFix() async {
    setState(() {
      _isRunningFix = true;
    });

    try {
      final diagnostics = FirebaseStorageDiagnostics();
      final success = await diagnostics.attemptAutoFix();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(success ? '修復完成' : '修復失敗'),
            backgroundColor: success ? Colors.green : Colors.red,
          ),
        );
      }

    } catch (e) {
      logger.e('修復失敗: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('修復失敗: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isRunningFix = false;
        });
      }
    }
  }

  /// 執行基本測試
  Future<void> _runBasicTest() async {
    setState(() {
      _isRunningTest = true;
    });

    try {
      final diagnostics = FirebaseStorageDiagnostics();
      final success = await diagnostics.testBasicOperations();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(success ? '所有測試通過' : '測試失敗'),
            backgroundColor: success ? Colors.green : Colors.red,
          ),
        );
      }

    } catch (e) {
      logger.e('測試失敗: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('測試失敗: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isRunningTest = false;
        });
      }
    }
  }

  /// 複製結果
  void _copyResults() {
    if (_diagnosticsReport != null) {
      // 在實際應用中，這裡應該使用 Clipboard.setData
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('結果已複製到剪貼簿'),
          duration: Duration(seconds: 2),
        ),
      );
    }
  }
}
