import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../astreal.dart';
import '../../../shared/widgets/common/responsive_wrapper.dart';
/// 星盤顯示設置頁面
class ChartDisplaySettingsPage extends StatefulWidget {
  final ChartViewModel? chartViewModel;
  final ChartType? initialChartType; // 新增：初始星盤類型

  const ChartDisplaySettingsPage({
    super.key,
    this.chartViewModel,
    this.initialChartType, // 接收初始星盤類型
  });

  @override
  State<ChartDisplaySettingsPage> createState() => _ChartDisplaySettingsPageState();
}

class _ChartDisplaySettingsPageState extends State<ChartDisplaySettingsPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);

    // 如果有傳入初始星盤類型，則在頁面載入後設定
    if (widget.initialChartType != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        final settingsViewModel = Provider.of<SettingsViewModel>(context, listen: false);
        settingsViewModel.setCurrentChartType(widget.initialChartType!);
      });
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.scaffoldBackground,
      appBar: AppBar(
        title: const Text(
          '星盤顯示設置',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
            fontSize: 18,
          ),
        ),
        backgroundColor: AppColors.indigoSurface,
        foregroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios),
          onPressed: () => Navigator.of(context).pop(),
        ),
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(
              icon: Icon(Icons.tune),
              text: '星盤設置',
            ),
            Tab(
              icon: Icon(Icons.palette),
              text: '顯示選項',
            ),
          ],
        ),
      ),
      body: Consumer<SettingsViewModel>(
        builder: (context, settingsViewModel, child) {
          if (settingsViewModel.chartSettings == null) {
            return const Center(
              child: CircularProgressIndicator(
                color: AppColors.royalIndigo,
              ),
            );
          }

          return ResponsivePageWrapper(
            maxWidth: 1000.0, // 星盤顯示設置需要較大寬度
            child: TabBarView(
              controller: _tabController,
              children: [
                // 第一個 Tab：星盤設置
                _buildChartSettingsTab(settingsViewModel),
                // 第二個 Tab：顯示選項
                _buildDisplayOptionsTab(settingsViewModel),
              ],
            ),
          );
        },
      ),
    );
  }

  /// 構建星盤設置 Tab
  Widget _buildChartSettingsTab(SettingsViewModel settingsViewModel) {
    return Row(
      children: [
        // 左側：星盤類型選擇器
        Container(
          width: 130,
          decoration: BoxDecoration(
            color: AppColors.cardBackground,
            border: Border(
              right: BorderSide(
                color: Colors.grey.shade300,
                width: 1,
              ),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 標題
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: AppColors.indigoLight.withValues(alpha: 0.1),
                  border: Border(
                    bottom: BorderSide(
                      color: Colors.grey.shade300,
                      width: 1,
                    ),
                  ),
                ),
                child: const Row(
                  children: [
                    Icon(
                      Icons.category,
                      color: AppColors.indigoLight,
                      size: 20,
                    ),
                    SizedBox(width: 8),
                    Text(
                      '星盤類型',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: AppColors.indigoLight,
                      ),
                    ),
                  ],
                ),
              ),
              // 星盤類型列表
              Expanded(
                child: _buildChartTypeList(settingsViewModel),
              ),
            ],
          ),
        ),
        // 右側：選中星盤類型的設置
        Expanded(
          child: _buildChartTypeSettings(settingsViewModel),
        ),
      ],
    );
  }

  /// 構建顯示選項 Tab
  Widget _buildDisplayOptionsTab(SettingsViewModel settingsViewModel) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 顯示模式設置
          _buildDisplayModeCard(settingsViewModel),
          const SizedBox(height: 16),

          // 星座界主星設置
          _buildZodiacRulersSection(settingsViewModel),
          const SizedBox(height: 16),

          // 重置按鈕
          _buildResetButton(settingsViewModel),
          const SizedBox(height: 32),
        ],
      ),
    );
  }

  /// 構建星盤類型列表
  Widget _buildChartTypeList(SettingsViewModel settingsViewModel) {
    const chartTypes = ChartType.values;

    return ListView.builder(
      itemCount: chartTypes.length,
      itemBuilder: (context, index) {
        final chartType = chartTypes[index];
        final isSelected = settingsViewModel.currentChartType == chartType;

        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
          decoration: BoxDecoration(
            color: isSelected
                ? AppColors.indigoLight.withValues(alpha: 0.1)
                : Colors.transparent,
            borderRadius: BorderRadius.circular(8),
            border: isSelected
                ? Border.all(color: AppColors.indigoLight.withValues(alpha: 0.3))
                : null,
          ),
          child: ListTile(
            title: Text(
              chartType.displayName,
              style: TextStyle(
                fontSize: 14,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                color: isSelected ? AppColors.indigoLight : AppColors.textDark,
              ),
            ),
            onTap: () {
              settingsViewModel.setCurrentChartType(chartType);
            },
            contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
            dense: true,
          ),
        );
      },
    );
  }

  /// 構建星盤類型設置區域
  Widget _buildChartTypeSettings(SettingsViewModel settingsViewModel) {
    final currentChartType = settingsViewModel.currentChartType;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 當前星盤類型標題
          Container(
            padding: const EdgeInsets.all(16),
            margin: const EdgeInsets.only(bottom: 16),
            decoration: BoxDecoration(
              color: AppColors.indigoLight.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: AppColors.indigoLight.withValues(alpha: 0.3)),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppColors.indigoLight.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    _getChartTypeIcon(currentChartType),
                    color: AppColors.indigoLight,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        currentChartType.displayName,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: AppColors.indigoLight,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _getChartTypeDescription(currentChartType),
                        style: const TextStyle(
                          fontSize: 12,
                          color: AppColors.textMedium,
                        ),
                      ),
                    ],
                  ),
                ),
                // 還原設定按鈕
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppColors.warning.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: IconButton(
                    onPressed: () => _showResetChartTypeDialog(settingsViewModel),
                    icon: const Icon(
                      Icons.restore,
                      color: AppColors.warning,
                      size: 20,
                    ),
                    tooltip: '還原${currentChartType.displayName}設定',
                    constraints: const BoxConstraints(
                      minWidth: 36,
                      minHeight: 36,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // 占星模式設置
          _buildAstrologyModeCard(settingsViewModel),
          const SizedBox(height: 16),

          // 宮位系統設置
          _buildHouseSystemSection(settingsViewModel),
          // const SizedBox(height: 16),

          // 行星顯示設置
          _buildPlanetVisibilitySection(settingsViewModel),
          // const SizedBox(height: 16),

          // 相位設置
          _buildAspectSettingsSection(settingsViewModel),
        ],
      ),
    );
  }

  /// 構建初始星盤類型提示信息
  Widget _buildInitialChartTypeInfo(SettingsViewModel settingsViewModel) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.royalIndigo.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.royalIndigo.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppColors.royalIndigo.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.info_outline,
              color: AppColors.royalIndigo,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  '當前設置',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: AppColors.royalIndigo,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '正在設置「${widget.initialChartType?.displayName ?? ''}」的顯示選項',
                  style: const TextStyle(
                    fontSize: 12,
                    color: AppColors.textMedium,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 構建顯示模式設置卡片
  Widget _buildDisplayModeCard(SettingsViewModel settingsViewModel) {
    return _buildSettingCard(
      title: '顯示模式',
      icon: Icons.visibility_outlined,
      iconColor: AppColors.royalIndigo,
      child: Column(
        children: [
          _buildRadioOption(
            title: '經典模式',
            subtitle: '傳統星盤顯示，簡潔清晰',
            value: 'classic',
            groupValue: _getCurrentDisplayMode(settingsViewModel),
            onChanged: (value) {
              _setDisplayMode(settingsViewModel, 'classic');
            },
          ),
          const Divider(height: 1, color: AppColors.textLight),
          _buildRadioOption(
            title: '度數模式',
            subtitle: '顯示宮位度數與行星度數',
            value: 'degrees',
            groupValue: _getCurrentDisplayMode(settingsViewModel),
            onChanged: (value) {
              _setDisplayMode(settingsViewModel, 'degrees');
            },
          ),
        ],
      ),
    );
  }

  /// 構建顏色主題設置卡片
  Widget _buildColorThemeCard(SettingsViewModel settingsViewModel) {
    return _buildSettingCard(
      title: '顏色主題',
      icon: Icons.palette_outlined,
      iconColor: AppColors.warning,
      child: Column(
        children: [
          _buildRadioOption(
            title: '經典主題',
            subtitle: '傳統的黑白配色方案',
            value: 'classic',
            groupValue: settingsViewModel.chartSettings?.colorTheme ?? 'modern',
            onChanged: (value) {
              settingsViewModel.updateColorTheme('classic');
            },
          ),
          const Divider(height: 1, color: AppColors.textLight),
          _buildRadioOption(
            title: '現代主題',
            subtitle: '彩色的現代配色方案',
            value: 'modern',
            groupValue: settingsViewModel.chartSettings?.colorTheme ?? 'modern',
            onChanged: (value) {
              settingsViewModel.updateColorTheme('modern');
            },
          ),
        ],
      ),
    );
  }

  /// 構建重置按鈕
  Widget _buildResetButton(SettingsViewModel settingsViewModel) {
    return Center(
      child: OutlinedButton.icon(
        onPressed: () => _showResetDialog(context, settingsViewModel),
        icon: const Icon(Icons.refresh),
        label: const Text('重置為預設值'),
        style: OutlinedButton.styleFrom(
          foregroundColor: AppColors.textMedium,
          side: const BorderSide(color: AppColors.textLight),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
    );
  }

  /// 構建宮位系統設置
  Widget _buildHouseSystemSection(SettingsViewModel viewModel) {
    return StyledCard(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(
                  Icons.home_work,
                  color: AppColors.indigoLight,
                  size: 20,
                ),
                SizedBox(width: 8),
                Text(
                  '宮位系統',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textDark,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              '選擇用於計算宮位的系統。不同系統會產生不同的宮位劃分。',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(8),
              ),
              child: DropdownButtonHideUnderline(
                child: DropdownButton<HouseSystem>(
                  value: viewModel.currentChartTypeSettings?.houseSystem ??
                      HouseSystem.placidus,
                  isExpanded: true,
                  items: HouseSystem.values.map((HouseSystem system) {
                    return DropdownMenuItem<HouseSystem>(
                      value: system,
                      child: Text(system.displayName),
                    );
                  }).toList(),
                  onChanged: (HouseSystem? newValue) {
                    if (newValue != null) {
                      viewModel.updateHouseSystem(newValue);
                    }
                  },
                ),
              ),
            ),
            const SizedBox(height: 12),
            _buildHouseSystemInfo(
                viewModel.currentChartTypeSettings?.houseSystem ?? HouseSystem.placidus),
          ],
        ),
      ),
    );
  }

  /// 構建宮位系統說明
  Widget _buildHouseSystemInfo(HouseSystem houseSystem) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.indigoLight.withValues(alpha: 0.08),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.indigoLight.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 標題行
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: _getHouseSystemTypeColor(houseSystem.type).withValues(alpha: 0.15),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Icon(
                  _getHouseSystemTypeIcon(houseSystem.type),
                  color: _getHouseSystemTypeColor(houseSystem.type),
                  size: 16,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      houseSystem.displayName,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: AppColors.textDark,
                      ),
                    ),
                    Text(
                      houseSystem.englishName,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          // 標籤
          _buildHouseSystemTags(houseSystem),
          const SizedBox(height: 12),
          // 分隔線
          Divider(
            color: AppColors.indigoLight.withValues(alpha: 0.2),
            height: 1,
          ),
          const SizedBox(height: 12),
          // 描述
          Text(
            houseSystem.shortDescription,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade700,
              height: 1.4,
            ),
          ),

          const SizedBox(height: 12),

          // 詳細資訊
          _buildHouseSystemDetails(houseSystem),
        ],
      ),
    );
  }

  /// 構建宮位系統標籤
  Widget _buildHouseSystemTags(HouseSystem houseSystem) {
    return Wrap(
      spacing: 6,
      children: [
        // 難度標籤
        _buildTag(
          _getDifficultyText(houseSystem.difficultyLevel),
          _getDifficultyColor(houseSystem.difficultyLevel),
        ),
        // 使用頻率標籤
        if (houseSystem.isCommonlyUsed)
          _buildTag('常用', AppColors.successGreen),
        // 歷史標籤
        if (houseSystem.isClassical)
          _buildTag('古典', AppColors.solarAmber),
      ],
    );
  }

  /// 構建標籤
  Widget _buildTag(String text, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.15),
        borderRadius: BorderRadius.circular(4),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
          width: 0.5,
        ),
      ),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 10,
          fontWeight: FontWeight.w500,
          color: color,
        ),
      ),
    );
  }

  /// 構建宮位系統詳細資訊
  Widget _buildHouseSystemDetails(HouseSystem houseSystem) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.7),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          // 適用地區
          if (houseSystem.suitableRegions.isNotEmpty) ...[
            _buildDetailRow(
              Icons.public,
              '適用地區',
              houseSystem.suitableRegions.join('、'),
            ),
            const SizedBox(height: 8),
          ],

          // 歷史時期
          _buildDetailRow(
            Icons.history,
            '歷史時期',
            _getHistoricalPeriodText(houseSystem.historicalPeriod),
          ),

          const SizedBox(height: 8),

          // 系統類型
          _buildDetailRow(
            Icons.category,
            '系統類型',
            _getHouseSystemTypeText(houseSystem.type),
          ),
        ],
      ),
    );
  }

  /// 構建詳細資訊行
  Widget _buildDetailRow(IconData icon, String label, String value) {
    return Row(
      children: [
        Icon(
          icon,
          size: 14,
          color: AppColors.indigoLight,
        ),
        const SizedBox(width: 8),
        Text(
          '$label: ',
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w500,
            color: Colors.grey.shade600,
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade700,
            ),
          ),
        ),
      ],
    );
  }

  /// 獲取宮位系統類型圖標
  IconData _getHouseSystemTypeIcon(HouseSystemType type) {
    switch (type) {
      case HouseSystemType.timeBasedQuadrant:
        return Icons.schedule;
      case HouseSystemType.spaceBasedQuadrant:
        return Icons.explore;
      case HouseSystemType.equalDivision:
        return Icons.pie_chart;
      case HouseSystemType.wholeSign:
        return Icons.crop_square;
      case HouseSystemType.special:
        return Icons.star;
    }
  }

  /// 獲取宮位系統類型顏色
  Color _getHouseSystemTypeColor(HouseSystemType type) {
    switch (type) {
      case HouseSystemType.timeBasedQuadrant:
        return AppColors.royalIndigo;
      case HouseSystemType.spaceBasedQuadrant:
        return AppColors.indigoLight;
      case HouseSystemType.equalDivision:
        return AppColors.successGreen;
      case HouseSystemType.wholeSign:
        return AppColors.solarAmber;
      case HouseSystemType.special:
        return AppColors.warning;
    }
  }

  /// 獲取難度文字
  String _getDifficultyText(DifficultyLevel level) {
    switch (level) {
      case DifficultyLevel.beginner:
        return '初學者';
      case DifficultyLevel.intermediate:
        return '中級';
      case DifficultyLevel.advanced:
        return '高級';
      case DifficultyLevel.expert:
        return '專家';
    }
  }

  /// 獲取難度顏色
  Color _getDifficultyColor(DifficultyLevel level) {
    switch (level) {
      case DifficultyLevel.beginner:
        return AppColors.successGreen;
      case DifficultyLevel.intermediate:
        return AppColors.solarAmber;
      case DifficultyLevel.advanced:
        return AppColors.warning;
      case DifficultyLevel.expert:
        return AppColors.error;
    }
  }

  /// 獲取歷史時期文字
  String _getHistoricalPeriodText(HistoricalPeriod period) {
    switch (period) {
      case HistoricalPeriod.ancient:
        return '古代';
      case HistoricalPeriod.medieval:
        return '中世紀';
      case HistoricalPeriod.renaissance:
        return '文藝復興';
      case HistoricalPeriod.modern:
        return '現代';
      case HistoricalPeriod.contemporary:
        return '當代';
    }
  }

  /// 獲取宮位系統類型文字
  String _getHouseSystemTypeText(HouseSystemType type) {
    switch (type) {
      case HouseSystemType.timeBasedQuadrant:
        return '時間象限制';
      case HouseSystemType.spaceBasedQuadrant:
        return '空間象限制';
      case HouseSystemType.equalDivision:
        return '等分制';
      case HouseSystemType.wholeSign:
        return '整宮制';
      case HouseSystemType.special:
        return '特殊用途';
    }
  }

  /// 構建宮位系統選擇器（優化版）
  Widget _buildHouseSystemSelector(SettingsViewModel viewModel) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.home_work,
                color: AppColors.royalIndigo,
                size: 20,
              ),
              const SizedBox(width: 8),
              const Text(
                '宮位系統',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textDark,
                ),
              ),
              const Spacer(),
              // 快速選擇按鈕
              _buildQuickSelectButtons(viewModel),
            ],
          ),
          const SizedBox(height: 12),

          // 宮位系統下拉選擇
          DropdownButtonFormField<HouseSystem>(
            value: viewModel.currentChartTypeSettings?.houseSystem ?? HouseSystem.placidus,
            decoration: InputDecoration(
              labelText: '選擇宮位系統',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            ),
            items: HouseSystem.values.map((system) {
              return DropdownMenuItem<HouseSystem>(
                value: system,
                child: Row(
                  children: [
                    Icon(
                      _getHouseSystemTypeIcon(system.type),
                      size: 16,
                      color: _getHouseSystemTypeColor(system.type),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            system.displayName,
                            style: const TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          if (system.isCommonlyUsed)
                            Text(
                              '常用',
                              style: TextStyle(
                                fontSize: 10,
                                color: AppColors.successGreen,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                        ],
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
            onChanged: (HouseSystem? newSystem) {
              if (newSystem != null) {
                viewModel.updateHouseSystem(newSystem);
              }
            },
          ),
        ],
      ),
    );
  }

  /// 構建快速選擇按鈕
  Widget _buildQuickSelectButtons(SettingsViewModel viewModel) {
    final commonSystems = [
      HouseSystem.placidus,
      HouseSystem.equal,
      HouseSystem.wholeSign,
      HouseSystem.koch,
    ];

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        PopupMenuButton<HouseSystem>(
          icon: Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: AppColors.royalIndigo.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(6),
            ),
            child: const Icon(
              Icons.flash_on,
              color: AppColors.royalIndigo,
              size: 16,
            ),
          ),
          tooltip: '快速選擇常用宮位系統',
          itemBuilder: (context) => commonSystems.map((system) {
            final isSelected = viewModel.currentChartTypeSettings?.houseSystem == system;
            return PopupMenuItem<HouseSystem>(
              value: system,
              child: Row(
                children: [
                  Icon(
                    _getHouseSystemTypeIcon(system.type),
                    size: 16,
                    color: isSelected
                        ? AppColors.royalIndigo
                        : _getHouseSystemTypeColor(system.type),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    system.displayName,
                    style: TextStyle(
                      fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                      color: isSelected ? AppColors.royalIndigo : null,
                    ),
                  ),
                  if (isSelected) ...[
                    const Spacer(),
                    const Icon(
                      Icons.check,
                      color: AppColors.royalIndigo,
                      size: 16,
                    ),
                  ],
                ],
              ),
            );
          }).toList(),
          onSelected: (HouseSystem system) {
            viewModel.updateHouseSystem(system);
          },
        ),
      ],
    );
  }

  /// 構建行星顯示設置
  Widget _buildPlanetVisibilitySection(SettingsViewModel viewModel) {
    return StyledCard(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(
                  Icons.public,
                  color: AppColors.solarAmber,
                  size: 20,
                ),
                SizedBox(width: 8),
                Text(
                  '行星顯示',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textDark,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              '選擇要在星盤中顯示的行星和天體。',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 16),
            ...(viewModel.currentChartTypeSettings?.planetVisibility.entries ??
                    {})
                .map((entry) {
              return Container(
                margin: const EdgeInsets.only(bottom: 8),
                child: Row(
                  children: [
                    SizedBox(
                      width: 24,
                      height: 24,
                      child: Checkbox(
                        value: entry.value,
                        onChanged: (bool? value) {
                          if (value != null) {
                            viewModel.updatePlanetVisibility(entry.key, value);
                          }
                        },
                        activeColor: AppColors.solarAmber,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        entry.key,
                        style: const TextStyle(
                          fontSize: 14,
                          color: AppColors.textDark,
                        ),
                      ),
                    ),
                    // _buildPlanetIcon(entry.key),
                  ],
                ),
              );
            }).toList(),
          ],
        ),
      ),
    );
  }

  /// 構建行星圖標
  Widget _buildPlanetIcon(String planetName) {
    IconData icon;
    Color color;

    switch (planetName) {
      case '太陽':
        icon = Icons.wb_sunny;
        color = Colors.orange;
        break;
      case '月亮':
        icon = Icons.nightlight_round;
        color = Colors.blue;
        break;
      case '水星':
        icon = Icons.speed;
        color = Colors.grey;
        break;
      case '金星':
        icon = Icons.favorite;
        color = Colors.pink;
        break;
      case '火星':
        icon = Icons.whatshot;
        color = Colors.red;
        break;
      case '木星':
        icon = Icons.expand;
        color = Colors.purple;
        break;
      case '土星':
        icon = Icons.schedule;
        color = Colors.brown;
        break;
      default:
        icon = Icons.circle;
        color = Colors.grey;
    }

    return Icon(icon, color: color, size: 16);
  }

  /// 構建星座界主星設置
  Widget _buildZodiacRulersSection(SettingsViewModel viewModel) {
    return StyledCard(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(
                  Icons.stars,
                  color: AppColors.royalIndigo,
                  size: 20,
                ),
                SizedBox(width: 8),
                Text(
                  '星座界主星',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textDark,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text('顯示星座界主星'),
              subtitle: const Text('在星盤外圈顯示星座界主星符號'),
              value:
                  viewModel.currentChartTypeSettings?.showZodiacRulers ?? false,
              onChanged: (bool value) {
                viewModel.updateZodiacRulersVisibility(value);
              },
              activeColor: AppColors.royalIndigo,
              contentPadding: EdgeInsets.zero,
            ),
            const Divider(height: 1, color: AppColors.textLight),
            SwitchListTile(
              title: const Text('顯示星座背景色'),
              subtitle: const Text('為每個星座區域添加淡色背景'),
              value:
                  viewModel.currentChartTypeSettings?.showZodiacBackground ?? false,
              onChanged: (bool value) {
                viewModel.updateZodiacBackgroundVisibility(value);
              },
              activeColor: AppColors.royalIndigo,
              contentPadding: EdgeInsets.zero,
            ),
            if (viewModel.currentChartTypeSettings?.showZodiacRulers ??
                false) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppColors.royalIndigo.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    const Icon(
                      Icons.info_outline,
                      color: AppColors.royalIndigo,
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        '星座界主星將顯示在星盤外圈，幫助您更好地理解星座的統治關係。',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade700,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// 構建度數顯示設置
  Widget _buildDegreeDisplaySection(SettingsViewModel viewModel) {
    return StyledCard(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.straighten,
                  color: Colors.blue.shade600,
                  size: 20,
                ),
                const SizedBox(width: 8),
                const Text(
                  '度數顯示設置',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textDark,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildDegreeSettingTile(
              title: '顯示宮位度數',
              subtitle: '在宮位線旁顯示宮頭度數',
              icon: Icons.home,
              value:
                  viewModel.currentChartTypeSettings?.showHouseDegrees ?? false,
              onChanged: (value) =>
                  viewModel.updateHouseDegreesVisibility(value),
            ),
            const SizedBox(height: 8),
            _buildDegreeSettingTile(
              title: '顯示行星度數',
              subtitle: '在行星符號旁顯示精確度數',
              icon: Icons.public,
              value: viewModel.currentChartTypeSettings?.showPlanetDegrees ??
                  false,
              onChanged: (value) =>
                  viewModel.updatePlanetDegreesVisibility(value),
            ),
          ],
        ),
      ),
    );
  }

  /// 構建度數設置項目
  Widget _buildDegreeSettingTile({
    required String title,
    required String subtitle,
    required IconData icon,
    required bool value,
    required Function(bool) onChanged,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Row(
        children: [
          Icon(
            icon,
            color: Colors.blue.shade600,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: AppColors.textDark,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: Colors.blue.shade600,
          ),
        ],
      ),
    );
  }

  /// 構建相位設置區域
  Widget _buildAspectSettingsSection(SettingsViewModel viewModel) {
    return StyledCard(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.timeline,
                  color: Colors.purple.shade600,
                  size: 20,
                ),
                const SizedBox(width: 8),
                const Text(
                  '相位設置',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textDark,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: Text(
                    '調整各種相位的容許度範圍。${viewModel.currentChartType?.displayName ?? '當前星盤類型'}有專用的預設值。',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ),
                TextButton.icon(
                  onPressed: () => _resetToChartTypeDefaults(viewModel),
                  icon: const Icon(
                    Icons.refresh,
                    size: 16,
                    color: AppColors.indigoLight,
                  ),
                  label: const Text(
                    '重置預設',
                    style: TextStyle(
                      fontSize: 12,
                      color: AppColors.indigoLight,
                    ),
                  ),
                  style: TextButton.styleFrom(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    minimumSize: Size.zero,
                    tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...(viewModel.currentChartTypeSettings?.aspectOrbs.entries ?? {})
                .map((entry) {
              return Builder(
                builder: (context) => _buildAspectOrbSlider(
                  context,
                  viewModel,
                  entry.key,
                  entry.value,
                ),
              );
            }).toList(),
            const SizedBox(height: 16),
            Builder(
              builder: (context) => _buildAspectPresets(context, viewModel),
            ),
          ],
        ),
      ),
    );
  }

  /// 構建相位容許度滑桿
  Widget _buildAspectOrbSlider(
    BuildContext context,
    SettingsViewModel viewModel,
    String aspectName,
    double currentValue,
  ) {
    final defaultValue = _getDefaultAspectOrbForChartType(viewModel.currentChartType, aspectName);
    final isDefault = defaultValue != null && currentValue == defaultValue;

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isDefault
            ? _getAspectColor(aspectName).withValues(alpha: 0.05)
            : Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isDefault
              ? _getAspectColor(aspectName).withValues(alpha: 0.3)
              : Colors.grey.shade200,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      aspectName,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: AppColors.textDark,
                      ),
                    ),
                    if (defaultValue != null && !isDefault)
                      Text(
                        '預設：${defaultValue.toInt()}°',
                        style: TextStyle(
                          fontSize: 11,
                          color: Colors.grey.shade600,
                        ),
                      ),
                  ],
                ),
              ),
              Row(
                children: [
                  if (isDefault)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      margin: const EdgeInsets.only(right: 8),
                      decoration: BoxDecoration(
                        color: AppColors.success.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Text(
                        '預設',
                        style: TextStyle(
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                          color: AppColors.success,
                        ),
                      ),
                    ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: _getAspectColor(aspectName).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '${currentValue.toInt()}°',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: _getAspectColor(aspectName),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 8),
          SliderTheme(
            data: SliderTheme.of(context).copyWith(
              activeTrackColor: _getAspectColor(aspectName),
              thumbColor: _getAspectColor(aspectName),
              overlayColor: _getAspectColor(aspectName).withValues(alpha: 0.2),
              inactiveTrackColor: Colors.grey.shade300,
            ),
            child: Slider(
              value: currentValue,
              min: 0,
              max: 30,
              divisions: 30,
              onChanged: (double value) {
                viewModel.updateAspectOrb(aspectName, value);
              },
            ),
          ),
        ],
      ),
    );
  }

  /// 構建相位預設按鈕
  Widget _buildAspectPresets(
      BuildContext context, SettingsViewModel viewModel) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '快速設定',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: AppColors.textDark,
          ),
        ),
        const SizedBox(height: 12),
        // 根據星盤類型顯示不同的預設按鈕
        if (viewModel.currentChartType == ChartType.natal) ...[
          _buildPresetButton(
            '本命盤標準',
            '合相: 8°, 對相: 8°, 三分相: 6°, 四分相: 6°, 六分相: 4°',
            Icons.person,
            Colors.blue,
            () => _applyAspectPreset('natal_standard', viewModel, context),
          ),
          _buildPresetButton(
            '本命盤嚴格',
            '合相: 6°, 對相: 6°, 三分相: 4°, 四分相: 4°, 六分相: 3°',
            Icons.precision_manufacturing,
            Colors.blue.shade700,
            () => _applyAspectPreset('natal_strict', viewModel, context),
          ),
        ] else if (viewModel.currentChartType == ChartType.transit) ...[
          _buildPresetButton(
            '流年盤標準',
            '合相: 6°, 對相: 6°, 三分相: 4°, 四分相: 4°, 六分相: 3°',
            Icons.timeline,
            Colors.green,
            () => _applyAspectPreset('transit_standard', viewModel, context),
          ),
          _buildPresetButton(
            '流年盤精確',
            '合相: 4°, 對相: 4°, 三分相: 3°, 四分相: 3°, 六分相: 2°',
            Icons.precision_manufacturing,
            Colors.green.shade700,
            () => _applyAspectPreset('transit_precise', viewModel, context),
          ),
        ] else if (viewModel.currentChartType == ChartType.eclipse) ...[
          _buildPresetButton(
            '日月蝕盤標準',
            '合相: 10°, 對相: 10°, 三分相: 8°, 四分相: 8°, 六分相: 5°',
            Icons.brightness_2,
            Colors.indigo,
            () => _applyAspectPreset('eclipse_standard', viewModel, context),
          ),
          _buildPresetButton(
            '日月蝕盤寬鬆',
            '合相: 12°, 對相: 12°, 三分相: 10°, 四分相: 10°, 六分相: 6°',
            Icons.brightness_high,
            Colors.indigo.shade700,
            () => _applyAspectPreset('eclipse_wide', viewModel, context),
          ),
        ] else ...[
          _buildPresetButton(
            '標準設定',
            '合相: 8°, 對相: 8°, 三分相: 6°, 四分相: 6°, 六分相: 4°',
            Icons.star,
            Colors.blue,
            () => _applyAspectPreset('standard', viewModel, context),
          ),
          _buildPresetButton(
            '嚴格設定',
            '合相: 6°, 對相: 6°, 三分相: 4°, 四分相: 4°, 六分相: 3°',
            Icons.precision_manufacturing,
            Colors.purple,
            () => _applyAspectPreset('strict', viewModel, context),
          ),
        ],
      ],
    );
  }

  /// 構建預設按鈕
  Widget _buildPresetButton(
    String title,
    String description,
    IconData icon,
    Color color,
    VoidCallback onPressed,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(8),
          child: Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: color.withValues(alpha: 0.2),
              ),
            ),
            child: Row(
              children: [
                Icon(icon, color: color, size: 16),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: color,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        description,
                        style: TextStyle(
                          fontSize: 10,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  color: color,
                  size: 12,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 獲取相位顏色
  Color _getAspectColor(String aspectName) {
    switch (aspectName) {
      case '合相':
        return Colors.red;
      case '六分相':
        return Colors.green;
      case '四分相':
        return Colors.red;
      case '三分相':
        return Colors.green;
      case '對分相':
        return const Color(0xFF0A0AFD);
      default:
        return Colors.grey;
    }
  }

  /// 應用相位預設設定
  void _applyAspectPreset(
      String presetType, SettingsViewModel viewModel, BuildContext context) {
    Map<String, double> newOrbs = {};

    switch (presetType) {
      case 'natal_standard':
        newOrbs = {
          '合相': 8.0,
          '六分相': 4.0,
          '四分相': 6.0,
          '三分相': 6.0,
          '對分相': 8.0,
        };
        break;
      case 'natal_strict':
        newOrbs = {
          '合相': 6.0,
          '六分相': 3.0,
          '四分相': 4.0,
          '三分相': 4.0,
          '對分相': 6.0,
        };
        break;
      case 'transit_standard':
        newOrbs = {
          '合相': 6.0,
          '六分相': 3.0,
          '四分相': 4.0,
          '三分相': 4.0,
          '對分相': 6.0,
        };
        break;
      case 'transit_precise':
        newOrbs = {
          '合相': 4.0,
          '六分相': 2.0,
          '四分相': 3.0,
          '三分相': 3.0,
          '對分相': 4.0,
        };
        break;
      case 'eclipse_standard':
        newOrbs = {
          '合相': 10.0,
          '六分相': 5.0,
          '四分相': 8.0,
          '三分相': 8.0,
          '對分相': 10.0,
        };
        break;
      case 'eclipse_wide':
        newOrbs = {
          '合相': 12.0,
          '六分相': 6.0,
          '四分相': 10.0,
          '三分相': 10.0,
          '對分相': 12.0,
        };
        break;
      case 'standard':
      default:
        newOrbs = {
          '合相': 8.0,
          '六分相': 4.0,
          '四分相': 6.0,
          '三分相': 6.0,
          '對分相': 8.0,
        };
        break;
    }

    // 應用新的容許度設置
    for (final entry in newOrbs.entries) {
      viewModel.updateAspectOrb(entry.key, entry.value);
    }

    // 顯示成功訊息
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('已應用 ${_getPresetDisplayName(presetType)} 設置'),
        backgroundColor: AppColors.royalIndigo,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  /// 獲取預設類型的顯示名稱
  String _getPresetDisplayName(String presetType) {
    switch (presetType) {
      case 'natal_standard':
        return '本命盤標準';
      case 'natal_strict':
        return '本命盤嚴格';
      case 'transit_standard':
        return '流年盤標準';
      case 'transit_precise':
        return '流年盤精確';
      case 'eclipse_standard':
        return '日月蝕盤標準';
      case 'eclipse_wide':
        return '日月蝕盤寬鬆';
      case 'standard':
        return '標準';
      case 'strict':
        return '嚴格';
      default:
        return '預設';
    }
  }

  /// 獲取特定星盤類型的預設相位容許度
  double? _getDefaultAspectOrbForChartType(ChartType? chartType, String aspectName) {
    if (chartType == null) return null;

    switch (chartType) {
      case ChartType.natal:
        switch (aspectName) {
          case '合相': return 8.0;
          case '六分相': return 4.0;
          case '四分相': return 8.0;
          case '三分相': return 8.0;
          case '對分相': return 8.0;
          default: return null;
        }
      case ChartType.transit:
        switch (aspectName) {
          case '合相': return 6.0;
          case '六分相': return 3.0;
          case '四分相': return 6.0;
          case '三分相': return 6.0;
          case '對分相': return 6.0;
          default: return null;
        }
      case ChartType.synastry:
        switch (aspectName) {
          case '合相': return 8.0;
          case '六分相': return 4.0;
          case '四分相': return 8.0;
          case '三分相': return 8.0;
          case '對分相': return 8.0;
          default: return null;
        }
      case ChartType.eclipse:
        switch (aspectName) {
          case '合相': return 10.0;
          case '六分相': return 5.0;
          case '四分相': return 8.0;
          case '三分相': return 8.0;
          case '對分相': return 10.0;
          default: return null;
        }
      case ChartType.equinoxSolstice:
        switch (aspectName) {
          case '合相': return 8.0;
          case '六分相': return 4.0;
          case '四分相': return 6.0;
          case '三分相': return 6.0;
          case '對分相': return 8.0;
          default: return null;
        }
      default:
        switch (aspectName) {
          case '合相': return 8.0;
          case '六分相': return 4.0;
          case '四分相': return 8.0;
          case '三分相': return 8.0;
          case '對分相': return 8.0;
          default: return null;
        }
    }
  }

  /// 重置到星盤類型的預設值
  void _resetToChartTypeDefaults(SettingsViewModel viewModel) {
    final chartType = viewModel.currentChartType;
    if (chartType == null) return;

    final defaultOrbs = <String, double>{};
    final aspectNames = ['合相', '六分相', '四分相', '三分相', '對分相'];

    for (final aspectName in aspectNames) {
      final defaultValue = _getDefaultAspectOrbForChartType(chartType, aspectName);
      if (defaultValue != null) {
        defaultOrbs[aspectName] = defaultValue;
      }
    }

    // 應用預設值
    for (final entry in defaultOrbs.entries) {
      viewModel.updateAspectOrb(entry.key, entry.value);
    }

    // 顯示成功訊息
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('已重置為${chartType.displayName}的預設容許度'),
        backgroundColor: AppColors.success,
        duration: const Duration(seconds: 2),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  /// 構建設置卡片
  Widget _buildSettingCard({
    required String title,
    required IconData icon,
    required Color iconColor,
    required Widget child,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.cardBackground,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 標題欄
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: iconColor.withValues(alpha: 0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: iconColor.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    icon,
                    color: iconColor,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: iconColor,
                  ),
                ),
              ],
            ),
          ),
          // 內容
          child,
        ],
      ),
    );
  }

  /// 構建單選選項
  Widget _buildRadioOption({
    required String title,
    required String subtitle,
    required String value,
    required String groupValue,
    required ValueChanged<String?> onChanged,
  }) {
    return RadioListTile<String>(
      title: Text(
        title,
        style: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w600,
          color: AppColors.textDark,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: const TextStyle(
          fontSize: 12,
          color: AppColors.textMedium,
        ),
      ),
      value: value,
      groupValue: groupValue,
      onChanged: onChanged,
      activeColor: AppColors.royalIndigo,
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
    );
  }

  /// 獲取當前顯示模式
  String _getCurrentDisplayMode(SettingsViewModel settingsViewModel) {
    final settings = settingsViewModel.chartSettings;
    if (settings == null) return 'classic';

    if (settings.showHouseDegrees && settings.showPlanetDegrees) {
      return 'degrees';
    }
    return 'classic';
  }

  /// 設置顯示模式
  void _setDisplayMode(SettingsViewModel settingsViewModel, String mode) {
    if (mode == 'degrees') {
      settingsViewModel.updateHouseDegreesVisibility(true);
      settingsViewModel.updatePlanetDegreesVisibility(true);
    } else {
      settingsViewModel.updateHouseDegreesVisibility(false);
      settingsViewModel.updatePlanetDegreesVisibility(false);
    }
  }

  /// 顯示重置對話框
  void _showResetDialog(BuildContext context, SettingsViewModel settingsViewModel) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: const Row(
          children: [
            Icon(
              Icons.refresh,
              color: AppColors.warning,
              size: 24,
            ),
            SizedBox(width: 8),
            Text(
              '重置設置',
              style: TextStyle(
                color: AppColors.textDark,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        content: const Text(
          '確定要將所有星盤顯示設置重置為預設值嗎？',
          style: TextStyle(
            color: AppColors.textMedium,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text(
              '取消',
              style: TextStyle(color: AppColors.textMedium),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              settingsViewModel.resetChartSettings();
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: const Text('設置已重置為預設值'),
                  backgroundColor: AppColors.success,
                  duration: const Duration(seconds: 2),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.royalIndigo,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text('確定'),
          ),
        ],
      ),
    );
  }

  /// 獲取星盤類型描述
  String _getChartTypeDescription(ChartType chartType) {
    switch (chartType) {
      case ChartType.natal:
        return '個人出生星盤，分析性格與命運';
      case ChartType.transit:
        return '流年星盤，預測未來趨勢';
      case ChartType.synastry:
        return '合盤分析，探索關係相容性';
      case ChartType.composite:
        return '組合盤，分析關係本質';
      case ChartType.davison:
        return '戴維森盤，關係的中點分析';
      case ChartType.solarReturn:
        return '太陽回歸盤，年度運勢預測';
      case ChartType.lunarReturn:
        return '月亮回歸盤，月度情緒週期';
      case ChartType.eclipse:
        return '日月蝕盤，重大轉折分析';
      case ChartType.equinoxSolstice:
        return '分至點盤，季節能量分析';
      case ChartType.horary:
        return '卜卦占星，回答具體問題';
      case ChartType.mundane:
        return '世俗占星，分析社會事件';
      default:
        return '星盤分析工具';
    }
  }

  /// 獲取星盤類型圖標
  IconData _getChartTypeIcon(ChartType chartType) {
    switch (chartType) {
      case ChartType.natal:
        return Icons.person;
      case ChartType.transit:
        return Icons.timeline;
      case ChartType.synastry:
        return Icons.favorite;
      case ChartType.composite:
        return Icons.merge_type;
      case ChartType.davison:
        return Icons.center_focus_strong;
      case ChartType.solarReturn:
        return Icons.wb_sunny;
      case ChartType.lunarReturn:
        return Icons.nightlight_round;
      case ChartType.eclipse:
        return Icons.brightness_2;
      case ChartType.equinoxSolstice:
        return Icons.calendar_today;
      case ChartType.horary:
        return Icons.help_outline;
      case ChartType.mundane:
        return Icons.public;
      default:
        return Icons.category;
    }
  }

  /// 顯示還原星盤類型設定對話框
  void _showResetChartTypeDialog(SettingsViewModel settingsViewModel) {
    final currentChartType = settingsViewModel.currentChartType;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppColors.warning.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                _getChartTypeIcon(currentChartType),
                color: AppColors.warning,
                size: 20,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                '還原設定',
                style: const TextStyle(
                  color: AppColors.textDark,
                  fontWeight: FontWeight.bold,
                  fontSize: 18,
                ),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '確定要將「${currentChartType.displayName}」的所有設定還原為預設值嗎？',
              style: const TextStyle(
                color: AppColors.textMedium,
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.warning.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: AppColors.warning.withValues(alpha: 0.3),
                ),
              ),
              child: const Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: AppColors.warning,
                    size: 16,
                  ),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      '這將重置宮位系統、行星顯示、相位設定等所有選項',
                      style: TextStyle(
                        fontSize: 12,
                        color: AppColors.warning,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text(
              '取消',
              style: TextStyle(color: AppColors.textMedium),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              _resetChartTypeSettings(settingsViewModel, currentChartType);
              Navigator.of(context).pop();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.warning,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text('確定還原'),
          ),
        ],
      ),
    );
  }

  /// 還原指定星盤類型的設定
  void _resetChartTypeSettings(SettingsViewModel settingsViewModel, ChartType chartType) {
    if (settingsViewModel.multiChartSettings != null) {
      // 創建該星盤類型的預設設定
      final defaultSettings = _createDefaultSettingsForChartType(chartType);

      // 更新設定
      settingsViewModel.multiChartSettings!.updateSettingsForChartType(chartType, defaultSettings);

      // 保存設定
      settingsViewModel.saveMultiChartSettings();

      // 顯示成功訊息
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.check_circle, color: Colors.white),
              const SizedBox(width: 8),
              Expanded(
                child: Text('「${chartType.displayName}」設定已還原為預設值'),
              ),
            ],
          ),
          backgroundColor: AppColors.success,
          duration: const Duration(seconds: 3),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      );
    }
  }

  /// 創建指定星盤類型的預設設定
  ChartTypeSettings _createDefaultSettingsForChartType(ChartType chartType) {
    return ChartTypeSettings(
      houseSystem: HouseSystem.placidus,
      planetVisibility: {
        '太陽': true,
        '月亮': true,
        '水星': true,
        '金星': true,
        '火星': true,
        '木星': true,
        '土星': true,
        '天王星': true,
        '海王星': true,
        '冥王星': true,
        '上升': true,
        '中天': true,
        '下降': true,
        '天底': true,
        '北交點': true,
        '南交點': true,
        '莉莉絲': false,
        '凱龍星': false,
        '人龍星': false,
        '穀神星': false,
        '智神星': false,
        '婚神星': false,
        '灶神星': false,
        '幸運點': true,
        '精神點': false,
        '旺點': false,
        '日月中點': false,
        '宿命點': false,
      },
      aspectOrbs: {
        '合相': 8.0,
        '六分相': 4.0,
        '四分相': 8.0,
        '三分相': 8.0,
        '對分相': 8.0,
        '十二分相': 2.0,
        '八分相': 3.0,
        '五分相': 2.0,
        '十分相': 1.5,
        '九分相': 2.0,
        '七分相': 1.5,
        '十一分相': 1.5,
      },
      showZodiacRulers: false,
      showHouseDegrees: false,
      showPlanetDegrees: false,
      showMajorAspects: true,
      showMinorAspects: false,
      colorTheme: 'modern',
      astrologyMode: 'modern',
    );
  }

  /// 構建占星模式設置卡片
  Widget _buildAstrologyModeCard(SettingsViewModel settingsViewModel) {
    return _buildSettingCard(
      title: '占星模式',
      icon: Icons.auto_awesome,
      iconColor: AppColors.royalIndigo,
      child: Column(
        children: [
          // 古典占星模式
          _buildAstrologyModeOption(
            settingsViewModel,
            AstrologyMode.classical,
            '古典占星',
            '整宮制 • 傳統七星 • 界主星顯示',
            Icons.auto_awesome,
            Colors.amber.shade700,
          ),
          const Divider(height: 1, color: AppColors.textLight),

          // 現代占星模式
          _buildAstrologyModeOption(
            settingsViewModel,
            AstrologyMode.modern,
            '現代占星',
            'Placidus宮位制 • 現代行星 • 簡潔顯示',
            Icons.psychology,
            Colors.blue.shade700,
          ),
          const Divider(height: 1, color: AppColors.textLight),

          // 特殊模式
          _buildAstrologyModeOption(
            settingsViewModel,
            AstrologyMode.special,
            '特殊模式',
            '等宮制 • 所有行星 • 特殊點顯示',
            Icons.star,
            Colors.purple.shade700,
          ),
        ],
      ),
    );
  }

  /// 構建占星模式選項
  Widget _buildAstrologyModeOption(
    SettingsViewModel settingsViewModel,
    AstrologyMode mode,
    String title,
    String subtitle,
    IconData icon,
    Color color,
  ) {
    final currentMode = _getCurrentAstrologyMode(settingsViewModel);
    final isSelected = currentMode == mode;

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () => _applyAstrologyMode(settingsViewModel, mode),
        child: Container(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // 模式圖標
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: color.withValues(alpha: isSelected ? 0.2 : 0.1),
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(
                    color: isSelected ? color : color.withValues(alpha: 0.3),
                    width: isSelected ? 2 : 1,
                  ),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 20,
                ),
              ),
              const SizedBox(width: 16),

              // 模式信息
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: isSelected ? color : AppColors.textDark,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      subtitle,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                        height: 1.3,
                      ),
                    ),
                  ],
                ),
              ),

              // 選中指示器
              if (isSelected)
                Icon(
                  Icons.check_circle,
                  color: color,
                  size: 20,
                ),
            ],
          ),
        ),
      ),
    );
  }

  /// 獲取當前占星模式
  AstrologyMode _getCurrentAstrologyMode(SettingsViewModel settingsViewModel) {
    final settings = settingsViewModel.currentChartTypeSettings;
    if (settings == null) return AstrologyMode.modern;

    // 直接從設定中讀取占星模式
    switch (settings.astrologyMode) {
      case 'classical':
        return AstrologyMode.classical;
      case 'special':
        return AstrologyMode.special;
      case 'modern':
      default:
        return AstrologyMode.modern;
    }
  }

  /// 應用占星模式設定
  void _applyAstrologyMode(SettingsViewModel settingsViewModel, AstrologyMode mode) {
    // 獲取模式配置
    final config = AstrologyModeConfig.getConfig(mode);
    if (config == null) return;

    // 應用模式的預設設定
    _applyModeSettings(settingsViewModel, config.defaultSettings);

    // 記錄占星模式到設定中
    settingsViewModel.updateAstrologyMode(config.name);

    // 顯示應用成功的訊息
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('已應用「${config.displayName}」模式設定'),
        backgroundColor: AppColors.successGreen,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  /// 應用模式設定到當前星盤類型
  void _applyModeSettings(SettingsViewModel settingsViewModel, ChartTypeSettings modeSettings) {
    // 應用宮位系統
    settingsViewModel.updateHouseSystem(modeSettings.houseSystem);

    // 應用顯示設定
    settingsViewModel.updateZodiacRulersVisibility(modeSettings.showZodiacRulers);
    settingsViewModel.updateHouseDegreesVisibility(modeSettings.showHouseDegrees);
    settingsViewModel.updatePlanetDegreesVisibility(modeSettings.showPlanetDegrees);
    settingsViewModel.updateMajorAspectsVisibility(modeSettings.showMajorAspects);
    settingsViewModel.updateMinorAspectsVisibility(modeSettings.showMinorAspects);

    // 應用行星顯示設定
    for (final entry in modeSettings.planetVisibility.entries) {
      settingsViewModel.updatePlanetVisibility(entry.key, entry.value);
    }

    // 應用相位容許度設定
    for (final entry in modeSettings.aspectOrbs.entries) {
      settingsViewModel.updateAspectOrb(entry.key, entry.value);
    }

    // 應用顏色主題（如果有對應的方法）
    // settingsViewModel.updateColorTheme(modeSettings.colorTheme);
  }
}
