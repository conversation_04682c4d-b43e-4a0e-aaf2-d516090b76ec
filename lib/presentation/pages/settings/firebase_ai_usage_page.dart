import 'package:flutter/material.dart';

import '../../../astreal.dart';
import '../../../data/services/api/ai_api_service.dart';
import '../../../data/services/api/firebase_ai_usage_service.dart';

/// Firebase AI 使用量管理頁面
class FirebaseAIUsagePage extends StatefulWidget {
  const FirebaseAIUsagePage({super.key});

  @override
  State<FirebaseAIUsagePage> createState() => _FirebaseAIUsagePageState();
}

class _FirebaseAIUsagePageState extends State<FirebaseAIUsagePage> {
  List<FirebaseAIUsageRecord> _records = [];
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadUsageData();
  }

  Future<void> _loadUsageData() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final records = await FirebaseAIUsageService.getAllTodayUsage();
      setState(() {
        _records = records;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _resetUsage() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('重置使用量'),
        content: const Text('確定要重置今天的 AI 使用量嗎？此操作無法復原。\n\n⚠️ 注意：這僅供測試使用。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('確定重置'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await FirebaseAIUsageService.resetAllUsage();
        await _loadUsageData();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('✅ AI 使用量已重置'),
              backgroundColor: AppColors.royalIndigo,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('❌ 重置失敗：$e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('AI 使用量控管'),
        backgroundColor: AppColors.royalIndigo,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadUsageData,
            tooltip: '重新載入',
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              if (value == 'reset') {
                _resetUsage();
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'reset',
                child: Row(
                  children: [
                    Icon(Icons.restore, color: Colors.red),
                    SizedBox(width: 8),
                    Text('重置使用量'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(color: AppColors.royalIndigo),
            SizedBox(height: 16),
            Text('載入使用量資料中...'),
          ],
        ),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              '載入失敗',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32),
              child: Text(
                _errorMessage!,
                textAlign: TextAlign.center,
                style: TextStyle(color: Colors.grey.shade600),
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _loadUsageData,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.royalIndigo,
                foregroundColor: Colors.white,
              ),
              child: const Text('重試'),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadUsageData,
      color: AppColors.royalIndigo,
      child: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          _buildSummaryCard(),
          const SizedBox(height: 16),
          _buildUsageList(),
          const SizedBox(height: 16),
          _buildInfoCard(),
        ],
      ),
    );
  }

  Widget _buildSummaryCard() {
    final totalTokens = _records.fold<int>(0, (sum, record) => sum + record.totalTokens);
    final totalCalls = _records.fold<int>(0, (sum, record) => sum + record.callCount);
    final limitReachedCount = _records.where((record) => record.limitReached).length;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.analytics, color: AppColors.royalIndigo),
                const SizedBox(width: 8),
                Text(
                  '今日使用量總覽',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem('總 Tokens', totalTokens.toString()),
                ),
                Expanded(
                  child: _buildSummaryItem('總調用次數', totalCalls.toString()),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    '達限制數',
                    limitReachedCount.toString(),
                    color: limitReachedCount > 0 ? Colors.red : Colors.green,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryItem(String label, String value, {Color? color}) {
    return Column(
      children: [
        Text(
          value,
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            color: color ?? AppColors.royalIndigo,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600,
          ),
        ),
      ],
    );
  }

  Widget _buildUsageList() {
    return Card(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                const Icon(Icons.list, color: AppColors.royalIndigo),
                const SizedBox(width: 8),
                Text(
                  'AI 提供商使用詳情',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
          ),
          const Divider(height: 1),
          ..._records.map((record) => _buildUsageItem(record)),
        ],
      ),
    );
  }

  Widget _buildUsageItem(FirebaseAIUsageRecord record) {
    final provider = _getAIProvider(record.aiProvider);
    final dailyLimit = FirebaseAIUsageService.getDailyLimits()[provider] ?? 200000;
    final usagePercentage = (record.totalTokens / dailyLimit * 100).clamp(0.0, 100.0);

    return ListTile(
      leading: CircleAvatar(
        backgroundColor: record.limitReached ? Colors.red : AppColors.royalIndigo,
        child: Text(
          _getProviderIcon(record.aiProvider),
          style: const TextStyle(color: Colors.white),
        ),
      ),
      title: Text(provider.displayName),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 4),
          LinearProgressIndicator(
            value: usagePercentage / 100,
            backgroundColor: Colors.grey.shade200,
            valueColor: AlwaysStoppedAnimation<Color>(
              record.limitReached ? Colors.red : AppColors.royalIndigo,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            '${record.totalTokens.toStringAsFixed(0)} / ${dailyLimit.toStringAsFixed(0)} tokens (${usagePercentage.toStringAsFixed(1)}%)',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ),
      trailing: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Text(
            '${record.callCount} 次',
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          if (record.limitReached)
            const Text(
              '已達限制',
              style: TextStyle(
                color: Colors.red,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.info, color: AppColors.royalIndigo),
                const SizedBox(width: 8),
                Text(
                  '使用說明',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
              ],
            ),
            const SizedBox(height: 12),
            const Text(
              '• 每個 AI 提供商每日限制 20 萬 tokens\n'
              '• 達到限制後當日無法繼續使用該提供商\n'
              '• 使用量會在每日 UTC 00:00 重置\n'
              '• 資料同步到 Firebase 雲端儲存',
              style: TextStyle(height: 1.5),
            ),
          ],
        ),
      ),
    );
  }

  AIProvider _getAIProvider(String providerName) {
    switch (providerName) {
      case 'gpt4o':
        return AIProvider.openai;
      case 'claude':
        return AIProvider.anthropic;
      case 'groq':
        return AIProvider.groq;
      case 'gemini':
        return AIProvider.gemini;
      default:
        return AIProvider.openai;
    }
  }

  String _getProviderIcon(String providerName) {
    switch (providerName) {
      case 'gpt4o':
        return '🧠';
      case 'claude':
        return '🤖';
      case 'groq':
        return '⚡';
      case 'gemini':
        return '✨';
      default:
        return '🤖';
    }
  }
}
