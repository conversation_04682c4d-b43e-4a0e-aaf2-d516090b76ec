import 'package:flutter/material.dart';

import '../../../astreal.dart';
import '../../../data/services/api/log_management_service.dart';
import 'log_viewer_page.dart';


/// 日誌管理頁面
class LogManagementPage extends StatefulWidget {
  const LogManagementPage({super.key});

  @override
  State<LogManagementPage> createState() => _LogManagementPageState();
}

class _LogManagementPageState extends State<LogManagementPage> {
  LogManagementSettings _settings = LogManagementSettings();
  LogStatistics _statistics = LogStatistics();
  bool _isLoading = false;
  bool _isUploading = false;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await LogManagementService.instance.initialize();
      _settings = LogManagementService.instance.settings;
      _statistics = await LogManagementService.instance.getLogStatistics();
    } catch (e) {
      logger.e('載入日誌管理資料失敗: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('載入資料失敗: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _updateSettings() async {
    try {
      await LogManagementService.instance.updateSettings(_settings);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('設定已保存')),
        );
      }
    } catch (e) {
      logger.e('更新日誌設定失敗: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('保存設定失敗: $e')),
        );
      }
    }
  }

  Future<void> _uploadLogs() async {
    setState(() {
      _isUploading = true;
    });

    try {
      final success = await LogManagementService.instance.uploadLogs();
      
      if (mounted) {
        logger.i('日誌上傳結果: $success');
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(success ? '日誌上傳成功' : '日誌上傳失敗'),
            backgroundColor: success ? Colors.green : Colors.red,
          ),
        );
      }

      if (success) {
        await _loadData(); // 重新載入統計資料
      }
    } catch (e) {
      logger.e('上傳日誌失敗: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('上傳失敗: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUploading = false;
        });
      }
    }
  }

  Future<void> _clearLogs() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('確認清理'),
        content: const Text('確定要清理所有本地日誌嗎？此操作無法復原。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('確認'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await LogManagementService.instance.clearAllLogs();
        await _loadData(); // 重新載入統計資料
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('本地日誌已清理')),
          );
        }
      } catch (e) {
        logger.e('清理日誌失敗: $e');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('清理失敗: $e')),
          );
        }
      }
    }
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
  }

  String _formatDateTime(DateTime? dateTime) {
    if (dateTime == null) return '從未';
    return '${dateTime.year}/${dateTime.month.toString().padLeft(2, '0')}/${dateTime.day.toString().padLeft(2, '0')} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('日誌管理'),
        elevation: 0,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _loadData,
              child: ListView(
                padding: const EdgeInsets.all(16),
                children: [
                  _buildStatisticsCard(),
                  const SizedBox(height: 16),
                  _buildSettingsCard(),
                  const SizedBox(height: 16),
                  _buildActionsCard(),
                ],
              ),
            ),
    );
  }

  Widget _buildStatisticsCard() {
    return StyledCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.analytics, color: Theme.of(context).primaryColor),
                const SizedBox(width: 8),
                Text(
                  '日誌統計',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildStatItem('本地日誌文件大小', _formatFileSize(_statistics.localLogFileSize)),
            _buildStatItem('已上傳日誌數量', '${_statistics.uploadedLogsCount} 個'),
            _buildStatItem('待上傳崩潰報告', '${_statistics.pendingCrashReports} 個'),
            _buildStatItem('待上傳性能日誌', '${_statistics.pendingPerformanceLogs} 個'),
            _buildStatItem('最後上傳時間', _formatDateTime(_statistics.lastUploadTime)),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(
            value,
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsCard() {
    return StyledCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.settings, color: Theme.of(context).primaryColor),
                const SizedBox(width: 8),
                Text(
                  '日誌設定',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text('自動上傳日誌'),
              subtitle: const Text('定期自動上傳日誌到雲端'),
              value: _settings.autoUpload,
              onChanged: (value) {
                setState(() {
                  _settings.autoUpload = value;
                });
                _updateSettings();
              },
            ),
            SwitchListTile(
              title: const Text('自動上傳崩潰報告'),
              subtitle: const Text('發生崩潰時自動上傳報告'),
              value: _settings.autoUploadCrashes,
              onChanged: (value) {
                setState(() {
                  _settings.autoUploadCrashes = value;
                });
                _updateSettings();
              },
            ),
            SwitchListTile(
              title: const Text('啟用性能日誌'),
              subtitle: const Text('記錄應用程式性能數據'),
              value: _settings.enablePerformanceLogs,
              onChanged: (value) {
                setState(() {
                  _settings.enablePerformanceLogs = value;
                });
                _updateSettings();
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionsCard() {
    return StyledCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.build, color: Theme.of(context).primaryColor),
                const SizedBox(width: 8),
                Text(
                  '操作',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const LogViewerPage(),
                    ),
                  );
                },
                icon: const Icon(Icons.visibility),
                label: const Text('查看日誌'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
            const SizedBox(height: 8),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isUploading ? null : _uploadLogs,
                icon: _isUploading
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Icon(Icons.cloud_upload),
                label: Text(_isUploading ? '上傳中...' : '立即上傳日誌'),
              ),
            ),
            const SizedBox(height: 8),
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: _clearLogs,
                icon: const Icon(Icons.delete_outline),
                label: const Text('清理本地日誌'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: Colors.red,
                ),
              ),
            ),
            const SizedBox(height: 16),
            Text(
              '說明：',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              '• 日誌會自動保存到本地文件\n'
              '• 啟用自動上傳後，日誌會定期上傳到雲端\n'
              '• 崩潰報告有助於改善應用程式穩定性\n'
              '• 性能日誌有助於優化應用程式效能\n'
              '• 所有日誌資料都會加密存儲',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
