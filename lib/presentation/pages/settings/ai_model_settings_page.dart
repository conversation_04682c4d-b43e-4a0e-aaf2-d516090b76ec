import 'package:flutter/material.dart';

import '../../../astreal.dart';
import '../../../data/services/api/ai_api_service.dart';
import 'api_key_settings_page.dart';

/// AI 模型設置頁面
class AIModelSettingsPage extends StatefulWidget {
  const AIModelSettingsPage({super.key});

  @override
  State<AIModelSettingsPage> createState() => _AIModelSettingsPageState();
}

class _AIModelSettingsPageState extends State<AIModelSettingsPage> {
  String? _selectedModelId;

  @override
  void initState() {
    super.initState();
    _loadSelectedModel();
  }

  Future<void> _loadSelectedModel() async {
    final modelId = await AIApiService.getSelectedModel();
    setState(() {
      _selectedModelId = modelId;
    });
  }

  Future<List<AIModelConfig>> _loadModelsWithStatus() async {
    return AIApiService.availableModels;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('模型設置'),
        backgroundColor: AppColors.royalIndigo,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.key),
            onPressed: () => Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const ApiKeySettingsPage(),
              ),
            ),
            tooltip: 'API Key 設置',
          ),
        ],
      ),
      body: FutureBuilder<List<AIModelConfig>>(
        future: _loadModelsWithStatus(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }

          final models = snapshot.data ?? [];
          return ListView(
            padding: const EdgeInsets.all(16.0),
            children: [
              // 頁面說明
              _buildPageDescription(),
              const SizedBox(height: 16),

              // API Key 狀態
              _buildApiKeyStatus(),
              // const SizedBox(height: 24),

              // AI 模型選擇
              _buildModelSelection(models),
              // const SizedBox(height: 24),

              // 模型性能比較
              // _buildModelComparison(),
              // const SizedBox(height: 24),

              // 使用建議
              _buildUsageRecommendations(),
            ],
          );
        },
      ),
    );
  }

  /// 構建頁面說明
  Widget _buildPageDescription() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.royalIndigo.withValues(alpha: 0.1),
            AppColors.solarAmber.withValues(alpha: 0.1),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.royalIndigo.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(
                Icons.psychology,
                color: AppColors.royalIndigo,
                size: 24,
              ),
              SizedBox(width: 8),
              Text(
                '模型選擇',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppColors.royalIndigo,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            '選擇用於星盤解讀的模型。不同模型在分析深度、回應速度和準確性方面各有特色，請根據您的需求選擇最適合的模型。',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade700,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  /// 構建 API Key 狀態
  Widget _buildApiKeyStatus() {
    return FutureBuilder<Map<AIProvider, bool>>(
      future: _checkApiKeyStatus(),
      builder: (context, snapshot) {
        final status = snapshot.data ?? {};

        return StyledCard(
          elevation: 2,
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Icon(
                      Icons.key,
                      color: AppColors.solarAmber,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    const Text(
                      'API Key 狀態',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textDark,
                      ),
                    ),
                    const Spacer(),
                    TextButton.icon(
                      onPressed: () => Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const ApiKeySettingsPage(),
                        ),
                      ),
                      icon: const Icon(Icons.settings, size: 16),
                      label: const Text('設置'),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                ...AIProvider.values.map((provider) {
                  final isConfigured = status[provider] ?? false;
                  return Container(
                    margin: const EdgeInsets.only(bottom: 8),
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: isConfigured
                          ? Colors.green.withValues(alpha: 0.1)
                          : Colors.red.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: isConfigured
                            ? Colors.green.withValues(alpha: 0.3)
                            : Colors.red.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          isConfigured ? Icons.check_circle : Icons.error,
                          color: isConfigured ? Colors.green : Colors.red,
                          size: 20,
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            provider.displayName,
                            style: const TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                        Text(
                          isConfigured ? '已設置' : '未設置',
                          style: TextStyle(
                            fontSize: 12,
                            color: isConfigured ? Colors.green : Colors.red,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  );
                }).toList(),
              ],
            ),
          ),
        );
      },
    );
  }

  Future<Map<AIProvider, bool>> _checkApiKeyStatus() async {
    final result = <AIProvider, bool>{};
    for (final provider in AIProvider.values) {
      result[provider] = await AIApiService.isApiKeyConfigured(provider);
    }
    return result;
  }

  /// 構建模型選擇區域
  Widget _buildModelSelection(List<AIModelConfig> models) {
    // 按提供商分組模型
    final groupedModels = <AIProvider, List<AIModelConfig>>{};
    for (final model in models) {
      groupedModels.putIfAbsent(model.provider, () => []).add(model);
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '可用模型',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: AppColors.textDark,
          ),
        ),
        const SizedBox(height: 16),

        // 按提供商分組顯示
        ...groupedModels.entries.map((entry) {
          final provider = entry.key;
          final providerModels = entry.value;

          return _buildProviderSection(provider, providerModels);
        }),
      ],
    );
  }

  /// 構建提供商區域
  Widget _buildProviderSection(AIProvider provider, List<AIModelConfig> models) {
    return FutureBuilder<bool>(
      future: AIApiService.isApiKeyConfigured(provider),
      builder: (context, snapshot) {
        final isApiKeyConfigured = snapshot.data ?? false;

        return Container(
          margin: const EdgeInsets.only(bottom: 0),
          child: StyledCard(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 提供商標題
                  Row(
                    children: [
                      Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: _getProviderColor(provider).withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(
                          _getProviderIcon(provider),
                          color: _getProviderColor(provider),
                          size: 24,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              provider.displayName,
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: _getProviderColor(provider),
                              ),
                            ),
                            Text(
                              _getProviderDescription(provider),
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey.shade600,
                              ),
                            ),
                          ],
                        ),
                      ),
                      // API Key 狀態指示器
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: isApiKeyConfigured
                              ? Colors.green.withValues(alpha: 0.1)
                              : Colors.red.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              isApiKeyConfigured ? Icons.check_circle : Icons.error,
                              size: 12,
                              color: isApiKeyConfigured ? Colors.green : Colors.red,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              isApiKeyConfigured ? '已配置' : '未配置',
                              style: TextStyle(
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                                color: isApiKeyConfigured ? Colors.green : Colors.red,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),

                  if (!isApiKeyConfigured) ...[
                    const SizedBox(height: 0),
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.orange.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
                      ),
                      child: Row(
                        children: [
                          const Icon(Icons.warning, color: Colors.orange, size: 16),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              '需要設置 API Key 才能使用 ${provider.displayName} 的模型',
                              style: const TextStyle(fontSize: 12, color: Colors.orange),
                            ),
                          ),
                          TextButton(
                            onPressed: () => _showApiKeyRequiredDialog(provider),
                            style: TextButton.styleFrom(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                              minimumSize: Size.zero,
                            ),
                            child: const Text('設置', style: TextStyle(fontSize: 12)),
                          ),
                        ],
                      ),
                    ),
                  ],

                  const SizedBox(height: 16),

                  // 模型列表
                  ...models.map((model) => _buildModelCard(model, isApiKeyConfigured)),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// 構建模型卡片
  Widget _buildModelCard(AIModelConfig model, bool isApiKeyConfigured) {
    final isSelected = model.id == _selectedModelId;

    return Container(
      margin: const EdgeInsets.only(bottom: 0),
      child: InkWell(
        onTap: isApiKeyConfigured
            ? () => _selectModel(model.id)
            : () => _showApiKeyRequiredDialog(model.provider),
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: isSelected
                ? _getProviderColor(model.provider).withValues(alpha: 0.1)
                : Colors.grey.withValues(alpha: 0.05),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: isSelected
                  ? _getProviderColor(model.provider)
                  : Colors.grey.withValues(alpha: 0.2),
              width: isSelected ? 2 : 1,
            ),
          ),
          child: Row(
            children: [
              // 選擇指示器
              Icon(
                isSelected ? Icons.radio_button_checked : Icons.radio_button_unchecked,
                color: isApiKeyConfigured
                    ? (isSelected ? _getProviderColor(model.provider) : Colors.grey)
                    : Colors.grey.shade400,
                size: 20,
              ),
              const SizedBox(width: 12),

              // 模型信息
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          model.name,
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: isApiKeyConfigured ? AppColors.textDark : Colors.grey,
                          ),
                        ),
                        if (isSelected) ...[
                          const SizedBox(width: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: _getProviderColor(model.provider),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: const Text(
                              '當前',
                              style: TextStyle(
                                fontSize: 8,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                    const SizedBox(height: 4),
                    _buildModelFeatures(model, isApiKeyConfigured),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 獲取提供商圖標
  IconData _getProviderIcon(AIProvider provider) {
    switch (provider) {
      case AIProvider.openai:
        return Icons.psychology;
      case AIProvider.anthropic:
        return Icons.smart_toy;
      case AIProvider.groq:
        return Icons.flash_on;
      case AIProvider.gemini:
        return Icons.auto_awesome;
    }
  }

  /// 獲取提供商描述
  String _getProviderDescription(AIProvider provider) {
    switch (provider) {
      case AIProvider.openai:
        return '業界領先的 AI 模型，創意與邏輯並重';
      case AIProvider.anthropic:
        return '注重安全性和可解釋性的 AI 助手';
      case AIProvider.groq:
        return '超高速推理，開源模型，免費使用';
      case AIProvider.gemini:
        return 'Google 最新多模態 AI 技術';
    }
  }

  /// 構建模型特性標籤
  Widget _buildModelFeatures(AIModelConfig model, bool isEnabled) {
    // 根據模型提供商和 ID 定義特性
    List<String> features = [];
    Color featureColor = _getProviderColor(model.provider);

    switch (model.provider) {
      case AIProvider.openai:
        if (model.id.contains('gpt-4')) {
          features = ['高準確性', '深度分析', '創意解讀'];
        } else {
          features = ['快速回應', '平衡性能', '經濟實用'];
        }
        break;
      case AIProvider.anthropic:
        features = ['邏輯清晰', '結構化', '詳細說明'];
        break;
      case AIProvider.groq:
        if (model.id.contains('llama-3.3')) {
          features = ['最新模型', '極速回應', '大容量'];
        } else if (model.id.contains('gemma')) {
          features = ['輕量快速', '高效能', '免費配額'];
        } else if (model.id.contains('70b')) {
          features = ['高性能', '深度分析', '免費配額'];
        } else {
          features = ['極速回應', '開源模型', '免費配額'];
        }
        break;
      case AIProvider.gemini:
        if (model.id.contains('2.0')) {
          features = ['最新技術', '多模態', '高效能'];
        } else if (model.id.contains('1.5-pro')) {
          features = ['專業級', '長上下文', '高準確性'];
        } else if (model.id.contains('flash')) {
          features = ['快速回應', '輕量級', '高效率'];
        } else {
          features = ['多模態', 'Google 出品', '高品質'];
        }
        break;
    }

    if (!isEnabled) {
      featureColor = Colors.grey;
    }

    return Wrap(
      spacing: 4,
      runSpacing: 2,
      children: features.take(3).map((feature) => Container(
        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
        decoration: BoxDecoration(
          color: featureColor.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: featureColor.withValues(alpha: 0.3),
            width: 0.5,
          ),
        ),
        child: Text(
          feature,
          style: TextStyle(
            fontSize: 9,
            fontWeight: FontWeight.w500,
            color: featureColor,
          ),
        ),
      )).toList(),
    );
  }

  /// 構建模型性能比較
  Widget _buildModelComparison() {
    return StyledCard(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(
                  Icons.compare_arrows,
                  color: AppColors.indigoLight,
                  size: 20,
                ),
                SizedBox(width: 8),
                Text(
                  '模型性能比較',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textDark,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildComparisonTable(),
          ],
        ),
      ),
    );
  }

  /// 構建比較表格
  Widget _buildComparisonTable() {
    return Table(
      border: TableBorder.all(
        color: Colors.grey.shade300,
        width: 1,
      ),
      columnWidths: const {
        0: FlexColumnWidth(2),
        1: FlexColumnWidth(1),
        2: FlexColumnWidth(1),
        3: FlexColumnWidth(1),
        4: FlexColumnWidth(1),
        5: FlexColumnWidth(1),
      },
      children: [
        // 表頭
        TableRow(
          decoration: BoxDecoration(color: Colors.grey.shade100),
          children: const [
            Padding(
              padding: EdgeInsets.all(8),
              child: Text(
                '特性',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
            ),
            Padding(
              padding: EdgeInsets.all(8),
              child: Text(
                'GPT-4',
                style: TextStyle(fontWeight: FontWeight.bold),
                textAlign: TextAlign.center,
              ),
            ),
            Padding(
              padding: EdgeInsets.all(8),
              child: Text(
                'GPT-3.5',
                style: TextStyle(fontWeight: FontWeight.bold),
                textAlign: TextAlign.center,
              ),
            ),
            Padding(
              padding: EdgeInsets.all(8),
              child: Text(
                'Claude-3',
                style: TextStyle(fontWeight: FontWeight.bold),
                textAlign: TextAlign.center,
              ),
            ),
            Padding(
              padding: EdgeInsets.all(8),
              child: Text(
                'Groq',
                style: TextStyle(fontWeight: FontWeight.bold),
                textAlign: TextAlign.center,
              ),
            ),
            Padding(
              padding: EdgeInsets.all(8),
              child: Text(
                'Gemini',
                style: TextStyle(fontWeight: FontWeight.bold),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
        // 數據行
        _buildTableRow('分析深度', '★★★★★', '★★★★☆', '★★★★★', '★★★★☆', '★★★★☆'),
        _buildTableRow('回應速度', '★★★☆☆', '★★★★★', '★★★★☆', '★★★★★', '★★★★☆'),
        _buildTableRow('準確性', '★★★★★', '★★★★☆', '★★★★★', '★★★★☆', '★★★★☆'),
        _buildTableRow('創意性', '★★★★★', '★★★☆☆', '★★★★☆', '★★★★☆', '★★★★☆'),
        _buildTableRow('成本效益', '★★☆☆☆', '★★★★☆', '★★★☆☆', '★★★★★', '★★★★☆'),
        _buildTableRow('多模態', '☆☆☆☆☆', '☆☆☆☆☆', '☆☆☆☆☆', '☆☆☆☆☆', '★★★★★'),
      ],
    );
  }

  /// 構建表格行
  TableRow _buildTableRow(String feature, String gpt4, String gpt35, String claude, String groq, String gemini) {
    return TableRow(
      children: [
        Padding(
          padding: const EdgeInsets.all(8),
          child: Text(feature),
        ),
        Padding(
          padding: const EdgeInsets.all(8),
          child: Text(gpt4, textAlign: TextAlign.center),
        ),
        Padding(
          padding: const EdgeInsets.all(8),
          child: Text(gpt35, textAlign: TextAlign.center),
        ),
        Padding(
          padding: const EdgeInsets.all(8),
          child: Text(claude, textAlign: TextAlign.center),
        ),
        Padding(
          padding: const EdgeInsets.all(8),
          child: Text(groq, textAlign: TextAlign.center),
        ),
        Padding(
          padding: const EdgeInsets.all(8),
          child: Text(gemini, textAlign: TextAlign.center),
        ),
      ],
    );
  }

  /// 構建使用建議
  Widget _buildUsageRecommendations() {
    return StyledCard(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(
                  Icons.lightbulb_outline,
                  color: AppColors.solarAmber,
                  size: 20,
                ),
                SizedBox(width: 8),
                Text(
                  '使用建議',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textDark,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            _buildRecommendationItem(
              '初學者',
              'GPT-3.5 Turbo',
              '快速回應，易於理解，適合日常使用',
              Icons.school,
              Colors.blue,
            ),
            _buildRecommendationItem(
              '進階用戶',
              'GPT-4',
              '深度分析，創意解讀，適合專業研究',
              Icons.psychology,
              Colors.green,
            ),
            _buildRecommendationItem(
              '邏輯導向',
              'Claude-3',
              '結構清晰，邏輯嚴謹，適合系統性學習',
              Icons.account_tree,
              Colors.purple,
            ),
            _buildRecommendationItem(
              '速度優先',
              'Groq (Llama 3.3 70B)',
              '極速回應，免費使用，最新模型，適合快速解讀',
              Icons.flash_on,
              Colors.purple,
            ),
            _buildRecommendationItem(
              '輕量快速',
              'Groq (Gemma 2 9B)',
              '輕量模型，超快速度，適合簡單解讀',
              Icons.speed,
              Colors.green,
            ),
            _buildRecommendationItem(
              '多模態分析',
              'Gemini 2.0 Flash',
              '支援圖像、文字等多模態，Google 最新技術',
              Icons.auto_awesome,
              Colors.blue,
            ),
            _buildRecommendationItem(
              '平衡選擇',
              'Gemini 1.5 Pro',
              '平衡性能與成本，適合專業解讀',
              Icons.balance,
              Colors.indigo,
            ),
          ],
        ),
      ),
    );
  }

  /// 構建建議項目
  Widget _buildRecommendationItem(
    String userType,
    String model,
    String description,
    IconData icon,
    Color color,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: color.withValues(alpha: 0.2),
        ),
      ),
      child: Row(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '$userType → $model',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 選擇模型
  Future<void> _selectModel(String modelId) async {
    await AIApiService.setSelectedModel(modelId);
    setState(() {
      _selectedModelId = modelId;
    });

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('已選擇模型: ${AIApiService.availableModels.firstWhere((m) => m.id == modelId).name}'),
          backgroundColor: AppColors.royalIndigo,
        ),
      );
    }
  }

  /// 顯示需要 API Key 的對話框
  void _showApiKeyRequiredDialog(AIProvider provider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('需要 ${provider.displayName} API Key'),
        content: Text('要使用 ${provider.displayName} 的模型，請先設置 API Key。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const ApiKeySettingsPage(),
                ),
              );
            },
            child: const Text('設置 API Key'),
          ),
        ],
      ),
    );
  }

  /// 獲取提供商顏色
  Color _getProviderColor(AIProvider provider) {
    switch (provider) {
      case AIProvider.openai:
        return Colors.green;
      case AIProvider.anthropic:
        return Colors.orange;
      case AIProvider.groq:
        return Colors.purple;
      case AIProvider.gemini:
        return Colors.blue;
    }
  }
}
