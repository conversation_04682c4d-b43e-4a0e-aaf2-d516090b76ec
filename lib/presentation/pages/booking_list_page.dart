import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../data/models/astrology/chart_data.dart';
import '../../../data/models/astrology/chart_type.dart';
import '../../../data/models/user/birth_data.dart';
import '../../data/models/payment/booking_model.dart';
import '../../data/services/api/firebase_service.dart';
import '../../presentation/viewmodels/chart_viewmodel.dart';
import '../themes/app_theme.dart';
import 'booking_page.dart';
import 'chart_page.dart';

class BookingListPage extends StatefulWidget {
  const BookingListPage({super.key});

  @override
  State<BookingListPage> createState() => _BookingListPageState();
}

class _BookingListPageState extends State<BookingListPage> {
  List<BookingModel> _bookings = [];
  List<BookingModel> _filteredBookings = [];
  bool _isLoading = true;

  // 篩選選項
  bool? _filterConfirmed;
  String? _filterConsultationType;

  // 排序選項
  String _sortBy = 'date'; // 'date', 'status', 'type'
  bool _sortAscending = true;

  // 選擇模式
  bool _isSelectionMode = false;
  Set<String> _selectedBookingIds = {};

  @override
  void initState() {
    super.initState();
    _loadBookings();
  }

  // 載入預約列表
  Future<void> _loadBookings() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // 從 Firebase 直接獲取預約資料
      final bookings = await FirebaseService.getAllBookings();

      setState(() {
        _bookings = bookings;
        _applyFiltersAndSort();
        _isLoading = false;
      });

      // 如果沒有預約資料，顯示提示
      if (bookings.isEmpty && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('目前沒有預約資料，可以點擊右下角按鈕新增')),
        );
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('載入預約列表時出錯: $e')),
        );
      }
    }
  }

  // 應用篩選和排序
  void _applyFiltersAndSort() {
    // 先應用篩選
    _filteredBookings = _bookings.where((booking) {
      // 篩選確認狀態
      if (_filterConfirmed != null && booking.isConfirmed != _filterConfirmed) {
        return false;
      }

      // 篩選諮詢類型
      if (_filterConsultationType != null &&
          booking.consultationType != _filterConsultationType) {
        return false;
      }

      return true;
    }).toList();

    // 再應用排序
    switch (_sortBy) {
      case 'date':
        _filteredBookings.sort((a, b) {
          final dateTimeA = DateTime(
            a.bookingDate.year,
            a.bookingDate.month,
            a.bookingDate.day,
            a.bookingTime.hour,
            a.bookingTime.minute,
          );
          final dateTimeB = DateTime(
            b.bookingDate.year,
            b.bookingDate.month,
            b.bookingDate.day,
            b.bookingTime.hour,
            b.bookingTime.minute,
          );
          return _sortAscending
              ? dateTimeA.compareTo(dateTimeB)
              : dateTimeB.compareTo(dateTimeA);
        });
        break;
      case 'status':
        _filteredBookings.sort((a, b) {
          final result =
              a.isConfirmed.toString().compareTo(b.isConfirmed.toString());
          return _sortAscending ? result : -result;
        });
        break;
      case 'type':
        _filteredBookings.sort((a, b) {
          final result = a.consultationType.compareTo(b.consultationType);
          return _sortAscending ? result : -result;
        });
        break;
    }
  }

  // 刪除預約
  Future<void> _deleteBooking(String id) async {
    try {
      // 直接使用 FirebaseService 刪除預約
      final success = await FirebaseService.deleteBooking(id);

      if (success) {
        await _loadBookings();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('預約已成功取消')),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('取消預約失敗，請稍後再試')),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('取消預約時出錯: $e')),
        );
      }
    }
  }

  // 批量刪除預約
  Future<void> _deleteSelectedBookings() async {
    if (_selectedBookingIds.isEmpty) return;

    try {
      // 顯示確認對話框
      final confirmed = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('確認刪除'),
          content: Text('確定要刪除選中的 ${_selectedBookingIds.length} 個預約嗎？此操作無法撤銷。'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () => Navigator.pop(context, true),
              style: TextButton.styleFrom(foregroundColor: Colors.red),
              child: const Text('確認刪除'),
            ),
          ],
        ),
      );

      if (confirmed != true) return;

      // 顯示進度對話框
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('正在刪除預約...'),
            ],
          ),
        ),
      );

      // 刪除選中的預約
      int successCount = 0;
      int failCount = 0;

      for (final id in _selectedBookingIds) {
        try {
          final success = await FirebaseService.deleteBooking(id);
          if (success) {
            successCount++;
          } else {
            failCount++;
          }
        } catch (e) {
          failCount++;
        }
      }

      // 關閉進度對話框
      if (mounted) Navigator.pop(context);

      // 重新載入預約列表
      await _loadBookings();

      // 退出選擇模式
      setState(() {
        _isSelectionMode = false;
        _selectedBookingIds.clear();
      });

      // 顯示結果
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('刪除完成: $successCount 個成功, $failCount 個失敗')),
        );
      }
    } catch (e) {
      // 關閉進度對話框
      if (mounted) Navigator.pop(context);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('批量刪除預約時出錯: $e')),
        );
      }
    }
  }

  // 確認預約
  Future<void> _confirmBooking(BookingModel booking) async {
    try {
      // 使用 FirebaseService 更新預約狀態
      final success =
          await FirebaseService.updateBookingStatus(booking.id, true);

      if (success) {
        // 重新載入預約列表
        await _loadBookings();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('預約已成功確認')),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('確認預約失敗，請稍後再試')),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('確認預約時出錯: $e')),
        );
      }
    }
  }

  // 取消確認預約
  Future<void> _unconfirmBooking(
      BookingModel booking, bool isConfirming) async {
    try {
      // 使用 FirebaseService 更新預約狀態
      final success =
          await FirebaseService.updateBookingStatus(booking.id, isConfirming);

      if (success) {
        // 重新載入預約列表
        await _loadBookings();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('預約已取消確認')),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('取消確認預約失敗，請稍後再試')),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('取消確認預約時出錯: $e')),
        );
      }
    }
  }

  String formatDateTime(DateTime date, DateTime time) {
    return '${date.year}/${date.month}/${date.day} '
        '${time.hour}:${time.minute.toString().padLeft(2, '0')}';
  }

  // 格式化日期和時間
  String _formatDateTime(DateTime date, TimeOfDay time) {
    return '${date.year}/${date.month}/${date.day} '
        '${time.hour}:${time.minute.toString().padLeft(2, '0')}';
  }

  // 顯示預約詳情
  void _showBookingDetails(BookingModel booking) {
    // 判斷預約是否已過期
    final now = DateTime.now();
    final bookingDateTime = DateTime(
      booking.bookingDate.year,
      booking.bookingDate.month,
      booking.bookingDate.day,
      booking.bookingTime.hour,
      booking.bookingTime.minute,
    );
    final bool isPast = bookingDateTime.isBefore(now);

    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Container(
          width: double.maxFinite,
          constraints: BoxConstraints(
            maxWidth: 500,
            maxHeight: MediaQuery.of(context).size.height * 0.8,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // 預約詳情標題列
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: AppColors.royalIndigo,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(16),
                    topRight: Radius.circular(16),
                  ),
                ),
                child: Row(
                  children: [
                    const Icon(
                      Icons.event_note,
                      color: Colors.white,
                      size: 28,
                    ),
                    const SizedBox(width: 12),
                    const Expanded(
                      child: Text(
                        '預約詳情',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    // 狀態標籤
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: booking.isConfirmed
                            ? Colors.green.shade100
                            : Colors.orange.shade100,
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            booking.isConfirmed
                                ? Icons.check_circle
                                : Icons.pending,
                            size: 16,
                            color: booking.isConfirmed
                                ? Colors.green.shade800
                                : Colors.orange.shade800,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            booking.isConfirmed ? '已確認' : '待確認',
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                              color: booking.isConfirmed
                                  ? Colors.green.shade800
                                  : Colors.orange.shade800,
                            ),
                          ),
                        ],
                      ),
                    ),
                    // 關閉按鈕
                    IconButton(
                      icon: const Icon(Icons.close, color: Colors.white),
                      onPressed: () => Navigator.pop(context),
                      tooltip: '關閉',
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                    ),
                  ],
                ),
              ),

              // 預約內容
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 預約基本信息卡片
                      _buildInfoCard(
                        title: '預約信息',
                        icon: Icons.event,
                        color: AppColors.royalIndigo,
                        children: [
                          // 諮詢類型
                          _buildDetailItemWithIcon(
                            label: '諮詢類型',
                            value: booking.consultationType,
                            icon: Icons.category,
                          ),
                          // 預約時間
                          // _buildDetailItemWithIcon(
                          //   label: '預約時間',
                          //   value: _formatDateTime(
                          //     booking.bookingDate,
                          //     booking.bookingTime,
                          //   ),
                          //   icon: Icons.access_time,
                          //   isPast: isPast,
                          // ),
                          // 建立時間
                          _buildDetailItemWithIcon(
                            label: '建立時間',
                            value: _formatDateTimeDetail(booking.createdAt),
                            icon: Icons.history,
                          ),
                          // 預約 ID
                          _buildDetailItemWithIcon(
                            label: '預約 ID',
                            value: booking.id,
                            icon: Icons.tag,
                            isMonospace: true,
                          ),
                        ],
                      ),

                      const SizedBox(height: 16),

                      // 個人資料卡片
                      _buildInfoCard(
                        title: '聯絡資訊',
                        icon: Icons.person,
                        color: Colors.blue,
                        children: [
                          // 姓名
                          // _buildDetailItemWithIcon(
                          //   label: '姓名',
                          //   value: booking.name,
                          //   icon: Icons.person_outline,
                          // ),
                          // 電子郵件
                          _buildDetailItemWithIcon(
                            label: '電子郵件',
                            value: booking.email,
                            icon: Icons.email_outlined,
                            isEmail: true,
                          ),
                          _buildDetailItemWithIcon(
                            label: '聯絡方式',
                            value: booking.contactMethod,
                            icon: Icons.contact_page,
                            isEmail: true,
                          ),
                          // 備註
                          if (booking.notes != null &&
                              booking.notes!.isNotEmpty)
                            _buildDetailItemWithIcon(
                              label: '備註',
                              value: booking.notes!,
                              icon: Icons.note_outlined,
                              isMultiLine: true,
                            ),
                        ],
                      ),

                      // 星盤資料卡片
                      if (booking.birthData != null) ...[
                        const SizedBox(height: 16),
                        _buildInfoCard(
                          title: '星盤資料',
                          icon: Icons.star,
                          color: Colors.amber,
                          children: [
                            // 姓名
                            _buildDetailItemWithIcon(
                              label: '姓名',
                              value: booking.birthData!['name'] ?? '無資料',
                              icon: Icons.person_outline,
                            ),
                            // 出生日期
                            _buildDetailItemWithIcon(
                              label: '出生日期',
                              value: booking.birthData!['birthDate'] ?? '無資料',
                              icon: Icons.calendar_today,
                            ),
                            // 出生地點
                            _buildDetailItemWithIcon(
                              label: '出生地點',
                              value: booking.birthData!['birthPlace'] ?? '無資料',
                              icon: Icons.location_on_outlined,
                            ),
                            // 性別
                            if (booking.birthData!['gender'] != null)
                              _buildDetailItemWithIcon(
                                label: '性別',
                                value: booking.birthData!['gender'] == 'male'
                                    ? '男'
                                    : '女',
                                icon: booking.birthData!['gender'] == 'male'
                                    ? Icons.male
                                    : Icons.female,
                              ),
                          ],
                        ),
                      ],

                      // Firebase 資料卡片
                      const SizedBox(height: 16),
                      _buildInfoCard(
                        title: 'Firestore 資料',
                        icon: Icons.storage,
                        color: Colors.deepPurple,
                        children: [
                          _buildDetailItemWithIcon(
                            label: '集合路徑',
                            value: 'bookings/${booking.id}',
                            icon: Icons.folder_outlined,
                            isMonospace: true,
                          ),
                          _buildDetailItemWithIcon(
                            label: '儲存方式',
                            value: 'set(booking.toJson())',
                            icon: Icons.code,
                            isMonospace: true,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),

              // 按鈕列
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: const BorderRadius.only(
                    bottomLeft: Radius.circular(16),
                    bottomRight: Radius.circular(16),
                  ),
                ),
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    // 如果屏幕寬度足夠，使用水平排列
                    if (constraints.maxWidth > 400) {
                      return Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          // 左側按鈕
                          Row(
                            children: [
                              // 確認狀態切換
                              const Text('確認狀態:'),
                              const SizedBox(width: 8),
                              Switch(
                                value: booking.isConfirmed,
                                onChanged: (value) {
                                  Navigator.pop(context);
                                  if (value) {
                                    _confirmBooking(booking);
                                  } else {
                                    _unconfirmBooking(booking, false);
                                  }
                                },
                                activeColor: AppColors.royalIndigo,
                              ),
                            ],
                          ),

                          // 右側按鈕
                          Row(
                            children: [
                              // 查看本命盤按鈕
                              if (booking.birthData != null) ...[
                                ElevatedButton.icon(
                                  onPressed: () {
                                    Navigator.pop(context);
                                    _navigateToNatalChart(booking);
                                  },
                                  icon: const Icon(Icons.star),
                                  label: const Text('查看本命盤'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: AppColors.royalIndigo,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                                const SizedBox(width: 8),
                              ],
                              // 取消預約按鈕
                              OutlinedButton.icon(
                                onPressed: () {
                                  Navigator.pop(context);
                                  _showDeleteConfirmation(booking);
                                },
                                icon: const Icon(Icons.delete),
                                label: const Text('取消預約'),
                                style: OutlinedButton.styleFrom(
                                  foregroundColor: Colors.red,
                                ),
                              ),
                            ],
                          ),
                        ],
                      );
                    } else {
                      // 屏幕較窄時，使用垂直排列
                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          // 確認狀態切換
                          Row(
                            children: [
                              const Text('確認狀態:'),
                              const SizedBox(width: 8),
                              Switch(
                                value: booking.isConfirmed,
                                onChanged: (value) {
                                  Navigator.pop(context);
                                  if (value) {
                                    _confirmBooking(booking);
                                  } else {
                                    _unconfirmBooking(booking, false);
                                  }
                                },
                                activeColor: AppColors.royalIndigo,
                              ),
                            ],
                          ),

                          const SizedBox(height: 16),

                          // 按鈕列
                          Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              // 查看本命盤按鈕
                              if (booking.birthData != null) ...[
                                Expanded(
                                  child: ElevatedButton.icon(
                                    onPressed: () {
                                      Navigator.pop(context);
                                      _navigateToNatalChart(booking);
                                    },
                                    icon: const Icon(Icons.star, size: 16),
                                    label: const Text('查看本命盤'),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: AppColors.royalIndigo,
                                      foregroundColor: Colors.white,
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 8),
                              ],
                              // 取消預約按鈕
                              Expanded(
                                child: OutlinedButton.icon(
                                  onPressed: () {
                                    Navigator.pop(context);
                                    _showDeleteConfirmation(booking);
                                  },
                                  icon: const Icon(Icons.delete, size: 16),
                                  label: const Text('取消預約'),
                                  style: OutlinedButton.styleFrom(
                                    foregroundColor: Colors.red,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      );
                    }
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 格式化日期時間詳細資訊
  String _formatDateTimeDetail(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
        '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}:${dateTime.second.toString().padLeft(2, '0')}';
  }

  // 建立信息卡片
  Widget _buildInfoCard({
    required String title,
    required IconData icon,
    required Color color,
    required List<Widget> children,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 卡片標題
            Row(
              children: [
                Icon(icon, color: color),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ],
            ),
            const Divider(height: 24),
            // 卡片內容
            ...children,
          ],
        ),
      ),
    );
  }

  // 建立帶圖標的詳情項目
  Widget _buildDetailItemWithIcon({
    required String label,
    required String value,
    required IconData icon,
    bool isMultiLine = false,
    bool isMonospace = false,
    bool isEmail = false,
    bool isPast = false,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        crossAxisAlignment:
            isMultiLine ? CrossAxisAlignment.start : CrossAxisAlignment.center,
        children: [
          Icon(icon, size: 20, color: Colors.grey),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.grey,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                if (isEmail)
                  InkWell(
                    onTap: () {
                      // 打開郵件應用
                      // launchUrl(Uri.parse('mailto:$value'));
                    },
                    child: Text(
                      value,
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.blue,
                        decoration: TextDecoration.underline,
                        fontFamily: isMonospace ? 'monospace' : null,
                      ),
                    ),
                  )
                else
                  Text(
                    value,
                    style: TextStyle(
                      fontSize: 16,
                      color: isPast ? Colors.grey : Colors.black87,
                      fontFamily: isMonospace ? 'monospace' : null,
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 從預約資料創建 BirthData 對象
  BirthData? _createBirthDataFromBooking(BookingModel booking) {
    try {
      // 檢查預約是否包含星盤資料
      if (booking.birthData == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('此預約沒有星盤資料')),
        );
        return null;
      }

      // 從預約的 birthData 創建 BirthData 對象
      final birthDataMap = booking.birthData!;

      // 檢查必要的欄位
      if (!birthDataMap.containsKey('name') ||
          !birthDataMap.containsKey('birthDate') ||
          !birthDataMap.containsKey('birthPlace') ||
          !birthDataMap.containsKey('latitude') ||
          !birthDataMap.containsKey('longitude')) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('星盤資料不完整')),
        );
        return null;
      }

      // 創建 BirthData 對象
      return BirthData(
        id: birthDataMap['id'] as String? ?? 'booking_${booking.id}',
        name: birthDataMap['name'] as String,
        dateTime: DateTime.parse(birthDataMap['birthDate'] as String),
        birthPlace: birthDataMap['birthPlace'] as String,
        notes: birthDataMap['notes'] as String?,
        latitude: (birthDataMap['latitude'] as num).toDouble(),
        longitude: (birthDataMap['longitude'] as num).toDouble(),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('處理星盤資料時出錯: $e')),
      );
      return null;
    }
  }

  // 導航到本命盤頁面
  void _navigateToNatalChart(BookingModel booking) {
    // 從預約創建 BirthData
    final birthData = _createBirthDataFromBooking(booking);
    if (birthData == null) return;

    // 創建 ChartData 對象
    final chartData = ChartData(
      chartType: ChartType.natal,
      primaryPerson: birthData,
    );

    // 導航到星盤頁面
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ChangeNotifierProvider(
          create: (_) =>
              ChartViewModel.withChartData(initialChartData: chartData),
          child: ChartPage(chartData: chartData),
        ),
      ),
    );
  }

  // 顯示刪除確認對話框
  void _showDeleteConfirmation(BookingModel booking) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('確認取消預約'),
        content: const Text('您確定要取消此預約嗎？此操作無法撤銷。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('返回'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteBooking(booking.id);
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('確認取消'),
          ),
        ],
      ),
    );
  }

  // 構建詳情項目
  Widget _buildDetailItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.grey,
              fontSize: 12,
            ),
          ),
          Text(
            value,
            style: const TextStyle(fontSize: 16),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: _isSelectionMode
            ? Text('已選擇 ${_selectedBookingIds.length} 個預約')
            : const Text('Firestore 預約管理'),
        leading: _isSelectionMode
            ? IconButton(
                icon: const Icon(Icons.close),
                onPressed: () {
                  setState(() {
                    _isSelectionMode = false;
                    _selectedBookingIds.clear();
                  });
                },
                tooltip: '取消選擇',
              )
            : null,
        actions: _isSelectionMode
            ? [
                // 全選按鈕
                IconButton(
                  icon: const Icon(Icons.select_all),
                  onPressed: () {
                    setState(() {
                      if (_selectedBookingIds.length ==
                          _filteredBookings.length) {
                        // 如果已經全選，則取消全選
                        _selectedBookingIds.clear();
                      } else {
                        // 否則全選
                        _selectedBookingIds =
                            _filteredBookings.map((b) => b.id).toSet();
                      }
                    });
                  },
                  tooltip:
                      _selectedBookingIds.length == _filteredBookings.length
                          ? '取消全選'
                          : '全選',
                ),
                // 刪除按鈕
                IconButton(
                  icon: const Icon(Icons.delete),
                  onPressed: _selectedBookingIds.isNotEmpty
                      ? _deleteSelectedBookings
                      : null,
                  tooltip: '刪除選中預約',
                ),
              ]
            : [
                // 選擇模式按鈕
                IconButton(
                  icon: const Icon(Icons.select_all),
                  onPressed: () {
                    setState(() {
                      _isSelectionMode = true;
                    });
                  },
                  tooltip: '選擇模式',
                ),
                // 篩選按鈕
                IconButton(
                  icon: const Icon(Icons.filter_list),
                  onPressed: _showFilterDialog,
                  tooltip: '篩選預約',
                ),
                // 排序按鈕
                IconButton(
                  icon: const Icon(Icons.sort),
                  onPressed: _showSortDialog,
                  tooltip: '排序預約',
                ),
                // 重新整理按鈕
                IconButton(
                  icon: const Icon(Icons.refresh),
                  onPressed: _loadBookings,
                  tooltip: '重新整理',
                ),
              ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _bookings.isEmpty
              ? _buildEmptyState()
              : _buildBookingList(),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const BookingPage(),
            ),
          ).then((_) => _loadBookings()); // 返回時重新載入預約列表
        },
        backgroundColor: AppColors.royalIndigo,
        child: const Icon(Icons.add),
      ),
    );
  }

  // 構建空狀態
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.event_busy,
            size: 80,
            color: Colors.grey,
          ),
          const SizedBox(height: 16),
          const Text(
            '您目前沒有任何預約',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            '點擊右下角的加號按鈕新增預約',
            style: TextStyle(
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  // 顯示篩選對話框
  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('篩選預約'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 確認狀態篩選
              const Text('確認狀態'),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    child: ChoiceChip(
                      label: const Text('全部'),
                      selected: _filterConfirmed == null,
                      onSelected: (selected) {
                        if (selected) {
                          setState(() => _filterConfirmed = null);
                        }
                      },
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: ChoiceChip(
                      label: const Text('已確認'),
                      selected: _filterConfirmed == true,
                      onSelected: (selected) {
                        if (selected) {
                          setState(() => _filterConfirmed = true);
                        }
                      },
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: ChoiceChip(
                      label: const Text('未確認'),
                      selected: _filterConfirmed == false,
                      onSelected: (selected) {
                        if (selected) {
                          setState(() => _filterConfirmed = false);
                        }
                      },
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // 諮詢類型篩選
              const Text('諮詢類型'),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: [
                  ChoiceChip(
                    label: const Text('全部'),
                    selected: _filterConsultationType == null,
                    onSelected: (selected) {
                      if (selected) {
                        setState(() => _filterConsultationType = null);
                      }
                    },
                  ),
                  ...ConsultationType.all.map((type) => ChoiceChip(
                        label: Text(type),
                        selected: _filterConsultationType == type,
                        onSelected: (selected) {
                          if (selected) {
                            setState(() => _filterConsultationType = type);
                          }
                        },
                      )),
                ],
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                this.setState(() {
                  _applyFiltersAndSort();
                });
              },
              style:
                  TextButton.styleFrom(foregroundColor: AppColors.royalIndigo),
              child: const Text('應用篩選'),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                this.setState(() {
                  _filterConfirmed = null;
                  _filterConsultationType = null;
                  _applyFiltersAndSort();
                });
              },
              style: TextButton.styleFrom(foregroundColor: Colors.red),
              child: const Text('清除篩選'),
            ),
          ],
        ),
      ),
    );
  }

  // 顯示排序對話框
  void _showSortDialog() {
    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('排序預約'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 排序方式
              const Text('排序方式'),
              const SizedBox(height: 8),
              RadioListTile<String>(
                title: const Text('按日期'),
                value: 'date',
                groupValue: _sortBy,
                onChanged: (value) {
                  setState(() => _sortBy = value!);
                },
              ),
              RadioListTile<String>(
                title: const Text('按確認狀態'),
                value: 'status',
                groupValue: _sortBy,
                onChanged: (value) {
                  setState(() => _sortBy = value!);
                },
              ),
              RadioListTile<String>(
                title: const Text('按諮詢類型'),
                value: 'type',
                groupValue: _sortBy,
                onChanged: (value) {
                  setState(() => _sortBy = value!);
                },
              ),

              // 排序方向
              const Divider(),
              const Text('排序方向'),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    child: RadioListTile<bool>(
                      title: const Text('升序'),
                      value: true,
                      groupValue: _sortAscending,
                      onChanged: (value) {
                        setState(() => _sortAscending = value!);
                      },
                    ),
                  ),
                  Expanded(
                    child: RadioListTile<bool>(
                      title: const Text('降序'),
                      value: false,
                      groupValue: _sortAscending,
                      onChanged: (value) {
                        setState(() => _sortAscending = value!);
                      },
                    ),
                  ),
                ],
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                this.setState(() {
                  _applyFiltersAndSort();
                });
              },
              style:
                  TextButton.styleFrom(foregroundColor: AppColors.royalIndigo),
              child: const Text('應用排序'),
            ),
          ],
        ),
      ),
    );
  }

  // 構建預約列表
  Widget _buildBookingList() {
    // 將預約分為未來和過去兩組
    final now = DateTime.now();
    final upcomingBookings = _filteredBookings.where((booking) {
      final bookingDateTime = DateTime(
        booking.bookingDate.year,
        booking.bookingDate.month,
        booking.bookingDate.day,
        booking.bookingTime.hour,
        booking.bookingTime.minute,
      );
      return bookingDateTime.isAfter(now);
    }).toList();

    final pastBookings = _filteredBookings.where((booking) {
      final bookingDateTime = DateTime(
        booking.bookingDate.year,
        booking.bookingDate.month,
        booking.bookingDate.day,
        booking.bookingTime.hour,
        booking.bookingTime.minute,
      );
      return bookingDateTime.isBefore(now);
    }).toList();

    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        if (upcomingBookings.isNotEmpty) ...[
          const Text(
            '即將到來的預約',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          ...upcomingBookings.map((booking) => _buildBookingCard(booking)),
          const SizedBox(height: 24),
        ],
        if (pastBookings.isNotEmpty) ...[
          const Text(
            '過去的預約',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 8),
          ...pastBookings
              .map((booking) => _buildBookingCard(booking, isPast: true)),
        ],
      ],
    );
  }

  // 構建預約卡片
  Widget _buildBookingCard(BookingModel booking, {bool isPast = false}) {
    // 取得星盤資料的姓名（如果有）
    final String birthDataName =
        booking.birthData != null && booking.birthData!['name'] != null
            ? booking.birthData!['name'] as String
            : '';

    final bool isSelected = _selectedBookingIds.contains(booking.id);

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: isPast ? 1 : (isSelected ? 4 : 3),
      color: isSelected
          ? AppColors.royalIndigo.withValues(alpha: 0.1)
          : (isPast ? Colors.grey.shade100 : Colors.white),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: isSelected
            ? const BorderSide(color: AppColors.royalIndigo, width: 2)
            : BorderSide.none,
      ),
      child: InkWell(
        onTap: _isSelectionMode
            ? () {
                setState(() {
                  if (isSelected) {
                    _selectedBookingIds.remove(booking.id);
                  } else {
                    _selectedBookingIds.add(booking.id);
                  }
                });
              }
            : () => _showBookingDetails(booking),
        onLongPress: !_isSelectionMode
            ? () {
                setState(() {
                  _isSelectionMode = true;
                  _selectedBookingIds.add(booking.id);
                });
              }
            : null,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 預約 ID 和狀態
              Row(
                children: [
                  // 選擇指示器
                  if (_isSelectionMode)
                    Padding(
                      padding: const EdgeInsets.only(right: 8),
                      child: Icon(
                        isSelected ? Icons.check_circle : Icons.circle_outlined,
                        color: isSelected ? AppColors.royalIndigo : Colors.grey,
                      ),
                    ),
                  Expanded(
                    child: Text(
                      'ID: ${booking.id}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: booking.isConfirmed
                          ? Colors.green.shade100
                          : Colors.orange.shade100,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      booking.isConfirmed ? '已確認' : '待確認',
                      style: TextStyle(
                        fontSize: 12,
                        color: booking.isConfirmed
                            ? Colors.green.shade800
                            : Colors.orange.shade800,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),

              // 諮詢類型
              Row(
                children: [
                  Icon(
                    Icons.event,
                    color: isPast ? Colors.grey : AppColors.royalIndigo,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      booking.consultationType,
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: isPast ? Colors.grey : Colors.black,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),

              // 預約時間
              Row(
                children: [
                  const Icon(
                    Icons.access_time,
                    size: 16,
                    color: Colors.grey,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    formatDateTime(booking.bookingDate, booking.createdAt),
                    style: TextStyle(
                      color: isPast ? Colors.grey : Colors.black87,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),

              // 預約人資料
              if (booking.name.isNotEmpty) ...[
                Row(
                  children: [
                    const Icon(
                      Icons.person,
                      size: 16,
                      color: Colors.grey,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      booking.name,
                      style: TextStyle(
                        color: isPast ? Colors.grey : Colors.black87,
                      ),
                    ),
                  ],
                ),
              ],
              // 星盤資料
              if (birthDataName.isNotEmpty) ...[
                const SizedBox(height: 4),
                Row(
                  children: [
                    const Icon(
                      Icons.star,
                      size: 16,
                      color: Colors.grey,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '名稱: $birthDataName',
                      style: TextStyle(
                        color: isPast ? Colors.grey : Colors.black87,
                      ),
                    ),
                  ],
                ),
              ],

              // 按鈕列
              if (!isPast) ...[
                const SizedBox(height: 8),
                Row(
                  // mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    if (!booking.isConfirmed)
                      Expanded(
                        flex: 7, // "確認" 2 字
                        child: TextButton.icon(
                          onPressed: () => _confirmBooking(booking),
                          icon: const Icon(Icons.check_circle, size: 16),
                          label: const Text('確認'),
                          style: TextButton.styleFrom(
                            foregroundColor: Colors.green,
                          ),
                        ),
                      ),
                    if (booking.isConfirmed)
                      Expanded(
                        flex: 10, // "取消確認" 4 字
                        child: TextButton.icon(
                          onPressed: () => _unconfirmBooking(booking, false),
                          icon: const Icon(Icons.cancel, size: 16),
                          label: const Text('取消確認'),
                          style: TextButton.styleFrom(
                            foregroundColor: Colors.orange,
                          ),
                        ),
                      ),
                    // const SizedBox(width: 1),
                    if (birthDataName.isNotEmpty)
                      Expanded(
                        flex: 10, // "查看星盤" 4 字
                        child: TextButton.icon(
                          onPressed: () => _navigateToNatalChart(booking),
                          icon: const Icon(Icons.star, size: 16),
                          label: const Text('查看星盤'),
                          style: TextButton.styleFrom(
                            foregroundColor: AppColors.royalIndigo,
                          ),
                        ),
                      ),
                    // const SizedBox(width: 1),
                    Expanded(
                      flex: 10, // "取消預約" 4 字
                      child: TextButton.icon(
                        onPressed: () => _showDeleteConfirmation(booking),
                        icon: const Icon(Icons.delete, size: 16),
                        label: const Text('取消預約'),
                        style: TextButton.styleFrom(
                          foregroundColor: Colors.red,
                        ),
                      ),
                    ),
                  ],
                )
              ],
            ],
          ),
        ),
      ),
    );
  }
}
