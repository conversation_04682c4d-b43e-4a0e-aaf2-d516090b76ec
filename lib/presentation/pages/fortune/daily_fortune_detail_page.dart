import 'package:flutter/material.dart';

import '../../../data/models/interpretation/daily_fortune.dart';
import '../../../data/models/user/birth_data.dart';
import '../../themes/app_theme.dart';

/// 今日運勢詳情頁面
class DailyFortuneDetailPage extends StatelessWidget {
  final BirthData birthData;
  final PersonalizedDailyFortune dailyFortune;

  const DailyFortuneDetailPage({
    super.key,
    required this.birthData,
    required this.dailyFortune,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.lightCornsilk,
      appBar: _buildAppBar(),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 整體運勢卡片
            _buildOverallFortuneCard(),
            
            const SizedBox(height: 16),
            
            // 四個面向運勢
            _buildAspectsGrid(),
            
            const SizedBox(height: 16),
            
            // 幸運元素
            _buildLuckyElementsCard(),
            
            const SizedBox(height: 16),
            
            // 星象影響
            _buildPlanetaryInfluencesCard(),
            
            const SizedBox(height: 16),
            
            // 建議與提醒
            _buildAdviceCard(),
            
            const SizedBox(height: 16),
            
            // 星座運勢
            _buildZodiacFortunesCard(),
          ],
        ),
      ),
    );
  }

  /// 構建 AppBar
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: AppColors.lightCornsilk,
      elevation: 0,
      title: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '${birthData.name} 的運勢',
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.textDark,
            ),
          ),
          Text(
            _formatDate(dailyFortune.personalFortune.date),
            style: TextStyle(
              fontSize: 12,
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
      iconTheme: const IconThemeData(color: AppColors.solarAmber),
    );
  }

  /// 構建整體運勢卡片
  Widget _buildOverallFortuneCard() {
    final fortune = dailyFortune.personalFortune;
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            _getFortuneScoreColor(fortune.overallScore).withValues(alpha: 0.1),
            _getFortuneScoreColor(fortune.overallScore).withValues(alpha: 0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: _getFortuneScoreColor(fortune.overallScore).withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: _getFortuneScoreColor(fortune.overallScore).withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.auto_awesome,
                  color: _getFortuneScoreColor(fortune.overallScore),
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      fortune.title,
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textDark,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Text(
                          '整體運勢：',
                          style: TextStyle(
                            fontSize: 14,
                            color: AppColors.textSecondary,
                          ),
                        ),
                        Text(
                          fortune.fortuneLevel,
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: _getFortuneScoreColor(fortune.overallScore),
                          ),
                        ),
                        const SizedBox(width: 8),
                        ...List.generate(5, (index) {
                          return Icon(
                            index < fortune.overallScore ? Icons.star : Icons.star_border,
                            color: _getFortuneScoreColor(fortune.overallScore),
                            size: 16,
                          );
                        }),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            fortune.description,
            style: TextStyle(
              fontSize: 15,
              color: AppColors.textSecondary,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  /// 構建四個面向運勢網格
  Widget _buildAspectsGrid() {
    final fortune = dailyFortune.personalFortune;
    final aspects = [
      {
        'title': '愛情運勢',
        'aspect': fortune.loveAspect,
        'icon': Icons.favorite,
        'color': const Color(0xFFEC4899),
      },
      {
        'title': '事業運勢',
        'aspect': fortune.careerAspect,
        'icon': Icons.work,
        'color': const Color(0xFF059669),
      },
      {
        'title': '財運',
        'aspect': fortune.wealthAspect,
        'icon': Icons.account_balance_wallet,
        'color': const Color(0xFFF59E0B),
      },
      {
        'title': '健康運勢',
        'aspect': fortune.healthAspect,
        'icon': Icons.favorite_border,
        'color': const Color(0xFF6366F1),
      },
    ];

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        mainAxisExtent: 165, // 增加高度以容納所有內容
      ),
      itemCount: aspects.length,
      itemBuilder: (context, index) {
        final item = aspects[index];
        final aspect = item['aspect'] as FortuneAspect;
        final color = item['color'] as Color;
        
        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: color.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      item['icon'] as IconData,
                      color: color,
                      size: 20,
                    ),
                  ),
                  const Spacer(),
                  ...List.generate(5, (starIndex) {
                    return Icon(
                      starIndex < aspect.score ? Icons.star : Icons.star_border,
                      color: color,
                      size: 12,
                    );
                  }),
                ],
              ),
              const SizedBox(height: 10),
              Text(
                item['title'] as String,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textDark,
                ),
              ),
              const SizedBox(height: 3),
              Expanded(
                child: Text(
                  aspect.description,
                  style: TextStyle(
                    fontSize: 12,
                    color: AppColors.textSecondary,
                    height: 1.3,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              const SizedBox(height: 6),
              Text(
                aspect.advice,
                style: TextStyle(
                  fontSize: 11,
                  color: color,
                  fontWeight: FontWeight.w500,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        );
      },
    );
  }

  /// 獲取運勢評分顏色
  Color _getFortuneScoreColor(int score) {
    switch (score) {
      case 5:
        return const Color(0xFF4CAF50);
      case 4:
        return const Color(0xFF8BC34A);
      case 3:
        return const Color(0xFFFFC107);
      case 2:
        return const Color(0xFFFF9800);
      case 1:
        return const Color(0xFFF44336);
      default:
        return const Color(0xFF9E9E9E);
    }
  }

  /// 構建幸運元素卡片
  Widget _buildLuckyElementsCard() {
    final fortune = dailyFortune.personalFortune;
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.solarAmber.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.auto_fix_high,
                  color: AppColors.solarAmber,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                '幸運元素',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textDark,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '幸運顏色',
                      style: TextStyle(
                        fontSize: 12,
                        color: AppColors.textSecondary,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: AppColors.solarAmber.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: AppColors.solarAmber.withValues(alpha: 0.3),
                        ),
                      ),
                      child: Text(
                        fortune.luckyColor,
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: AppColors.solarAmber,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '幸運數字',
                      style: TextStyle(
                        fontSize: 12,
                        color: AppColors.textSecondary,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Wrap(
                      spacing: 6,
                      children: fortune.luckyNumbers.map((number) {
                        return Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: AppColors.royalIndigo.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: AppColors.royalIndigo.withValues(alpha: 0.3),
                            ),
                          ),
                          child: Text(
                            number.toString(),
                            style: const TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                              color: AppColors.royalIndigo,
                            ),
                          ),
                        );
                      }).toList(),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 構建星象影響卡片
  Widget _buildPlanetaryInfluencesCard() {
    final influences = dailyFortune.personalFortune.planetaryInfluences;
    if (influences.isEmpty) return const SizedBox.shrink();

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.cosmicPurple.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.public,
                  color: AppColors.cosmicPurple,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                '主要星象影響',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textDark,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          ...influences.map((influence) {
            return Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    margin: const EdgeInsets.only(top: 6),
                    width: 4,
                    height: 4,
                    decoration: BoxDecoration(
                      color: AppColors.cosmicPurple,
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      influence,
                      style: TextStyle(
                        fontSize: 14,
                        color: AppColors.textSecondary,
                        height: 1.4,
                      ),
                    ),
                  ),
                ],
              ),
            );
          }),
        ],
      ),
    );
  }

  /// 構建建議與提醒卡片
  Widget _buildAdviceCard() {
    final fortune = dailyFortune.personalFortune;
    final hasRecommendations = fortune.recommendations.isNotEmpty;
    final hasWarnings = fortune.warnings.isNotEmpty;

    if (!hasRecommendations && !hasWarnings) return const SizedBox.shrink();

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.successGreen.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.lightbulb_outline,
                  color: AppColors.successGreen,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                '建議與提醒',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textDark,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          if (hasRecommendations) ...[
            _buildAdviceSection('建議行動', fortune.recommendations, AppColors.successGreen, Icons.check_circle_outline),
            if (hasWarnings) const SizedBox(height: 16),
          ],

          if (hasWarnings) ...[
            _buildAdviceSection('注意事項', fortune.warnings, AppColors.warning, Icons.warning_amber_outlined),
          ],
        ],
      ),
    );
  }

  /// 構建建議區塊
  Widget _buildAdviceSection(String title, List<String> items, Color color, IconData icon) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, color: color, size: 16),
            const SizedBox(width: 8),
            Text(
              title,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        ...items.map((item) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 6),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  margin: const EdgeInsets.only(top: 6),
                  width: 4,
                  height: 4,
                  decoration: BoxDecoration(
                    color: color,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    item,
                    style: TextStyle(
                      fontSize: 14,
                      color: AppColors.textSecondary,
                      height: 1.4,
                    ),
                  ),
                ),
              ],
            ),
          );
        }),
      ],
    );
  }

  /// 構建星座運勢卡片
  Widget _buildZodiacFortunesCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.royalIndigo.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.stars,
                  color: AppColors.royalIndigo,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                '星座運勢',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textDark,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          _buildZodiacFortuneItem('太陽星座', dailyFortune.sunSignFortune),

          if (dailyFortune.moonSignFortune != null) ...[
            const SizedBox(height: 12),
            _buildZodiacFortuneItem('月亮星座', dailyFortune.moonSignFortune!),
          ],

          if (dailyFortune.risingSignFortune != null) ...[
            const SizedBox(height: 12),
            _buildZodiacFortuneItem('上升星座', dailyFortune.risingSignFortune!),
          ],
        ],
      ),
    );
  }

  /// 構建星座運勢項目
  Widget _buildZodiacFortuneItem(String label, ZodiacDailyFortune zodiacFortune) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.royalIndigo.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: AppColors.royalIndigo.withValues(alpha: 0.1),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                '$label：${zodiacFortune.zodiacName}',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textDark,
                ),
              ),
              const Spacer(),
              ...List.generate(5, (index) {
                return Icon(
                  index < zodiacFortune.fortune.overallScore ? Icons.star : Icons.star_border,
                  color: AppColors.royalIndigo,
                  size: 14,
                );
              }),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            zodiacFortune.fortune.description,
            style: TextStyle(
              fontSize: 13,
              color: AppColors.textSecondary,
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  /// 格式化日期
  String _formatDate(DateTime date) {
    final weekdays = ['週一', '週二', '週三', '週四', '週五', '週六', '週日'];
    final weekday = weekdays[date.weekday - 1];
    return '${date.year}年${date.month}月${date.day}日 $weekday';
  }
}
