import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../data/models/astrology/chart_data.dart';
import '../../../data/models/astrology/chart_type.dart';
import '../../../data/models/user/birth_data.dart';
import '../../../presentation/themes/app_theme.dart';
import '../../core/utils/logger_utils.dart';
import '../../data/models/astrology/chart_settings.dart';
import '../../data/services/api/interpretation_config_service.dart';
import '../../presentation/viewmodels/chart_viewmodel.dart';
import '../../shared/widgets/common/date_time_picker_bottom_sheet.dart';
import '../../shared/widgets/recent_persons_section.dart';
import '../viewmodels/files_viewmodel.dart';
import 'ai_interpretation_result_page.dart';
import 'person_selector_page.dart';

/// 初心者模式的星盤選擇頁面 - 專注於參數選擇
class StarlightChartSelectionPage extends StatefulWidget {
  final BirthData? primaryPerson;
  final BirthData? secondaryPerson;
  final ChartType chartType;
  final String analysisTitle;
  final bool fromAnalysisPage;

  const StarlightChartSelectionPage({
    super.key,
    this.primaryPerson,
    this.secondaryPerson,
    required this.chartType,
    required this.analysisTitle,
    this.fromAnalysisPage = false,
  });

  @override
  State<StarlightChartSelectionPage> createState() => _StarlightChartSelectionPageState();
}

class _StarlightChartSelectionPageState extends State<StarlightChartSelectionPage> {
  // 選擇的人物
  BirthData? _primaryPerson;
  BirthData? _secondaryPerson;
  
  // 選擇的時間
  DateTime _selectedDate = DateTime.now();

  @override
  void initState() {
    super.initState();
    _primaryPerson = widget.primaryPerson;
    _secondaryPerson = widget.secondaryPerson;
    
    // 加載出生資料
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadBirthData();
    });
  }

  void _loadBirthData() async {
    final filesViewModel = Provider.of<FilesViewModel>(context, listen: false);
    await filesViewModel.loadBirthData();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.analysisTitle,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppColors.solarAmber,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppColors.solarAmber.withValues(alpha: 0.1),
              Colors.white,
            ],
            stops: const [0.0, 0.3],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // 說明卡片
              _buildInstructionCard(),
              
              // 參數選擇區域
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 近期選中人物
                      _buildRecentPersonsSection(),
                      
                      const SizedBox(height: 20),
                      
                      // 主要人物選擇
                      _buildPersonSelectionSection(),
                      
                      // 第二個人選擇（如果需要）
                      if (widget.chartType.requiresTwoPersons) ...[
                        const SizedBox(height: 20),
                        _buildSecondPersonSelectionSection(),
                      ],
                      
                      // 時間選擇（如果需要）
                      if (widget.chartType.requiresSpecificDate) ...[
                        const SizedBox(height: 20),
                        _buildTimeSelectionSection(),
                      ],
                      
                      const SizedBox(height: 32),
                    ],
                  ),
                ),
              ),
              
              // 底部按鈕
              _buildBottomButton(),
            ],
          ),
        ),
      ),
    );
  }

  /// 構建說明卡片
  Widget _buildInstructionCard() {
    String description = _getAnalysisDescription();
    
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.solarAmber.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.solarAmber.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.solarAmber.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.info_outline,
                  color: AppColors.solarAmber,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  widget.analysisTitle,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textDark,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            description,
            style: const TextStyle(
              fontSize: 14,
              color: AppColors.textDark,
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  /// 構建近期選中人物區域
  Widget _buildRecentPersonsSection() {
    return Consumer<FilesViewModel>(
      builder: (context, filesViewModel, child) {
        if (filesViewModel.birthDataList.isEmpty) {
          return const SizedBox.shrink();
        }
        
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '快速選擇',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppColors.textDark,
              ),
            ),
            const SizedBox(height: 8),
            RecentPersonsSection(
              allPersons: filesViewModel.birthDataList,
              onPersonSelected: _selectRecentPerson,
              maxCount: 5,
              showClearButton: false,
              hideWhenSearching: false,
              title: '',
              icon: Icons.history,
              themeColor: AppColors.solarAmber,
              showSelectedState: true,
              isPersonSelected: (person) =>
                  _primaryPerson?.id == person.id ||
                  _secondaryPerson?.id == person.id,
            ),
          ],
        );
      },
    );
  }

  /// 構建主要人物選擇區域
  Widget _buildPersonSelectionSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '選擇要分析的人',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: AppColors.textDark,
          ),
        ),
        const SizedBox(height: 8),
        _primaryPerson != null
            ? _buildPersonCard(
                person: _primaryPerson!,
                label: '分析對象',
                color: AppColors.solarAmber,
                onTap: _selectPrimaryPerson,
                onRemove: () => setState(() => _primaryPerson = null),
              )
            : _buildSelectPersonCard(
                label: '點擊選擇要分析的人',
                color: AppColors.solarAmber,
                onTap: _selectPrimaryPerson,
              ),
      ],
    );
  }

  /// 構建第二個人選擇區域
  Widget _buildSecondPersonSelectionSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '選擇比較對象',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: AppColors.textDark,
          ),
        ),
        const SizedBox(height: 8),
        _secondaryPerson != null
            ? _buildPersonCard(
                person: _secondaryPerson!,
                label: '比較對象',
                color: Colors.pink,
                onTap: _selectSecondaryPerson,
                onRemove: () => setState(() => _secondaryPerson = null),
              )
            : _buildSelectPersonCard(
                label: '點擊選擇比較對象',
                color: Colors.pink,
                onTap: _selectSecondaryPerson,
              ),
      ],
    );
  }

  /// 構建時間選擇區域
  Widget _buildTimeSelectionSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '選擇分析時間',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: AppColors.textDark,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: AppColors.textLight),
          ),
          child: ListTile(
            leading: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppColors.indigoLight.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(
                Icons.access_time,
                color: AppColors.indigoLight,
                size: 20,
              ),
            ),
            title: const Text(
              '分析時間',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
            subtitle: Text(
              _formatDateTime(_selectedDate),
              style: const TextStyle(fontSize: 13),
            ),
            trailing: const Icon(Icons.edit, size: 18),
            onTap: _selectDateTime,
          ),
        ),
      ],
    );
  }

  /// 構建人物卡片
  Widget _buildPersonCard({
    required BirthData person,
    required String label,
    required Color color,
    required VoidCallback onTap,
    required VoidCallback onRemove,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            Icons.person,
            color: color,
            size: 20,
          ),
        ),
        title: Text(
          person.name,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: color,
                fontWeight: FontWeight.w500,
              ),
            ),
            Text(
              '${_formatDateTime(person.dateTime)} | ${person.birthPlace}',
              style: const TextStyle(
                fontSize: 12,
                color: AppColors.textMedium,
              ),
            ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.edit, size: 18),
              onPressed: onTap,
              tooltip: '更換',
            ),
            IconButton(
              icon: const Icon(Icons.close, size: 18),
              onPressed: onRemove,
              tooltip: '移除',
            ),
          ],
        ),
        onTap: onTap,
      ),
    );
  }

  /// 構建選擇人物卡片
  Widget _buildSelectPersonCard({
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
          style: BorderStyle.solid,
        ),
      ),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            Icons.person_add,
            color: color,
            size: 20,
          ),
        ),
        title: Text(
          label,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        subtitle: const Text(
          '從您的出生資料中選擇',
          style: TextStyle(
            fontSize: 12,
            color: AppColors.textMedium,
          ),
        ),
        trailing: Icon(
          Icons.arrow_forward_ios,
          color: color,
          size: 16,
        ),
        onTap: onTap,
      ),
    );
  }

  /// 構建底部按鈕
  Widget _buildBottomButton() {
    final bool canProceed = _canProceed();

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 5,
            offset: const Offset(0, -1),
          ),
        ],
      ),
      child: SizedBox(
        width: double.infinity,
        child: ElevatedButton(
          onPressed: canProceed ? _navigateToInterpretation : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: canProceed ? AppColors.solarAmber : Colors.grey.shade300,
            foregroundColor: canProceed ? Colors.white : Colors.grey.shade600,
            padding: const EdgeInsets.symmetric(vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            elevation: canProceed ? 2 : 0,
          ),
          child: Text(
            canProceed ? '開始分析' : _getMissingRequirement(),
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
    );
  }

  /// 獲取分析描述
  String _getAnalysisDescription() {
    switch (widget.chartType) {
      case ChartType.natal:
        return '透過您的出生時間和地點，分析您的性格特質、天賦能力和人生方向。';
      case ChartType.synastry:
        return '比較兩個人的出生資料，分析你們的相處模式和關係相容性。';
      case ChartType.composite:
        return '將兩個人的出生資料結合，了解你們在一起會產生什麼樣的化學反應。';
      case ChartType.davison:
        return '分析兩個人相遇的時空意義，了解這段關係的深層含義。';
      case ChartType.marks:
        return '從您的角度分析這段關係，了解您在關係中的內心感受。';
      case ChartType.transit:
        return '分析當前天象對您的影響，了解目前的運勢和需要注意的事項。';
      case ChartType.secondaryProgression:
        return '分析您未來的人生發展方向和重要轉折點。';
      case ChartType.tertiaryProgression:
        return '了解您近期的心理狀態和情緒變化趨勢。';
      case ChartType.solarReturn:
        return '分析這一年的整體運勢和發展重點。';
      case ChartType.lunarReturn:
        return '了解每個月的情緒變化和內在需求。';
      default:
        return '請選擇所需的參數，我們將為您提供詳細的分析。';
    }
  }

  /// 選擇近期人物
  void _selectRecentPerson(BirthData person) {
    setState(() {
      if (_primaryPerson == null) {
        _primaryPerson = person;
      } else if (widget.chartType.requiresTwoPersons &&
                 _secondaryPerson == null &&
                 _primaryPerson!.id != person.id) {
        _secondaryPerson = person;
      } else if (_primaryPerson!.id != person.id) {
        _primaryPerson = person;
      }
    });
  }

  /// 選擇主要人物
  Future<void> _selectPrimaryPerson() async {
    final filesViewModel = Provider.of<FilesViewModel>(context, listen: false);

    final selectedPerson = await PersonSelectorPage.show(
      context: context,
      birthDataList: filesViewModel.birthDataList,
      title: '選擇要分析的人',
      buttonColor: AppColors.solarAmber,
      excludeIds: _secondaryPerson != null ? [_secondaryPerson!.id] : null,
    );

    if (selectedPerson != null) {
      setState(() {
        _primaryPerson = selectedPerson;
      });
    }
  }

  /// 選擇第二個人
  Future<void> _selectSecondaryPerson() async {
    final filesViewModel = Provider.of<FilesViewModel>(context, listen: false);

    final selectedPerson = await PersonSelectorPage.show(
      context: context,
      birthDataList: filesViewModel.birthDataList,
      title: '選擇比較對象',
      buttonColor: Colors.pink,
      excludeIds: _primaryPerson != null ? [_primaryPerson!.id] : null,
    );

    if (selectedPerson != null) {
      setState(() {
        _secondaryPerson = selectedPerson;
      });
    }
  }

  /// 選擇日期時間
  Future<void> _selectDateTime() async {
    final selectedDateTime = await DateTimePickerBottomSheet.show(
      context: context,
      initialDateTime: _selectedDate,
      title: '選擇分析時間',
      minDate: DateTime(1800, 1, 1),
      maxDate: DateTime(2100, 12, 31),
      defaultDateTime: DateTime.now(),
    );

    if (selectedDateTime != null) {
      setState(() {
        _selectedDate = selectedDateTime;
      });
    }
  }

  /// 檢查是否可以進行分析
  bool _canProceed() {
    // 檢查主要人物
    if (_primaryPerson == null) return false;

    // 檢查第二個人（如果需要）
    if (widget.chartType.requiresTwoPersons && _secondaryPerson == null) {
      return false;
    }

    return true;
  }

  /// 獲取缺少的需求
  String _getMissingRequirement() {
    if (_primaryPerson == null) {
      return '請選擇要分析的人';
    }

    if (widget.chartType.requiresTwoPersons && _secondaryPerson == null) {
      return '請選擇比較對象';
    }

    return '請完成所有設定';
  }

  /// 直接進行分析
  Future<void> _navigateToInterpretation() async {
    if (!_canProceed()) return;

    // 創建 ChartData
    final chartData = ChartData(
      chartType: widget.chartType,
      primaryPerson: _primaryPerson!,
      secondaryPerson: _secondaryPerson,
      specificDate: widget.chartType.requiresSpecificDate ? _selectedDate : null,
    );

    try {
      // 顯示載入提示
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('正在計算星盤並準備分析...'),
            backgroundColor: AppColors.royalIndigo,
            duration: Duration(seconds: 2),
          ),
        );
      }

      // 計算星盤數據
      final calculatedChartData = await ChartViewModel.calculateChartData(chartData);

      // 獲取預設的解讀選項（使用系統設定）
      InterpretationOption? defaultOption = await _getDefaultInterpretationOption();

      if (mounted && defaultOption != null) {
        // 直接導航到 AI 解讀結果頁面
        await _performDirectInterpretation(calculatedChartData, defaultOption);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).clearSnackBars();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('計算星盤時發生錯誤: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// 獲取預設的解讀選項（系統設定）
  Future<InterpretationOption?> _getDefaultInterpretationOption() async {
    try {
      // 根據星盤類型獲取配置
      final config = await InterpretationConfigService.instance.loadConfig(widget.chartType);

      // 尋找系統設定選項
      final systemOption = config.options.firstWhere(
        (option) => option.id == 'system_settings',
        orElse: () => config.options.first, // 如果沒有系統設定選項，使用第一個選項
      );

      return systemOption;
    } catch (e) {
      logger.e('獲取預設解讀選項失敗: $e');
      return null;
    }
  }

  /// 執行直接分析
  Future<void> _performDirectInterpretation(
    ChartData chartData,
    InterpretationOption option,
  ) async {
    try {
      // 保存 context 以避免異步間隙問題
      final navigator = Navigator.of(context);
      ChartData chartDataToUse = chartData;

      // 如果解讀選項指定了宮位制或使用系統設定，需要重新計算星盤
      if (option.houseSystem != null || option.useSystemHouseSystem) {
        HouseSystem? houseSystemToUse;

        if (option.useSystemHouseSystem) {
          // 使用系統設定的宮位制
          final chartSettings = await ChartSettings.loadFromPrefs();
          houseSystemToUse = chartSettings.houseSystem;
          logger.d('使用系統設定的宮位制: $houseSystemToUse');
        } else {
          // 使用選項指定的宮位制
          houseSystemToUse = option.houseSystem;
          logger.d('使用指定的宮位制: $houseSystemToUse');
        }

        // 使用 ChartViewModel 重新計算星盤
        final chartViewModel = ChartViewModel.withChartData(
          initialChartData: chartData,
        );

        // 異步計算星盤
        await chartViewModel.calculateChart(houseSystem: houseSystemToUse);

        // 更新星盤資料
        chartDataToUse = chartViewModel.chartData;

        debugPrint('星盤重新計算完成，使用宮位制: $houseSystemToUse');
      }

      if (!mounted) return;

      // 清除載入提示
      ScaffoldMessenger.of(context).clearSnackBars();

      // 導航到 AI 解讀結果頁面
      navigator.push(
        MaterialPageRoute(
          builder: (context) => AIInterpretationResultPage(
            chartData: chartDataToUse,
            interpretationTitle: option.title,
            subtitle: option.subtitle,
            suggestedQuestions: option.questions,
            keyPoint: option.keyPoint,
            autoExecuteFirstQuestion: false, // 自動執行第一個問題
          ),
        ),
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).clearSnackBars();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('執行分析時發生錯誤: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
  
  /// 格式化日期時間
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}年${dateTime.month}月${dateTime.day}日 '
           '${dateTime.hour.toString().padLeft(2, '0')}:'
           '${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
