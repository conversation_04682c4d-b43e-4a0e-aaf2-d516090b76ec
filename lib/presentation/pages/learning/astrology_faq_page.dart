import 'package:flutter/material.dart';

import '../../../astreal.dart';
import '../../../shared/widgets/common/responsive_wrapper.dart';

/// 占星常見問題頁面
class AstrologyFaqPage extends StatefulWidget {
  const AstrologyFaqPage({super.key});

  @override
  State<AstrologyFaqPage> createState() => _AstrologyFaqPageState();
}

class _AstrologyFaqPageState extends State<AstrologyFaqPage> {
  
  /// FAQ 分類
  final List<FaqCategory> _faqCategories = [
    FaqCategory(
      title: '基礎概念',
      icon: Icons.school,
      color: AppColors.royalIndigo,
      faqs: [
        Faq(
          question: '什麼是占星學？',
          answer: '占星學是一門古老的學問，研究天體運動與人類生活的關聯。它認為天體的位置和運動會影響人的性格、命運和生活事件。現代占星學更注重心理分析和自我認知的工具。',
        ),
        Faq(
          question: '星座和星盤有什麼不同？',
          answer: '星座通常指的是太陽星座，只是根據出生日期確定。而星盤是完整的天體圖，包含所有行星在十二星座和十二宮位的位置，需要精確的出生時間、日期和地點才能計算。',
        ),
        Faq(
          question: '為什麼需要精確的出生時間？',
          answer: '出生時間決定了上升星座和宮位系統。即使相差幾分鐘，上升星座可能會改變，進而影響整個宮位的分布。這會大大影響星盤解讀的準確性。',
        ),
        Faq(
          question: '占星學有科學根據嗎？',
          answer: '占星學目前沒有被主流科學認可，但它作為一種心理工具和自我探索的方法，對許多人具有實用價值。重要的是以開放但理性的態度來看待占星學。',
        ),
      ],
    ),
    FaqCategory(
      title: '星盤解讀',
      icon: Icons.auto_awesome,
      color: AppColors.solarAmber,
      faqs: [
        Faq(
          question: '如何開始解讀自己的星盤？',
          answer: '建議從三大要素開始：太陽星座（核心自我）、月亮星座（情感需求）、上升星座（外在表現）。然後逐步了解其他行星的位置和相位關係。',
        ),
        Faq(
          question: '什麼是上升星座？',
          answer: '上升星座是出生時東方地平線上升起的星座，代表你給他人的第一印象和外在表現方式。它也決定了整個星盤的宮位分布。',
        ),
        Faq(
          question: '行星逆行是什麼意思？',
          answer: '行星逆行是從地球觀察，行星看起來向後移動的現象。在占星學中，逆行行星的能量表達會更加內化、反思，可能帶來重新檢視相關領域的機會。',
        ),
        Faq(
          question: '空宮是什麼意思？',
          answer: '空宮指沒有行星落入的宮位。這不代表該領域不重要，而是可能較少主動關注，或者通過宮主星來表達該宮位的主題。',
        ),
      ],
    ),
    FaqCategory(
      title: '相位與宮位',
      icon: Icons.timeline,
      color: AppColors.warning,
      faqs: [
        Faq(
          question: '什麼是相位？',
          answer: '相位是行星之間的角度關係。主要相位包括合相（0°）、六分相（60°）、四分相（90°）、三分相（120°）和對分相（180°）。不同相位代表不同的能量互動模式。',
        ),
        Faq(
          question: '哪些相位是好的，哪些是壞的？',
          answer: '現代占星學不再簡單地將相位分為好壞。三分相和六分相通常帶來和諧能量，四分相和對分相帶來挑戰但也是成長機會。合相的影響取決於涉及的行星。',
        ),
        Faq(
          question: '什麼是宮位？',
          answer: '宮位是星盤被分成的12個區域，每個宮位代表生活的不同領域。例如第一宮代表自我，第七宮代表伙伴關係，第十宮代表事業等。',
        ),
        Faq(
          question: '宮位和星座有什麼關係？',
          answer: '宮位代表生活領域，星座代表表達方式。例如，金星在第五宮表示在創造和戀愛方面的表達，如果金星在天秤座，則會以和諧、美感的方式表達。',
        ),
      ],
    ),
    FaqCategory(
      title: '實用應用',
      icon: Icons.lightbulb,
      color: AppColors.success,
      faqs: [
        Faq(
          question: '占星學可以預測未來嗎？',
          answer: '占星學更適合用來了解趨勢和可能性，而不是預測具體事件。它可以幫助我們了解某個時期的能量特質，但最終的選擇和行動仍在我們手中。',
        ),
        Faq(
          question: '如何運用占星學改善生活？',
          answer: '占星學可以幫助自我認知、了解個人優勢和挑戰、改善人際關係、選擇適合的時機行動，以及理解生命中的重要轉折點。',
        ),
        Faq(
          question: '星盤會隨時間改變嗎？',
          answer: '出生星盤是固定的，但天空中的行星持續運動，形成「流年」或「推運」，這些會與出生星盤產生新的相位，影響我們在不同時期的經歷。',
        ),
        Faq(
          question: '合盤是什麼？',
          answer: '合盤是比較兩個人星盤的技術，用來了解兩人的相容性和關係動態。它可以應用在愛情、友誼、商業夥伴等各種關係中。',
        ),
      ],
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          '占星常見問題',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppColors.royalIndigo,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppColors.royalIndigo.withValues(alpha: 0.1),
              Colors.white,
            ],
            stops: const [0.0, 0.3],
          ),
        ),
        child: ResponsivePageWrapper(
          maxWidth: 800.0,
          child: ListView.builder(
            padding: ResponsiveUtils.getResponsivePadding(context),
            itemCount: _faqCategories.length,
            itemBuilder: (context, index) {
              final category = _faqCategories[index];
              return Padding(
                padding: const EdgeInsets.only(bottom: 24),
                child: ResponsiveCardWrapper(
                  maxWidth: 700.0,
                  child: _buildCategoryCard(category),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  /// 構建分類卡片
  Widget _buildCategoryCard(FaqCategory category) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 分類標題
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: category.color.withValues(alpha: 0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: category.color.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    category.icon,
                    color: category.color,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  category.title,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: category.color,
                  ),
                ),
              ],
            ),
          ),
          
          // FAQ 列表
          ...category.faqs.asMap().entries.map((entry) {
            final index = entry.key;
            final faq = entry.value;
            final isLast = index == category.faqs.length - 1;

            return _buildFaqItem(faq, category.color, isLast);
          }),
        ],
      ),
    );
  }

  /// 構建FAQ項目
  Widget _buildFaqItem(Faq faq, Color categoryColor, bool isLast) {
    return Container(
      decoration: BoxDecoration(
        border: isLast ? null : Border(
          bottom: BorderSide(
            color: Colors.grey.withValues(alpha: 0.1),
            width: 1,
          ),
        ),
      ),
      child: ExpansionTile(
        tilePadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
        childrenPadding: const EdgeInsets.only(
          left: 20,
          right: 20,
          bottom: 16,
        ),
        title: Text(
          faq.question,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppColors.textDark,
          ),
        ),
        iconColor: categoryColor,
        collapsedIconColor: Colors.grey[600],
        children: [
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: categoryColor.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: categoryColor.withValues(alpha: 0.1),
              ),
            ),
            child: Text(
              faq.answer,
              style: const TextStyle(
                fontSize: 14,
                color: AppColors.textDark,
                height: 1.6,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// FAQ 分類數據類
class FaqCategory {
  final String title;
  final IconData icon;
  final Color color;
  final List<Faq> faqs;

  FaqCategory({
    required this.title,
    required this.icon,
    required this.color,
    required this.faqs,
  });
}

/// FAQ 數據類
class Faq {
  final String question;
  final String answer;

  Faq({
    required this.question,
    required this.answer,
  });
}
