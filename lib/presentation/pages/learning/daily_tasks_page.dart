import 'package:flutter/material.dart';

import '../../../astreal.dart';
import '../../../data/services/api/learning_progress_service.dart';

/// 每日任務頁面
class DailyTasksPage extends StatefulWidget {
  const DailyTasksPage({super.key});

  @override
  State<DailyTasksPage> createState() => _DailyTasksPageState();
}

class _DailyTasksPageState extends State<DailyTasksPage> {
  List<DailyTask> _tasks = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadDailyTasks();
  }

  /// 載入每日任務
  Future<void> _loadDailyTasks() async {
    try {
      final progress = await LearningProgressService.getProgress();
      final tasks = _generateDailyTasks(progress);
      
      setState(() {
        _tasks = tasks;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 生成每日任務
  List<DailyTask> _generateDailyTasks(LearningProgress progress) {
    final today = DateTime.now();
    final tasks = <DailyTask>[];

    // 基礎任務
    tasks.add(DailyTask(
      id: 'daily_zodiac',
      title: '學習一個星座',
      description: '深入了解一個星座的特質',
      points: 10,
      isCompleted: _isTaskCompletedToday('daily_zodiac', today),
      icon: Icons.star,
      color: AppColors.solarAmber,
    ));

    tasks.add(DailyTask(
      id: 'daily_planet',
      title: '了解一個行星',
      description: '學習一個行星的占星意義',
      points: 15,
      isCompleted: _isTaskCompletedToday('daily_planet', today),
      icon: Icons.public,
      color: AppColors.indigoLight,
    ));

    tasks.add(DailyTask(
      id: 'daily_chart',
      title: '查看星盤',
      description: '觀察並分析一個星盤',
      points: 20,
      isCompleted: _isTaskCompletedToday('daily_chart', today),
      icon: Icons.circle_outlined,
      color: AppColors.royalIndigo,
    ));

    // 根據進度添加進階任務
    if (progress.progressPercentage > 25) {
      tasks.add(DailyTask(
        id: 'daily_compatibility',
        title: '星座配對分析',
        description: '探索兩個星座的相容性',
        points: 25,
        isCompleted: _isTaskCompletedToday('daily_compatibility', today),
        icon: Icons.favorite,
        color: AppColors.warning,
      ));
    }

    if (progress.progressPercentage > 50) {
      tasks.add(DailyTask(
        id: 'daily_advanced',
        title: '進階學習',
        description: '完成一個進階占星課程',
        points: 30,
        isCompleted: _isTaskCompletedToday('daily_advanced', today),
        icon: Icons.school,
        color: Colors.purple,
      ));
    }

    return tasks;
  }

  /// 檢查任務是否在今天完成
  bool _isTaskCompletedToday(String taskId, DateTime today) {
    // 這裡可以實現檢查邏輯，暫時返回 false
    return false;
  }

  /// 完成任務
  Future<void> _completeTask(DailyTask task) async {
    try {
      // 更新學習進度
      await LearningProgressService.completeLesson(task.id, task.points);
      
      setState(() {
        task.isCompleted = true;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('任務完成！獲得 ${task.points} 積分'),
            backgroundColor: AppColors.successGreen,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('完成任務失敗：$e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          '每日任務',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppColors.solarAmber,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppColors.solarAmber.withValues(alpha: 0.1),
              Colors.white,
            ],
            stops: const [0.0, 0.3],
          ),
        ),
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : ListView(
                padding: const EdgeInsets.all(16),
                children: [
                  // 標題卡片
                  StyledCard(
                    child: Padding(
                      padding: const EdgeInsets.all(20),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Container(
                                width: 50,
                                height: 50,
                                decoration: BoxDecoration(
                                  color: AppColors.solarAmber.withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(25),
                                ),
                                child: const Icon(
                                  Icons.task_alt,
                                  color: AppColors.solarAmber,
                                  size: 24,
                                ),
                              ),
                              const SizedBox(width: 16),
                              const Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      '今日任務',
                                      style: TextStyle(
                                        fontSize: 20,
                                        fontWeight: FontWeight.bold,
                                        color: AppColors.textDark,
                                      ),
                                    ),
                                    SizedBox(height: 4),
                                    Text(
                                      '完成任務獲得積分，提升學習等級',
                                      style: TextStyle(
                                        fontSize: 14,
                                        color: Colors.grey,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 20),
                  
                  // 任務列表
                  ..._tasks.map((task) => Padding(
                    padding: const EdgeInsets.only(bottom: 12),
                    child: _buildTaskCard(task),
                  )),
                ],
              ),
      ),
    );
  }

  /// 構建任務卡片
  Widget _buildTaskCard(DailyTask task) {
    return StyledCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            // 任務圖標
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: task.isCompleted 
                    ? AppColors.successGreen.withValues(alpha: 0.1)
                    : task.color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(25),
              ),
              child: Icon(
                task.isCompleted ? Icons.check_circle : task.icon,
                color: task.isCompleted ? AppColors.successGreen : task.color,
                size: 24,
              ),
            ),
            
            const SizedBox(width: 16),
            
            // 任務詳情
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    task.title,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: task.isCompleted ? Colors.grey : AppColors.textDark,
                      decoration: task.isCompleted ? TextDecoration.lineThrough : null,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    task.description,
                    style: TextStyle(
                      fontSize: 14,
                      color: task.isCompleted ? Colors.grey : Colors.grey.shade600,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Icon(
                        Icons.stars,
                        size: 16,
                        color: task.isCompleted ? Colors.grey : AppColors.solarAmber,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '${task.points} 積分',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: task.isCompleted ? Colors.grey : AppColors.solarAmber,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            
            // 完成按鈕
            if (!task.isCompleted) ...[
              const SizedBox(width: 12),
              ElevatedButton(
                onPressed: () => _completeTask(task),
                style: ElevatedButton.styleFrom(
                  backgroundColor: task.color,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(20),
                  ),
                ),
                child: const Text(
                  '完成',
                  style: TextStyle(fontSize: 12),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// 每日任務模型
class DailyTask {
  final String id;
  final String title;
  final String description;
  final int points;
  bool isCompleted;
  final IconData icon;
  final Color color;

  DailyTask({
    required this.id,
    required this.title,
    required this.description,
    required this.points,
    required this.isCompleted,
    required this.icon,
    required this.color,
  });
}
