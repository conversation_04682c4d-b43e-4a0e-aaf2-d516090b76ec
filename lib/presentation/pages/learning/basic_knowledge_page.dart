import 'package:flutter/material.dart';

import '../../../astreal.dart';
import '../../../shared/widgets/common/responsive_wrapper.dart';

/// 基礎知識頁面 - 十二星座介紹
class BasicKnowledgePage extends StatefulWidget {
  const BasicKnowledgePage({super.key});

  @override
  State<BasicKnowledgePage> createState() => _BasicKnowledgePageState();
}

class _BasicKnowledgePageState extends State<BasicKnowledgePage> {
  
  /// 十二星座資料
  final List<Map<String, dynamic>> _zodiacSigns = [
    {
      'name': '牡羊座',
      'symbol': '♈',
      'element': '火象',
      'quality': '基本',
      'dates': '3/21 - 4/19',
      'keywords': ['勇敢', '積極', '領導', '衝動'],
      'description': '牡羊座是黃道十二宮的第一個星座，象徵著新的開始和無窮的活力。牡羊座的人通常充滿熱情，勇於冒險，喜歡挑戰。',
      'color': Colors.red,
    },
    {
      'name': '金牛座',
      'symbol': '♉',
      'element': '土象',
      'quality': '固定',
      'dates': '4/20 - 5/20',
      'keywords': ['穩定', '實際', '堅持', '享受'],
      'description': '金牛座以穩定和實際著稱，他們重視安全感和物質享受，做事踏實可靠，但有時會顯得固執。',
      'color': Colors.green,
    },
    {
      'name': '雙子座',
      'symbol': '♊',
      'element': '風象',
      'quality': '變動',
      'dates': '5/21 - 6/20',
      'keywords': ['聰明', '好奇', '溝通', '多變'],
      'description': '雙子座的人機智聰明，善於溝通，對新事物充滿好奇心。他們適應力強，但有時會顯得不夠專注。',
      'color': Colors.yellow,
    },
    {
      'name': '巨蟹座',
      'symbol': '♋',
      'element': '水象',
      'quality': '基本',
      'dates': '6/21 - 7/22',
      'keywords': ['感性', '家庭', '保護', '直覺'],
      'description': '巨蟹座重視家庭和情感，具有強烈的保護欲和直覺力。他們溫柔體貼，但有時會過於敏感。',
      'color': Colors.blue,
    },
    {
      'name': '獅子座',
      'symbol': '♌',
      'element': '火象',
      'quality': '固定',
      'dates': '7/23 - 8/22',
      'keywords': ['自信', '創造', '表演', '慷慨'],
      'description': '獅子座充滿自信和創造力，喜歡成為注意的焦點。他們慷慨大方，具有領導才能，但有時會顯得自負。',
      'color': Colors.orange,
    },
    {
      'name': '處女座',
      'symbol': '♍',
      'element': '土象',
      'quality': '變動',
      'dates': '8/23 - 9/22',
      'keywords': ['完美', '分析', '服務', '細心'],
      'description': '處女座追求完美，注重細節，具有強烈的分析能力。他們樂於助人，但有時會過於挑剔。',
      'color': Colors.brown,
    },
    {
      'name': '天秤座',
      'symbol': '♎',
      'element': '風象',
      'quality': '基本',
      'dates': '9/23 - 10/22',
      'keywords': ['平衡', '和諧', '美感', '合作'],
      'description': '天秤座追求平衡和和諧，具有優雅的美感和良好的人際關係。他們善於合作，但有時會猶豫不決。',
      'color': Colors.pink,
    },
    {
      'name': '天蠍座',
      'symbol': '♏',
      'element': '水象',
      'quality': '固定',
      'dates': '10/23 - 11/21',
      'keywords': ['深刻', '神秘', '轉化', '專注'],
      'description': '天蠍座深刻而神秘，具有強烈的洞察力和轉化能力。他們專注執著，但有時會顯得過於激烈。',
      'color': Colors.deepPurple,
    },
    {
      'name': '射手座',
      'symbol': '♐',
      'element': '火象',
      'quality': '變動',
      'dates': '11/22 - 12/21',
      'keywords': ['自由', '探索', '哲學', '樂觀'],
      'description': '射手座熱愛自由和探索，具有哲學思維和樂觀態度。他們喜歡冒險，但有時會缺乏耐心。',
      'color': Colors.purple,
    },
    {
      'name': '摩羯座',
      'symbol': '♑',
      'element': '土象',
      'quality': '基本',
      'dates': '12/22 - 1/19',
      'keywords': ['責任', '目標', '實際', '耐心'],
      'description': '摩羯座具有強烈的責任感和目標導向，做事實際而有耐心。他們追求成就，但有時會過於嚴肅。',
      'color': Colors.grey,
    },
    {
      'name': '水瓶座',
      'symbol': '♒',
      'element': '風象',
      'quality': '固定',
      'dates': '1/20 - 2/18',
      'keywords': ['創新', '獨立', '人道', '未來'],
      'description': '水瓶座具有創新精神和獨立思維，關心人道主義和未來發展。他們獨特而前衛，但有時會顯得疏離。',
      'color': Colors.cyan,
    },
    {
      'name': '雙魚座',
      'symbol': '♓',
      'element': '水象',
      'quality': '變動',
      'dates': '2/19 - 3/20',
      'keywords': ['夢想', '同情', '直覺', '藝術'],
      'description': '雙魚座富有想像力和同情心，具有強烈的直覺和藝術天賦。他們溫柔敏感，但有時會過於理想化。',
      'color': Colors.teal,
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          '基礎知識 - 十二星座',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppColors.solarAmber,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Container(
        decoration: BoxDecoration(
          color: AppColors.solarAmber.withValues(alpha: 0.05),
        ),
        child: ResponsivePageWrapper(
          maxWidth: 800.0,
          child: ListView.builder(
            padding: ResponsiveUtils.getResponsivePadding(context),
            itemCount: _zodiacSigns.length,
            itemBuilder: (context, index) {
              final sign = _zodiacSigns[index];
              return Padding(
                padding: const EdgeInsets.only(bottom: 16),
                child: ResponsiveCardWrapper(
                  maxWidth: 600.0,
                  child: _buildZodiacCard(sign),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  /// 構建星座卡片
  Widget _buildZodiacCard(Map<String, dynamic> sign) {
    return StyledCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 星座標題
            Row(
              children: [
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: (sign['color'] as Color).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(30),
                  ),
                  child: Center(
                    child: Text(
                      sign['symbol'],
                      style: TextStyle(
                        fontSize: 28,
                        color: sign['color'],
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        sign['name'],
                        style: const TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: AppColors.textDark,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        sign['dates'],
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // 星座屬性
            Row(
              children: [
                _buildAttributeChip('元素', sign['element'], sign['color']),
                const SizedBox(width: 8),
                _buildAttributeChip('性質', sign['quality'], sign['color']),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // 關鍵詞
            const Text(
              '關鍵特質：',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppColors.textDark,
              ),
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: (sign['keywords'] as List<String>).map((keyword) {
                return Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: (sign['color'] as Color).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: (sign['color'] as Color).withValues(alpha: 0.3),
                    ),
                  ),
                  child: Text(
                    keyword,
                    style: TextStyle(
                      fontSize: 12,
                      color: sign['color'],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                );
              }).toList(),
            ),
            
            const SizedBox(height: 16),
            
            // 描述
            Text(
              sign['description'],
              style: const TextStyle(
                fontSize: 14,
                color: AppColors.textDark,
                height: 1.5,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 構建屬性標籤
  Widget _buildAttributeChip(String label, String value, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(width: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 12,
              color: color,
            ),
          ),
        ],
      ),
    );
  }
}
