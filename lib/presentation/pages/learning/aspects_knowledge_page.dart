import 'package:flutter/material.dart';

import '../../../shared/widgets/common/responsive_wrapper.dart';
import '../../themes/app_theme.dart';

/// 相位知識頁面
class AspectsKnowledgePage extends StatefulWidget {
  const AspectsKnowledgePage({super.key});

  @override
  State<AspectsKnowledgePage> createState() => _AspectsKnowledgePageState();
}

class _AspectsKnowledgePageState extends State<AspectsKnowledgePage> {
  
  /// 相位資料
  final List<Map<String, dynamic>> _aspects = [
    {
      'name': '合相',
      'symbol': '☌',
      'angle': '0°',
      'orb': '±8°',
      'nature': '中性',
      'keywords': ['融合', '強化', '集中', '統一'],
      'description': '合相是最強烈的相位，代表兩個行星的能量完全融合在一起。這個相位會強化行星的特質，但效果取決於涉及的行星性質。',
      'meaning': '合相象徵著新的開始和強烈的能量集中。它可以帶來強大的創造力，但也可能造成能量過度集中的問題。',
      'interpretation': '當兩個行星形成合相時，它們的能量會完全融合，創造出一種全新的能量表達方式。這個相位需要學習如何平衡和整合不同的能量。',
      'color': Colors.black,
    },
    {
      'name': '對分相',
      'symbol': '☍',
      'angle': '180°',
      'orb': '±8°',
      'nature': '挑戰',
      'keywords': ['對立', '平衡', '投射', '整合'],
      'description': '對分相代表兩個行星處於完全對立的位置，創造出緊張和需要平衡的能量。這個相位常常涉及與他人的關係和外在投射。',
      'meaning': '對分相象徵著需要在兩個極端之間找到平衡。它常常通過與他人的關係來學習整合對立的能量。',
      'interpretation': '對分相要求我們學習接納和整合看似對立的特質。這個相位常常通過伙伴關係或競爭對手來反映我們內在的衝突。',
      'color': Colors.red,
    },
    {
      'name': '三分相',
      'symbol': '△',
      'angle': '120°',
      'orb': '±8°',
      'nature': '和諧',
      'keywords': ['和諧', '天賦', '流暢', '創造'],
      'description': '三分相是最和諧的相位之一，代表能量的自然流動和天賦才能。這個相位帶來輕鬆和創造性的表達。',
      'meaning': '三分相象徵著天賦和自然的才能。它代表能量的和諧流動，但有時可能因為太容易而缺乏動力。',
      'interpretation': '三分相帶來天然的才能和和諧的能量流動。這個相位需要主動發展，否則才能可能被浪費。',
      'color': Colors.blue,
    },
    {
      'name': '四分相',
      'symbol': '□',
      'angle': '90°',
      'orb': '±8°',
      'nature': '挑戰',
      'keywords': ['緊張', '動力', '挑戰', '成長'],
      'description': '四分相創造內在緊張和衝突，但也提供成長和發展的動力。這個相位要求我們採取行動來解決問題。',
      'meaning': '四分相象徵著內在的緊張和需要解決的衝突。它提供成長的動力，但需要努力和行動來克服挑戰。',
      'interpretation': '四分相是成長的催化劑，雖然帶來挑戰，但也提供發展新技能和克服困難的機會。',
      'color': Colors.orange,
    },
    {
      'name': '六分相',
      'symbol': '⚹',
      'angle': '60°',
      'orb': '±6°',
      'nature': '和諧',
      'keywords': ['機會', '合作', '溝通', '學習'],
      'description': '六分相提供機會和合作的可能性。這個相位鼓勵溝通、學習和建立有益的連結。',
      'meaning': '六分相象徵著機會和潛在的合作。它需要主動的努力來實現其正面潛力。',
      'interpretation': '六分相提供學習和成長的機會，但需要主動參與和努力才能實現其潛力。',
      'color': Colors.green,
    },
    {
      'name': '八分相',
      'symbol': '∠',
      'angle': '45°',
      'orb': '±3°',
      'nature': '輕微挑戰',
      'keywords': ['調整', '適應', '微調', '改進'],
      'description': '八分相是較輕微的挑戰相位，代表需要小幅調整和適應。它提供改進和精煉的機會。',
      'meaning': '八分相象徵著需要微調和適應的小挑戰。它幫助我們精煉技能和改進表現。',
      'interpretation': '八分相要求我們進行細微的調整和改進，雖然挑戰較小，但對個人發展很重要。',
      'color': Colors.amber,
    },
    {
      'name': '十二分相',
      'symbol': '⚺',
      'angle': '30°',
      'orb': '±2°',
      'nature': '輕微和諧',
      'keywords': ['連結', '支持', '輔助', '細微'],
      'description': '十二分相是最輕微的和諧相位，提供細微的支持和連結。它常常在背景中發揮作用。',
      'meaning': '十二分相象徵著細微但持續的支持。它提供溫和的幫助和連結。',
      'interpretation': '十二分相提供溫和的支持和連結，雖然影響較小，但可以在關鍵時刻提供幫助。',
      'color': Colors.lightBlue,
    },
    {
      'name': '五分相',
      'symbol': 'Q',
      'angle': '72°',
      'orb': '±2°',
      'nature': '創造',
      'keywords': ['創造', '天賦', '藝術', '獨特'],
      'description': '五分相是創造性的相位，與藝術天賦和獨特的表達方式相關。它帶來創新和原創性。',
      'meaning': '五分相象徵著創造性的天賦和獨特的表達能力。它與藝術、創新和原創性相關。',
      'interpretation': '五分相帶來創造性的天賦，特別是在藝術和創新領域。它鼓勵獨特和原創的表達。',
      'color': Colors.purple,
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          '相位知識',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppColors.warning,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppColors.warning.withValues(alpha: 0.1),
              Colors.white,
            ],
            stops: const [0.0, 0.3],
          ),
        ),
        child: ResponsivePageWrapper(
          maxWidth: 800.0,
          child: ListView.builder(
            padding: ResponsiveUtils.getResponsivePadding(context),
            itemCount: _aspects.length,
            itemBuilder: (context, index) {
              final aspect = _aspects[index];
              return Padding(
                padding: const EdgeInsets.only(bottom: 16),
                child: ResponsiveCardWrapper(
                  maxWidth: 600.0,
                  child: _buildAspectCard(aspect),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  /// 構建相位卡片
  Widget _buildAspectCard(Map<String, dynamic> aspect) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 相位標題
            Row(
              children: [
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: (aspect['color'] as Color).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(30),
                    border: Border.all(
                      color: aspect['color'],
                      width: 2,
                    ),
                  ),
                  child: Center(
                    child: Text(
                      aspect['symbol'],
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: aspect['color'],
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        aspect['name'],
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: AppColors.textDark,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Text(
                            aspect['angle'],
                            style: TextStyle(
                              fontSize: 14,
                              color: aspect['color'],
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            '(${aspect['orb']})',
                            style: const TextStyle(
                              fontSize: 12,
                              color: Colors.grey,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                        decoration: BoxDecoration(
                          color: _getNatureColor(aspect['nature']).withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: _getNatureColor(aspect['nature']).withValues(alpha: 0.3),
                          ),
                        ),
                        child: Text(
                          aspect['nature'],
                          style: TextStyle(
                            fontSize: 12,
                            color: _getNatureColor(aspect['nature']),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // 關鍵字標籤
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: (aspect['keywords'] as List<String>).map((keyword) {
                return Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: (aspect['color'] as Color).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: (aspect['color'] as Color).withValues(alpha: 0.3),
                    ),
                  ),
                  child: Text(
                    keyword,
                    style: TextStyle(
                      fontSize: 12,
                      color: aspect['color'],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                );
              }).toList(),
            ),
            
            const SizedBox(height: 16),
            
            // 基本描述
            const Text(
              '基本意義：',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppColors.textDark,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              aspect['description'],
              style: const TextStyle(
                fontSize: 14,
                color: AppColors.textDark,
                height: 1.5,
              ),
            ),
            
            const SizedBox(height: 16),
            
            // 深層意義
            const Text(
              '深層意義：',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppColors.textDark,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              aspect['meaning'],
              style: const TextStyle(
                fontSize: 14,
                color: AppColors.textDark,
                height: 1.5,
              ),
            ),
            
            const SizedBox(height: 16),
            
            // 解讀要點
            const Text(
              '解讀要點：',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppColors.textDark,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              aspect['interpretation'],
              style: const TextStyle(
                fontSize: 14,
                color: AppColors.textDark,
                height: 1.5,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 獲取相位性質對應的顏色
  Color _getNatureColor(String nature) {
    switch (nature) {
      case '和諧':
        return Colors.green;
      case '挑戰':
        return Colors.red;
      case '中性':
        return Colors.grey;
      case '輕微挑戰':
        return Colors.orange;
      case '輕微和諧':
        return Colors.lightGreen;
      case '創造':
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }
}
