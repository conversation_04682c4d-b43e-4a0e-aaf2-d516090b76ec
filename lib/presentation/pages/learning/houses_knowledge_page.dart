import 'package:flutter/material.dart';

import '../../../shared/widgets/common/responsive_wrapper.dart';
import '../../themes/app_theme.dart';

/// 宮位知識頁面
class HousesKnowledgePage extends StatefulWidget {
  const HousesKnowledgePage({super.key});

  @override
  State<HousesKnowledgePage> createState() => _HousesKnowledgePageState();
}

class _HousesKnowledgePageState extends State<HousesKnowledgePage> {
  
  /// 十二宮位資料
  final List<Map<String, dynamic>> _houses = [
    {
      'number': 1,
      'name': '第一宮',
      'title': '命宮 (自我宮)',
      'keywords': ['自我', '外表', '個性', '第一印象'],
      'description': '第一宮代表個人的外在形象、個性特質和生命活力。它影響著我們給他人的第一印象，以及我們如何展現自己。',
      'meaning': '第一宮是整個星盤的起點，代表著「我是誰」的基本問題。它關乎個人的身體外貌、性格特徵和生命態度。',
      'themes': ['個人形象', '身體健康', '生命活力', '自我表達'],
      'color': Colors.red,
    },
    {
      'number': 2,
      'name': '第二宮',
      'title': '財帛宮 (價值宮)',
      'keywords': ['金錢', '價值觀', '物質', '才能'],
      'description': '第二宮掌管個人的財務狀況、價值觀念和物質享受。它顯示我們如何賺錢、花錢，以及對物質的態度。',
      'meaning': '第二宮反映了個人的經濟能力、理財觀念和對安全感的需求。它也代表個人的天賦才能和資源。',
      'themes': ['財務管理', '價值觀念', '物質享受', '天賦才能'],
      'color': Colors.green,
    },
    {
      'number': 3,
      'name': '第三宮',
      'title': '兄弟宮 (溝通宮)',
      'keywords': ['溝通', '學習', '兄弟姊妹', '短途旅行'],
      'description': '第三宮關乎溝通能力、學習方式和與兄弟姊妹的關係。它影響著我們的思維模式和表達能力。',
      'meaning': '第三宮代表日常的溝通交流、基礎教育和鄰里關係。它顯示我們如何處理資訊和與周圍環境的互動。',
      'themes': ['溝通技巧', '學習能力', '兄弟關係', '鄰里互動'],
      'color': Colors.yellow,
    },
    {
      'number': 4,
      'name': '第四宮',
      'title': '田宅宮 (家庭宮)',
      'keywords': ['家庭', '根源', '房產', '內心安全'],
      'description': '第四宮代表家庭背景、根源和內心的安全感。它關乎我們的家庭關係和對「家」的概念。',
      'meaning': '第四宮是星盤的底部，象徵著我們的根基和內在世界。它反映了家庭影響、房產狀況和情感安全。',
      'themes': ['家庭關係', '房產投資', '內心安全', '傳統根源'],
      'color': Colors.blue,
    },
    {
      'number': 5,
      'name': '第五宮',
      'title': '子女宮 (創造宮)',
      'keywords': ['創造', '娛樂', '戀愛', '子女'],
      'description': '第五宮掌管創造力、娛樂活動和浪漫愛情。它顯示我們如何表達創意和享受生活。',
      'meaning': '第五宮代表個人的創造潛能、娛樂偏好和與子女的關係。它關乎自我表達和生活樂趣。',
      'themes': ['創意表達', '娛樂活動', '浪漫愛情', '子女關係'],
      'color': Colors.orange,
    },
    {
      'number': 6,
      'name': '第六宮',
      'title': '奴僕宮 (工作宮)',
      'keywords': ['工作', '健康', '服務', '日常習慣'],
      'description': '第六宮關乎日常工作、健康狀況和服務精神。它影響著我們的工作態度和生活習慣。',
      'meaning': '第六宮代表日常的工作環境、健康管理和對他人的服務。它顯示我們如何維持身心健康和工作效率。',
      'themes': ['工作效率', '健康管理', '服務精神', '日常習慣'],
      'color': Colors.brown,
    },
    {
      'number': 7,
      'name': '第七宮',
      'title': '夫妻宮 (合作宮)',
      'keywords': ['婚姻', '合作', '伙伴', '公開敵人'],
      'description': '第七宮代表婚姻關係、商業合作和一對一的伙伴關係。它顯示我們如何與他人建立平等關係。',
      'meaning': '第七宮是第一宮的對宮，代表「他人」和「關係」。它關乎婚姻、合作和公開的競爭對手。',
      'themes': ['婚姻關係', '商業合作', '法律事務', '公開競爭'],
      'color': Colors.pink,
    },
    {
      'number': 8,
      'name': '第八宮',
      'title': '疾厄宮 (轉化宮)',
      'keywords': ['轉化', '共同資源', '神秘', '生死'],
      'description': '第八宮掌管深層轉化、共同財產和神秘事物。它關乎生死、性愛和心理層面的探索。',
      'meaning': '第八宮代表深層的心理轉化、他人的資源和隱藏的事物。它是最神秘和強烈的宮位之一。',
      'themes': ['心理轉化', '共同財產', '神秘學', '深層探索'],
      'color': Colors.deepPurple,
    },
    {
      'number': 9,
      'name': '第九宮',
      'title': '遷移宮 (哲學宮)',
      'keywords': ['哲學', '高等教育', '遠行', '宗教'],
      'description': '第九宮關乎高等教育、哲學思考和遠距離旅行。它代表我們對真理和智慧的追求。',
      'meaning': '第九宮代表擴展視野的活動，包括高等教育、國外旅行和宗教信仰。它關乎人生哲學和世界觀。',
      'themes': ['高等教育', '哲學思考', '國外旅行', '宗教信仰'],
      'color': Colors.indigo,
    },
    {
      'number': 10,
      'name': '第十宮',
      'title': '官祿宮 (事業宮)',
      'keywords': ['事業', '名聲', '權威', '社會地位'],
      'description': '第十宮代表事業成就、社會地位和公眾形象。它顯示我們在社會中的角色和追求的目標。',
      'meaning': '第十宮是星盤的頂點，象徵著個人的事業高峰和社會成就。它關乎名聲、權威和公眾認知。',
      'themes': ['事業發展', '社會地位', '公眾形象', '權威地位'],
      'color': Colors.teal,
    },
    {
      'number': 11,
      'name': '第十一宮',
      'title': '福德宮 (朋友宮)',
      'keywords': ['朋友', '團體', '希望', '社會理想'],
      'description': '第十一宮掌管友誼關係、團體活動和未來希望。它代表我們的社交圈和社會理想。',
      'meaning': '第十一宮代表友誼、團體歸屬和對未來的希望。它關乎社會網絡、集體活動和人道主義理想。',
      'themes': ['友誼關係', '團體活動', '未來希望', '社會理想'],
      'color': Colors.cyan,
    },
    {
      'number': 12,
      'name': '第十二宮',
      'title': '玄秘宮 (靈性宮)',
      'keywords': ['潛意識', '靈性', '犧牲', '隱藏'],
      'description': '第十二宮關乎潛意識、靈性修行和自我犧牲。它代表隱藏的事物和內在的靈性世界。',
      'meaning': '第十二宮是最神秘的宮位，代表潛意識、靈性覺醒和業力清理。它關乎犧牲、服務和超越自我。',
      'themes': ['潛意識探索', '靈性修行', '自我犧牲', '業力清理'],
      'color': Colors.purple,
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          '宮位知識',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppColors.royalIndigo,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppColors.royalIndigo.withValues(alpha: 0.1),
              Colors.white,
            ],
            stops: const [0.0, 0.3],
          ),
        ),
        child: ResponsivePageWrapper(
          maxWidth: 800.0,
          child: ListView.builder(
            padding: ResponsiveUtils.getResponsivePadding(context),
            itemCount: _houses.length,
            itemBuilder: (context, index) {
              final house = _houses[index];
              return Padding(
                padding: const EdgeInsets.only(bottom: 16),
                child: ResponsiveCardWrapper(
                  maxWidth: 600.0,
                  child: _buildHouseCard(house),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  /// 構建宮位卡片
  Widget _buildHouseCard(Map<String, dynamic> house) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 宮位標題
            Row(
              children: [
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    color: (house['color'] as Color).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(25),
                    border: Border.all(
                      color: house['color'],
                      width: 2,
                    ),
                  ),
                  child: Center(
                    child: Text(
                      house['number'].toString(),
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: house['color'],
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        house['title'],
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: AppColors.textDark,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        house['name'],
                        style: TextStyle(
                          fontSize: 14,
                          color: house['color'],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // 關鍵字標籤
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: (house['keywords'] as List<String>).map((keyword) {
                return Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: (house['color'] as Color).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: (house['color'] as Color).withValues(alpha: 0.3),
                    ),
                  ),
                  child: Text(
                    keyword,
                    style: TextStyle(
                      fontSize: 12,
                      color: house['color'],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                );
              }).toList(),
            ),
            
            const SizedBox(height: 16),
            
            // 基本描述
            const Text(
              '基本意義：',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppColors.textDark,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              house['description'],
              style: const TextStyle(
                fontSize: 14,
                color: AppColors.textDark,
                height: 1.5,
              ),
            ),
            
            const SizedBox(height: 16),
            
            // 深層意義
            const Text(
              '深層意義：',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppColors.textDark,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              house['meaning'],
              style: const TextStyle(
                fontSize: 14,
                color: AppColors.textDark,
                height: 1.5,
              ),
            ),
            
            const SizedBox(height: 16),
            
            // 主要主題
            const Text(
              '主要主題：',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppColors.textDark,
              ),
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: (house['themes'] as List<String>).map((theme) {
                return Container(
                  padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.grey.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Colors.grey.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Text(
                    theme,
                    style: const TextStyle(
                      fontSize: 12,
                      color: AppColors.textDark,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }
}
