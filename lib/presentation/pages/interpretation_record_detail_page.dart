import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../astreal.dart';
import '../../data/services/api/interpretation_record_service.dart';
import '../../data/services/api/interpretation_share_service.dart';
import 'chart_page.dart';

/// 解讀紀錄詳情頁面
class InterpretationRecordDetailPage extends StatefulWidget {
  final InterpretationRecord record;

  const InterpretationRecordDetailPage({
    super.key,
    required this.record,
  });

  @override
  State<InterpretationRecordDetailPage> createState() =>
      _InterpretationRecordDetailPageState();
}

class _InterpretationRecordDetailPageState
    extends State<InterpretationRecordDetailPage> {
  late InterpretationRecord _record;
  bool _isMarkdownMode = true; // 預設使用 Markdown 模式

  @override
  void initState() {
    super.initState();
    _record = widget.record;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_record.title),
        backgroundColor: AppColors.royalIndigo,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.copy),
            onPressed: _copyContent,
            tooltip: '複製內容',
          ),
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: _shareContent,
            tooltip: '分享',
          ),
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    Icon(Icons.delete, color: Colors.red),
                    SizedBox(width: 8),
                    Text('刪除紀錄'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 基本信息卡片
            _buildInfoCard(),

            const SizedBox(height: 0),

            // 解讀內容卡片
            _buildContentCard(),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoCard() {
    return StyledCard(
      elevation: 2,
      child: InkWell(
        onTap: _navigateToChartPage,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text(
                    _record.interpretationTypeIcon,
                    style: const TextStyle(fontSize: 24),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          _record.title,
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: AppColors.textDark,
                          ),
                        ),
                        // const SizedBox(height: 4),
                        // Text(
                        //   _record.interpretationTypeDisplayName,
                        //   style: const TextStyle(
                        //     fontSize: 14,
                        //     color: AppColors.royalIndigo,
                        //     fontWeight: FontWeight.w500,
                        //   ),
                        // ),
                      ],
                    ),
                  ),
                  // 添加查看星盤提示
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: AppColors.solarAmber.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: AppColors.solarAmber.withValues(alpha: 0.3),
                        width: 1,
                      ),
                    ),
                    child: const Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.visibility_rounded,
                          size: 12,
                          color: AppColors.solarAmber,
                        ),
                        SizedBox(width: 4),
                        Text(
                          '查看星盤',
                          style: TextStyle(
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                            color: AppColors.solarAmber,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // 詳細信息
              _buildInfoRow(
                icon: Icons.access_time,
                label: '創建時間',
                value:
                    '${_record.createdAt.year}/${_record.createdAt.month.toString().padLeft(2, '0')}/${_record.createdAt.day.toString().padLeft(2, '0')} ${_record.createdAt.hour.toString().padLeft(2, '0')}:${_record.createdAt.minute.toString().padLeft(2, '0')}',
              ),
              const SizedBox(height: 8),
              _buildInfoRow(
                icon: Icons.person,
                label: '主要人物',
                value: _record.primaryPersonName,
              ),
              if (_record.secondaryPersonName != null) ...[
                const SizedBox(height: 8),
                _buildInfoRow(
                  icon: Icons.people,
                  label: '次要人物',
                  value: _record.secondaryPersonName!,
                ),
              ],
              const SizedBox(height: 8),
              _buildInfoRow(
                icon: Icons.category,
                label: '星盤類型',
                value: _record.chartType,
              ),
              if (_record.specificDate != null) ...[
                const SizedBox(height: 8),
                _buildInfoRow(
                  icon: Icons.calendar_today,
                  label: '特定日期',
                  value:
                      '${_record.specificDate!.year}/${_record.specificDate!.month.toString().padLeft(2, '0')}/${_record.specificDate!.day.toString().padLeft(2, '0')}',
                ),
              ],
              if (_record.tags != null) ...[
                const SizedBox(height: 12),
                Wrap(
                  spacing: 6,
                  runSpacing: 6,
                  children: _record.tags!
                      .map((tag) => Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color:
                                  AppColors.solarAmber.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              tag,
                              style: const TextStyle(
                                fontSize: 10,
                                color: AppColors.solarAmber,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ))
                      .toList(),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoRow({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          icon,
          size: 16,
          color: Colors.grey.shade600,
        ),
        const SizedBox(width: 8),
        Text(
          '$label：',
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey.shade600,
            fontWeight: FontWeight.w500,
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              color: AppColors.textDark,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildContentCard() {
    return StyledCard(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(
                  Icons.auto_awesome,
                  color: AppColors.solarAmber,
                  size: 20,
                ),
                SizedBox(width: 8),
                Text(
                  '解讀內容',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textDark,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildContentDisplay(),
          ],
        ),
      ),
    );
  }

  void _copyContent() {
    // 移除解讀內容中的 Markdown 格式，方便分享給其他人
    final cleanContent = _removeMarkdownFormatting(_record.content);

    final content = '''
${_record.title}

創建時間：${_record.createdAt.year}/${_record.createdAt.month.toString().padLeft(2, '0')}/${_record.createdAt.day.toString().padLeft(2, '0')} ${_record.createdAt.hour.toString().padLeft(2, '0')}:${_record.createdAt.minute.toString().padLeft(2, '0')}
主要人物：${_record.primaryPersonName}
${_record.secondaryPersonName != null ? '次要人物：${_record.secondaryPersonName}\n' : ''}星盤類型：${_record.chartType}

解讀內容：
$cleanContent
''';

    Clipboard.setData(ClipboardData(text: content));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('解讀內容已複製到剪貼板（已移除格式）'),
        backgroundColor: AppColors.royalIndigo,
      ),
    );
  }

  /// 分享解讀內容
  Future<void> _shareContent() async {
    // 使用共用的分享服務
    await InterpretationShareService.shareInterpretation(
      context: context,
      interpretation: widget.record.content,
      title: widget.record.title,
      personName: widget.record.primaryPersonName.isNotEmpty
          ? widget.record.primaryPersonName
          : null,
      secondaryPersonName: widget.record.secondaryPersonName?.isNotEmpty == true
          ? widget.record.secondaryPersonName
          : null,
      interpretationType: widget.record.interpretationType,
    );
  }

  /// 分享解讀內容（別名方法）
  Future<void> _shareInterpretation() async {
    await _shareContent();
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'delete':
        _showDeleteDialog();
        break;
    }
  }

  void _showDeleteDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('刪除紀錄'),
        content: const Text('確定要刪除這條解讀紀錄嗎？此操作無法復原。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _deleteRecord();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('刪除'),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteRecord() async {
    try {
      await InterpretationRecordService.deleteRecord(_record.id);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('紀錄已刪除'),
            backgroundColor: AppColors.royalIndigo,
          ),
        );
        Navigator.of(context).pop(); // 返回上一頁
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('刪除失敗: $e')),
        );
      }
    }
  }

  /// 構建內容顯示
  Widget _buildContentDisplay() {
    if (_isMarkdownMode) {
      return MarkdownBody(
        data: _record.content,
        styleSheet: MarkdownStyleSheet(
          p: const TextStyle(
            fontSize: 15,
            height: 1.6,
            color: AppColors.textDark,
          ),
          h1: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: AppColors.royalIndigo,
            height: 1.4,
          ),
          h2: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppColors.royalIndigo,
            height: 1.4,
          ),
          h3: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: AppColors.textDark,
            height: 1.4,
          ),
          strong: const TextStyle(
            fontWeight: FontWeight.bold,
            color: AppColors.textDark,
          ),
          em: const TextStyle(
            fontStyle: FontStyle.italic,
            color: AppColors.textDark,
          ),
          blockquote: TextStyle(
            fontSize: 14,
            fontStyle: FontStyle.italic,
            color: Colors.grey.shade600,
            backgroundColor: Colors.grey.shade100,
          ),
          code: TextStyle(
            fontSize: 13,
            fontFamily: 'monospace',
            backgroundColor: Colors.grey.shade200,
            color: AppColors.textDark,
          ),
          listBullet: const TextStyle(
            fontSize: 15,
            color: AppColors.royalIndigo,
          ),
        ),
        onTapLink: (text, href, title) {
          if (href != null) {
            _launchUrl(href);
          }
        },
      );
    } else {
      return Text(
        _record.content,
        style: const TextStyle(
          fontSize: 15,
          height: 1.6,
          color: AppColors.textDark,
        ),
      );
    }
  }

  /// 切換顯示模式
  void _toggleDisplayMode() {
    setState(() {
      _isMarkdownMode = !_isMarkdownMode;
    });
  }

  /// 導航到星盤頁面
  void _navigateToChartPage() {
    try {
      // 嘗試重建 ChartData 對象
      final chartData = _record.rebuildChartData();

      if (chartData == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('此解讀紀錄沒有完整的星盤數據，無法顯示星盤'),
            backgroundColor: Colors.orange,
            duration: Duration(seconds: 3),
          ),
        );
        return;
      }

      // 導航到星盤頁面
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => ChangeNotifierProvider(
            create: (context) => ChartViewModel.withChartData(
              initialChartData: chartData,
            ),
            child: const ChartPage(),
          ),
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('無法打開星盤頁面：$e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// 啟動 URL
  Future<void> _launchUrl(String url) async {
    final uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('無法打開連結: $url')),
        );
      }
    }
  }

  /// 移除 Markdown 格式，方便分享給其他人
  String _removeMarkdownFormatting(String content) {
    // 移除 Markdown 標記，但保留內容
    String cleaned = content;

    // 先移除代碼塊（包含內容）
    final lines = cleaned.split('\n');
    final filteredLines = <String>[];
    bool inCodeBlock = false;

    for (final line in lines) {
      if (line.trim().startsWith('```')) {
        inCodeBlock = !inCodeBlock;
        continue; // 跳過代碼塊標記行
      }
      if (!inCodeBlock) {
        filteredLines.add(line);
      }
    }

    cleaned = filteredLines.join('\n');

    // 處理粗體：**text** -> text
    cleaned = cleaned.replaceAllMapped(RegExp(r'\*\*(.*?)\*\*'), (match) => match.group(1) ?? '');

    // 處理斜體：*text* -> text
    cleaned = cleaned.replaceAllMapped(RegExp(r'\*(.*?)\*'), (match) => match.group(1) ?? '');

    // 移除標題：# -> 空
    cleaned = cleaned.replaceAll(RegExp(r'#{1,6}\s*'), '');

    // 處理連結：[text](url) -> text
    cleaned = cleaned.replaceAllMapped(RegExp(r'\[([^\]]+)\]\([^)]+\)'), (match) => match.group(1) ?? '');

    // 處理行內代碼：`code` -> code
    cleaned = cleaned.replaceAllMapped(RegExp(r'`([^`]+)`'), (match) => match.group(1) ?? '');

    // 列表項目：- item -> • item
    cleaned = cleaned.replaceAll(RegExp(r'^\s*[-*+]\s+', multiLine: true), '• ');

    // 數字列表：1. item -> item
    cleaned = cleaned.replaceAll(RegExp(r'^\s*\d+\.\s+', multiLine: true), '');

    // 多餘的換行：最多保留兩個
    cleaned = cleaned.replaceAll(RegExp(r'\n{3,}'), '\n\n');

    return cleaned.trim();
  }
}
