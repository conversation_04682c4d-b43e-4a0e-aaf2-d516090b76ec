import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../../data/models/astrology/chart_type.dart';
import '../../../data/models/user/birth_data.dart';
import '../../../presentation/themes/app_theme.dart';
import '../../shared/widgets/common/responsive_wrapper.dart';
import '../pages/chart_selection_page.dart';

/// 星盤類型選擇頁面
class ChartTypesPage extends StatefulWidget {
  final BirthData? selectedPerson;

  const ChartTypesPage({
    super.key,
    this.selectedPerson,
  });

  @override
  State<ChartTypesPage> createState() => _ChartTypesPageState();
}

class _ChartTypesPageState extends State<ChartTypesPage> {
  String _selectedCategory = '全部';
  final Set<ChartType> _expandedItems = <ChartType>{};

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: const Text(
          '星盤分析',
          style: TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 20,
            color: AppColors.textDark,
            letterSpacing: 0.3,
          ),
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
        iconTheme: const IconThemeData(color: AppColors.textDark),
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.dark,
        ),
      ),
      body: ResponsivePageWrapper(
        maxWidth: 900.0, // 星盤類型頁面適合中等寬度
        child: Column(
          children: [
            // 分類選擇器
            _buildCategorySelector(),

            // 星盤類型列表
            Expanded(
              child: _buildChartTypesList(),
            ),
          ],
        ),
      ),
    );
  }

  /// 構建分類選擇器
  Widget _buildCategorySelector() {
    final categories = [
      '全部',
      '個人星盤',
      '預測類星盤',
      '返照盤類',
      '合盤類',
      '合盤預測類',
      '特殊星盤',
      '天象盤',
    ];

    return Container(
      height: 56,
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        itemCount: categories.length,
        itemBuilder: (context, index) {
          final category = categories[index];
          final isSelected = _selectedCategory == category;

          return GestureDetector(
            onTap: () {
              setState(() {
                _selectedCategory = category;
                // 清除展開狀態
                _expandedItems.clear();
              });
            },
            child: Container(
              margin: const EdgeInsets.only(right: 12),
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
              decoration: BoxDecoration(
                color: isSelected ? AppColors.royalIndigo : Colors.grey[50],
                borderRadius: BorderRadius.circular(24),
                border: Border.all(
                  color: isSelected ? AppColors.royalIndigo : Colors.grey[200]!,
                  width: 1.5,
                ),
                boxShadow: isSelected ? [
                  BoxShadow(
                    color: AppColors.royalIndigo.withValues(alpha: 0.2),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ] : null,
              ),
              child: Center(
                child: Text(
                  category,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                    color: isSelected ? Colors.white : AppColors.textDark,
                    letterSpacing: 0.2,
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  /// 構建星盤類型列表
  Widget _buildChartTypesList() {
    final filteredChartTypes = _getFilteredChartTypes();
    
    return ListView.builder(
      padding: const EdgeInsets.fromLTRB(16, 20, 16, 16),
      itemCount: filteredChartTypes.length,
      itemBuilder: (context, index) {
        final chartType = filteredChartTypes[index];
        return _buildChartTypeItem(chartType);
      },
    );
  }

  /// 獲取過濾後的星盤類型
  List<ChartType> _getFilteredChartTypes() {
    List<ChartType> allChartTypes = List.from(ChartType.values);
    allChartTypes.remove(ChartType.profection);
    allChartTypes.remove(ChartType.horary);
    allChartTypes.remove(ChartType.event);
    allChartTypes.remove(ChartType.conjunctionJupiterSaturn);
    allChartTypes.remove(ChartType.conjunctionMarsSaturn);

    if (_selectedCategory == '全部') {
      return allChartTypes;
    }
    
    return allChartTypes.where((chartType) {
      switch (_selectedCategory) {
        case '天象盤':
          return chartType == ChartType.mundane;
        case '個人星盤':
          return chartType == ChartType.natal;
        case '預測類星盤':
          return chartType.isPredictiveChart;
        case '返照盤類':
          return chartType.isReturnChart;
        case '合盤類':
          return chartType.isRelationshipChart;
        case '合盤預測類':
          return chartType.isRelationshipPredictiveChart;
        // case '事件占星':
        //   return chartType.isEventChart;
        case '特殊星盤':
          return chartType.isSpecialChart || 
                 chartType == ChartType.equinoxSolstice ||
                 chartType == ChartType.conjunctionJupiterSaturn ||
                 chartType == ChartType.conjunctionMarsSaturn;
        default:
          return true;
      }
    }).toList();
  }

  /// 構建星盤類型項目
  Widget _buildChartTypeItem(ChartType chartType) {
    final info = _getChartTypeInfo(chartType);

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.06),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _navigateToChartSelection(chartType),
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 圖標容器
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: info['color'].withValues(alpha: 0.12),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    info['icon'],
                    color: info['color'],
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),

                // 內容區域
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 標題
                      Text(
                        chartType.displayName,
                        style: const TextStyle(
                          fontSize: 17,
                          fontWeight: FontWeight.w600,
                          color: AppColors.textDark,
                          letterSpacing: 0.2,
                        ),
                      ),
                      const SizedBox(height: 4),

                      // 英文名稱
                      Text(
                        chartType.englishName,
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[500],
                          fontStyle: FontStyle.italic,
                          letterSpacing: 0.1,
                        ),
                      ),
                      const SizedBox(height: 8),

                      // 描述 - 可展開
                      _buildExpandableDescription(chartType.description),
                    ],
                  ),
                ),

                // 箭頭圖標
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: info['color'].withValues(alpha: 0.08),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.arrow_forward_ios,
                    color: info['color'],
                    size: 14,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 構建可展開的描述
  Widget _buildExpandableDescription(String description) {
    // 判斷描述是否過長（超過80個字符）
    final isLongDescription = description.length > 80;
    final shouldShowExpandButton = isLongDescription;

    return StatefulBuilder(
      builder: (context, setState) {
        final chartType = ChartType.values.firstWhere(
          (type) => type.description == description,
          orElse: () => ChartType.natal,
        );
        final isExpanded = _expandedItems.contains(chartType);

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            AnimatedCrossFade(
              duration: const Duration(milliseconds: 200),
              crossFadeState: isExpanded || !shouldShowExpandButton
                  ? CrossFadeState.showSecond
                  : CrossFadeState.showFirst,
              firstChild: Text(
                description,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                  height: 1.4,
                  letterSpacing: 0.1,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              secondChild: Text(
                description,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                  height: 1.4,
                  letterSpacing: 0.1,
                ),
              ),
            ),
            if (shouldShowExpandButton) ...[
              const SizedBox(height: 8),
              GestureDetector(
                onTap: () {
                  setState(() {
                    if (isExpanded) {
                      _expandedItems.remove(chartType);
                    } else {
                      _expandedItems.add(chartType);
                    }
                  });
                },
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      isExpanded ? '收起' : '展開',
                      style: TextStyle(
                        fontSize: 12,
                        color: AppColors.royalIndigo,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(width: 4),
                    Icon(
                      isExpanded ? Icons.expand_less : Icons.expand_more,
                      color: AppColors.royalIndigo,
                      size: 16,
                    ),
                  ],
                ),
              ),
            ],
          ],
        );
      },
    );
  }

  /// 獲取星盤類型資訊
  Map<String, dynamic> _getChartTypeInfo(ChartType chartType) {
    switch (chartType) {
      case ChartType.natal:
        return {'color': const Color(0xFF6366F1), 'icon': Icons.person};
      case ChartType.transit:
        return {'color': const Color(0xFF10B981), 'icon': Icons.timeline};
      case ChartType.secondaryProgression:
        return {'color': const Color(0xFF8B5CF6), 'icon': Icons.trending_up};
      case ChartType.tertiaryProgression:
        return {'color': const Color(0xFFF59E0B), 'icon': Icons.show_chart};
      case ChartType.solarArcDirection:
        return {'color': const Color(0xFFEF4444), 'icon': Icons.wb_sunny};
      case ChartType.solarReturn:
        return {'color': const Color(0xFFF59E0B), 'icon': Icons.wb_sunny};
      case ChartType.lunarReturn:
        return {'color': const Color(0xFF6366F1), 'icon': Icons.brightness_2};
      case ChartType.synastry:
      case ChartType.synastrySecondary:
      case ChartType.synastryTertiary:
        return {'color': const Color(0xFFEC4899), 'icon': Icons.favorite};
      case ChartType.composite:
      case ChartType.compositeSecondary:
      case ChartType.compositeTertiary:
        return {'color': const Color(0xFF8B5CF6), 'icon': Icons.group};
      case ChartType.davison:
      case ChartType.davisonSecondary:
      case ChartType.davisonTertiary:
        return {'color': const Color(0xFF06B6D4), 'icon': Icons.merge_type};
      case ChartType.marks:
      case ChartType.marksSecondary:
      case ChartType.marksTertiary:
        return {'color': const Color(0xFF84CC16), 'icon': Icons.psychology};
      case ChartType.horary:
        return {'color': const Color(0xFF6366F1), 'icon': Icons.help_outline};
      case ChartType.event:
        return {'color': const Color(0xFFEF4444), 'icon': Icons.event};
      case ChartType.mundane:
        return {'color': const Color(0xFF059669), 'icon': Icons.public};
      case ChartType.firdaria:
        return {'color': const Color(0xFF7C3AED), 'icon': Icons.hourglass_full};
      case ChartType.profection:
        return {'color': const Color(0xFFDC2626), 'icon': Icons.rotate_right};
      case ChartType.eclipse:
        return {'color': const Color(0xFF1F2937), 'icon': Icons.brightness_2};
      case ChartType.equinoxSolstice:
        return {'color': const Color(0xFFF59E0B), 'icon': Icons.wb_twilight};
      case ChartType.conjunctionJupiterSaturn:
        return {'color': const Color(0xFF6366F1), 'icon': Icons.join_inner};
      case ChartType.conjunctionMarsSaturn:
        return {'color': const Color(0xFFEF4444), 'icon': Icons.join_full};
    }
  }

  /// 導航到星盤選擇頁面
  void _navigateToChartSelection(ChartType chartType) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ChartSelectionPage(
          initialChartType: chartType,
          primaryPerson: widget.selectedPerson,
        ),
      ),
    );
  }
}
