import 'package:flutter/material.dart';

import '../../data/services/api/location_service.dart';
import '../../shared/widgets/common/responsive_wrapper.dart';

class LocationPickerPage extends StatefulWidget {
  const LocationPickerPage({super.key});

  @override
  State<LocationPickerPage> createState() => _LocationPickerPageState();
}

class _LocationPickerPageState extends State<LocationPickerPage> {
  final _formKey = GlobalKey<FormState>();
  final _locationController = TextEditingController();
  bool _isLoading = false;

  Future<void> _searchLocation() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final result =
          await LocationService.getLocationFromName(_locationController.text);
      if (mounted) {
        Navigator.pop(context, result);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('搜尋地點時出錯：$e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _locationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('選擇地點'),
      ),
      body: ResponsiveFormWrapper(
        maxWidth: 500.0, // 地點選擇表單適合較小寬度
        child: Padding(
          padding: ResponsiveUtils.getResponsivePadding(context),
          child: Form(
            key: _formKey,
            child: Column(
              children: [
                TextFormField(
                  controller: _locationController,
                  decoration: const InputDecoration(
                    labelText: '輸入地點名稱',
                    hintText: '例如：台北市',
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return '請輸入地點名稱';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                ElevatedButton.icon(
                  onPressed: _isLoading ? null : _searchLocation,
                  icon: _isLoading
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Icon(Icons.search),
                  label: Text(_isLoading ? '搜尋中...' : '搜尋'),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
