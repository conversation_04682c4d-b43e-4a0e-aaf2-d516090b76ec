import 'package:flutter/material.dart';

import '../../astreal.dart';
import '../../data/services/api/astro_calendar_service.dart';
import '../../shared/utils/geocoding_service.dart';
import '../../shared/widgets/common/responsive_wrapper.dart';
import 'eclipse_results_page.dart';

class EclipseSetupPage extends StatefulWidget {
  final BirthData? natalPerson;

  const EclipseSetupPage({
    super.key,
    this.natalPerson,
  });

  @override
  State<EclipseSetupPage> createState() => _EclipseSetupPageState();
}

class _EclipseSetupPageState extends State<EclipseSetupPage> {
  // 地點輸入控制器
  final _locationController = TextEditingController();

  // 選擇的年份
  int _selectedYear = DateTime.now().year;

  // 選擇的地點
  double? _selectedLatitude;
  double? _selectedLongitude;
  String? _locationError; // 地點錯誤訊息

  // 篩選類型
  String _selectedFilter = '全部'; // 全部、日蝕、月蝕

  // 載入狀態
  bool _isLoading = false;
  bool _isLocationLoading = false;

  @override
  void initState() {
    super.initState();
    // 設定預設地點為台北市
    _locationController.text = '台北市';
    _selectedLatitude = 25.0330;
    _selectedLongitude = 121.5654;
  }

  @override
  void dispose() {
    _locationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('日月蝕盤設定'),
        backgroundColor: AppColors.indigoSurface,
        foregroundColor: Colors.white,
      ),
      body: SafeArea(
        child: ResponsiveFormWrapper(
          maxWidth: 600.0, // 日月蝕盤設定頁面適合表單寬度
          child: Column(
            children: [
              // 設定區域
              Expanded(
                child: SingleChildScrollView(
                  padding: ResponsiveUtils.getResponsivePadding(context),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildYearSelector(),
                      // const SizedBox(height: 16),
                      _buildLocationSelector(),
                      // const SizedBox(height: 16),
                      _buildFilterSelector(),
                      const SizedBox(height: 16),
                      _buildSearchButton(),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 構建年份選擇器
  Widget _buildYearSelector() {
    return StyledCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.calendar_today, color: AppColors.indigoSurface,),
                SizedBox(width: 8),
                Text(
                  '選擇年份',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(8),
              ),
              child: DropdownButtonHideUnderline(
                child: DropdownButton<int>(
                  value: _selectedYear,
                  isExpanded: true,
                  items: List.generate(2000, (index) {
                    final year = DateTime.now().year - 1000 + index;
                    return DropdownMenuItem<int>(
                      value: year,
                      child: Text('$year年'),
                    );
                  }),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _selectedYear = value;
                      });
                    }
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 構建地點選擇器
  Widget _buildLocationSelector() {
    return StyledCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.location_on, color: AppColors.indigoSurface),
                SizedBox(width: 8),
                Text(
                  '觀測地點',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),

            // 地點輸入框
            Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: AppColors.indigoSurface.withValues(alpha: 0.3),
                ),
                color: Colors.grey[50],
              ),
              child: TextFormField(
                controller: _locationController,
                decoration: InputDecoration(
                  hintText: '請輸入觀測地點（例：台北市中正區）',
                  hintStyle: TextStyle(
                    color: Colors.grey[400],
                    fontSize: 14,
                  ),
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 16,
                  ),
                  prefixIcon: Icon(
                    Icons.location_on_outlined,
                    color: AppColors.indigoSurface.withValues(alpha: 0.7),
                    size: 20,
                  ),
                  suffixIcon: _isLocationLoading
                      ? Container(
                          margin: const EdgeInsets.all(12),
                          width: 20,
                          height: 20,
                          child: const CircularProgressIndicator(
                            strokeWidth: 2,
                          ),
                        )
                      : Container(
                          margin: const EdgeInsets.only(right: 8),
                          child: IconButton(
                            icon: Icon(
                              Icons.my_location,
                              color: AppColors.solarAmber.withValues(alpha: 0.8),
                              size: 18,
                            ),
                            onPressed: _getCurrentLocation,
                            tooltip: '取得當前位置',
                          ),
                        ),
                ),
                onChanged: (value) async {
                  // 當用戶輸入地址時，嘗試獲取經緯度
                  if (value.isNotEmpty && value.length > 1) {
                    setState(() {
                      _isLocationLoading = true;
                    });

                    try {
                      final coordinates = await GeocodingService.getCoordinatesFromAddress(value);
                      if (mounted) {
                        setState(() {
                          _selectedLatitude = coordinates['latitude'];
                          _selectedLongitude = coordinates['longitude'];
                          _locationError = null; // 清除錯誤訊息
                          _isLocationLoading = false;
                        });
                      }
                    } catch (e) {
                      if (mounted) {
                        setState(() {
                          _selectedLatitude = null;
                          _selectedLongitude = null;
                          _locationError = e.toString(); // 保存錯誤訊息
                          _isLocationLoading = false;
                        });
                      }
                    }
                  } else {
                    // 如果輸入太短，清除經緯度
                    setState(() {
                      _selectedLatitude = null;
                      _selectedLongitude = null;
                    });
                  }
                },
              ),
            ),

            // 錯誤訊息顯示
            if (_locationError != null) ...[
              const SizedBox(height: 8),
              _buildLocationError(),
            ],

            // 經緯度資訊顯示
            if (_selectedLatitude != null && _selectedLongitude != null) ...[
              const SizedBox(height: 8),
              _buildLocationInfo(),
            ],
          ],
        ),
      ),
    );
  }

  /// 構建篩選類型選擇器
  Widget _buildFilterSelector() {
    return StyledCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.filter_list, color: AppColors.indigoSurface),
                SizedBox(width: 8),
                Text(
                  '蝕相類型',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildFilterChip('全部', Icons.brightness_4),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildFilterChip('日蝕', Icons.wb_sunny),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildFilterChip('月蝕', Icons.brightness_2),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 構建篩選選項
  Widget _buildFilterChip(String label, IconData icon) {
    final isSelected = _selectedFilter == label;
    return InkWell(
      onTap: () {
        setState(() {
          _selectedFilter = label;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.indigoSurface : Colors.grey.shade100,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected ? AppColors.indigoSurface : Colors.grey.shade300,
          ),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: isSelected ? Colors.white : Colors.grey.shade600,
              size: 24,
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                color: isSelected ? Colors.white : Colors.grey.shade700,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 構建查詢按鈕
  Widget _buildSearchButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _isLoading ? null : _searchEclipses,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.indigoSurface,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: _isLoading
            ? const SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : const Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.search),
                  SizedBox(width: 8),
                  Text(
                    '查詢日月蝕盤',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
      ),
    );
  }

  /// 構建地點錯誤訊息顯示
  Widget _buildLocationError() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.red.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.red.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.error_outline,
            color: Colors.red[600],
            size: 16,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              _locationError!,
              style: TextStyle(
                fontSize: 12,
                color: Colors.red[700],
              ),
            ),
          ),
          TextButton(
            onPressed: () {
              setState(() {
                _locationError = null;
              });
            },
            child: Text(
              '關閉',
              style: TextStyle(
                fontSize: 12,
                color: Colors.red[600],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 構建位置資訊顯示
  Widget _buildLocationInfo() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.blue.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                Icons.location_on,
                color: Colors.blue[600],
                size: 16,
              ),
              const SizedBox(width: 8),
              Text(
                '地理位置資訊',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue[700],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: Row(
                  children: [
                    Icon(
                      Icons.my_location,
                      color: Colors.grey[600],
                      size: 14,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '緯度: ${_selectedLatitude!.toStringAsFixed(4)}°',
                      style: TextStyle(
                        fontSize: 11,
                        color: Colors.grey[700],
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Row(
                  children: [
                    Icon(
                      Icons.explore,
                      color: Colors.grey[600],
                      size: 14,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '經度: ${_selectedLongitude!.toStringAsFixed(4)}°',
                      style: TextStyle(
                        fontSize: 11,
                        color: Colors.grey[700],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 獲取當前位置
  Future<void> _getCurrentLocation() async {
    setState(() {
      _isLocationLoading = true;
    });

    try {
      // 獲取當前位置的經緯度
      final coordinates = await GeocodingService.getCurrentLocation();

      if (coordinates == null) {
        if (mounted) {
          setState(() {
            _locationError = '無法獲取當前位置，請確保已開啟位置服務和權限';
            _isLocationLoading = false;
          });
        }
        return;
      }

      // 嘗試將經緯度轉換為地址
      final address = await GeocodingService.getAddressFromCoordinates(
        coordinates['latitude']!,
        coordinates['longitude']!,
      );

      if (address != null && mounted) {
        setState(() {
          _locationController.text = address;
          _selectedLatitude = coordinates['latitude'];
          _selectedLongitude = coordinates['longitude'];
          _locationError = null; // 清除錯誤訊息
          _isLocationLoading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('已獲取當前位置: $address'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        // 如果無法獲取地址，但有經緯度，則直接使用經緯度字符串
        if (mounted) {
          final locationStr =
              '緯度: ${coordinates['latitude']!.toStringAsFixed(6)}, 經度: ${coordinates['longitude']!.toStringAsFixed(6)}';
          setState(() {
            _locationController.text = locationStr;
            _selectedLatitude = coordinates['latitude'];
            _selectedLongitude = coordinates['longitude'];
            _locationError = null; // 清除錯誤訊息
            _isLocationLoading = false;
          });

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('無法獲取地址名稱，已使用經緯度: $locationStr'),
              backgroundColor: Colors.orange,
            ),
          );
        }
      }
    } catch (e) {
      setState(() {
        _locationError = '獲取位置失敗: ${e.toString()}';
        _isLocationLoading = false;
      });
    }
  }

  /// 查詢日月蝕事件
  void _searchEclipses() async {
    // 檢查是否有有效的地點資訊
    if (_selectedLatitude == null || _selectedLongitude == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('請先輸入有效的觀測地點'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // 使用 AstroCalendarService 查詢日月蝕事件
      final allEvents = await AstroCalendarService().getYearEclipseEvents(
        _selectedYear,
        latitude: _selectedLatitude!,
        longitude: _selectedLongitude!,
      );

      // 篩選日月蝕事件
      List<AstroEvent> eclipses = allEvents.where((event) {
        final isEclipse = event.type == AstroEventType.eclipse;

        if (!isEclipse) return false;

        switch (_selectedFilter) {
          case '日蝕':
            return event.title.contains('日') || event.title.contains('Solar');
          case '月蝕':
            return event.title.contains('月') || event.title.contains('Lunar');
          default:
            return true;
        }
      }).toList();

      // 按時間排序
      eclipses.sort((a, b) => a.dateTime.compareTo(b.dateTime));

      setState(() {
        _isLoading = false;
      });

      // 導航到結果頁面
      if (mounted) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => EclipseResultsPage(
              eclipseResults: eclipses,
              selectedYear: _selectedYear,
              selectedLocation: _locationController.text,
              selectedLatitude: _selectedLatitude!,
              selectedLongitude: _selectedLongitude!,
              selectedFilter: _selectedFilter,
              natalPerson: widget.natalPerson,
            ),
          ),
        );
      }

    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('查詢失敗: ${e.toString()}'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }
}
