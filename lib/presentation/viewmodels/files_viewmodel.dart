import 'package:clipboard/clipboard.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../astreal.dart';
import '../../data/services/api/birth_data_service.dart';
import '../../shared/utils/csv_helper.dart';
import '../../shared/utils/geocoding_service.dart';

/// 排序方式
enum SortOption {
  nameAsc,
  nameDesc,
  dateNewest,
  dateOldest,
  createdNewest,  // 建立時間（最新）
  createdOldest,  // 建立時間（最舊）
}

/// 檔案頁面的 ViewModel
class FilesViewModel extends ChangeNotifier {
  // 出生資料列表
  List<BirthData> _birthDataList = [];
  List<BirthData> _filteredList = [];
  bool _isLoading = true;

  // 多選模式相關
  bool _isMultiSelectMode = false;
  Set<String> _selectedItems = {};

  // 搜索相關
  final TextEditingController searchController = TextEditingController();

  // 排序相關
  SortOption _currentSortOption = SortOption.createdNewest;

  // 過濾相關
  Set<ChartCategory> _categoryFilter = Set.from(ChartCategory.values);

  // 複製相關
  bool _isCopying = false;

  // CSV 匯入相關
  bool _isImporting = false;
  String _importStatus = '';
  int _importProgress = 0;
  int _importTotal = 0;

  // Getters
  List<BirthData> get birthDataList => _birthDataList;
  List<BirthData> get filteredList => _filteredList;
  bool get isLoading => _isLoading;
  bool get isMultiSelectMode => _isMultiSelectMode;
  Set<String> get selectedItems => _selectedItems;

  SortOption get currentSortOption => _currentSortOption;
  Set<ChartCategory> get categoryFilter => _categoryFilter;
  bool get isCopying => _isCopying;

  // CSV 匯入相關 getters
  bool get isImporting => _isImporting;
  String get importStatus => _importStatus;
  int get importProgress => _importProgress;
  int get importTotal => _importTotal;
  double get importProgressPercent => _importTotal > 0 ? _importProgress / _importTotal : 0.0;

  // Constructor
  FilesViewModel() {
    _initAsync();
  }

  // 非同步初始化
  void _initAsync() {
    _init().then((_) {
      // 初始化完成
    }).catchError((error) {
      logger.e('初始化錯誤: $error');
    });
  }

  Future<void> _init() async {
    // 從 SharedPreferences 加載排序選項
    await _loadSortOption();

    loadBirthData();
    searchController.addListener(_filterBirthData);
  }

  // 從 SharedPreferences 加載排序選項
  Future<void> _loadSortOption() async {
    final prefs = await SharedPreferences.getInstance();
    final sortOptionIndex = prefs.getInt('sortOption');

    if (sortOptionIndex != null && sortOptionIndex < SortOption.values.length) {
      _currentSortOption = SortOption.values[sortOptionIndex];
    }
  }

  @override
  void dispose() {
    searchController.removeListener(_filterBirthData);
    searchController.dispose();
    super.dispose();
  }

  // 根據搜索文本和類別過濾出生資料
  void _filterBirthData() {
    final query = searchController.text.toLowerCase();

    // 先根據類別過濾
    List<BirthData> categoryFiltered = _birthDataList.where((data) {
      return _categoryFilter.contains(data.category);
    }).toList();

    // 再根據搜索文本過濾
    if (query.isEmpty) {
      _filteredList = categoryFiltered;
    } else {
      _filteredList = categoryFiltered.where((data) {
        return data.name.toLowerCase().contains(query) ||
            data.birthPlace.toLowerCase().contains(query) ||
            (data.notes != null && data.notes!.toLowerCase().contains(query));
      }).toList();
    }

    _sortBirthData();
    notifyListeners();
  }

  // 排序出生資料
  void _sortBirthData() {
    switch (_currentSortOption) {
      case SortOption.nameAsc:
        _filteredList.sort((a, b) => a.name.compareTo(b.name));
        break;
      case SortOption.nameDesc:
        _filteredList.sort((a, b) => b.name.compareTo(a.name));
        break;
      case SortOption.dateNewest:
        _filteredList.sort((a, b) => b.dateTime.compareTo(a.dateTime));
        break;
      case SortOption.dateOldest:
        _filteredList.sort((a, b) => a.dateTime.compareTo(b.dateTime));
        break;
      case SortOption.createdNewest:
        _filteredList.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        break;
      case SortOption.createdOldest:
        _filteredList.sort((a, b) => a.createdAt.compareTo(b.createdAt));
        break;
    }
    notifyListeners();
  }

  // 設置排序選項
  Future<void> setSortOption(SortOption option) async {
    _currentSortOption = option;
    _sortBirthData();

    // 將排序選項儲存到 SharedPreferences
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt('sortOption', option.index);
  }

  // 設置類別過濾
  void setCategoryFilter(Set<ChartCategory> categories) {
    _categoryFilter = Set.from(categories);
    _filterBirthData();
  }

  // 使用 BirthDataService 加載資料
  Future<void> loadBirthData() async {
    _isLoading = true;
    notifyListeners();

    try {
      final birthDataService = BirthDataService();
      _birthDataList = await birthDataService.getAllBirthData();
      _filteredList = List.from(_birthDataList);
      _sortBirthData();
      logger.d('已載入 ${_birthDataList.length} 筆出生資料');
    } catch (e) {
      logger.e('加載資料時出錯: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // 使用 BirthDataService 儲存資料
  Future<void> saveBirthData() async {
    try {
      final birthDataService = BirthDataService();
      await birthDataService.saveAllBirthData(_birthDataList);
      logger.d('已儲存 ${_birthDataList.length} 筆出生資料');
    } catch (e) {
      logger.e('儲存資料時出錯: $e');
    }
  }

  /// 清除所有出生資料
  ///
  /// 清除內存中的資料並通知 UI 更新
  void clearAllData() {
    _birthDataList.clear();
    _filteredList.clear();
    _selectedItems.clear();
    _isMultiSelectMode = false;
    notifyListeners();
    logger.d('清除所有出生資料成功');
  }

  // 匯出出生資料到 CSV 文件
  Future<String?> exportToCsv() async {
    if (_birthDataList.isEmpty) {
      return null;
    }

    try {
      // 匯出數據
      final filePath = await CsvHelper.exportBirthData(_birthDataList);
      return filePath;
    } catch (e) {
      logger.e('匯出資料時出錯: $e');
      return null;
    }
  }

  // 從 CSV 文件匯入出生資料
  Future<List<BirthData>> importFromCsv() async {
    _setImportStatus(true, '正在選擇 CSV 文件...', 0, 0);

    try {
      // 匯入數據
      _setImportStatus(true, '正在讀取 CSV 文件...', 0, 0);
      final importedData = await CsvHelper.importBirthDataFromCsvWithProgress(
        onProgress: (status, current, total) {
          _setImportStatus(true, status, current, total);
        },
      );

      if (importedData.isNotEmpty) {
        _setImportStatus(false, '匯入完成，共找到 ${importedData.length} 筆資料', importedData.length, importedData.length);
      } else {
        _setImportStatus(false, '未匯入任何資料', 0, 0);
      }

      return importedData;
    } catch (e) {
      logger.e('匯入資料時出錯: $e');
      _setImportStatus(false, '匯入失敗: $e', 0, 0);
      return [];
    }
  }

  // 設置匯入狀態
  void _setImportStatus(bool isImporting, String status, int progress, int total) {
    _isImporting = isImporting;
    _importStatus = status;
    _importProgress = progress;
    _importTotal = total;
    notifyListeners();
  }

  // 替換現有資料
  Future<void> replaceExistingData(List<BirthData> newData) async {
    _birthDataList = newData;
    await saveBirthData();
    _filteredList = List.from(_birthDataList);
    _sortBirthData();
    notifyListeners(); // 通知 UI 更新
    logger.d('已替換資料並更新 UI，共 ${_birthDataList.length} 筆');
  }

  // 合併到現有資料
  Future<int> mergeWithExistingData(List<BirthData> newData) async {
    // 合併資料，確保 ID 唯一性
    final existingIds = _birthDataList.map((data) => data.id).toSet();
    final processedData = <BirthData>[];

    for (final data in newData) {
      if (existingIds.contains(data.id)) {
        // ID 重複，生成新的唯一 ID
        final newId = _generateUniqueId(existingIds);
        final updatedData = data.copyWith(id: newId);
        processedData.add(updatedData);
        existingIds.add(newId); // 添加到已存在的 ID 集合中
        logger.i('ID 重複，已生成新的唯一 ID: ${data.id} -> $newId');
      } else {
        processedData.add(data);
        existingIds.add(data.id);
      }
    }

    _birthDataList.addAll(processedData);
    await saveBirthData();
    _filteredList = List.from(_birthDataList);
    _sortBirthData();
    notifyListeners(); // 通知 UI 更新
    logger.d('已合併資料並更新 UI，新增 ${processedData.length} 筆，總計 ${_birthDataList.length} 筆');

    return processedData.length;
  }

  // 添加新的出生資料
  Future<void> addBirthData(BirthData newData) async {
    _birthDataList.add(newData);
    await saveBirthData();
    _filteredList = List.from(_birthDataList);
    _sortBirthData();
  }

  // 更新出生資料
  Future<void> updateBirthData(String id, BirthData updatedData) async {
    // 先找到要更新的項目在原始列表中的索引
    final index = _birthDataList.indexWhere((data) => data.id == id);
    if (index != -1) {
      _birthDataList[index] = updatedData;
      await saveBirthData();
      _filteredList = List.from(_birthDataList);
      _sortBirthData();
    }
  }

  // 刪除出生資料
  Future<void> deleteBirthData(String id) async {
    // 先找到要刪除的項目在原始列表中的索引
    final index = _birthDataList.indexWhere((data) => data.id == id);
    if (index != -1) {
      _birthDataList.removeAt(index);
      await saveBirthData();
      _filteredList = List.from(_birthDataList);
      _sortBirthData();
    }
  }

  // 刪除選中的出生資料
  Future<int> deleteSelectedBirthData() async {
    if (_selectedItems.isEmpty) return 0;

    final selectedCount = _selectedItems.length;
    _birthDataList.removeWhere((data) => _selectedItems.contains(data.id));
    _selectedItems.clear();

    // 如果刪除後沒有項目了，退出多選模式
    if (_birthDataList.isEmpty) {
      _isMultiSelectMode = false;
    }

    await saveBirthData();
    _filteredList = List.from(_birthDataList);
    _sortBirthData();

    return selectedCount;
  }

  // 切換多選模式
  void toggleMultiSelectMode() {
    _isMultiSelectMode = !_isMultiSelectMode;
    if (!_isMultiSelectMode) {
      _selectedItems.clear();
    }
    notifyListeners();
  }

  // 切換項目選中狀態
  void toggleItemSelection(String id) {
    if (_selectedItems.contains(id)) {
      _selectedItems.remove(id);
    } else {
      _selectedItems.add(id);
    }
    notifyListeners();
  }

  // 全選或取消全選
  void toggleSelectAll() {
    if (_selectedItems.length == _birthDataList.length) {
      // 如果已經全選，則取消全選
      _selectedItems.clear();
    } else {
      // 全選所有項目
      _selectedItems = _birthDataList.map((data) => data.id).toSet();
    }
    notifyListeners();
  }

  // 清除搜索
  void clearSearch() {
    searchController.clear();
    _filteredList = List.from(_birthDataList);
    _sortBirthData();
  }

  // 獲取地理編碼
  Future<Map<String, double>> getCoordinatesFromAddress(String address) async {
    return await GeocodingService.getCoordinatesFromAddress(address);
  }

  // 格式化日期時間
  String formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
        '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  // 設置複製狀態
  void setCopying(bool copying) {
    _isCopying = copying;
    notifyListeners();
  }

  /// 生成唯一 ID
  String _generateUniqueId(Set<String> existingIds) {
    String newId;
    do {
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final random = (timestamp % 10000).toString().padLeft(4, '0');
      newId = 'bd_${timestamp}_$random';
    } while (existingIds.contains(newId));
    return newId;
  }

  // 複製選中的出生資料
  Future<bool> copySelectedBirthData() async {
    if (_selectedItems.isEmpty) return false;

    setCopying(true);

    try {
      final selectedData = _birthDataList.where((data) => _selectedItems.contains(data.id)).toList();
      final StringBuffer text = StringBuffer();

      text.writeln('===== 出生資料 =====');

      for (final data in selectedData) {
        text.writeln('\n姓名: ${data.name}');
        text.writeln('出生日期: ${formatDateTime(data.dateTime)}');
        text.writeln('出生地點: ${data.birthPlace}');
        text.writeln('經度: ${data.longitude.toStringAsFixed(4)}');
        text.writeln('緯度: ${data.latitude.toStringAsFixed(4)}');
        if (data.notes != null && data.notes!.isNotEmpty) {
          text.writeln('備註: ${data.notes}');
        }
        text.writeln('-------------------');
      }

      // 複製到剪貼板
      await FlutterClipboard.copy(text.toString());
      return true;
    } catch (e) {
      logger.e('複製資料時出錯: $e');
      return false;
    } finally {
      setCopying(false);
    }
  }
}
