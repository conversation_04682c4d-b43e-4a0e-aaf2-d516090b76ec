import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../core/utils/logger_utils.dart';
import '../../data/models/astrology/chart_data.dart';
import '../../data/models/astrology/chart_type.dart';
import '../../data/models/user/birth_data.dart';
import '../../data/services/api/astrology_service.dart';

class RelationshipAnalysisViewModel extends ChangeNotifier {
  bool _isLoading = false;

  bool get isLoading => _isLoading;

  ChartData? _chartData;

  ChartData? get chartData => _chartData;

  String? _errorMessage;

  String? get errorMessage => _errorMessage;

  // 設置加載狀態
  void setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // 設置錯誤訊息
  void setErrorMessage(String? message) {
    _errorMessage = message;
    notifyListeners();
  }

  // 獲取用戶的出生數據
  Future<List<BirthData>> loadBirthData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final String? birthDataJson = prefs.getString('birthDataList');

      if (birthDataJson == null || birthDataJson.isEmpty) {
        return [];
      }

      final List<dynamic> decodedData = jsonDecode(birthDataJson);
      return decodedData.map((item) => BirthData.fromJson(item)).toList();
    } catch (e) {
      logger.e('加載出生數據時出錯: $e');
      setErrorMessage('加載出生數據時出錯: $e');
      return [];
    }
  }

  // 分析戀愛象徵
  Future<void> analyzeRelationshipSymbols(BirthData birthData) async {
    setLoading(true);
    setErrorMessage(null);

    try {
      // 創建星盤數據
      final chartData = ChartData(
        chartType: ChartType.natal,
        primaryPerson: birthData,
      );

      // 計算行星位置
      _chartData = await AstrologyService().calculateChartData(chartData);
      setLoading(false);
      notifyListeners();
    } catch (e) {
      setLoading(false);
      setErrorMessage('分析戀愛象徵時出錯: $e');
      logger.e('分析戀愛象徵時出錯: $e');
    }
  }

  // 根據宮頭經度獲取宮主
  String getHouseRuler(double cuspLongitude) {
    // 獲取宮頭所在星座
    final signIndex = (cuspLongitude / 30).floor() % 12;
    final sign = getZodiacSignByIndex(signIndex);

    // 根據星座確定宮主
    switch (sign) {
      case '牡羊座':
        return '火星';
      case '金牛座':
        return '金星';
      case '雙子座':
        return '水星';
      case '巨蟹座':
        return '月亮';
      case '獅子座':
        return '太陽';
      case '處女座':
        return '水星';
      case '天秤座':
        return '金星';
      case '天蠍座':
        return '火星';
      case '射手座':
        return '木星';
      case '摩羯座':
        return '土星';
      case '水瓶座':
        return '土星';
      case '雙魚座':
        return '木星';
      default:
        return '未知';
    }
  }

  // 根據索引獲取星座名稱
  String getZodiacSignByIndex(int index) {
    final signs = [
      '牡羊座',
      '金牛座',
      '雙子座',
      '巨蟹座',
      '獅子座',
      '處女座',
      '天秤座',
      '天蠍座',
      '射手座',
      '摩羯座',
      '水瓶座',
      '雙魚座'
    ];
    return signs[index];
  }

  // 金星在不同星座的描述
  String getVenusDescription(String sign) {
    switch (sign) {
      case '牡羊座':
        return '在戀愛中熱情充滿，直接表達愛意，喜歡快速發展關係';
      case '金牛座':
        return '重視愛情的穩定性和安全感，追求感官享受和物質興趣';
      case '雙子座':
        return '重視戀愛中的溝通和智慧，喜歡多樣化的愛情體驗';
      case '巨蟹座':
        return '重視愛情中的情感聯繫和安全感，非常關心伴侶的需求';
      case '獅子座':
        return '在戀愛中充滿自信和戲劇性，喜歡被讚美和關注';
      case '處女座':
        return '在戀愛中謹慎細心，重視細節和實用性，追求完美';
      case '天秤座':
        return '重視愛情中的和諧、平衡和公正，追求優雅的關係';
      case '天蠍座':
        return '在戀愛中充滿深度和強烈的情感，喜歡神秘和變化';
      case '射手座':
        return '重視愛情中的自由和探索，喜歡充滿冒險和樂觀的關係';
      case '摩羯座':
        return '重視愛情中的責任和承諾，追求具有社會地位的伴侶';
      case '水瓶座':
        return '重視愛情中的友誼和獨特性，喜歡非傳統的關係';
      case '雙魚座':
        return '重視愛情中的情感深度和精神連結，充滿浪漫和理想主義';
      default:
        return '具有獨特的愛情觀';
    }
  }

  // 火星在不同星座的描述
  String getMarsDescription(String sign) {
    switch (sign) {
      case '牡羊座':
        return '直接且熱情地追求所愛，行動力強，充滿競爭精神';
      case '金牛座':
        return '耐心且堅定地追求所愛，重視實質和安全感';
      case '雙子座':
        return '通過溝通和智慧追求所愛，充滿靈活性和變化';
      case '巨蟹座':
        return '通過情感和關懷表達所愛，保護性強，有時易情緒化';
      case '獅子座':
        return '自信且充滿戲劇性地追求所愛，喜歡展示自己';
      case '處女座':
        return '謹慎且分析地追求所愛，重視細節和完美';
      case '天秤座':
        return '通過平衡和公正追求所愛，重視和諧和合作';
      case '天蠍座':
        return '充滿強烈性欲和控制欲，追求深度和轉變';
      case '射手座':
        return '直接且充滿冒險精神地追求所愛，重視自由';
      case '摩羯座':
        return '耐心且有結構地追求所愛，重視成就和地位';
      case '水瓶座':
        return '通過獨特和創新的方式追求所愛，重視自由和友誼';
      case '雙魚座':
        return '充滿情感和直覺地追求所愛，有時較被動';
      default:
        return '有獨特的追求方式';
    }
  }

  // 行星在不同星座的描述
  String getPlanetInSignDescription(String planet, String sign) {
    switch (sign) {
      case '牡羊座':
        return '熱情直接、充滿行動力';
      case '金牛座':
        return '穩定可靠、重視實質';
      case '雙子座':
        return '聯繫良好、思想活躍';
      case '巨蟹座':
        return '情感豐富、充滿關懷';
      case '獅子座':
        return '自信光輝、充滿魅力';
      case '處女座':
        return '謹慎細心、重視細節';
      case '天秤座':
        return '公正和諧、重視關係';
      case '天蠍座':
        return '深度強烈、充滿神秘';
      case '射手座':
        return '樂觀開朗、熱愛自由';
      case '摩羯座':
        return '責任感強、重視成就';
      case '水瓶座':
        return '獨特創新、重視友誼';
      case '雙魚座':
        return '情感豐富、充滿想像力';
      default:
        return '具有獨特特質';
    }
  }

  // 宮位描述
  String getHouseDescription(int house) {
    switch (house) {
      case 1:
        return '您的戀愛表達直接影響您的個人形象和自我表達';
      case 2:
        return '您的戀愛表達與價值觀和物質安全感密切相關';
      case 3:
        return '您的戀愛表達與溝通、學習和日常互動密切相關';
      case 4:
        return '您的戀愛表達與家庭、情感安全感和個人根基密切相關';
      case 5:
        return '您的戀愛表達與創造力、娛樂和浪漫關係密切相關';
      case 6:
        return '您的戀愛表達與日常服務、健康和工作環境密切相關';
      case 7:
        return '您的戀愛表達與一對一關係、合作和伴侶關係密切相關';
      case 8:
        return '您的戀愛表達與親密、轉變和共享資源密切相關';
      case 9:
        return '您的戀愛表達與探索、旅行和更高的學習密切相關';
      case 10:
        return '您的戀愛表達與公眾形象、職業和社會地位密切相關';
      case 11:
        return '您的戀愛表達與友誼、團體和長期目標密切相關';
      case 12:
        return '您的戀愛表達與精神、私密和潛意識活動密切相關';
      default:
        return '影響您的戀愛表達方式';
    }
  }
}
