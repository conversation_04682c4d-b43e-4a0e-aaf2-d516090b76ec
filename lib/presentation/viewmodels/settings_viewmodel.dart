import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../astreal.dart';

class SettingsViewModel extends ChangeNotifier {
  static const String _selectedModelKey = 'selected_ai_model';

  // State variables
  bool _isLoading = true;
  String _selectedModelId = 'gemma2-9b-it';
  ChartSettings? _chartSettings;
  MultiChartSettings? _multiChartSettings;
  ChartType _currentChartType = ChartType.natal;
  TabController? _tabController;

  // AI 模型列表
  final List<AIModel> _models = const [
    AIModel(
      name: 'Gemma2 9B',
      id: 'gemma2-9b-it',
      description: 'Google 的開源模型，速度快，資源佔用少',
    ),
    AIModel(
      name: 'Mixtral 8x7B',
      id: 'mixtral-8x7b-32768',
      description: '強大的開源模型，支援多語言，適合複雜的星盤解讀',
    ),
    AIModel(
      name: 'Claude 3 Opus',
      id: 'claude-3-opus-20240229',
      description: '最新的 Claude 模型，具有強大的推理能力和準確性',
    ),
    AIModel(
      name: 'GPT-4',
      id: 'chatgpt-4o-latest',
      description: 'OpenAI 的頂級模型，提供準確和創新的解讀',
    ),
  ];

  // 宮位系統列表
  final List<String> _houseSystems = [
    'Placidus',
    'Koch',
    'Campanus',
    'Regiomontanus',
    'Equal House',
    'Whole Sign',
    'Porphyrius',
    'Meridian',
    'Morinus',
    'Topocentric',
  ];

  // Constructor with initialization
  SettingsViewModel() {
    // Load settings asynchronously without notifying during construction
    _initSettings();
  }

  // Getters
  bool get isLoading => _isLoading;
  String get selectedModelId => _selectedModelId;
  ChartSettings? get chartSettings => _chartSettings;
  MultiChartSettings? get multiChartSettings => _multiChartSettings;
  ChartType get currentChartType => _currentChartType;
  ChartTypeSettings? get currentChartTypeSettings =>
      _multiChartSettings?.getSettingsForChartType(_currentChartType);
  TabController? get tabController => _tabController;
  List<AIModel> get models => _models;
  List<String> get houseSystems => _houseSystems;

  // Initialize with TabController
  void initTabController(TabController controller) {
    _tabController = controller;
  }

  // Initialize settings without triggering UI updates
  Future<void> _initSettings() async {
    final prefs = await SharedPreferences.getInstance();
    final chartSettings = await ChartSettings.loadFromPrefs();
    final multiChartSettings = await MultiChartSettings.loadFromPrefs();

    _selectedModelId = prefs.getString(_selectedModelKey) ?? 'gemma2-9b-it';
    _chartSettings = chartSettings;
    _multiChartSettings = multiChartSettings;
    _currentChartType = multiChartSettings.currentChartType;
    _isLoading = false;

    // Now it's safe to notify listeners
    notifyListeners();
  }

  // Save selected AI model
  Future<void> saveSelectedModel(String modelId) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_selectedModelKey, modelId);

    _selectedModelId = modelId;
    notifyListeners();
  }

  // Save chart settings
  Future<void> saveChartSettings() async {
    if (_chartSettings != null) {
      await _chartSettings!.saveToPrefs();
      notifyListeners();
    }
  }

  // Save multi chart settings
  Future<void> saveMultiChartSettings() async {
    if (_multiChartSettings != null) {
      await _multiChartSettings!.saveToPrefs();
      notifyListeners();
    }
  }

  // Set current chart type
  void setCurrentChartType(ChartType chartType) {
    _currentChartType = chartType;
    if (_multiChartSettings != null) {
      _multiChartSettings!.setCurrentChartType(chartType);
    }
    // 立即通知 UI 更新，然後異步保存設定
    notifyListeners();
    if (_multiChartSettings != null) {
      saveMultiChartSettings();
    }
  }

  // Get settings for specific chart type
  ChartTypeSettings? getSettingsForChartType(ChartType chartType) {
    return _multiChartSettings?.getSettingsForChartType(chartType);
  }

  // Update house system for current chart type
  void updateHouseSystem(HouseSystem newValue) {
    // Update legacy settings
    if (_chartSettings != null) {
      _chartSettings!.houseSystem = newValue;
      saveChartSettings();
    }

    // Update multi chart settings
    if (_multiChartSettings != null) {
      final currentSettings = _multiChartSettings!.getSettingsForChartType(_currentChartType);
      currentSettings.houseSystem = newValue;
      _multiChartSettings!.updateSettingsForChartType(_currentChartType, currentSettings);
      saveMultiChartSettings();
    }
  }

  // Update planet visibility for current chart type
  void updatePlanetVisibility(String planet, bool isVisible) {
    // Update legacy settings
    if (_chartSettings != null) {
      _chartSettings!.planetVisibility[planet] = isVisible;
      saveChartSettings();
    }

    // Update multi chart settings
    if (_multiChartSettings != null) {
      final currentSettings = _multiChartSettings!.getSettingsForChartType(_currentChartType);
      currentSettings.planetVisibility[planet] = isVisible;
      _multiChartSettings!.updateSettingsForChartType(_currentChartType, currentSettings);
      saveMultiChartSettings();
    }
  }

  // Update zodiac rulers visibility for current chart type
  void updateZodiacRulersVisibility(bool isVisible) {
    // Update legacy settings
    if (_chartSettings != null) {
      _chartSettings!.showZodiacRulers = isVisible;
      saveChartSettings();
    }

    // Update multi chart settings
    if (_multiChartSettings != null) {
      final currentSettings = _multiChartSettings!.getSettingsForChartType(_currentChartType);
      currentSettings.showZodiacRulers = isVisible;
      _multiChartSettings!.updateSettingsForChartType(_currentChartType, currentSettings);
      saveMultiChartSettings();
    }
  }

  // Update zodiac background visibility for current chart type
  void updateZodiacBackgroundVisibility(bool isVisible) {
    // Update legacy settings
    if (_chartSettings != null) {
      _chartSettings!.showZodiacBackground = isVisible;
      saveChartSettings();
    }

    // Update multi chart settings
    if (_multiChartSettings != null) {
      final currentSettings = _multiChartSettings!.getSettingsForChartType(_currentChartType);
      currentSettings.showZodiacBackground = isVisible;
      _multiChartSettings!.updateSettingsForChartType(_currentChartType, currentSettings);
      saveMultiChartSettings();
    }
  }

  // Update house degrees visibility for current chart type
  void updateHouseDegreesVisibility(bool isVisible) {
    // Update legacy settings
    if (_chartSettings != null) {
      _chartSettings!.showHouseDegrees = isVisible;
      saveChartSettings();
    }

    // Update multi chart settings
    if (_multiChartSettings != null) {
      final currentSettings = _multiChartSettings!.getSettingsForChartType(_currentChartType);
      currentSettings.showHouseDegrees = isVisible;
      _multiChartSettings!.updateSettingsForChartType(_currentChartType, currentSettings);
      saveMultiChartSettings();
    }
  }

  // Update planet degrees visibility for current chart type
  void updatePlanetDegreesVisibility(bool isVisible) {
    // Update legacy settings
    if (_chartSettings != null) {
      _chartSettings!.showPlanetDegrees = isVisible;
      saveChartSettings();
    }

    // Update multi chart settings
    if (_multiChartSettings != null) {
      final currentSettings = _multiChartSettings!.getSettingsForChartType(_currentChartType);
      currentSettings.showPlanetDegrees = isVisible;
      _multiChartSettings!.updateSettingsForChartType(_currentChartType, currentSettings);
      saveMultiChartSettings();
    }
  }

  // Update aspect orb for current chart type
  void updateAspectOrb(String aspect, double value) {
    // 確保值為整數且不超過30
    int intValue = value.toInt();
    if (intValue > 30) intValue = 30;
    if (intValue < 0) intValue = 0;
    final finalValue = intValue.toDouble();

    // Update legacy settings
    if (_chartSettings != null) {
      _chartSettings!.aspectOrbs[aspect] = finalValue;
      saveChartSettings();
    }

    // Update multi chart settings
    if (_multiChartSettings != null) {
      final currentSettings = _multiChartSettings!.getSettingsForChartType(_currentChartType);
      currentSettings.aspectOrbs[aspect] = finalValue;
      _multiChartSettings!.updateSettingsForChartType(_currentChartType, currentSettings);
      saveMultiChartSettings();
    }
  }

  // Update major aspects visibility for current chart type
  void updateMajorAspectsVisibility(bool isVisible) {
    // Update legacy settings
    if (_chartSettings != null) {
      _chartSettings!.showMajorAspects = isVisible;
      saveChartSettings();
    }

    // Update multi chart settings
    if (_multiChartSettings != null) {
      final currentSettings = _multiChartSettings!.getSettingsForChartType(_currentChartType);
      currentSettings.showMajorAspects = isVisible;
      _multiChartSettings!.updateSettingsForChartType(_currentChartType, currentSettings);
      saveMultiChartSettings();
    }
  }

  // Update minor aspects visibility for current chart type
  void updateMinorAspectsVisibility(bool isVisible) {
    // Update legacy settings
    if (_chartSettings != null) {
      _chartSettings!.showMinorAspects = isVisible;
      saveChartSettings();
    }

    // Update multi chart settings
    if (_multiChartSettings != null) {
      final currentSettings = _multiChartSettings!.getSettingsForChartType(_currentChartType);
      currentSettings.showMinorAspects = isVisible;
      _multiChartSettings!.updateSettingsForChartType(_currentChartType, currentSettings);
      saveMultiChartSettings();
    }
  }

  // Update color theme for current chart type
  void updateColorTheme(String theme) {
    // Update legacy settings
    if (_chartSettings != null) {
      _chartSettings!.colorTheme = theme;
      saveChartSettings();
    }

    // Update multi chart settings
    if (_multiChartSettings != null) {
      final currentSettings = _multiChartSettings!.getSettingsForChartType(_currentChartType);
      currentSettings.colorTheme = theme;
      _multiChartSettings!.updateSettingsForChartType(_currentChartType, currentSettings);
      saveMultiChartSettings();
    }
  }

  // Reset chart settings to defaults
  void resetChartSettings() {
    // Reset legacy settings
    _chartSettings = ChartSettings();
    saveChartSettings();

    // Reset multi chart settings
    _multiChartSettings = MultiChartSettings();
    saveMultiChartSettings();
  }

  // Update astrology mode for current chart type
  void updateAstrologyMode(String mode) {
    // Update multi chart settings
    if (_multiChartSettings != null) {
      final currentSettings = _multiChartSettings!.getSettingsForChartType(_currentChartType);
      currentSettings.astrologyMode = mode;
      _multiChartSettings!.updateSettingsForChartType(_currentChartType, currentSettings);
      saveMultiChartSettings();
    }
  }

  /// 重置所有設定為預設值
  ///
  /// 這個方法會重置所有應用設定為預設值，但不會清除出生資料
  Future<void> resetToDefaults() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // 清除 AI 模型選擇
      await prefs.remove(_selectedModelKey);

      // 清除星盤設定
      await prefs.remove('chart_settings');
      await prefs.remove('multi_chart_settings');

      // 清除用戶資訊
      await prefs.remove('user_name');
      await prefs.remove('user_email');
      await prefs.remove('user_phone');
      await prefs.remove('user_contact_method');

      // 清除其他設定
      await prefs.remove('last_viewed_birth_data_id');
      await prefs.remove('selected_chart_type');

      // 重新載入預設設定
      _selectedModelId = 'gemma2-9b-it';
      _chartSettings = ChartSettings(); // 建立預設設定
      _multiChartSettings = MultiChartSettings(); // 建立預設多星盤設定
      _currentChartType = ChartType.natal;

      // 通知 UI 更新
      notifyListeners();

      debugPrint('成功重置所有設定為預設值');
    } catch (e) {
      debugPrint('重置設定時出錯: $e');
      rethrow;
    }
  }

  /// 清除所有儲存資料與設定
  ///
  /// 清除後會重置所有設定為預設值，並通知其他 ViewModel 更新
  /// 注意：此方法不會直接調用 FilesViewModel 的 clearAllData 方法，
  /// 而是由調用者負責在清除設定後調用該方法。
  Future<void> clearAllSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // 清除 AI 模型選擇
      await prefs.remove(_selectedModelKey);

      // 清除星盤設定
      await prefs.remove('chart_settings');

      // 清除出生資料列表
      await prefs.remove('birthDataList');
      await prefs.remove('selectedBirthDataId');

      // 清除用戶資訊
      await prefs.remove('user_name');
      await prefs.remove('user_email');
      await prefs.remove('user_phone');
      await prefs.remove('user_contact_method');

      // 清除預約相關資訊
      await prefs.remove('user_contact_method');

      // 清除其他設定
      await prefs.remove('last_viewed_birth_data_id');
      await prefs.remove('selected_chart_type');

      // 重新載入預設設定
      _selectedModelId = 'gemma2-9b-it';
      _chartSettings = ChartSettings(); // 建立預設設定

      // 通知 UI 更新
      notifyListeners();

      debugPrint('成功清除所有設定與儲存資料');
      return Future.value();
    } catch (e) {
      debugPrint('清除設定時出錯: $e');
      return Future.error(e);
    }
  }
}
