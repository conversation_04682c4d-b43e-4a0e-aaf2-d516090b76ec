
import 'package:flutter/foundation.dart';

import '../../astreal.dart';
import '../../data/services/api/financial_analysis_service.dart';

/// 理財頁面的 ViewModel
class FinanceViewModel extends ChangeNotifier {
  final FinancialAnalysisService _financialService = FinancialAnalysisService();

  // 狀態變量
  BirthData? _selectedPerson;
  FinancialAnalysis? _currentAnalysis;
  bool _isLoading = false;
  String? _errorMessage;

  // Getters
  BirthData? get selectedPerson => _selectedPerson;
  FinancialAnalysis? get currentAnalysis => _currentAnalysis;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  bool get hasAnalysis => _currentAnalysis != null;

  /// 選擇人物
  Future<void> selectPerson(BirthData person) async {
    _selectedPerson = person;
    _errorMessage = null;
    notifyListeners();

    // 自動開始分析
    await _performAnalysis();
  }

  /// 執行財務分析
  Future<void> _performAnalysis() async {
    if (_selectedPerson == null) return;

    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      _currentAnalysis = await _financialService.analyzeFinancialProfile(_selectedPerson!);
    } catch (e) {
      _errorMessage = '分析失敗：$e';
      _currentAnalysis = null;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// 刷新分析
  Future<void> refreshAnalysis() async {
    if (_selectedPerson != null) {
      await _performAnalysis();
    }
  }

  /// 清除選擇
  void clearSelection() {
    _selectedPerson = null;
    _currentAnalysis = null;
    _errorMessage = null;
    notifyListeners();
  }

  /// 獲取財務風格描述
  String getFinancialStyleDescription() {
    if (_currentAnalysis == null) return '暫無分析數據';

    final style = _currentAnalysis!.financialStyle;
    switch (style.riskTolerance) {
      case RiskTolerance.conservative:
        return '保守型投資者：偏好穩定收益，風險承受度較低';
      case RiskTolerance.moderate:
        return '穩健型投資者：平衡風險與收益，適度投資';
      case RiskTolerance.aggressive:
        return '積極型投資者：追求高收益，能承受較高風險';
    }
  }

  /// 獲取當前財運狀態
  String getCurrentFinancialStatus() {
    if (_currentAnalysis == null) return '暫無數據';

    final forecast = _currentAnalysis!.yearlyForecast;
    if (forecast.monthlyForecasts.isEmpty) return '暫無預測數據';

    final currentMonth = DateTime.now().month;
    final currentMonthForecast = forecast.monthlyForecasts.firstWhere(
      (f) => f.month == currentMonth,
      orElse: () => forecast.monthlyForecasts.first,
    );

    return currentMonthForecast.isPositive ? '財運亨通' : '需謹慎理財';
  }

  /// 獲取投資建議摘要
  String getInvestmentAdviceSummary() {
    if (_currentAnalysis == null) return '暫無建議';

    final advice = _currentAnalysis!.investmentAdvice;
    if (advice.isEmpty) return '暫無投資建議';

    return advice.first.description;
  }
}
