import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../astreal.dart';

/// 管理最近使用和收藏的星盤記錄
class RecentChartsViewModel extends ChangeNotifier {
  static const String _recentChartsKey = 'recent_charts';
  static const int _maxRecentCharts = 10; // 最多保存10個最近使用的星盤記錄

  bool _isLoading = true;
  List<RecentChartRecord> _recentCharts = [];
  List<RecentChartRecord> _favoriteCharts = [];

  // Getters
  bool get isLoading => _isLoading;
  List<RecentChartRecord> get recentCharts => _recentCharts;
  List<RecentChartRecord> get favoriteCharts => _favoriteCharts;

  // 構造函數
  RecentChartsViewModel() {
    _loadRecentCharts();
  }

  /// 加載最近使用的星盤記錄
  Future<void> _loadRecentCharts() async {
    _isLoading = true;
    notifyListeners();

    try {
      final prefs = await SharedPreferences.getInstance();
      final String? recentChartsJson = prefs.getString(_recentChartsKey);

      if (recentChartsJson != null) {
        final List<dynamic> decodedData = jsonDecode(recentChartsJson);
        _recentCharts = decodedData
            .map((item) => RecentChartRecord.fromJson(item))
            .toList();

        // 按使用時間排序（最新的在前）
        _recentCharts.sort((a, b) => b.usedAt.compareTo(a.usedAt));

        // 過濾出收藏的星盤
        _favoriteCharts = _recentCharts.where((chart) => chart.isFavorite).toList();
      }
    } catch (e) {
      logger.e('加載最近使用的星盤記錄時出錯: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// 保存最近使用的星盤記錄
  Future<void> _saveRecentCharts() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final List<Map<String, dynamic>> encodedData =
          _recentCharts.map((record) => record.toJson()).toList();
      await prefs.setString(_recentChartsKey, jsonEncode(encodedData));
    } catch (e) {
      logger.e('保存最近使用的星盤記錄時出錯: $e');
    }
  }

  /// 添加或更新最近使用的星盤記錄
  Future<void> addOrUpdateRecentChart(ChartData chartData) async {
    if (chartData.primaryPerson == null) {
      logger.w('無法添加最近使用的星盤記錄：主要人物為空');
      return;
    }

    try {
      // 創建唯一ID
      final String id = _generateChartId(chartData);

      // 檢查是否已存在相同ID的記錄
      final existingIndex = _recentCharts.indexWhere((record) => record.id == id);

      if (existingIndex != -1) {
        // 更新現有記錄的使用時間
        final existingRecord = _recentCharts[existingIndex];
        final updatedRecord = existingRecord.copyWithNewUsedAt();
        _recentCharts[existingIndex] = updatedRecord;
      } else {
        // 創建新記錄
        final newRecord = RecentChartRecord(
          id: id,
          chartType: chartData.chartType,
          primaryPersonId: chartData.primaryPerson.id,
          secondaryPersonId: chartData.secondaryPerson?.id,
          specificDate: chartData.specificDate,
          usedAt: DateTime.now(),
        );

        // 添加到列表
        _recentCharts.add(newRecord);

        // 如果超過最大數量，移除最舊的非收藏記錄
        if (_recentCharts.length > _maxRecentCharts) {
          // 找出最舊的非收藏記錄
          final oldestNonFavoriteIndex = _findOldestNonFavoriteIndex();
          if (oldestNonFavoriteIndex != -1) {
            _recentCharts.removeAt(oldestNonFavoriteIndex);
          }
        }
      }

      // 按使用時間排序（最新的在前）
      _recentCharts.sort((a, b) => b.usedAt.compareTo(a.usedAt));

      // 保存更新後的記錄
      await _saveRecentCharts();

      // 通知監聽器
      notifyListeners();
    } catch (e) {
      logger.e('添加或更新最近使用的星盤記錄時出錯: $e');
    }
  }

  /// 切換星盤記錄的收藏狀態
  Future<void> toggleFavorite(String recordId) async {
    try {
      final index = _recentCharts.indexWhere((record) => record.id == recordId);
      if (index != -1) {
        final record = _recentCharts[index];
        final updatedRecord = record.copyWithFavorite(!record.isFavorite);
        _recentCharts[index] = updatedRecord;

        // 更新收藏列表
        if (updatedRecord.isFavorite) {
          _favoriteCharts.add(updatedRecord);
        } else {
          _favoriteCharts.removeWhere((chart) => chart.id == recordId);
        }

        // 保存更新後的記錄
        await _saveRecentCharts();

        // 通知監聽器
        notifyListeners();
      }
    } catch (e) {
      logger.e('切換星盤記錄收藏狀態時出錯: $e');
    }
  }

  /// 移除星盤記錄
  Future<void> removeChart(String recordId) async {
    try {
      _recentCharts.removeWhere((record) => record.id == recordId);
      _favoriteCharts.removeWhere((record) => record.id == recordId);

      // 保存更新後的記錄
      await _saveRecentCharts();

      // 通知監聽器
      notifyListeners();
    } catch (e) {
      logger.e('移除星盤記錄時出錯: $e');
    }
  }

  /// 根據ID獲取星盤記錄
  RecentChartRecord? getChartById(String recordId) {
    try {
      return _recentCharts.firstWhere((record) => record.id == recordId);
    } catch (e) {
      return null;
    }
  }

  /// 根據星盤數據生成唯一ID
  String _generateChartId(ChartData chartData) {
    final buffer = StringBuffer();
    buffer.write(chartData.chartType.toString());
    buffer.write('_');
    buffer.write(chartData.primaryPerson.id);

    if (chartData.secondaryPerson != null) {
      buffer.write('_');
      buffer.write(chartData.secondaryPerson!.id);
    }

    if (chartData.specificDate != null) {
      buffer.write('_');
      buffer.write(chartData.specificDate!.millisecondsSinceEpoch);
    }

    return buffer.toString();
  }

  /// 找出最舊的非收藏記錄的索引
  int _findOldestNonFavoriteIndex() {
    int oldestIndex = -1;
    DateTime? oldestTime;

    for (int i = 0; i < _recentCharts.length; i++) {
      final record = _recentCharts[i];
      if (!record.isFavorite) {
        if (oldestTime == null || record.usedAt.isBefore(oldestTime)) {
          oldestTime = record.usedAt;
          oldestIndex = i;
        }
      }
    }

    return oldestIndex;
  }

  /// 根據記錄ID和出生數據列表創建ChartData
  Future<ChartData?> createChartDataFromRecord(
    String recordId,
    List<BirthData> birthDataList,
  ) async {
    try {
      final record = getChartById(recordId);
      if (record == null) return null;

      // 查找主要人物
      final primaryPerson = birthDataList.firstWhere(
        (data) => data.id == record.primaryPersonId,
        orElse: () => throw Exception('找不到主要人物'),
      );

      // 查找次要人物（如果有）
      BirthData? secondaryPerson;
      if (record.secondaryPersonId != null) {
        try {
          secondaryPerson = birthDataList.firstWhere(
            (data) => data.id == record.secondaryPersonId,
          );
        } catch (e) {
          // 如果找不到次要人物，繼續但不設置次要人物
          logger.w('找不到次要人物: ${record.secondaryPersonId}');
        }
      }

      // 創建ChartData
      return ChartData(
        chartType: record.chartType,
        primaryPerson: primaryPerson,
        secondaryPerson: secondaryPerson,
        specificDate: record.specificDate,
      );
    } catch (e) {
      logger.e('從記錄創建ChartData時出錯: $e');
      return null;
    }
  }

  /// 清除所有記錄
  Future<void> clearAllRecords() async {
    try {
      _recentCharts.clear();
      _favoriteCharts.clear();

      // 保存更新後的記錄
      await _saveRecentCharts();

      // 通知監聽器
      notifyListeners();
    } catch (e) {
      logger.e('清除所有星盤記錄時出錯: $e');
    }
  }

  /// 獲取近期選中的人物ID列表（按使用頻率和時間排序）
  List<String> getRecentPersonIds({int maxCount = 6}) {
    final Map<String, DateTime> personLastUsed = {};
    final Map<String, int> personUsageCount = {};

    // 統計每個人物的使用次數和最後使用時間
    for (final record in _recentCharts) {
      // 統計主要人物
      final primaryId = record.primaryPersonId;
      personUsageCount[primaryId] = (personUsageCount[primaryId] ?? 0) + 1;
      if (personLastUsed[primaryId] == null ||
          record.usedAt.isAfter(personLastUsed[primaryId]!)) {
        personLastUsed[primaryId] = record.usedAt;
      }

      // 統計次要人物（如果有）
      if (record.secondaryPersonId != null) {
        final secondaryId = record.secondaryPersonId!;
        personUsageCount[secondaryId] = (personUsageCount[secondaryId] ?? 0) + 1;
        if (personLastUsed[secondaryId] == null ||
            record.usedAt.isAfter(personLastUsed[secondaryId]!)) {
          personLastUsed[secondaryId] = record.usedAt;
        }
      }
    }

    // 按使用頻率和最後使用時間排序
    final sortedPersonIds = personLastUsed.keys.toList()
      ..sort((a, b) {
        final aCount = personUsageCount[a] ?? 0;
        final bCount = personUsageCount[b] ?? 0;

        // 首先按使用次數排序
        if (aCount != bCount) {
          return bCount.compareTo(aCount);
        }

        // 如果使用次數相同，按最後使用時間排序
        final aTime = personLastUsed[a]!;
        final bTime = personLastUsed[b]!;
        return bTime.compareTo(aTime);
      });

    // 返回指定數量的人物ID
    return sortedPersonIds.take(maxCount).toList();
  }

  /// 獲取近期選中的人物（包含完整的BirthData信息）
  List<BirthData> getRecentPersons(List<BirthData> allPersons, {int maxCount = 6}) {
    final recentPersonIds = getRecentPersonIds(maxCount: maxCount);
    final List<BirthData> recentPersons = [];

    for (final personId in recentPersonIds) {
      try {
        final person = allPersons.firstWhere((p) => p.id == personId);
        recentPersons.add(person);
      } catch (e) {
        // 如果找不到對應的人物，跳過
        logger.w('找不到ID為 $personId 的人物');
      }
    }

    return recentPersons;
  }
}
