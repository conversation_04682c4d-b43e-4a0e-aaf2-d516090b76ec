import 'package:flutter/material.dart';

import '../../core/utils/logger_utils.dart';
import '../../data/models/video/video_data.dart';
import '../../data/services/api/youtube_video_service.dart';

/// 影片列表頁面的 ViewModel
/// 使用 MVVM 架構管理影片列表的狀態和業務邏輯
class VideoListViewModel extends ChangeNotifier {
  // 狀態管理
  bool _isLoading = false;
  bool _hasError = false;
  String _errorMessage = '';
  
  // 資料
  List<VideoCategory> _categories = [];
  List<VideoData> _allVideos = [];
  List<VideoData> _filteredVideos = [];
  List<String> _allTags = [];
  
  // 篩選和搜尋
  String _selectedCategoryId = '';
  String _searchQuery = '';
  String _selectedTag = '';
  bool _showOnlyPopular = false;
  
  // 搜尋控制器
  final TextEditingController searchController = TextEditingController();

  // Getters
  bool get isLoading => _isLoading;
  bool get hasError => _hasError;
  String get errorMessage => _errorMessage;
  List<VideoCategory> get categories => _categories;
  List<VideoData> get allVideos => _allVideos;
  List<VideoData> get filteredVideos => _filteredVideos;
  List<String> get allTags => _allTags;
  String get selectedCategoryId => _selectedCategoryId;
  String get searchQuery => _searchQuery;
  String get selectedTag => _selectedTag;
  bool get showOnlyPopular => _showOnlyPopular;
  
  /// 獲取當前選中的分類
  VideoCategory? get selectedCategory {
    if (_selectedCategoryId.isEmpty) return null;
    try {
      return _categories.firstWhere((category) => category.id == _selectedCategoryId);
    } catch (e) {
      return null;
    }
  }

  /// 獲取熱門影片
  List<VideoData> get popularVideos {
    return _allVideos.where((video) => video.isPopular).toList();
  }

  /// 初始化 ViewModel
  VideoListViewModel() {
    searchController.addListener(_onSearchChanged);
    loadVideos();
  }

  @override
  void dispose() {
    searchController.removeListener(_onSearchChanged);
    searchController.dispose();
    super.dispose();
  }

  /// 載入影片資料
  Future<void> loadVideos() async {
    logger.d('開始載入影片資料');
    _setLoading(true);
    _clearError();

    try {
      // 並行載入所有資料
      final results = await Future.wait([
        YouTubeVideoService.getCategories(),
        YouTubeVideoService.getAllVideos(),
        YouTubeVideoService.getAllTags(),
      ]);

      _categories = results[0] as List<VideoCategory>;
      _allVideos = results[1] as List<VideoData>;
      _allTags = results[2] as List<String>;

      logger.i('成功載入影片資料：${_categories.length} 個分類，${_allVideos.length} 個影片');

      // 應用當前篩選條件
      _applyFilters();
    } catch (e) {
      logger.e('載入影片資料失敗: $e');
      _setError('載入影片資料失敗：$e');
    } finally {
      _setLoading(false);
    }
  }

  /// 刷新影片資料
  Future<void> refreshVideos() async {
    logger.d('刷新影片資料');
    try {
      await YouTubeVideoService.refreshConfig();
      await loadVideos();
    } catch (e) {
      logger.e('刷新影片資料失敗: $e');
      _setError('刷新失敗：$e');
    }
  }

  /// 搜尋影片
  Future<void> searchVideos(String query) async {
    logger.d('搜尋影片：$query');
    _searchQuery = query.trim();
    searchController.text = _searchQuery;
    
    if (_searchQuery.isEmpty) {
      _applyFilters();
      return;
    }

    _setLoading(true);
    try {
      final searchResults = await YouTubeVideoService.searchVideos(_searchQuery);
      _filteredVideos = _applyCurrentFilters(searchResults);
      logger.d('搜尋結果：${_filteredVideos.length} 個影片');
    } catch (e) {
      logger.e('搜尋影片失敗: $e');
      _setError('搜尋失敗：$e');
    } finally {
      _setLoading(false);
    }
  }

  /// 選擇分類
  void selectCategory(String categoryId) {
    logger.d('選擇分類：$categoryId');
    _selectedCategoryId = categoryId;
    _applyFilters();
  }

  /// 清除分類選擇
  void clearCategorySelection() {
    logger.d('清除分類選擇');
    _selectedCategoryId = '';
    _applyFilters();
  }

  /// 選擇標籤
  void selectTag(String tag) {
    logger.d('選擇標籤：$tag');
    _selectedTag = tag;
    _applyFilters();
  }

  /// 清除標籤選擇
  void clearTagSelection() {
    logger.d('清除標籤選擇');
    _selectedTag = '';
    _applyFilters();
  }

  /// 切換只顯示熱門影片
  void toggleShowOnlyPopular() {
    logger.d('切換只顯示熱門影片：${!_showOnlyPopular}');
    _showOnlyPopular = !_showOnlyPopular;
    _applyFilters();
  }

  /// 清除所有篩選條件
  void clearAllFilters() {
    logger.d('清除所有篩選條件');
    _selectedCategoryId = '';
    _selectedTag = '';
    _showOnlyPopular = false;
    _searchQuery = '';
    searchController.clear();
    _applyFilters();
  }

  /// 根據標籤獲取影片
  Future<void> getVideosByTag(String tag) async {
    logger.d('根據標籤獲取影片：$tag');
    _setLoading(true);
    try {
      final videos = await YouTubeVideoService.getVideosByTag(tag);
      _filteredVideos = videos;
      _selectedTag = tag;
      logger.d('標籤篩選結果：${_filteredVideos.length} 個影片');
    } catch (e) {
      logger.e('根據標籤獲取影片失敗: $e');
      _setError('篩選失敗：$e');
    } finally {
      _setLoading(false);
    }
  }

  /// 搜尋控制器變化監聽
  void _onSearchChanged() {
    final query = searchController.text.trim();
    if (query != _searchQuery) {
      // 延遲搜尋，避免頻繁請求
      Future.delayed(const Duration(milliseconds: 500), () {
        if (searchController.text.trim() == query) {
          searchVideos(query);
        }
      });
    }
  }

  /// 應用篩選條件
  void _applyFilters() {
    List<VideoData> videos = List.from(_allVideos);
    _filteredVideos = _applyCurrentFilters(videos);
    notifyListeners();
  }

  /// 應用當前篩選條件到指定影片列表
  List<VideoData> _applyCurrentFilters(List<VideoData> videos) {
    List<VideoData> filtered = List.from(videos);

    // 分類篩選
    if (_selectedCategoryId.isNotEmpty) {
      final category = selectedCategory;
      if (category != null) {
        final categoryVideoIds = category.videos.map((v) => v.id).toSet();
        filtered = filtered.where((video) => categoryVideoIds.contains(video.id)).toList();
      }
    }

    // 標籤篩選
    if (_selectedTag.isNotEmpty) {
      filtered = filtered.where((video) => video.tags.contains(_selectedTag)).toList();
    }

    // 熱門篩選
    if (_showOnlyPopular) {
      filtered = filtered.where((video) => video.isPopular).toList();
    }

    return filtered;
  }

  /// 設置載入狀態
  void _setLoading(bool loading) {
    if (_isLoading != loading) {
      _isLoading = loading;
      notifyListeners();
    }
  }

  /// 設置錯誤狀態
  void _setError(String message) {
    _hasError = true;
    _errorMessage = message;
    notifyListeners();
  }

  /// 清除錯誤狀態
  void _clearError() {
    if (_hasError) {
      _hasError = false;
      _errorMessage = '';
      notifyListeners();
    }
  }

  /// 獲取統計資訊
  Map<String, dynamic> getStatistics() {
    return {
      'totalCategories': _categories.length,
      'totalVideos': _allVideos.length,
      'filteredVideos': _filteredVideos.length,
      'popularVideos': popularVideos.length,
      'totalTags': _allTags.length,
      'hasFilters': _selectedCategoryId.isNotEmpty || 
                   _selectedTag.isNotEmpty || 
                   _showOnlyPopular || 
                   _searchQuery.isNotEmpty,
    };
  }
}
