import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../astreal.dart';

/// 最近選中人物的ViewModel
class RecentPersonsViewModel extends ChangeNotifier {
  static const String _storageKey = 'recent_persons';
  static const int _maxRecentPersons = 20; // 最多保存20個最近選中的人物

  List<RecentPersonRecord> _recentPersons = [];

  /// 獲取最近選中的人物記錄
  List<RecentPersonRecord> get recentPersons => List.unmodifiable(_recentPersons);

  /// 初始化，從存儲中加載數據
  Future<void> initialize() async {
    await _loadRecentPersons();
  }

  /// 記錄選中的人物
  Future<void> recordSelectedPerson(BirthData person) async {
    try {
      // 查找是否已存在該人物的記錄（使用多重條件匹配）
      final existingIndex = _recentPersons.indexWhere(
        (record) => record.isSamePersonAsBirthData(person),
      );

      if (existingIndex != -1) {
        // 如果已存在，移除舊記錄，並在最前面添加新記錄（更新選中時間）
        final existingRecord = _recentPersons[existingIndex];
        _recentPersons.removeAt(existingIndex);
        _recentPersons.insert(0, RecentPersonRecord(
          personId: existingRecord.personId,
          name: existingRecord.name,
          birthDate: existingRecord.birthDate,
          birthPlace: existingRecord.birthPlace,
          latitude: existingRecord.latitude,
          longitude: existingRecord.longitude,
          selectedAt: DateTime.now(), // 更新選中時間
          usageCount: existingRecord.usageCount, // 保持原有使用次數
        ));
      } else {
        // 如果不存在，創建新記錄並添加到最前面
        _recentPersons.insert(0, RecentPersonRecord(
          personId: person.id,
          name: person.name,
          birthDate: person.dateTime,
          birthPlace: person.birthPlace,
          latitude: person.latitude,
          longitude: person.longitude,
          selectedAt: DateTime.now(),
        ));
      }

      // 限制最大數量
      if (_recentPersons.length > _maxRecentPersons) {
        _recentPersons = _recentPersons.take(_maxRecentPersons).toList();
      }

      // 保存到存儲
      await _saveRecentPersons();

      // 通知監聽器
      notifyListeners();
    } catch (e) {
      logger.e('記錄選中人物時出錯: $e');
    }
  }

  /// 獲取最近選中的人物（按選中時間排序，後選中的排前面）
  List<BirthData> getRecentPersons(List<BirthData> allPersons, {int maxCount = 5}) {
    try {
      // 按最後選中時間排序（後選中的排前面）
      final sortedRecords = List<RecentPersonRecord>.from(_recentPersons);
      sortedRecords.sort((a, b) => b.selectedAt.compareTo(a.selectedAt));

      final List<BirthData> recentPersons = [];
      final Set<String> addedPersonKeys = <String>{}; // 用於排除重複

      for (final record in sortedRecords) {
        // 如果已達到最大數量，停止添加
        if (recentPersons.length >= maxCount) break;

        // 創建唯一標識符來排除重複
        final personKey = '${record.name}_${record.birthDate.toIso8601String()}_${record.birthPlace}';

        // 如果已經添加過相同的人物，跳過
        if (addedPersonKeys.contains(personKey)) continue;

        try {
          // 使用多重條件匹配找到對應的人物
          final person = allPersons.firstWhere(
            (p) => record.isSamePersonAsBirthData(p),
          );
          recentPersons.add(person);
          addedPersonKeys.add(personKey);
        } catch (e) {
          // 如果找不到對應的人物，跳過
          logger.w('找不到匹配的人物: ${record.name} (${record.birthDate})');
        }
      }

      return recentPersons;
    } catch (e) {
      logger.e('獲取最近選中人物時出錯: $e');
      return [];
    }
  }

  /// 刪除指定人物的記錄
  Future<void> removePersonRecord(BirthData person) async {
    try {
      // 查找並移除匹配的記錄
      _recentPersons.removeWhere(
        (record) => record.isSamePersonAsBirthData(person),
      );

      // 保存到存儲
      await _saveRecentPersons();

      // 通知監聽器
      notifyListeners();
    } catch (e) {
      logger.e('刪除人物記錄時出錯: $e');
    }
  }

  /// 清空所有記錄
  Future<void> clearAllRecords() async {
    try {
      _recentPersons.clear();

      // 保存到存儲
      await _saveRecentPersons();

      // 通知監聽器
      notifyListeners();
    } catch (e) {
      logger.e('清空記錄時出錯: $e');
    }
  }

  /// 從存儲中加載最近選中的人物
  Future<void> _loadRecentPersons() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString(_storageKey);

      if (jsonString != null) {
        final List<dynamic> jsonList = json.decode(jsonString);
        _recentPersons = [];

        for (final json in jsonList) {
          try {
            // 檢查是否為新格式（包含所有必要字段）
            if (json is Map<String, dynamic> &&
                json.containsKey('name') &&
                json.containsKey('birthDate') &&
                json.containsKey('birthPlace') &&
                json.containsKey('latitude') &&
                json.containsKey('longitude')) {
              _recentPersons.add(RecentPersonRecord.fromJson(json));
            }
            // 忽略舊格式的記錄
          } catch (e) {
            // 跳過無法解析的記錄
            logger.w('跳過無法解析的記錄: $e');
          }
        }
      }
    } catch (e) {
      logger.e('加載最近選中人物時出錯: $e');
      // 如果加載失敗，清除舊數據並重新開始
      _recentPersons = [];
      try {
        final prefs = await SharedPreferences.getInstance();
        await prefs.remove(_storageKey);
      } catch (clearError) {
        logger.w('清除舊數據時出錯: $clearError');
      }
    }
  }

  /// 移除特定的近期記錄
  Future<void> removeRecentPerson(String personId) async {
    try {
      _recentPersons.removeWhere((record) => record.personId == personId);
      await _saveRecentPersons();
      notifyListeners();
      logger.i('已移除近期記錄: $personId');
    } catch (e) {
      logger.e('移除近期記錄時出錯: $e');
    }
  }

  /// 保存最近選中的人物到存儲
  Future<void> _saveRecentPersons() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = json.encode(
        _recentPersons.map((record) => record.toJson()).toList(),
      );
      await prefs.setString(_storageKey, jsonString);
    } catch (e) {
      logger.e('保存最近選中人物時出錯: $e');
    }
  }
}
