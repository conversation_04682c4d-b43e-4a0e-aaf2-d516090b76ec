import 'dart:convert';
import 'dart:io';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart' as foundation;
import 'package:http/http.dart' as http;

import '../../core/config/firebase_config_windows.dart';
import '../../core/constants/firebase_collections.dart';
import '../../core/utils/logger_utils.dart';
import 'user_profile_repository.dart';

/// 用戶解讀次數管理擴展
/// 專門處理與 interpretation_credits 相關的操作
class UserProfileCreditsRepository {

  // ==================== 解讀次數操作 ====================

  /// 獲取用戶的解讀次數
  static Future<int> getInterpretationCredits(String userId) async {
    try {
      logger.i('獲取用戶解讀次數: $userId');

      final userProfile = await UserProfileRepository.getUserById(userId);
      if (userProfile != null) {
        logger.i('用戶 $userId 的解讀次數: ${userProfile.interpretationCredits}');
        return userProfile.interpretationCredits;
      } else {
        logger.w('用戶不存在: $userId');
        return 0;
      }
    } catch (e) {
      logger.e('獲取解讀次數失敗: $e');
      return -1; // 返回 -1 表示獲取失敗
    }
  }

  /// 添加解讀次數
  static Future<bool> addInterpretationCredits(String userId, int count) async {
    try {
      logger.i('為用戶 $userId 添加 $count 個解讀次數');

      if (_shouldUseRestApi()) {
        return await _addCreditsViaRestApi(userId, count);
      } else {
        return await _addCreditsViaSDK(userId, count);
      }
    } catch (e) {
      logger.e('添加解讀次數失敗: $e');
      return false;
    }
  }

  /// 使用一次解讀次數
  static Future<bool> useInterpretationCredit(String userId) async {
    try {
      logger.i('用戶 $userId 使用一次解讀次數');

      if (_shouldUseRestApi()) {
        return await _useCreditsViaRestApi(userId, 1);
      } else {
        return await _useCreditsViaSDK(userId, 1);
      }
    } catch (e) {
      logger.e('使用解讀次數失敗: $e');
      return false;
    }
  }

  /// 批量使用解讀次數
  static Future<bool> useInterpretationCredits(String userId, int count) async {
    try {
      logger.i('用戶 $userId 使用 $count 個解讀次數');

      if (_shouldUseRestApi()) {
        return await _useCreditsViaRestApi(userId, count);
      } else {
        return await _useCreditsViaSDK(userId, count);
      }
    } catch (e) {
      logger.e('使用解讀次數失敗: $e');
      return false;
    }
  }

  /// 設置解讀次數（直接設定為指定數量）
  static Future<bool> setInterpretationCredits(String userId, int count) async {
    try {
      logger.i('設置用戶 $userId 的解讀次數為 $count');

      if (count < 0) {
        logger.w('解讀次數不能為負數');
        return false;
      }

      if (_shouldUseRestApi()) {
        return await _setCreditsViaRestApi(userId, count);
      } else {
        return await _setCreditsViaSDK(userId, count);
      }
    } catch (e) {
      logger.e('設置解讀次數失敗: $e');
      return false;
    }
  }

  /// 重置解讀次數為 0
  static Future<bool> resetInterpretationCredits(String userId) async {
    return await setInterpretationCredits(userId, 0);
  }

  // ==================== 批量操作 ====================

  /// 批量添加解讀次數
  static Future<Map<String, bool>> batchAddInterpretationCredits(
    Map<String, int> userCredits,
  ) async {
    final results = <String, bool>{};
    
    for (final entry in userCredits.entries) {
      final userId = entry.key;
      final count = entry.value;
      results[userId] = await addInterpretationCredits(userId, count);
    }
    
    return results;
  }

  /// 批量設置解讀次數
  static Future<Map<String, bool>> batchSetInterpretationCredits(
    Map<String, int> userCredits,
  ) async {
    final results = <String, bool>{};
    
    for (final entry in userCredits.entries) {
      final userId = entry.key;
      final count = entry.value;
      results[userId] = await setInterpretationCredits(userId, count);
    }
    
    return results;
  }

  // ==================== 統計操作 ====================

  /// 獲取解讀次數統計
  static Future<Map<String, dynamic>> getCreditsStatistics() async {
    try {
      logger.i('獲取解讀次數統計...');

      final users = await UserProfileRepository.getAllUsers();
      
      int totalCredits = 0;
      int usersWithCredits = 0;
      int maxCredits = 0;
      int minCredits = users.isNotEmpty ? users.first.interpretationCredits : 0;
      
      for (final user in users) {
        final credits = user.interpretationCredits;
        totalCredits += credits;
        
        if (credits > 0) {
          usersWithCredits++;
        }
        
        if (credits > maxCredits) {
          maxCredits = credits;
        }
        
        if (credits < minCredits) {
          minCredits = credits;
        }
      }
      
      final avgCredits = users.isNotEmpty ? totalCredits / users.length : 0.0;
      
      final stats = {
        'totalUsers': users.length,
        'totalCredits': totalCredits,
        'usersWithCredits': usersWithCredits,
        'usersWithoutCredits': users.length - usersWithCredits,
        'averageCredits': avgCredits,
        'maxCredits': maxCredits,
        'minCredits': minCredits,
      };
      
      logger.i('解讀次數統計: $stats');
      return stats;
    } catch (e) {
      logger.e('獲取解讀次數統計失敗: $e');
      return {};
    }
  }

  // ==================== 私有方法 - REST API ====================

  /// 通過 REST API 添加解讀次數
  static Future<bool> _addCreditsViaRestApi(String userId, int count) async {
    try {
      // 先獲取當前次數
      final currentCredits = await getInterpretationCredits(userId);
      if (currentCredits < 0) return false; // 獲取失敗
      
      final newCredits = currentCredits + count;
      return await _setCreditsViaRestApi(userId, newCredits);
    } catch (e) {
      logger.e('REST API 添加解讀次數失敗: $e');
      return false;
    }
  }

  /// 通過 REST API 使用解讀次數
  static Future<bool> _useCreditsViaRestApi(String userId, int count) async {
    try {
      // 先獲取當前次數
      final currentCredits = await getInterpretationCredits(userId);
      if (currentCredits < count) {
        logger.w('解讀次數不足: 當前 $currentCredits，需要 $count');
        return false;
      }
      
      final newCredits = currentCredits - count;
      return await _setCreditsViaRestApi(userId, newCredits);
    } catch (e) {
      logger.e('REST API 使用解讀次數失敗: $e');
      return false;
    }
  }

  /// 通過 REST API 設置解讀次數
  static Future<bool> _setCreditsViaRestApi(String userId, int count) async {
    try {
      final url = Uri.parse(
        '${FirebaseConfigWindows.firestoreBaseUrl}/projects/${FirebaseConfigWindows.projectId}/databases/(default)/documents/${FirebaseCollections.userProfiles}/$userId'
      );

      final data = {
        'fields': {
          'interpretation_credits': {'integerValue': count.toString()},
          'credits_last_updated': {'timestampValue': DateTime.now().toUtc().toIso8601String()},
          'updated_at': {'timestampValue': DateTime.now().toUtc().toIso8601String()},
        }
      };

      final response = await http.patch(
        url,
        headers: {'Content-Type': 'application/json'},
        body: json.encode(data),
      );

      if (response.statusCode == 200) {
        logger.i('REST API 設置解讀次數成功: $userId -> $count');
        return true;
      } else {
        logger.e('REST API 設置解讀次數失敗: ${response.statusCode} - ${response.body}');
        return false;
      }
    } catch (e) {
      logger.e('REST API 設置解讀次數失敗: $e');
      return false;
    }
  }

  // ==================== 私有方法 - SDK ====================

  /// 通過 SDK 添加解讀次數
  static Future<bool> _addCreditsViaSDK(String userId, int count) async {
    try {
      final docRef = FirebaseFirestore.instance.collection(FirebaseCollections.userProfiles).doc(userId);
      
      await docRef.update({
        'interpretation_credits': FieldValue.increment(count),
        'credits_last_updated': FieldValue.serverTimestamp(),
        'updated_at': FieldValue.serverTimestamp(),
      });
      
      logger.i('SDK 添加解讀次數成功: $userId + $count');
      return true;
    } on FirebaseException catch (e) {
      logger.e('Firestore 錯誤 - 添加解讀次數失敗: ${e.code} - ${e.message}');
      return false;
    } catch (e) {
      logger.e('SDK 添加解讀次數失敗: $e');
      return false;
    }
  }

  /// 通過 SDK 使用解讀次數
  static Future<bool> _useCreditsViaSDK(String userId, int count) async {
    try {
      final docRef = FirebaseFirestore.instance.collection(FirebaseCollections.userProfiles).doc(userId);
      
      // 使用事務確保原子性
      return await FirebaseFirestore.instance.runTransaction((transaction) async {
        final snapshot = await transaction.get(docRef);
        
        if (!snapshot.exists) {
          logger.w('用戶不存在: $userId');
          return false;
        }
        
        final data = snapshot.data()!;
        final currentCredits = data['interpretation_credits'] as int? ?? 0;
        
        if (currentCredits < count) {
          logger.w('解讀次數不足: 當前 $currentCredits，需要 $count');
          return false;
        }
        
        final newCredits = currentCredits - count;
        
        transaction.update(docRef, {
          'interpretation_credits': newCredits,
          'credits_last_updated': FieldValue.serverTimestamp(),
          'updated_at': FieldValue.serverTimestamp(),
        });
        
        logger.i('SDK 使用解讀次數成功: $userId - $count = $newCredits');
        return true;
      });
    } on FirebaseException catch (e) {
      logger.e('Firestore 錯誤 - 使用解讀次數失敗: ${e.code} - ${e.message}');
      return false;
    } catch (e) {
      logger.e('SDK 使用解讀次數失敗: $e');
      return false;
    }
  }

  /// 通過 SDK 設置解讀次數
  static Future<bool> _setCreditsViaSDK(String userId, int count) async {
    try {
      final docRef = FirebaseFirestore.instance.collection(FirebaseCollections.userProfiles).doc(userId);
      
      await docRef.update({
        'interpretation_credits': count,
        'credits_last_updated': FieldValue.serverTimestamp(),
        'updated_at': FieldValue.serverTimestamp(),
      });
      
      logger.i('SDK 設置解讀次數成功: $userId -> $count');
      return true;
    } on FirebaseException catch (e) {
      logger.e('Firestore 錯誤 - 設置解讀次數失敗: ${e.code} - ${e.message}');
      return false;
    } catch (e) {
      logger.e('SDK 設置解讀次數失敗: $e');
      return false;
    }
  }

  // ==================== 輔助方法 ====================

  /// 判斷是否使用 REST API
  static bool _shouldUseRestApi() {
    return Platform.isWindows && !foundation.kDebugMode;
  }
}
