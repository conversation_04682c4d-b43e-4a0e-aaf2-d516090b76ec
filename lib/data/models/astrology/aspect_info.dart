import 'planet_position.dart';

/// 互容接納類型
enum ReceptionType {
  none, // 無互容接納
  reception, // 互容（不需要相位）
  mutualReception, // 互相互容（不需要相位）
  acceptance, // 接納（需要相位）
  mutualAcceptance, // 互容接納（需要相位）
}

/// 相位方向（入相或出相）
enum AspectDirection {
  applying, // 入相
  separating, // 出相
}

/// 相位信息模型类
class AspectInfo {
  final PlanetPosition planet1;
  final PlanetPosition planet2;
  final String aspect;
  final String shortZh; // 添加中文簡稱
  final String symbol;
  final int angle; // 修改為 int 類型
  final double orb;
  final ReceptionType receptionType; // 互容接納類型
  final String? receptionDescription; // 互容接納描述
  final AspectDirection? direction; // 相位方向（入相或出相）

  AspectInfo({
    required this.planet1,
    required this.planet2,
    required this.aspect,
    required this.shortZh, // 添加中文簡稱
    required this.symbol,
    required this.angle,
    required this.orb,
    this.receptionType = ReceptionType.none,
    this.receptionDescription,
    this.direction,
  });

  /// 从 JSON 创建 AspectInfo 对象
  factory AspectInfo.fromJson(Map<String, dynamic> json) {
    return AspectInfo(
      planet1: PlanetPosition.fromJson(json['planet1'] as Map<String, dynamic>),
      planet2: PlanetPosition.fromJson(json['planet2'] as Map<String, dynamic>),
      aspect: json['aspect'] as String,
      shortZh: json['shortZh'] as String,
      // 添加中文簡稱
      symbol: json['symbol'] as String,
      angle: json['angle'] as int,
      // 修改為 int 類型
      orb: json['orb'].toDouble(),
      receptionType: json['receptionType'] != null
          ? ReceptionType.values[json['receptionType'] as int]
          : ReceptionType.none,
      receptionDescription: json['receptionDescription'] as String?,
      direction: json['direction'] != null
          ? AspectDirection.values[json['direction'] as int]
          : null,
    );
  }

  /// 将 AspectInfo 对象转换为 JSON
  Map<String, dynamic> toJson() {
    return {
      'planet1': planet1.toJson(),
      'planet2': planet2.toJson(),
      'aspect': aspect,
      'shortZh': shortZh, // 添加中文簡稱
      'symbol': symbol,
      'angle': angle,
      'orb': orb,
      'receptionType': receptionType.index,
      'receptionDescription': receptionDescription,
      'direction': direction?.index,
    };
  }

  /// 獲取互容接納的文字描述
  String getReceptionText() {
    switch (receptionType) {
      case ReceptionType.reception:
        return '互容';
      case ReceptionType.mutualReception:
        return '互相互容';
      case ReceptionType.acceptance:
        return '接納';
      case ReceptionType.mutualAcceptance:
        return '互容接納';
      case ReceptionType.none:
        return '';
    }
  }

  /// 獲取入相或出相的文字描述
  String getDirectionText() {
    if (direction == null) {
      return '';
    } else if (direction == AspectDirection.applying) {
      return '入相';
    } else {
      return '出相';
    }
  }
}
