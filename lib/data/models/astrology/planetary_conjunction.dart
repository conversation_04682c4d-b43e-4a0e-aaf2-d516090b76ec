/// 行星會合數據模型
class PlanetaryConjunction {
  /// 會合類型
  final ConjunctionType type;
  
  /// 會合時間
  final DateTime dateTime;
  
  /// 會合時的經度位置
  final double longitude;
  
  /// 會合時的星座
  final String zodiacSign;
  
  /// 會合的精確度（角度差）
  final double exactness;
  
  /// 會合的財務意義
  final String financialMeaning;
  
  /// 投資建議
  final String investmentAdvice;
  
  /// 是否為歷史會合
  final bool isHistorical;

  const PlanetaryConjunction({
    required this.type,
    required this.dateTime,
    required this.longitude,
    required this.zodiacSign,
    required this.exactness,
    required this.financialMeaning,
    required this.investmentAdvice,
    required this.isHistorical,
  });

  /// 從JSON創建對象
  factory PlanetaryConjunction.fromJson(Map<String, dynamic> json) {
    return PlanetaryConjunction(
      type: ConjunctionType.values.firstWhere(
        (e) => e.toString() == json['type'],
        orElse: () => ConjunctionType.jupiterSaturn,
      ),
      dateTime: DateTime.parse(json['dateTime']),
      longitude: json['longitude']?.toDouble() ?? 0.0,
      zodiacSign: json['zodiacSign'] ?? '',
      exactness: json['exactness']?.toDouble() ?? 0.0,
      financialMeaning: json['financialMeaning'] ?? '',
      investmentAdvice: json['investmentAdvice'] ?? '',
      isHistorical: json['isHistorical'] ?? false,
    );
  }

  /// 轉換為JSON
  Map<String, dynamic> toJson() {
    return {
      'type': type.toString(),
      'dateTime': dateTime.toIso8601String(),
      'longitude': longitude,
      'zodiacSign': zodiacSign,
      'exactness': exactness,
      'financialMeaning': financialMeaning,
      'investmentAdvice': investmentAdvice,
      'isHistorical': isHistorical,
    };
  }

  /// 獲取會合類型的中文名稱
  String get typeName {
    switch (type) {
      case ConjunctionType.jupiterSaturn:
        return '木土會合';
      case ConjunctionType.marsSaturn:
        return '火土會合';
    }
  }

  /// 獲取會合類型的英文名稱
  String get typeNameEn {
    switch (type) {
      case ConjunctionType.jupiterSaturn:
        return 'Jupiter-Saturn Conjunction';
      case ConjunctionType.marsSaturn:
        return 'Mars-Saturn Conjunction';
    }
  }

  /// 獲取會合週期描述
  String get cycleDescription {
    switch (type) {
      case ConjunctionType.jupiterSaturn:
        return '約20年一次的重要經濟週期';
      case ConjunctionType.marsSaturn:
        return '約2年一次的財務調整期';
    }
  }

  /// 獲取會合的重要性等級
  ConjunctionImportance get importance {
    switch (type) {
      case ConjunctionType.jupiterSaturn:
        return ConjunctionImportance.high;
      case ConjunctionType.marsSaturn:
        return ConjunctionImportance.medium;
    }
  }

  /// 獲取距離當前時間的描述
  String getTimeDescription() {
    final now = DateTime.now();
    final difference = dateTime.difference(now);
    
    if (isHistorical) {
      final daysPast = difference.inDays.abs();
      if (daysPast < 30) {
        return '${daysPast}天前';
      } else if (daysPast < 365) {
        final monthsPast = (daysPast / 30).round();
        return '${monthsPast}個月前';
      } else {
        final yearsPast = (daysPast / 365).round();
        return '${yearsPast}年前';
      }
    } else {
      final daysFuture = difference.inDays;
      if (daysFuture < 30) {
        return '${daysFuture}天後';
      } else if (daysFuture < 365) {
        final monthsFuture = (daysFuture / 30).round();
        return '${monthsFuture}個月後';
      } else {
        final yearsFuture = (daysFuture / 365).round();
        return '${yearsFuture}年後';
      }
    }
  }

  @override
  String toString() {
    return 'PlanetaryConjunction(type: $type, dateTime: $dateTime, zodiacSign: $zodiacSign)';
  }
}

/// 行星會合類型
enum ConjunctionType {
  /// 木土會合
  jupiterSaturn,
  /// 火土會合
  marsSaturn,
}

/// 會合重要性等級
enum ConjunctionImportance {
  /// 高重要性
  high,
  /// 中等重要性
  medium,
  /// 低重要性
  low,
}

/// 行星會合分析結果
class ConjunctionAnalysisResult {
  /// 歷史會合列表
  final List<PlanetaryConjunction> historicalConjunctions;
  
  /// 未來會合列表
  final List<PlanetaryConjunction> futureConjunctions;
  
  /// 當前最接近的會合
  final PlanetaryConjunction? nearestConjunction;
  
  /// 分析摘要
  final String analysisSummary;
  
  /// 投資建議摘要
  final String investmentSummary;

  const ConjunctionAnalysisResult({
    required this.historicalConjunctions,
    required this.futureConjunctions,
    this.nearestConjunction,
    required this.analysisSummary,
    required this.investmentSummary,
  });

  /// 獲取所有會合
  List<PlanetaryConjunction> get allConjunctions {
    return [...historicalConjunctions, ...futureConjunctions];
  }

  /// 獲取指定類型的會合
  List<PlanetaryConjunction> getConjunctionsByType(ConjunctionType type) {
    return allConjunctions.where((c) => c.type == type).toList();
  }

  /// 獲取指定時間範圍內的會合
  List<PlanetaryConjunction> getConjunctionsInRange(DateTime start, DateTime end) {
    return allConjunctions.where((c) => 
      c.dateTime.isAfter(start) && c.dateTime.isBefore(end)
    ).toList();
  }
}
