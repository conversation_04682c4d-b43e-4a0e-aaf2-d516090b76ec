import 'package:flutter/material.dart';

import '../../../astreal.dart';
/// 出生資料類別枚舉
enum ChartCategory {
  personal('個人', Icons.person, AppColors.royalIndigo),
  family('家人', Icons.family_restroom, AppColors.solarAmber),
  friend('朋友', Icons.people, AppColors.indigoLight),
  partner('伴侶', Icons.favorite, Colors.pink),
  colleague('同事', Icons.groups, Colors.blue),
  classmate('同學', Icons.school, Colors.cyan),
  celebrity('名人', Icons.star, Colors.amber),
  pet('寵物', Icons.pets, Colors.brown),
  work('工作', Icons.work, Colors.green),
  client('客戶', Icons.business, Colors.orange),
  research('研究', Icons.science, Colors.indigo),
  event('事件', Icons.event, Colors.purple),
  celestial('天象', Icons.public, Colors.teal),
  other('其他', Icons.more_horiz, Colors.grey);

  const ChartCategory(this.displayName, this.icon, this.color);

  /// 顯示名稱
  final String displayName;
  
  /// 圖標
  final IconData icon;
  
  /// 顏色
  final Color color;

  /// 從字符串值獲取枚舉
  static ChartCategory fromString(String value) {
    switch (value) {
      case '個人':
        return ChartCategory.personal;
      case '家人':
        return ChartCategory.family;
      case '朋友':
        return ChartCategory.friend;
      case '伴侶':
        return ChartCategory.partner;
      case '同事':
        return ChartCategory.colleague;
      case '同學':
        return ChartCategory.classmate;
      case '名人':
        return ChartCategory.celebrity;
      case '寵物':
        return ChartCategory.pet;
      case '工作':
        return ChartCategory.work;
      case '客戶':
        return ChartCategory.client;
      case '研究':
        return ChartCategory.research;
      case '事件':
        return ChartCategory.event;
      case '天象':
        return ChartCategory.celestial;
      case '其他':
        return ChartCategory.other;
      default:
        return ChartCategory.personal; // 默認為個人
    }
  }

  /// 轉換為字符串值（用於存儲）
  String toStorageString() {
    return displayName;
  }

  /// 獲取所有類別列表
  static List<ChartCategory> get allCategories => ChartCategory.values;

  /// 獲取個人相關類別
  static List<ChartCategory> get personalCategories => [
    ChartCategory.personal,
    ChartCategory.family,
    ChartCategory.friend,
    ChartCategory.partner,
    ChartCategory.colleague,
    ChartCategory.classmate,
    ChartCategory.pet,
  ];

  /// 獲取專業相關類別
  static List<ChartCategory> get professionalCategories => [
    ChartCategory.work,
    ChartCategory.client,
    ChartCategory.research,
  ];

  /// 獲取特殊類別
  static List<ChartCategory> get specialCategories => [
    ChartCategory.celebrity,
    ChartCategory.event,
    ChartCategory.celestial,
    ChartCategory.other,
  ];

  @override
  String toString() => displayName;
}
