import 'package:flutter/material.dart';

import '../../../features/astrology/constants/zodiac_definitions.dart';

/// 星座模型
class ZodiacSign {
  /// 星座 ID (1-12)
  final int id;

  /// 星座名稱
  final String name;

  /// 英文名稱
  final String englishName;

  /// 星座符號
  final String symbol;

  /// Unicode 符號
  final String unicode;

  /// 星座元素（火、土、風、水）
  final String element;

  /// 星座性質（基本、固定、變動）
  final String quality;

  /// 星座極性（陽性、陰性）
  final String polarity;

  /// 主宰行星
  final String ruler;

  /// 旺星
  final String? exaltationRuler;

  /// 陷星
  final String? detrimentRuler;

  /// 弱星
  final String? fallRuler;

  /// 起始度數
  final double startDegree;

  /// 結束度數
  final double endDegree;

  /// 顏色
  final Color color;

  /// 季節
  final String season;

  /// 身體部位
  final List<String> bodyPart;

  /// 關鍵詞
  final List<String> keywords;

  /// 正面特質
  final List<String> positiveTraits;

  /// 負面特質
  final List<String> negativeTraits;

  /// 描述
  final String description;

  /// 神話故事
  final String mythology;

  /// 在星座內的度數（0-29.999...）
  final double? degreeInSign;

  /// 格式化的度數分秒字符串（如 "25°30'45""）
  final String? formattedDegree;

  const ZodiacSign({
    required this.id,
    required this.name,
    required this.englishName,
    required this.symbol,
    required this.unicode,
    required this.element,
    required this.quality,
    required this.polarity,
    required this.ruler,
    this.exaltationRuler,
    this.detrimentRuler,
    this.fallRuler,
    required this.startDegree,
    required this.endDegree,
    required this.color,
    required this.season,
    this.bodyPart = const [],
    this.keywords = const [],
    this.positiveTraits = const [],
    this.negativeTraits = const [],
    this.description = '',
    this.mythology = '',
    this.degreeInSign,
    this.formattedDegree,
  });

  /// 從 Map 創建星座對象
  factory ZodiacSign.fromMap(Map<String, dynamic> map) {
    return ZodiacSign(
      id: map['id'] as int,
      name: map['name'] as String,
      englishName: map['englishName'] as String? ?? '',
      symbol: map['symbol'] as String,
      unicode: map['unicode'] as String? ?? map['symbol'] as String,
      element: map['element'] as String,
      quality: map['quality'] as String,
      polarity: map['polarity'] as String? ?? (map['isPositive'] == true ? '陽性' : '陰性'),
      ruler: map['ruler'] as String? ?? map['rulingPlanet'] as String,
      exaltationRuler: map['exaltationRuler'] as String?,
      detrimentRuler: map['detrimentRuler'] as String?,
      fallRuler: map['fallRuler'] as String?,
      startDegree: (map['startDegree'] as num?)?.toDouble() ?? ((map['id'] as int) - 1) * 30.0,
      endDegree: (map['endDegree'] as num?)?.toDouble() ?? (map['id'] as int) * 30.0,
      color: map['color'] as Color? ?? Colors.grey,
      season: map['season'] as String? ?? '',
      bodyPart: (map['bodyPart'] as List<dynamic>?)?.cast<String>() ?? [],
      keywords: (map['keywords'] as List<dynamic>?)?.cast<String>() ?? [],
      positiveTraits: (map['positiveTraits'] as List<dynamic>?)?.cast<String>() ?? [],
      negativeTraits: (map['negativeTraits'] as List<dynamic>?)?.cast<String>() ?? [],
      description: map['description'] as String? ?? '',
      mythology: map['mythology'] as String? ?? '',
      degreeInSign: (map['degreeInSign'] as num?)?.toDouble(),
      formattedDegree: map['formattedDegree'] as String?,
    );
  }

  /// 從星座 ID 創建星座對象
  factory ZodiacSign.fromId(int id) {
    // 使用 ZodiacDefinitions 的類型安全方法
    final zodiacSign = ZodiacDefinitions.getSignByIdTyped(id);
    if (zodiacSign == null) {
      // 如果找不到，返回牡羊座
      return ZodiacDefinitions.getSignByIdTyped(1)!;
    }
    return zodiacSign;
  }

  /// 從經度創建帶度數信息的星座對象
  factory ZodiacSign.fromLongitude(double longitude) {
    // 計算星座索引
    final signIndex = (longitude / 30).floor() + 1;

    // 獲取基本星座信息
    final baseSign = ZodiacDefinitions.getSignByIdTyped(signIndex);
    if (baseSign == null) {
      // 如果找不到，返回牡羊座
      final defaultSign = ZodiacDefinitions.getSignByIdTyped(1)!;
      return _addDegreeInfo(defaultSign, longitude);
    }

    return _addDegreeInfo(baseSign, longitude);
  }

  /// 為星座添加度數信息的私有方法
  static ZodiacSign _addDegreeInfo(ZodiacSign baseSign, double longitude) {
    // 計算在星座內的度數
    final degreeInSign = longitude % 30;
    final degree = degreeInSign.floor();
    final minute = ((degreeInSign - degree) * 60).floor();
    final second = (((degreeInSign - degree) * 60 - minute) * 60).floor();

    // 格式化度數字符串
    final formattedDegree = '${degree.toString().padLeft(2, '0')}°'
        '${minute.toString().padLeft(2, '0')}\''
        '${second.toString().padLeft(2, '0')}"';

    // 創建新的 ZodiacSign 對象，包含度數信息
    return ZodiacSign(
      id: baseSign.id,
      name: baseSign.name,
      englishName: baseSign.englishName,
      symbol: baseSign.symbol,
      unicode: baseSign.unicode,
      element: baseSign.element,
      quality: baseSign.quality,
      polarity: baseSign.polarity,
      ruler: baseSign.ruler,
      exaltationRuler: baseSign.exaltationRuler,
      detrimentRuler: baseSign.detrimentRuler,
      fallRuler: baseSign.fallRuler,
      startDegree: baseSign.startDegree,
      endDegree: baseSign.endDegree,
      color: baseSign.color,
      season: baseSign.season,
      bodyPart: baseSign.bodyPart,
      keywords: baseSign.keywords,
      positiveTraits: baseSign.positiveTraits,
      negativeTraits: baseSign.negativeTraits,
      description: baseSign.description,
      mythology: baseSign.mythology,
      degreeInSign: degreeInSign,
      formattedDegree: formattedDegree,
    );
  }

  /// 便利屬性
  /// 是否為陽性星座
  bool get isPositive => polarity == '陽性';

  /// 是否為陰性星座
  bool get isNegative => polarity == '陰性';

  /// 是否為火象星座
  bool get isFireSign => element == '火';

  /// 是否為土象星座
  bool get isEarthSign => element == '土';

  /// 是否為風象星座
  bool get isAirSign => element == '風';

  /// 是否為水象星座
  bool get isWaterSign => element == '水';

  /// 是否為基本星座
  bool get isCardinalSign => quality == '基本';

  /// 是否為固定星座
  bool get isFixedSign => quality == '固定';

  /// 是否為變動星座
  bool get isMutableSign => quality == '變動';

  /// 檢查給定度數是否在此星座範圍內
  bool containsDegree(double longitude) {
    final normalizedLongitude = longitude % 360;
    return normalizedLongitude >= startDegree && normalizedLongitude < endDegree;
  }

  /// 獲取度數（整數部分）
  int? get degree => degreeInSign?.floor();

  /// 獲取分數（0-59）
  int? get minute {
    if (degreeInSign == null) return null;
    final fractionalPart = degreeInSign! - degreeInSign!.floor();
    return (fractionalPart * 60).floor();
  }

  /// 獲取秒數（0-59）
  int? get second {
    if (degreeInSign == null) return null;
    final fractionalPart = degreeInSign! - degreeInSign!.floor();
    final minuteFractional = (fractionalPart * 60) - (fractionalPart * 60).floor();
    return (minuteFractional * 60).floor();
  }

  /// 獲取簡化的度數分數字符串（如 "25°30'"）
  String? get simpleDegreeString {
    if (degreeInSign == null) return null;
    final deg = degree!;
    final min = minute!;
    return '${deg.toString().padLeft(2, '0')}°${min.toString().padLeft(2, '0')}\'';
  }

  /// 檢查是否有度數信息
  bool get hasDegreeInfo => degreeInSign != null;

  /// 轉換為 JSON/Map（向後兼容）
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'englishName': englishName,
      'symbol': symbol,
      'unicode': unicode,
      'element': element,
      'quality': quality,
      'polarity': polarity,
      'ruler': ruler,
      'exaltationRuler': exaltationRuler,
      'detrimentRuler': detrimentRuler,
      'fallRuler': fallRuler,
      'startDegree': startDegree,
      'endDegree': endDegree,
      'color': color,
      'season': season,
      'bodyPart': bodyPart,
      'keywords': keywords,
      'positiveTraits': positiveTraits,
      'negativeTraits': negativeTraits,
      'description': description,
      'mythology': mythology,
      'degreeInSign': degreeInSign,
      'formattedDegree': formattedDegree,
      // 向後兼容的屬性
      'isPositive': polarity == '陽性',
      'rulingPlanet': ruler,
      'modernRulingPlanet': exaltationRuler,
      // 度數相關的向後兼容屬性
      'degree': formattedDegree ?? '',
    };
  }

  /// 轉換為 Map（別名）
  Map<String, dynamic> toMap() => toJson();

  /// 從 JSON 創建星座對象
  factory ZodiacSign.fromJson(Map<String, dynamic> json) {
    return ZodiacSign.fromMap(json);
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ZodiacSign && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'ZodiacSign(id: $id, name: $name, symbol: $symbol)';
  }

  /// 獲取所有星座
  static List<ZodiacSign> get allSigns {
    return List.generate(12, (index) => ZodiacSign.fromId(index + 1));
  }

  /// 獲取星座的對宮星座
  ZodiacSign get oppositeSign {
    final oppositeId = ((id + 5) % 12) + 1;
    return ZodiacSign.fromId(oppositeId);
  }

  /// 獲取同元素的星座
  List<ZodiacSign> get sameElementSigns {
    return allSigns.where((sign) => sign.element == element).toList();
  }

  /// 獲取同性質的星座
  List<ZodiacSign> get sameQualitySigns {
    return allSigns.where((sign) => sign.quality == quality).toList();
  }
}
