import 'package:flutter/material.dart';

/// 相位數據模型
class Aspect {
  final String name;
  final String shortName;
  final String shortZh;
  final int angle;
  final double orb;
  final String symbol;
  final Color color;
  final String description;
  final String nature; // 'harmonious', 'challenging', 'neutral'
  final double strength; // 相位強度 0.0-1.0
  final List<String> keywords;
  final String? traditionalName;

  const Aspect({
    required this.name,
    required this.shortName,
    required this.shortZh,
    required this.angle,
    required this.orb,
    required this.symbol,
    required this.color,
    this.description = '',
    this.nature = 'neutral',
    this.strength = 1.0,
    this.keywords = const [],
    this.traditionalName,
  });

  /// 從 Map 創建 Aspect 實例
  factory Aspect.fromMap(Map<String, dynamic> map) {
    return Aspect(
      name: map['name'] as String,
      shortName: map['short'] as String? ?? map['shortName'] as String? ?? '',
      shortZh: map['shortZh'] as String? ?? '',
      angle: map['angle'] as int,
      orb: (map['orb'] as num).toDouble(),
      symbol: map['symbol'] as String,
      color: map['color'] as Color? ?? Colors.grey,
      description: map['description'] as String? ?? '',
      nature: map['nature'] as String? ?? _determineNature(map['angle'] as int),
      strength: (map['strength'] as num?)?.toDouble() ?? 1.0,
      keywords: (map['keywords'] as List<dynamic>?)?.cast<String>() ?? [],
      traditionalName: map['traditionalName'] as String?,
    );
  }

  /// 轉換為 Map
  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'short': shortName,
      'shortZh': shortZh,
      'angle': angle,
      'orb': orb,
      'symbol': symbol,
      'color': color,
      'description': description,
      'nature': nature,
      'strength': strength,
      'keywords': keywords,
      'traditionalName': traditionalName,
    };
  }

  /// 根據角度判斷相位性質
  static String _determineNature(int angle) {
    switch (angle) {
      case 0: // 合相
      case 60: // 六分相
      case 120: // 三分相
        return 'harmonious';
      case 90: // 四分相
      case 180: // 對分相
        return 'challenging';
      default:
        return 'neutral';
    }
  }

  /// 是否為主要相位
  bool get isMajorAspect => [0, 60, 90, 120, 180].contains(angle);

  /// 是否為次要相位
  bool get isMinorAspect => !isMajorAspect;

  /// 是否為和諧相位
  bool get isHarmonious => nature == 'harmonious';

  /// 是否為挑戰相位
  bool get isChallenging => nature == 'challenging';

  /// 是否為中性相位
  bool get isNeutral => nature == 'neutral';

  /// 獲取相位的強度等級
  String get strengthLevel {
    if (strength >= 0.8) return '強';
    if (strength >= 0.6) return '中強';
    if (strength >= 0.4) return '中等';
    if (strength >= 0.2) return '弱';
    return '極弱';
  }

  /// 獲取相位的影響描述
  String get influenceDescription {
    switch (nature) {
      case 'harmonious':
        return '和諧流暢的能量，有利於發展和成長';
      case 'challenging':
        return '具有挑戰性的能量，需要努力整合';
      case 'neutral':
        return '中性能量，影響取決於具體情況';
      default:
        return description;
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Aspect && 
           other.name == name && 
           other.angle == angle;
  }

  @override
  int get hashCode => name.hashCode ^ angle.hashCode;

  @override
  String toString() {
    return 'Aspect(name: $name, angle: $angle°, symbol: $symbol)';
  }

  /// 複製並修改部分屬性
  Aspect copyWith({
    String? name,
    String? shortName,
    String? shortZh,
    int? angle,
    double? orb,
    String? symbol,
    Color? color,
    String? description,
    String? nature,
    double? strength,
    List<String>? keywords,
    String? traditionalName,
  }) {
    return Aspect(
      name: name ?? this.name,
      shortName: shortName ?? this.shortName,
      shortZh: shortZh ?? this.shortZh,
      angle: angle ?? this.angle,
      orb: orb ?? this.orb,
      symbol: symbol ?? this.symbol,
      color: color ?? this.color,
      description: description ?? this.description,
      nature: nature ?? this.nature,
      strength: strength ?? this.strength,
      keywords: keywords ?? this.keywords,
      traditionalName: traditionalName ?? this.traditionalName,
    );
  }
}
