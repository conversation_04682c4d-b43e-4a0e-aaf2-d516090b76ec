import 'package:flutter/material.dart';

/// 法達盤週期數據模型
class FirdariaData {
  /// 週期開始日期
  final DateTime startDate;

  /// 週期結束日期
  final DateTime endDate;

  /// 主要行星 ID
  final int majorPlanetId;

  /// 主要行星名稱
  final String majorPlanetName;

  /// 主要行星符號
  final String majorPlanetSymbol;

  /// 主要行星顏色
  final Color majorPlanetColor;

  /// 子週期數據列表
  final List<FirdariaSubPeriod> subPeriods;

  /// 是否為當前週期
  final bool isCurrent;

  /// 建構函數
  FirdariaData({
    required this.startDate,
    required this.endDate,
    required this.majorPlanetId,
    required this.majorPlanetName,
    required this.majorPlanetSymbol,
    required this.majorPlanetColor,
    required this.subPeriods,
    this.isCurrent = false,
  });

  /// 週期持續時間（年）
  double get durationYears {
    return endDate.difference(startDate).inDays / 365.25;
  }

  /// 週期持續時間（格式化）
  String get durationFormatted {
    final years = durationYears.floor();
    final months = ((durationYears - years) * 12).floor();
    return '$years年${months > 0 ? ' $months個月' : ''}';
  }

  /// 從 JSON 創建
  factory FirdariaData.fromJson(Map<String, dynamic> json) {
    return FirdariaData(
      startDate: DateTime.parse(json['startDate']),
      endDate: DateTime.parse(json['endDate']),
      majorPlanetId: json['majorPlanetId'],
      majorPlanetName: json['majorPlanetName'],
      majorPlanetSymbol: json['majorPlanetSymbol'],
      majorPlanetColor: Color(json['majorPlanetColor']),
      subPeriods: (json['subPeriods'] as List)
          .map((e) => FirdariaSubPeriod.fromJson(e))
          .toList(),
      isCurrent: json['isCurrent'] ?? false,
    );
  }

  /// 轉換為 JSON
  Map<String, dynamic> toJson() {
    return {
      'startDate': startDate.toIso8601String(),
      'endDate': endDate.toIso8601String(),
      'majorPlanetId': majorPlanetId,
      'majorPlanetName': majorPlanetName,
      'majorPlanetSymbol': majorPlanetSymbol,
      'majorPlanetColor': majorPlanetColor.value,
      'subPeriods': subPeriods.map((e) => e.toJson()).toList(),
      'isCurrent': isCurrent,
    };
  }
}

/// 法達盤子週期數據模型
class FirdariaSubPeriod {
  /// 子週期開始日期
  final DateTime startDate;

  /// 子週期結束日期
  final DateTime endDate;

  /// 子週期行星 ID
  final int subPlanetId;

  /// 子週期行星名稱
  final String subPlanetName;

  /// 子週期行星符號
  final String subPlanetSymbol;

  /// 子週期行星顏色
  final Color subPlanetColor;

  /// 是否為當前子週期
  final bool isCurrent;

  /// 建構函數
  FirdariaSubPeriod({
    required this.startDate,
    required this.endDate,
    required this.subPlanetId,
    required this.subPlanetName,
    required this.subPlanetSymbol,
    required this.subPlanetColor,
    this.isCurrent = false,
  });

  /// 子週期持續時間（年）
  double get durationYears {
    return endDate.difference(startDate).inDays / 365.25;
  }

  /// 子週期持續時間（格式化）
  String get durationFormatted {
    final years = durationYears.floor();
    final months = ((durationYears - years) * 12).floor();
    return '$years年${months > 0 ? ' $months個月' : ''}';
  }

  /// 從 JSON 創建
  factory FirdariaSubPeriod.fromJson(Map<String, dynamic> json) {
    return FirdariaSubPeriod(
      startDate: DateTime.parse(json['startDate']),
      endDate: DateTime.parse(json['endDate']),
      subPlanetId: json['subPlanetId'],
      subPlanetName: json['subPlanetName'],
      subPlanetSymbol: json['subPlanetSymbol'],
      subPlanetColor: Color(json['subPlanetColor']),
      isCurrent: json['isCurrent'] ?? false,
    );
  }

  /// 轉換為 JSON
  Map<String, dynamic> toJson() {
    return {
      'startDate': startDate.toIso8601String(),
      'endDate': endDate.toIso8601String(),
      'subPlanetId': subPlanetId,
      'subPlanetName': subPlanetName,
      'subPlanetSymbol': subPlanetSymbol,
      'subPlanetColor': subPlanetColor.value,
      'isCurrent': isCurrent,
    };
  }
}
