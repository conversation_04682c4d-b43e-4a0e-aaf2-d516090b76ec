import 'package:flutter/material.dart';
import 'package:sweph/sweph.dart';

/// 行星數據模型
class Planet {
  final int id;
  final String name;
  final String englishName;
  final String symbol;
  final String description;
  final Color color;
  final String category;
  final double? orbModifier;
  final bool isVisible;
  final String? mythology;
  final List<String> keywords;
  final String? modernRuler;
  final String? traditionalRuler;

  const Planet({
    required this.id,
    required this.name,
    required this.englishName,
    required this.symbol,
    required this.description,
    required this.color,
    required this.category,
    this.orbModifier,
    this.isVisible = true,
    this.mythology,
    this.keywords = const [],
    this.modernRuler,
    this.traditionalRuler,
  });

  /// 從 Map 創建 Planet 實例
  factory Planet.fromMap(Map<String, dynamic> map) {
    return Planet(
      id: map['id'] as int,
      name: map['name'] as String,
      englishName: map['englishName'] as String? ?? '',
      symbol: map['symbol'] as String,
      description: map['description'] as String? ?? '',
      color: map['color'] as Color? ?? Colors.grey,
      category: map['category'] as String? ?? 'planet',
      orbModifier: map['orbModifier'] as double?,
      isVisible: map['isVisible'] as bool? ?? true,
      mythology: map['mythology'] as String?,
      keywords: (map['keywords'] as List<dynamic>?)?.cast<String>() ?? [],
      modernRuler: map['modernRuler'] as String?,
      traditionalRuler: map['traditionalRuler'] as String?,
    );
  }

  /// 轉換為 Map（向後兼容格式）
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'englishName': englishName,
      'unicode': symbol, // 舊代碼期望 unicode 鍵
      'symbol': symbol,
      'body': getHeavenlyBody(), // 添加 HeavenlyBody 映射
      'description': description,
      'color': color,
      'category': category,
      'orbModifier': orbModifier,
      'isVisible': isVisible,
      'mythology': mythology,
      'keywords': keywords,
      'modernRuler': modernRuler,
      'traditionalRuler': traditionalRuler,
    };
  }

  /// 根據行星 ID 獲取對應的 HeavenlyBody
  HeavenlyBody getHeavenlyBody() {
    switch (id) {
      case 0: // SUN
        return HeavenlyBody.SE_SUN;
      case 1: // MOON
        return HeavenlyBody.SE_MOON;
      case 2: // MERCURY
        return HeavenlyBody.SE_MERCURY;
      case 3: // VENUS
        return HeavenlyBody.SE_VENUS;
      case 4: // MARS
        return HeavenlyBody.SE_MARS;
      case 5: // JUPITER
        return HeavenlyBody.SE_JUPITER;
      case 6: // SATURN
        return HeavenlyBody.SE_SATURN;
      case 7: // URANUS
        return HeavenlyBody.SE_URANUS;
      case 8: // NEPTUNE
        return HeavenlyBody.SE_NEPTUNE;
      case 9: // PLUTO
        return HeavenlyBody.SE_PLUTO;
      case 10: // NORTH_NODE
        return HeavenlyBody.SE_TRUE_NODE;
      case 12: // LILITH
        return HeavenlyBody.SE_MEAN_APOG;
      case 15: // CHIRON
        return HeavenlyBody.SE_CHIRON;
      case 17: // CERES
        return HeavenlyBody.SE_CERES;
      case 18: // PALLAS
        return HeavenlyBody.SE_PALLAS;
      case 19: // JUNO
        return HeavenlyBody.SE_JUNO;
      case 20: // VESTA
        return HeavenlyBody.SE_VESTA;
      default:
        // 對於特殊點位或未知行星，返回太陽作為默認值
        return HeavenlyBody.SE_SUN;
    }
  }

  /// 是否為內行星（水星、金星）
  bool get isInnerPlanet => ['水星', '金星'].contains(name);

  /// 是否為外行星（火星、木星、土星、天王星、海王星、冥王星）
  bool get isOuterPlanet => ['火星', '木星', '土星', '天王星', '海王星', '冥王星'].contains(name);

  /// 是否為發光體（太陽、月亮）
  bool get isLuminary => ['太陽', '月亮'].contains(name);

  /// 是否為現代行星（天王星、海王星、冥王星）
  bool get isModernPlanet => ['天王星', '海王星', '冥王星'].contains(name);

  /// 是否為古典行星（太陽到土星）
  bool get isClassicalPlanet => !isModernPlanet && category == 'planet';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Planet && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Planet(id: $id, name: $name, symbol: $symbol)';
  }

  /// 複製並修改部分屬性
  Planet copyWith({
    int? id,
    String? name,
    String? englishName,
    String? symbol,
    String? description,
    Color? color,
    String? category,
    double? orbModifier,
    bool? isVisible,
    String? mythology,
    List<String>? keywords,
    String? modernRuler,
    String? traditionalRuler,
  }) {
    return Planet(
      id: id ?? this.id,
      name: name ?? this.name,
      englishName: englishName ?? this.englishName,
      symbol: symbol ?? this.symbol,
      description: description ?? this.description,
      color: color ?? this.color,
      category: category ?? this.category,
      orbModifier: orbModifier ?? this.orbModifier,
      isVisible: isVisible ?? this.isVisible,
      mythology: mythology ?? this.mythology,
      keywords: keywords ?? this.keywords,
      modernRuler: modernRuler ?? this.modernRuler,
      traditionalRuler: traditionalRuler ?? this.traditionalRuler,
    );
  }
}
