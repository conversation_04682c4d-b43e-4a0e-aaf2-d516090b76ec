import 'chart_settings.dart';

/// 占星模式類型
enum AstrologyMode {
  classical,  // 古典占星
  modern,     // 現代占星
  special,    // 特殊模式
}

/// 占星模式配置
class AstrologyModeConfig {
  final String name;
  final String displayName;
  final String description;
  final ChartTypeSettings defaultSettings;

  const AstrologyModeConfig({
    required this.name,
    required this.displayName,
    required this.description,
    required this.defaultSettings,
  });

  /// 獲取所有占星模式配置
  static Map<AstrologyMode, AstrologyModeConfig> getAllConfigs() {
    return {
      AstrologyMode.classical: _getClassicalConfig(),
      AstrologyMode.modern: getModernConfig(),
      AstrologyMode.special: _getSpecialConfig(),
    };
  }

  /// 古典占星模式配置
  static AstrologyModeConfig _getClassicalConfig() {
    return AstrologyModeConfig(
      name: 'classical',
      displayName: '古典占星',
      description: '使用傳統七星、整宮制、古典相位容許度，適合傳統占星學研究',
      defaultSettings: ChartTypeSettings(
        // 宮位系統：整宮制（古典占星常用）
        houseSystem: HouseSystem.wholeSign,
        
        // 行星顯示：只顯示傳統七星 + 軸點 + 交點
        planetVisibility: {
          // 傳統七星（古典占星核心）
          '太陽': true,
          '月亮': true,
          '水星': true,
          '金星': true,
          '火星': true,
          '木星': true,
          '土星': true,
          
          // 現代行星（古典占星不使用）
          '天王星': false,
          '海王星': false,
          '冥王星': false,
          
          // 軸點（重要參考點）
          '上升': true,
          '中天': true,
          '下降': true,
          '天底': true,
          
          // 交點（古典占星重視）
          '北交點': true,
          '南交點': true,
          
          // 小行星和虛點（古典占星較少使用）
          '莉莉絲': false,
          '凱龍星': false,
          '人龍星': false,
          '穀神星': false,
          '智神星': false,
          '婚神星': false,
          '灶神星': false,
          
          // 阿拉伯點（古典占星重要）
          '幸運點': true,
          '精神點': false,
          '旺點': false,
          '日月中點': false,
          '宿命點': false,
        },
        
        // 相位容許度：古典占星較寬鬆的容許度
        aspectOrbs: {
          '合相': 10.0,    // 古典占星合相容許度較大
          '六分相': 6.0,   // 60度
          '四分相': 10.0,  // 90度，重要相位
          '三分相': 8.0,   // 120度，吉相位
          '對分相': 10.0,  // 180度，重要相位
          '十二分相': 3.0, // 30度，次要相位
          '八分相': 3.0,   // 45度，次要相位
          '五分相': 2.0,   // 72度，較少使用
          '十分相': 1.5,   // 36度，較少使用
          '九分相': 2.0,   // 40度，較少使用
          '七分相': 1.5,   // 較少使用
          '十一分相': 1.5, // 較少使用
        },
        
        // 顯示設定：古典占星重視界主星和度數
        showZodiacRulers: true,   // 顯示星座界主星（古典占星重要）
        showHouseDegrees: true,   // 顯示宮位度數（古典占星精確計算）
        showPlanetDegrees: true,  // 顯示行星度數（古典占星重視度數）
        showMajorAspects: true,   // 顯示主要相位
        showMinorAspects: false,  // 古典占星較少使用次要相位
        colorTheme: 'classical',  // 古典色彩主題
        astrologyMode: 'classical',
      ),
    );
  }

  /// 現代占星模式配置
  static AstrologyModeConfig getModernConfig() {
    return AstrologyModeConfig(
      name: 'modern',
      displayName: '現代占星',
      description: '包含現代行星、Placidus宮位制、心理占星學取向，適合現代占星實務',
      defaultSettings: ChartTypeSettings(
        // 宮位系統：Placidus制（現代占星標準）
        houseSystem: HouseSystem.placidus,
        
        // 行星顯示：包含所有現代行星
        planetVisibility: {
          // 傳統七星
          '太陽': true,
          '月亮': true,
          '水星': true,
          '金星': true,
          '火星': true,
          '木星': true,
          '土星': true,
          
          // 現代行星（現代占星核心）
          '天王星': true,
          '海王星': true,
          '冥王星': true,
          
          // 軸點
          '上升': true,
          '中天': true,
          '下降': true,  // 現代占星較少顯示
          '天底': true,  // 現代占星較少顯示
          
          // 交點
          '北交點': true,
          '南交點': true, // 現代占星通常只看北交點
          
          // 小行星（現代占星選擇性使用）
          '莉莉絲': false,   // 黑月莉莉絲在現代占星中重要
          '凱龍星': false,   // 療癒小行星，現代占星重視
          '人龍星': false,
          '穀神星': false,
          '智神星': false,
          '婚神星': false,
          '灶神星': false,
          
          // 虛點（現代占星較少使用）
          '幸運點': true,
          '精神點': false,
          '旺點': false,
          '日月中點': false,  // 現代占星重視中點
          '宿命點': false,
        },
        
        // 相位容許度：現代占星較精確的容許度
        aspectOrbs: {
          '合相': 8.0,     // 標準容許度
          '六分相': 4.0,   // 較小容許度
          '四分相': 8.0,   // 重要相位
          '三分相': 8.0,   // 重要相位
          '對分相': 8.0,   // 重要相位
          '十二分相': 2.0, // 次要相位，較小容許度
          '八分相': 3.0,   // 次要相位
          '五分相': 2.0,   // 創意相位
          '十分相': 1.5,   // 較少使用
          '九分相': 2.0,   // 較少使用
          '七分相': 1.5,   // 較少使用
          '十一分相': 1.5, // 較少使用
        },
        
        // 顯示設定：現代占星重視簡潔
        showZodiacRulers: false,  // 現代占星較少使用界主星
        showHouseDegrees: false,  // 現代占星較少顯示度數
        showPlanetDegrees: false, // 現代占星重視相位勝過度數
        showMajorAspects: true,   // 顯示主要相位
        showMinorAspects: true,   // 現代占星也重視次要相位
        colorTheme: 'modern',     // 現代色彩主題
        astrologyMode: 'modern',
      ),
    );
  }

  /// 特殊模式配置
  static AstrologyModeConfig _getSpecialConfig() {
    return AstrologyModeConfig(
      name: 'special',
      displayName: '特殊模式',
      description: '包含小行星、特殊點、等宮制，適合進階占星研究和特殊用途',
      defaultSettings: ChartTypeSettings(
        // 宮位系統：等宮制（特殊研究用）
        houseSystem: HouseSystem.equal,
        
        // 行星顯示：包含所有天體和特殊點
        planetVisibility: {
          // 所有行星
          '太陽': true,
          '月亮': true,
          '水星': true,
          '金星': true,
          '火星': true,
          '木星': true,
          '土星': true,
          '天王星': true,
          '海王星': true,
          '冥王星': true,
          
          // 所有軸點
          '上升': true,
          '中天': true,
          '下降': true,
          '天底': true,
          
          // 所有交點
          '北交點': true,
          '南交點': true,
          
          // 所有小行星（特殊模式全部顯示）
          '莉莉絲': true,
          '凱龍星': true,
          '人龍星': true,
          '穀神星': true,
          '智神星': true,
          '婚神星': true,
          '灶神星': true,
          
          // 所有虛點
          '幸運點': true,
          '精神點': true,
          '旺點': true,
          '日月中點': true,
          '宿命點': true,
        },
        
        // 相位容許度：特殊模式使用中等容許度
        aspectOrbs: {
          '合相': 6.0,     // 中等容許度
          '六分相': 4.0,
          '四分相': 6.0,
          '三分相': 6.0,
          '對分相': 6.0,
          '十二分相': 2.0,
          '八分相': 2.0,
          '五分相': 2.0,
          '十分相': 1.5,
          '九分相': 2.0,
          '七分相': 1.5,
          '十一分相': 1.5,
        },
        
        // 顯示設定：特殊模式顯示所有資訊
        showZodiacRulers: true,   // 顯示界主星
        showHouseDegrees: true,   // 顯示宮位度數
        showPlanetDegrees: true,  // 顯示行星度數
        showMajorAspects: true,   // 顯示主要相位
        showMinorAspects: true,   // 顯示次要相位
        colorTheme: 'special',    // 特殊色彩主題
        astrologyMode: 'special',
      ),
    );
  }

  /// 根據模式名稱獲取配置
  static AstrologyModeConfig? getConfigByName(String modeName) {
    final configs = getAllConfigs();
    for (final entry in configs.entries) {
      if (entry.value.name == modeName) {
        return entry.value;
      }
    }
    return null;
  }

  /// 根據模式獲取配置
  static AstrologyModeConfig? getConfig(AstrologyMode mode) {
    final configs = getAllConfigs();
    return configs[mode];
  }
}

/// 占星模式擴展
extension AstrologyModeExtension on AstrologyMode {
  /// 顯示名稱
  String get displayName {
    switch (this) {
      case AstrologyMode.classical:
        return '古典占星';
      case AstrologyMode.modern:
        return '現代占星';
      case AstrologyMode.special:
        return '特殊模式';
    }
  }

  /// 描述
  String get description {
    switch (this) {
      case AstrologyMode.classical:
        return '使用傳統七星、整宮制、古典相位容許度';
      case AstrologyMode.modern:
        return '包含現代行星、Placidus宮位制、心理占星學取向';
      case AstrologyMode.special:
        return '包含小行星、特殊點、等宮制，適合進階研究';
    }
  }

  /// 獲取預設配置
  ChartTypeSettings get defaultSettings {
    final config = AstrologyModeConfig.getConfig(this);
    return config?.defaultSettings ?? AstrologyModeConfig.getModernConfig().defaultSettings;
  }
}
