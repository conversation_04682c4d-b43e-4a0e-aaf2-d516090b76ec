import 'package:astreal/core/constants/astrology_constants.dart';
import 'package:astreal/features/astrology/constants/aspect_definitions.dart';
import 'package:flutter/material.dart';

/// 行星顯示設定資料類別
/// 
/// 取代 Map<String, bool> planetVisibility
class PlanetVisibilitySettings {
  final bool sun;           // 太陽
  final bool moon;          // 月亮
  final bool mercury;       // 水星
  final bool venus;         // 金星
  final bool mars;          // 火星
  final bool jupiter;       // 木星
  final bool saturn;        // 土星
  final bool uranus;        // 天王星
  final bool neptune;       // 海王星
  final bool pluto;         // 冥王星
  final bool ascendant;     // 上升
  final bool midheaven;     // 中天
  final bool descendant;    // 下降
  final bool imumCoeli;     // 天底
  final bool northNode;     // 北交點
  final bool southNode;     // 南交點
  final bool lilith;        // 莉莉絲
  final bool chiron;        // 凱龍星
  final bool pholus;        // 人龍星
  final bool ceres;         // 穀神星
  final bool pallas;        // 智神星
  final bool juno;          // 婚神星
  final bool vesta;         // 灶神星
  final bool fortunePoint;  // 幸運點
  final bool spiritPoint;   // 精神點
  final bool exaltationPoint; // 旺點
  final bool sunMoonMidpoint; // 日月中點
  final bool vertexPoint;   // 宿命點

  const PlanetVisibilitySettings({
    this.sun = true,
    this.moon = true,
    this.mercury = true,
    this.venus = true,
    this.mars = true,
    this.jupiter = true,
    this.saturn = true,
    this.uranus = true,
    this.neptune = true,
    this.pluto = true,
    this.ascendant = true,
    this.midheaven = true,
    this.descendant = true,
    this.imumCoeli = true,
    this.northNode = true,
    this.southNode = true,
    this.lilith = false,
    this.chiron = false,
    this.pholus = false,
    this.ceres = false,
    this.pallas = false,
    this.juno = false,
    this.vesta = false,
    this.fortunePoint = true,
    this.spiritPoint = false,
    this.exaltationPoint = false,
    this.sunMoonMidpoint = false,
    this.vertexPoint = false,
  });

  /// 轉換為 Map 格式（向後相容）
  Map<String, bool> toMap() {
    return {
      '太陽': sun,
      '月亮': moon,
      '水星': mercury,
      '金星': venus,
      '火星': mars,
      '木星': jupiter,
      '土星': saturn,
      '天王星': uranus,
      '海王星': neptune,
      '冥王星': pluto,
      '上升': ascendant,
      '中天': midheaven,
      '下降': descendant,
      '天底': imumCoeli,
      '北交點': northNode,
      '南交點': southNode,
      '莉莉絲': lilith,
      '凱龍星': chiron,
      '人龍星': pholus,
      '穀神星': ceres,
      '智神星': pallas,
      '婚神星': juno,
      '灶神星': vesta,
      '幸運點': fortunePoint,
      '精神點': spiritPoint,
      '旺點': exaltationPoint,
      '日月中點': sunMoonMidpoint,
      '宿命點': vertexPoint,
    };
  }

  /// 從 Map 建立實例
  factory PlanetVisibilitySettings.fromMap(Map<String, bool> map) {
    return PlanetVisibilitySettings(
      sun: map['太陽'] ?? true,
      moon: map['月亮'] ?? true,
      mercury: map['水星'] ?? true,
      venus: map['金星'] ?? true,
      mars: map['火星'] ?? true,
      jupiter: map['木星'] ?? true,
      saturn: map['土星'] ?? true,
      uranus: map['天王星'] ?? true,
      neptune: map['海王星'] ?? true,
      pluto: map['冥王星'] ?? true,
      ascendant: map['上升'] ?? true,
      midheaven: map['中天'] ?? true,
      descendant: map['下降'] ?? true,
      imumCoeli: map['天底'] ?? true,
      northNode: map['北交點'] ?? true,
      southNode: map['南交點'] ?? true,
      lilith: map['莉莉絲'] ?? false,
      chiron: map['凱龍星'] ?? false,
      pholus: map['人龍星'] ?? false,
      ceres: map['穀神星'] ?? false,
      pallas: map['智神星'] ?? false,
      juno: map['婚神星'] ?? false,
      vesta: map['灶神星'] ?? false,
      fortunePoint: map['幸運點'] ?? true,
      spiritPoint: map['精神點'] ?? false,
      exaltationPoint: map['旺點'] ?? false,
      sunMoonMidpoint: map['日月中點'] ?? false,
      vertexPoint: map['宿命點'] ?? false,
    );
  }

  /// 建立副本並修改指定行星的顯示狀態
  PlanetVisibilitySettings copyWithPlanet(String planetName, bool isVisible) {
    final map = toMap();
    map[planetName] = isVisible;
    return PlanetVisibilitySettings.fromMap(map);
  }

  /// 預設的分析模式設定
  static const PlanetVisibilitySettings analysisDefault = PlanetVisibilitySettings(
    sun: true,
    moon: true,
    mercury: true,
    venus: true,
    mars: true,
    jupiter: true,
    saturn: true,
    uranus: true,
    neptune: true,
    pluto: true,
    ascendant: true,
    midheaven: true,
    descendant: true,
    imumCoeli: true,
    northNode: true,
    southNode: true,
    lilith: true,
    chiron: true,
    pholus: false,
    ceres: false,
    pallas: false,
    juno: true,
    vesta: false,
    fortunePoint: true,
    spiritPoint: false,
    exaltationPoint: false,
    sunMoonMidpoint: true,
    vertexPoint: true,
  );

  /// 預設的首頁模式設定
  static const PlanetVisibilitySettings homeDefault = PlanetVisibilitySettings(
    sun: true,
    moon: true,
    mercury: true,
    venus: true,
    mars: true,
    jupiter: true,
    saturn: true,
    uranus: true,
    neptune: true,
    pluto: true,
    ascendant: false,
    midheaven: false,
    descendant: false,
    imumCoeli: false,
    northNode: false,
    southNode: false,
    lilith: false,
    chiron: false,
    pholus: false,
    ceres: false,
    pallas: false,
    juno: false,
    vesta: false,
    fortunePoint: false,
    spiritPoint: false,
    exaltationPoint: false,
    sunMoonMidpoint: false,
    vertexPoint: false,
  );

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PlanetVisibilitySettings &&
        other.sun == sun &&
        other.moon == moon &&
        other.mercury == mercury &&
        other.venus == venus &&
        other.mars == mars &&
        other.jupiter == jupiter &&
        other.saturn == saturn &&
        other.uranus == uranus &&
        other.neptune == neptune &&
        other.pluto == pluto &&
        other.ascendant == ascendant &&
        other.midheaven == midheaven &&
        other.descendant == descendant &&
        other.imumCoeli == imumCoeli &&
        other.northNode == northNode &&
        other.southNode == southNode &&
        other.lilith == lilith &&
        other.chiron == chiron &&
        other.pholus == pholus &&
        other.ceres == ceres &&
        other.pallas == pallas &&
        other.juno == juno &&
        other.vesta == vesta &&
        other.fortunePoint == fortunePoint &&
        other.spiritPoint == spiritPoint &&
        other.exaltationPoint == exaltationPoint &&
        other.sunMoonMidpoint == sunMoonMidpoint &&
        other.vertexPoint == vertexPoint;
  }

  @override
  int get hashCode {
    return Object.hashAll([
      sun, moon, mercury, venus, mars, jupiter, saturn,
      uranus, neptune, pluto, ascendant, midheaven, descendant,
      imumCoeli, northNode, southNode, lilith, chiron, pholus,
      ceres, pallas, juno, vesta, fortunePoint, spiritPoint,
      exaltationPoint, sunMoonMidpoint, vertexPoint,
    ]);
  }

  @override
  String toString() {
    return 'PlanetVisibilitySettings(sun: $sun, moon: $moon, mercury: $mercury, ...)';
  }
}

/// 相位容許度設定資料類別
///
/// 取代 Map<String, double> aspectOrbs
class AspectOrbSettings {
  final double conjunction;    // 合相
  final double sextile;        // 六分相
  final double square;         // 四分相
  final double trine;          // 三分相
  final double opposition;     // 對分相
  final double semisextile;    // 十二分相
  final double semisquare;     // 八分相
  final double quintile;       // 五分相
  final double decile;         // 十分相
  final double novile;         // 九分相
  final double septile;        // 七分相
  final double undecile;       // 十一分相

  const AspectOrbSettings({
    this.conjunction = 8.0,
    this.sextile = 4.0,
    this.square = 8.0,
    this.trine = 8.0,
    this.opposition = 8.0,
    this.semisextile = 2.0,
    this.semisquare = 3.0,
    this.quintile = 2.0,
    this.decile = 1.5,
    this.novile = 2.0,
    this.septile = 1.5,
    this.undecile = 1.5,
  });

  /// 轉換為 Map 格式（向後相容）
  Map<String, double> toMap() {
    return {
      '合相': conjunction,
      '六分相': sextile,
      '四分相': square,
      '三分相': trine,
      '對分相': opposition,
      '十二分相': semisextile,
      '八分相': semisquare,
      '五分相': quintile,
      '十分相': decile,
      '九分相': novile,
      '七分相': septile,
      '十一分相': undecile,
    };
  }

  /// 從 Map 建立實例
  factory AspectOrbSettings.fromMap(Map<String, double> map) {
    return AspectOrbSettings(
      conjunction: map['合相'] ?? 8.0,
      sextile: map['六分相'] ?? 4.0,
      square: map['四分相'] ?? 8.0,
      trine: map['三分相'] ?? 8.0,
      opposition: map['對分相'] ?? 8.0,
      semisextile: map['十二分相'] ?? 2.0,
      semisquare: map['八分相'] ?? 3.0,
      quintile: map['五分相'] ?? 2.0,
      decile: map['十分相'] ?? 1.5,
      novile: map['九分相'] ?? 2.0,
      septile: map['七分相'] ?? 1.5,
      undecile: map['十一分相'] ?? 1.5,
    );
  }

  /// 建立副本並修改指定相位的容許度
  AspectOrbSettings copyWithAspect(String aspectName, double orb) {
    final map = toMap();
    map[aspectName] = orb;
    return AspectOrbSettings.fromMap(map);
  }

  /// 預設的分析模式設定
  static const AspectOrbSettings analysisDefault = AspectOrbSettings(
    conjunction: 10.0,
    sextile: 8.0,
    square: 8.0,
    trine: 8.0,
    opposition: 8.0,
    semisextile: 3.0,
    semisquare: 3.0,
    quintile: 2.0,
    decile: 1.5,
    novile: 2.0,
    septile: 1.5,
    undecile: 1.5,
  );

  /// 預設的首頁模式設定
  static const AspectOrbSettings homeDefault = AspectOrbSettings(
    conjunction: 3.0,
    sextile: 3.0,
    square: 3.0,
    trine: 3.0,
    opposition: 3.0,
    semisextile: 2.0,
    semisquare: 2.0,
    quintile: 1.5,
    decile: 1.0,
    novile: 1.5,
    septile: 1.0,
    undecile: 1.0,
  );

  /// 古典占星模式設定
  static const AspectOrbSettings classicalDefault = AspectOrbSettings(
    conjunction: 10.0,
    sextile: 6.0,
    square: 10.0,
    trine: 8.0,
    opposition: 10.0,
    semisextile: 3.0,
    semisquare: 3.0,
    quintile: 2.0,
    decile: 1.5,
    novile: 2.0,
    septile: 1.5,
    undecile: 1.5,
  );

  /// 現代占星模式設定
  static const AspectOrbSettings modernDefault = AspectOrbSettings(
    conjunction: 8.0,
    sextile: 4.0,
    square: 8.0,
    trine: 8.0,
    opposition: 8.0,
    semisextile: 2.0,
    semisquare: 3.0,
    quintile: 2.0,
    decile: 1.5,
    novile: 2.0,
    septile: 1.5,
    undecile: 1.5,
  );

  /// 特殊模式設定
  static const AspectOrbSettings specialDefault = AspectOrbSettings(
    conjunction: 6.0,
    sextile: 4.0,
    square: 6.0,
    trine: 6.0,
    opposition: 6.0,
    semisextile: 2.0,
    semisquare: 2.0,
    quintile: 2.0,
    decile: 1.5,
    novile: 2.0,
    septile: 1.5,
    undecile: 1.5,
  );

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AspectOrbSettings &&
        other.conjunction == conjunction &&
        other.sextile == sextile &&
        other.square == square &&
        other.trine == trine &&
        other.opposition == opposition &&
        other.semisextile == semisextile &&
        other.semisquare == semisquare &&
        other.quintile == quintile &&
        other.decile == decile &&
        other.novile == novile &&
        other.septile == septile &&
        other.undecile == undecile;
  }

  @override
  int get hashCode {
    return Object.hashAll([
      conjunction, sextile, square, trine, opposition,
      semisextile, semisquare, quintile, decile, novile,
      septile, undecile,
    ]);
  }

  @override
  String toString() {
    return 'AspectOrbSettings(conjunction: $conjunction, sextile: $sextile, square: $square, ...)';
  }
}

/// 相位顏色設定資料類別
///
/// 取代 Map<String, Color> aspectColors
class AspectColorSettings {
  final Color conjunction;    // 合相
  final Color sextile;        // 六分相
  final Color square;         // 四分相
  final Color trine;          // 三分相
  final Color opposition;     // 對分相
  final Color semisextile;    // 十二分相
  final Color semisquare;     // 八分相
  final Color quintile;       // 五分相
  final Color decile;         // 十分相
  final Color novile;         // 九分相
  final Color septile;        // 七分相
  final Color undecile;       // 十一分相

  const AspectColorSettings({
    this.conjunction = const Color(0xFFFF6B6B),
    this.sextile = const Color(0xFF2999A4),
    this.square = Colors.red,
    this.trine = Colors.green,
    this.opposition = const Color(0xFF051883),
    this.semisextile = const Color(0xFF9B59B6),
    this.semisquare = const Color(0xFFE67E22),
    this.quintile = const Color(0xFF1ABC9C),
    this.decile = const Color(0xFFF39C12),
    this.novile = const Color(0xFF8E44AD),
    this.septile = const Color(0xFF34495E),
    this.undecile = const Color(0xFF95A5A6),
  });

  /// 轉換為 Map 格式（向後相容）
  Map<String, Color> toMap() {
    return {
      '合相': conjunction,
      '六分相': sextile,
      '四分相': square,
      '三分相': trine,
      '對分相': opposition,
      '十二分相': semisextile,
      '八分相': semisquare,
      '五分相': quintile,
      '十分相': decile,
      '九分相': novile,
      '七分相': septile,
      '十一分相': undecile,
    };
  }

  /// 從 Map 建立實例
  factory AspectColorSettings.fromMap(Map<String, Color> map) {
    return AspectColorSettings(
      conjunction: map['合相'] ?? const Color(0xFFFF6B6B),
      sextile: map['六分相'] ?? const Color(0xFF2999A4),
      square: map['四分相'] ?? Colors.red,
      trine: map['三分相'] ?? Colors.green,
      opposition: map['對分相'] ?? const Color(0xFF051883),
      semisextile: map['十二分相'] ?? const Color(0xFF9B59B6),
      semisquare: map['八分相'] ?? const Color(0xFFE67E22),
      quintile: map['五分相'] ?? const Color(0xFF1ABC9C),
      decile: map['十分相'] ?? const Color(0xFFF39C12),
      novile: map['九分相'] ?? const Color(0xFF8E44AD),
      septile: map['七分相'] ?? const Color(0xFF34495E),
      undecile: map['十一分相'] ?? const Color(0xFF95A5A6),
    );
  }

  /// 建立副本並修改指定相位的顏色
  AspectColorSettings copyWithAspect(String aspectName, Color color) {
    final map = toMap();
    map[aspectName] = color;
    return AspectColorSettings.fromMap(map);
  }

  /// 從 AspectDefinitions 獲取預設顏色
  static AspectColorSettings fromAspectDefinitions() {
    final Map<String, Color> colorMap = {};

    for (final aspect in AspectDefinitions.allAspects) {
      final name = aspect['name'] as String;
      final color = aspect['color'] as Color;
      colorMap[name] = color;
    }

    return AspectColorSettings.fromMap(colorMap);
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AspectColorSettings &&
        other.conjunction == conjunction &&
        other.sextile == sextile &&
        other.square == square &&
        other.trine == trine &&
        other.opposition == opposition &&
        other.semisextile == semisextile &&
        other.semisquare == semisquare &&
        other.quintile == quintile &&
        other.decile == decile &&
        other.novile == novile &&
        other.septile == septile &&
        other.undecile == undecile;
  }

  @override
  int get hashCode {
    return Object.hashAll([
      conjunction, sextile, square, trine, opposition,
      semisextile, semisquare, quintile, decile, novile,
      septile, undecile,
    ]);
  }

  @override
  String toString() {
    return 'AspectColorSettings(conjunction: $conjunction, sextile: $sextile, square: $square, ...)';
  }
}

/// 行星顏色設定資料類別
///
/// 取代 Map<String, Color> planetColors
class PlanetColorSettings {
  final Color sun;           // 太陽
  final Color moon;          // 月亮
  final Color mercury;       // 水星
  final Color venus;         // 金星
  final Color mars;          // 火星
  final Color jupiter;       // 木星
  final Color saturn;        // 土星
  final Color uranus;        // 天王星
  final Color neptune;       // 海王星
  final Color pluto;         // 冥王星
  final Color ascendant;     // 上升
  final Color midheaven;     // 中天
  final Color descendant;    // 下降
  final Color imumCoeli;     // 天底
  final Color northNode;     // 北交點
  final Color southNode;     // 南交點
  final Color lilith;        // 莉莉絲
  final Color chiron;        // 凱龍星
  final Color pholus;        // 人龍星
  final Color ceres;         // 穀神星
  final Color pallas;        // 智神星
  final Color juno;          // 婚神星
  final Color vesta;         // 灶神星

  const PlanetColorSettings({
    this.sun = const Color(0xFFFF0000),
    this.moon = const Color(0xFF0A0AFF),
    this.mercury = const Color(0xFF127116),
    this.venus = const Color(0xFFCC9933),
    this.mars = Colors.red,
    this.jupiter = const Color(0xFF0000FF),
    this.saturn = const Color(0xFF000000),
    this.uranus = const Color(0xFF00FFFF),
    this.neptune = const Color(0xFF0080FF),
    this.pluto = const Color(0xFF800080),
    this.ascendant = const Color(0xFF8B4513),
    this.midheaven = const Color(0xFF8B4513),
    this.descendant = const Color(0xFF8B4513),
    this.imumCoeli = const Color(0xFF8B4513),
    this.northNode = const Color(0xFF800080),
    this.southNode = const Color(0xFF800080),
    this.lilith = const Color(0xFF000000),
    this.chiron = const Color(0xFF8B4513),
    this.pholus = const Color(0xFF8B4513),
    this.ceres = const Color(0xFF8B4513),
    this.pallas = const Color(0xFF8B4513),
    this.juno = const Color(0xFF8B4513),
    this.vesta = const Color(0xFF8B4513),
  });

  /// 轉換為 Map 格式（向後相容）
  Map<String, Color> toMap() {
    return {
      '太陽': sun,
      '月亮': moon,
      '水星': mercury,
      '金星': venus,
      '火星': mars,
      '木星': jupiter,
      '土星': saturn,
      '天王星': uranus,
      '海王星': neptune,
      '冥王星': pluto,
      '上升': ascendant,
      '中天': midheaven,
      '下降': descendant,
      '天底': imumCoeli,
      '北交點': northNode,
      '南交點': southNode,
      '莉莉絲': lilith,
      '凱龍星': chiron,
      '人龍星': pholus,
      '穀神星': ceres,
      '智神星': pallas,
      '婚神星': juno,
      '灶神星': vesta,
    };
  }

  /// 從 Map 建立實例
  factory PlanetColorSettings.fromMap(Map<String, Color> map) {
    return PlanetColorSettings(
      sun: map['太陽'] ?? const Color(0xFFFF0000),
      moon: map['月亮'] ?? const Color(0xFF0A0AFF),
      mercury: map['水星'] ?? const Color(0xFF127116),
      venus: map['金星'] ?? const Color(0xFFCC9933),
      mars: map['火星'] ?? Colors.red,
      jupiter: map['木星'] ?? const Color(0xFF0000FF),
      saturn: map['土星'] ?? const Color(0xFF000000),
      uranus: map['天王星'] ?? const Color(0xFF00FFFF),
      neptune: map['海王星'] ?? const Color(0xFF0080FF),
      pluto: map['冥王星'] ?? const Color(0xFF800080),
      ascendant: map['上升'] ?? const Color(0xFF8B4513),
      midheaven: map['中天'] ?? const Color(0xFF8B4513),
      descendant: map['下降'] ?? const Color(0xFF8B4513),
      imumCoeli: map['天底'] ?? const Color(0xFF8B4513),
      northNode: map['北交點'] ?? const Color(0xFF800080),
      southNode: map['南交點'] ?? const Color(0xFF800080),
      lilith: map['莉莉絲'] ?? const Color(0xFF000000),
      chiron: map['凱龍星'] ?? const Color(0xFF8B4513),
      pholus: map['人龍星'] ?? const Color(0xFF8B4513),
      ceres: map['穀神星'] ?? const Color(0xFF8B4513),
      pallas: map['智神星'] ?? const Color(0xFF8B4513),
      juno: map['婚神星'] ?? const Color(0xFF8B4513),
      vesta: map['灶神星'] ?? const Color(0xFF8B4513),
    );
  }

  /// 建立副本並修改指定行星的顏色
  PlanetColorSettings copyWithPlanet(String planetName, Color color) {
    final map = toMap();
    map[planetName] = color;
    return PlanetColorSettings.fromMap(map);
  }

  /// 從 AstrologyConstants 獲取預設顏色
  static PlanetColorSettings fromAstrologyConstants() {
    final Map<String, Color> colorMap = {};

    for (final planet in AstrologyConstants.PLANETS) {
      final name = planet['name'] as String;
      final color = planet['color'] as Color;
      colorMap[name] = color;
    }

    return PlanetColorSettings.fromMap(colorMap);
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PlanetColorSettings &&
        other.sun == sun &&
        other.moon == moon &&
        other.mercury == mercury &&
        other.venus == venus &&
        other.mars == mars &&
        other.jupiter == jupiter &&
        other.saturn == saturn &&
        other.uranus == uranus &&
        other.neptune == neptune &&
        other.pluto == pluto &&
        other.ascendant == ascendant &&
        other.midheaven == midheaven &&
        other.descendant == descendant &&
        other.imumCoeli == imumCoeli &&
        other.northNode == northNode &&
        other.southNode == southNode &&
        other.lilith == lilith &&
        other.chiron == chiron &&
        other.pholus == pholus &&
        other.ceres == ceres &&
        other.pallas == pallas &&
        other.juno == juno &&
        other.vesta == vesta;
  }

  @override
  int get hashCode {
    return Object.hashAll([
      sun, moon, mercury, venus, mars, jupiter, saturn,
      uranus, neptune, pluto, ascendant, midheaven, descendant,
      imumCoeli, northNode, southNode, lilith, chiron, pholus,
      ceres, pallas, juno, vesta,
    ]);
  }

  @override
  String toString() {
    return 'PlanetColorSettings(sun: $sun, moon: $moon, mercury: $mercury, ...)';
  }
}
