import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../core/constants/astrology_constants.dart';
import 'astrology_mode_config.dart';
import 'chart_type.dart';

/// 宮位系統類型
enum HouseSystemType {
  timeBasedQuadrant, // 基於時間的象限劃分
  spaceBasedQuadrant, // 基於空間的象限劃分
  equalDivision, // 等分劃分
  wholeSign, // 整宮制
  special, // 特殊用途
}

/// 難度等級
enum DifficultyLevel {
  beginner, // 初學者
  intermediate, // 中級
  advanced, // 高級
  expert, // 專家
}

/// 使用頻率
enum UsageFrequency {
  veryHigh, // 非常高
  high, // 高
  medium, // 中等
  low, // 低
  veryLow, // 非常低
}

/// 歷史時期
enum HistoricalPeriod {
  ancient, // 古代
  medieval, // 中世紀
  renaissance, // 文藝復興
  modern, // 現代
  contemporary, // 當代
}

/// 宮位系統（House System）列舉
enum HouseSystem {
  placidus, // P
  wholeSign, // W
  equal, // A, E
  alcabitius, // B
  campanus, // C
  gauquelinSectors, // G
  horizonAzimuth, // H
  koch, // K
  morinus, // M
  porphyry, // O
  regiomontanus, // R
  topocentric, // T
  krusinskiPisaGoelzer, // U
  equalVehlow, // V
  meridianHouses, // X
  apcHouses // Y
}

/// House system 字母對應
const Map<String, HouseSystem> houseSystemMap = {
  'A': HouseSystem.equal,
  'E': HouseSystem.equal,
  'B': HouseSystem.alcabitius,
  'C': HouseSystem.campanus,
  'G': HouseSystem.gauquelinSectors,
  'H': HouseSystem.horizonAzimuth,
  'K': HouseSystem.koch,
  'M': HouseSystem.morinus,
  'O': HouseSystem.porphyry,
  'P': HouseSystem.placidus,
  'R': HouseSystem.regiomontanus,
  'T': HouseSystem.topocentric,
  'U': HouseSystem.krusinskiPisaGoelzer,
  'V': HouseSystem.equalVehlow,
  'W': HouseSystem.wholeSign,
  'X': HouseSystem.meridianHouses,
  'Y': HouseSystem.apcHouses,
};

/// HouseSystem 擴展功能
extension HouseSystemExtension on HouseSystem {
  /// 中文顯示名稱
  String get displayName {
    switch (this) {
      case HouseSystem.equal:
        return '等分宮位制';
      case HouseSystem.alcabitius:
        return '阿爾卡比提烏斯制';
      case HouseSystem.campanus:
        return '坎帕納斯制';
      case HouseSystem.gauquelinSectors:
        return '高克蘭區域';
      case HouseSystem.horizonAzimuth:
        return '地平方位系統';
      case HouseSystem.koch:
        return '科赫制';
      case HouseSystem.morinus:
        return '莫里納斯制';
      case HouseSystem.porphyry:
        return '波耳弗里制';
      case HouseSystem.placidus:
        return '普拉西德制';
      case HouseSystem.regiomontanus:
        return '雷基蒙塔納斯制';
      case HouseSystem.topocentric:
        return '托波森特制';
      case HouseSystem.krusinskiPisaGoelzer:
        return '克魯辛斯基制';
      case HouseSystem.equalVehlow:
        return '等分威洛制';
      case HouseSystem.wholeSign:
        return '整宮制';
      case HouseSystem.meridianHouses:
        return '子午線系統';
      case HouseSystem.apcHouses:
        return 'APC 宮位系統';
    }
  }

  /// 英文名稱
  String get englishName {
    switch (this) {
      case HouseSystem.equal:
        return 'Equal House';
      case HouseSystem.alcabitius:
        return 'Alcabitius';
      case HouseSystem.campanus:
        return 'Campanus';
      case HouseSystem.gauquelinSectors:
        return 'Gauquelin Sectors';
      case HouseSystem.horizonAzimuth:
        return 'Horizon/Azimuth';
      case HouseSystem.koch:
        return 'Koch';
      case HouseSystem.morinus:
        return 'Morinus';
      case HouseSystem.porphyry:
        return 'Porphyry';
      case HouseSystem.placidus:
        return 'Placidus';
      case HouseSystem.regiomontanus:
        return 'Regiomontanus';
      case HouseSystem.topocentric:
        return 'Topocentric';
      case HouseSystem.krusinskiPisaGoelzer:
        return 'Krusinski-Pisa-Goelzer';
      case HouseSystem.equalVehlow:
        return 'Equal Vehlow';
      case HouseSystem.wholeSign:
        return 'Whole Sign';
      case HouseSystem.meridianHouses:
        return 'Meridian Houses';
      case HouseSystem.apcHouses:
        return 'APC Houses';
    }
  }

  /// 完整顯示名稱（中文 + 英文）
  String get fullDisplayName {
    return '$displayName ($englishName)';
  }

  /// 簡短描述
  String get shortDescription {
    switch (this) {
      case HouseSystem.placidus:
        return '最常用的宮位系統，基於時間劃分';
      case HouseSystem.koch:
        return '基於出生地地理位置，高緯度地區表現較好';
      case HouseSystem.equal:
        return '每個宮位均為30度，簡單直觀';
      case HouseSystem.wholeSign:
        return '整個星座為一個宮位，古典占星常用';
      case HouseSystem.campanus:
        return '基於地平線劃分，適合特定分析';
      case HouseSystem.regiomontanus:
        return '中世紀占星學常用，基於天球劃分';
      case HouseSystem.topocentric:
        return '現代宮位系統，考慮地球曲率影響';
      case HouseSystem.porphyry:
        return '古典宮位系統，簡單的三等分方法';
      case HouseSystem.morinus:
        return '基於赤道劃分的宮位系統';
      case HouseSystem.alcabitius:
        return '中世紀阿拉伯占星學宮位系統';
      case HouseSystem.equalVehlow:
        return '等分宮位的威洛變體';
      case HouseSystem.gauquelinSectors:
        return '統計占星學研究用的區域劃分';
      case HouseSystem.horizonAzimuth:
        return '基於地平座標系統的宮位劃分';
      case HouseSystem.krusinskiPisaGoelzer:
        return '現代改良的宮位計算方法';
      case HouseSystem.meridianHouses:
        return '基於子午線的宮位系統';
      case HouseSystem.apcHouses:
        return '天體位置計算的特殊宮位系統';
    }
  }

  /// 詳細描述
  String get detailedDescription {
    switch (this) {
      case HouseSystem.placidus:
        return '普拉西德制是現代占星學中最廣泛使用的宮位系統。它基於時間的劃分，將每個象限分為三等分，適合大多數地區使用。在極地地區可能會出現計算困難。';
      case HouseSystem.koch:
        return '科赫制基於出生地的地理位置進行計算，在高緯度地區表現比普拉西德制更好。它考慮了地球的實際形狀，提供更精確的宮位劃分。';
      case HouseSystem.equal:
        return '等分宮位制將黃道分為12個相等的30度區間，以上升點為起始點。這是最簡單直觀的宮位系統，適合初學者使用，也被一些現代占星師偏愛。';
      case HouseSystem.wholeSign:
        return '整宮制將整個星座作為一個宮位，是古典占星學中最古老的宮位系統之一。近年來在古典占星復興中重新受到重視，計算簡單且符合古典理論。';
      case HouseSystem.campanus:
        return '坎帕納斯制基於地平線進行劃分，將天球分為相等的弧段。這個系統在某些占星分析中有特殊用途，特別是在研究地理位置對個人的影響時。';
      case HouseSystem.regiomontanus:
        return '雷基蒙塔納斯制是中世紀占星學常用的宮位系統，基於天球的劃分。它在文藝復興時期非常流行，現在仍被一些傳統占星師使用。';
      case HouseSystem.topocentric:
        return '托波森特制是相對現代的宮位系統，考慮了地球的曲率和觀測者的實際位置。它試圖提供更精確的宮位計算，特別是在技術占星學中。';
      case HouseSystem.porphyry:
        return '波耳弗里制是古典時期的宮位系統，使用簡單的三等分方法將每個象限劃分為三個宮位。它的計算方法直接且易於理解。';
      case HouseSystem.morinus:
        return '莫里納斯制基於赤道進行劃分，將天球赤道分為12個相等的部分。這個系統在某些占星學派中被使用，特別注重天體的赤道位置。';
      case HouseSystem.alcabitius:
        return '阿爾卡比提烏斯制起源於中世紀的阿拉伯占星學，是一個歷史悠久的宮位系統。它在伊斯蘭占星學傳統中佔有重要地位。';
      case HouseSystem.equalVehlow:
        return '等分威洛制是等分宮位制的一個變體，由德國占星師威洛提出。它在等分宮位的基礎上進行了一些調整，在德語區占星學中較為常見。';
      case HouseSystem.gauquelinSectors:
        return '高克蘭區域是法國統計學家米歇爾·高克蘭為統計占星學研究而設計的區域劃分系統。它主要用於研究行星位置與職業傾向的統計關聯。';
      case HouseSystem.horizonAzimuth:
        return '地平方位系統基於地平座標系統進行宮位劃分，考慮了觀測者的地平線和方位角。這個系統在某些技術占星學應用中有特殊用途。';
      case HouseSystem.krusinskiPisaGoelzer:
        return '克魯辛斯基-比薩-戈爾策制是現代發展的宮位計算方法，試圖改良傳統宮位系統的一些計算問題，提供更精確的宮位劃分。';
      case HouseSystem.meridianHouses:
        return '子午線系統基於天體的子午線通過進行宮位劃分。它特別關注天體與子午線的關係，在某些占星學派中被認為具有特殊意義。';
      case HouseSystem.apcHouses:
        return 'APC宮位系統是為特殊的天體位置計算而設計的宮位系統。它在某些技術占星學計算中有特定的應用場景。';
    }
  }

  /// 宮位系統類型
  HouseSystemType get type {
    switch (this) {
      case HouseSystem.placidus:
      case HouseSystem.koch:
      case HouseSystem.topocentric:
      case HouseSystem.krusinskiPisaGoelzer:
        return HouseSystemType.timeBasedQuadrant;
      case HouseSystem.equal:
      case HouseSystem.equalVehlow:
        return HouseSystemType.equalDivision;
      case HouseSystem.wholeSign:
        return HouseSystemType.wholeSign;
      case HouseSystem.campanus:
      case HouseSystem.regiomontanus:
      case HouseSystem.porphyry:
      case HouseSystem.morinus:
      case HouseSystem.alcabitius:
      case HouseSystem.meridianHouses:
        return HouseSystemType.spaceBasedQuadrant;
      case HouseSystem.gauquelinSectors:
      case HouseSystem.horizonAzimuth:
      case HouseSystem.apcHouses:
        return HouseSystemType.special;
    }
  }

  /// 難度等級
  DifficultyLevel get difficultyLevel {
    switch (this) {
      case HouseSystem.equal:
      case HouseSystem.wholeSign:
        return DifficultyLevel.beginner;
      case HouseSystem.placidus:
      case HouseSystem.koch:
      case HouseSystem.porphyry:
        return DifficultyLevel.intermediate;
      case HouseSystem.campanus:
      case HouseSystem.regiomontanus:
      case HouseSystem.topocentric:
      case HouseSystem.alcabitius:
      case HouseSystem.morinus:
      case HouseSystem.equalVehlow:
        return DifficultyLevel.advanced;
      case HouseSystem.gauquelinSectors:
      case HouseSystem.horizonAzimuth:
      case HouseSystem.krusinskiPisaGoelzer:
      case HouseSystem.meridianHouses:
      case HouseSystem.apcHouses:
        return DifficultyLevel.expert;
    }
  }

  /// 使用頻率
  UsageFrequency get usageFrequency {
    switch (this) {
      case HouseSystem.placidus:
        return UsageFrequency.veryHigh;
      case HouseSystem.equal:
      case HouseSystem.koch:
      case HouseSystem.wholeSign:
        return UsageFrequency.high;
      case HouseSystem.campanus:
      case HouseSystem.regiomontanus:
      case HouseSystem.porphyry:
        return UsageFrequency.medium;
      case HouseSystem.topocentric:
      case HouseSystem.alcabitius:
      case HouseSystem.morinus:
        return UsageFrequency.low;
      case HouseSystem.equalVehlow:
      case HouseSystem.gauquelinSectors:
      case HouseSystem.horizonAzimuth:
      case HouseSystem.krusinskiPisaGoelzer:
      case HouseSystem.meridianHouses:
      case HouseSystem.apcHouses:
        return UsageFrequency.veryLow;
    }
  }

  /// 歷史時期
  HistoricalPeriod get historicalPeriod {
    switch (this) {
      case HouseSystem.wholeSign:
      case HouseSystem.porphyry:
        return HistoricalPeriod.ancient;
      case HouseSystem.alcabitius:
      case HouseSystem.regiomontanus:
        return HistoricalPeriod.medieval;
      case HouseSystem.placidus:
      case HouseSystem.campanus:
        return HistoricalPeriod.renaissance;
      case HouseSystem.koch:
      case HouseSystem.equal:
      case HouseSystem.morinus:
        return HistoricalPeriod.modern;
      case HouseSystem.topocentric:
      case HouseSystem.equalVehlow:
      case HouseSystem.gauquelinSectors:
      case HouseSystem.horizonAzimuth:
      case HouseSystem.krusinskiPisaGoelzer:
      case HouseSystem.meridianHouses:
      case HouseSystem.apcHouses:
        return HistoricalPeriod.contemporary;
    }
  }

  /// 適用地區
  List<String> get suitableRegions {
    switch (this) {
      case HouseSystem.placidus:
        return ['中低緯度地區', '溫帶地區', '全球通用'];
      case HouseSystem.koch:
        return ['高緯度地區', '極地附近', '北歐地區'];
      case HouseSystem.equal:
      case HouseSystem.wholeSign:
        return ['全球通用', '任何緯度'];
      case HouseSystem.topocentric:
        return ['技術占星應用', '精密計算需求'];
      default:
        return ['特定用途', '研究應用'];
    }
  }

  /// 取得對應縮寫（反推字母）
  String get abbreviation {
    for (final entry in houseSystemMap.entries) {
      if (entry.value == this) {
        return entry.key;
      }
    }
    return '';
  }

  /// 是否為常用系統
  bool get isCommonlyUsed {
    return usageFrequency == UsageFrequency.veryHigh ||
        usageFrequency == UsageFrequency.high;
  }

  /// 是否適合初學者
  bool get isBeginner {
    return difficultyLevel == DifficultyLevel.beginner;
  }

  /// 是否為古典系統
  bool get isClassical {
    return historicalPeriod == HistoricalPeriod.ancient ||
        historicalPeriod == HistoricalPeriod.medieval;
  }

  /// 是否為現代系統
  bool get isModern {
    return historicalPeriod == HistoricalPeriod.modern ||
        historicalPeriod == HistoricalPeriod.contemporary;
  }
}

/// 反查：由字母找 HouseSystem
HouseSystem? houseSystemFromAbbreviation(String abbr) {
  return houseSystemMap[abbr.toUpperCase()];
}

/// 反查：由中文名稱找 HouseSystem
HouseSystem? houseSystemFromDisplayName(String name) {
  return HouseSystem.values.firstWhere(
    (hs) => hs.displayName == name,
    orElse: () => throw Exception('找不到對應的宮位系統'),
  );
}

Map<String, bool> planetVisibilityHome = {
  '太陽': true,
  '月亮': true,
  '水星': true,
  '金星': true,
  '火星': true,
  '木星': true,
  '土星': true,
  '天王星': true,
  '海王星': true,
  '冥王星': true,
  '上升': false,
  '中天': false,
  '下降': false,
  '天底': false,
  '北交點': false,
  '南交點': false,
  '莉莉絲': false,
  '凱龍星': false,
  '人龍星': false,
  '穀神星': false,
  '智神星': false,
  '婚神星': false,
  '灶神星': false,
  '日月中點': false,
  '宿命點': false,
};

Map<String, bool> planetVisibilityAnalysis = {
  '太陽': true,
  '月亮': true,
  '水星': true,
  '金星': true,
  '火星': true,
  '木星': true,
  '土星': true,
  '天王星': true,
  '海王星': true,
  '冥王星': true,
  '上升': true,
  '中天': true,
  '下降': true,
  '天底': true,
  '北交點': true,
  '南交點': true,
  '莉莉絲': true,
  '凱龍星': true,
  '人龍星': false,
  '穀神星': false,
  '智神星': false,
  '婚神星': true,
  '灶神星': false,
  '日月中點': true,
  '宿命點': true,
};

Map<String, double> aspectOrbsHome = {
  '合相': 3.0,
  '六分相': 3.0,
  '四分相': 3.0,
  '三分相': 3.0,
  '對分相': 3.0,
};

Map<String, double> aspectOrbsAnalysis = {
  '合相': 10.0,
  '六分相': 8.0,
  '四分相': 8.0,
  '三分相': 8.0,
  '對分相': 8.0,
};

/// 單一星盤類型的設定
class ChartTypeSettings {
  // 宮位系統
  HouseSystem houseSystem;

  // 行星顯示設定
  Map<String, bool> planetVisibility;

  // 相位容許度設定
  Map<String, double> aspectOrbs;

  // 相位顏色設定
  Map<String, Color> aspectColors;

  // 行星顏色設定
  Map<String, Color> planetColors;

  // 星座界主星顯示設定
  bool showZodiacRulers;

  // 星座背景色顯示設定
  bool showZodiacBackground;

  // 宮位度數顯示設定
  bool showHouseDegrees;

  // 行星度數顯示設定
  bool showPlanetDegrees;

  // 主要相位顯示設定
  bool showMajorAspects;

  // 次要相位顯示設定
  bool showMinorAspects;

  // 顏色主題設定
  String colorTheme;

  // 占星模式設定
  String astrologyMode;

  ChartTypeSettings({
    this.houseSystem = HouseSystem.placidus,
    Map<String, bool>? planetVisibility,
    Map<String, double>? aspectOrbs,
    Map<String, Color>? aspectColors,
    Map<String, Color>? planetColors,
    this.showZodiacRulers = false,
    this.showZodiacBackground = false,
    this.showHouseDegrees = false,
    this.showPlanetDegrees = false,
    this.showMajorAspects = true,
    this.showMinorAspects = false,
    this.colorTheme = 'modern',
    this.astrologyMode = 'modern',
  })  : planetVisibility = planetVisibility ?? _getDefaultPlanetVisibility(),
        aspectOrbs = aspectOrbs ?? _getDefaultAspectOrbs(),
        aspectColors = aspectColors ?? _getDefaultAspectColors(),
        planetColors = planetColors ?? _getPlanetColorsFromConstants();

  // 從 JSON 創建設定
  factory ChartTypeSettings.fromJson(Map<String, dynamic> json) {
    return ChartTypeSettings(
      houseSystem: _parseHouseSystemFromJson(json['houseSystem']) ??
          HouseSystem.placidus,
      planetVisibility: json['planetVisibility'] != null
          ? Map<String, bool>.from(json['planetVisibility'] as Map)
          : _getDefaultPlanetVisibility(),
      aspectOrbs: json['aspectOrbs'] != null
          ? Map<String, double>.from(
              (json['aspectOrbs'] as Map).map(
                (key, value) {
                  int intValue = (value as num).toInt();
                  if (intValue > 30) intValue = 30;
                  if (intValue < 0) intValue = 0;
                  return MapEntry(key, intValue.toDouble());
                },
              ),
            )
          : _getDefaultAspectOrbs(),
      aspectColors: json['aspectColors'] != null
          ? Map<String, Color>.from(
              (json['aspectColors'] as Map).map(
                (key, value) => MapEntry(key, Color(value as int)),
              ),
            )
          : _getDefaultAspectColors(),
      planetColors: json['planetColors'] != null
          ? Map<String, Color>.from(
              (json['planetColors'] as Map).map(
                (key, value) => MapEntry(key, Color(value as int)),
              ),
            )
          : _getPlanetColorsFromConstants(),
      showZodiacRulers: json['showZodiacRulers'] as bool? ?? false,
      showZodiacBackground: json['showZodiacBackground'] as bool? ?? false,
      showHouseDegrees: json['showHouseDegrees'] as bool? ?? false,
      showPlanetDegrees: json['showPlanetDegrees'] as bool? ?? false,
      showMajorAspects: json['showMajorAspects'] as bool? ?? true,
      showMinorAspects: json['showMinorAspects'] as bool? ?? false,
      colorTheme: json['colorTheme'] as String? ?? 'modern',
      astrologyMode: json['astrologyMode'] as String? ?? 'modern',
    );
  }

  /// 從 JSON 值解析 HouseSystem 枚舉
  static HouseSystem? _parseHouseSystemFromJson(dynamic value) {
    if (value == null) return null;

    if (value is HouseSystem) {
      // 如果已經是枚舉類型，直接返回
      return value;
    }

    if (value is String) {
      // 如果是字符串，嘗試解析
      final enumString = value.toLowerCase();

      // 處理 toString() 格式：HouseSystem.placidus -> placidus
      if (enumString.startsWith('housesystem.')) {
        final enumName = enumString.substring('housesystem.'.length);
        return _parseHouseSystemByName(enumName);
      }

      // 直接按名稱解析
      return _parseHouseSystemByName(enumString);
    }

    return null;
  }

  /// 根據名稱解析 HouseSystem
  static HouseSystem? _parseHouseSystemByName(String name) {
    switch (name.toLowerCase()) {
      case 'equal':
        return HouseSystem.equal;
      case 'alcabitius':
        return HouseSystem.alcabitius;
      case 'campanus':
        return HouseSystem.campanus;
      case 'gauquelinsectors':
        return HouseSystem.gauquelinSectors;
      case 'horizonazimuth':
        return HouseSystem.horizonAzimuth;
      case 'koch':
        return HouseSystem.koch;
      case 'morinus':
        return HouseSystem.morinus;
      case 'porphyry':
        return HouseSystem.porphyry;
      case 'placidus':
        return HouseSystem.placidus;
      case 'regiomontanus':
        return HouseSystem.regiomontanus;
      case 'topocentric':
        return HouseSystem.topocentric;
      case 'krusinskipisagoelzer':
        return HouseSystem.krusinskiPisaGoelzer;
      case 'equalvehlow':
        return HouseSystem.equalVehlow;
      case 'wholesign':
        return HouseSystem.wholeSign;
      case 'meridianhouses':
        return HouseSystem.meridianHouses;
      case 'apchouses':
        return HouseSystem.apcHouses;
      default:
        return null;
    }
  }

  // 轉換為 JSON
  Map<String, dynamic> toJson() {
    return {
      'houseSystem': houseSystem.toString(),
      'planetVisibility': planetVisibility,
      'aspectOrbs': aspectOrbs,
      'aspectColors': aspectColors.map(
        (key, value) => MapEntry(key, value.value),
      ),
      'planetColors': planetColors.map(
        (key, value) => MapEntry(key, value.value),
      ),
      'showZodiacRulers': showZodiacRulers,
      'showZodiacBackground': showZodiacBackground,
      'showHouseDegrees': showHouseDegrees,
      'showPlanetDegrees': showPlanetDegrees,
      'showMajorAspects': showMajorAspects,
      'showMinorAspects': showMinorAspects,
      'colorTheme': colorTheme,
      'astrologyMode': astrologyMode,
    };
  }

  // 預設行星顯示設定
  static Map<String, bool> _getDefaultPlanetVisibility() {
    return {
      '太陽': true,
      '月亮': true,
      '水星': true,
      '金星': true,
      '火星': true,
      '木星': true,
      '土星': true,
      '天王星': true,
      '海王星': true,
      '冥王星': true,
      '上升': true,
      '中天': true,
      '下降': true,
      '天底': true,
      '北交點': true,
      '南交點': true,
      '莉莉絲': false,
      '凱龍星': false,
      '人龍星': false,
      '穀神星': false,
      '智神星': false,
      '婚神星': false,
      '灶神星': false,
      '幸運點': true,
      '精神點': false,
      '旺點': false,
      '日月中點': false,
      '宿命點': false,
    };
  }

  // 預設相位容許度設定
  static Map<String, double> _getDefaultAspectOrbs() {
    return {
      '合相': 8.0,
      '六分相': 4.0,
      '四分相': 8.0,
      '三分相': 8.0,
      '對分相': 8.0,
    };
  }

  // 預設相位顏色設定
  static Map<String, Color> _getDefaultAspectColors() {
    return {
      '合相': Colors.red,
      '六分相': Colors.green,
      '四分相': Colors.red,
      '三分相': Colors.green,
      '對分相': const Color(0xFF0A0AFD),
    };
  }

  // 從 AstrologyConstants.PLANETS 中獲取行星顏色
  static Map<String, Color> _getPlanetColorsFromConstants() {
    final Map<String, Color> colors = {};

    for (final planet in AstrologyConstants.PLANETS) {
      final name = planet['name'] as String;
      final color = planet['color'] as Color;
      colors[name] = color;
    }

    return colors;
  }
}

/// 多星盤類型設定管理類
class MultiChartSettings {
  // 各星盤類型的設定
  Map<ChartType, ChartTypeSettings> chartTypeSettings;

  // 當前選中的星盤類型
  ChartType currentChartType;

  MultiChartSettings({
    Map<ChartType, ChartTypeSettings>? chartTypeSettings,
    this.currentChartType = ChartType.natal,
  }) : chartTypeSettings = chartTypeSettings ?? _getDefaultChartTypeSettings();

  // 獲取當前星盤類型的設定
  ChartTypeSettings get currentSettings => chartTypeSettings[currentChartType]!;

  // 設定當前星盤類型
  void setCurrentChartType(ChartType chartType) {
    currentChartType = chartType;
  }

  // 獲取指定星盤類型的設定
  ChartTypeSettings getSettingsForChartType(ChartType chartType) {
    return chartTypeSettings[chartType] ?? ChartTypeSettings();
  }

  // 更新指定星盤類型的設定
  void updateSettingsForChartType(
      ChartType chartType, ChartTypeSettings settings) {
    chartTypeSettings[chartType] = settings;
  }

  // 從 JSON 創建設定
  factory MultiChartSettings.fromJson(Map<String, dynamic> json) {
    final Map<ChartType, ChartTypeSettings> settings = {};

    if (json['chartTypeSettings'] != null) {
      final settingsMap = json['chartTypeSettings'] as Map<String, dynamic>;
      for (final entry in settingsMap.entries) {
        final chartType = ChartType.values.firstWhere(
          (type) => type.toString() == entry.key,
          orElse: () => ChartType.natal,
        );
        settings[chartType] =
            ChartTypeSettings.fromJson(entry.value as Map<String, dynamic>);
      }
    }

    return MultiChartSettings(
      chartTypeSettings:
          settings.isNotEmpty ? settings : _getDefaultChartTypeSettings(),
      currentChartType: json['currentChartType'] != null
          ? ChartType.values.firstWhere(
              (type) => type.toString() == json['currentChartType'],
              orElse: () => ChartType.natal,
            )
          : ChartType.natal,
    );
  }

  // 轉換為 JSON
  Map<String, dynamic> toJson() {
    return {
      'chartTypeSettings': chartTypeSettings.map(
        (key, value) => MapEntry(key.toString(), value.toJson()),
      ),
      'currentChartType': currentChartType.toString(),
    };
  }

  // 保存設定到 SharedPreferences
  Future<void> saveToPrefs() async {
    final prefs = await SharedPreferences.getInstance();
    final jsonString = jsonEncode(toJson());
    await prefs.setString('multi_chart_settings', jsonString);
  }

  // 從 SharedPreferences 加載設定
  static Future<MultiChartSettings> loadFromPrefs() async {
    final prefs = await SharedPreferences.getInstance();
    final String? settingsJson = prefs.getString('multi_chart_settings');

    if (settingsJson != null) {
      try {
        final Map<String, dynamic> jsonMap = jsonDecode(settingsJson);
        return MultiChartSettings.fromJson(jsonMap);
      } catch (e) {
        print('加載多星盤設定時出錯: $e');
        return MultiChartSettings();
      }
    }

    return MultiChartSettings();
  }

  // 獲取預設的各星盤類型設定
  static Map<ChartType, ChartTypeSettings> _getDefaultChartTypeSettings() {
    final Map<ChartType, ChartTypeSettings> settings = {};

    for (final chartType in ChartType.values) {
      settings[chartType] = _getDefaultSettingsForChartType(chartType);
    }

    return settings;
  }

  // 根據星盤類型獲取預設設定
  static ChartTypeSettings _getDefaultSettingsForChartType(
      ChartType chartType) {
    switch (chartType) {
      case ChartType.natal:
        return ChartTypeSettings(
          houseSystem: HouseSystem.placidus,
          showZodiacRulers: false,
          showHouseDegrees: true,
          showPlanetDegrees: true,
        );

      case ChartType.transit:
        return ChartTypeSettings(
          houseSystem: HouseSystem.placidus,
          showZodiacRulers: false,
          showHouseDegrees: false,
          showPlanetDegrees: true,
          aspectOrbs: {
            '合相': 6.0,
            '六分相': 3.0,
            '四分相': 6.0,
            '三分相': 6.0,
            '對分相': 6.0,
          },
        );

      case ChartType.synastry:
        return ChartTypeSettings(
          houseSystem: HouseSystem.placidus,
          showZodiacRulers: false,
          showHouseDegrees: false,
          showPlanetDegrees: false,
          aspectOrbs: {
            '合相': 8.0,
            '六分相': 4.0,
            '四分相': 8.0,
            '三分相': 8.0,
            '對分相': 8.0,
          },
        );

      case ChartType.composite:
        return ChartTypeSettings(
          houseSystem: HouseSystem.placidus,
          showZodiacRulers: false,
          showHouseDegrees: true,
          showPlanetDegrees: true,
        );

      case ChartType.eclipse:
        return ChartTypeSettings(
          houseSystem: HouseSystem.placidus,
          showZodiacRulers: true,
          showHouseDegrees: true,
          showPlanetDegrees: true,
          aspectOrbs: {
            '合相': 10.0,
            '六分相': 5.0,
            '四分相': 8.0,
            '三分相': 8.0,
            '對分相': 10.0,
          },
        );

      case ChartType.equinoxSolstice:
        return ChartTypeSettings(
          houseSystem: HouseSystem.placidus,
          showZodiacRulers: true,
          showHouseDegrees: true,
          showPlanetDegrees: true,
          aspectOrbs: {
            '合相': 8.0,
            '六分相': 4.0,
            '四分相': 6.0,
            '三分相': 6.0,
            '對分相': 8.0,
          },
        );

      default:
        return ChartTypeSettings();
    }
  }
}

/// 星盤設定類（保持向後兼容）
class ChartSettings {
  // 宮位系統
  HouseSystem houseSystem;

  // 行星顯示設定
  Map<String, bool> planetVisibility;

  // 相位容許度設定
  Map<String, double> aspectOrbs;

  // 相位顏色設定
  Map<String, Color> aspectColors;

  // 行星顏色設定
  Map<String, Color> planetColors;

  // 星座界主星顯示設定
  bool showZodiacRulers;

  // 星座背景色顯示設定
  bool showZodiacBackground;

  // 宮位度數顯示設定
  bool showHouseDegrees;

  // 行星度數顯示設定
  bool showPlanetDegrees;

  // 主要相位顯示設定
  bool showMajorAspects;

  // 次要相位顯示設定
  bool showMinorAspects;

  // 顏色主題設定
  String colorTheme;

  ChartSettings({
    this.houseSystem = HouseSystem.placidus,
    Map<String, bool>? planetVisibility,
    Map<String, double>? aspectOrbs,
    Map<String, Color>? aspectColors,
    Map<String, Color>? planetColors,
    this.showZodiacRulers = false,
    this.showZodiacBackground = false,
    this.showHouseDegrees = false,
    this.showPlanetDegrees = false,
    this.showMajorAspects = true,
    this.showMinorAspects = false,
    this.colorTheme = 'modern',
  })  : planetVisibility = planetVisibility ??
            AstrologyModeConfig.getModernConfig()
                .defaultSettings
                .planetVisibility,
        aspectOrbs = aspectOrbs ??
            AstrologyModeConfig.getModernConfig().defaultSettings.aspectOrbs,
        aspectColors = aspectColors ??
            AstrologyModeConfig.getModernConfig().defaultSettings.aspectColors,
        planetColors = planetColors ?? AstrologyModeConfig.getModernConfig().defaultSettings.planetColors;

  /// 從 JSON 值解析 HouseSystem 枚舉
  static HouseSystem? _parseHouseSystemFromJson(dynamic value) {
    if (value == null) return null;

    if (value is HouseSystem) {
      // 如果已經是枚舉類型，直接返回
      return value;
    }

    if (value is String) {
      // 如果是字符串，嘗試解析
      final enumString = value.toLowerCase();

      // 處理 toString() 格式：HouseSystem.placidus -> placidus
      if (enumString.startsWith('housesystem.')) {
        final enumName = enumString.substring('housesystem.'.length);
        return _parseHouseSystemByName(enumName);
      }

      // 直接按名稱解析
      return _parseHouseSystemByName(enumString);
    }

    return null;
  }

  /// 根據名稱解析 HouseSystem
  static HouseSystem? _parseHouseSystemByName(String name) {
    switch (name.toLowerCase()) {
      case 'equal':
        return HouseSystem.equal;
      case 'alcabitius':
        return HouseSystem.alcabitius;
      case 'campanus':
        return HouseSystem.campanus;
      case 'gauquelinsectors':
        return HouseSystem.gauquelinSectors;
      case 'horizonazimuth':
        return HouseSystem.horizonAzimuth;
      case 'koch':
        return HouseSystem.koch;
      case 'morinus':
        return HouseSystem.morinus;
      case 'porphyry':
        return HouseSystem.porphyry;
      case 'placidus':
        return HouseSystem.placidus;
      case 'regiomontanus':
        return HouseSystem.regiomontanus;
      case 'topocentric':
        return HouseSystem.topocentric;
      case 'krusinskipisagoelzer':
        return HouseSystem.krusinskiPisaGoelzer;
      case 'equalvehlow':
        return HouseSystem.equalVehlow;
      case 'wholesign':
        return HouseSystem.wholeSign;
      case 'meridianhouses':
        return HouseSystem.meridianHouses;
      case 'apchouses':
        return HouseSystem.apcHouses;
      default:
        return null;
    }
  }

  // 從 JSON 創建設定
  factory ChartSettings.fromJson(Map<String, dynamic> json) {
    return ChartSettings(
      houseSystem: _parseHouseSystemFromJson(json['houseSystem']) ??
          HouseSystem.placidus,
      planetVisibility: Map<String, bool>.from(json['planetVisibility'] as Map),
      aspectOrbs: Map<String, double>.from(
        (json['aspectOrbs'] as Map).map(
          (key, value) {
            // 確保相位容許度值為整數且不超過30
            int intValue = (value as num).toInt();
            if (intValue > 30) intValue = 30;
            if (intValue < 0) intValue = 0;
            return MapEntry(key, intValue.toDouble());
          },
        ),
      ),
      aspectColors: Map<String, Color>.from(
        (json['aspectColors'] as Map).map(
          (key, value) => MapEntry(key, Color(value as int)),
        ),
      ),
      planetColors: Map<String, Color>.from(
        (json['planetColors'] as Map).map(
          (key, value) => MapEntry(key, Color(value as int)),
        ),
      ),
      showZodiacRulers: json['showZodiacRulers'] as bool? ?? false,
      showZodiacBackground: json['showZodiacBackground'] as bool? ?? false,
      showHouseDegrees: json['showHouseDegrees'] as bool? ?? false,
      showPlanetDegrees: json['showPlanetDegrees'] as bool? ?? false,
      showMajorAspects: json['showMajorAspects'] as bool? ?? true,
      showMinorAspects: json['showMinorAspects'] as bool? ?? false,
      colorTheme: json['colorTheme'] as String? ?? 'modern',
    );
  }

  // 轉換為 JSON
  Map<String, dynamic> toJson() {
    // 創建一個新的 Map 來存儲行星顏色
    final Map<String, int> planetColorValues = {};

    // 使用 AstrologyConstants.PLANETS 中的顏色
    for (final planet in AstrologyConstants.PLANETS) {
      final name = planet['name'] as String;
      final color = planet['color'] as Color;
      planetColorValues[name] = color.value;
    }

    return {
      'houseSystem': houseSystem.toString(),
      'planetVisibility': planetVisibility,
      'aspectOrbs': aspectOrbs,
      'aspectColors': aspectColors.map(
        (key, value) => MapEntry(key, value.value),
      ),
      'planetColors': planetColorValues,
      'showZodiacRulers': showZodiacRulers,
      'showZodiacBackground': showZodiacBackground,
      'showHouseDegrees': showHouseDegrees,
      'showPlanetDegrees': showPlanetDegrees,
      'showMajorAspects': showMajorAspects,
      'showMinorAspects': showMinorAspects,
      'colorTheme': colorTheme,
    };
  }

  // 保存設定到 SharedPreferences
  Future<void> saveToPrefs() async {
    final prefs = await SharedPreferences.getInstance();
    final jsonString = jsonEncode(toJson());
    await prefs.setString('chart_settings', jsonString);
  }

  // 從 AstrologyConstants.PLANETS 中獲取行星顏色
  static Map<String, Color> _getPlanetColorsFromConstants() {
    final Map<String, Color> colors = {};

    for (final planet in AstrologyConstants.PLANETS) {
      final name = planet['name'] as String;
      final color = planet['color'] as Color;
      colors[name] = color;
    }

    return colors;
  }

  // 從 SharedPreferences 加載設定
  static Future<ChartSettings> loadFromPrefs() async {
    final prefs = await SharedPreferences.getInstance();
    final String? settingsJson = prefs.getString('chart_settings');

    if (settingsJson != null) {
      try {
        // 將 JSON 字符串轉換為 Map
        final Map<String, dynamic> jsonMap = jsonDecode(settingsJson);
        return ChartSettings.fromJson(jsonMap);
      } catch (e) {
        print('加載星盤設定時出錯: $e');
        return ChartSettings();
      }
    }

    return ChartSettings();
  }
}
