/// 阿拉伯點數據模型
class ArabicPoint {
  final int id;
  final String name;
  final String englishName;
  final String symbol;
  final String description;
  final String formula;
  final String dayFormula;
  final String nightFormula;
  final String category; // 'major', 'minor', 'special'
  final double importance; // 重要性等級 0.0-1.0
  final List<String> keywords;
  final String? traditionalName;
  final String? arabicName;
  final bool isTimeDependent; // 是否依賴日夜時間
  final List<String> relatedPlanets; // 相關行星
  final String interpretation; // 解釋說明

  const ArabicPoint({
    required this.id,
    required this.name,
    required this.englishName,
    required this.symbol,
    required this.description,
    required this.formula,
    required this.dayFormula,
    required this.nightFormula,
    this.category = 'minor',
    this.importance = 0.5,
    this.keywords = const [],
    this.traditionalName,
    this.arabicName,
    this.isTimeDependent = true,
    this.relatedPlanets = const [],
    this.interpretation = '',
  });

  /// 從 Map 創建 ArabicPoint 實例
  factory ArabicPoint.fromMap(Map<String, dynamic> map) {
    return ArabicPoint(
      id: map['id'] as int,
      name: map['name'] as String,
      englishName: map['englishName'] as String? ?? '',
      symbol: map['symbol'] as String? ?? '',
      description: map['description'] as String? ?? '',
      formula: map['formula'] as String? ?? '',
      dayFormula: map['dayFormula'] as String? ?? map['formula'] as String? ?? '',
      nightFormula: map['nightFormula'] as String? ?? map['formula'] as String? ?? '',
      category: map['category'] as String? ?? 'minor',
      importance: (map['importance'] as num?)?.toDouble() ?? 0.5,
      keywords: (map['keywords'] as List<dynamic>?)?.cast<String>() ?? [],
      traditionalName: map['traditionalName'] as String?,
      arabicName: map['arabicName'] as String?,
      isTimeDependent: map['isTimeDependent'] as bool? ?? true,
      relatedPlanets: (map['relatedPlanets'] as List<dynamic>?)?.cast<String>() ?? [],
      interpretation: map['interpretation'] as String? ?? '',
    );
  }

  /// 轉換為 Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'englishName': englishName,
      'symbol': symbol,
      'description': description,
      'formula': formula,
      'dayFormula': dayFormula,
      'nightFormula': nightFormula,
      'category': category,
      'importance': importance,
      'keywords': keywords,
      'traditionalName': traditionalName,
      'arabicName': arabicName,
      'isTimeDependent': isTimeDependent,
      'relatedPlanets': relatedPlanets,
      'interpretation': interpretation,
    };
  }

  /// 是否為主要阿拉伯點
  bool get isMajorPoint => category == 'major';

  /// 是否為次要阿拉伯點
  bool get isMinorPoint => category == 'minor';

  /// 是否為特殊阿拉伯點
  bool get isSpecialPoint => category == 'special';

  /// 獲取重要性等級描述
  String get importanceLevel {
    if (importance >= 0.8) return '極重要';
    if (importance >= 0.6) return '重要';
    if (importance >= 0.4) return '中等';
    if (importance >= 0.2) return '次要';
    return '參考';
  }

  /// 根據日夜時間獲取適當的公式
  String getFormulaForTime(bool isDayTime) {
    if (!isTimeDependent) return formula;
    return isDayTime ? dayFormula : nightFormula;
  }

  /// 獲取簡化的公式描述
  String get simplifiedFormula {
    // 移除複雜的符號，提供簡化描述
    if (formula.contains('太陽') && formula.contains('月亮')) {
      return '基於太陽和月亮的位置計算';
    } else if (formula.contains('上升')) {
      return '基於上升點的位置計算';
    } else if (relatedPlanets.isNotEmpty) {
      return '基於${relatedPlanets.join('、')}的位置計算';
    }
    return '特殊計算方式';
  }

  /// 是否與特定行星相關
  bool isRelatedToPlanet(String planetName) {
    return relatedPlanets.contains(planetName) ||
           formula.contains(planetName) ||
           dayFormula.contains(planetName) ||
           nightFormula.contains(planetName);
  }

  /// 獲取相關行星的數量
  int get relatedPlanetCount => relatedPlanets.length;

  /// 是否為福點（最重要的阿拉伯點）
  bool get isPartOfFortune => name.contains('福點') || englishName.toLowerCase().contains('fortune');

  /// 是否為靈魂點
  bool get isPartOfSpirit => name.contains('靈魂') || englishName.toLowerCase().contains('spirit');

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ArabicPoint && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'ArabicPoint(id: $id, name: $name, symbol: $symbol, category: $category)';
  }

  /// 複製並修改部分屬性
  ArabicPoint copyWith({
    int? id,
    String? name,
    String? englishName,
    String? symbol,
    String? description,
    String? formula,
    String? dayFormula,
    String? nightFormula,
    String? category,
    double? importance,
    List<String>? keywords,
    String? traditionalName,
    String? arabicName,
    bool? isTimeDependent,
    List<String>? relatedPlanets,
    String? interpretation,
  }) {
    return ArabicPoint(
      id: id ?? this.id,
      name: name ?? this.name,
      englishName: englishName ?? this.englishName,
      symbol: symbol ?? this.symbol,
      description: description ?? this.description,
      formula: formula ?? this.formula,
      dayFormula: dayFormula ?? this.dayFormula,
      nightFormula: nightFormula ?? this.nightFormula,
      category: category ?? this.category,
      importance: importance ?? this.importance,
      keywords: keywords ?? this.keywords,
      traditionalName: traditionalName ?? this.traditionalName,
      arabicName: arabicName ?? this.arabicName,
      isTimeDependent: isTimeDependent ?? this.isTimeDependent,
      relatedPlanets: relatedPlanets ?? this.relatedPlanets,
      interpretation: interpretation ?? this.interpretation,
    );
  }

  /// 預定義的重要阿拉伯點
  static const List<ArabicPoint> majorPoints = [
    ArabicPoint(
      id: 1,
      name: '福點',
      englishName: 'Part of Fortune',
      symbol: '⊗',
      description: '幸福、機遇、天賦才能的指標',
      formula: '上升 + 月亮 - 太陽',
      dayFormula: '上升 + 月亮 - 太陽',
      nightFormula: '上升 + 太陽 - 月亮',
      category: 'major',
      importance: 1.0,
      keywords: ['幸福', '機遇', '才能', '物質成功'],
      relatedPlanets: ['太陽', '月亮'],
      interpretation: '代表個人的幸福感和成功機會，是最重要的阿拉伯點',
    ),
    ArabicPoint(
      id: 2,
      name: '靈魂點',
      englishName: 'Part of Spirit',
      symbol: '☽',
      description: '精神層面的發展和靈性成長',
      formula: '上升 + 太陽 - 月亮',
      dayFormula: '上升 + 太陽 - 月亮',
      nightFormula: '上升 + 月亮 - 太陽',
      category: 'major',
      importance: 0.9,
      keywords: ['靈性', '精神', '意識', '成長'],
      relatedPlanets: ['太陽', '月亮'],
      interpretation: '代表精神層面的發展和靈性追求',
    ),
  ];
}
