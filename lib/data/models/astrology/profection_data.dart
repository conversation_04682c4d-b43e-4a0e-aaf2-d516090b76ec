/// 小限法數據模型
/// 
/// 小限法（Profection）是古典占星中的年度預測技術
/// 透過將上升點按年遞進一宮，來推演每年主題的變化
class ProfectionData {
  /// 當前年齡
  final int currentAge;
  
  /// 小限宮位（1-12）
  final int profectionHouse;
  
  /// 小限宮位對應的星座
  final String profectionSign;
  
  /// 小限宮位的主星（Time Lord）
  final int timeLordPlanetId;
  
  /// 小限宮位主星名稱
  final String timeLordPlanetName;
  
  /// 小限宮位主星符號
  final String timeLordPlanetSymbol;
  
  /// 小限宮位主星顏色
  final String timeLordPlanetColor;
  
  /// 本命盤中該宮位的主題
  final String houseTheme;
  
  /// 該宮位在本命盤中的行星
  final List<String> planetsInHouse;
  
  /// 小限年的開始日期
  final DateTime profectionYearStart;
  
  /// 小限年的結束日期
  final DateTime profectionYearEnd;
  
  /// 是否為當前小限年
  final bool isCurrent;
  
  /// 小限宮位的詳細描述
  final String description;
  
  /// 該年的重點關鍵字
  final List<String> keywords;

  const ProfectionData({
    required this.currentAge,
    required this.profectionHouse,
    required this.profectionSign,
    required this.timeLordPlanetId,
    required this.timeLordPlanetName,
    required this.timeLordPlanetSymbol,
    required this.timeLordPlanetColor,
    required this.houseTheme,
    required this.planetsInHouse,
    required this.profectionYearStart,
    required this.profectionYearEnd,
    required this.isCurrent,
    required this.description,
    required this.keywords,
  });

  /// 從 JSON 創建實例
  factory ProfectionData.fromJson(Map<String, dynamic> json) {
    return ProfectionData(
      currentAge: json['currentAge'] as int,
      profectionHouse: json['profectionHouse'] as int,
      profectionSign: json['profectionSign'] as String,
      timeLordPlanetId: json['timeLordPlanetId'] as int,
      timeLordPlanetName: json['timeLordPlanetName'] as String,
      timeLordPlanetSymbol: json['timeLordPlanetSymbol'] as String,
      timeLordPlanetColor: json['timeLordPlanetColor'] as String,
      houseTheme: json['houseTheme'] as String,
      planetsInHouse: List<String>.from(json['planetsInHouse'] as List),
      profectionYearStart: DateTime.parse(json['profectionYearStart'] as String),
      profectionYearEnd: DateTime.parse(json['profectionYearEnd'] as String),
      isCurrent: json['isCurrent'] as bool,
      description: json['description'] as String,
      keywords: List<String>.from(json['keywords'] as List),
    );
  }

  /// 轉換為 JSON
  Map<String, dynamic> toJson() {
    return {
      'currentAge': currentAge,
      'profectionHouse': profectionHouse,
      'profectionSign': profectionSign,
      'timeLordPlanetId': timeLordPlanetId,
      'timeLordPlanetName': timeLordPlanetName,
      'timeLordPlanetSymbol': timeLordPlanetSymbol,
      'timeLordPlanetColor': timeLordPlanetColor,
      'houseTheme': houseTheme,
      'planetsInHouse': planetsInHouse,
      'profectionYearStart': profectionYearStart.toIso8601String(),
      'profectionYearEnd': profectionYearEnd.toIso8601String(),
      'isCurrent': isCurrent,
      'description': description,
      'keywords': keywords,
    };
  }

  /// 創建副本
  ProfectionData copyWith({
    int? currentAge,
    int? profectionHouse,
    String? profectionSign,
    int? timeLordPlanetId,
    String? timeLordPlanetName,
    String? timeLordPlanetSymbol,
    String? timeLordPlanetColor,
    String? houseTheme,
    List<String>? planetsInHouse,
    DateTime? profectionYearStart,
    DateTime? profectionYearEnd,
    bool? isCurrent,
    String? description,
    List<String>? keywords,
  }) {
    return ProfectionData(
      currentAge: currentAge ?? this.currentAge,
      profectionHouse: profectionHouse ?? this.profectionHouse,
      profectionSign: profectionSign ?? this.profectionSign,
      timeLordPlanetId: timeLordPlanetId ?? this.timeLordPlanetId,
      timeLordPlanetName: timeLordPlanetName ?? this.timeLordPlanetName,
      timeLordPlanetSymbol: timeLordPlanetSymbol ?? this.timeLordPlanetSymbol,
      timeLordPlanetColor: timeLordPlanetColor ?? this.timeLordPlanetColor,
      houseTheme: houseTheme ?? this.houseTheme,
      planetsInHouse: planetsInHouse ?? this.planetsInHouse,
      profectionYearStart: profectionYearStart ?? this.profectionYearStart,
      profectionYearEnd: profectionYearEnd ?? this.profectionYearEnd,
      isCurrent: isCurrent ?? this.isCurrent,
      description: description ?? this.description,
      keywords: keywords ?? this.keywords,
    );
  }

  @override
  String toString() {
    return 'ProfectionData(age: $currentAge, house: $profectionHouse, sign: $profectionSign, timeLord: $timeLordPlanetName)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ProfectionData &&
        other.currentAge == currentAge &&
        other.profectionHouse == profectionHouse &&
        other.profectionSign == profectionSign &&
        other.timeLordPlanetId == timeLordPlanetId;
  }

  @override
  int get hashCode {
    return Object.hash(
      currentAge,
      profectionHouse,
      profectionSign,
      timeLordPlanetId,
    );
  }
}

/// 小限法時間軸結果
class ProfectionTimelineResult {
  /// 當前小限法信息
  final ProfectionData currentProfection;
  
  /// 完整的小限法時間軸（過去、現在、未來）
  final List<ProfectionData> timeline;
  
  /// 出生日期時間
  final DateTime birthDateTime;
  
  /// 上升點經度
  final double ascendantLongitude;
  
  /// 緯度
  final double latitude;
  
  /// 經度
  final double longitude;

  const ProfectionTimelineResult({
    required this.currentProfection,
    required this.timeline,
    required this.birthDateTime,
    required this.ascendantLongitude,
    required this.latitude,
    required this.longitude,
  });

  /// 從 JSON 創建實例
  factory ProfectionTimelineResult.fromJson(Map<String, dynamic> json) {
    return ProfectionTimelineResult(
      currentProfection: ProfectionData.fromJson(json['currentProfection']),
      timeline: (json['timeline'] as List)
          .map((item) => ProfectionData.fromJson(item))
          .toList(),
      birthDateTime: DateTime.parse(json['birthDateTime'] as String),
      ascendantLongitude: json['ascendantLongitude'] as double,
      latitude: json['latitude'] as double,
      longitude: json['longitude'] as double,
    );
  }

  /// 轉換為 JSON
  Map<String, dynamic> toJson() {
    return {
      'currentProfection': currentProfection.toJson(),
      'timeline': timeline.map((item) => item.toJson()).toList(),
      'birthDateTime': birthDateTime.toIso8601String(),
      'ascendantLongitude': ascendantLongitude,
      'latitude': latitude,
      'longitude': longitude,
    };
  }

  @override
  String toString() {
    return 'ProfectionTimelineResult(current: ${currentProfection.currentAge}歲, timeline: ${timeline.length}年)';
  }
}
