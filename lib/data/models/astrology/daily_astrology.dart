import 'package:cloud_firestore/cloud_firestore.dart';

/// 每日星相事件類型
enum DailyAstrologyEventType {
  moonSignChange,     // 月亮換星座
  planetaryAspect,    // 行星相位
  retrogradeStart,    // 逆行開始
  retrogradeEnd,      // 逆行結束
  newMoon,           // 新月
  fullMoon,          // 滿月
  planetSignChange,   // 行星換星座
  lunarPhase,        // 月相變化
  moonVoidOfCourse,  // 月亮空亡
  specialEvent,      // 特殊天象
}

/// 每日星相事件
class DailyAstrologyEvent {
  final String id;
  final DailyAstrologyEventType type;
  final String title;
  final String description;
  final DateTime eventTime;
  final Map<String, dynamic> eventData; // 存儲具體的占星數據
  final int priority; // 優先級 1-5，5 最重要
  final List<String> tags; // 標籤，如 ['愛情', '事業', '健康']

  const DailyAstrologyEvent({
    required this.id,
    required this.type,
    required this.title,
    required this.description,
    required this.eventTime,
    required this.eventData,
    this.priority = 3,
    this.tags = const [],
  });

  /// 從 JSON 創建實例
  factory DailyAstrologyEvent.fromJson(Map<String, dynamic> json) {
    return DailyAstrologyEvent(
      id: json['id'] as String,
      type: DailyAstrologyEventType.values.firstWhere(
        (e) => e.toString() == json['type'],
        orElse: () => DailyAstrologyEventType.specialEvent,
      ),
      title: json['title'] as String,
      description: json['description'] as String,
      eventTime: _parseDateTime(json['event_time']),
      eventData: json['event_data'] as Map<String, dynamic>? ?? {},
      priority: json['priority'] as int? ?? 3,
      tags: List<String>.from(json['tags'] as List? ?? []),
    );
  }

  /// 轉換為 JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.toString(),
      'title': title,
      'description': description,
      'event_time': eventTime.toIso8601String(),
      'event_data': eventData,
      'priority': priority,
      'tags': tags,
    };
  }

  /// 解析 DateTime
  static DateTime _parseDateTime(dynamic value) {
    if (value == null) return DateTime.now();
    
    if (value is Timestamp) {
      return value.toDate();
    }
    
    if (value is String) {
      return DateTime.parse(value);
    }
    
    if (value is DateTime) {
      return value;
    }
    
    return DateTime.now();
  }

  @override
  String toString() {
    return 'DailyAstrologyEvent(id: $id, type: $type, title: $title, eventTime: $eventTime)';
  }
}

/// 每日星相數據
class DailyAstrologyData {
  final String id;
  final DateTime date;
  final List<DailyAstrologyEvent> events;
  final String generalMessage; // 當日總體訊息
  final Map<String, String> signMessages; // 各星座專屬訊息
  final DateTime createdAt;
  final DateTime updatedAt;

  const DailyAstrologyData({
    required this.id,
    required this.date,
    required this.events,
    required this.generalMessage,
    this.signMessages = const {},
    required this.createdAt,
    required this.updatedAt,
  });

  /// 從 JSON 創建實例
  factory DailyAstrologyData.fromJson(Map<String, dynamic> json) {
    final eventsData = json['events'] as List? ?? [];
    final events = eventsData
        .map((e) => DailyAstrologyEvent.fromJson(e as Map<String, dynamic>))
        .toList();

    return DailyAstrologyData(
      id: json['id'] as String,
      date: _parseDateTime(json['date']),
      events: events,
      generalMessage: json['general_message'] as String? ?? '',
      signMessages: Map<String, String>.from(json['sign_messages'] as Map? ?? {}),
      createdAt: _parseDateTime(json['created_at']),
      updatedAt: _parseDateTime(json['updated_at']),
    );
  }

  /// 轉換為 JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'date': date.toIso8601String(),
      'events': events.map((e) => e.toJson()).toList(),
      'general_message': generalMessage,
      'sign_messages': signMessages,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// 轉換為 Firestore 格式
  Map<String, dynamic> toFirestoreJson() {
    return {
      'date': Timestamp.fromDate(date),
      'events': events.map((e) => e.toJson()).toList(),
      'general_message': generalMessage,
      'sign_messages': signMessages,
      'created_at': FieldValue.serverTimestamp(),
      'updated_at': FieldValue.serverTimestamp(),
    };
  }

  /// 獲取最重要的事件
  DailyAstrologyEvent? get mostImportantEvent {
    if (events.isEmpty) return null;
    
    events.sort((a, b) => b.priority.compareTo(a.priority));
    return events.first;
  }

  /// 獲取特定類型的事件
  List<DailyAstrologyEvent> getEventsByType(DailyAstrologyEventType type) {
    return events.where((event) => event.type == type).toList();
  }

  /// 獲取包含特定標籤的事件
  List<DailyAstrologyEvent> getEventsByTag(String tag) {
    return events.where((event) => event.tags.contains(tag)).toList();
  }

  /// 解析 DateTime
  static DateTime _parseDateTime(dynamic value) {
    if (value == null) return DateTime.now();
    
    if (value is Timestamp) {
      return value.toDate();
    }
    
    if (value is String) {
      return DateTime.parse(value);
    }
    
    if (value is DateTime) {
      return value;
    }
    
    return DateTime.now();
  }

  @override
  String toString() {
    return 'DailyAstrologyData(id: $id, date: $date, events: ${events.length})';
  }
}

/// 個人化每日星相
class PersonalizedDailyAstrology {
  final String id;
  final String userId;
  final DateTime date;
  final String personalizedMessage; // 個人化訊息
  final List<String> personalAspects; // 個人相位提醒
  final List<String> recommendations; // 個人建議
  final Map<String, dynamic> birthDataSnapshot; // 出生資料快照
  final DateTime createdAt;

  const PersonalizedDailyAstrology({
    required this.id,
    required this.userId,
    required this.date,
    required this.personalizedMessage,
    this.personalAspects = const [],
    this.recommendations = const [],
    this.birthDataSnapshot = const {},
    required this.createdAt,
  });

  /// 從 JSON 創建實例
  factory PersonalizedDailyAstrology.fromJson(Map<String, dynamic> json) {
    return PersonalizedDailyAstrology(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      date: DailyAstrologyData._parseDateTime(json['date']),
      personalizedMessage: json['personalized_message'] as String? ?? '',
      personalAspects: List<String>.from(json['personal_aspects'] as List? ?? []),
      recommendations: List<String>.from(json['recommendations'] as List? ?? []),
      birthDataSnapshot: json['birth_data_snapshot'] as Map<String, dynamic>? ?? {},
      createdAt: DailyAstrologyData._parseDateTime(json['created_at']),
    );
  }

  /// 轉換為 JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'date': date.toIso8601String(),
      'personalized_message': personalizedMessage,
      'personal_aspects': personalAspects,
      'recommendations': recommendations,
      'birth_data_snapshot': birthDataSnapshot,
      'created_at': createdAt.toIso8601String(),
    };
  }

  /// 轉換為 Firestore 格式
  Map<String, dynamic> toFirestoreJson() {
    return {
      'user_id': userId,
      'date': Timestamp.fromDate(date),
      'personalized_message': personalizedMessage,
      'personal_aspects': personalAspects,
      'recommendations': recommendations,
      'birth_data_snapshot': birthDataSnapshot,
      'created_at': FieldValue.serverTimestamp(),
    };
  }

  @override
  String toString() {
    return 'PersonalizedDailyAstrology(id: $id, userId: $userId, date: $date)';
  }
}
