import 'package:flutter/material.dart';

/// 通知類型枚舉
enum NotificationType {
  /// 系統公告
  systemAnnouncement('system_announcement', '系統公告', Icons.announcement, Colors.blue),
  
  /// 功能更新
  featureUpdate('feature_update', '功能更新', Icons.new_releases, Colors.green),
  
  /// 促銷活動
  promotion('promotion', '促銷活動', Icons.local_offer, Colors.orange),
  
  /// 重要通知
  important('important', '重要通知', Icons.priority_high, Colors.red),
  
  /// 一般通知
  general('general', '一般通知', Icons.notifications, Colors.grey),
  
  /// 占星事件
  astroEvent('astro_event', '占星事件', Icons.stars, Colors.purple),
  
  /// 個人提醒
  personalReminder('personal_reminder', '個人提醒', Icons.person, Colors.teal);

  const NotificationType(this.value, this.displayName, this.icon, this.color);

  final String value;
  final String displayName;
  final IconData icon;
  final Color color;

  /// 從字符串值獲取通知類型
  static NotificationType fromValue(String value) {
    return NotificationType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => NotificationType.general,
    );
  }
}

/// 通知優先級
enum NotificationPriority {
  low(0, '低'),
  normal(1, '普通'),
  high(2, '高'),
  urgent(3, '緊急');

  const NotificationPriority(this.level, this.displayName);

  final int level;
  final String displayName;

  /// 從等級獲取優先級
  static NotificationPriority fromLevel(int level) {
    return NotificationPriority.values.firstWhere(
      (priority) => priority.level == level,
      orElse: () => NotificationPriority.normal,
    );
  }
}

/// 通知模型
class NotificationModel {
  final String id;
  final String title;
  final String body;
  final NotificationType type;
  final NotificationPriority priority;
  final DateTime createdAt;
  final DateTime? scheduledAt;
  final bool isRead;
  final Map<String, dynamic>? data;
  final String? imageUrl;
  final String? actionUrl;
  final String? userId; // 特定用戶通知，null 表示全體通知

  const NotificationModel({
    required this.id,
    required this.title,
    required this.body,
    required this.type,
    this.priority = NotificationPriority.normal,
    required this.createdAt,
    this.scheduledAt,
    this.isRead = false,
    this.data,
    this.imageUrl,
    this.actionUrl,
    this.userId,
  });

  /// 從 JSON 創建通知模型
  factory NotificationModel.fromJson(Map<String, dynamic> json) {
    return NotificationModel(
      id: json['id'] as String,
      title: json['title'] as String,
      body: json['body'] as String,
      type: NotificationType.fromValue(json['type'] as String? ?? 'general'),
      priority: NotificationPriority.fromLevel(json['priority'] as int? ?? 1),
      createdAt: DateTime.parse(json['createdAt'] as String),
      scheduledAt: json['scheduledAt'] != null 
          ? DateTime.parse(json['scheduledAt'] as String)
          : null,
      isRead: json['isRead'] as bool? ?? false,
      data: json['data'] as Map<String, dynamic>?,
      imageUrl: json['imageUrl'] as String?,
      actionUrl: json['actionUrl'] as String?,
      userId: json['userId'] as String?,
    );
  }

  /// 轉換為 JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'body': body,
      'type': type.value,
      'priority': priority.level,
      'createdAt': createdAt.toIso8601String(),
      'scheduledAt': scheduledAt?.toIso8601String(),
      'isRead': isRead,
      'data': data,
      'imageUrl': imageUrl,
      'actionUrl': actionUrl,
      'userId': userId,
    };
  }

  /// 從 Firebase Messaging RemoteMessage 創建通知模型
  factory NotificationModel.fromRemoteMessage(
    String id,
    Map<String, dynamic> data,
  ) {
    return NotificationModel(
      id: id,
      title: data['title'] as String? ?? '新通知',
      body: data['body'] as String? ?? '',
      type: NotificationType.fromValue(data['type'] as String? ?? 'general'),
      priority: NotificationPriority.fromLevel(
        int.tryParse(data['priority'] as String? ?? '1') ?? 1,
      ),
      createdAt: DateTime.now(),
      scheduledAt: data['scheduledAt'] != null 
          ? DateTime.tryParse(data['scheduledAt'] as String)
          : null,
      data: data,
      imageUrl: data['imageUrl'] as String?,
      actionUrl: data['actionUrl'] as String?,
      userId: data['userId'] as String?,
    );
  }

  /// 創建副本並修改某些屬性
  NotificationModel copyWith({
    String? id,
    String? title,
    String? body,
    NotificationType? type,
    NotificationPriority? priority,
    DateTime? createdAt,
    DateTime? scheduledAt,
    bool? isRead,
    Map<String, dynamic>? data,
    String? imageUrl,
    String? actionUrl,
    String? userId,
  }) {
    return NotificationModel(
      id: id ?? this.id,
      title: title ?? this.title,
      body: body ?? this.body,
      type: type ?? this.type,
      priority: priority ?? this.priority,
      createdAt: createdAt ?? this.createdAt,
      scheduledAt: scheduledAt ?? this.scheduledAt,
      isRead: isRead ?? this.isRead,
      data: data ?? this.data,
      imageUrl: imageUrl ?? this.imageUrl,
      actionUrl: actionUrl ?? this.actionUrl,
      userId: userId ?? this.userId,
    );
  }

  /// 是否為全體通知
  bool get isGlobalNotification => userId == null;

  /// 是否為個人通知
  bool get isPersonalNotification => userId != null;

  /// 是否為緊急通知
  bool get isUrgent => priority == NotificationPriority.urgent;

  /// 是否為高優先級通知
  bool get isHighPriority => 
      priority == NotificationPriority.high || priority == NotificationPriority.urgent;

  /// 格式化創建時間
  String get formattedCreatedAt {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inMinutes < 1) {
      return '剛剛';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}分鐘前';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}小時前';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}天前';
    } else {
      return '${createdAt.month}/${createdAt.day}';
    }
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is NotificationModel &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'NotificationModel{id: $id, title: $title, type: ${type.displayName}, priority: ${priority.displayName}, isRead: $isRead}';
  }
}

/// 帶有 ID 的通知模型（用於 Firestore）
class NotificationWithId {
  final String id;
  final NotificationModel notification;

  const NotificationWithId({
    required this.id,
    required this.notification,
  });

  /// 從 Firestore 文檔創建
  factory NotificationWithId.fromFirestore(String id, Map<String, dynamic> data) {
    return NotificationWithId(
      id: id,
      notification: NotificationModel.fromJson({...data, 'id': id}),
    );
  }
}
