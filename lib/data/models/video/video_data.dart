/// YouTube 影片資料模型
/// 用於儲存和管理 YouTube 影片的相關資訊
class VideoData {
  final String id;
  final String title;
  final String description;
  final String youtubeId;
  final String thumbnailUrl;
  final String duration;
  final DateTime publishedAt;
  final List<String> tags;
  final bool isPopular;

  const VideoData({
    required this.id,
    required this.title,
    required this.description,
    required this.youtubeId,
    required this.thumbnailUrl,
    required this.duration,
    required this.publishedAt,
    required this.tags,
    this.isPopular = false,
  });

  /// 從 JSON 創建 VideoData 實例
  factory VideoData.fromJson(Map<String, dynamic> json) {
    return VideoData(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      youtubeId: json['youtubeId'] as String,
      thumbnailUrl: json['thumbnailUrl'] as String,
      duration: json['duration'] as String,
      publishedAt: DateTime.parse(json['publishedAt'] as String),
      tags: List<String>.from(json['tags'] as List),
      isPopular: json['isPopular'] as bool? ?? false,
    );
  }

  /// 轉換為 JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'youtubeId': youtubeId,
      'thumbnailUrl': thumbnailUrl,
      'duration': duration,
      'publishedAt': publishedAt.toIso8601String(),
      'tags': tags,
      'isPopular': isPopular,
    };
  }

  /// 獲取 YouTube 影片 URL
  String get youtubeUrl => 'https://www.youtube.com/watch?v=$youtubeId';

  /// 獲取 YouTube 嵌入 URL
  String get embedUrl => 'https://www.youtube.com/embed/$youtubeId';

  /// 複製並修改屬性
  VideoData copyWith({
    String? id,
    String? title,
    String? description,
    String? youtubeId,
    String? thumbnailUrl,
    String? duration,
    DateTime? publishedAt,
    List<String>? tags,
    bool? isPopular,
  }) {
    return VideoData(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      youtubeId: youtubeId ?? this.youtubeId,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      duration: duration ?? this.duration,
      publishedAt: publishedAt ?? this.publishedAt,
      tags: tags ?? this.tags,
      isPopular: isPopular ?? this.isPopular,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is VideoData && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'VideoData(id: $id, title: $title, youtubeId: $youtubeId)';
  }
}

/// 影片分類資料模型
class VideoCategory {
  final String id;
  final String name;
  final String description;
  final List<VideoData> videos;

  const VideoCategory({
    required this.id,
    required this.name,
    required this.description,
    required this.videos,
  });

  /// 從 JSON 創建 VideoCategory 實例
  factory VideoCategory.fromJson(Map<String, dynamic> json) {
    return VideoCategory(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      videos: (json['videos'] as List)
          .map((video) => VideoData.fromJson(video as Map<String, dynamic>))
          .toList(),
    );
  }

  /// 轉換為 JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'videos': videos.map((video) => video.toJson()).toList(),
    };
  }

  /// 獲取熱門影片
  List<VideoData> get popularVideos {
    return videos.where((video) => video.isPopular).toList();
  }

  /// 根據標籤篩選影片
  List<VideoData> getVideosByTag(String tag) {
    return videos.where((video) => video.tags.contains(tag)).toList();
  }

  /// 複製並修改屬性
  VideoCategory copyWith({
    String? id,
    String? name,
    String? description,
    List<VideoData>? videos,
  }) {
    return VideoCategory(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      videos: videos ?? this.videos,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is VideoCategory && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'VideoCategory(id: $id, name: $name, videoCount: ${videos.length})';
  }
}

/// YouTube 影片配置資料模型
class YouTubeVideosConfig {
  final List<VideoCategory> categories;
  final DateTime lastUpdated;

  const YouTubeVideosConfig({
    required this.categories,
    required this.lastUpdated,
  });

  /// 從 JSON 創建 YouTubeVideosConfig 實例
  factory YouTubeVideosConfig.fromJson(Map<String, dynamic> json) {
    return YouTubeVideosConfig(
      categories: (json['categories'] as List)
          .map((category) => VideoCategory.fromJson(category as Map<String, dynamic>))
          .toList(),
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
    );
  }

  /// 轉換為 JSON
  Map<String, dynamic> toJson() {
    return {
      'categories': categories.map((category) => category.toJson()).toList(),
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }

  /// 獲取所有影片
  List<VideoData> get allVideos {
    return categories.expand((category) => category.videos).toList();
  }

  /// 獲取所有熱門影片
  List<VideoData> get popularVideos {
    return allVideos.where((video) => video.isPopular).toList();
  }

  /// 根據分類 ID 獲取分類
  VideoCategory? getCategoryById(String categoryId) {
    try {
      return categories.firstWhere((category) => category.id == categoryId);
    } catch (e) {
      return null;
    }
  }

  /// 根據影片 ID 獲取影片
  VideoData? getVideoById(String videoId) {
    for (final category in categories) {
      try {
        return category.videos.firstWhere((video) => video.id == videoId);
      } catch (e) {
        continue;
      }
    }
    return null;
  }

  /// 搜尋影片
  List<VideoData> searchVideos(String query) {
    final lowerQuery = query.toLowerCase();
    return allVideos.where((video) {
      return video.title.toLowerCase().contains(lowerQuery) ||
             video.description.toLowerCase().contains(lowerQuery) ||
             video.tags.any((tag) => tag.toLowerCase().contains(lowerQuery));
    }).toList();
  }

  @override
  String toString() {
    return 'YouTubeVideosConfig(categories: ${categories.length}, lastUpdated: $lastUpdated)';
  }
}
