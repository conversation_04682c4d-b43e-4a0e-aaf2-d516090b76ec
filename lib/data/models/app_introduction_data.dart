import 'package:flutter/material.dart';

/// 應用程式功能資料類別
/// 用於表示應用程式的各項功能特色
class AppFeature {
  /// 功能名稱
  final String title;
  
  /// 功能描述
  final String description;
  
  /// 功能圖標
  final IconData icon;
  
  /// 功能顏色
  final Color color;

  const AppFeature({
    required this.title,
    required this.description,
    required this.icon,
    required this.color,
  });
}

/// 介紹頁面資料類別
/// 用於管理應用程式介紹頁面的內容
class IntroductionPageData {
  /// 頁面標題
  final String title;
  
  /// 頁面副標題
  final String subtitle;
  
  /// 頁面描述
  final String description;
  
  /// 頁面圖標
  final IconData icon;
  
  /// 頁面主色調
  final Color primaryColor;
  
  /// 頁面功能列表
  final List<AppFeature> features;
  
  /// 頁面圖片路徑（可選）
  final String? imagePath;

  const IntroductionPageData({
    required this.title,
    required this.subtitle,
    required this.description,
    required this.icon,
    required this.primaryColor,
    required this.features,
    this.imagePath,
  });
}

/// 應用程式介紹內容配置
/// 提供預設的介紹頁面內容
class AppIntroductionConfig {
  /// 獲取所有介紹頁面資料
  static List<IntroductionPageData> getIntroductionPages() {
    return [
      // 第一頁：歡迎頁面
      IntroductionPageData(
        title: '歡迎來到 AstReal',
        subtitle: '專業占星分析應用',
        description: '結合傳統占星學與現代科技\n為您提供精準的星盤解讀與人生指引',
        icon: Icons.auto_awesome,
        primaryColor: const Color(0xFF3F51B5), // royalIndigo
        features: [
          AppFeature(
            title: '專業占星',
            description: '基於高精度計算引擎',
            icon: Icons.precision_manufacturing,
            color: const Color(0xFF3F51B5),
          ),
          AppFeature(
            title: '深入剖析',
            description: '服務提供智能解讀',
            icon: Icons.psychology,
            color: const Color(0xFF7986CB),
          ),
          AppFeature(
            title: '跨平台支援',
            description: '支援 iOS、Android、Web、桌面版',
            icon: Icons.devices,
            color: const Color(0xFFF5A623),
          ),
        ],
      ),
      
      // 第二頁：雙模式設計
      IntroductionPageData(
        title: '雙模式設計',
        subtitle: '適合不同程度的用戶',
        description: '無論您是占星初學者還是專業占星師\n都能找到適合的使用模式',
        icon: Icons.people,
        primaryColor: const Color(0xFFF5A623), // solarAmber
        features: [
          AppFeature(
            title: 'Starlight 初心者模式',
            description: '簡化介面，通俗易懂的解讀',
            icon: Icons.star_outline,
            color: const Color(0xFFF5A623),
          ),
          AppFeature(
            title: 'Starmaster 占星師模式',
            description: '專業工具，完整功能存取',
            icon: Icons.star,
            color: const Color(0xFF3F51B5),
          ),
          AppFeature(
            title: '隨時切換',
            description: '可在設定中隨時切換使用模式',
            icon: Icons.swap_horiz,
            color: const Color(0xFF7986CB),
          ),
        ],
      ),
      
      // 第三頁：豐富功能
      IntroductionPageData(
        title: '豐富的占星功能',
        subtitle: '全方位的星盤分析',
        description: '支援多種星盤類型與分析方法\n滿足各種占星研究需求',
        icon: Icons.dashboard,
        primaryColor: const Color(0xFF7986CB), // indigoLight
        features: [
          AppFeature(
            title: '多種星盤類型',
            description: '本命盤、合盤、推運盤、返照盤等',
            icon: Icons.donut_large,
            color: const Color(0xFF6366F1),
          ),
          AppFeature(
            title: '專業分析工具',
            description: '相位分析、行運預測、時事分析',
            icon: Icons.analytics,
            color: const Color(0xFF10B981),
          ),
          AppFeature(
            title: '個人化設定',
            description: '自訂宮位系統、行星選擇、相位設定',
            icon: Icons.tune,
            color: const Color(0xFFEC4899),
          ),
        ],
      ),
      
      // 第四頁：開始使用
      IntroductionPageData(
        title: '開始您的占星之旅',
        subtitle: '準備好探索星空的奧秘了嗎？',
        description: '選擇適合您的使用模式\n開始探索個人的星盤世界',
        icon: Icons.rocket_launch,
        primaryColor: const Color(0xFF303F9F), // indigoSurface
        features: [
          AppFeature(
            title: '建立個人檔案',
            description: '輸入出生資料，生成專屬星盤',
            icon: Icons.person_add,
            color: const Color(0xFF3F51B5),
          ),
          AppFeature(
            title: '探索星盤功能',
            description: '體驗各種分析工具和解讀功能',
            icon: Icons.explore,
            color: const Color(0xFFF5A623),
          ),
          AppFeature(
            title: '持續學習成長',
            description: '透過實際使用深入了解占星學',
            icon: Icons.school,
            color: const Color(0xFF7986CB),
          ),
        ],
      ),
    ];
  }
}
