/// 占星諮詢分析結果資料類別
/// 遵循 MVVM 架構，包含分析結果的所有資訊
class ConsultationAnalysisResult {
  /// 分析主題ID
  final String themeId;
  
  /// 分析主題標題
  final String themeTitle;
  
  /// 分析主題副標題
  final String themeSubtitle;
  
  /// 分析主題描述
  final String themeDescription;
  
  /// 分析要點列表
  final List<String> analysisPoints;
  
  /// 完整星盤資訊文本
  final String chartInfoText;
  
  /// 分析文案內容
  final String analysisContent;
  
  /// 分析時間
  final DateTime analysisTime;
  
  /// 建構函數
  const ConsultationAnalysisResult({
    required this.themeId,
    required this.themeTitle,
    required this.themeSubtitle,
    required this.themeDescription,
    required this.analysisPoints,
    required this.chartInfoText,
    required this.analysisContent,
    required this.analysisTime,
  });
  
  /// 生成完整的分析報告文本
  String generateFullAnalysisText() {
    final buffer = StringBuffer();
    
    // 添加標題
    buffer.writeln('=' * 50);
    buffer.writeln('占星諮詢分析報告');
    buffer.writeln('=' * 50);
    buffer.writeln();
    
    // 添加主題資訊
    buffer.writeln('【分析主題】');
    buffer.writeln('主題：$themeTitle');
    buffer.writeln('重點：$themeSubtitle');
    buffer.writeln('說明：$themeDescription');
    buffer.writeln();
    
    // 添加分析要點
    buffer.writeln('【分析要點】');
    for (int i = 0; i < analysisPoints.length; i++) {
      buffer.writeln('${i + 1}. ${analysisPoints[i]}');
    }
    buffer.writeln();
    
    // 添加完整星盤資訊
    buffer.writeln('【完整星盤資訊】');
    buffer.writeln(chartInfoText);
    buffer.writeln();
    
    // 添加分析文案
    buffer.writeln('【分析文案】');
    buffer.writeln(analysisContent);
    buffer.writeln();
    
    // 添加分析時間
    buffer.writeln('分析時間：${_formatDateTime(analysisTime)}');
    buffer.writeln('=' * 50);
    
    return buffer.toString();
  }
  
  /// 格式化日期時間
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}年${dateTime.month}月${dateTime.day}日 '
           '${dateTime.hour.toString().padLeft(2, '0')}:'
           '${dateTime.minute.toString().padLeft(2, '0')}';
  }
  
  /// 複製到剪貼簿的方法
  Map<String, dynamic> toJson() {
    return {
      'themeId': themeId,
      'themeTitle': themeTitle,
      'themeSubtitle': themeSubtitle,
      'themeDescription': themeDescription,
      'analysisPoints': analysisPoints,
      'chartInfoText': chartInfoText,
      'analysisContent': analysisContent,
      'analysisTime': analysisTime.toIso8601String(),
    };
  }
  
  /// 從 JSON 建立物件
  factory ConsultationAnalysisResult.fromJson(Map<String, dynamic> json) {
    return ConsultationAnalysisResult(
      themeId: json['themeId'] as String,
      themeTitle: json['themeTitle'] as String,
      themeSubtitle: json['themeSubtitle'] as String,
      themeDescription: json['themeDescription'] as String,
      analysisPoints: List<String>.from(json['analysisPoints'] as List),
      chartInfoText: json['chartInfoText'] as String,
      analysisContent: json['analysisContent'] as String,
      analysisTime: DateTime.parse(json['analysisTime'] as String),
    );
  }
  
  /// 建立副本
  ConsultationAnalysisResult copyWith({
    String? themeId,
    String? themeTitle,
    String? themeSubtitle,
    String? themeDescription,
    List<String>? analysisPoints,
    String? chartInfoText,
    String? analysisContent,
    DateTime? analysisTime,
  }) {
    return ConsultationAnalysisResult(
      themeId: themeId ?? this.themeId,
      themeTitle: themeTitle ?? this.themeTitle,
      themeSubtitle: themeSubtitle ?? this.themeSubtitle,
      themeDescription: themeDescription ?? this.themeDescription,
      analysisPoints: analysisPoints ?? this.analysisPoints,
      chartInfoText: chartInfoText ?? this.chartInfoText,
      analysisContent: analysisContent ?? this.analysisContent,
      analysisTime: analysisTime ?? this.analysisTime,
    );
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is ConsultationAnalysisResult &&
        other.themeId == themeId &&
        other.themeTitle == themeTitle &&
        other.themeSubtitle == themeSubtitle &&
        other.themeDescription == themeDescription &&
        other.chartInfoText == chartInfoText &&
        other.analysisContent == analysisContent &&
        other.analysisTime == analysisTime;
  }
  
  @override
  int get hashCode {
    return themeId.hashCode ^
        themeTitle.hashCode ^
        themeSubtitle.hashCode ^
        themeDescription.hashCode ^
        chartInfoText.hashCode ^
        analysisContent.hashCode ^
        analysisTime.hashCode;
  }
  
  @override
  String toString() {
    return 'ConsultationAnalysisResult('
        'themeId: $themeId, '
        'themeTitle: $themeTitle, '
        'themeSubtitle: $themeSubtitle, '
        'analysisTime: $analysisTime'
        ')';
  }
}
