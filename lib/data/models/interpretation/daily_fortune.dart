/// 今日運勢模型
class DailyFortune {
  /// 日期
  final DateTime date;
  
  /// 整體運勢評分 (1-5)
  final int overallScore;
  
  /// 運勢標題
  final String title;
  
  /// 運勢描述
  final String description;
  
  /// 幸運顏色
  final String luckyColor;
  
  /// 幸運數字
  final List<int> luckyNumbers;
  
  /// 愛情運勢
  final FortuneAspect loveAspect;
  
  /// 事業運勢
  final FortuneAspect careerAspect;
  
  /// 財運
  final FortuneAspect wealthAspect;
  
  /// 健康運勢
  final FortuneAspect healthAspect;
  
  /// 主要星象影響
  final List<String> planetaryInfluences;
  
  /// 建議行動
  final List<String> recommendations;
  
  /// 需要注意的事項
  final List<String> warnings;

  DailyFortune({
    required this.date,
    required this.overallScore,
    required this.title,
    required this.description,
    required this.luckyColor,
    required this.luckyNumbers,
    required this.loveAspect,
    required this.careerAspect,
    required this.wealthAspect,
    required this.healthAspect,
    required this.planetaryInfluences,
    required this.recommendations,
    required this.warnings,
  });

  /// 獲取運勢等級文字
  String get fortuneLevel {
    switch (overallScore) {
      case 5:
        return '極佳';
      case 4:
        return '良好';
      case 3:
        return '普通';
      case 2:
        return '需注意';
      case 1:
        return '謹慎';
      default:
        return '普通';
    }
  }

  /// 獲取運勢顏色
  String get fortuneColor {
    switch (overallScore) {
      case 5:
        return '#4CAF50'; // 綠色
      case 4:
        return '#8BC34A'; // 淺綠色
      case 3:
        return '#FFC107'; // 黃色
      case 2:
        return '#FF9800'; // 橙色
      case 1:
        return '#F44336'; // 紅色
      default:
        return '#9E9E9E'; // 灰色
    }
  }

  /// 獲取運勢圖標
  String get fortuneIcon {
    switch (overallScore) {
      case 5:
        return '🌟';
      case 4:
        return '✨';
      case 3:
        return '⭐';
      case 2:
        return '🌙';
      case 1:
        return '☁️';
      default:
        return '⭐';
    }
  }
}

/// 運勢面向
class FortuneAspect {
  /// 評分 (1-5)
  final int score;
  
  /// 描述
  final String description;
  
  /// 建議
  final String advice;

  FortuneAspect({
    required this.score,
    required this.description,
    required this.advice,
  });

  /// 獲取評分文字
  String get scoreText {
    switch (score) {
      case 5:
        return '極佳';
      case 4:
        return '良好';
      case 3:
        return '普通';
      case 2:
        return '需注意';
      case 1:
        return '謹慎';
      default:
        return '普通';
    }
  }

  /// 獲取評分顏色
  String get scoreColor {
    switch (score) {
      case 5:
        return '#4CAF50';
      case 4:
        return '#8BC34A';
      case 3:
        return '#FFC107';
      case 2:
        return '#FF9800';
      case 1:
        return '#F44336';
      default:
        return '#9E9E9E';
    }
  }
}

/// 星座今日運勢
class ZodiacDailyFortune {
  /// 星座 ID (1-12)
  final int zodiacId;
  
  /// 星座名稱
  final String zodiacName;
  
  /// 星座符號
  final String zodiacSymbol;
  
  /// 今日運勢
  final DailyFortune fortune;

  ZodiacDailyFortune({
    required this.zodiacId,
    required this.zodiacName,
    required this.zodiacSymbol,
    required this.fortune,
  });
}

/// 個人化今日運勢
class PersonalizedDailyFortune {
  /// 基於出生資料的個人運勢
  final DailyFortune personalFortune;
  
  /// 太陽星座運勢
  final ZodiacDailyFortune sunSignFortune;
  
  /// 月亮星座運勢
  final ZodiacDailyFortune? moonSignFortune;
  
  /// 上升星座運勢
  final ZodiacDailyFortune? risingSignFortune;
  
  /// 主要行運影響
  final List<String> transitInfluences;
  
  /// 個人化建議
  final List<String> personalizedAdvice;

  PersonalizedDailyFortune({
    required this.personalFortune,
    required this.sunSignFortune,
    this.moonSignFortune,
    this.risingSignFortune,
    required this.transitInfluences,
    required this.personalizedAdvice,
  });
}
