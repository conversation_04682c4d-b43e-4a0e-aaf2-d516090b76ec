import 'package:flutter/material.dart';

/// 分析類別模型
class AnalysisCategory {
  /// 類別 ID
  final String id;
  
  /// 類別名稱
  final String name;
  
  /// 類別描述
  final String description;
  
  /// 類別圖標
  final IconData icon;
  
  /// 類別顏色
  final Color color;
  
  /// 分析要點
  final List<String> analysisPoints;
  
  /// 相關行星
  final List<String> relatedPlanets;
  
  /// 相關宮位
  final List<int> relatedHouses;
  
  /// 相關相位
  final List<String> relatedAspects;

  const AnalysisCategory({
    required this.id,
    required this.name,
    required this.description,
    required this.icon,
    required this.color,
    required this.analysisPoints,
    required this.relatedPlanets,
    required this.relatedHouses,
    required this.relatedAspects,
  });

  /// 預定義的分析類別
  static const List<AnalysisCategory> categories = [
    AnalysisCategory(
      id: 'personality',
      name: '個性與人格',
      description: '分析您的性格特質、行為模式和內在動機',
      icon: Icons.psychology,
      color: Color(0xFF6366F1), // 靛藍色
      analysisPoints: [
        '基本性格特質',
        '行為模式分析',
        '內在動機探索',
        '潛在能力發掘',
        '性格優勢與挑戰',
      ],
      relatedPlanets: ['太陽', '月亮', '上升'],
      relatedHouses: [1, 12],
      relatedAspects: ['太陽相位', '月亮相位', '上升相位'],
    ),
    AnalysisCategory(
      id: 'love_marriage',
      name: '感情與婚姻',
      description: '探索您的愛情觀、感情模式和婚姻運勢',
      icon: Icons.favorite,
      color: Color(0xFFEC4899), // 粉紅色
      analysisPoints: [
        '愛情觀與感情模式',
        '理想伴侶特質',
        '感情發展趨勢',
        '婚姻運勢分析',
        '感情挑戰與建議',
      ],
      relatedPlanets: ['金星', '火星', '月亮'],
      relatedHouses: [5, 7, 8],
      relatedAspects: ['金星相位', '火星相位', '第七宮相位'],
    ),
    AnalysisCategory(
      id: 'wealth_fortune',
      name: '財富與格局',
      description: '分析您的財運、理財能力和財富格局',
      icon: Icons.account_balance_wallet,
      color: Color(0xFFF59E0B), // 金黃色
      analysisPoints: [
        '財運發展趨勢',
        '理財觀念分析',
        '投資偏好探索',
        '財富累積方式',
        '經濟格局評估',
      ],
      relatedPlanets: ['金星', '木星', '土星'],
      relatedHouses: [2, 8, 11],
      relatedAspects: ['第二宮相位', '第八宮相位', '木星相位'],
    ),
    AnalysisCategory(
      id: 'career_ambition',
      name: '職業與志向',
      description: '探索您的職業天賦、事業發展和人生志向',
      icon: Icons.work,
      color: Color(0xFF059669), // 綠色
      analysisPoints: [
        '職業天賦分析',
        '事業發展方向',
        '工作風格特點',
        '成功要素探索',
        '職涯規劃建議',
      ],
      relatedPlanets: ['太陽', '火星', '土星', '木星'],
      relatedHouses: [6, 10],
      relatedAspects: ['第十宮相位', '火星相位', '土星相位'],
    ),
    AnalysisCategory(
      id: 'social_relationships',
      name: '社交與人際',
      description: '分析您的社交能力、人際關係和交友模式',
      icon: Icons.people,
      color: Color(0xFF8B5CF6), // 紫色
      analysisPoints: [
        '社交風格分析',
        '人際關係模式',
        '交友偏好探索',
        '溝通能力評估',
        '人際挑戰與建議',
      ],
      relatedPlanets: ['水星', '金星', '木星'],
      relatedHouses: [3, 11],
      relatedAspects: ['水星相位', '第三宮相位', '第十一宮相位'],
    ),
    AnalysisCategory(
      id: 'family_relatives',
      name: '六親與家庭',
      description: '探索您與家人的關係、家庭運勢和親情模式',
      icon: Icons.home,
      color: Color(0xFF0EA5E9), // 藍色
      analysisPoints: [
        '家庭關係分析',
        '父母緣分探索',
        '兄弟姊妹關係',
        '子女運勢分析',
        '家庭責任與角色',
      ],
      relatedPlanets: ['月亮', '太陽', '土星'],
      relatedHouses: [3, 4, 5],
      relatedAspects: ['第四宮相位', '月亮相位', '太陽相位'],
    ),
    AnalysisCategory(
      id: 'health_constitution',
      name: '健康與體質',
      description: '分析您的健康狀況、體質特點和養生建議',
      icon: Icons.health_and_safety,
      color: Color(0xFF10B981), // 翠綠色
      analysisPoints: [
        '體質特點分析',
        '健康趨勢預測',
        '易患疾病提醒',
        '養生保健建議',
        '運動健身指導',
      ],
      relatedPlanets: ['太陽', '月亮', '火星', '土星'],
      relatedHouses: [1, 6, 8, 12],
      relatedAspects: ['第六宮相位', '火星相位', '土星相位'],
    ),
  ];

  /// 根據 ID 獲取分析類別
  static AnalysisCategory? getCategoryById(String id) {
    try {
      return categories.firstWhere((category) => category.id == id);
    } catch (e) {
      return null;
    }
  }

  /// 獲取所有分析類別
  static List<AnalysisCategory> getAllCategories() {
    return categories;
  }

  /// 轉換為 JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'analysisPoints': analysisPoints,
      'relatedPlanets': relatedPlanets,
      'relatedHouses': relatedHouses,
      'relatedAspects': relatedAspects,
    };
  }

  /// 從 JSON 創建分析類別
  factory AnalysisCategory.fromJson(Map<String, dynamic> json) {
    // 根據 ID 返回預定義的類別
    return getCategoryById(json['id']) ?? categories.first;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AnalysisCategory && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() => '$name ($id)';
}
