import '../../services/api/ai_api_service.dart';

/// Firebase AI 使用記錄模型
class FirebaseAIUsageRecord {
  final String date; // YYYY-MM-DD 格式
  final String aiProvider; // AI 提供商名稱 (gpt4o, claude, groq, gemini)
  final int totalTokens; // 總 token 使用量
  final int callCount; // 調用次數
  final bool limitReached; // 是否達到每日上限
  final DateTime createdAt; // 創建時間
  final DateTime updatedAt; // 更新時間

  FirebaseAIUsageRecord({
    required this.date,
    required this.aiProvider,
    required this.totalTokens,
    required this.callCount,
    required this.limitReached,
    required this.createdAt,
    required this.updatedAt,
  });

  /// 從 Firestore 文檔創建
  factory FirebaseAIUsageRecord.fromFirestore(Map<String, dynamic> data) {
    return FirebaseAIUsageRecord(
      date: data['date'] as String,
      aiProvider: data['aiProvider'] as String,
      totalTokens: data['totalTokens'] as int? ?? 0,
      callCount: data['callCount'] as int? ?? 0,
      limitReached: data['limitReached'] as bool? ?? false,
      createdAt: _parseDateTime(data['createdAt']),
      updatedAt: _parseDateTime(data['updatedAt']),
    );
  }

  /// 解析 DateTime，支援 Firestore Timestamp 和 ISO 字符串
  static DateTime _parseDateTime(dynamic value) {
    if (value == null) {
      return DateTime.now().toUtc();
    }

    // Firestore SDK Timestamp 類型
    if (value.runtimeType.toString() == 'Timestamp') {
      return (value as dynamic).toDate().toUtc();
    }

    // ISO 字符串格式（REST API）
    if (value is String) {
      try {
        return DateTime.parse(value).toUtc();
      } catch (e) {
        return DateTime.now().toUtc();
      }
    }

    // DateTime 類型
    if (value is DateTime) {
      return value.toUtc();
    }

    return DateTime.now().toUtc();
  }

  /// 轉換為 Firestore 文檔
  Map<String, dynamic> toFirestore() {
    return {
      'date': date,
      'aiProvider': aiProvider,
      'totalTokens': totalTokens,
      'callCount': callCount,
      'limitReached': limitReached,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  /// 創建空的記錄
  factory FirebaseAIUsageRecord.empty({
    required String date,
    required String aiProvider,
  }) {
    final now = DateTime.now().toUtc();
    return FirebaseAIUsageRecord(
      date: date,
      aiProvider: aiProvider,
      totalTokens: 0,
      callCount: 0,
      limitReached: false,
      createdAt: now,
      updatedAt: now,
    );
  }

  /// 添加使用量
  FirebaseAIUsageRecord addUsage(int tokens) {
    final newTotalTokens = totalTokens + tokens;
    final newCallCount = callCount + 1;

    // 檢查是否達到限制（預設 20 萬 tokens）
    const dailyLimit = 200000;
    final newLimitReached = newTotalTokens >= dailyLimit;

    return FirebaseAIUsageRecord(
      date: date,
      aiProvider: aiProvider,
      totalTokens: newTotalTokens,
      callCount: newCallCount,
      limitReached: newLimitReached,
      createdAt: createdAt,
      updatedAt: DateTime.now().toUtc(),
    );
  }

  /// 獲取文檔 ID
  String get documentId => '${date}_$aiProvider';

  /// 獲取剩餘可用 tokens
  int get remainingTokens {
    const dailyLimit = 200000;
    final remaining = dailyLimit - totalTokens;
    return remaining > 0 ? remaining : 0;
  }

  /// 獲取使用百分比
  double get usagePercentage {
    const dailyLimit = 200000;
    return (totalTokens / dailyLimit * 100).clamp(0.0, 100.0);
  }

  @override
  String toString() {
    return 'FirebaseAIUsageRecord(date: $date, provider: $aiProvider, tokens: $totalTokens, calls: $callCount, limitReached: $limitReached)';
  }
}

/// AI 使用統計數據模型
class AIUsageStats {
  final String date; // YYYY-MM-DD 格式
  final Map<AIProvider, int> tokenUsage; // 各提供商的 token 使用量
  final Map<AIProvider, int> requestCount; // 各提供商的請求次數
  final DateTime lastUpdated;

  AIUsageStats({
    required this.date,
    required this.tokenUsage,
    required this.requestCount,
    required this.lastUpdated,
  });

  /// 創建空的統計數據
  factory AIUsageStats.empty(String date) {
    return AIUsageStats(
      date: date,
      tokenUsage: {
        for (final provider in AIProvider.values) provider: 0,
      },
      requestCount: {
        for (final provider in AIProvider.values) provider: 0,
      },
      lastUpdated: DateTime.now().toUtc(),
    );
  }

  /// 從 JSON 創建
  factory AIUsageStats.fromJson(Map<String, dynamic> json) {
    return AIUsageStats(
      date: json['date'] as String,
      tokenUsage: Map<AIProvider, int>.fromEntries(
        (json['tokenUsage'] as Map<String, dynamic>).entries.map(
          (entry) => MapEntry(
            AIProvider.values.firstWhere((p) => p.name == entry.key),
            entry.value as int,
          ),
        ),
      ),
      requestCount: Map<AIProvider, int>.fromEntries(
        (json['requestCount'] as Map<String, dynamic>).entries.map(
          (entry) => MapEntry(
            AIProvider.values.firstWhere((p) => p.name == entry.key),
            entry.value as int,
          ),
        ),
      ),
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
    );
  }

  /// 轉換為 JSON
  Map<String, dynamic> toJson() {
    return {
      'date': date,
      'tokenUsage': Map<String, int>.fromEntries(
        tokenUsage.entries.map(
          (entry) => MapEntry(entry.key.name, entry.value),
        ),
      ),
      'requestCount': Map<String, int>.fromEntries(
        requestCount.entries.map(
          (entry) => MapEntry(entry.key.name, entry.value),
        ),
      ),
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }

  /// 添加使用量
  AIUsageStats addUsage({
    required AIProvider provider,
    required int tokens,
  }) {
    final newTokenUsage = Map<AIProvider, int>.from(tokenUsage);
    final newRequestCount = Map<AIProvider, int>.from(requestCount);
    
    newTokenUsage[provider] = (newTokenUsage[provider] ?? 0) + tokens;
    newRequestCount[provider] = (newRequestCount[provider] ?? 0) + 1;
    
    return AIUsageStats(
      date: date,
      tokenUsage: newTokenUsage,
      requestCount: newRequestCount,
      lastUpdated: DateTime.now().toUtc(),
    );
  }

  /// 獲取總 token 使用量
  int get totalTokens => tokenUsage.values.fold(0, (sum, tokens) => sum + tokens);

  /// 獲取總請求次數
  int get totalRequests => requestCount.values.fold(0, (sum, count) => sum + count);

  /// 獲取特定提供商的 token 使用量
  int getTokenUsage(AIProvider provider) => tokenUsage[provider] ?? 0;

  /// 獲取特定提供商的請求次數
  int getRequestCount(AIProvider provider) => requestCount[provider] ?? 0;

  /// 獲取特定提供商的使用百分比
  double getUsagePercentage(AIProvider provider) {
    if (totalTokens == 0) return 0.0;
    return (getTokenUsage(provider) / totalTokens) * 100;
  }

  /// 獲取今天的日期字符串 (UTC)
  static String getTodayDateString() {
    final now = DateTime.now().toUtc();
    return '${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}';
  }

  /// 檢查是否需要重置（新的一天）
  bool shouldReset() {
    final today = getTodayDateString();
    return date != today;
  }

  @override
  String toString() {
    return 'AIUsageStats(date: $date, totalTokens: $totalTokens, totalRequests: $totalRequests)';
  }
}

/// AI 使用統計項目
class AIUsageItem {
  final AIProvider provider;
  final int tokens;
  final int requests;
  final double percentage;

  AIUsageItem({
    required this.provider,
    required this.tokens,
    required this.requests,
    required this.percentage,
  });

  /// 從統計數據創建項目列表
  static List<AIUsageItem> fromStats(AIUsageStats stats) {
    return AIProvider.values.map((provider) {
      return AIUsageItem(
        provider: provider,
        tokens: stats.getTokenUsage(provider),
        requests: stats.getRequestCount(provider),
        percentage: stats.getUsagePercentage(provider),
      );
    }).where((item) => item.tokens > 0 || item.requests > 0).toList()
      ..sort((a, b) => b.tokens.compareTo(a.tokens)); // 按 token 使用量降序排列
  }
}
