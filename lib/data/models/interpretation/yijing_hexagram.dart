/// 周易卦象模型
class YijingHexagram {
  final int number; // 卦序號 (1-64)
  final String name; // 卦名
  final String symbol; // 卦象符號
  final List<bool> lines; // 六爻，true為陽爻，false為陰爻
  final String upperTrigram; // 上卦
  final String lowerTrigram; // 下卦
  final String judgment; // 卦辭
  final String image; // 象辭
  final List<String> lineTexts; // 爻辭

  const YijingHexagram({
    required this.number,
    required this.name,
    required this.symbol,
    required this.lines,
    required this.upperTrigram,
    required this.lowerTrigram,
    required this.judgment,
    required this.image,
    required this.lineTexts,
  });

  /// 從 JSON 創建卦象
  factory YijingHexagram.fromJson(Map<String, dynamic> json) {
    return YijingHexagram(
      number: json['number'] as int,
      name: json['name'] as String,
      symbol: json['symbol'] as String,
      lines: List<bool>.from(json['lines'] as List),
      upperTrigram: json['upperTrigram'] as String,
      lowerTrigram: json['lowerTrigram'] as String,
      judgment: json['judgment'] as String,
      image: json['image'] as String,
      lineTexts: List<String>.from(json['lineTexts'] as List),
    );
  }

  /// 轉換為 JSON
  Map<String, dynamic> toJson() {
    return {
      'number': number,
      'name': name,
      'symbol': symbol,
      'lines': lines,
      'upperTrigram': upperTrigram,
      'lowerTrigram': lowerTrigram,
      'judgment': judgment,
      'image': image,
      'lineTexts': lineTexts,
    };
  }

  /// 獲取卦象的文字表示
  String get hexagramText {
    return lines.map((line) => line ? '━━━' : '━ ━').join('\n');
  }

  /// 獲取變爻位置（如果有的話）
  List<int> getChangingLines() {
    // 這裡可以根據實際的變爻邏輯來實現
    return [];
  }
}

/// 周易卜卦結果
class YijingDivinationResult {
  final String id;
  final String question;
  final DateTime divinationTime;
  final YijingHexagram primaryHexagram; // 本卦
  final YijingHexagram? changingHexagram; // 變卦（如果有變爻）
  final List<int> changingLines; // 變爻位置
  final String? aiInterpretation; // AI 解讀

  const YijingDivinationResult({
    required this.id,
    required this.question,
    required this.divinationTime,
    required this.primaryHexagram,
    this.changingHexagram,
    this.changingLines = const [],
    this.aiInterpretation,
  });

  /// 從 JSON 創建卜卦結果
  factory YijingDivinationResult.fromJson(Map<String, dynamic> json) {
    return YijingDivinationResult(
      id: json['id'] as String,
      question: json['question'] as String,
      divinationTime: DateTime.parse(json['divinationTime'] as String),
      primaryHexagram: YijingHexagram.fromJson(json['primaryHexagram'] as Map<String, dynamic>),
      changingHexagram: json['changingHexagram'] != null
          ? YijingHexagram.fromJson(json['changingHexagram'] as Map<String, dynamic>)
          : null,
      changingLines: List<int>.from(json['changingLines'] as List? ?? []),
      aiInterpretation: json['aiInterpretation'] as String?,
    );
  }

  /// 轉換為 JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'question': question,
      'divinationTime': divinationTime.toIso8601String(),
      'primaryHexagram': primaryHexagram.toJson(),
      'changingHexagram': changingHexagram?.toJson(),
      'changingLines': changingLines,
      'aiInterpretation': aiInterpretation,
    };
  }

  /// 創建副本
  YijingDivinationResult copyWith({
    String? id,
    String? question,
    DateTime? divinationTime,
    YijingHexagram? primaryHexagram,
    YijingHexagram? changingHexagram,
    List<int>? changingLines,
    String? aiInterpretation,
  }) {
    return YijingDivinationResult(
      id: id ?? this.id,
      question: question ?? this.question,
      divinationTime: divinationTime ?? this.divinationTime,
      primaryHexagram: primaryHexagram ?? this.primaryHexagram,
      changingHexagram: changingHexagram ?? this.changingHexagram,
      changingLines: changingLines ?? this.changingLines,
      aiInterpretation: aiInterpretation ?? this.aiInterpretation,
    );
  }

  /// 是否有變卦
  bool get hasChangingHexagram => changingHexagram != null && changingLines.isNotEmpty;

  /// 獲取簡短描述
  String get shortDescription {
    if (hasChangingHexagram) {
      return '${primaryHexagram.name} 之 ${changingHexagram!.name}';
    } else {
      return primaryHexagram.name;
    }
  }
}
