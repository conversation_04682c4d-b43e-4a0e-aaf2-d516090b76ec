/// 財務分析相關的數據模型

/// 風險承受度枚舉
enum RiskTolerance {
  conservative, // 保守型
  moderate,     // 穩健型
  aggressive,   // 積極型
}

/// 投資類型枚舉
enum InvestmentType {
  savings,      // 儲蓄
  bonds,        // 債券
  stocks,       // 股票
  etf,          // ETF
  crypto,       // 加密貨幣
  forex,        // 外匯
  realEstate,   // 房地產
  commodities,  // 商品
}

/// 財務風格分析
class FinancialStyle {
  final RiskTolerance riskTolerance;
  final String incomeStyle;        // 收入方式
  final String moneyAttraction;    // 金錢吸引力
  final String investmentStyle;    // 投資風格
  final String stabilityLevel;     // 穩定性水平
  final Map<String, double> houseInfluences; // 宮位影響力

  FinancialStyle({
    required this.riskTolerance,
    required this.incomeStyle,
    required this.moneyAttraction,
    required this.investmentStyle,
    required this.stabilityLevel,
    required this.houseInfluences,
  });
}

/// 月度財運預測
class MonthlyForecast {
  final int month;
  final String monthName;
  final bool isPositive;
  final double fortuneScore;       // 財運評分 (0-100)
  final String description;
  final List<String> keyEvents;    // 關鍵事件
  final List<String> planetaryInfluences; // 行星影響

  MonthlyForecast({
    required this.month,
    required this.monthName,
    required this.isPositive,
    required this.fortuneScore,
    required this.description,
    required this.keyEvents,
    required this.planetaryInfluences,
  });
}

/// 年度財運預測
class YearlyFinancialForecast {
  final int year;
  final double overallScore;       // 整體評分
  final String overallTrend;       // 整體趨勢
  final List<MonthlyForecast> monthlyForecasts;
  final List<String> majorTransits; // 主要行運
  final Map<String, String> planetaryInfluences; // 行星影響詳情

  YearlyFinancialForecast({
    required this.year,
    required this.overallScore,
    required this.overallTrend,
    required this.monthlyForecasts,
    required this.majorTransits,
    required this.planetaryInfluences,
  });
}

/// 投資建議
class InvestmentAdvice {
  final InvestmentType type;
  final String name;
  final String description;
  final double suitabilityScore;   // 適合度評分 (0-100)
  final RiskTolerance riskLevel;
  final String reasoning;          // 推薦理由
  final List<String> considerations; // 注意事項

  InvestmentAdvice({
    required this.type,
    required this.name,
    required this.description,
    required this.suitabilityScore,
    required this.riskLevel,
    required this.reasoning,
    required this.considerations,
  });
}

/// 宮位財務分析
class HouseFinancialAnalysis {
  final int houseNumber;
  final String houseName;
  final String description;
  final List<String> planets;     // 落入的行星
  final List<String> aspects;     // 相關相位
  final double influence;         // 影響力 (0-100)
  final String interpretation;    // 解釋

  HouseFinancialAnalysis({
    required this.houseNumber,
    required this.houseName,
    required this.description,
    required this.planets,
    required this.aspects,
    required this.influence,
    required this.interpretation,
  });
}

/// 行星財務影響
class PlanetFinancialInfluence {
  final String planetName;
  final String sign;              // 所在星座
  final int house;                // 所在宮位
  final String influence;         // 影響描述
  final double strength;          // 影響強度 (0-100)
  final List<String> aspects;     // 相關相位

  PlanetFinancialInfluence({
    required this.planetName,
    required this.sign,
    required this.house,
    required this.influence,
    required this.strength,
    required this.aspects,
  });
}

/// 完整的財務分析結果
class FinancialAnalysis {
  final String personName;
  final DateTime birthDate;
  final DateTime analysisDate;
  
  // 核心分析
  final FinancialStyle financialStyle;
  final List<HouseFinancialAnalysis> houseAnalyses;
  final List<PlanetFinancialInfluence> planetInfluences;
  
  // 預測和建議
  final YearlyFinancialForecast yearlyForecast;
  final List<InvestmentAdvice> investmentAdvice;
  
  // 摘要
  final String overallSummary;
  final List<String> keyStrengths;
  final List<String> potentialChallenges;

  FinancialAnalysis({
    required this.personName,
    required this.birthDate,
    required this.analysisDate,
    required this.financialStyle,
    required this.houseAnalyses,
    required this.planetInfluences,
    required this.yearlyForecast,
    required this.investmentAdvice,
    required this.overallSummary,
    required this.keyStrengths,
    required this.potentialChallenges,
  });
}

/// 財運觸發事件
class FinancialTriggerEvent {
  final DateTime date;
  final String eventType;         // 事件類型
  final String description;
  final String planetaryAspect;   // 觸發的行星相位
  final bool isPositive;
  final double impact;            // 影響程度 (0-100)

  FinancialTriggerEvent({
    required this.date,
    required this.eventType,
    required this.description,
    required this.planetaryAspect,
    required this.isPositive,
    required this.impact,
  });
}

/// 資產配置建議
class AssetAllocationAdvice {
  final Map<InvestmentType, double> allocation; // 資產配置比例
  final String strategy;          // 配置策略
  final String reasoning;         // 配置理由
  final List<String> rebalancingTips; // 再平衡建議

  AssetAllocationAdvice({
    required this.allocation,
    required this.strategy,
    required this.reasoning,
    required this.rebalancingTips,
  });
}

/// 財務目標建議
class FinancialGoalAdvice {
  final String goalType;          // 目標類型（短期、中期、長期）
  final String description;
  final double targetAmount;      // 目標金額
  final DateTime targetDate;      // 目標日期
  final String strategy;          // 實現策略
  final List<String> milestones;  // 里程碑

  FinancialGoalAdvice({
    required this.goalType,
    required this.description,
    required this.targetAmount,
    required this.targetDate,
    required this.strategy,
    required this.milestones,
  });
}
