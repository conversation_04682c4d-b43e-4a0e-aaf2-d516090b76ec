/// 星盤主題分析結果的 Data Class
/// 
/// 包含各種主題分析的結果，用於生成主題相關的星盤資訊文本
class ChartThemeAnalysisResult {
  /// 分析主題類型
  final ChartThemeType themeType;
  
  /// 分析標題
  final String title;
  
  /// 分析摘要
  final String summary;
  
  /// 詳細分析內容
  final List<ThemeAnalysisSection> sections;
  
  /// 關鍵要素
  final List<String> keyElements;
  
  /// 建議與指引
  final List<String> recommendations;
  
  /// 分析時間
  final DateTime analysisTime;

  const ChartThemeAnalysisResult({
    required this.themeType,
    required this.title,
    required this.summary,
    required this.sections,
    required this.keyElements,
    required this.recommendations,
    required this.analysisTime,
  });

  /// 從 Map 創建 ChartThemeAnalysisResult
  factory ChartThemeAnalysisResult.fromMap(Map<String, dynamic> map) {
    return ChartThemeAnalysisResult(
      themeType: ChartThemeType.values[map['themeType'] as int],
      title: map['title'] as String,
      summary: map['summary'] as String,
      sections: (map['sections'] as List<dynamic>)
          .map((item) => ThemeAnalysisSection.fromMap(item as Map<String, dynamic>))
          .toList(),
      keyElements: List<String>.from(map['keyElements']),
      recommendations: List<String>.from(map['recommendations']),
      analysisTime: DateTime.parse(map['analysisTime'] as String),
    );
  }

  /// 轉換為 Map
  Map<String, dynamic> toMap() {
    return {
      'themeType': themeType.index,
      'title': title,
      'summary': summary,
      'sections': sections.map((section) => section.toMap()).toList(),
      'keyElements': keyElements,
      'recommendations': recommendations,
      'analysisTime': analysisTime.toIso8601String(),
    };
  }

  @override
  String toString() {
    return 'ChartThemeAnalysisResult('
        'themeType: $themeType, '
        'title: $title, '
        'sections: ${sections.length}, '
        'keyElements: ${keyElements.length}, '
        'recommendations: ${recommendations.length})';
  }
}

/// 主題分析章節的 Data Class
class ThemeAnalysisSection {
  /// 章節標題
  final String title;
  
  /// 章節內容
  final String content;
  
  /// 相關行星位置
  final List<String> relatedPlanets;
  
  /// 相關宮位
  final List<int> relatedHouses;
  
  /// 相關相位
  final List<String> relatedAspects;
  
  /// 重要程度（1-5）
  final int importance;

  const ThemeAnalysisSection({
    required this.title,
    required this.content,
    required this.relatedPlanets,
    required this.relatedHouses,
    required this.relatedAspects,
    required this.importance,
  });

  /// 從 Map 創建 ThemeAnalysisSection
  factory ThemeAnalysisSection.fromMap(Map<String, dynamic> map) {
    return ThemeAnalysisSection(
      title: map['title'] as String,
      content: map['content'] as String,
      relatedPlanets: List<String>.from(map['relatedPlanets']),
      relatedHouses: List<int>.from(map['relatedHouses']),
      relatedAspects: List<String>.from(map['relatedAspects']),
      importance: map['importance'] as int,
    );
  }

  /// 轉換為 Map
  Map<String, dynamic> toMap() {
    return {
      'title': title,
      'content': content,
      'relatedPlanets': relatedPlanets,
      'relatedHouses': relatedHouses,
      'relatedAspects': relatedAspects,
      'importance': importance,
    };
  }

  @override
  String toString() {
    return 'ThemeAnalysisSection('
        'title: $title, '
        'importance: $importance, '
        'relatedPlanets: ${relatedPlanets.length})';
  }
}

/// 星盤主題分析類型枚舉
enum ChartThemeType {
  /// 一、命盤核心架構（性格與天賦）
  coreStructure,
  
  /// 二、情感與親密關係
  emotionalRelationships,
  
  /// 三、職涯方向與人生使命
  careerAndMission,
  
  /// 四、心理療癒與潛意識模式
  psychologicalHealing,
  
  /// 五、流年與推運階段分析
  transitionalAnalysis,
  
  /// 六、決策與問題討論
  decisionMaking,
  
  /// 七、占星諮詢的進階風格差異
  consultationStyles,
}

/// 星盤主題分析類型的擴展方法
extension ChartThemeTypeExtension on ChartThemeType {
  /// 獲取主題名稱
  String get name {
    switch (this) {
      case ChartThemeType.coreStructure:
        return '命盤核心架構（性格與天賦）';
      case ChartThemeType.emotionalRelationships:
        return '情感與親密關係';
      case ChartThemeType.careerAndMission:
        return '職涯方向與人生使命';
      case ChartThemeType.psychologicalHealing:
        return '心理療癒與潛意識模式';
      case ChartThemeType.transitionalAnalysis:
        return '流年與推運階段分析';
      case ChartThemeType.decisionMaking:
        return '決策與問題討論';
      case ChartThemeType.consultationStyles:
        return '占星諮詢的進階風格差異';
    }
  }

  /// 獲取主題描述
  String get description {
    switch (this) {
      case ChartThemeType.coreStructure:
        return '用於建立基本信任感與自我理解';
      case ChartThemeType.emotionalRelationships:
        return '用於協助當事人理解自己的感情模式與挑戰';
      case ChartThemeType.careerAndMission:
        return '協助釐清適合的方向與階段任務';
      case ChartThemeType.psychologicalHealing:
        return '用於深層覺察與創傷處理的輔助';
      case ChartThemeType.transitionalAnalysis:
        return '預測未來動向、理解當下關卡、找出轉變節點';
      case ChartThemeType.decisionMaking:
        return '聚焦某件事要不要做、該怎麼選的推演';
      case ChartThemeType.consultationStyles:
        return '根據風格不同也會有所差異';
    }
  }
}

/// 占星諮詢風格枚舉
enum ConsultationStyle {
  /// 心理占星
  psychological,
  
  /// 古典占星
  classical,
  
  /// 靈性占星
  spiritual,
  
  /// 現代整合式
  modernIntegrated,
}

/// 占星諮詢風格的擴展方法
extension ConsultationStyleExtension on ConsultationStyle {
  /// 獲取風格名稱
  String get name {
    switch (this) {
      case ConsultationStyle.psychological:
        return '心理占星';
      case ConsultationStyle.classical:
        return '古典占星';
      case ConsultationStyle.spiritual:
        return '靈性占星';
      case ConsultationStyle.modernIntegrated:
        return '現代整合式';
    }
  }

  /// 獲取風格強調內容
  String get emphasis {
    switch (this) {
      case ConsultationStyle.psychological:
        return '情緒與潛意識探索、家庭背景模式';
      case ConsultationStyle.classical:
        return '阿拉伯點、法達、時盤、業力傾向';
      case ConsultationStyle.spiritual:
        return '靈魂藍圖、北交點與內在任務';
      case ConsultationStyle.modernIntegrated:
        return '結合心理、關係、流年與療癒取向';
    }
  }
}
