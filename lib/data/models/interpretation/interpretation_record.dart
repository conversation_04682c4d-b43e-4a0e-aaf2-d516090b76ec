import '../astrology/chart_data.dart';

/// AI 解讀紀錄模型
class InterpretationRecord {
  final String id;
  final String title;
  final String interpretationType;
  final String content;
  final DateTime createdAt;
  final String chartTitle;
  final String chartType;
  final String primaryPersonName;
  final String? secondaryPersonName;
  final DateTime? specificDate;
  final List<String>? tags;
  final Map<String, dynamic>? chartData; // 添加完整星盤數據

  const InterpretationRecord({
    required this.id,
    required this.title,
    required this.interpretationType,
    required this.content,
    required this.createdAt,
    required this.chartTitle,
    required this.chartType,
    required this.primaryPersonName,
    this.secondaryPersonName,
    this.specificDate,
    this.tags = const [],
    this.chartData,
  });

  /// 從 ChartData 和解讀信息創建記錄
  factory InterpretationRecord.fromChartData({
    required String id,
    required String title,
    required String interpretationType,
    required String content,
    required ChartData chartData,
    List<String>? tags,
  }) {
    return InterpretationRecord(
      id: id,
      title: title,
      interpretationType: interpretationType,
      content: content,
      createdAt: DateTime.now(),
      chartTitle: chartData.title,
      chartType: chartData.chartType.displayName,
      primaryPersonName: chartData.primaryPerson.name,
      secondaryPersonName: chartData.secondaryPerson?.name,
      specificDate: chartData.specificDate,
      tags: tags,
      chartData: chartData.toJson(), // 保存完整星盤數據
    );
  }

  /// 從 JSON 創建對象
  factory InterpretationRecord.fromJson(Map<String, dynamic> json) {
    return InterpretationRecord(
      id: json['id'] as String,
      title: json['title'] as String,
      interpretationType: json['interpretationType'] as String,
      content: json['content'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      chartTitle: json['chartTitle'] as String,
      chartType: json['chartType'] as String,
      primaryPersonName: json['primaryPersonName'] as String,
      secondaryPersonName: json['secondaryPersonName'] as String?,
      specificDate: json['specificDate'] != null
          ? DateTime.parse(json['specificDate'] as String)
          : null,
      tags: _parseTagsFromJson(json['tags']),
      chartData: json['chartData'] as Map<String, dynamic>?,
    );
  }

  /// 安全地解析 tags 從 JSON
  static List<String> _parseTagsFromJson(dynamic tagsData) {
    if (tagsData == null) {
      return [];
    } else if (tagsData is List) {
      return tagsData.map((tag) => tag.toString()).toList();
    } else if (tagsData is String) {
      // 如果是單個字符串，轉換為列表
      return [tagsData];
    } else {
      // 其他類型，嘗試轉換為字符串
      return [tagsData.toString()];
    }
  }

  /// 轉換為 JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'interpretationType': interpretationType,
      'content': content,
      'createdAt': createdAt.toIso8601String(),
      'chartTitle': chartTitle,
      'chartType': chartType,
      'primaryPersonName': primaryPersonName,
      'secondaryPersonName': secondaryPersonName,
      'specificDate': specificDate?.toIso8601String(),
      'tags': tags,
      'chartData': chartData,
    };
  }

  /// 創建副本
  InterpretationRecord copyWith({
    String? id,
    String? title,
    String? interpretationType,
    String? content,
    DateTime? createdAt,
    String? chartTitle,
    String? chartType,
    String? primaryPersonName,
    String? secondaryPersonName,
    DateTime? specificDate,
    List<String>? tags,
    Map<String, dynamic>? chartData,
  }) {
    return InterpretationRecord(
      id: id ?? this.id,
      title: title ?? this.title,
      interpretationType: interpretationType ?? this.interpretationType,
      content: content ?? this.content,
      createdAt: createdAt ?? this.createdAt,
      chartTitle: chartTitle ?? this.chartTitle,
      chartType: chartType ?? this.chartType,
      primaryPersonName: primaryPersonName ?? this.primaryPersonName,
      secondaryPersonName: secondaryPersonName ?? this.secondaryPersonName,
      specificDate: specificDate ?? this.specificDate,
      tags: tags ?? this.tags,
      chartData: chartData ?? this.chartData,
    );
  }

  /// 獲取格式化的創建時間
  String get formattedCreatedAt {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inDays > 0) {
      return '${difference.inDays} 天前';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} 小時前';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} 分鐘前';
    } else {
      return '剛剛';
    }
  }

  /// 獲取簡短的內容預覽
  String get contentPreview {
    if (content.length <= 100) {
      return content;
    }
    return '${content.substring(0, 100)}...';
  }

  /// 重建 ChartData 對象
  ChartData? rebuildChartData() {
    if (chartData == null) return null;

    try {
      // 從 JSON 重建 ChartData
      return ChartData.fromJson(chartData!);
    } catch (e) {
      // 如果重建失敗，返回 null
      return null;
    }
  }

  /// 獲取解讀類型的圖標
  String get interpretationTypeIcon {
    switch (interpretationType) {
      case 'personality':
      case 'comprehensive':
        return '🧑';
      case 'career_finance':
      case 'career_timing':
        return '💼';
      case 'relationships':
      case 'compatibility':
      case 'love_timing':
        return '💕';
      case 'health':
        return '🏥';
      case 'spiritual':
        return '🧘';
      case 'communication':
        return '💬';
      case 'emotional_bond':
        return '💖';
      case 'future_goals':
        return '🎯';
      case 'current_trends':
        return '📈';
      case 'decision_guidance':
        return '🤔';
      case 'yearly_overview':
        return '📅';
      case 'monthly_emotions':
        return '🌙';
      case 'monthly_opportunities':
        return '⏰';
      case 'life_periods':
        return '📊';
      case 'timing_guidance':
        return '⏱️';
      case 'investment_personality':
        return '🧠';
      default:
        return '✨';
    }
  }

  @override
  String toString() {
    return 'InterpretationRecord(id: $id, title: $title, type: $interpretationType, createdAt: $createdAt)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is InterpretationRecord && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
