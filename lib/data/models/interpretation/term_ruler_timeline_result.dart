import 'term_ruler_progression_result.dart';

/// 界主星配置法時間表結果的 Data Class
///
/// 包含界主星配置法的完整時間表結果，包括當前資訊、
/// 時間表、出生資料等
class TermRulerTimelineResult {
  /// 當前界主星配置法資訊
  final TermRulerProgressionResult currentInfo;

  /// 界主星時間表
  final List<TermRulerTimelineItem> timeline;

  /// 出生日期時間
  final DateTime birthDateTime;

  /// 上升點經度
  final double ascendantLongitude;

  /// 緯度
  final double latitude;

  /// 經度
  final double longitude;

  const TermRulerTimelineResult({
    required this.currentInfo,
    required this.timeline,
    required this.birthDateTime,
    required this.ascendantLongitude,
    required this.latitude,
    required this.longitude,
  });

  /// 從 Map 創建 TermRulerTimelineResult
  factory TermRulerTimelineResult.fromMap(Map<String, dynamic> map) {
    return TermRulerTimelineResult(
      currentInfo: map['currentInfo'],
      timeline: (map['timeline'] as List<dynamic>)
          .map((item) =>
              TermRulerTimelineItem.fromMap(item as Map<String, dynamic>))
          .toList(),
      birthDateTime: map['birthDateTime'] as DateTime,
      ascendantLongitude: (map['ascendantLongitude'] as num).toDouble(),
      latitude: (map['latitude'] as num).toDouble(),
      longitude: (map['longitude'] as num).toDouble(),
    );
  }

  /// 轉換為 Map（用於與現有 UI 組件兼容）
  Map<String, dynamic> toMap() {
    return {
      'currentInfo': currentInfo,
      'timeline': timeline.map((item) => item.toMap()).toList(),
      'birthDateTime': birthDateTime,
      'ascendantLongitude': ascendantLongitude,
      'latitude': latitude,
      'longitude': longitude,
    };
  }

  @override
  String toString() {
    return 'TermRulerTimelineResult('
        'timeline: ${timeline.length} items, '
        'birthDateTime: $birthDateTime, '
        'ascendantLongitude: $ascendantLongitude, '
        'latitude: $latitude, '
        'longitude: $longitude)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is TermRulerTimelineResult &&
        other.currentInfo.toString() == currentInfo.toString() &&
        other.timeline.length == timeline.length &&
        other.birthDateTime == birthDateTime &&
        other.ascendantLongitude == ascendantLongitude &&
        other.latitude == latitude &&
        other.longitude == longitude;
  }

  @override
  int get hashCode {
    return currentInfo.hashCode ^
        timeline.hashCode ^
        birthDateTime.hashCode ^
        ascendantLongitude.hashCode ^
        latitude.hashCode ^
        longitude.hashCode;
  }
}

/// 界主星時間表項目的 Data Class
class TermRulerTimelineItem {
  /// 界主星ID
  final int termRuler;

  /// 界主星名稱
  final String termRulerName;

  /// 每度所需時間（天）
  final double timePerDegree;

  /// 開始度數
  final double startDegree;

  /// 結束度數
  final double endDegree;

  /// 開始日期時間
  final DateTime startDateTime;

  /// 結束日期時間
  final DateTime endDateTime;

  /// 持續天數
  final double durationDays;

  /// 是否為當前界
  final bool isCurrentTerm;

  /// 星座
  final String sign;

  /// 方向
  final String direction;

  /// 經度
  final double longitude;

  /// 星座總時間（可選）
  final double? signTotalTime;

  /// 是否為星座切換
  final bool isSignChange;

  /// 當前圈數（第幾圈）
  final int circleNumber;

  /// 圈內累計度數
  final double degreesInCircle;

  const TermRulerTimelineItem({
    required this.termRuler,
    required this.termRulerName,
    required this.timePerDegree,
    required this.startDegree,
    required this.endDegree,
    required this.startDateTime,
    required this.endDateTime,
    required this.durationDays,
    required this.isCurrentTerm,
    required this.sign,
    required this.direction,
    required this.longitude,
    this.signTotalTime,
    required this.isSignChange,
    this.circleNumber = 1,
    this.degreesInCircle = 0.0,
  });

  /// 從 Map 創建 TermRulerTimelineItem
  factory TermRulerTimelineItem.fromMap(Map<String, dynamic> map) {
    return TermRulerTimelineItem(
      termRuler: map['termRuler'] as int,
      termRulerName: map['termRulerName'] as String,
      timePerDegree: (map['timePerDegree'] as num).toDouble(),
      startDegree: (map['startDegree'] as num).toDouble(),
      endDegree: (map['endDegree'] as num).toDouble(),
      startDateTime: map['startDateTime'] as DateTime,
      endDateTime: map['endDateTime'] as DateTime,
      durationDays: (map['durationDays'] as num).toDouble(),
      isCurrentTerm: map['isCurrentTerm'] as bool,
      sign: map['sign'] as String,
      direction: map['direction'] as String,
      longitude: (map['longitude'] as num).toDouble(),
      signTotalTime: map['signTotalTime'] != null
          ? (map['signTotalTime'] as num).toDouble()
          : null,
      isSignChange: map['isSignChange'] as bool,
      circleNumber: map['circleNumber'] as int? ?? 1,
      degreesInCircle: map['degreesInCircle'] != null
          ? (map['degreesInCircle'] as num).toDouble()
          : 0.0,
    );
  }

  /// 轉換為 Map
  Map<String, dynamic> toMap() {
    return {
      'termRuler': termRuler,
      'termRulerName': termRulerName,
      'startDegree': startDegree,
      'endDegree': endDegree,
      'startDateTime': startDateTime,
      'endDateTime': endDateTime,
      'durationDays': durationDays,
      'isCurrentTerm': isCurrentTerm,
      'sign': sign,
      'direction': direction,
      'longitude': longitude,
      'signTotalTime': signTotalTime,
      'isSignChange': isSignChange,
      'circleNumber': circleNumber,
      'degreesInCircle': degreesInCircle,
    };
  }

  @override
  String toString() {
    return 'TermRulerTimelineItem('
        'termRulerName: $termRulerName, '
        'sign: $sign, '
        'startDegree: $startDegree, '
        'endDegree: $endDegree, '
        'durationDays: $durationDays, '
        'isCurrentTerm: $isCurrentTerm, '
        'circleNumber: $circleNumber, '
        'degreesInCircle: $degreesInCircle)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is TermRulerTimelineItem &&
        other.termRuler == termRuler &&
        other.termRulerName == termRulerName &&
        other.startDegree == startDegree &&
        other.endDegree == endDegree &&
        other.startDateTime == startDateTime &&
        other.endDateTime == endDateTime &&
        other.durationDays == durationDays &&
        other.isCurrentTerm == isCurrentTerm &&
        other.sign == sign &&
        other.direction == direction &&
        other.longitude == longitude &&
        other.signTotalTime == signTotalTime &&
        other.isSignChange == isSignChange &&
        other.circleNumber == circleNumber &&
        other.degreesInCircle == degreesInCircle;
  }

  @override
  int get hashCode {
    return termRuler.hashCode ^
        termRulerName.hashCode ^
        startDegree.hashCode ^
        endDegree.hashCode ^
        startDateTime.hashCode ^
        endDateTime.hashCode ^
        durationDays.hashCode ^
        isCurrentTerm.hashCode ^
        sign.hashCode ^
        direction.hashCode ^
        longitude.hashCode ^
        signTotalTime.hashCode ^
        isSignChange.hashCode ^
        circleNumber.hashCode ^
        degreesInCircle.hashCode;
  }
}
