/// 界主星配置法計算結果的 Data Class
/// 
/// 包含界主星配置法的所有計算結果，包括當前界主星資訊、
/// 下一個界主星資訊、時間計算等
class TermRulerProgressionResult {
  /// 上升星座
  final String ascendantSign;
  
  /// 上升界主星ID
  final int ascendantTermRuler;
  
  /// 上升界主星名稱
  final String ascendantTermRulerName;
  
  /// 每度所需時間（天）
  final double timePerDegree;
  
  /// 走完整個星座所需時間（天）
  final double totalSignTime;
  
  /// 緯度
  final double latitude;
  
  /// 是否為北半球
  final bool isNorth;
  
  /// 緯度索引調試資訊
  final Map<String, dynamic> latitudeDebugInfo;
  
  /// 下一個界主星資訊
  final NextTermInfo nextTermInfo;
  
  /// 當前星座內的度數
  final double currentDegreeInSign;

  const TermRulerProgressionResult({
    required this.ascendantSign,
    required this.ascendantTermRuler,
    required this.ascendantTermRulerName,
    required this.timePerDegree,
    required this.totalSignTime,
    required this.latitude,
    required this.isNorth,
    required this.latitudeDebugInfo,
    required this.nextTermInfo,
    required this.currentDegreeInSign,
  });

  /// 從 Map 創建 TermRulerProgressionResult
  factory TermRulerProgressionResult.fromMap(Map<String, dynamic> map) {
    return TermRulerProgressionResult(
      ascendantSign: map['ascendantSign'] as String,
      ascendantTermRuler: map['ascendantTermRuler'] as int,
      ascendantTermRulerName: map['ascendantTermRulerName'] as String,
      timePerDegree: (map['timePerDegree'] as num).toDouble(),
      totalSignTime: (map['totalSignTime'] as num).toDouble(),
      latitude: (map['latitude'] as num).toDouble(),
      isNorth: map['isNorth'] as bool,
      latitudeDebugInfo: Map<String, dynamic>.from(map['latitudeDebugInfo']),
      nextTermInfo: NextTermInfo.fromMap(map['nextTermInfo'] as Map<String, dynamic>),
      currentDegreeInSign: (map['currentDegreeInSign'] as num).toDouble(),
    );
  }

  /// 轉換為 Map
  Map<String, dynamic> toMap() {
    return {
      'ascendantSign': ascendantSign,
      'ascendantTermRuler': ascendantTermRuler,
      'ascendantTermRulerName': ascendantTermRulerName,
      'timePerDegree': timePerDegree,
      'totalSignTime': totalSignTime,
      'latitude': latitude,
      'isNorth': isNorth,
      'latitudeDebugInfo': latitudeDebugInfo,
      'nextTermInfo': nextTermInfo.toMap(),
      'currentDegreeInSign': currentDegreeInSign,
    };
  }

  @override
  String toString() {
    return 'TermRulerProgressionResult('
        'ascendantSign: $ascendantSign, '
        'ascendantTermRuler: $ascendantTermRuler, '
        'ascendantTermRulerName: $ascendantTermRulerName, '
        'timePerDegree: $timePerDegree, '
        'totalSignTime: $totalSignTime, '
        'latitude: $latitude, '
        'isNorth: $isNorth, '
        'nextTermInfo: $nextTermInfo, '
        'currentDegreeInSign: $currentDegreeInSign)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is TermRulerProgressionResult &&
        other.ascendantSign == ascendantSign &&
        other.ascendantTermRuler == ascendantTermRuler &&
        other.ascendantTermRulerName == ascendantTermRulerName &&
        other.timePerDegree == timePerDegree &&
        other.totalSignTime == totalSignTime &&
        other.latitude == latitude &&
        other.isNorth == isNorth &&
        other.nextTermInfo == nextTermInfo &&
        other.currentDegreeInSign == currentDegreeInSign;
  }

  @override
  int get hashCode {
    return ascendantSign.hashCode ^
        ascendantTermRuler.hashCode ^
        ascendantTermRulerName.hashCode ^
        timePerDegree.hashCode ^
        totalSignTime.hashCode ^
        latitude.hashCode ^
        isNorth.hashCode ^
        nextTermInfo.hashCode ^
        currentDegreeInSign.hashCode;
  }
}

/// 下一個界主星資訊的 Data Class
class NextTermInfo {
  /// 下一個界的起始度數
  final double? nextTermDegree;
  
  /// 下一個界主星ID
  final int? nextTermRuler;
  
  /// 下一個界主星名稱
  final String nextTermRulerName;
  
  /// 到下一個界需要的度數
  final double degreesToNextTerm;
  
  /// 到下一個界需要的時間（天）
  final double timeToNextTermDays;
  
  /// 是否跨越星座
  final bool crossesSign;
  
  /// 目標星座
  final String targetSign;

  const NextTermInfo({
    this.nextTermDegree,
    this.nextTermRuler,
    required this.nextTermRulerName,
    required this.degreesToNextTerm,
    required this.timeToNextTermDays,
    required this.crossesSign,
    required this.targetSign,
  });

  /// 從 Map 創建 NextTermInfo
  factory NextTermInfo.fromMap(Map<String, dynamic> map) {
    return NextTermInfo(
      nextTermDegree: map['nextTermDegree'] as double?,
      nextTermRuler: map['nextTermRuler'] as int?,
      nextTermRulerName: map['nextTermRulerName'] as String,
      degreesToNextTerm: (map['degreesToNextTerm'] as num).toDouble(),
      timeToNextTermDays: (map['timeToNextTermDays'] as num).toDouble(),
      crossesSign: map['crossesSign'] as bool,
      targetSign: map['targetSign'] as String,
    );
  }

  /// 轉換為 Map
  Map<String, dynamic> toMap() {
    return {
      'nextTermDegree': nextTermDegree,
      'nextTermRuler': nextTermRuler,
      'nextTermRulerName': nextTermRulerName,
      'degreesToNextTerm': degreesToNextTerm,
      'timeToNextTermDays': timeToNextTermDays,
      'crossesSign': crossesSign,
      'targetSign': targetSign,
    };
  }

  @override
  String toString() {
    return 'NextTermInfo('
        'nextTermDegree: $nextTermDegree, '
        'nextTermRuler: $nextTermRuler, '
        'nextTermRulerName: $nextTermRulerName, '
        'degreesToNextTerm: $degreesToNextTerm, '
        'timeToNextTermDays: $timeToNextTermDays, '
        'crossesSign: $crossesSign, '
        'targetSign: $targetSign)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is NextTermInfo &&
        other.nextTermDegree == nextTermDegree &&
        other.nextTermRuler == nextTermRuler &&
        other.nextTermRulerName == nextTermRulerName &&
        other.degreesToNextTerm == degreesToNextTerm &&
        other.timeToNextTermDays == timeToNextTermDays &&
        other.crossesSign == crossesSign &&
        other.targetSign == targetSign;
  }

  @override
  int get hashCode {
    return nextTermDegree.hashCode ^
        nextTermRuler.hashCode ^
        nextTermRulerName.hashCode ^
        degreesToNextTerm.hashCode ^
        timeToNextTermDays.hashCode ^
        crossesSign.hashCode ^
        targetSign.hashCode;
  }
}
