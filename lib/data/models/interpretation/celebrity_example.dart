import 'package:flutter/material.dart';

import '../user/birth_data.dart';
/// 名人解讀範例模型
class CelebrityExample {
  /// 名人姓名
  final String name;
  
  /// 出生資料
  final BirthData birthData;
  
  /// 特點描述
  final String description;
  
  /// 類別標籤
  final CelebrityCategory category;
  
  /// 推薦的解讀主題
  final List<String> recommendedTopics;
  
  /// 是否為熱門範例
  final bool isPopular;
  
  /// 圖片 URL（可選）
  final String? imageUrl;

  const CelebrityExample({
    required this.name,
    required this.birthData,
    required this.description,
    required this.category,
    required this.recommendedTopics,
    this.isPopular = false,
    this.imageUrl,
  });

  /// 從 JSON 創建實例
  factory CelebrityExample.fromJson(Map<String, dynamic> json) {
    return CelebrityExample(
      name: json['name'] as String,
      birthData: BirthData.fromJson(json['birthData'] as Map<String, dynamic>),
      description: json['description'] as String,
      category: CelebrityCategory.values.firstWhere(
        (c) => c.name == json['category'],
        orElse: () => CelebrityCategory.other,
      ),
      recommendedTopics: List<String>.from(json['recommendedTopics'] as List),
      isPopular: json['isPopular'] as bool? ?? false,
      imageUrl: json['imageUrl'] as String?,
    );
  }

  /// 轉換為 JSON
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'birthData': birthData.toJson(),
      'description': description,
      'category': category.name,
      'recommendedTopics': recommendedTopics,
      'isPopular': isPopular,
      'imageUrl': imageUrl,
    };
  }
}

/// 名人類別枚舉
enum CelebrityCategory {
  politician('政治人物', Icons.account_balance, Colors.blue),
  entertainment('娛樂明星', Icons.star, Colors.amber),
  scholar('學者專家', Icons.school, Colors.indigo),
  business('商業領袖', Icons.business, Colors.green),
  artist('藝術家', Icons.palette, Colors.purple),
  athlete('運動員', Icons.sports, Colors.orange),
  writer('作家', Icons.edit, Colors.brown),
  scientist('科學家', Icons.science, Colors.teal),
  other('其他', Icons.person, Colors.grey);

  const CelebrityCategory(this.displayName, this.icon, this.color);

  /// 顯示名稱
  final String displayName;
  
  /// 圖示
  final IconData icon;
  
  /// 顏色
  final Color color;

  @override
  String toString() => displayName;
}
