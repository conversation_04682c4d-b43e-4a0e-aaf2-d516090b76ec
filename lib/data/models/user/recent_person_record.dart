import 'birth_data.dart';

/// 最近選中的人物記錄
class RecentPersonRecord {
  final String personId; // 人物ID
  final String name; // 姓名
  final DateTime birthDate; // 出生日期
  final String birthPlace; // 出生地點
  final double latitude; // 緯度
  final double longitude; // 經度
  final DateTime selectedAt; // 選中時間
  final int usageCount; // 使用次數

  RecentPersonRecord({
    required this.personId,
    required this.name,
    required this.birthDate,
    required this.birthPlace,
    required this.latitude,
    required this.longitude,
    required this.selectedAt,
    this.usageCount = 1,
  });

  /// 從JSON創建記錄
  factory RecentPersonRecord.fromJson(Map<String, dynamic> json) {
    return RecentPersonRecord(
      personId: json['personId'],
      name: json['name'],
      birthDate: DateTime.parse(json['birthDate']),
      birthPlace: json['birthPlace'],
      latitude: json['latitude'],
      longitude: json['longitude'],
      selectedAt: DateTime.parse(json['selectedAt']),
      usageCount: json['usageCount'] ?? 1,
    );
  }

  /// 轉換為JSON
  Map<String, dynamic> toJson() {
    return {
      'personId': personId,
      'name': name,
      'birthDate': birthDate.toIso8601String(),
      'birthPlace': birthPlace,
      'latitude': latitude,
      'longitude': longitude,
      'selectedAt': selectedAt.toIso8601String(),
      'usageCount': usageCount,
    };
  }

  /// 創建一個新的記錄，增加使用次數並更新時間
  RecentPersonRecord copyWithIncrementedUsage() {
    return RecentPersonRecord(
      personId: personId,
      name: name,
      birthDate: birthDate,
      birthPlace: birthPlace,
      latitude: latitude,
      longitude: longitude,
      selectedAt: DateTime.now(),
      usageCount: usageCount + 1,
    );
  }

  /// 檢查是否為同一個人（使用多重條件）
  bool isSamePerson(RecentPersonRecord other) {
    // 首先檢查姓名是否相同
    if (name != other.name) return false;

    // 檢查出生日期是否相同
    if (birthDate != other.birthDate) return false;

    // 檢查出生地點是否相同
    if (birthPlace != other.birthPlace) return false;

    // 檢查經緯度是否相近（允許小誤差）
    const double tolerance = 0.001; // 約100米的誤差
    if ((latitude - other.latitude).abs() > tolerance) return false;
    if ((longitude - other.longitude).abs() > tolerance) return false;

    return true;
  }

  /// 檢查是否與BirthData為同一個人
  bool isSamePersonAsBirthData(BirthData birthData) {
    // 首先檢查姓名是否相同
    if (name != birthData.name) return false;

    // 檢查出生日期是否相同
    if (birthDate != birthData.dateTime) return false;

    // 檢查出生地點是否相同
    if (birthPlace != birthData.birthPlace) return false;

    // 檢查經緯度是否相近（允許小誤差）
    const double tolerance = 0.001; // 約100米的誤差
    if ((latitude - birthData.latitude).abs() > tolerance) return false;
    if ((longitude - birthData.longitude).abs() > tolerance) return false;

    return true;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is RecentPersonRecord && isSamePerson(other);
  }

  @override
  int get hashCode => Object.hash(name, birthDate, birthPlace, latitude, longitude);
}
