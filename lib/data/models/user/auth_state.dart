/// 認證狀態枚舉
enum AuthStatus {
  /// 未初始化
  uninitialized,
  
  /// 已認證
  authenticated,
  
  /// 未認證
  unauthenticated,
  
  /// 認證中
  authenticating,
}

/// 認證狀態模型
class AuthState {
  final AuthStatus status;
  final String? user;
  final String? errorMessage;

  const AuthState({
    required this.status,
    this.user,
    this.errorMessage,
  });

  /// 初始狀態
  const AuthState.uninitialized() : this(status: AuthStatus.uninitialized);

  /// 已認證狀態
  const AuthState.authenticated(String user) : this(
    status: AuthStatus.authenticated,
    user: user,
  );

  /// 未認證狀態
  const AuthState.unauthenticated() : this(status: AuthStatus.unauthenticated);

  /// 認證中狀態
  const AuthState.authenticating() : this(status: AuthStatus.authenticating);

  /// 錯誤狀態
  const AuthState.error(String errorMessage) : this(
    status: AuthStatus.unauthenticated,
    errorMessage: errorMessage,
  );

  /// 是否已認證
  bool get isAuthenticated => status == AuthStatus.authenticated;

  /// 是否未認證
  bool get isUnauthenticated => status == AuthStatus.unauthenticated;

  /// 是否認證中
  bool get isAuthenticating => status == AuthStatus.authenticating;

  /// 是否未初始化
  bool get isUninitialized => status == AuthStatus.uninitialized;

  /// 是否有錯誤
  bool get hasError => errorMessage != null;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AuthState &&
        other.status == status &&
        other.user == user &&
        other.errorMessage == errorMessage;
  }

  @override
  int get hashCode => Object.hash(status, user, errorMessage);

  @override
  String toString() {
    return 'AuthState(status: $status, user: $user, errorMessage: $errorMessage)';
  }
}
