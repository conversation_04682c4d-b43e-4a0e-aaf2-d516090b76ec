import 'package:cloud_firestore/cloud_firestore.dart';

import 'birth_data.dart';

/// 用戶出生資料模型
/// 儲存在 user_birth_data 集合中
class UserBirthData {
  final String id; // 文檔 ID
  final String userId; // 用戶 ID
  final BirthData birthData; // 出生資料
  final DateTime createdAt; // 創建時間
  final DateTime updatedAt; // 更新時間
  final bool isSelected; // 是否為當前選中的出生資料

  const UserBirthData({
    required this.id,
    required this.userId,
    required this.birthData,
    required this.createdAt,
    required this.updatedAt,
    this.isSelected = false,
  });

  /// 從 JSON 創建實例
  factory UserBirthData.fromJson(Map<String, dynamic> json) {
    return UserBirthData(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      birthData: BirthData.fromJson(json['birth_data'] as Map<String, dynamic>),
      createdAt: _parseDateTime(json['created_at']),
      updatedAt: _parseDateTime(json['updated_at']),
      isSelected: json['is_selected'] as bool? ?? false,
    );
  }

  /// 轉換為 JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'birth_data': birthData.toJson(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'is_selected': isSelected,
    };
  }

  /// 轉換為 Firestore 格式
  Map<String, dynamic> toFirestoreJson({bool isUpdate = false}) {
    final data = <String, dynamic>{
      'user_id': userId,
      'birth_data': birthData.toJson(),
      'is_selected': isSelected,
    };

    if (isUpdate) {
      data['updated_at'] = FieldValue.serverTimestamp();
    } else {
      data['created_at'] = FieldValue.serverTimestamp();
      data['updated_at'] = FieldValue.serverTimestamp();
    }

    return data;
  }

  /// 創建副本
  UserBirthData copyWith({
    String? id,
    String? userId,
    BirthData? birthData,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isSelected,
  }) {
    return UserBirthData(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      birthData: birthData ?? this.birthData,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isSelected: isSelected ?? this.isSelected,
    );
  }

  /// 解析 DateTime，支援多種格式
  static DateTime _parseDateTime(dynamic value) {
    if (value == null) return DateTime.now();
    
    if (value is Timestamp) {
      return value.toDate();
    }
    
    if (value is String) {
      return DateTime.parse(value);
    }
    
    if (value is DateTime) {
      return value;
    }
    
    return DateTime.now();
  }

  @override
  String toString() {
    return 'UserBirthData(id: $id, userId: $userId, birthData: ${birthData.name}, isSelected: $isSelected)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserBirthData && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
