import 'package:cloud_firestore/cloud_firestore.dart';

/// 用戶分析結果模型
/// 儲存在 user_analysis 集合中
class UserAnalysis {
  final String id; // 文檔 ID
  final String userId; // 用戶 ID
  final String birthDataId; // 對應的出生資料 ID
  final String selfIntroduction; // 自我介紹
  final String strengths; // 性格優點
  final String weaknesses; // 性格缺點
  final String favoriteTopics; // 喜歡話題
  final String socialPreferences; // 人際偏好
  final DateTime createdAt; // 創建時間
  final DateTime updatedAt; // 更新時間
  final String analysisVersion; // 分析版本（用於追蹤不同的分析算法）

  const UserAnalysis({
    required this.id,
    required this.userId,
    required this.birthDataId,
    required this.selfIntroduction,
    required this.strengths,
    required this.weaknesses,
    required this.favoriteTopics,
    required this.socialPreferences,
    required this.createdAt,
    required this.updatedAt,
    this.analysisVersion = '1.0',
  });

  /// 從 JSON 創建實例
  factory UserAnalysis.fromJson(Map<String, dynamic> json) {
    return UserAnalysis(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      birthDataId: json['birth_data_id'] as String,
      selfIntroduction: json['self_introduction'] as String? ?? '',
      strengths: json['strengths'] as String? ?? '',
      weaknesses: json['weaknesses'] as String? ?? '',
      favoriteTopics: json['favorite_topics'] as String? ?? '',
      socialPreferences: json['social_preferences'] as String? ?? '',
      createdAt: _parseDateTime(json['created_at']),
      updatedAt: _parseDateTime(json['updated_at']),
      analysisVersion: json['analysis_version'] as String? ?? '1.0',
    );
  }

  /// 轉換為 JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'birth_data_id': birthDataId,
      'self_introduction': selfIntroduction,
      'strengths': strengths,
      'weaknesses': weaknesses,
      'favorite_topics': favoriteTopics,
      'social_preferences': socialPreferences,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'analysis_version': analysisVersion,
    };
  }

  /// 轉換為 Firestore 格式
  Map<String, dynamic> toFirestoreJson({bool isUpdate = false}) {
    final data = <String, dynamic>{
      'user_id': userId,
      'birth_data_id': birthDataId,
      'self_introduction': selfIntroduction,
      'strengths': strengths,
      'weaknesses': weaknesses,
      'favorite_topics': favoriteTopics,
      'social_preferences': socialPreferences,
      'analysis_version': analysisVersion,
    };

    if (isUpdate) {
      data['updated_at'] = FieldValue.serverTimestamp();
    } else {
      data['created_at'] = FieldValue.serverTimestamp();
      data['updated_at'] = FieldValue.serverTimestamp();
    }

    return data;
  }

  /// 創建副本
  UserAnalysis copyWith({
    String? id,
    String? userId,
    String? birthDataId,
    String? selfIntroduction,
    String? strengths,
    String? weaknesses,
    String? favoriteTopics,
    String? socialPreferences,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? analysisVersion,
  }) {
    return UserAnalysis(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      birthDataId: birthDataId ?? this.birthDataId,
      selfIntroduction: selfIntroduction ?? this.selfIntroduction,
      strengths: strengths ?? this.strengths,
      weaknesses: weaknesses ?? this.weaknesses,
      favoriteTopics: favoriteTopics ?? this.favoriteTopics,
      socialPreferences: socialPreferences ?? this.socialPreferences,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      analysisVersion: analysisVersion ?? this.analysisVersion,
    );
  }

  /// 轉換為 Map 格式（用於分析結果顯示）
  Map<String, dynamic> toAnalysisMap() {
    return {
      'selfIntroduction': selfIntroduction,
      'strengths': strengths,
      'weaknesses': weaknesses,
      'favoriteTopics': favoriteTopics,
      'socialPreferences': socialPreferences,
    };
  }

  /// 解析 DateTime，支援多種格式
  static DateTime _parseDateTime(dynamic value) {
    if (value == null) return DateTime.now();
    
    if (value is Timestamp) {
      return value.toDate();
    }
    
    if (value is String) {
      return DateTime.parse(value);
    }
    
    if (value is DateTime) {
      return value;
    }
    
    return DateTime.now();
  }

  @override
  String toString() {
    return 'UserAnalysis(id: $id, userId: $userId, birthDataId: $birthDataId, version: $analysisVersion)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserAnalysis && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
