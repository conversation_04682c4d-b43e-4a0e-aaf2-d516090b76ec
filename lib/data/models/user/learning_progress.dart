/// 學習進度模型
class LearningProgress {
  final String userId;
  final Map<String, bool> completedLessons;
  final Map<String, DateTime> lessonCompletionDates;
  final int totalScore;
  final String currentLevel;
  final List<String> unlockedFeatures;
  final DateTime lastUpdated;

  LearningProgress({
    required this.userId,
    required this.completedLessons,
    required this.lessonCompletionDates,
    required this.totalScore,
    required this.currentLevel,
    required this.unlockedFeatures,
    required this.lastUpdated,
  });

  factory LearningProgress.initial(String userId) {
    return LearningProgress(
      userId: userId,
      completedLessons: {},
      lessonCompletionDates: {},
      totalScore: 0,
      currentLevel: '初學者',
      unlockedFeatures: ['basic_zodiac'],
      lastUpdated: DateTime.now(),
    );
  }

  factory LearningProgress.fromJson(Map<String, dynamic> json) {
    return LearningProgress(
      userId: json['userId'] ?? '',
      completedLessons: Map<String, bool>.from(json['completedLessons'] ?? {}),
      lessonCompletionDates: (json['lessonCompletionDates'] as Map<String, dynamic>?)
          ?.map((key, value) => MapEntry(key, DateTime.parse(value))) ?? {},
      totalScore: json['totalScore'] ?? 0,
      currentLevel: json['currentLevel'] ?? '初學者',
      unlockedFeatures: List<String>.from(json['unlockedFeatures'] ?? ['basic_zodiac']),
      lastUpdated: DateTime.parse(json['lastUpdated'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'completedLessons': completedLessons,
      'lessonCompletionDates': lessonCompletionDates.map((key, value) => MapEntry(key, value.toIso8601String())),
      'totalScore': totalScore,
      'currentLevel': currentLevel,
      'unlockedFeatures': unlockedFeatures,
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }

  /// 計算學習進度百分比
  double get progressPercentage {
    const totalLessons = 20; // 假設總共有20個課程
    final completedCount = completedLessons.values.where((completed) => completed).length;
    return (completedCount / totalLessons * 100).clamp(0.0, 100.0);
  }

  /// 獲取下一個建議課程
  String? get nextRecommendedLesson {
    const lessonOrder = [
      'basic_zodiac_signs',
      'zodiac_elements',
      'zodiac_qualities',
      'planet_basics',
      'sun_moon_rising',
      'houses_introduction',
      'aspects_basics',
      'chart_reading_basics',
    ];

    for (final lesson in lessonOrder) {
      if (!(completedLessons[lesson] ?? false)) {
        return lesson;
      }
    }
    return null;
  }

  /// 檢查是否解鎖某個功能
  bool isFeatureUnlocked(String feature) {
    return unlockedFeatures.contains(feature);
  }

  /// 完成課程
  LearningProgress completeLesson(String lessonId, int points) {
    final newCompletedLessons = Map<String, bool>.from(completedLessons);
    final newCompletionDates = Map<String, DateTime>.from(lessonCompletionDates);
    final newUnlockedFeatures = List<String>.from(unlockedFeatures);
    
    newCompletedLessons[lessonId] = true;
    newCompletionDates[lessonId] = DateTime.now();
    
    final newScore = totalScore + points;
    String newLevel = currentLevel;
    
    // 根據分數更新等級
    if (newScore >= 500) {
      newLevel = '占星專家';
      if (!newUnlockedFeatures.contains('advanced_features')) {
        newUnlockedFeatures.add('advanced_features');
      }
    } else if (newScore >= 200) {
      newLevel = '進階學習者';
      if (!newUnlockedFeatures.contains('intermediate_features')) {
        newUnlockedFeatures.add('intermediate_features');
      }
    } else if (newScore >= 50) {
      newLevel = '入門學習者';
      if (!newUnlockedFeatures.contains('basic_features')) {
        newUnlockedFeatures.add('basic_features');
      }
    }

    return LearningProgress(
      userId: userId,
      completedLessons: newCompletedLessons,
      lessonCompletionDates: newCompletionDates,
      totalScore: newScore,
      currentLevel: newLevel,
      unlockedFeatures: newUnlockedFeatures,
      lastUpdated: DateTime.now(),
    );
  }
}
