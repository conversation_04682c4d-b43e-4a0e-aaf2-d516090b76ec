import 'package:flutter/material.dart';

/// 性別枚舉
enum Gender {
  male('男性', Icons.male, Colors.blue),
  female('女性', Icons.female, Colors.pink);

  const Gender(this.displayName, this.icon, this.color);

  final String displayName;
  final IconData icon;
  final Color color;

  /// 從字符串值獲取枚舉
  static Gender fromString(String value) {
    final normalizedValue = value.toLowerCase().trim();

    // 支援中文
    switch (value) {
      case '男性':
        return Gender.male;
      case '女性':
        return Gender.female;
    }

    // 支援英文和其他格式
    switch (normalizedValue) {
      case 'male':
      case 'm':
      case '男':
        return Gender.male;
      case 'female':
      case 'f':
      case '女':
        return Gender.female;
    }
    return Gender.female;
  }

  /// 轉換為存儲字符串
  String toStorageString() {
    return displayName;
  }

  /// 獲取所有性別選項
  static List<Gender> get allGenders => Gender.values;
}
