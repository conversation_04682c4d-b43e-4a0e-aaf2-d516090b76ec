import 'package:intl/intl.dart';

/// 意見回饋模型
class FeedbackModel {
  final String id;
  final String content;
  final String? email;
  final String deviceId;
  final DateTime createdAt;
  final String? category;
  final bool isResolved;

  FeedbackModel({
    required this.id,
    required this.content,
    this.email,
    required this.deviceId,
    DateTime? createdAt,
    this.category,
    this.isResolved = false,
  }) : createdAt = createdAt ?? DateTime.now();

  /// 從 JSON 創建 FeedbackModel
  factory FeedbackModel.fromJson(Map<String, dynamic> json) {
    return FeedbackModel(
      id: json['id'] as String,
      content: json['content'] as String,
      email: json['email'] as String?,
      deviceId: json['deviceId'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      category: json['category'] as String?,
      isResolved: json['isResolved'] as bool? ?? false,
    );
  }

  /// 轉換為 JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'content': content,
      'email': email,
      'deviceId': deviceId,
      'createdAt': createdAt.toIso8601String(),
      'category': category,
      'isResolved': isResolved,
    };
  }

  /// 格式化創建時間
  String get formattedCreatedAt {
    return DateFormat('yyyy-MM-dd HH:mm').format(createdAt);
  }
}
