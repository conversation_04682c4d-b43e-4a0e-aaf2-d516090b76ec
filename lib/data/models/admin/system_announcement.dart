import 'package:flutter/material.dart';

/// 系統公告類型
enum AnnouncementType {
  info,
  warning,
  maintenance,
  feature,
  promotion;

  String get displayName {
    switch (this) {
      case AnnouncementType.info:
        return '一般資訊';
      case AnnouncementType.warning:
        return '重要警告';
      case AnnouncementType.maintenance:
        return '維護通知';
      case AnnouncementType.feature:
        return '功能更新';
      case AnnouncementType.promotion:
        return '活動推廣';
    }
  }

  Color get color {
    switch (this) {
      case AnnouncementType.info:
        return Colors.blue;
      case AnnouncementType.warning:
        return Colors.orange;
      case AnnouncementType.maintenance:
        return Colors.red;
      case AnnouncementType.feature:
        return Colors.green;
      case AnnouncementType.promotion:
        return Colors.purple;
    }
  }

  IconData get icon {
    switch (this) {
      case AnnouncementType.info:
        return Icons.info_outline;
      case AnnouncementType.warning:
        return Icons.warning_outlined;
      case AnnouncementType.maintenance:
        return Icons.build_outlined;
      case AnnouncementType.feature:
        return Icons.new_releases_outlined;
      case AnnouncementType.promotion:
        return Icons.campaign_outlined;
    }
  }
}

/// 系統公告優先級
enum AnnouncementPriority {
  low,
  normal,
  high,
  urgent;

  String get displayName {
    switch (this) {
      case AnnouncementPriority.low:
        return '低';
      case AnnouncementPriority.normal:
        return '一般';
      case AnnouncementPriority.high:
        return '高';
      case AnnouncementPriority.urgent:
        return '緊急';
    }
  }

  Color get color {
    switch (this) {
      case AnnouncementPriority.low:
        return Colors.grey;
      case AnnouncementPriority.normal:
        return Colors.blue;
      case AnnouncementPriority.high:
        return Colors.orange;
      case AnnouncementPriority.urgent:
        return Colors.red;
    }
  }
}

/// 系統公告模型
class SystemAnnouncement {
  final String id;
  final String title;
  final String content;
  final AnnouncementType type;
  final AnnouncementPriority priority;
  final bool isActive;
  final bool isSticky; // 是否置頂
  final DateTime? startDate; // 開始顯示時間
  final DateTime? endDate; // 結束顯示時間
  final List<String> targetUserTypes; // 目標用戶類型 ['all', 'starmaster', 'starlight', 'admin']
  final String? imageUrl; // 圖片 URL
  final String? actionUrl; // 點擊後的跳轉 URL
  final String? actionText; // 行動按鈕文字
  final Map<String, dynamic>? metadata; // 額外資料
  final DateTime createdAt;
  final DateTime updatedAt;
  final String createdBy;
  final String updatedBy;

  const SystemAnnouncement({
    required this.id,
    required this.title,
    required this.content,
    required this.type,
    required this.priority,
    required this.isActive,
    required this.isSticky,
    this.startDate,
    this.endDate,
    required this.targetUserTypes,
    this.imageUrl,
    this.actionUrl,
    this.actionText,
    this.metadata,
    required this.createdAt,
    required this.updatedAt,
    required this.createdBy,
    required this.updatedBy,
  });

  /// 從 JSON 創建實例
  factory SystemAnnouncement.fromJson(Map<String, dynamic> json) {
    return SystemAnnouncement(
      id: json['id'] as String,
      title: json['title'] as String,
      content: json['content'] as String,
      type: AnnouncementType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => AnnouncementType.info,
      ),
      priority: AnnouncementPriority.values.firstWhere(
        (e) => e.name == json['priority'],
        orElse: () => AnnouncementPriority.normal,
      ),
      isActive: json['isActive'] as bool? ?? true,
      isSticky: json['isSticky'] as bool? ?? false,
      startDate: json['startDate'] != null 
          ? DateTime.parse(json['startDate'] as String)
          : null,
      endDate: json['endDate'] != null 
          ? DateTime.parse(json['endDate'] as String)
          : null,
      targetUserTypes: List<String>.from(json['targetUserTypes'] as List? ?? ['all']),
      imageUrl: json['imageUrl'] as String?,
      actionUrl: json['actionUrl'] as String?,
      actionText: json['actionText'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      createdBy: json['createdBy'] as String,
      updatedBy: json['updatedBy'] as String,
    );
  }

  /// 轉換為 JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'content': content,
      'type': type.name,
      'priority': priority.name,
      'isActive': isActive,
      'isSticky': isSticky,
      'startDate': startDate?.toIso8601String(),
      'endDate': endDate?.toIso8601String(),
      'targetUserTypes': targetUserTypes,
      'imageUrl': imageUrl,
      'actionUrl': actionUrl,
      'actionText': actionText,
      'metadata': metadata,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'createdBy': createdBy,
      'updatedBy': updatedBy,
    };
  }

  /// 轉換為 Firestore 格式
  Map<String, dynamic> toFirestore() {
    final data = toJson();
    // 移除 id，因為 Firestore 會自動生成
    data.remove('id');
    return data;
  }

  /// 複製並更新部分屬性
  SystemAnnouncement copyWith({
    String? id,
    String? title,
    String? content,
    AnnouncementType? type,
    AnnouncementPriority? priority,
    bool? isActive,
    bool? isSticky,
    DateTime? startDate,
    DateTime? endDate,
    List<String>? targetUserTypes,
    String? imageUrl,
    String? actionUrl,
    String? actionText,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdBy,
    String? updatedBy,
  }) {
    return SystemAnnouncement(
      id: id ?? this.id,
      title: title ?? this.title,
      content: content ?? this.content,
      type: type ?? this.type,
      priority: priority ?? this.priority,
      isActive: isActive ?? this.isActive,
      isSticky: isSticky ?? this.isSticky,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      targetUserTypes: targetUserTypes ?? this.targetUserTypes,
      imageUrl: imageUrl ?? this.imageUrl,
      actionUrl: actionUrl ?? this.actionUrl,
      actionText: actionText ?? this.actionText,
      metadata: metadata ?? this.metadata,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      createdBy: createdBy ?? this.createdBy,
      updatedBy: updatedBy ?? this.updatedBy,
    );
  }

  /// 檢查公告是否應該顯示
  bool shouldDisplay({String userType = 'all'}) {
    // 檢查是否啟用
    if (!isActive) return false;

    // 檢查時間範圍
    final now = DateTime.now();
    if (startDate != null && now.isBefore(startDate!)) return false;
    if (endDate != null && now.isAfter(endDate!)) return false;

    // 檢查目標用戶類型
    if (!targetUserTypes.contains('all') && !targetUserTypes.contains(userType)) {
      return false;
    }

    return true;
  }

  @override
  String toString() {
    return 'SystemAnnouncement(id: $id, title: $title, type: $type, priority: $priority, isActive: $isActive)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SystemAnnouncement && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// 系統公告包含 ID 的模型（用於列表顯示）
class SystemAnnouncementWithId {
  final String id;
  final SystemAnnouncement announcement;

  const SystemAnnouncementWithId({
    required this.id,
    required this.announcement,
  });
}
