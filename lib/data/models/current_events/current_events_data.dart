import 'package:flutter/material.dart';

/// 時事分析相關的資料模型
/// 包含卜卦分析、二分二至盤、日月蝕盤等時事占星分析功能

/// 時事分析類型枚舉
enum CurrentEventsType {
  horary,        // 卜卦分析
  solstice,      // 二分二至盤
  eclipse,       // 日月蝕盤
}

/// 時事分析資料模型
class CurrentEventsData {
  final String id;
  final CurrentEventsType type;
  final String title;
  final String description;
  final String beginnerDescription; // 初心者模式的描述
  final DateTime eventDate;
  final String? question; // 卜卦分析的問題
  final Map<String, dynamic>? chartData; // 星盤資料
  final bool isActive; // 是否為當前活躍的事件
  final String iconPath;
  final List<String> keywords;

  const CurrentEventsData({
    required this.id,
    required this.type,
    required this.title,
    required this.description,
    required this.beginnerDescription,
    required this.eventDate,
    this.question,
    this.chartData,
    this.isActive = false,
    required this.iconPath,
    required this.keywords,
  });

  /// 從 JSON 創建實例
  factory CurrentEventsData.fromJson(Map<String, dynamic> json) {
    return CurrentEventsData(
      id: json['id'] as String,
      type: CurrentEventsType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => CurrentEventsType.horary,
      ),
      title: json['title'] as String,
      description: json['description'] as String,
      beginnerDescription: json['beginnerDescription'] as String,
      eventDate: DateTime.parse(json['eventDate'] as String),
      question: json['question'] as String?,
      chartData: json['chartData'] as Map<String, dynamic>?,
      isActive: json['isActive'] as bool? ?? false,
      iconPath: json['iconPath'] as String,
      keywords: List<String>.from(json['keywords'] as List),
    );
  }

  /// 轉換為 JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'title': title,
      'description': description,
      'beginnerDescription': beginnerDescription,
      'eventDate': eventDate.toIso8601String(),
      'question': question,
      'chartData': chartData,
      'isActive': isActive,
      'iconPath': iconPath,
      'keywords': keywords,
    };
  }

  /// 獲取類型的中文名稱
  String get typeDisplayName {
    switch (type) {
      case CurrentEventsType.horary:
        return '卜卦分析';
      case CurrentEventsType.solstice:
        return '二分二至盤';
      case CurrentEventsType.eclipse:
        return '日月蝕盤';
    }
  }

  /// 獲取初心者友善的類型描述
  String get beginnerTypeDescription {
    switch (type) {
      case CurrentEventsType.horary:
        return '問事占星 - 針對特定問題尋求占星指引';
      case CurrentEventsType.solstice:
        return '季節轉換 - 春分、夏至、秋分、冬至的能量分析';
      case CurrentEventsType.eclipse:
        return '日月蝕 - 重要的宇宙能量轉換時刻';
    }
  }

  /// 獲取適合的顏色
  Color get themeColor {
    switch (type) {
      case CurrentEventsType.horary:
        return const Color(0xFF9C27B0); // 紫色
      case CurrentEventsType.solstice:
        return const Color(0xFF4CAF50); // 綠色
      case CurrentEventsType.eclipse:
        return const Color(0xFFFF5722); // 橘紅色
    }
  }

  /// 複製並修改屬性
  CurrentEventsData copyWith({
    String? id,
    CurrentEventsType? type,
    String? title,
    String? description,
    String? beginnerDescription,
    DateTime? eventDate,
    String? question,
    Map<String, dynamic>? chartData,
    bool? isActive,
    String? iconPath,
    List<String>? keywords,
  }) {
    return CurrentEventsData(
      id: id ?? this.id,
      type: type ?? this.type,
      title: title ?? this.title,
      description: description ?? this.description,
      beginnerDescription: beginnerDescription ?? this.beginnerDescription,
      eventDate: eventDate ?? this.eventDate,
      question: question ?? this.question,
      chartData: chartData ?? this.chartData,
      isActive: isActive ?? this.isActive,
      iconPath: iconPath ?? this.iconPath,
      keywords: keywords ?? this.keywords,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CurrentEventsData && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'CurrentEventsData(id: $id, type: $type, title: $title)';
  }
}

/// 時事分析配置
class CurrentEventsConfig {
  final List<CurrentEventsData> events;
  final DateTime lastUpdated;

  const CurrentEventsConfig({
    required this.events,
    required this.lastUpdated,
  });

  /// 從 JSON 創建實例
  factory CurrentEventsConfig.fromJson(Map<String, dynamic> json) {
    return CurrentEventsConfig(
      events: (json['events'] as List)
          .map((event) => CurrentEventsData.fromJson(event as Map<String, dynamic>))
          .toList(),
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
    );
  }

  /// 轉換為 JSON
  Map<String, dynamic> toJson() {
    return {
      'events': events.map((event) => event.toJson()).toList(),
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }

  /// 獲取活躍的事件
  List<CurrentEventsData> get activeEvents {
    return events.where((event) => event.isActive).toList();
  }

  /// 根據類型獲取事件
  List<CurrentEventsData> getEventsByType(CurrentEventsType type) {
    return events.where((event) => event.type == type).toList();
  }

  /// 獲取最近的事件
  List<CurrentEventsData> getRecentEvents({int limit = 5}) {
    final sortedEvents = List<CurrentEventsData>.from(events);
    sortedEvents.sort((a, b) => b.eventDate.compareTo(a.eventDate));
    return sortedEvents.take(limit).toList();
  }

  /// 搜尋事件
  List<CurrentEventsData> searchEvents(String query) {
    final lowerQuery = query.toLowerCase();
    return events.where((event) {
      return event.title.toLowerCase().contains(lowerQuery) ||
             event.description.toLowerCase().contains(lowerQuery) ||
             event.keywords.any((keyword) => keyword.toLowerCase().contains(lowerQuery));
    }).toList();
  }

  @override
  String toString() {
    return 'CurrentEventsConfig(events: ${events.length}, lastUpdated: $lastUpdated)';
  }
}
