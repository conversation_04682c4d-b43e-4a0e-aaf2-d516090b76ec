/// 支付記錄模型
class PaymentRecord {
  final String id;
  final String planType; // 'monthly', 'quarterly', 'yearly'
  final double amount;
  final String currency;
  final DateTime paymentDate;
  final DateTime expiryDate;
  final bool isValid;
  final String paymentMethod; // 'credit_card', 'paypal', 'bank_transfer', etc.
  final String? transactionId;
  final String? notes;

  PaymentRecord({
    required this.id,
    required this.planType,
    required this.amount,
    required this.currency,
    required this.paymentDate,
    required this.expiryDate,
    required this.isValid,
    required this.paymentMethod,
    this.transactionId,
    this.notes,
  });

  /// 從 JSON 創建 PaymentRecord
  factory PaymentRecord.fromJson(Map<String, dynamic> json) {
    return PaymentRecord(
      id: json['id'] as String,
      planType: json['planType'] as String,
      amount: (json['amount'] as num).toDouble(),
      currency: json['currency'] as String,
      paymentDate: DateTime.parse(json['paymentDate'] as String),
      expiryDate: DateTime.parse(json['expiryDate'] as String),
      isValid: json['isValid'] as bool,
      paymentMethod: json['paymentMethod'] as String,
      transactionId: json['transactionId'] as String?,
      notes: json['notes'] as String?,
    );
  }

  /// 轉換為 JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'planType': planType,
      'amount': amount,
      'currency': currency,
      'paymentDate': paymentDate.toIso8601String(),
      'expiryDate': expiryDate.toIso8601String(),
      'isValid': isValid,
      'paymentMethod': paymentMethod,
      'transactionId': transactionId,
      'notes': notes,
    };
  }

  /// 創建更新後的支付記錄
  PaymentRecord copyWith({
    String? id,
    String? planType,
    double? amount,
    String? currency,
    DateTime? paymentDate,
    DateTime? expiryDate,
    bool? isValid,
    String? paymentMethod,
    String? transactionId,
    String? notes,
  }) {
    return PaymentRecord(
      id: id ?? this.id,
      planType: planType ?? this.planType,
      amount: amount ?? this.amount,
      currency: currency ?? this.currency,
      paymentDate: paymentDate ?? this.paymentDate,
      expiryDate: expiryDate ?? this.expiryDate,
      isValid: isValid ?? this.isValid,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      transactionId: transactionId ?? this.transactionId,
      notes: notes ?? this.notes,
    );
  }

  /// 檢查支付是否仍然有效
  bool get isCurrentlyValid {
    return isValid && expiryDate.isAfter(DateTime.now());
  }

  /// 獲取剩餘天數
  int get remainingDays {
    if (!isCurrentlyValid) return 0;
    return expiryDate.difference(DateTime.now()).inDays;
  }

  /// 獲取計劃顯示名稱
  String get planDisplayName {
    switch (planType) {
      case 'monthly':
        return '月度方案';
      case 'quarterly':
        return '季度方案';
      case 'yearly':
        return '年度方案';
      default:
        return planType;
    }
  }

  /// 獲取支付方式顯示名稱
  String get paymentMethodDisplayName {
    switch (paymentMethod) {
      case 'credit_card':
        return '信用卡';
      case 'paypal':
        return 'PayPal';
      case 'bank_transfer':
        return '銀行轉帳';
      case 'simulation':
        return '模擬支付';
      default:
        return paymentMethod;
    }
  }

  @override
  String toString() {
    return 'PaymentRecord(id: $id, planType: $planType, amount: $amount, '
           'currency: $currency, paymentDate: $paymentDate, '
           'expiryDate: $expiryDate, isValid: $isValid, '
           'paymentMethod: $paymentMethod, transactionId: $transactionId)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PaymentRecord &&
           other.id == id &&
           other.planType == planType &&
           other.amount == amount &&
           other.currency == currency &&
           other.paymentDate == paymentDate &&
           other.expiryDate == expiryDate &&
           other.isValid == isValid &&
           other.paymentMethod == paymentMethod &&
           other.transactionId == transactionId &&
           other.notes == notes;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      planType,
      amount,
      currency,
      paymentDate,
      expiryDate,
      isValid,
      paymentMethod,
      transactionId,
      notes,
    );
  }
}

/// 支付計劃類型
class PaymentPlanType {
  static const String monthly = 'monthly';
  static const String quarterly = 'quarterly';
  static const String yearly = 'yearly';

  static List<String> get all => [monthly, quarterly, yearly];

  static String getDisplayName(String planType) {
    switch (planType) {
      case monthly:
        return '月度方案';
      case quarterly:
        return '季度方案';
      case yearly:
        return '年度方案';
      default:
        return planType;
    }
  }
}

/// 支付方式類型
class PaymentMethodType {
  static const String creditCard = 'credit_card';
  static const String paypal = 'paypal';
  static const String bankTransfer = 'bank_transfer';
  static const String simulation = 'simulation';

  static List<String> get all => [creditCard, paypal, bankTransfer];

  static String getDisplayName(String paymentMethod) {
    switch (paymentMethod) {
      case creditCard:
        return '信用卡';
      case paypal:
        return 'PayPal';
      case bankTransfer:
        return '銀行轉帳';
      case simulation:
        return '模擬支付';
      default:
        return paymentMethod;
    }
  }
}
