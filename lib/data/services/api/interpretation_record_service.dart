import 'dart:convert';

import 'package:shared_preferences/shared_preferences.dart';

import '../../../core/utils/logger_utils.dart';
import '../../models/interpretation/interpretation_record.dart';

/// 解讀紀錄服務
class InterpretationRecordService {
  static const String _recordsKey = 'interpretation_records';
  static const int _maxRecords = 100; // 最多保存 100 條記錄

  /// 保存解讀紀錄
  static Future<void> saveRecord(InterpretationRecord record) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final records = await getAllRecords();
      
      // 檢查是否已存在相同 ID 的記錄
      final existingIndex = records.indexWhere((r) => r.id == record.id);
      if (existingIndex != -1) {
        // 更新現有記錄
        records[existingIndex] = record;
      } else {
        // 添加新記錄到開頭
        records.insert(0, record);
      }
      
      // 限制記錄數量
      if (records.length > _maxRecords) {
        records.removeRange(_maxRecords, records.length);
      }
      
      // 保存到 SharedPreferences
      final jsonList = records.map((r) => r.toJson()).toList();
      await prefs.setString(_recordsKey, jsonEncode(jsonList));
      
      logger.i("解讀紀錄已保存：${record.title}");
    } catch (e) {
      logger.e("保存解讀紀錄失敗：$e");
      rethrow;
    }
  }

  /// 獲取所有解讀紀錄
  static Future<List<InterpretationRecord>> getAllRecords() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString(_recordsKey);
      
      if (jsonString == null || jsonString.isEmpty) {
        return [];
      }
      
      final jsonList = jsonDecode(jsonString) as List;
      final records = jsonList
          .map((json) => InterpretationRecord.fromJson(json as Map<String, dynamic>))
          .toList();
      
      // 按創建時間降序排列
      records.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      
      return records;
    } catch (e) {
      logger.e("獲取解讀紀錄失敗：$e");
      return [];
    }
  }

  /// 根據 ID 獲取解讀紀錄
  static Future<InterpretationRecord?> getRecordById(String id) async {
    try {
      final records = await getAllRecords();
      return records.firstWhere(
        (record) => record.id == id,
        orElse: () => throw StateError('Record not found'),
      );
    } catch (e) {
      logger.w("未找到 ID 為 $id 的解讀紀錄");
      return null;
    }
  }

  /// 刪除解讀紀錄
  static Future<void> deleteRecord(String id) async {
    try {
      final records = await getAllRecords();
      records.removeWhere((record) => record.id == id);
      
      final prefs = await SharedPreferences.getInstance();
      final jsonList = records.map((r) => r.toJson()).toList();
      await prefs.setString(_recordsKey, jsonEncode(jsonList));
      
      logger.i("解讀紀錄已刪除：$id");
    } catch (e) {
      logger.e("刪除解讀紀錄失敗：$e");
      rethrow;
    }
  }

  /// 清空所有解讀紀錄
  static Future<void> clearAllRecords() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_recordsKey);
      logger.i("所有解讀紀錄已清空");
    } catch (e) {
      logger.e("清空解讀紀錄失敗：$e");
      rethrow;
    }
  }

  /// 搜索解讀紀錄
  static Future<List<InterpretationRecord>> searchRecords(String query) async {
    try {
      if (query.trim().isEmpty) {
        return await getAllRecords();
      }
      
      final records = await getAllRecords();
      final lowerQuery = query.toLowerCase();
      
      return records.where((record) =>
        record.title.toLowerCase().contains(lowerQuery) ||
        record.content.toLowerCase().contains(lowerQuery) ||
        record.primaryPersonName.toLowerCase().contains(lowerQuery) ||
        (record.secondaryPersonName?.toLowerCase().contains(lowerQuery) ?? false) ||
        record.chartType.toLowerCase().contains(lowerQuery)
      ).toList();
    } catch (e) {
      logger.e("搜索解讀紀錄失敗：$e");
      return [];
    }
  }

  /// 獲取最近的解讀紀錄
  static Future<List<InterpretationRecord>> getRecentRecords({int limit = 10}) async {
    try {
      final records = await getAllRecords();
      return records.take(limit).toList();
    } catch (e) {
      logger.e("獲取最近解讀紀錄失敗：$e");
      return [];
    }
  }
}
