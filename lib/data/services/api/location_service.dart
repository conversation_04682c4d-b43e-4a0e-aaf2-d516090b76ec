import 'package:geolocator/geolocator.dart';

class LocationService {
  static Future<Position> getCurrentLocation() async {
    bool serviceEnabled;
    LocationPermission permission;

    // 檢查位置服務是否啟用
    serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      throw Exception('位置服務未啟用');
    }

    // 檢查位置權限
    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        throw Exception('位置權限被拒絕');
      }
    }

    if (permission == LocationPermission.deniedForever) {
      throw Exception('位置權限被永久拒絕');
    }

    // 獲取當前位置
    return await Geolocator.getCurrentPosition();
  }

  static Future<Map<String, dynamic>> getLocationFromName(String name) async {
    // TODO: 實作地理編碼功能，將地名轉換為經緯度
    // 這裡先返回預設值
    return {
      'name': name,
      'latitude': 25.0330,
      'longitude': 121.5654,
    };
  }
}
