import '../../../core/utils/logger_utils.dart';
import '../../models/payment/payment_record.dart';
import 'ai_api_service.dart';
import 'auth_service.dart';
import 'firebase_ai_usage_service.dart';
import 'firebase_payment_service.dart';
import 'remote_config_service.dart';

/// 支付服務類，處理支付狀態檢查和支付記錄管理
/// 完全基於 Firebase 的支付服務，不再使用本地存儲
class PaymentService {

  /// 判斷是否為訂閱方案（而非單次購買或次數包）
  static bool _isSubscriptionPlan(String planType) {
    // 訂閱方案的 ID 通常是 monthly, yearly, quarterly 等
    const subscriptionPlans = ['monthly', 'yearly', 'quarterly'];
    return subscriptionPlans.contains(planType);
  }

  static Future<bool> enableAIUsageCheck() async {
    try {
      // 🔥 檢查 Firebase AI 使用量限制（通過 remote config 控制）
      final enableAIUsageCheck = RemoteConfigService.getBoolValue(
        'enable_ai_usage_check',
        defaultValue: false,
      );

      if (enableAIUsageCheck) {
        logger.i('🔍 AI 使用量檢查已啟用，正在檢查當前 AI Provider 使用量...');

        try {
          // 獲取當前選擇的 AI Provider
          final currentProvider = await AIApiService.getCurrentProvider();

          // 檢查是否已達每日使用限制
          final isLimitReached = await FirebaseAIUsageService.isLimitReached(currentProvider);

          if (isLimitReached) {
            logger.w('⚠️ ${currentProvider.displayName} 今日使用量已達上限');
            return false;
          }

          // 檢查預估使用量是否會超過限制
          final estimatedTokens = 2000; // 預估每次解讀使用的 tokens
          final canUse = await FirebaseAIUsageService.canUseProvider(
            provider: currentProvider,
            estimatedTokens: estimatedTokens,
          );

          if (!canUse) {
            logger.w('⚠️ 預估使用 $estimatedTokens tokens 會超過 ${currentProvider.displayName} 每日限制');
            return false;
          }

          logger.i('✅ AI 使用量檢查通過，可以繼續使用 ${currentProvider.displayName}');
          return true;
        } catch (aiUsageError) {
          logger.e('AI 使用量檢查失敗: $aiUsageError');
          // AI 使用量檢查失敗時，為了安全起見，允許繼續（降級處理）
          logger.w('AI 使用量檢查失敗，允許繼續使用（降級處理）');
          return false;
        }
      } else {
        logger.d('AI 使用量檢查已停用（remote config: enable_ai_usage_check = false）');
        return false;
      }
    } catch (e) {
      logger.e('檢查解讀權限時出錯: $e');
      return false;
    }
  }

  /// 檢查用戶是否有權限進行 AI 解讀
  static Future<bool> hasInterpretationPermission() async {
    try {
      if (await enableAIUsageCheck()) {
        return true;
      }

      // 檢查是否為付費用戶
      if (await isPremiumUser()) {
        return true;
      }

      // 檢查單次購買次數
      final remainingSinglePurchases = await getRemainingSinglePurchases();
      if (remainingSinglePurchases > 0) {
        return true;
      }

      // 沒有已購買次數，無權限
      return false;
    } catch (e) {
      logger.e('檢查解讀權限時出錯: $e');
      return false;
    }
  }

  /// 檢查是否為付費用戶
  static Future<bool> isPremiumUser() async {
    try {
      // 檢查用戶是否已登入
      final currentUser = AuthService.getCurrentUser();
      if (currentUser == null) {
        logger.w('用戶未登入，無法檢查付費狀態');
        return false;
      }

      // 從 Firebase 獲取支付記錄
      final payments = await getAllPayments();
      final validSubscriptions = payments.where((payment) =>
        payment.isValid &&
        payment.expiryDate.isAfter(DateTime.now()) &&
        _isSubscriptionPlan(payment.planType)  // 只包含訂閱方案
      ).toList();

      return validSubscriptions.isNotEmpty;
    } catch (e) {
      logger.e('檢查付費狀態時出錯: $e');
      return false;
    }
  }

  /// 重置免費試用記錄（已廢棄，因為不再使用免費試用）
  @Deprecated('免費試用功能已移除')
  static Future<bool> resetFreeTrialForUser(String userId) async {
    logger.w('resetFreeTrialForUser 方法已廢棄，免費試用功能已移除');
    return false;
  }

  /// 獲取剩餘單次購買次數
  static Future<int> getRemainingSinglePurchases() async {
    try {
      // 檢查用戶是否已登入
      final currentUser = AuthService.getCurrentUser();
      if (currentUser == null) {
        logger.w('用戶未登入，無法獲取單次購買次數');
        return 0;
      }

      // 從 Firebase 獲取
      final firebaseCount = await FirebasePaymentService.getRemainingSinglePurchases();
      logger.i('從 Firebase 獲取單次購買次數: $firebaseCount');
      return firebaseCount >= 0 ? firebaseCount : 0;
    } catch (e) {
      logger.e('獲取單次購買次數時出錯: $e');
      return 0;
    }
  }

  /// 添加單次購買次數
  static Future<bool> addSinglePurchase(int count) async {
    try {
      // 檢查用戶是否已登入
      final currentUser = AuthService.getCurrentUser();
      if (currentUser == null) {
        logger.w('用戶未登入，無法添加單次購買次數');
        return false;
      }

      logger.i('開始添加單次購買次數: $count');

      // 更新 Firebase
      final success = await FirebasePaymentService.addSinglePurchase(count);
      if (success) {
        logger.i('Firebase 添加單次購買次數成功');
      } else {
        logger.w('Firebase 添加單次購買次數失敗');
      }

      return success;
    } catch (e) {
      logger.e('添加單次購買次數時出錯: $e');
      return false;
    }
  }

  /// 使用一次單次購買
  static Future<bool> useSinglePurchaseAttempt() async {
    try {
      // 檢查用戶是否已登入
      final currentUser = AuthService.getCurrentUser();
      if (currentUser == null) {
        logger.w('用戶未登入，無法使用單次購買');
        return false;
      }

      final remainingPurchases = await getRemainingSinglePurchases();
      if (remainingPurchases <= 0) {
        logger.w('沒有剩餘的單次購買次數');
        return false;
      }

      logger.i('開始使用單次購買，當前次數: $remainingPurchases');

      // 更新 Firebase
      final success = await FirebasePaymentService.useSinglePurchaseAttempt();
      if (success) {
        logger.i('使用單次購買成功，剩餘次數: ${remainingPurchases - 1}');
      } else {
        logger.w('使用單次購買失敗');
      }

      return success;
    } catch (e) {
      logger.e('使用單次購買時出錯: $e');
      return false;
    }
  }

  /// 獲取所有支付記錄
  static Future<List<PaymentRecord>> getAllPayments() async {
    try {
      // 檢查用戶是否已登入
      final currentUser = AuthService.getCurrentUser();
      if (currentUser == null) {
        logger.w('用戶未登入，無法獲取支付記錄');
        return [];
      }

      // 從 Firebase 獲取支付記錄
      return await FirebasePaymentService.getUserPaymentRecords();
    } catch (e) {
      logger.e('獲取支付記錄時出錯: $e');
      return [];
    }
  }

  /// 添加支付記錄
  static Future<bool> addPaymentRecord(PaymentRecord payment) async {
    try {
      // 檢查用戶是否已登入
      final user = AuthService.getCurrentUser();
      if (user == null) {
        logger.w('用戶未登入，無法添加支付記錄');
        return false;
      }

      // 保存到 Firebase
      final firebaseSuccess = await FirebasePaymentService.savePaymentRecord(payment);
      if (firebaseSuccess) {
        logger.i('支付記錄已保存到 Firebase: ${payment.id}');

        // 更新用戶訂閱狀態到 Firebase
        final subscriptionData = await getSubscriptionSummary();
        await FirebasePaymentService.updateUserSubscriptionStatus(subscriptionData);

        return true;
      } else {
        logger.w('支付記錄保存到 Firebase 失敗');
        return false;
      }
    } catch (e) {
      logger.e('添加支付記錄時出錯: $e');
      return false;
    }
  }

  /// 模擬支付流程（開發用）
  static Future<PaymentRecord?> simulatePayment({
    required String planType,
    required double amount,
    required int durationMonths,
  }) async {
    try {
      logger.i('模擬支付流程: $planType, 金額: $amount, 期限: $durationMonths 個月');

      // 模擬支付延遲
      await Future.delayed(const Duration(seconds: 2));

      // 如果是單次購買，添加單次購買次數
      if (planType == 'single') {
        final success = await addSinglePurchase(1);
        if (success) {
          logger.i('單次購買成功，添加 1 次解讀次數');
          // 創建單次購買記錄（不需要過期時間）
          final payment = PaymentRecord(
            id: 'single_${DateTime.now().millisecondsSinceEpoch}',
            planType: planType,
            amount: amount,
            currency: 'TWD',
            paymentDate: DateTime.now(),
            expiryDate: DateTime.now().add(const Duration(days: 365)), // 單次購買設為1年有效期
            isValid: true,
            paymentMethod: 'simulation',
            transactionId: 'sim_${DateTime.now().millisecondsSinceEpoch}',
          );
          await addPaymentRecord(payment);
          return payment;
        } else {
          logger.e('單次購買失敗');
          return null;
        }
      }

      // 訂閱方案的處理
      final payment = PaymentRecord(
        id: 'payment_${DateTime.now().millisecondsSinceEpoch}',
        planType: planType,
        amount: amount,
        currency: 'TWD',
        paymentDate: DateTime.now(),
        expiryDate: DateTime.now().add(Duration(days: durationMonths * 30)),
        isValid: true,
        paymentMethod: 'simulation',
        transactionId: 'sim_${DateTime.now().millisecondsSinceEpoch}',
      );

      // 保存支付記錄
      final success = await addPaymentRecord(payment);
      if (success) {
        logger.i('模擬支付成功: ${payment.id}');
        return payment;
      } else {
        logger.e('模擬支付失敗');
        return null;
      }
    } catch (e) {
      logger.e('模擬支付時出錯: $e');
      return null;
    }
  }

  /// 獲取支付計劃選項
  static List<Map<String, dynamic>> getPaymentPlans() {
    return [
      {
        'id': 'single',
        'name': '單次解讀',
        'description': '購買一次解讀服務',
        'price': 99.0,
        'currency': 'TWD',
        'duration': 0, // 單次購買
        'type': 'single', // 標記為單次購買
        'features': [
          '一次完整的星盤解讀',
          '專業占星分析',
          '個人化建議',
          '立即可用',
        ],
      },
      {
        'id': 'monthly',
        'name': '月度方案',
        'description': '無限次解讀，月度訂閱',
        'price': 299.0,
        'currency': 'TWD',
        'duration': 1, // 月
        'type': 'subscription', // 標記為訂閱
        'features': [
          '無限次星盤解讀',
          '專業占星分析',
          '個人化建議',
          '優先客服支援',
        ],
      },
      {
        'id': 'quarterly',
        'name': '季度方案',
        'description': '無限次解讀，季度訂閱（省 10%）',
        'price': 799.0,
        'currency': 'TWD',
        'duration': 3, // 月
        'type': 'subscription', // 標記為訂閱
        'features': [
          '無限次星盤解讀',
          '專業占星分析',
          '個人化建議',
          '優先客服支援',
          '季度專屬報告',
        ],
      },
      {
        'id': 'yearly',
        'name': '年度方案',
        'description': '無限次解讀，年度訂閱（省 20%）',
        'price': 2899.0,
        'currency': 'TWD',
        'duration': 12, // 月
        'type': 'subscription', // 標記為訂閱
        'features': [
          '無限次星盤解讀',
          '專業占星分析',
          '個人化建議',
          '優先客服支援',
          '年度專屬報告',
          '一對一諮詢機會',
        ],
      },
    ];
  }

  /// 重置免費試用（已廢棄，因為不再使用免費試用）
  @Deprecated('免費試用功能已移除')
  static Future<void> resetFreeTrials() async {
    logger.w('resetFreeTrials 方法已廢棄，免費試用功能已移除');
  }

  /// 清除所有支付數據（僅用於測試）
  static Future<void> clearAllPaymentData() async {
    try {
      final currentUser = AuthService.getCurrentUser();
      if (currentUser != null) {
        // 注意：由於 FirebasePaymentService 沒有直接清除所有支付記錄的方法
        // 這裡只能重置單次購買次數
        // 支付記錄通常不應該被刪除，以保持交易歷史
        // 免費試用功能已移除

        logger.i('支付數據已重置（免費試用次數已清零）');
      } else {
        logger.w('用戶未登入，無法清除支付數據');
      }
    } catch (e) {
      logger.e('清除支付數據時出錯: $e');
    }
  }

  /// 檢查用戶權限（包含 Firebase 同步）
  static Future<bool> hasInterpretationPermissionWithSync() async {
    try {
      // 檢查用戶是否已登入
      final user = AuthService.getCurrentUser();
      if (user == null) {
        logger.w('用戶未登入，無法檢查解讀權限');
        return false;
      }

      // 直接從 Firebase 檢查權限
      return await hasInterpretationPermission();
    } catch (e) {
      logger.e('檢查解讀權限（含同步）時出錯: $e');
      // 如果同步失敗，回退到本地檢查
      return await hasInterpretationPermission();
    }
  }

  /// 獲取當前訂閱狀態摘要
  static Future<Map<String, dynamic>> getSubscriptionSummary() async {
    try {
      final isPremium = await isPremiumUser();
      const remainingTrials = 0; // 免費試用功能已移除
      final remainingSinglePurchases = await getRemainingSinglePurchases();
      final payments = await getAllPayments();

      PaymentRecord? activePayment;
      if (isPremium) {
        activePayment = payments
            .where((p) => p.isValid && p.expiryDate.isAfter(DateTime.now()))
            .fold<PaymentRecord?>(null, (latest, current) {
          if (latest == null || current.expiryDate.isAfter(latest.expiryDate)) {
            return current;
          }
          return latest;
        });
      }

      final user = AuthService.getCurrentUser();
      return {
        'isPremium': isPremium,
        'remainingTrials': remainingTrials,
        'remainingSinglePurchases': remainingSinglePurchases,
        'activePayment': activePayment?.toJson(),
        'totalPayments': payments.length,
        'userId': user?.uid,
        'userEmail': user?.email,
        'lastSyncTime': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      logger.e('獲取訂閱狀態摘要時出錯: $e');
      return {
        'isPremium': false,
        'remainingTrials': 0,
        'remainingSinglePurchases': 0,
        'activePayment': null,
        'totalPayments': 0,
        'userId': null,
        'userEmail': null,
        'lastSyncTime': null,
      };
    }
  }
}
