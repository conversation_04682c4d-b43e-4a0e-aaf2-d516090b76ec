// Web platform implementation for file download
// Web 平台的檔案下載實作

import 'dart:html' as html;

/// 使用 HTML Anchor 元素下載檔案（Web 平台專用）
void downloadFileWithAnchor(String url, String filename) {
  try {
    final anchor = html.AnchorElement(href: url)
      ..target = '_blank'
      ..download = filename
      ..style.display = 'none';
    
    // 將 anchor 添加到 DOM 並觸發點擊
    html.document.body!.append(anchor);
    anchor.click();
    
    // 清理 DOM
    anchor.remove();
    
    print('Anchor 下載已觸發: $filename');
  } catch (e) {
    print('Anchor 下載失敗: $e');
    rethrow;
  }
}
