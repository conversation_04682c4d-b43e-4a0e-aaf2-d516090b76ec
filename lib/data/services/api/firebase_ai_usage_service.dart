import 'dart:convert';
import 'dart:io';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

import '../../../core/config/firebase_config_windows.dart';
import '../../../core/constants/firebase_collections.dart';
import '../../../core/utils/logger_utils.dart';
import '../../models/interpretation/ai_usage_stats.dart';
import 'ai_api_service.dart';

/// Firebase AI 使用量控管與記錄服務
///
/// 功能：
/// 1. 記錄每日每個 AIProvider 的使用量到 Firestore
/// 2. 檢查是否達到每日使用限制
/// 3. 提供使用量統計和監控
/// 4. 支援 REST API（Windows）和 SDK（其他平台）
class FirebaseAIUsageService {
  static const String _cacheKeyPrefix = 'firebase_ai_usage_';
  static const Duration _cacheTimeout = Duration(minutes: 5);

  // 每日使用量限制（tokens）
  static const Map<AIProvider, int> _dailyLimits = {
    AIProvider.openai: 200000,    // GPT-4o: 20萬 tokens
    AIProvider.anthropic: 200000, // Claude: 20萬 tokens
    AIProvider.groq: 200000,      // Groq: 20萬 tokens
    AIProvider.gemini: 200000,    // Gemini: 20萬 tokens
  };

  // AI Provider 名稱映射
  static const Map<AIProvider, String> _providerNames = {
    AIProvider.openai: 'gpt4o',
    AIProvider.anthropic: 'claude',
    AIProvider.groq: 'groq',
    AIProvider.gemini: 'gemini',
  };

  /// 獲取今天的日期字串 (YYYY-MM-DD)
  static String _getTodayDateString() {
    final now = DateTime.now().toUtc();
    return '${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}';
  }

  /// 獲取文件 ID (格式: YYYY-MM-DD_provider)
  static String _getDocumentId(AIProvider provider) {
    final providerName = _providerNames[provider] ?? provider.name;
    return '${_getTodayDateString()}_$providerName';
  }

  /// 檢查是否可以使用指定的 AI Provider 和 token 數量
  ///
  /// 參數：
  /// - provider: AI 提供商
  /// - estimatedTokens: 預估使用的 token 數量
  ///
  /// 返回：
  /// - true: 可以使用
  /// - false: 不可使用（已達限制或會超過限制）
  static Future<bool> canUseProvider({
    required AIProvider provider,
    required int estimatedTokens,
  }) async {
    try {
      // 獲取今日使用記錄
      final record = await _getTodayUsageRecord(provider);

      // 檢查是否已達限制
      if (record.limitReached) {
        logger.w('⚠️ 今日 ${provider.displayName} 使用量已滿（${record.totalTokens} tokens），請明日再試。');
        return false;
      }

      // 檢查預估使用量是否會超過限制
      final dailyLimit = _dailyLimits[provider] ?? 200000;
      final wouldExceedLimit = (record.totalTokens + estimatedTokens) > dailyLimit;

      if (wouldExceedLimit) {
        logger.w('⚠️ 預估使用 $estimatedTokens tokens 會超過 ${provider.displayName} 每日限制');
        return false;
      }

      return true;
    } catch (e) {
      logger.e('檢查 AI Provider 使用量失敗: $e');
      // 發生錯誤時，為了安全起見，允許使用
      return true;
    }
  }

  /// 檢查 AI Provider 是否已達每日使用限制（向後相容）
  ///
  /// 返回：
  /// - true: 已達限制，不可再使用
  /// - false: 未達限制，可以使用
  static Future<bool> isLimitReached(AIProvider provider) async {
    try {
      final record = await _getTodayUsageRecord(provider);
      return record.limitReached;
    } catch (e) {
      logger.e('檢查使用限制失敗: $e');
      // 發生錯誤時，為了安全起見，假設未達限制
      return false;
    }
  }

  /// 記錄 AI 使用量
  ///
  /// 參數：
  /// - provider: AI 提供商
  /// - actualTokens: 實際使用的 token 數量
  /// - prompt: 輸入提示詞（用於 token 估算驗證）
  /// - response: AI 回應（用於 token 估算驗證）
  static Future<void> recordUsage({
    required AIProvider provider,
    required int actualTokens,
    String? prompt,
    String? response,
  }) async {
    try {
      // 獲取當前記錄
      final currentRecord = await _getTodayUsageRecord(provider);

      // 創建更新後的記錄
      final updatedRecord = FirebaseAIUsageRecord(
        date: currentRecord.date,
        aiProvider: _providerNames[provider] ?? provider.name,
        totalTokens: currentRecord.totalTokens + actualTokens,
        callCount: currentRecord.callCount + 1,
        limitReached: _checkLimitReached(provider, currentRecord.totalTokens + actualTokens),
        createdAt: currentRecord.createdAt,
        updatedAt: DateTime.now().toUtc(),
      );

      // 保存到 Firebase
      await _saveUsageRecord(updatedRecord);

      // 更新本地快取
      await _cacheUsageRecord(updatedRecord);

      logger.d('記錄 AI 使用量：${provider.displayName} - $actualTokens tokens (總計: ${updatedRecord.totalTokens})');

      // 檢查是否達到限制
      if (updatedRecord.limitReached) {
        logger.w('⚠️ ${provider.displayName} 已達每日使用限制！');
      }

    } catch (e) {
      logger.e('記錄 AI 使用量失敗: $e');
      // 記錄失敗不應該影響主要功能，只記錄錯誤
    }
  }

  /// 檢查是否達到限制
  static bool _checkLimitReached(AIProvider provider, int totalTokens) {
    final dailyLimit = _dailyLimits[provider] ?? 200000;
    return totalTokens >= dailyLimit;
  }

  /// 獲取所有 AI Provider 今日使用狀況
  static Future<List<FirebaseAIUsageRecord>> getAllTodayUsage() async {
    final records = <FirebaseAIUsageRecord>[];

    for (final provider in AIProvider.values) {
      try {
        final record = await _getTodayUsageRecord(provider);
        records.add(record);
      } catch (e) {
        logger.e('獲取 ${provider.displayName} 使用記錄失敗: $e');
        // 添加空記錄
        records.add(FirebaseAIUsageRecord.empty(
          date: _getTodayDateString(),
          aiProvider: _providerNames[provider] ?? provider.name,
        ));
      }
    }

    return records;
  }

  /// 獲取今日使用量統計（向後相容）
  static Future<Map<AIProvider, AIUsageRecord>> getTodayUsage() async {
    try {
      final result = <AIProvider, AIUsageRecord>{};

      for (final provider in AIProvider.values) {
        try {
          final firebaseRecord = await _getTodayUsageRecord(provider);
          result[provider] = AIUsageRecord(
            date: firebaseRecord.date,
            provider: provider,
            totalTokens: firebaseRecord.totalTokens,
            callCount: firebaseRecord.callCount,
            limitReached: firebaseRecord.limitReached,
            createdAt: firebaseRecord.createdAt,
            lastUpdated: firebaseRecord.updatedAt,
          );
        } catch (e) {
          logger.e('獲取 ${provider.displayName} 使用記錄失敗: $e');
          result[provider] = AIUsageRecord.empty(provider);
        }
      }

      return result;

    } catch (e) {
      logger.e('獲取今日使用量失敗: $e');

      // 返回空記錄
      return {
        for (final provider in AIProvider.values)
          provider: AIUsageRecord.empty(provider),
      };
    }
  }

  /// 獲取特定 AI Provider 的今日使用量
  static Future<AIUsageRecord> getProviderTodayUsage(AIProvider provider) async {
    try {
      final firebaseRecord = await _getTodayUsageRecord(provider);
      return AIUsageRecord(
        date: firebaseRecord.date,
        provider: provider,
        totalTokens: firebaseRecord.totalTokens,
        callCount: firebaseRecord.callCount,
        limitReached: firebaseRecord.limitReached,
        createdAt: firebaseRecord.createdAt,
        lastUpdated: firebaseRecord.updatedAt,
      );
    } catch (e) {
      logger.e('獲取 ${provider.displayName} 今日使用量失敗: $e');
      return AIUsageRecord.empty(provider);
    }
  }

  /// 獲取特定 Provider 的今日使用記錄
  static Future<FirebaseAIUsageRecord> getProviderTodayUsageRecord(AIProvider provider) async {
    return await _getTodayUsageRecord(provider);
  }

  /// 獲取剩餘可用 tokens
  static Future<int> getRemainingTokens(AIProvider provider) async {
    try {
      final record = await _getTodayUsageRecord(provider);
      return record.remainingTokens;
    } catch (e) {
      logger.e('獲取剩餘 tokens 失敗: $e');
      return 0;
    }
  }

  /// 重置所有 AI Provider 的使用量（僅供測試使用）
  static Future<void> resetAllUsage() async {
    try {
      final today = _getTodayDateString();

      for (final provider in AIProvider.values) {
        final emptyRecord = FirebaseAIUsageRecord.empty(
          date: today,
          aiProvider: _providerNames[provider] ?? provider.name,
        );

        await _saveUsageRecord(emptyRecord);
        await _cacheUsageRecord(emptyRecord);
      }

      logger.i('所有 AI 使用記錄已重置');
    } catch (e) {
      logger.e('重置使用記錄失敗: $e');
    }
  }

  // ==================== 私有方法 ====================

  /// 獲取今日使用記錄（含快取機制）
  static Future<FirebaseAIUsageRecord> _getTodayUsageRecord(AIProvider provider) async {
    final providerName = _providerNames[provider] ?? provider.name;
    final today = _getTodayDateString();

    // 先檢查本地快取
    final cachedRecord = await _getCachedUsageRecord(today, providerName);
    if (cachedRecord != null) {
      return cachedRecord;
    }

    // 從 Firebase 獲取
    final record = await _getUsageRecordFromFirebase(today, providerName);

    // 快取結果
    await _cacheUsageRecord(record);

    return record;
  }

  /// 從 Firebase 獲取使用記錄
  static Future<FirebaseAIUsageRecord> _getUsageRecordFromFirebase(
    String date,
    String providerName,
  ) async {
    try {
      if (Platform.isWindows) {
        return await _getUsageRecordViaRestApi(date, providerName);
      } else {
        return await _getUsageRecordViaSDK(date, providerName);
      }
    } catch (e) {
      logger.e('從 Firebase 獲取使用記錄失敗: $e');
      // 返回空記錄
      return FirebaseAIUsageRecord.empty(
        date: date,
        aiProvider: providerName,
      );
    }
  }

  /// 通過 REST API 獲取使用記錄
  static Future<FirebaseAIUsageRecord> _getUsageRecordViaRestApi(
    String date,
    String providerName,
  ) async {
    final documentId = '${date}_$providerName';
    final url = '${FirebaseConfigWindows.firestoreBaseUrl}/${FirebaseCollections.aiUsage}/$documentId';

    final response = await http.get(Uri.parse(url));

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      if (data != null && data['fields'] != null) {
        // 轉換 Firestore REST API 格式
        final fields = data['fields'] as Map<String, dynamic>;
        final convertedData = _convertFirestoreFields(fields);
        return FirebaseAIUsageRecord.fromFirestore(convertedData);
      }
    }

    // 文檔不存在，返回空記錄
    return FirebaseAIUsageRecord.empty(
      date: date,
      aiProvider: providerName,
    );
  }

  /// 通過 SDK 獲取使用記錄
  static Future<FirebaseAIUsageRecord> _getUsageRecordViaSDK(
    String date,
    String providerName,
  ) async {
    try {
      final firestore = FirebaseFirestore.instance;
      final documentId = '${date}_$providerName';
      final docRef = firestore.collection(FirebaseCollections.aiUsage).doc(documentId);

      final docSnapshot = await docRef.get();

      if (docSnapshot.exists) {
        final data = docSnapshot.data();
        if (data != null) {
          logger.d('通過 SDK 獲取使用記錄成功: $documentId');
          return FirebaseAIUsageRecord.fromFirestore(data);
        }
      }

      logger.d('使用記錄不存在，返回空記錄: $documentId');
      return FirebaseAIUsageRecord.empty(
        date: date,
        aiProvider: providerName,
      );
    } on FirebaseException catch (e) {
      logger.e('Firestore 錯誤 - 獲取使用記錄失敗: ${e.code} - ${e.message}');
      return FirebaseAIUsageRecord.empty(
        date: date,
        aiProvider: providerName,
      );
    } catch (e) {
      logger.e('通過 SDK 獲取使用記錄失敗: $e');
      return FirebaseAIUsageRecord.empty(
        date: date,
        aiProvider: providerName,
      );
    }
  }

  /// 保存使用記錄到 Firebase
  static Future<void> _saveUsageRecord(FirebaseAIUsageRecord record) async {
    try {
      if (Platform.isWindows) {
        await _saveUsageRecordViaRestApi(record);
      } else {
        await _saveUsageRecordViaSDK(record);
      }
    } catch (e) {
      logger.e('保存使用記錄到 Firebase 失敗: $e');
      rethrow;
    }
  }
  /// 通過 REST API 保存使用記錄
  static Future<void> _saveUsageRecordViaRestApi(FirebaseAIUsageRecord record) async {
    final url = '${FirebaseConfigWindows.firestoreBaseUrl}/${FirebaseCollections.aiUsage}/${record.documentId}';

    // 轉換為 Firestore REST API 格式
    final firestoreData = _convertToFirestoreFields(record.toFirestore());

    final response = await http.patch(
      Uri.parse(url),
      headers: {
        'Content-Type': 'application/json',
      },
      body: jsonEncode({
        'fields': firestoreData,
      }),
    );

    if (response.statusCode != 200) {
      throw Exception('保存使用記錄失敗: ${response.statusCode} - ${response.body}');
    }
  }

  /// 通過 SDK 保存使用記錄
  static Future<void> _saveUsageRecordViaSDK(FirebaseAIUsageRecord record) async {
    try {
      final firestore = FirebaseFirestore.instance;
      final docRef = firestore.collection(FirebaseCollections.aiUsage).doc(record.documentId);

      // 準備要保存的資料
      final data = record.toFirestore();

      // 使用伺服器時間戳更新時間欄位
      data['updatedAt'] = FieldValue.serverTimestamp();

      // 如果是新記錄，設定創建時間
      final docSnapshot = await docRef.get();
      if (!docSnapshot.exists) {
        data['createdAt'] = FieldValue.serverTimestamp();
      }

      // 使用 merge 選項保留現有欄位（如果有的話）
      await docRef.set(data, SetOptions(merge: true));

      logger.d('通過 SDK 保存使用記錄成功: ${record.documentId}');
    } on FirebaseException catch (e) {
      logger.e('Firestore 錯誤 - 保存使用記錄失敗: ${e.code} - ${e.message}');
      throw Exception('Firestore 保存失敗: ${e.message}');
    } catch (e) {
      logger.e('通過 SDK 保存使用記錄失敗: $e');
      throw Exception('保存使用記錄失敗: $e');
    }
  }

  /// 獲取快取的使用記錄
  static Future<FirebaseAIUsageRecord?> _getCachedUsageRecord(
    String date,
    String providerName,
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = '$_cacheKeyPrefix${date}_$providerName';
      final cachedJson = prefs.getString(cacheKey);

      if (cachedJson != null) {
        final cachedData = jsonDecode(cachedJson);
        final cacheTime = DateTime.parse(cachedData['cacheTime']);

        // 檢查快取是否過期
        if (DateTime.now().difference(cacheTime) < _cacheTimeout) {
          return FirebaseAIUsageRecord.fromFirestore(cachedData['record']);
        }
      }

      return null;
    } catch (e) {
      logger.e('獲取快取記錄失敗: $e');
      return null;
    }
  }

  /// 快取使用記錄
  static Future<void> _cacheUsageRecord(FirebaseAIUsageRecord record) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = '$_cacheKeyPrefix${record.date}_${record.aiProvider}';

      final cacheData = {
        'record': record.toFirestore(),
        'cacheTime': DateTime.now().toIso8601String(),
      };

      await prefs.setString(cacheKey, jsonEncode(cacheData));
    } catch (e) {
      logger.e('快取記錄失敗: $e');
    }
  }

  /// 轉換 Firestore 欄位格式（REST API 用）
  static Map<String, dynamic> _convertFirestoreFields(Map<String, dynamic> fields) {
    final result = <String, dynamic>{};

    for (final entry in fields.entries) {
      final key = entry.key;
      final value = entry.value;

      if (value is Map<String, dynamic>) {
        if (value.containsKey('stringValue')) {
          result[key] = value['stringValue'];
        } else if (value.containsKey('integerValue')) {
          result[key] = int.parse(value['integerValue'].toString());
        } else if (value.containsKey('booleanValue')) {
          result[key] = value['booleanValue'];
        }
      }
    }

    return result;
  }

  /// 轉換為 Firestore 欄位格式（REST API 用）
  static Map<String, dynamic> _convertToFirestoreFields(Map<String, dynamic> data) {
    final result = <String, dynamic>{};

    for (final entry in data.entries) {
      final key = entry.key;
      final value = entry.value;

      if (value is String) {
        result[key] = {'stringValue': value};
      } else if (value is int) {
        result[key] = {'integerValue': value.toString()};
      } else if (value is bool) {
        result[key] = {'booleanValue': value};
      }
    }

    return result;
  }

  /// 獲取使用量限制資訊
  static Map<AIProvider, int> getDailyLimits() {
    return Map.from(_dailyLimits);
  }

  /// 檢查是否可以使用指定的 token 數量（向後相容）
  static Future<bool> canUseTokens(AIProvider provider, int tokens) async {
    return await canUseProvider(provider: provider, estimatedTokens: tokens);
  }

  // ==================== SDK 專用高級功能 ====================

  /// 批量獲取多個日期的使用記錄（僅 SDK 支援）
  static Future<List<FirebaseAIUsageRecord>> getBatchUsageRecords({
    required List<String> dates,
    required AIProvider provider,
  }) async {
    if (Platform.isWindows) {
      logger.w('Windows 平台不支援批量操作，將逐一獲取');
      final records = <FirebaseAIUsageRecord>[];
      for (final date in dates) {
        final record = await _getUsageRecordFromFirebase(
          date,
          _providerNames[provider] ?? provider.name,
        );
        records.add(record);
      }
      return records;
    }

    try {
      final firestore = FirebaseFirestore.instance;
      final providerName = _providerNames[provider] ?? provider.name;
      final documentIds = dates.map((date) => '${date}_$providerName').toList();

      // 使用 whereIn 查詢批量獲取
      final querySnapshot = await firestore
          .collection(FirebaseCollections.aiUsage)
          .where(FieldPath.documentId, whereIn: documentIds)
          .get();

      final records = <FirebaseAIUsageRecord>[];
      final foundDocIds = querySnapshot.docs.map((doc) => doc.id).toSet();

      // 處理找到的記錄
      for (final doc in querySnapshot.docs) {
        final data = doc.data();
        records.add(FirebaseAIUsageRecord.fromFirestore(data));
      }

      // 為沒有找到的日期創建空記錄
      for (final date in dates) {
        final docId = '${date}_$providerName';
        if (!foundDocIds.contains(docId)) {
          records.add(FirebaseAIUsageRecord.empty(
            date: date,
            aiProvider: providerName,
          ));
        }
      }

      logger.d('批量獲取使用記錄成功，共 ${records.length} 筆');
      return records;
    } on FirebaseException catch (e) {
      logger.e('Firestore 錯誤 - 批量獲取使用記錄失敗: ${e.code} - ${e.message}');
      return [];
    } catch (e) {
      logger.e('批量獲取使用記錄失敗: $e');
      return [];
    }
  }

  /// 監聽特定 Provider 的使用量變化（僅 SDK 支援）
  static Stream<FirebaseAIUsageRecord>? listenToUsageChanges(AIProvider provider) {
    if (Platform.isWindows) {
      logger.w('Windows 平台不支援實時監聽');
      return null;
    }

    try {
      final firestore = FirebaseFirestore.instance;
      final providerName = _providerNames[provider] ?? provider.name;
      final today = _getTodayDateString();
      final documentId = '${today}_$providerName';

      return firestore
          .collection(FirebaseCollections.aiUsage)
          .doc(documentId)
          .snapshots()
          .map((docSnapshot) {
        if (docSnapshot.exists) {
          final data = docSnapshot.data();
          if (data != null) {
            return FirebaseAIUsageRecord.fromFirestore(data);
          }
        }

        return FirebaseAIUsageRecord.empty(
          date: today,
          aiProvider: providerName,
        );
      });
    } on FirebaseException catch (e) {
      logger.e('Firestore 錯誤 - 監聽使用量變化失敗: ${e.code} - ${e.message}');
      return null;
    } catch (e) {
      logger.e('監聽使用量變化失敗: $e');
      return null;
    }
  }

  /// 批量重置多個 Provider 的使用量（僅 SDK 支援）
  static Future<void> batchResetUsage({
    required List<AIProvider> providers,
    String? targetDate,
  }) async {
    if (Platform.isWindows) {
      logger.w('Windows 平台不支援批量操作，將逐一重置');
      for (final provider in providers) {
        final emptyRecord = FirebaseAIUsageRecord.empty(
          date: targetDate ?? _getTodayDateString(),
          aiProvider: _providerNames[provider] ?? provider.name,
        );
        await _saveUsageRecordViaRestApi(emptyRecord);
      }
      return;
    }

    try {
      final firestore = FirebaseFirestore.instance;
      final batch = firestore.batch();
      final date = targetDate ?? _getTodayDateString();

      for (final provider in providers) {
        final providerName = _providerNames[provider] ?? provider.name;
        final emptyRecord = FirebaseAIUsageRecord.empty(
          date: date,
          aiProvider: providerName,
        );

        final docRef = firestore.collection(FirebaseCollections.aiUsage).doc(emptyRecord.documentId);
        final data = emptyRecord.toFirestore();
        data['createdAt'] = FieldValue.serverTimestamp();
        data['updatedAt'] = FieldValue.serverTimestamp();

        batch.set(docRef, data);
      }

      await batch.commit();
      logger.i('批量重置 ${providers.length} 個 Provider 的使用量成功');
    } on FirebaseException catch (e) {
      logger.e('Firestore 錯誤 - 批量重置使用量失敗: ${e.code} - ${e.message}');
      throw Exception('批量重置失敗: ${e.message}');
    } catch (e) {
      logger.e('批量重置使用量失敗: $e');
      throw Exception('批量重置失敗: $e');
    }
  }

  /// 獲取使用量統計摘要（僅 SDK 支援）
  static Future<Map<String, dynamic>> getUsageSummary({
    String? targetDate,
  }) async {
    if (Platform.isWindows) {
      logger.w('Windows 平台不支援統計查詢，使用基本方法');
      final records = await getAllTodayUsage();
      final totalTokens = records.fold<int>(0, (sum, record) => sum + record.totalTokens);
      final totalCalls = records.fold<int>(0, (sum, record) => sum + record.callCount);
      final limitReachedCount = records.where((record) => record.limitReached).length;

      return {
        'date': targetDate ?? _getTodayDateString(),
        'totalTokens': totalTokens,
        'totalCalls': totalCalls,
        'limitReachedCount': limitReachedCount,
        'providerCount': records.length,
      };
    }

    try {
      final firestore = FirebaseFirestore.instance;
      final date = targetDate ?? _getTodayDateString();

      // 查詢指定日期的所有使用記錄
      final querySnapshot = await firestore
          .collection(FirebaseCollections.aiUsage)
          .where('date', isEqualTo: date)
          .get();

      int totalTokens = 0;
      int totalCalls = 0;
      int limitReachedCount = 0;

      for (final doc in querySnapshot.docs) {
        final data = doc.data();
        totalTokens += (data['totalTokens'] as int? ?? 0);
        totalCalls += (data['callCount'] as int? ?? 0);
        if (data['limitReached'] as bool? ?? false) {
          limitReachedCount++;
        }
      }

      return {
        'date': date,
        'totalTokens': totalTokens,
        'totalCalls': totalCalls,
        'limitReachedCount': limitReachedCount,
        'providerCount': querySnapshot.docs.length,
        'records': querySnapshot.docs.map((doc) =>
          FirebaseAIUsageRecord.fromFirestore(doc.data())
        ).toList(),
      };
    } on FirebaseException catch (e) {
      logger.e('Firestore 錯誤 - 獲取使用量統計失敗: ${e.code} - ${e.message}');
      return {
        'date': targetDate ?? _getTodayDateString(),
        'totalTokens': 0,
        'totalCalls': 0,
        'limitReachedCount': 0,
        'providerCount': 0,
        'error': e.message,
      };
    } catch (e) {
      logger.e('獲取使用量統計失敗: $e');
      return {
        'date': targetDate ?? _getTodayDateString(),
        'totalTokens': 0,
        'totalCalls': 0,
        'limitReachedCount': 0,
        'providerCount': 0,
        'error': e.toString(),
      };
    }
  }
}

/// AI 使用記錄資料模型（向後相容）
class AIUsageRecord {
  final String date;
  final AIProvider provider;
  final int totalTokens;
  final int callCount;
  final bool limitReached;
  final DateTime? createdAt;
  final DateTime? lastUpdated;
  final DateTime? limitReachedAt;

  AIUsageRecord({
    required this.date,
    required this.provider,
    required this.totalTokens,
    required this.callCount,
    required this.limitReached,
    this.createdAt,
    this.lastUpdated,
    this.limitReachedAt,
  });

  /// 從 Firestore 資料創建記錄
  factory AIUsageRecord.fromFirestore(Map<String, dynamic> data) {
    final providerName = data['aiProvider'] as String;

    // 支援新的 provider 名稱映射
    AIProvider provider = AIProvider.openai; // 預設值
    for (final entry in FirebaseAIUsageService._providerNames.entries) {
      if (entry.value == providerName || entry.key.name == providerName) {
        provider = entry.key;
        break;
      }
    }

    return AIUsageRecord(
      date: data['date'] as String,
      provider: provider,
      totalTokens: data['totalTokens'] as int? ?? 0,
      callCount: data['callCount'] as int? ?? 0,
      limitReached: data['limitReached'] as bool? ?? false,
      createdAt: data['createdAt'] is String
          ? DateTime.parse(data['createdAt'])
          : null, // (data['createdAt'] as Timestamp?)?.toDate(),
      lastUpdated: data['updatedAt'] is String
          ? DateTime.parse(data['updatedAt'])
          : null, // (data['lastUpdated'] as Timestamp?)?.toDate(),
      limitReachedAt: data['limitReachedAt'] is String
          ? DateTime.parse(data['limitReachedAt'])
          : null, // (data['limitReachedAt'] as Timestamp?)?.toDate(),
    );
  }

  /// 創建空記錄
  factory AIUsageRecord.empty(AIProvider provider) {
    return AIUsageRecord(
      date: FirebaseAIUsageService._getTodayDateString(),
      provider: provider,
      totalTokens: 0,
      callCount: 0,
      limitReached: false,
    );
  }

  /// 獲取使用百分比
  double get usagePercentage {
    final limit = FirebaseAIUsageService._dailyLimits[provider] ?? 200000;
    return (totalTokens / limit * 100).clamp(0.0, 100.0);
  }

  /// 獲取剩餘 token 數量
  int get remainingTokens {
    final limit = FirebaseAIUsageService._dailyLimits[provider] ?? 200000;
    final remaining = limit - totalTokens;
    return remaining > 0 ? remaining : 0;
  }

  @override
  String toString() {
    return 'AIUsageRecord(provider: ${provider.displayName}, tokens: $totalTokens, calls: $callCount, limitReached: $limitReached)';
  }
}
