import '../../../astreal.dart';
import '../../../shared/utils/device_info_service.dart';
import 'user_profile_service.dart';

/// 用戶設備追蹤服務
/// 負責在用戶登入時更新設備信息和 IP 地址
class UserDeviceTrackingService {
  /// 更新用戶的設備信息和 IP 地址
  /// 
  /// [userId] - 用戶 ID
  /// [forceUpdate] - 是否強制更新（忽略快取）
  static Future<bool> updateUserDeviceInfo(String userId, {bool forceUpdate = false}) async {
    try {
      logger.i('開始更新用戶設備信息: $userId');

      // 獲取當前用戶資料
      final currentUser = await UserProfileService.getUserById(userId);
      if (currentUser == null) {
        logger.w('用戶不存在，無法更新設備信息: $userId');
        return false;
      }

      // 獲取設備和網路信息
      final deviceInfo = await DeviceInfoService.getUserDeviceAndNetworkInfo();
      final platform = deviceInfo['platform'];
      final ipAddress = deviceInfo['ip_address'];

      // 檢查是否需要更新
      if (!forceUpdate && 
          currentUser.platform == platform && 
          currentUser.lastLoginIp == ipAddress) {
        // logger.d('設備信息無變化，跳過更新: $userId');
        return true;
      }

      // 更新用戶資料
      final updatedUser = currentUser.copyWith(
        lastLoginIp: ipAddress,
        platform: platform,
        lastLoginAt: DateTime.now(),
        loginCount: currentUser.loginCount + 1,
        updatedAt: DateTime.now(),
      );

      // 保存到資料庫
      try {
        await UserProfileService.updateUser(userId, updatedUser);
        logger.i('用戶設備信息更新成功: $userId');
        logger.d('  - 平台: $platform');
        logger.d('  - IP 地址: $ipAddress');
        logger.d('  - 登入次數: ${updatedUser.loginCount}');
        return true;
      } catch (e) {
        logger.e('用戶設備信息更新失敗: $userId, 錯誤: $e');
        return false;
      }
    } catch (e) {
      logger.e('更新用戶設備信息時發生錯誤: $e');
      return false;
    }
  }

  /// 在用戶登入時調用此方法
  /// 
  /// [userId] - 用戶 ID
  static Future<void> onUserLogin(String userId) async {
    try {
      logger.i('用戶登入，更新設備信息: $userId');
      
      // 異步更新設備信息，不阻塞登入流程
      updateUserDeviceInfo(userId, forceUpdate: true).then((success) {
        if (success) {
          logger.i('用戶登入設備信息更新完成: $userId');
        } else {
          logger.w('用戶登入設備信息更新失敗: $userId');
        }
      }).catchError((error) {
        logger.e('用戶登入設備信息更新異常: $error');
      });
    } catch (e) {
      logger.e('處理用戶登入設備信息更新時發生錯誤: $e');
    }
  }

  /// 批量更新所有用戶的設備信息（管理員功能）
  /// 
  /// [limit] - 每次處理的用戶數量限制
  static Future<Map<String, int>> batchUpdateAllUsersDeviceInfo({int limit = 50}) async {
    try {
      logger.i('開始批量更新所有用戶的設備信息...');
      
      int successCount = 0;
      int failureCount = 0;
      int totalCount = 0;

      // 分批獲取用戶
      final users = await UserProfileService.getAllUsers();
      totalCount = users.length;
      
      logger.i('找到 $totalCount 個用戶需要更新設備信息');

      // 分批處理，避免一次性處理太多用戶
      for (int i = 0; i < users.length; i += limit) {
        final batch = users.skip(i).take(limit).toList();
        logger.d('處理第 ${(i ~/ limit) + 1} 批用戶 (${batch.length} 個)');

        // 並行處理當前批次的用戶
        final futures = batch.map((user) =>
          updateUserDeviceInfo(user.userId, forceUpdate: false)
        ).toList();

        final results = await Future.wait(futures);

        // 統計結果
        for (final success in results) {
          if (success) {
            successCount++;
          } else {
            failureCount++;
          }
        }

        // 避免過於頻繁的請求
        if (i + limit < users.length) {
          await Future.delayed(const Duration(milliseconds: 500));
        }
      }

      final result = {
        'total': totalCount,
        'success': successCount,
        'failure': failureCount,
      };

      logger.i('批量更新用戶設備信息完成: $result');
      return result;
    } catch (e) {
      logger.e('批量更新用戶設備信息時發生錯誤: $e');
      return {
        'total': 0,
        'success': 0,
        'failure': 0,
        'error': 1,
      };
    }
  }

  /// 獲取設備信息統計
  static Future<Map<String, dynamic>> getDeviceInfoStatistics() async {
    try {
      logger.i('獲取設備信息統計...');
      
      final users = await UserProfileService.getAllUsers();
      final stats = <String, dynamic>{
        'total_users': users.length,
        'users_with_platform': 0,
        'users_with_ip': 0,
        'platform_distribution': <String, int>{},
        'recent_logins': 0, // 最近24小時內登入的用戶
      };

      final now = DateTime.now();
      final yesterday = now.subtract(const Duration(days: 1));

      for (final user in users) {
        // 統計有平台信息的用戶
        if (user.platform != null && user.platform!.isNotEmpty) {
          stats['users_with_platform']++;
          
          // 統計平台分佈
          final platform = user.platform!;
          final platformKey = platform.split(' ')[0]; // 取平台名稱的第一部分
          stats['platform_distribution'][platformKey] = 
            (stats['platform_distribution'][platformKey] ?? 0) + 1;
        }

        // 統計有 IP 地址的用戶
        if (user.lastLoginIp != null && user.lastLoginIp!.isNotEmpty) {
          stats['users_with_ip']++;
        }

        // 統計最近登入的用戶
        if (user.lastLoginAt.isAfter(yesterday)) {
          stats['recent_logins']++;
        }
      }

      logger.i('設備信息統計完成: $stats');
      return stats;
    } catch (e) {
      logger.e('獲取設備信息統計時發生錯誤: $e');
      return {
        'error': e.toString(),
      };
    }
  }

  /// 清理過期的設備信息快取
  static Future<void> clearExpiredCache() async {
    try {
      DeviceInfoService.clearCache();
      logger.i('設備信息快取已清理');
    } catch (e) {
      logger.e('清理設備信息快取時發生錯誤: $e');
    }
  }
}
