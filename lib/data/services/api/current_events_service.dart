import '../../../core/utils/logger_utils.dart';
import '../../models/current_events/current_events_data.dart';
import 'remote_config_service.dart';

/// 時事分析服務
/// 負責管理卜卦分析、二分二至盤、日月蝕盤等時事占星功能
class CurrentEventsService {
  static CurrentEventsConfig? _cachedConfig;
  static DateTime? _lastFetchTime;
  static const Duration _cacheExpiration = Duration(hours: 6);

  /// 獲取時事分析配置
  static Future<CurrentEventsConfig> getEventsConfig() async {
    logger.d('=== 獲取時事分析配置 ===');

    // 檢查快取是否有效
    if (_cachedConfig != null && _lastFetchTime != null) {
      final timeSinceLastFetch = DateTime.now().difference(_lastFetchTime!);
      if (timeSinceLastFetch < _cacheExpiration) {
        logger.d('使用快取的時事分析配置');
        return _cachedConfig!;
      }
    }

    try {
      // 從 Remote Config 獲取配置
      final configData = RemoteConfigService.getCurrentEventsConfig();
      
      if (configData.isEmpty) {
        logger.w('時事分析配置為空，返回預設配置');
        return _createDefaultConfig();
      }

      // 解析配置
      final config = CurrentEventsConfig.fromJson(configData);
      
      // 更新快取
      _cachedConfig = config;
      _lastFetchTime = DateTime.now();
      
      logger.i('成功獲取時事分析配置，包含 ${config.events.length} 個事件');
      return config;
    } catch (e) {
      logger.e('獲取時事分析配置失敗: $e');
      
      // 如果有快取，返回快取
      if (_cachedConfig != null) {
        logger.w('使用過期的快取配置');
        return _cachedConfig!;
      }
      
      // 返回預設配置
      return _createDefaultConfig();
    }
  }

  /// 獲取所有事件
  static Future<List<CurrentEventsData>> getAllEvents() async {
    final config = await getEventsConfig();
    return config.events;
  }

  /// 獲取活躍事件
  static Future<List<CurrentEventsData>> getActiveEvents() async {
    final config = await getEventsConfig();
    return config.activeEvents;
  }

  /// 根據類型獲取事件
  static Future<List<CurrentEventsData>> getEventsByType(CurrentEventsType type) async {
    final config = await getEventsConfig();
    return config.getEventsByType(type);
  }

  /// 獲取最近事件
  static Future<List<CurrentEventsData>> getRecentEvents({int limit = 5}) async {
    final config = await getEventsConfig();
    return config.getRecentEvents(limit: limit);
  }

  /// 搜尋事件
  static Future<List<CurrentEventsData>> searchEvents(String query) async {
    if (query.trim().isEmpty) {
      return await getAllEvents();
    }
    
    final config = await getEventsConfig();
    return config.searchEvents(query);
  }

  /// 根據 ID 獲取事件
  static Future<CurrentEventsData?> getEventById(String eventId) async {
    final events = await getAllEvents();
    try {
      return events.firstWhere((event) => event.id == eventId);
    } catch (e) {
      return null;
    }
  }

  /// 清除快取
  static void clearCache() {
    logger.d('清除時事分析配置快取');
    _cachedConfig = null;
    _lastFetchTime = null;
  }

  /// 手動刷新配置
  static Future<CurrentEventsConfig> refreshConfig() async {
    logger.i('手動刷新時事分析配置');
    clearCache();
    
    // 嘗試刷新 Remote Config
    try {
      await RemoteConfigService.refresh();
    } catch (e) {
      logger.w('刷新 Remote Config 失敗: $e');
    }
    
    return await getEventsConfig();
  }

  /// 創建預設配置
  static CurrentEventsConfig _createDefaultConfig() {
    final now = DateTime.now();
    
    return CurrentEventsConfig(
      events: [
        // 卜卦分析範例
        CurrentEventsData(
          id: 'horary_001',
          type: CurrentEventsType.horary,
          title: '卜卦占星入門',
          description: '學習如何使用卜卦占星來回答具體問題',
          beginnerDescription: '卜卦占星是一種針對特定問題的占星技巧，當你有明確的疑問時，可以透過當下的星象來尋求指引。',
          eventDate: now,
          iconPath: 'assets/icons/horary.png',
          keywords: ['卜卦', '問事', '占星', '指引'],
          isActive: true,
        ),
        
        // 二分二至盤範例
        CurrentEventsData(
          id: 'solstice_001',
          type: CurrentEventsType.solstice,
          title: '春分能量分析',
          description: '探索春分時刻的宇宙能量與個人影響',
          beginnerDescription: '二分二至是一年中四個重要的能量轉換點：春分、夏至、秋分、冬至。這些時刻標誌著季節的轉換，也帶來不同的宇宙能量。',
          eventDate: DateTime(now.year, 3, 20),
          iconPath: 'assets/icons/solstice.png',
          keywords: ['春分', '季節', '能量', '轉換'],
          isActive: false,
        ),
        
        // 日月蝕盤範例
        CurrentEventsData(
          id: 'eclipse_001',
          type: CurrentEventsType.eclipse,
          title: '月蝕能量影響',
          description: '分析月蝕對個人和集體意識的深層影響',
          beginnerDescription: '日月蝕是非常強大的宇宙事件，代表著重要的結束與開始。月蝕通常帶來情感的釋放和內在的轉化，而日蝕則象徵新的開始和機會。',
          eventDate: DateTime(now.year, now.month + 1, 15),
          iconPath: 'assets/icons/eclipse.png',
          keywords: ['月蝕', '轉化', '釋放', '情感'],
          isActive: true,
        ),
      ],
      lastUpdated: now,
    );
  }

  /// 獲取類型說明
  static String getTypeExplanation(CurrentEventsType type) {
    switch (type) {
      case CurrentEventsType.horary:
        return '''
卜卦占星（Horary Astrology）是占星學中的一個特殊分支，專門用來回答具體的問題。

🔮 什麼是卜卦占星？
當你有明確的疑問時，占星師會根據你提問的確切時間和地點繪製星盤，透過分析當下的星象來尋求答案。

💫 適合的問題類型：
• 感情問題：「他/她喜歡我嗎？」
• 工作決策：「我應該接受這份工作嗎？」
• 失物尋找：「我的東西在哪裡？」
• 時機選擇：「什麼時候是最佳時機？」

⭐ 初心者小提醒：
卜卦占星需要非常具體和真誠的問題，而且一個問題只能問一次。問題越明確，答案就越準確！
        ''';
        
      case CurrentEventsType.solstice:
        return '''
二分二至盤是根據一年中四個重要天文時刻繪製的星盤。

🌸 春分（3月20-21日）
白天和黑夜等長，象徵新的開始、成長和希望。適合開始新計劃、種下新的種子。

☀️ 夏至（6月20-21日）
一年中白天最長的日子，代表豐盛、活力和創造力的高峰。適合慶祝成果、展現才華。

🍂 秋分（9月22-23日）
再次日夜等長，象徵收穫、平衡和感恩。適合整理、反思和準備過冬。

❄️ 冬至（12月21-22日）
一年中白天最短，代表內省、休息和重生。適合放慢腳步、積蓄能量。

⭐ 初心者小提醒：
每個季節都有其獨特的能量，了解這些自然節律可以幫助你更好地安排生活和工作！
        ''';
        
      case CurrentEventsType.eclipse:
        return '''
日月蝕是非常強大的宇宙事件，對個人和集體都有深遠的影響。

🌑 日蝕（新月時發生）
象徵新的開始、機會和可能性。日蝕帶來的能量通常會在未來6個月內逐漸展現。

🌕 月蝕（滿月時發生）
代表結束、釋放和轉化。月蝕幫助我們放下不再需要的東西，為新的成長騰出空間。

💫 蝕相的影響：
• 情感層面：深層的情感釋放和療癒
• 關係層面：重要關係的轉變或結束
• 事業層面：職業方向的重大調整
• 靈性層面：意識的覺醒和提升

⭐ 初心者小提醒：
蝕相期間（前後兩週）是能量非常強烈的時期，建議保持開放的心態，接受生活中的變化。避免做重大決定，讓變化自然發生。
        ''';
    }
  }

  /// 獲取統計資訊
  static Future<Map<String, dynamic>> getStatistics() async {
    final config = await getEventsConfig();
    
    return {
      'totalEvents': config.events.length,
      'activeEvents': config.activeEvents.length,
      'horaryEvents': config.getEventsByType(CurrentEventsType.horary).length,
      'solsticeEvents': config.getEventsByType(CurrentEventsType.solstice).length,
      'eclipseEvents': config.getEventsByType(CurrentEventsType.eclipse).length,
      'lastUpdated': config.lastUpdated.toIso8601String(),
      'cacheStatus': _cachedConfig != null ? 'cached' : 'not_cached',
      'lastFetchTime': _lastFetchTime?.toIso8601String(),
    };
  }
}
