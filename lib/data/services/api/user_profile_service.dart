import 'package:cloud_firestore/cloud_firestore.dart';

import '../../../core/constants/firebase_collections.dart';
import '../../../core/utils/logger_utils.dart';
import '../../models/user/app_user.dart';
import '../../models/user/user_profile.dart';
import '../../repositories/user_profile_repository.dart';
import '../user_profile_unified_service.dart';

/// 用戶資料服務 - 處理 Firestore 中的用戶資料操作
///
/// @deprecated 請使用 UserProfileUnifiedService 替代
/// 此類保留用於向後兼容，建議逐步遷移到新的統一服務
class UserProfileService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static const String _collection = FirebaseCollections.userProfiles;

  /// 獲取所有用戶資料
  /// @deprecated 請使用 UserProfileUnifiedService.getAllUsers()
  static Future<List<UserProfile>> getAllUsers({
    int? limit,
    String? orderBy = 'created_at',
    bool descending = true,
  }) async {
    return await UserProfileRepository.getAllUsers(
      limit: limit,
      orderBy: orderBy,
      descending: descending,
    );
  }

  /// 根據 UID 獲取用戶資料
  /// @deprecated 請使用 UserProfileUnifiedService.getUserById()
  static Future<UserProfile?> getUserById(String uid) async {
    return await UserProfileUnifiedService.getUserById(uid);
  }

  /// 更新用戶資料
  /// @deprecated 請使用 UserProfileUnifiedService.updateUser()
  static Future<void> updateUser(String uid, UserProfile userProfile) async {
    await UserProfileUnifiedService.updateUser(uid, userProfile);
  }

  /// 更新用戶資料（使用 Map）- 向後兼容
  static Future<void> updateUserData(String uid, Map<String, dynamic> data) async {
    try {
      logger.i('更新用戶資料: $uid');

      // 添加更新時間
      data['updated_at'] = FieldValue.serverTimestamp();

      await _firestore.collection(_collection).doc(uid).update(data);

      logger.i('用戶資料更新成功: $uid');
    } catch (e) {
      logger.e('更新用戶資料失敗: $e');
      rethrow;
    }
  }

  /// 創建用戶資料
  static Future<void> createUser(String uid, UserProfile userProfile) async {
    try {
      logger.i('創建用戶資料: $uid');

      final data = userProfile.toFirestoreJson();

      await _firestore.collection(_collection).doc(uid).set(data);

      logger.i('用戶資料創建成功: $uid');
    } catch (e) {
      logger.e('創建用戶資料失敗: $e');
      rethrow;
    }
  }

  /// 創建用戶資料（使用 Map）- 向後兼容
  static Future<void> createUserData(String uid, Map<String, dynamic> data) async {
    try {
      logger.i('創建用戶資料: $uid');

      // 添加創建時間
      data['created_at'] = FieldValue.serverTimestamp();
      data['updated_at'] = FieldValue.serverTimestamp();

      await _firestore.collection(_collection).doc(uid).set(data);

      logger.i('用戶資料創建成功: $uid');
    } catch (e) {
      logger.e('創建用戶資料失敗: $e');
      rethrow;
    }
  }

  /// 刪除用戶資料
  static Future<void> deleteUser(String uid) async {
    try {
      logger.i('刪除用戶資料: $uid');
      
      await _firestore.collection(_collection).doc(uid).delete();
      
      logger.i('用戶資料刪除成功: $uid');
    } catch (e) {
      logger.e('刪除用戶資料失敗: $e');
      rethrow;
    }
  }

  /// 搜尋用戶
  static Future<List<Map<String, dynamic>>> searchUsers({
    String? email,
    String? displayName,
    bool? emailVerified,
    bool? isAnonymous,
    bool? isAdmin,
    int? limit = 50,
  }) async {
    try {
      logger.i('搜尋用戶...');
      
      Query query = _firestore.collection(_collection);
      
      // 添加篩選條件
      if (email != null && email.isNotEmpty) {
        query = query.where('email', isEqualTo: email);
      }
      
      if (displayName != null && displayName.isNotEmpty) {
        query = query.where('displayName', isEqualTo: displayName);
      }
      
      if (emailVerified != null) {
        query = query.where('emailVerified', isEqualTo: emailVerified);
      }
      
      if (isAnonymous != null) {
        query = query.where('isAnonymous', isEqualTo: isAnonymous);
      }
      
      if (isAdmin != null) {
        query = query.where('isAdmin', isEqualTo: isAdmin);
      }
      
      if (limit != null) {
        query = query.limit(limit);
      }
      
      final querySnapshot = await query.get();
      
      final users = querySnapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        data['id'] = doc.id;
        return data;
      }).toList();
      
      logger.i('搜尋到 ${users.length} 個用戶');
      return users;
    } catch (e) {
      logger.e('搜尋用戶失敗: $e');
      rethrow;
    }
  }

  /// 獲取用戶統計資料
  static Future<Map<String, int>> getUserStats() async {
    try {
      logger.i('獲取用戶統計資料...');
      
      // 總用戶數
      final totalUsersSnapshot = await _firestore.collection(_collection).get();
      final totalUsers = totalUsersSnapshot.docs.length;
      
      // 已驗證用戶數
      final verifiedUsersSnapshot = await _firestore
          .collection(_collection)
          .where('emailVerified', isEqualTo: true)
          .get();
      final verifiedUsers = verifiedUsersSnapshot.docs.length;
      
      // 匿名用戶數
      final anonymousUsersSnapshot = await _firestore
          .collection(_collection)
          .where('isAnonymous', isEqualTo: true)
          .get();
      final anonymousUsers = anonymousUsersSnapshot.docs.length;
      
      // 管理者數 (使用正確的欄位名稱)
      final adminUsersSnapshot = await _firestore
          .collection(_collection)
          .where('is_admin', isEqualTo: true)
          .get();
      final adminUsers = adminUsersSnapshot.docs.length;
      
      // 今日新增用戶（過去24小時）
      final yesterday = DateTime.now().subtract(const Duration(days: 1));
      final todayUsersSnapshot = await _firestore
          .collection(_collection)
          .where('createdAt', isGreaterThan: Timestamp.fromDate(yesterday))
          .get();
      final todayUsers = todayUsersSnapshot.docs.length;
      
      final stats = {
        'totalUsers': totalUsers,
        'verifiedUsers': verifiedUsers,
        'anonymousUsers': anonymousUsers,
        'adminUsers': adminUsers,
        'todayUsers': todayUsers,
      };
      
      logger.i('用戶統計資料: $stats');
      return stats;
    } catch (e) {
      logger.e('獲取用戶統計資料失敗: $e');
      return {
        'totalUsers': 0,
        'verifiedUsers': 0,
        'anonymousUsers': 0,
        'adminUsers': 0,
        'todayUsers': 0,
      };
    }
  }

  /// 批量更新用戶資料
  static Future<void> batchUpdateUsers(
    List<String> uids,
    Map<String, dynamic> updateData,
  ) async {
    try {
      logger.i('批量更新 ${uids.length} 個用戶');
      
      final batch = _firestore.batch();
      updateData['updatedAt'] = FieldValue.serverTimestamp();
      
      for (final uid in uids) {
        final docRef = _firestore.collection(_collection).doc(uid);
        batch.update(docRef, updateData);
      }
      
      await batch.commit();
      
      logger.i('批量更新完成');
    } catch (e) {
      logger.e('批量更新失敗: $e');
      rethrow;
    }
  }

  /// 監聽用戶資料變更
  static Stream<List<UserProfile>> watchUsers({
    int? limit,
    String? orderBy = 'created_at',
    bool descending = true,
  }) {
    try {
      logger.i('開始監聽用戶資料變更');

      Query query = _firestore.collection(_collection);

      if (orderBy != null) {
        query = query.orderBy(orderBy, descending: descending);
      }

      if (limit != null) {
        query = query.limit(limit);
      }

      return query.snapshots().map((snapshot) {
        return snapshot.docs.map((doc) {
          final data = doc.data() as Map<String, dynamic>;
          return UserProfile.fromJson(data);
        }).toList();
      });
    } catch (e) {
      logger.e('監聽用戶資料失敗: $e');
      rethrow;
    }
  }

  /// 從 AppUser 轉換為 Firestore 資料
  static Map<String, dynamic> fromAppUser(AppUser user) {
    return {
      'email': user.email,
      'displayName': user.displayName,
      'photoURL': user.photoURL,
      'emailVerified': user.emailVerified,
      'isAnonymous': user.isAnonymous,
      'isAdmin': user.isAdmin,
      'createdAt': user.createdAt != null 
          ? Timestamp.fromDate(user.createdAt!) 
          : FieldValue.serverTimestamp(),
      'lastSignInAt': user.lastSignInAt != null 
          ? Timestamp.fromDate(user.lastSignInAt!) 
          : null,
      'updatedAt': FieldValue.serverTimestamp(),
    };
  }

  /// 從 Firestore 資料轉換為 AppUser
  static AppUser toAppUser(Map<String, dynamic> data) {
    return AppUser(
      uid: data['id'] as String,
      email: data['email'] as String?,
      displayName: data['displayName'] as String?,
      photoURL: data['photoURL'] as String?,
      emailVerified: data['emailVerified'] as bool? ?? false,
      isAnonymous: data['isAnonymous'] as bool? ?? false,
      isAdmin: data['isAdmin'] as bool? ?? false,
      createdAt: (data['createdAt'] as Timestamp?)?.toDate(),
      lastSignInAt: (data['lastSignInAt'] as Timestamp?)?.toDate(),
    );
  }
}
