import 'dart:async';

import 'package:in_app_purchase/in_app_purchase.dart';

import '../../../core/utils/logger_utils.dart';
import 'auth_service.dart';
import 'remote_config_service.dart';

/// IAP (In-App Purchase) 服務
class IAPService {
  static final InAppPurchase _inAppPurchase = InAppPurchase.instance;
  static late StreamSubscription<List<PurchaseDetails>> _subscription;
  static bool _isAvailable = false;
  static List<ProductDetails> _products = [];
  static Map<String, dynamic> _productConfig = {};

  /// 初始化 IAP 服務
  static Future<bool> initialize() async {
    try {
      // 檢查 IAP 是否可用
      _isAvailable = await _inAppPurchase.isAvailable();
      
      if (!_isAvailable) {
        logger.w('IAP 服務不可用');
        return false;
      }

      // 載入商品配置
      await _loadProductConfig();

      // 設置購買監聽器
      _subscription = _inAppPurchase.purchaseStream.listen(
        _onPurchaseUpdate,
        onDone: () => _subscription.cancel(),
        onError: (error) => logger.e('IAP 購買流監聽錯誤: $error'),
      );

      // 載入商品資訊
      await _loadProducts();

      // 恢復購買
      await _restorePurchases();

      logger.i('IAP 服務初始化成功');
      return true;
    } catch (e) {
      logger.e('IAP 服務初始化失敗: $e');
      return false;
    }
  }

  /// 載入商品配置
  static Future<void> _loadProductConfig() async {
    try {
      _productConfig = await RemoteConfigService.getIAPProductConfig();
      logger.i('已載入 IAP 商品配置: ${_productConfig.keys}');
    } catch (e) {
      logger.e('載入 IAP 商品配置失敗: $e');
    }
  }

  /// 載入商品資訊
  static Future<void> _loadProducts() async {
    try {
      final productIds = _getProductIds();
      
      if (productIds.isEmpty) {
        logger.w('沒有可載入的商品 ID');
        return;
      }

      final ProductDetailsResponse response = await _inAppPurchase.queryProductDetails(productIds.toSet());
      
      if (response.error != null) {
        logger.e('查詢商品詳情失敗: ${response.error}');
        return;
      }

      _products = response.productDetails;
      logger.i('已載入 ${_products.length} 個商品');
      
      for (final product in _products) {
        logger.d('商品: ${product.id} - ${product.title} - ${product.price}');
      }
    } catch (e) {
      logger.e('載入商品資訊失敗: $e');
    }
  }

  /// 獲取商品 ID 列表
  static List<String> _getProductIds() {
    final products = _productConfig['products'] as Map<String, dynamic>? ?? {};
    return products.keys.toList();
  }

  /// 恢復購買
  static Future<void> _restorePurchases() async {
    try {
      await _inAppPurchase.restorePurchases();
      logger.i('已恢復購買記錄');
    } catch (e) {
      logger.e('恢復購買失敗: $e');
    }
  }

  /// 購買商品
  static Future<bool> purchaseProduct(String productId) async {
    try {
      final product = _products.firstWhere(
        (p) => p.id == productId,
        orElse: () => throw Exception('找不到商品: $productId'),
      );

      final PurchaseParam purchaseParam = PurchaseParam(productDetails: product);
      
      final bool success = await _inAppPurchase.buyConsumable(
        purchaseParam: purchaseParam,
      );

      if (success) {
        logger.i('開始購買商品: $productId');
      } else {
        logger.w('購買商品失敗: $productId');
      }

      return success;
    } catch (e) {
      logger.e('購買商品時出錯: $e');
      return false;
    }
  }

  /// 購買訂閱
  static Future<bool> purchaseSubscription(String productId) async {
    try {
      final product = _products.firstWhere(
        (p) => p.id == productId,
        orElse: () => throw Exception('找不到訂閱商品: $productId'),
      );

      final PurchaseParam purchaseParam = PurchaseParam(productDetails: product);
      
      final bool success = await _inAppPurchase.buyNonConsumable(
        purchaseParam: purchaseParam,
      );

      if (success) {
        logger.i('開始購買訂閱: $productId');
      } else {
        logger.w('購買訂閱失敗: $productId');
      }

      return success;
    } catch (e) {
      logger.e('購買訂閱時出錯: $e');
      return false;
    }
  }

  /// 處理購買更新
  static void _onPurchaseUpdate(List<PurchaseDetails> purchaseDetailsList) {
    for (final PurchaseDetails purchaseDetails in purchaseDetailsList) {
      _handlePurchase(purchaseDetails);
    }
  }

  /// 處理單個購買
  static Future<void> _handlePurchase(PurchaseDetails purchaseDetails) async {
    try {
      logger.i('處理購買: ${purchaseDetails.productID} - ${purchaseDetails.status}');

      if (purchaseDetails.status == PurchaseStatus.purchased) {
        // 購買成功，驗證並記錄
        await _verifyAndRecordPurchase(purchaseDetails);
      } else if (purchaseDetails.status == PurchaseStatus.error) {
        // 購買失敗
        logger.e('購買失敗: ${purchaseDetails.error}');
      } else if (purchaseDetails.status == PurchaseStatus.canceled) {
        // 購買取消
        logger.i('用戶取消購買: ${purchaseDetails.productID}');
      }

      // 完成購買（對於消耗性商品）
      if (purchaseDetails.pendingCompletePurchase) {
        await _inAppPurchase.completePurchase(purchaseDetails);
      }
    } catch (e) {
      logger.e('處理購買時出錯: $e');
    }
  }

  /// 驗證並記錄購買
  static Future<void> _verifyAndRecordPurchase(PurchaseDetails purchaseDetails) async {
    try {
      // 驗證購買憑證
      final isValid = await _verifyPurchase(purchaseDetails);
      
      if (!isValid) {
        logger.e('購買憑證驗證失敗: ${purchaseDetails.productID}');
        return;
      }

      // 記錄購買到 Firebase
      await _recordPurchaseToFirebase(purchaseDetails);

      logger.i('購買驗證並記錄成功: ${purchaseDetails.productID}');
    } catch (e) {
      logger.e('驗證並記錄購買時出錯: $e');
    }
  }

  /// 驗證購買憑證
  static Future<bool> _verifyPurchase(PurchaseDetails purchaseDetails) async {
    try {
      // 這裡應該實現服務端驗證邏輯
      // 暫時返回 true，實際應用中需要與後端 API 驗證
      logger.i('驗證購買憑證: ${purchaseDetails.productID}');
      return true;
    } catch (e) {
      logger.e('驗證購買憑證失敗: $e');
      return false;
    }
  }

  /// 記錄購買到 Firebase
  static Future<void> _recordPurchaseToFirebase(PurchaseDetails purchaseDetails) async {
    try {
      final currentUser = AuthService.getCurrentUser();
      if (currentUser == null) {
        logger.w('用戶未登入，無法記錄購買');
        return;
      }

      logger.i('處理購買記錄: ${purchaseDetails.productID}');

      // TODO: 實現 Firebase 購買記錄功能
      // final isSubscription = config['type'] == 'subscription';
      // final expiryDate = isSubscription
      //     ? _calculateExpiryDate(config['period'])
      //     : DateTime.now().add(const Duration(days: 365));
      //
      // final paymentRecord = PaymentRecord(...);
      // await FirebasePaymentService.recordPayment(paymentRecord);

      logger.i('已處理購買: ${purchaseDetails.productID}');
    } catch (e) {
      logger.e('記錄購買到 Firebase 失敗: $e');
    }
  }

  /// 獲取商品配置
  static Map<String, dynamic> _getProductConfig(String productId) {
    final products = _productConfig['products'] as Map<String, dynamic>? ?? {};
    return products[productId] as Map<String, dynamic>? ?? {};
  }

  /// 獲取可用商品列表
  static List<ProductDetails> getAvailableProducts() {
    return _products;
  }

  /// 檢查 IAP 是否可用
  static bool isAvailable() {
    return _isAvailable;
  }

  /// 清理資源
  static void dispose() {
    _subscription.cancel();
  }
}
