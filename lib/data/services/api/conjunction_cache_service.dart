import 'dart:convert';

import 'package:shared_preferences/shared_preferences.dart';

import '../../../core/utils/logger_utils.dart';
import '../../models/astrology/planetary_conjunction.dart';
/// 行星會合緩存服務
/// 負責保存和載入行星會合分析結果，避免重複計算
class ConjunctionCacheService {
  static const String _cacheKeyPrefix = 'conjunction_cache_';
  static const String _cacheIndexKey = 'conjunction_cache_index';
  static const int _maxCacheEntries = 50; // 最多保存50個緩存條目
  static const int _cacheValidDays = 30; // 緩存有效期30天

  /// 生成緩存鍵
  static String _generateCacheKey({
    required double latitude,
    required double longitude,
    required DateTime startDate,
    required DateTime endDate,
  }) {
    // 使用位置和時間範圍生成唯一鍵
    final locationKey = '${latitude.toStringAsFixed(2)}_${longitude.toStringAsFixed(2)}';
    final timeKey = '${startDate.millisecondsSinceEpoch}_${endDate.millisecondsSinceEpoch}';
    return '${_cacheKeyPrefix}${locationKey}_$timeKey';
  }

  /// 保存會合分析結果到緩存
  static Future<void> saveConjunctionResult({
    required double latitude,
    required double longitude,
    required DateTime startDate,
    required DateTime endDate,
    required ConjunctionAnalysisResult result,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = _generateCacheKey(
        latitude: latitude,
        longitude: longitude,
        startDate: startDate,
        endDate: endDate,
      );

      // 創建緩存條目
      final cacheEntry = ConjunctionCacheEntry(
        cacheKey: cacheKey,
        latitude: latitude,
        longitude: longitude,
        startDate: startDate,
        endDate: endDate,
        result: result,
        cachedAt: DateTime.now(),
      );

      // 保存緩存條目
      final cacheJson = jsonEncode(cacheEntry.toJson());
      await prefs.setString(cacheKey, cacheJson);

      // 更新緩存索引
      await _updateCacheIndex(cacheKey);

      logger.d('會合分析結果已緩存: $cacheKey');
    } catch (e) {
      logger.d('保存會合緩存失敗: $e');
    }
  }

  /// 從緩存載入會合分析結果
  static Future<ConjunctionAnalysisResult?> loadConjunctionResult({
    required double latitude,
    required double longitude,
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = _generateCacheKey(
        latitude: latitude,
        longitude: longitude,
        startDate: startDate,
        endDate: endDate,
      );

      final cacheJson = prefs.getString(cacheKey);
      if (cacheJson == null) {
        logger.d('未找到緩存: $cacheKey');
        return null;
      }

      final cacheEntry = ConjunctionCacheEntry.fromJson(jsonDecode(cacheJson));

      // 檢查緩存是否過期
      final daysSinceCached = DateTime.now().difference(cacheEntry.cachedAt).inDays;
      if (daysSinceCached > _cacheValidDays) {
        logger.d('緩存已過期: $cacheKey (${daysSinceCached}天前)');
        await _removeCacheEntry(cacheKey);
        return null;
      }

      logger.d('從緩存載入會合分析結果: $cacheKey');
      return cacheEntry.result;
    } catch (e) {
      logger.d('載入會合緩存失敗: $e');
      return null;
    }
  }

  /// 更新緩存索引
  static Future<void> _updateCacheIndex(String cacheKey) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final indexJson = prefs.getString(_cacheIndexKey);
      
      List<String> cacheIndex = [];
      if (indexJson != null) {
        cacheIndex = List<String>.from(jsonDecode(indexJson));
      }

      // 添加新的緩存鍵（如果不存在）
      if (!cacheIndex.contains(cacheKey)) {
        cacheIndex.add(cacheKey);
      }

      // 如果超過最大條目數，移除最舊的條目
      while (cacheIndex.length > _maxCacheEntries) {
        final oldestKey = cacheIndex.removeAt(0);
        await prefs.remove(oldestKey);
        logger.d('移除過舊緩存: $oldestKey');
      }

      // 保存更新的索引
      await prefs.setString(_cacheIndexKey, jsonEncode(cacheIndex));
    } catch (e) {
      logger.d('更新緩存索引失敗: $e');
    }
  }

  /// 移除緩存條目
  static Future<void> _removeCacheEntry(String cacheKey) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(cacheKey);

      // 從索引中移除
      final indexJson = prefs.getString(_cacheIndexKey);
      if (indexJson != null) {
        final cacheIndex = List<String>.from(jsonDecode(indexJson));
        cacheIndex.remove(cacheKey);
        await prefs.setString(_cacheIndexKey, jsonEncode(cacheIndex));
      }
    } catch (e) {
      logger.d('移除緩存條目失敗: $e');
    }
  }

  /// 清理過期緩存
  static Future<void> cleanExpiredCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final indexJson = prefs.getString(_cacheIndexKey);
      
      if (indexJson == null) return;

      final cacheIndex = List<String>.from(jsonDecode(indexJson));
      final validCacheKeys = <String>[];

      for (final cacheKey in cacheIndex) {
        final cacheJson = prefs.getString(cacheKey);
        if (cacheJson != null) {
          try {
            final cacheEntry = ConjunctionCacheEntry.fromJson(jsonDecode(cacheJson));
            final daysSinceCached = DateTime.now().difference(cacheEntry.cachedAt).inDays;
            
            if (daysSinceCached <= _cacheValidDays) {
              validCacheKeys.add(cacheKey);
            } else {
              await prefs.remove(cacheKey);
              logger.d('清理過期緩存: $cacheKey');
            }
          } catch (e) {
            // 如果解析失敗，也移除這個緩存
            await prefs.remove(cacheKey);
            logger.d('清理無效緩存: $cacheKey');
          }
        }
      }

      // 更新索引
      await prefs.setString(_cacheIndexKey, jsonEncode(validCacheKeys));
      logger.d('緩存清理完成，保留 ${validCacheKeys.length} 個有效緩存');
    } catch (e) {
      logger.d('清理緩存失敗: $e');
    }
  }

  /// 獲取緩存統計信息
  static Future<ConjunctionCacheStats> getCacheStats() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final indexJson = prefs.getString(_cacheIndexKey);
      
      if (indexJson == null) {
        return ConjunctionCacheStats(totalEntries: 0, validEntries: 0, expiredEntries: 0);
      }

      final cacheIndex = List<String>.from(jsonDecode(indexJson));
      int validEntries = 0;
      int expiredEntries = 0;

      for (final cacheKey in cacheIndex) {
        final cacheJson = prefs.getString(cacheKey);
        if (cacheJson != null) {
          try {
            final cacheEntry = ConjunctionCacheEntry.fromJson(jsonDecode(cacheJson));
            final daysSinceCached = DateTime.now().difference(cacheEntry.cachedAt).inDays;
            
            if (daysSinceCached <= _cacheValidDays) {
              validEntries++;
            } else {
              expiredEntries++;
            }
          } catch (e) {
            expiredEntries++;
          }
        }
      }

      return ConjunctionCacheStats(
        totalEntries: cacheIndex.length,
        validEntries: validEntries,
        expiredEntries: expiredEntries,
      );
    } catch (e) {
      logger.d('獲取緩存統計失敗: $e');
      return ConjunctionCacheStats(totalEntries: 0, validEntries: 0, expiredEntries: 0);
    }
  }

  /// 清除所有緩存
  static Future<void> clearAllCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final indexJson = prefs.getString(_cacheIndexKey);
      
      if (indexJson != null) {
        final cacheIndex = List<String>.from(jsonDecode(indexJson));
        for (final cacheKey in cacheIndex) {
          await prefs.remove(cacheKey);
        }
      }

      await prefs.remove(_cacheIndexKey);
      logger.d('所有會合緩存已清除');
    } catch (e) {
      logger.d('清除緩存失敗: $e');
    }
  }
}

/// 會合緩存條目
class ConjunctionCacheEntry {
  final String cacheKey;
  final double latitude;
  final double longitude;
  final DateTime startDate;
  final DateTime endDate;
  final ConjunctionAnalysisResult result;
  final DateTime cachedAt;

  const ConjunctionCacheEntry({
    required this.cacheKey,
    required this.latitude,
    required this.longitude,
    required this.startDate,
    required this.endDate,
    required this.result,
    required this.cachedAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'cacheKey': cacheKey,
      'latitude': latitude,
      'longitude': longitude,
      'startDate': startDate.toIso8601String(),
      'endDate': endDate.toIso8601String(),
      'result': {
        'historicalConjunctions': result.historicalConjunctions.map((c) => c.toJson()).toList(),
        'futureConjunctions': result.futureConjunctions.map((c) => c.toJson()).toList(),
        'analysisSummary': result.analysisSummary,
        'investmentSummary': result.investmentSummary,
      },
      'cachedAt': cachedAt.toIso8601String(),
    };
  }

  factory ConjunctionCacheEntry.fromJson(Map<String, dynamic> json) {
    final resultJson = json['result'] as Map<String, dynamic>;
    
    final historicalConjunctions = (resultJson['historicalConjunctions'] as List)
        .map((c) => PlanetaryConjunction.fromJson(c))
        .toList();
    
    final futureConjunctions = (resultJson['futureConjunctions'] as List)
        .map((c) => PlanetaryConjunction.fromJson(c))
        .toList();

    final result = ConjunctionAnalysisResult(
      historicalConjunctions: historicalConjunctions,
      futureConjunctions: futureConjunctions,
      analysisSummary: resultJson['analysisSummary'],
      investmentSummary: resultJson['investmentSummary'],
    );

    return ConjunctionCacheEntry(
      cacheKey: json['cacheKey'],
      latitude: json['latitude'],
      longitude: json['longitude'],
      startDate: DateTime.parse(json['startDate']),
      endDate: DateTime.parse(json['endDate']),
      result: result,
      cachedAt: DateTime.parse(json['cachedAt']),
    );
  }
}

/// 緩存統計信息
class ConjunctionCacheStats {
  final int totalEntries;
  final int validEntries;
  final int expiredEntries;

  const ConjunctionCacheStats({
    required this.totalEntries,
    required this.validEntries,
    required this.expiredEntries,
  });
}
