import '../../../core/constants/astrology_constants.dart';
import '../../models/astrology/chart_data.dart';
import '../../models/astrology/planet_position.dart';
import '../../models/interpretation/chart_theme_analysis_result.dart';
/// 星盤主題分析服務類
/// 
/// 提供各種主題分析的具體實現方法
class ChartThemeAnalysisService {
  final ChartData chartData;

  ChartThemeAnalysisService(this.chartData);

  // ==================== 一、命盤核心架構分析方法 ====================

  /// 分析太陽、月亮、上升三大要素
  Future<ThemeAnalysisSection> analyzeSunMoonAscendant() async {
    final content = StringBuffer();
    final relatedPlanets = <String>[];
    final relatedHouses = <int>[];
    final relatedAspects = <String>[];

    // 找到太陽、月亮位置
    final sun = _findPlanet('太陽');
    final moon = _findPlanet('月亮');
    
    if (sun != null) {
      relatedPlanets.add('太陽');
      relatedHouses.add(sun.house);
      content.writeln('太陽位於${sun.sign}座第${sun.house}宮，代表核心自我與生命目標。');
      content.writeln('${sun.sign}座的太陽展現出${_getSunSignTraits(sun.sign)}的特質。');
    }

    if (moon != null) {
      relatedPlanets.add('月亮');
      relatedHouses.add(moon.house);
      content.writeln('月亮位於${moon.sign}座第${moon.house}宮，反映情感需求與內在安全感。');
      content.writeln('${moon.sign}座的月亮需要${_getMoonSignNeeds(moon.sign)}。');
    }

    // 分析上升星座（從第一宮推斷）
    if (chartData.houses != null && chartData.houses!.cusps.isNotEmpty) {
      final ascendantSign = _getZodiacSign(chartData.houses!.cusps[0]);
      content.writeln('上升星座為${ascendantSign}座，展現出${_getAscendantTraits(ascendantSign)}的外在形象。');
    }

    return ThemeAnalysisSection(
      title: '太陽、月亮、上升三大要素解析',
      content: content.toString(),
      relatedPlanets: relatedPlanets,
      relatedHouses: relatedHouses,
      relatedAspects: relatedAspects,
      importance: 5,
    );
  }

  /// 分析宮主星的落點與相位
  Future<ThemeAnalysisSection> analyzeHouseRulers() async {
    final content = StringBuffer();
    final relatedPlanets = <String>[];
    final relatedHouses = <int>[];
    final relatedAspects = <String>[];

    content.writeln('宮主星分析顯示天賦的分布與表現方式：');

    // 分析重要宮位的宮主星
    final importantHouses = [1, 4, 7, 10]; // 角宮
    for (final houseNumber in importantHouses) {
      final ruler = _getHouseRuler(houseNumber);
      if (ruler != null) {
        relatedPlanets.add(ruler.name);
        relatedHouses.add(ruler.house);
        content.writeln('第${houseNumber}宮宮主星${ruler.name}位於${ruler.sign}座第${ruler.house}宮，');
        content.writeln('${_getHouseRulerMeaning(houseNumber, ruler)}');
      }
    }

    return ThemeAnalysisSection(
      title: '宮主星的落點與相位分析',
      content: content.toString(),
      relatedPlanets: relatedPlanets,
      relatedHouses: relatedHouses,
      relatedAspects: relatedAspects,
      importance: 4,
    );
  }

  /// 分析重點合相
  Future<ThemeAnalysisSection> analyzeKeyConjunctions() async {
    final content = StringBuffer();
    final relatedPlanets = <String>[];
    final relatedHouses = <int>[];
    final relatedAspects = <String>[];

    if (chartData.aspects != null) {
      final conjunctions = chartData.aspects!
          .where((aspect) => aspect.aspect == '合相' || aspect.shortZh == '合')
          .toList();

      content.writeln('重點合相分析：');

      for (final conjunction in conjunctions) {
        relatedPlanets.addAll([conjunction.planet1.name, conjunction.planet2.name]);
        relatedAspects.add('${conjunction.planet1.name}合${conjunction.planet2.name}');

        content.writeln('${conjunction.planet1.name}合${conjunction.planet2.name}：');
        content.writeln('${_getConjunctionMeaning(conjunction.planet1.name, conjunction.planet2.name)}');
      }

      if (conjunctions.isEmpty) {
        content.writeln('本命盤中沒有特別突出的緊密合相。');
      }
    }

    return ThemeAnalysisSection(
      title: '重點合相分析',
      content: content.toString(),
      relatedPlanets: relatedPlanets,
      relatedHouses: relatedHouses,
      relatedAspects: relatedAspects,
      importance: 4,
    );
  }

  /// 分析四元素比例與三分性
  Future<ThemeAnalysisSection> analyzeElementsAndQualities() async {
    final content = StringBuffer();
    final relatedPlanets = <String>[];
    final relatedHouses = <int>[];
    final relatedAspects = <String>[];

    // 計算四元素分布
    final elementCounts = <String, int>{
      '火': 0, '土': 0, '風': 0, '水': 0,
    };

    // 計算三分性分布
    final qualityCounts = <String, int>{
      '基本': 0, '固定': 0, '變動': 0,
    };

    if (chartData.planets != null) {
      for (final planet in chartData.planets!) {
        relatedPlanets.add(planet.name);
        final element = _getSignElement(planet.sign);
        final quality = _getSignQuality(planet.sign);
        
        elementCounts[element] = (elementCounts[element] ?? 0) + 1;
        qualityCounts[quality] = (qualityCounts[quality] ?? 0) + 1;
      }
    }

    content.writeln('四元素分布：');
    elementCounts.forEach((element, count) {
      content.writeln('$element元素：$count個行星 ${_getElementMeaning(element, count)}');
    });

    content.writeln('\n三分性分布：');
    qualityCounts.forEach((quality, count) {
      content.writeln('$quality宮性：$count個行星 ${_getQualityMeaning(quality, count)}');
    });

    return ThemeAnalysisSection(
      title: '四元素比例與三分性分析',
      content: content.toString(),
      relatedPlanets: relatedPlanets,
      relatedHouses: relatedHouses,
      relatedAspects: relatedAspects,
      importance: 3,
    );
  }

  /// 分析行星集群
  Future<ThemeAnalysisSection> analyzePlanetaryStelliums() async {
    final content = StringBuffer();
    final relatedPlanets = <String>[];
    final relatedHouses = <int>[];
    final relatedAspects = <String>[];

    // 找出行星集群（3個或以上行星在同一宮位或星座）
    final houseGroups = <int, List<PlanetPosition>>{};
    final signGroups = <String, List<PlanetPosition>>{};

    if (chartData.planets != null) {
      for (final planet in chartData.planets!) {
        // 按宮位分組
        houseGroups.putIfAbsent(planet.house, () => []).add(planet);
        // 按星座分組
        signGroups.putIfAbsent(planet.sign, () => []).add(planet);
      }
    }

    content.writeln('行星集群分析：');

    // 分析宮位集群
    houseGroups.forEach((house, planets) {
      if (planets.length >= 3) {
        relatedHouses.add(house);
        relatedPlanets.addAll(planets.map((p) => p.name).toList());
        content.writeln('第${house}宮集群：${planets.map((p) => p.name).join('、')}');
        content.writeln('${_getHouseStelliumMeaning(house, planets)}');
      }
    });

    // 分析星座集群
    signGroups.forEach((sign, planets) {
      if (planets.length >= 3) {
        relatedPlanets.addAll(planets.map((p) => p.name).toList());
        content.writeln('${sign}座集群：${planets.map((p) => p.name).join('、')}');
        content.writeln('${_getSignStelliumMeaning(sign, planets)}');
      }
    });

    if (relatedPlanets.isEmpty) {
      content.writeln('本命盤中沒有明顯的行星集群現象。');
    }

    return ThemeAnalysisSection(
      title: '行星集群分析',
      content: content.toString(),
      relatedPlanets: relatedPlanets,
      relatedHouses: relatedHouses,
      relatedAspects: relatedAspects,
      importance: 3,
    );
  }

  // ==================== 輔助方法 ====================

  /// 根據名稱找到行星
  PlanetPosition? _findPlanet(String name) {
    if (chartData.planets == null) return null;
    try {
      return chartData.planets!.firstWhere((planet) => planet.name == name);
    } catch (e) {
      return null;
    }
  }

  /// 獲取星座的元素
  String _getSignElement(String sign) {
    const fireSigns = ['牡羊座', '獅子座', '射手座'];
    const earthSigns = ['金牛座', '處女座', '摩羯座'];
    const airSigns = ['雙子座', '天秤座', '水瓶座'];
    const waterSigns = ['巨蟹座', '天蠍座', '雙魚座'];

    if (fireSigns.contains(sign)) return '火';
    if (earthSigns.contains(sign)) return '土';
    if (airSigns.contains(sign)) return '風';
    if (waterSigns.contains(sign)) return '水';
    return '未知';
  }

  /// 獲取星座的性質
  String _getSignQuality(String sign) {
    const cardinalSigns = ['牡羊座', '巨蟹座', '天秤座', '摩羯座'];
    const fixedSigns = ['金牛座', '獅子座', '天蠍座', '水瓶座'];
    const mutableSigns = ['雙子座', '處女座', '射手座', '雙魚座'];

    if (cardinalSigns.contains(sign)) return '基本';
    if (fixedSigns.contains(sign)) return '固定';
    if (mutableSigns.contains(sign)) return '變動';
    return '未知';
  }

  /// 根據經度獲取星座
  String _getZodiacSign(double longitude) {
    final signs = AstrologyConstants.ZODIAC_SIGNS;
    final signIndex = (longitude / 30).floor();
    return signs[signIndex % 12];
  }

  /// 獲取宮主星
  PlanetPosition? _getHouseRuler(int houseNumber) {
    // 這裡需要實現宮主星的計算邏輯
    // 暫時返回 null，實際實現需要根據宮位星座和行星守護關係
    return null;
  }

  // 以下是各種解釋方法的佔位符，實際實現時需要添加詳細的占星學解釋
  String _getSunSignTraits(String sign) => '${sign}的核心特質';
  String _getMoonSignNeeds(String sign) => '${sign}的情感需求';
  String _getAscendantTraits(String sign) => '${sign}的外在表現';
  String _getHouseRulerMeaning(int house, PlanetPosition ruler) => '宮主星的意義';
  String _getConjunctionMeaning(String planet1, String planet2) => '合相的意義';
  String _getElementMeaning(String element, int count) => '元素的意義';
  String _getQualityMeaning(String quality, int count) => '性質的意義';
  String _getHouseStelliumMeaning(int house, List<PlanetPosition> planets) => '宮位集群的意義';
  String _getSignStelliumMeaning(String sign, List<PlanetPosition> planets) => '星座集群的意義';
}
