import 'dart:convert';

import 'package:shared_preferences/shared_preferences.dart';

import '../../models/user/learning_progress.dart';

/// 學習進度服務
class LearningProgressService {
  static const String _progressKey = 'learning_progress';

  /// 獲取學習進度
  static Future<LearningProgress> getProgress() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final progressJson = prefs.getString(_progressKey);
      
      if (progressJson != null) {
        final progressData = jsonDecode(progressJson);
        return LearningProgress.fromJson(progressData);
      } else {
        // 創建初始進度
        final initialProgress = LearningProgress.initial('default_user');
        await saveProgress(initialProgress);
        return initialProgress;
      }
    } catch (e) {
      // 如果出錯，返回初始進度
      return LearningProgress.initial('default_user');
    }
  }

  /// 保存學習進度
  static Future<void> saveProgress(LearningProgress progress) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final progressJson = jsonEncode(progress.toJson());
      await prefs.setString(_progressKey, progressJson);
    } catch (e) {
      throw Exception('保存學習進度失敗: $e');
    }
  }

  /// 完成課程
  static Future<LearningProgress> completeLesson(String lessonId, int points) async {
    final currentProgress = await getProgress();
    final updatedProgress = currentProgress.completeLesson(lessonId, points);
    await saveProgress(updatedProgress);
    return updatedProgress;
  }

  /// 解鎖功能
  static Future<void> unlockFeature(String feature) async {
    final currentProgress = await getProgress();
    if (!currentProgress.unlockedFeatures.contains(feature)) {
      final newUnlockedFeatures = List<String>.from(currentProgress.unlockedFeatures);
      newUnlockedFeatures.add(feature);
      
      final updatedProgress = LearningProgress(
        userId: currentProgress.userId,
        completedLessons: currentProgress.completedLessons,
        lessonCompletionDates: currentProgress.lessonCompletionDates,
        totalScore: currentProgress.totalScore,
        currentLevel: currentProgress.currentLevel,
        unlockedFeatures: newUnlockedFeatures,
        lastUpdated: DateTime.now(),
      );
      
      await saveProgress(updatedProgress);
    }
  }

  /// 重置進度
  static Future<void> resetProgress() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_progressKey);
  }

  /// 獲取學習統計
  static Future<Map<String, dynamic>> getLearningStats() async {
    final progress = await getProgress();
    final completedCount = progress.completedLessons.values.where((completed) => completed).length;
    const totalLessons = 20; // 假設總共有20個課程
    
    return {
      'completedLessons': completedCount,
      'totalLessons': totalLessons,
      'progressPercentage': progress.progressPercentage,
      'currentLevel': progress.currentLevel,
      'totalScore': progress.totalScore,
      'unlockedFeatures': progress.unlockedFeatures.length,
      'nextRecommendedLesson': progress.nextRecommendedLesson,
    };
  }

  /// 獲取學習建議
  static Future<List<String>> getLearningRecommendations() async {
    final progress = await getProgress();
    final recommendations = <String>[];

    // 根據進度提供建議
    if (progress.progressPercentage < 25) {
      recommendations.add('建議先完成基礎星座課程');
      recommendations.add('了解十二星座的基本特質');
    } else if (progress.progressPercentage < 50) {
      recommendations.add('學習行星的占星意義');
      recommendations.add('探索太陽、月亮、上升星座');
    } else if (progress.progressPercentage < 75) {
      recommendations.add('深入了解宮位系統');
      recommendations.add('學習行星相位的基本概念');
    } else {
      recommendations.add('練習完整的星盤解讀');
      recommendations.add('探索進階占星技巧');
    }

    return recommendations;
  }
}
