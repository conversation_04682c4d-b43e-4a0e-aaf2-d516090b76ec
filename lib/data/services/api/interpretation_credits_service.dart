import '../../../core/utils/logger_utils.dart';
import '../../models/payment/payment_record.dart';
import 'auth_service.dart';
import 'firebase_payment_service.dart';

/// 統一解讀次數服務
/// 統一管理所有解讀次數，不再區分免費試用和已購買次數
class InterpretationCreditsService {

  /// 獲取總可用解讀次數
  static Future<int> getTotalAvailableCredits() async {
    try {
      final currentUser = AuthService.getCurrentUser();
      if (currentUser == null) {
        logger.w('用戶未登入，返回0次數');
        return 0;
      }

      // 檢查是否為付費會員（無限次數）
      if (await _isPremiumUser()) {
        return -1; // -1 表示無限次數
      }

      // 獲取已購買剩餘次數
      final purchasedCredits = await _getRemainingPurchasedCredits();

      logger.i('總可用解讀次數: $purchasedCredits');

      return purchasedCredits;
    } catch (e) {
      logger.e('獲取總可用解讀次數時出錯: $e');
      return 0;
    }
  }

  /// 檢查是否有解讀權限
  static Future<bool> hasInterpretationPermission() async {
    try {
      final credits = await getTotalAvailableCredits();
      return credits == -1 || credits > 0; // -1 為無限次數，>0 為有剩餘次數
    } catch (e) {
      logger.e('檢查解讀權限時出錯: $e');
      return false;
    }
  }

  /// 使用一次解讀次數
  static Future<bool> useInterpretationCredit() async {
    try {
      final currentUser = AuthService.getCurrentUser();
      if (currentUser == null) {
        logger.w('用戶未登入，無法使用解讀次數');
        return false;
      }

      // 檢查是否為付費會員
      if (await _isPremiumUser()) {
        logger.i('付費會員使用解讀，無需扣除次數');
        return true;
      }

      // 使用已購買次數
      final purchasedCredits = await _getRemainingPurchasedCredits();
      if (purchasedCredits > 0) {
        final success = await _usePurchasedCredit();
        if (success) {
          logger.i('使用已購買解讀次數成功，剩餘次數: ${purchasedCredits - 1}');
          return true;
        }
      }

      logger.w('沒有可用的解讀次數');
      return false;
    } catch (e) {
      logger.e('使用解讀次數時出錯: $e');
      return false;
    }
  }

  /// 獲取解讀次數詳細資訊
  static Future<Map<String, dynamic>> getCreditsDetails() async {
    try {
      final currentUser = AuthService.getCurrentUser();
      if (currentUser == null) {
        return {
          'isPremium': false,
          'totalCredits': 0,
          'isLoggedIn': false,
        };
      }

      final isPremium = await _isPremiumUser();
      final totalCredits = await getTotalAvailableCredits();

      return {
        'isPremium': isPremium,
        'totalCredits': totalCredits,
        'isLoggedIn': true,
      };
    } catch (e) {
      logger.e('獲取解讀次數詳細資訊時出錯: $e');
      return {
        'isPremium': false,
        'totalCredits': 0,
        'isLoggedIn': false,
      };
    }
  }

  /// 新增已購買次數
  static Future<bool> addPurchasedCredits(int count, PaymentRecord paymentRecord) async {
    try {
      final currentUser = AuthService.getCurrentUser();
      if (currentUser == null) {
        logger.w('用戶未登入，無法新增已購買次數');
        return false;
      }

      // 保存購買記錄到 Firebase
      final success = await FirebasePaymentService.savePaymentRecord(paymentRecord);
      if (success) {
        logger.i('成功新增 $count 次已購買解讀次數');
        return true;
      } else {
        logger.e('保存購買記錄失敗');
        return false;
      }
    } catch (e) {
      logger.e('新增已購買次數時出錯: $e');
      return false;
    }
  }

  // ==================== 私有方法 ====================

  /// 檢查是否為付費會員
  static Future<bool> _isPremiumUser() async {
    try {
      final currentUser = AuthService.getCurrentUser();
      if (currentUser == null) return false;

      final payments = await FirebasePaymentService.getUserPaymentRecords();
      final validSubscriptions = payments.where((payment) =>
        payment.isValid &&
        payment.expiryDate.isAfter(DateTime.now()) &&
        _isSubscriptionPlan(payment.planType)
      ).toList();

      return validSubscriptions.isNotEmpty;
    } catch (e) {
      logger.e('檢查付費狀態時出錯: $e');
      return false;
    }
  }

  /// 判斷是否為訂閱方案
  static bool _isSubscriptionPlan(String planType) {
    const subscriptionPlans = ['monthly', 'yearly', 'quarterly'];
    return subscriptionPlans.contains(planType);
  }

  /// 獲取剩餘已購買次數
  static Future<int> _getRemainingPurchasedCredits() async {
    try {
      final currentUser = AuthService.getCurrentUser();
      if (currentUser == null) return 0;

      return await FirebasePaymentService.getRemainingSinglePurchases();
    } catch (e) {
      logger.e('獲取剩餘已購買次數時出錯: $e');
      return 0;
    }
  }

  /// 使用一次已購買次數
  static Future<bool> _usePurchasedCredit() async {
    try {
      return await FirebasePaymentService.useSinglePurchaseAttempt();
    } catch (e) {
      logger.e('使用已購買次數時出錯: $e');
      return false;
    }
  }
}
