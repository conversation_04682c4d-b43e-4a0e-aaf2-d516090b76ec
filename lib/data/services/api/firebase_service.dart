import 'package:flutter/foundation.dart';

import '../../../core/utils/logger_utils.dart';
import '../../models/payment/booking_model.dart';
import '../../models/user/feedback_model.dart';

/// Firebase 服務類，用於處理與 Firebase 的交互
class FirebaseService {
  // 使用 kDebugMode 來判斷是否為開發模式
  static bool get isDevelopmentMode => kDebugMode;

  static bool _initialized = false;

  /// 初始化 Firebase（僅用於 Auth）
  static Future<void> initialize() async {
    if (_initialized) return;

    try {
      // Firebase 已移除，服務已停用
      _initialized = true;
      logger.i('Firebase 服務已停用（已移除依賴）');
    } catch (e) {
      logger.e('Firebase 初始化失敗: $e');
      // 在生產模式下，如果初始化失敗，不要拋出異常
      // 這樣應用程序仍然可以運行，只是不使用 Firebase 功能
      _initialized = false;
    }
  }

  /// 保存預約到 Firestore
  static Future<bool> saveBooking(BookingModel booking) async {
    if (!_initialized) {
      try {
        await initialize();
      } catch (e) {
        logger.e('Firebase 初始化失敗，無法保存預約: $e');
        return false;
      }
    }

    try {
      // if (_firestore != null) {
      //   await _firestore!
      //       .collection('bookings')
      //       .doc(booking.id)
      //       .set(booking.toJson());
      //   logger.i('預約已保存到 Firestore: ${booking.id}');
      //   return true;
      // } else {
      //   logger.w('Firestore 實例為空，無法保存預約');
      //   return false;
      // }
      return false;
    } catch (e) {
      logger.e('保存預約到 Firestore 失敗: $e');
      return false;
    }
  }

  /// 獲取所有預約
  static Future<List<BookingModel>> getAllBookings() async {
    if (!_initialized) {
      try {
        await initialize();
      } catch (e) {
        logger.e('Firebase 初始化失敗，無法獲取預約: $e');
        return [];
      }
    }

    try {
      // 在開發模式下模擬獲取預約
      // if (isDevelopmentMode && _firestore == null) {
      //   logger.i('模擬從 Firestore 獲取預約');
      //   return [];
      // } else if (_firestore != null) {
      //   // 從 Firestore 獲取預約資料
      //   // final snapshot = await _firestore!.collection('bookings').get();
      //   // final bookings = snapshot.docs
      //   //     .map((doc) => BookingModel.fromJson(doc.data()))
      //   //     .toList();
      //   // logger.i('從 Firestore 獲取預約成功: ${bookings.length} 筆資料');
      //   // return bookings;
      // } else {
      //   logger.w('Firestore 實例為空，無法獲取預約');
      //   return [];
      // }
      return [];
    } catch (e) {
      logger.e('獲取預約失敗: $e');
      return [];
    }
  }

  /// 更新預約狀態
  static Future<bool> updateBookingStatus(
      String bookingId, bool isConfirmed) async {
    if (!_initialized) {
      try {
        await initialize();
      } catch (e) {
        logger.e('Firebase 初始化失敗，無法更新預約狀態: $e');
        return false;
      }
    }

    try {
      // 在開發模式下模擬更新預約狀態
      // if (isDevelopmentMode && _firestore == null) {
      //   logger.i('模擬更新預約狀態: $bookingId, isConfirmed: $isConfirmed');
      //   return true;
      // } else if (_firestore != null) {
      //   await _firestore!.collection('bookings').doc(bookingId).update({
      //     'isConfirmed': isConfirmed,
      //   });
      //   logger.i('預約狀態已更新: $bookingId, isConfirmed: $isConfirmed');
      //   return true;
      // } else {
      //   logger.w('Firestore 實例為空，無法更新預約狀態');
      //   return false;
      // }
    } catch (e) {
      logger.e('更新預約狀態失敗: $e');
      return false;
    }
    return false;
  }

  /// 刪除預約
  static Future<bool> deleteBooking(String bookingId) async {
    if (!_initialized) {
      try {
        await initialize();
      } catch (e) {
        logger.e('Firebase 初始化失敗，無法刪除預約: $e');
        return false;
      }
    }

    try {
      // 在開發模式下模擬刪除預約
      // if (isDevelopmentMode && _firestore == null) {
      //   logger.i('模擬刪除預約: $bookingId');
      //   return true;
      // } else if (_firestore != null) {
      //   await _firestore!.collection('bookings').doc(bookingId).delete();
      //   logger.i('預約已刪除: $bookingId');
      //   return true;
      // } else {
      //   logger.w('Firestore 實例為空，無法刪除預約');
      //   return false;
      // }
    } catch (e) {
      logger.e('刪除預約失敗: $e');
      return false;
    }
    return false;
  }

  /// 保存意見回饋到 Firestore
  static Future<bool> saveFeedback(FeedbackModel feedback) async {
    if (!_initialized) {
      try {
        await initialize();
      } catch (e) {
        logger.e('Firebase 初始化失敗，無法保存意見回饋: $e');
        return false;
      }
    }

    try {
      // 在開發模式下模擬保存意見回饋到 Firebase
      // if (isDevelopmentMode && _firestore == null) {
      //   logger.i('模擬保存意見回饋到 Firestore: ${feedback.id}');
      //   logger.i('意見回饋資料: ${feedback.toJson()}');
      //   return true;
      // } else if (_firestore != null) {
      //   await _firestore!.collection('feedbacks').doc(feedback.id).set(feedback.toJson());
      //   logger.i('意見回饋已保存到 Firestore: ${feedback.id}');
      //   return true;
      // } else {
      //   logger.w('Firestore 實例為空，無法保存意見回饋');
      //   return false;
      // }
    } catch (e) {
      logger.e('保存意見回饋到 Firestore 失敗: $e');
      return false;
    }
    return false;
  }
}
