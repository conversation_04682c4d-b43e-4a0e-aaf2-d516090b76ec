import '../../../core/utils/logger_utils.dart';
import '../../models/video/video_data.dart';
import 'remote_config_service.dart';

/// YouTube 影片管理服務
/// 負責從 Remote Config 獲取和管理 YouTube 影片資料
class YouTubeVideoService {
  static YouTubeVideosConfig? _cachedConfig;
  static DateTime? _lastFetchTime;
  static const Duration _cacheExpiration = Duration(minutes: 30);

  /// 獲取 YouTube 影片配置
  static Future<YouTubeVideosConfig> getVideosConfig() async {
    logger.d('=== 獲取 YouTube 影片配置 ===');

    // 檢查快取是否有效
    if (_cachedConfig != null && _lastFetchTime != null) {
      final timeSinceLastFetch = DateTime.now().difference(_lastFetchTime!);
      if (timeSinceLastFetch < _cacheExpiration) {
        logger.d('使用快取的 YouTube 影片配置');
        return _cachedConfig!;
      }
    }

    try {
      // 從 Remote Config 獲取配置
      final configData = RemoteConfigService.getYouTubeVideosConfig();
      
      if (configData.isEmpty) {
        logger.w('YouTube 影片配置為空，返回空配置');
        return _createEmptyConfig();
      }

      // 解析配置
      final config = YouTubeVideosConfig.fromJson(configData);
      
      // 更新快取
      _cachedConfig = config;
      _lastFetchTime = DateTime.now();
      
      logger.i('成功獲取 YouTube 影片配置，包含 ${config.categories.length} 個分類');
      return config;
    } catch (e) {
      logger.e('獲取 YouTube 影片配置失敗: $e');
      
      // 如果有快取，返回快取
      if (_cachedConfig != null) {
        logger.w('使用過期的快取配置');
        return _cachedConfig!;
      }
      
      // 返回空配置
      return _createEmptyConfig();
    }
  }

  /// 獲取所有影片分類
  static Future<List<VideoCategory>> getCategories() async {
    final config = await getVideosConfig();
    return config.categories;
  }

  /// 根據分類 ID 獲取分類
  static Future<VideoCategory?> getCategoryById(String categoryId) async {
    final config = await getVideosConfig();
    return config.getCategoryById(categoryId);
  }

  /// 獲取所有影片
  static Future<List<VideoData>> getAllVideos() async {
    final config = await getVideosConfig();
    return config.allVideos;
  }

  /// 獲取熱門影片
  static Future<List<VideoData>> getPopularVideos() async {
    final config = await getVideosConfig();
    return config.popularVideos;
  }

  /// 根據影片 ID 獲取影片
  static Future<VideoData?> getVideoById(String videoId) async {
    final config = await getVideosConfig();
    return config.getVideoById(videoId);
  }

  /// 搜尋影片
  static Future<List<VideoData>> searchVideos(String query) async {
    if (query.trim().isEmpty) {
      return await getAllVideos();
    }
    
    final config = await getVideosConfig();
    return config.searchVideos(query);
  }

  /// 根據標籤獲取影片
  static Future<List<VideoData>> getVideosByTag(String tag) async {
    final allVideos = await getAllVideos();
    return allVideos.where((video) => video.tags.contains(tag)).toList();
  }

  /// 獲取所有標籤
  static Future<List<String>> getAllTags() async {
    final allVideos = await getAllVideos();
    final tags = <String>{};
    
    for (final video in allVideos) {
      tags.addAll(video.tags);
    }
    
    return tags.toList()..sort();
  }

  /// 清除快取
  static void clearCache() {
    logger.d('清除 YouTube 影片配置快取');
    _cachedConfig = null;
    _lastFetchTime = null;
  }

  /// 手動刷新配置
  static Future<YouTubeVideosConfig> refreshConfig() async {
    logger.i('手動刷新 YouTube 影片配置');
    clearCache();
    
    // 嘗試刷新 Remote Config
    try {
      await RemoteConfigService.refresh();
    } catch (e) {
      logger.w('刷新 Remote Config 失敗: $e');
    }
    
    return await getVideosConfig();
  }

  /// 創建空配置
  static YouTubeVideosConfig _createEmptyConfig() {
    return YouTubeVideosConfig(
      categories: [],
      lastUpdated: DateTime.now(),
    );
  }

  /// 驗證影片 URL
  static bool isValidYouTubeUrl(String url) {
    final youtubeRegex = RegExp(
      r'^https?:\/\/(www\.)?(youtube\.com\/watch\?v=|youtu\.be\/)([a-zA-Z0-9_-]{11})',
      caseSensitive: false,
    );
    return youtubeRegex.hasMatch(url);
  }

  /// 從 URL 提取 YouTube 影片 ID
  static String? extractYouTubeId(String url) {
    final youtubeRegex = RegExp(
      r'(?:youtube\.com\/watch\?v=|youtu\.be\/)([a-zA-Z0-9_-]{11})',
      caseSensitive: false,
    );
    final match = youtubeRegex.firstMatch(url);
    return match?.group(1);
  }

  /// 生成 YouTube 縮圖 URL
  static String generateThumbnailUrl(String youtubeId, {String quality = 'maxresdefault'}) {
    return 'https://img.youtube.com/vi/$youtubeId/$quality.jpg';
  }

  /// 生成 YouTube 嵌入 URL
  static String generateEmbedUrl(String youtubeId, {Map<String, String>? parameters}) {
    var url = 'https://www.youtube.com/embed/$youtubeId';
    
    if (parameters != null && parameters.isNotEmpty) {
      final queryParams = parameters.entries
          .map((entry) => '${entry.key}=${entry.value}')
          .join('&');
      url += '?$queryParams';
    }
    
    return url;
  }

  /// 獲取統計資訊
  static Future<Map<String, dynamic>> getStatistics() async {
    final config = await getVideosConfig();
    final allVideos = config.allVideos;
    
    return {
      'totalCategories': config.categories.length,
      'totalVideos': allVideos.length,
      'popularVideos': config.popularVideos.length,
      'lastUpdated': config.lastUpdated.toIso8601String(),
      'cacheStatus': _cachedConfig != null ? 'cached' : 'not_cached',
      'lastFetchTime': _lastFetchTime?.toIso8601String(),
    };
  }

  /// 診斷配置狀態
  static Future<Map<String, dynamic>> diagnoseConfig() async {
    final diagnostics = <String, dynamic>{};
    
    try {
      // 檢查 Remote Config 狀態
      diagnostics['remoteConfigInitialized'] = RemoteConfigService.isInitialized;
      
      // 檢查快取狀態
      diagnostics['cacheExists'] = _cachedConfig != null;
      diagnostics['lastFetchTime'] = _lastFetchTime?.toIso8601String();
      
      // 嘗試獲取配置
      final config = await getVideosConfig();
      diagnostics['configLoadSuccess'] = true;
      diagnostics['categoriesCount'] = config.categories.length;
      diagnostics['totalVideos'] = config.allVideos.length;
      
      // 檢查配置內容
      if (config.categories.isNotEmpty) {
        final firstCategory = config.categories.first;
        diagnostics['firstCategoryId'] = firstCategory.id;
        diagnostics['firstCategoryVideoCount'] = firstCategory.videos.length;
      }
      
    } catch (e) {
      diagnostics['configLoadSuccess'] = false;
      diagnostics['error'] = e.toString();
    }
    
    return diagnostics;
  }
}
