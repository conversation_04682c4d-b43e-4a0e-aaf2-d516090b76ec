import 'package:shared_preferences/shared_preferences.dart';

import '../../../astreal.dart';

/// 隱藏公告管理服務
/// 負責管理用戶隱藏的系統公告
class HiddenAnnouncementService {
  static const String _hiddenAnnouncementsKey = 'hidden_system_announcements';
  
  /// 獲取已隱藏的公告 ID 列表
  static Future<Set<String>> getHiddenAnnouncementIds() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final hiddenIds = prefs.getStringList(_hiddenAnnouncementsKey) ?? [];
      return hiddenIds.toSet();
    } catch (e) {
      logger.e('獲取已隱藏公告列表失敗: $e');
      return <String>{};
    }
  }
  
  /// 隱藏公告
  static Future<bool> hideAnnouncement(String announcementId) async {
    try {
      final hiddenIds = await getHiddenAnnouncementIds();
      hiddenIds.add(announcementId);
      
      final prefs = await SharedPreferences.getInstance();
      await prefs.setStringList(_hiddenAnnouncementsKey, hiddenIds.toList());
      
      logger.i('已隱藏公告: $announcementId');
      return true;
    } catch (e) {
      logger.e('隱藏公告失敗: $e');
      return false;
    }
  }
  
  /// 取消隱藏公告
  static Future<bool> unhideAnnouncement(String announcementId) async {
    try {
      final hiddenIds = await getHiddenAnnouncementIds();
      hiddenIds.remove(announcementId);
      
      final prefs = await SharedPreferences.getInstance();
      await prefs.setStringList(_hiddenAnnouncementsKey, hiddenIds.toList());
      
      logger.i('已取消隱藏公告: $announcementId');
      return true;
    } catch (e) {
      logger.e('取消隱藏公告失敗: $e');
      return false;
    }
  }
  
  /// 還原所有已隱藏的公告
  static Future<bool> restoreAllHiddenAnnouncements() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_hiddenAnnouncementsKey);
      
      logger.i('已還原所有隱藏的公告');
      return true;
    } catch (e) {
      logger.e('還原所有隱藏公告失敗: $e');
      return false;
    }
  }
  
  /// 檢查公告是否已隱藏
  static Future<bool> isAnnouncementHidden(String announcementId) async {
    try {
      final hiddenIds = await getHiddenAnnouncementIds();
      return hiddenIds.contains(announcementId);
    } catch (e) {
      logger.e('檢查公告隱藏狀態失敗: $e');
      return false;
    }
  }
  
  /// 獲取隱藏公告的統計信息
  static Future<Map<String, dynamic>> getHiddenAnnouncementsStats() async {
    try {
      final hiddenIds = await getHiddenAnnouncementIds();
      final prefs = await SharedPreferences.getInstance();
      
      // 獲取最後更新時間
      final lastUpdateTimestamp = prefs.getInt('hidden_announcements_last_update') ?? 0;
      final lastUpdateTime = DateTime.fromMillisecondsSinceEpoch(lastUpdateTimestamp);
      
      return {
        'totalHidden': hiddenIds.length,
        'hiddenIds': hiddenIds.toList(),
        'lastUpdate': lastUpdateTime,
        'hasHiddenAnnouncements': hiddenIds.isNotEmpty,
      };
    } catch (e) {
      logger.e('獲取隱藏公告統計失敗: $e');
      return {
        'totalHidden': 0,
        'hiddenIds': <String>[],
        'lastUpdate': DateTime.now(),
        'hasHiddenAnnouncements': false,
      };
    }
  }
  
  /// 更新最後操作時間
  static Future<void> _updateLastOperationTime() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(
        'hidden_announcements_last_update',
        DateTime.now().millisecondsSinceEpoch,
      );
    } catch (e) {
      logger.e('更新最後操作時間失敗: $e');
    }
  }
  
  /// 批量隱藏公告
  static Future<bool> hideMultipleAnnouncements(List<String> announcementIds) async {
    try {
      final hiddenIds = await getHiddenAnnouncementIds();
      hiddenIds.addAll(announcementIds);
      
      final prefs = await SharedPreferences.getInstance();
      await prefs.setStringList(_hiddenAnnouncementsKey, hiddenIds.toList());
      await _updateLastOperationTime();
      
      logger.i('已批量隱藏 ${announcementIds.length} 個公告');
      return true;
    } catch (e) {
      logger.e('批量隱藏公告失敗: $e');
      return false;
    }
  }
  
  /// 批量取消隱藏公告
  static Future<bool> unhideMultipleAnnouncements(List<String> announcementIds) async {
    try {
      final hiddenIds = await getHiddenAnnouncementIds();
      for (final id in announcementIds) {
        hiddenIds.remove(id);
      }
      
      final prefs = await SharedPreferences.getInstance();
      await prefs.setStringList(_hiddenAnnouncementsKey, hiddenIds.toList());
      await _updateLastOperationTime();
      
      logger.i('已批量取消隱藏 ${announcementIds.length} 個公告');
      return true;
    } catch (e) {
      logger.e('批量取消隱藏公告失敗: $e');
      return false;
    }
  }
  
  /// 清理過期的隱藏記錄（可選功能）
  static Future<bool> cleanupExpiredHiddenRecords({
    Duration maxAge = const Duration(days: 365),
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastUpdateTimestamp = prefs.getInt('hidden_announcements_last_update') ?? 0;
      final lastUpdateTime = DateTime.fromMillisecondsSinceEpoch(lastUpdateTimestamp);
      
      // 如果記錄太舊，清理它們
      if (DateTime.now().difference(lastUpdateTime) > maxAge) {
        await prefs.remove(_hiddenAnnouncementsKey);
        await prefs.remove('hidden_announcements_last_update');
        
        logger.i('已清理過期的隱藏公告記錄');
        return true;
      }
      
      return false;
    } catch (e) {
      logger.e('清理過期隱藏記錄失敗: $e');
      return false;
    }
  }
  
  /// 導出隱藏公告設定（用於備份）
  static Future<Map<String, dynamic>> exportHiddenAnnouncementsSettings() async {
    try {
      final stats = await getHiddenAnnouncementsStats();
      return {
        'version': '1.0',
        'exportTime': DateTime.now().toIso8601String(),
        'hiddenAnnouncements': stats,
      };
    } catch (e) {
      logger.e('導出隱藏公告設定失敗: $e');
      return {};
    }
  }
  
  /// 導入隱藏公告設定（用於還原）
  static Future<bool> importHiddenAnnouncementsSettings(Map<String, dynamic> settings) async {
    try {
      final hiddenAnnouncements = settings['hiddenAnnouncements'] as Map<String, dynamic>?;
      if (hiddenAnnouncements == null) {
        return false;
      }
      
      final hiddenIds = (hiddenAnnouncements['hiddenIds'] as List<dynamic>?)
          ?.cast<String>() ?? [];
      
      final prefs = await SharedPreferences.getInstance();
      await prefs.setStringList(_hiddenAnnouncementsKey, hiddenIds);
      await _updateLastOperationTime();
      
      logger.i('已導入隱藏公告設定，包含 ${hiddenIds.length} 個隱藏公告');
      return true;
    } catch (e) {
      logger.e('導入隱藏公告設定失敗: $e');
      return false;
    }
  }
}
