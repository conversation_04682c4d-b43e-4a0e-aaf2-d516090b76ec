import 'package:flutter/material.dart';
import 'package:share_plus/share_plus.dart';

import '../../../presentation/themes/app_theme.dart';
import '../../models/astrology/chart_data.dart';
/// 解讀內容分享服務
/// 提供統一的分享功能，可用於各種解讀頁面
class InterpretationShareService {
  /// 分享解讀內容
  /// 
  /// [context] - BuildContext，用於顯示對話框
  /// [interpretation] - 解讀內容
  /// [title] - 解讀標題
  /// [chartData] - 星盤資料（可選）
  /// [personName] - 人物姓名（可選）
  /// [secondaryPersonName] - 次要人物姓名（可選）
  /// [interpretationType] - 解讀類型（可選）
  static Future<void> shareInterpretation({
    required BuildContext context,
    required String interpretation,
    required String title,
    ChartData? chartData,
    String? personName,
    String? secondaryPersonName,
    String? interpretationType,
  }) async {
    // 顯示分享選項對話框
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => _ShareOptionsSheet(
        interpretation: interpretation,
        title: title,
        chartData: chartData,
        personName: personName,
        secondaryPersonName: secondaryPersonName,
        interpretationType: interpretationType,
      ),
    );
  }

  /// 直接分享為文字（不顯示選項對話框）
  static Future<bool> shareAsText({
    required String interpretation,
    required String title,
    ChartData? chartData,
    String? personName,
    String? secondaryPersonName,
    String? interpretationType,
  }) async {
    try {
      final shareContent = _buildShareContent(
        interpretation: interpretation,
        title: title,
        chartData: chartData,
        personName: personName,
        secondaryPersonName: secondaryPersonName,
        interpretationType: interpretationType,
      );

      await Share.share(shareContent);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// 直接分享為摘要（不顯示選項對話框）
  static Future<bool> shareAsSummary({
    required String interpretation,
    required String title,
    ChartData? chartData,
    String? personName,
    String? secondaryPersonName,
    String? interpretationType,
  }) async {
    try {
      final summaryContent = _buildSummaryContent(
        interpretation: interpretation,
        title: title,
        chartData: chartData,
        personName: personName,
        secondaryPersonName: secondaryPersonName,
        interpretationType: interpretationType,
      );

      await Share.share(summaryContent);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// 構建完整分享內容
  static String _buildShareContent({
    required String interpretation,
    required String title,
    ChartData? chartData,
    String? personName,
    String? secondaryPersonName,
    String? interpretationType,
  }) {
    final buffer = StringBuffer();
    
    // 標題
    buffer.writeln('✨ $title');
    buffer.writeln();
    
    // 解讀類型
    if (interpretationType != null && interpretationType.isNotEmpty) {
      buffer.writeln('📝 $interpretationType');
      buffer.writeln();
    }
    
    // 人物資訊
    if (personName != null && personName.isNotEmpty) {
      buffer.writeln('👤 $personName');
      if (secondaryPersonName != null && secondaryPersonName.isNotEmpty) {
        buffer.writeln('👥 與 $secondaryPersonName');
      }
      buffer.writeln();
    }
    
    // 解讀內容（移除 Markdown 格式）
    final cleanContent = _removeMarkdownFormatting(interpretation);
    buffer.writeln('🔮 解讀內容：');
    buffer.writeln(cleanContent);
    buffer.writeln();
    
    // 創建時間
    final now = DateTime.now();
    final formattedDate = '${now.year}/${now.month.toString().padLeft(2, '0')}/${now.day.toString().padLeft(2, '0')}';
    buffer.writeln('📅 解讀日期：$formattedDate');
    buffer.writeln();
    
    // 應用簽名
    buffer.writeln('━━━━━━━━━━━━━━━━━━━━');
    buffer.writeln('🌟 來自AstReal');
    buffer.writeln('專業的占星分析應用');
    buffer.writeln('https://astreal.web.app');
    
    return buffer.toString();
  }

  /// 構建摘要分享內容
  static String _buildSummaryContent({
    required String interpretation,
    required String title,
    ChartData? chartData,
    String? personName,
    String? secondaryPersonName,
    String? interpretationType,
  }) {
    final buffer = StringBuffer();
    
    // 標題
    buffer.writeln('✨ $title');
    buffer.writeln();
    
    // 人物資訊
    if (personName != null && personName.isNotEmpty) {
      buffer.writeln('👤 $personName');
      if (secondaryPersonName != null && secondaryPersonName.isNotEmpty) {
        buffer.writeln('👥 與 $secondaryPersonName');
      }
      buffer.writeln();
    }
    
    // 解讀摘要（取前200字）
    final cleanContent = _removeMarkdownFormatting(interpretation);
    final summary = cleanContent.length > 200 
        ? '${cleanContent.substring(0, 200)}...' 
        : cleanContent;
    
    buffer.writeln('🔮 解讀摘要：');
    buffer.writeln(summary);
    buffer.writeln();
    
    // 創建時間
    final now = DateTime.now();
    final formattedDate = '${now.year}/${now.month.toString().padLeft(2, '0')}/${now.day.toString().padLeft(2, '0')}';
    buffer.writeln('📅 解讀日期：$formattedDate');
    buffer.writeln();
    
    // 應用簽名
    buffer.writeln('━━━━━━━━━━━━━━━━━━━━');
    buffer.writeln('🌟 來自AstReal');
    buffer.writeln('專業的占星分析應用');
    buffer.writeln('https://astreal.web.app');
    
    return buffer.toString();
  }

  /// 移除 Markdown 格式，但保留內容
  static String _removeMarkdownFormatting(String content) {
    // 移除 Markdown 標記，但保留內容
    String cleaned = content;

    // 先移除代碼塊（包含內容）
    final lines = cleaned.split('\n');
    final filteredLines = <String>[];
    bool inCodeBlock = false;

    for (final line in lines) {
      if (line.trim().startsWith('```')) {
        inCodeBlock = !inCodeBlock;
        continue; // 跳過代碼塊標記行
      }
      if (!inCodeBlock) {
        filteredLines.add(line);
      }
    }

    cleaned = filteredLines.join('\n');

    // 處理粗體：**text** -> text
    cleaned = cleaned.replaceAllMapped(RegExp(r'\*\*(.*?)\*\*'), (match) => match.group(1) ?? '');

    // 處理斜體：*text* -> text
    cleaned = cleaned.replaceAllMapped(RegExp(r'\*(.*?)\*'), (match) => match.group(1) ?? '');

    // 移除標題：# -> 空
    cleaned = cleaned.replaceAll(RegExp(r'#{1,6}\s*'), '');

    // 處理連結：[text](url) -> text
    cleaned = cleaned.replaceAllMapped(RegExp(r'\[([^\]]+)\]\([^)]+\)'), (match) => match.group(1) ?? '');

    // 處理行內代碼：`code` -> code
    cleaned = cleaned.replaceAllMapped(RegExp(r'`([^`]+)`'), (match) => match.group(1) ?? '');

    // 列表項目：- item -> • item
    cleaned = cleaned.replaceAll(RegExp(r'^\s*[-*+]\s+', multiLine: true), '• ');

    // 數字列表：1. item -> item
    cleaned = cleaned.replaceAll(RegExp(r'^\s*\d+\.\s+', multiLine: true), '');

    // 多餘的換行：最多保留兩個
    cleaned = cleaned.replaceAll(RegExp(r'\n{3,}'), '\n\n');

    // 限制長度（避免分享內容過長）
    // if (cleaned.length > 800) {
    //   cleaned = '${cleaned.substring(0, 800)}...';
    // }

    return cleaned.trim();
  }

  /// 顯示分享成功訊息
  static void showShareSuccessMessage(BuildContext context) {
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Row(
            children: [
              Icon(Icons.check_circle, color: Colors.white, size: 20),
              SizedBox(width: 8),
              Text('分享成功'),
            ],
          ),
          backgroundColor: AppColors.successGreen,
          duration: Duration(seconds: 2),
        ),
      );
    }
  }

  /// 顯示分享錯誤訊息
  static void showShareErrorMessage(BuildContext context, String error) {
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.error, color: Colors.white, size: 20),
              const SizedBox(width: 8),
              Expanded(child: Text('分享失敗：$error')),
            ],
          ),
          backgroundColor: AppColors.error,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }
}

/// 分享選項底部表單組件
class _ShareOptionsSheet extends StatelessWidget {
  final String interpretation;
  final String title;
  final ChartData? chartData;
  final String? personName;
  final String? secondaryPersonName;
  final String? interpretationType;

  const _ShareOptionsSheet({
    required this.interpretation,
    required this.title,
    this.chartData,
    this.personName,
    this.secondaryPersonName,
    this.interpretationType,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 標題
            Container(
              padding: const EdgeInsets.all(20),
              child: Row(
                children: [
                  const Icon(
                    Icons.share,
                    color: AppColors.royalIndigo,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  const Text(
                    '分享解讀內容',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textDark,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.close),
                    color: Colors.grey,
                  ),
                ],
              ),
            ),

            // 分享選項
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                children: [
                  // 分享為文字
                  _buildShareOption(
                    context: context,
                    icon: Icons.text_fields,
                    title: '分享為文字',
                    subtitle: '分享解讀內容的文字版本',
                    onTap: () {
                      Navigator.pop(context);
                      _shareAsText(context);
                    },
                  ),

                  const SizedBox(height: 12),

                  // 分享摘要
                  _buildShareOption(
                    context: context,
                    icon: Icons.summarize,
                    title: '分享摘要',
                    subtitle: '分享解讀內容的簡短摘要',
                    onTap: () {
                      Navigator.pop(context);
                      _shareAsSummary(context);
                    },
                  ),
                ],
              ),
            ),

            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  /// 構建分享選項項目
  Widget _buildShareOption({
    required BuildContext context,
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: AppColors.royalIndigo.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                color: AppColors.royalIndigo,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: AppColors.textDark,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),
            const Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: Colors.grey,
            ),
          ],
        ),
      ),
    );
  }

  /// 分享為文字
  Future<void> _shareAsText(BuildContext context) async {
    try {
      final success = await InterpretationShareService.shareAsText(
        interpretation: interpretation,
        title: title,
        chartData: chartData,
        personName: personName,
        secondaryPersonName: secondaryPersonName,
        interpretationType: interpretationType,
      );

      if (success) {
        InterpretationShareService.showShareSuccessMessage(context);
      }
    } catch (e) {
      InterpretationShareService.showShareErrorMessage(context, e.toString());
    }
  }

  /// 分享為摘要
  Future<void> _shareAsSummary(BuildContext context) async {
    try {
      final success = await InterpretationShareService.shareAsSummary(
        interpretation: interpretation,
        title: title,
        chartData: chartData,
        personName: personName,
        secondaryPersonName: secondaryPersonName,
        interpretationType: interpretationType,
      );

      if (success) {
        InterpretationShareService.showShareSuccessMessage(context);
      }
    } catch (e) {
      InterpretationShareService.showShareErrorMessage(context, e.toString());
    }
  }
}
