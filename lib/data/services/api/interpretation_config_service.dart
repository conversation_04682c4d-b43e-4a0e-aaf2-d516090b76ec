import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../../astreal.dart';
import 'remote_config_service.dart';

/// 解讀選項配置服務
class InterpretationConfigService {
  static InterpretationConfigService? _instance;
  static InterpretationConfigService get instance => _instance ??= InterpretationConfigService._();
  
  InterpretationConfigService._();

  /// 快取已載入的配置
  final Map<ChartType, InterpretationConfig> _configCache = {};

  /// 根據星盤類型載入解讀選項配置
  Future<InterpretationConfig> loadConfig(ChartType chartType) async {
    // 檢查快取
    // if (_configCache.containsKey(chartType)) {
    //   return _configCache[chartType]!;
    // }

    InterpretationConfig? config;

    // 優先嘗試從 Remote Config 獲取配置
    try {
      config = await _loadConfigFromRemoteConfig(chartType);
      if (config != null) {
        logger.i('成功從 Remote Config 載入 ${chartType.name} 解讀配置');
        // 快取配置
        _configCache[chartType] = config;
        return config;
      }
    } catch (e) {
      logger.w('從 Remote Config 載入 ${chartType.name} 解讀配置失敗: $e');
    }

    // 降級使用 Assets 配置
    try {
      config = await _loadConfigFromAssets(chartType);
      if (config != null) {
        logger.i('從 Assets 載入 ${chartType.name} 解讀配置');
        // 快取配置
        _configCache[chartType] = config;
        return config;
      }
    } catch (e) {
      logger.e('從 Assets 載入 ${chartType.name} 解讀配置失敗: $e');
    }

    // 如果都失敗，返回預設配置
    logger.w('所有配置載入方式都失敗，使用預設配置: ${chartType.name}');
    config = _getDefaultConfig(chartType);
    _configCache[chartType] = config;
    return config;
  }

  /// 從 Remote Config 載入配置
  Future<InterpretationConfig?> _loadConfigFromRemoteConfig(ChartType chartType) async {
    try {
      // 獲取 Remote Config 鍵值
      final String configKey = _getRemoteConfigKey(chartType);

      // 從 Remote Config 獲取配置
      final String configJson = RemoteConfigService.getConfigValue(configKey);

      if (configJson.isEmpty) {
        logger.d('Remote Config 中 $configKey 為空');
        return null;
      }

      // 解析 JSON 配置
      final Map<String, dynamic> jsonData = json.decode(configJson);

      // 解析配置
      final config = InterpretationConfig.fromJson(jsonData);

      return config;
    } catch (e) {
      logger.e('從 Remote Config 載入配置失敗: $e');
      return null;
    }
  }

  /// 從 Assets 載入配置
  Future<InterpretationConfig?> _loadConfigFromAssets(ChartType chartType) async {
    try {
      // 根據星盤類型選擇配置檔案
      String configPath = _getConfigPath(chartType);

      // 載入 JSON 檔案
      final String jsonString = await rootBundle.loadString(configPath);
      final Map<String, dynamic> jsonData = json.decode(jsonString);

      // 解析配置
      final config = InterpretationConfig.fromJson(jsonData);

      return config;
    } catch (e) {
      logger.e('從 Assets 載入配置失敗: $e');
      return null;
    }
  }

  /// 獲取預設配置（作為後備方案）
  InterpretationConfig _getDefaultConfig(ChartType chartType) {
    switch (chartType) {
      case ChartType.natal:
        return InterpretationConfig(
          version: '1.0.0',
          chartType: 'natal',
          options: [
            InterpretationOption(
              id: 'comprehensive',
              title: '綜合星盤分析',
              subtitle: '全面分析星盤配置和主要特徵',
              icon: Icons.auto_awesome,
              color: AppColors.royalIndigo,
              questions: [
                '這個星盤的主要特徵是什麼？',
                '最重要的行星配置有哪些？',
                '我應該關注哪些重點？',
                '這個星盤想告訴我什麼？',
              ],
              enabled: true,
              order: 1,
            ),
          ],
        );
      default:
        return InterpretationConfig(
          version: '1.0.0',
          chartType: 'general',
          options: [],
        );
    }
  }

  /// 根據星盤類型獲取 Remote Config 鍵值
  String _getRemoteConfigKey(ChartType chartType) {
    switch (chartType) {
      // 個人星盤
      case ChartType.natal:
        return 'interpretation_config_natal';

      // 預測類星盤
      case ChartType.transit:
        return 'interpretation_config_transit';
      case ChartType.secondaryProgression:
        return 'interpretation_config_secondary_progression';
      case ChartType.tertiaryProgression:
        return 'interpretation_config_tertiary_progression';
      case ChartType.solarArcDirection:
        return 'interpretation_config_solar_arc';

      // 返照盤類
      case ChartType.solarReturn:
        return 'interpretation_config_solar_return';
      case ChartType.lunarReturn:
        return 'interpretation_config_lunar_return';

      // 合盤類
      case ChartType.synastry:
        return 'interpretation_config_synastry';
      case ChartType.composite:
        return 'interpretation_config_composite';
      case ChartType.davison:
        return 'interpretation_config_davison';
      case ChartType.marks:
        return 'interpretation_config_marks';

      // 比較盤推運
      case ChartType.synastrySecondary:
      case ChartType.synastryTertiary:
        return 'interpretation_config_synastry_progression';

      // 組合盤推運
      case ChartType.compositeSecondary:
      case ChartType.compositeTertiary:
        return 'interpretation_config_composite_progression';

      // 時空中點盤推運
      case ChartType.davisonSecondary:
      case ChartType.davisonTertiary:
        return 'interpretation_config_davison_progression';

      // 馬克思盤推運
      case ChartType.marksSecondary:
      case ChartType.marksTertiary:
        return 'interpretation_config_marks_progression';

      // 事件占星
      case ChartType.horary:
        return 'interpretation_config_horary';
      case ChartType.event:
        return 'interpretation_config_event';

      // 特殊星盤
      case ChartType.mundane:
        return 'interpretation_config_mundane';
      case ChartType.firdaria:
        return 'interpretation_config_firdaria';
      case ChartType.profection:
        return 'interpretation_config_profection';

      // 季節節氣星盤
      case ChartType.equinoxSolstice:
        return 'interpretation_config_equinox_solstice';

      // 日月蝕星盤
      case ChartType.eclipse:
        return 'interpretation_config_eclipse';
      case ChartType.conjunctionJupiterSaturn:
        return 'interpretation_config_jupiter_saturn_conjunction';
      case ChartType.conjunctionMarsSaturn:
        return 'interpretation_config_mars_saturn_conjunction';
    }
  }

  /// 根據星盤類型獲取配置檔案路徑
  String _getConfigPath(ChartType chartType) {
    switch (chartType) {
      // 個人星盤
      case ChartType.natal:
        return 'assets/config/interpretation_config_natal.json';

      // 預測類星盤
      case ChartType.transit:
        return 'assets/config/interpretation_config_transit.json';
      case ChartType.secondaryProgression:
        return 'assets/config/interpretation_config_secondary_progression.json';
      case ChartType.tertiaryProgression:
        return 'assets/config/interpretation_config_tertiary_progression.json';
      case ChartType.solarArcDirection:
        return 'assets/config/interpretation_config_solar_arc.json';

      // 返照盤類
      case ChartType.solarReturn:
        return 'assets/config/interpretation_config_solar_return.json';
      case ChartType.lunarReturn:
        return 'assets/config/interpretation_config_lunar_return.json';

      // 合盤類
      case ChartType.synastry:
      case ChartType.composite:
      case ChartType.davison:
      case ChartType.marks:
        return 'assets/config/interpretation_config_relationship.json';

      // 比較盤推運
      case ChartType.synastrySecondary:
      case ChartType.synastryTertiary:
        return 'assets/config/interpretation_config_synastry_progression.json';

      // 組合盤推運
      case ChartType.compositeSecondary:
      case ChartType.compositeTertiary:
        return 'assets/config/interpretation_config_composite_progression.json';

      // 時空中點盤推運
      case ChartType.davisonSecondary:
      case ChartType.davisonTertiary:
        return 'assets/config/interpretation_config_davison_progression.json';

      // 馬克思盤推運
      case ChartType.marksSecondary:
      case ChartType.marksTertiary:
        return 'assets/config/interpretation_config_marks_progression.json';

      // 事件占星
      case ChartType.horary:
        return 'assets/config/interpretation_config_horary.json';
      case ChartType.event:
        return 'assets/config/interpretation_config_event.json';

      // 特殊星盤
      case ChartType.mundane:
        return 'assets/config/interpretation_config_mundane.json';
      case ChartType.firdaria:
        return 'assets/config/interpretation_config_firdaria.json';
      case ChartType.profection:
        return 'assets/config/interpretation_config_profection.json';

      // 季節節氣星盤
      case ChartType.equinoxSolstice:
        return 'assets/config/interpretation_config_equinox_solstice.json';

      // 日月蝕星盤
      case ChartType.eclipse:
        return 'assets/config/interpretation_config_eclipse.json';
      case ChartType.conjunctionJupiterSaturn:
        return 'assets/config/interpretation_config_jupiter_saturn_conjunction.json';
      case ChartType.conjunctionMarsSaturn:
        return 'assets/config/interpretation_config_mars_saturn_conjunction.json';
    }
  }

  /// 清除快取
  void clearCache() {
    _configCache.clear();
  }

  /// 清除特定星盤類型的快取
  void clearCacheForChartType(ChartType chartType) {
    _configCache.remove(chartType);
  }

  /// 強制重新載入配置（清除快取後重新載入）
  Future<InterpretationConfig> reloadConfig(ChartType chartType) async {
    clearCacheForChartType(chartType);
    return await loadConfig(chartType);
  }
}

/// 解讀配置模型
class InterpretationConfig {
  final String version;
  final String chartType;
  final List<InterpretationOption> options;
  final String? description;
  final String? lastUpdated;

  InterpretationConfig({
    required this.version,
    required this.chartType,
    required this.options,
    this.description,
    this.lastUpdated,
  });

  factory InterpretationConfig.fromJson(Map<String, dynamic> json) {
    return InterpretationConfig(
      version: json['version'] ?? '1.0.0',
      chartType: json['chartType'] ?? 'general',
      description: json['description'],
      lastUpdated: json['lastUpdated'],
      options: (json['options'] as List<dynamic>?)
          ?.map((optionJson) => InterpretationOption.fromJson(optionJson))
          .where((option) => option.enabled) // 只載入啟用的選項
          .toList() ?? [],
    );
  }

  /// 獲取已啟用且按順序排列的選項
  List<InterpretationOption> get enabledOptions {
    final enabled = options.where((option) => option.enabled).toList();
    enabled.sort((a, b) => a.order.compareTo(b.order));
    return enabled;
  }
}

/// 解讀選項模型
class InterpretationOption {
  final String id;
  final String title;
  final String subtitle;
  final IconData icon;
  final Color color;
  final List<String> questions;
  final String? keyPoint;
  final bool enabled;
  final int order;
  final HouseSystem? houseSystem; // 指定的宮位制
  final bool useSystemHouseSystem; // 是否使用系統設定的宮位制

  InterpretationOption({
    required this.id,
    required this.title,
    required this.subtitle,
    required this.icon,
    required this.color,
    required this.questions,
    this.keyPoint,
    this.enabled = true,
    this.order = 0,
    this.houseSystem, // 宮位制參數，可選
    this.useSystemHouseSystem = false, // 預設不使用系統設定
  });

  factory InterpretationOption.fromJson(Map<String, dynamic> json) {
    final houseSystemValue = json['houseSystem'];
    final parsedHouseSystem = _parseHouseSystem(houseSystemValue);
    final useSystemHouseSystem = houseSystemValue != null &&
        (houseSystemValue.toString().toLowerCase() == 'system' ||
         houseSystemValue.toString().toLowerCase() == 'app_default' ||
         houseSystemValue.toString().toLowerCase() == 'default');

    return InterpretationOption(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      subtitle: json['subtitle'] ?? '',
      icon: _parseIcon(json['icon']),
      color: _parseColor(json['color']),
      questions: (json['questions'] as List<dynamic>?)
          ?.map((q) => q.toString())
          .toList() ?? [],
      keyPoint: json['keyPoint'],
      enabled: json['enabled'] ?? true,
      order: json['order'] ?? 0,
      houseSystem: parsedHouseSystem, // 解析宮位制
      useSystemHouseSystem: useSystemHouseSystem, // 是否使用系統設定
    );
  }

  /// 解析圖示
  static IconData _parseIcon(dynamic iconValue) {
    if (iconValue == null) return Icons.auto_awesome;
    
    final iconName = iconValue.toString();
    switch (iconName) {
      case 'person':
        return Icons.person;
      case 'monetization_on':
        return Icons.monetization_on;
      case 'favorite':
        return Icons.favorite;
      case 'family_restroom':
        return Icons.family_restroom;
      case 'work':
        return Icons.work;
      case 'people':
        return Icons.people;
      case 'health_and_safety':
        return Icons.health_and_safety;
      case 'balance':
        return Icons.balance;
      case 'auto_awesome':
        return Icons.auto_awesome;
      case 'trending_up':
        return Icons.trending_up;
      case 'business_center':
        return Icons.business_center;
      case 'chat':
        return Icons.chat;
      case 'psychology':
        return Icons.psychology;
      default:
        return Icons.auto_awesome;
    }
  }

  /// 解析顏色
  static Color _parseColor(dynamic colorValue) {
    if (colorValue == null) return AppColors.royalIndigo;

    final colorName = colorValue.toString();
    switch (colorName) {
      case 'royalIndigo':
        return AppColors.royalIndigo;
      case 'green':
        return Colors.green;
      case 'pink':
        return Colors.pink;
      case 'blue':
        return Colors.blue;
      case 'indigo':
        return Colors.indigo;
      case 'teal':
        return Colors.teal;
      case 'orange':
        return Colors.orange;
      case 'purple':
        return Colors.purple;
      case 'red':
        return Colors.red;
      default:
        return AppColors.royalIndigo;
    }
  }

  /// 解析宮位制
  static HouseSystem? _parseHouseSystem(dynamic houseSystemValue) {
    if (houseSystemValue == null) return null;

    if (houseSystemValue is String) {
      // 處理字符串形式的宮位制名稱
      switch (houseSystemValue.toLowerCase()) {
        case 'system':
        case 'app_default':
        case 'default':
          // 特殊值：使用應用當前設定的宮位制
          return null; // 返回 null 表示使用系統設定
        case 'placidus':
        case 'p':
          return HouseSystem.placidus;
        case 'koch':
        case 'k':
          return HouseSystem.koch;
        case 'equal':
        case 'equal_house':
        case 'e':
        case 'a':
          return HouseSystem.equal;
        case 'whole_sign':
        case 'whole':
        case 'w':
          return HouseSystem.wholeSign;
        case 'campanus':
        case 'c':
          return HouseSystem.campanus;
        case 'regiomontanus':
        case 'r':
          return HouseSystem.regiomontanus;
        case 'topocentric':
        case 't':
          return HouseSystem.topocentric;
        case 'alcabitius':
        case 'b':
          return HouseSystem.alcabitius;
        case 'porphyry':
        case 'o':
          return HouseSystem.porphyry;
        case 'morinus':
        case 'm':
          return HouseSystem.morinus;
        case 'equal_vehlow':
        case 'vehlow':
        case 'v':
          return HouseSystem.equalVehlow;
        case 'meridian_houses':
        case 'meridian':
        case 'x':
          return HouseSystem.meridianHouses;
        case 'horizon_azimuth':
        case 'horizon':
        case 'h':
          return HouseSystem.horizonAzimuth;
        case 'gauquelin_sectors':
        case 'gauquelin':
        case 'g':
          return HouseSystem.gauquelinSectors;
        case 'krusinski_pisa_goelzer':
        case 'krusinski':
        case 'u':
          return HouseSystem.krusinskiPisaGoelzer;
        case 'apc_houses':
        case 'apc':
        case 'y':
          return HouseSystem.apcHouses;
        default:
          logger.w('未知的宮位制: $houseSystemValue');
          return null;
      }
    }

    return null;
  }
}
