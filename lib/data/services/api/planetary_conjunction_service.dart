import 'package:sweph/sweph.dart';

import '../../../core/utils/logger_utils.dart';
import '../../../shared/utils/julian_date_utils.dart';
import '../../models/astrology/aspect_info.dart';
import '../../models/astrology/planet_position.dart';
import '../../models/astrology/planetary_conjunction.dart';
import 'astrology_service.dart';
import 'conjunction_cache_service.dart';

/// 行星會合計算服務
/// 使用 Swiss Ephemeris 精確計算木土會合和火土會合
class PlanetaryConjunctionService {
  static const double _conjunctionOrb = 10; // 會合容許度：5度
  static const double _precisionThreshold = 0.1; // 精度閾值：0.01度

  // 創建 AstrologyService 實例
  static final AstrologyService _astrologyService = AstrologyService();

  /// 計算行星會合分析結果
  ///
  /// [latitude] 緯度
  /// [longitude] 經度
  /// [startDate] 開始日期（默認為當前日期前5年）
  /// [endDate] 結束日期（默認為當前日期後5年）
  static Future<ConjunctionAnalysisResult> calculateConjunctions({
    required double latitude,
    required double longitude,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    final now = DateTime.now();
    final effectiveStartDate =
        startDate ?? now.subtract(const Duration(days: 3650)); // 5年前 1825 3650
    final effectiveEndDate =
        endDate ?? now.add(const Duration(days: 3650)); // 5年後

    // 首先嘗試從緩存載入
    logger.d('檢查緩存中是否有相同參數的分析結果...');
    final cachedResult = await ConjunctionCacheService.loadConjunctionResult(
      latitude: latitude,
      longitude: longitude,
      startDate: effectiveStartDate,
      endDate: effectiveEndDate,
    );

    if (cachedResult != null) {
      logger.d('從緩存載入會合分析結果');
      return cachedResult;
    }

    logger.d('緩存中無相同參數的結果，開始新的計算...');

    // 計算木土會合
    final jupiterSaturnConjunctions = await _findConjunctions(
      HeavenlyBody.SE_JUPITER,
      HeavenlyBody.SE_SATURN,
      ConjunctionType.jupiterSaturn,
      effectiveStartDate,
      effectiveEndDate,
      latitude,
      longitude,
    );

    // 計算火土會合
    final marsSaturnConjunctions = await _findConjunctions(
      HeavenlyBody.SE_MARS,
      HeavenlyBody.SE_SATURN,
      ConjunctionType.marsSaturn,
      effectiveStartDate,
      effectiveEndDate,
      latitude,
      longitude,
    );

    // 合併所有會合
    final allConjunctions = [
      ...jupiterSaturnConjunctions,
      ...marsSaturnConjunctions
    ];
    allConjunctions.sort((a, b) => a.dateTime.compareTo(b.dateTime));

    // 分離歷史和未來會合
    final historicalConjunctions =
        allConjunctions.where((c) => c.dateTime.isBefore(now)).toList();
    final futureConjunctions =
        allConjunctions.where((c) => c.dateTime.isAfter(now)).toList();

    // 找到最接近的會合
    PlanetaryConjunction? nearestConjunction;
    if (futureConjunctions.isNotEmpty) {
      nearestConjunction = futureConjunctions.first;
    } else if (historicalConjunctions.isNotEmpty) {
      nearestConjunction = historicalConjunctions.last;
    }

    // 生成分析摘要
    final analysisSummary = _generateAnalysisSummary(allConjunctions);
    final investmentSummary =
        _generateInvestmentSummary(allConjunctions, nearestConjunction);

    final result = ConjunctionAnalysisResult(
      historicalConjunctions: historicalConjunctions,
      futureConjunctions: futureConjunctions,
      nearestConjunction: nearestConjunction,
      analysisSummary: analysisSummary,
      investmentSummary: investmentSummary,
    );

    // 保存結果到緩存
    logger.d('保存分析結果到緩存...');
    await ConjunctionCacheService.saveConjunctionResult(
      latitude: latitude,
      longitude: longitude,
      startDate: effectiveStartDate,
      endDate: effectiveEndDate,
      result: result,
    );

    return result;
  }

  /// 尋找兩個行星之間的會合
  static Future<List<PlanetaryConjunction>> _findConjunctions(
    HeavenlyBody planet1,
    HeavenlyBody planet2,
    ConjunctionType type,
    DateTime startDate,
    DateTime endDate,
    double latitude,
    double longitude,
  ) async {
    final conjunctions = <PlanetaryConjunction>[];

    // 確保 AstrologyService 已初始化
    await _astrologyService.initialize();

    logger.d('開始搜索 ${_getPlanetName(planet1)} 和 ${_getPlanetName(planet2)} 的會合');
    logger.d('搜索範圍: ${startDate.toString().substring(0, 10)} 到 ${endDate.toString().substring(0, 10)}');

    // 使用更智能的搜索策略
    await _searchConjunctionsInRange(
      planet1,
      planet2,
      type,
      startDate,
      endDate,
      latitude,
      longitude,
      conjunctions,
    );

    // 按時間排序
    conjunctions.sort((a, b) => a.dateTime.compareTo(b.dateTime));

    logger.d('${_getPlanetName(planet1)} 和 ${_getPlanetName(planet2)} 搜索完成，找到 ${conjunctions.length} 個會合');
    return conjunctions;
  }

  /// 在指定範圍內搜索會合
  static Future<void> _searchConjunctionsInRange(
    HeavenlyBody planet1,
    HeavenlyBody planet2,
    ConjunctionType type,
    DateTime startDate,
    DateTime endDate,
    double latitude,
    double longitude,
    List<PlanetaryConjunction> conjunctions,
  ) async {
    DateTime currentDate = startDate;

    // 根據會合類型調整搜索步長
    final searchStep = type == ConjunctionType.jupiterSaturn
        ? const Duration(days: 60)  // 木土會合較少，可以用較大步長
        : const Duration(days: 30); // 火土會合較頻繁，用較小步長

    double? previousAngleDiff;
    bool wasDecreasing = false;

    while (currentDate.isBefore(endDate)) {
      try {
        // 轉換為儒略日
        final julianDay = await JulianDateUtils.dateTimeToJulianDay(
          currentDate,
          latitude,
          longitude,
        );

        // 計算兩個行星的位置
        final planet1Result = _astrologyService.sweCalcUt(julianDay, planet1);
        final planet2Result = _astrologyService.sweCalcUt(julianDay, planet2);

        // 計算角度差
        final angleDiff = _calculateAngleDifference(
          planet1Result.longitude,
          planet2Result.longitude,
        );

        // logger.d('檢查日期: ${currentDate.toString().substring(0, 10)}, '
        //     '${_getPlanetName(planet1)} 經度=${planet1Result.longitude.toStringAsFixed(2)}, '
        //     '${_getPlanetName(planet2)} 經度=${planet2Result.longitude.toStringAsFixed(2)}, '
        //     '角度差=${angleDiff.toStringAsFixed(2)}');

        // 檢測角度差的變化趨勢
        if (previousAngleDiff != null) {
          final isDecreasing = angleDiff < previousAngleDiff;

          // 如果角度差從減少變為增加，可能剛過了會合點
          if (wasDecreasing && !isDecreasing && previousAngleDiff <= _conjunctionOrb) {
            logger.d('檢測到可能的會合點，開始精確搜索');

            // 在前一個檢查點和當前檢查點之間進行精確搜索
            final searchStart = currentDate.subtract(searchStep);
            final exactConjunction = await _findExactConjunctionInRange(
              planet1,
              planet2,
              type,
              searchStart,
              currentDate,
              latitude,
              longitude,
            );

            if (exactConjunction != null) {
              // 檢查是否已經有相近時間的會合事件，避免重複
              final isDuplicate = conjunctions.any((existing) =>
                  existing.type == exactConjunction.type &&
                  (existing.dateTime.difference(exactConjunction.dateTime).inDays.abs() < 30));

              if (!isDuplicate) {
                conjunctions.add(exactConjunction);
                logger.d('找到會合: ${exactConjunction.typeName} 在 ${exactConjunction.dateTime}');
              } else {
                logger.d('跳過重複會合: ${exactConjunction.typeName} 在 ${exactConjunction.dateTime}');
              }
            }
          }

          wasDecreasing = isDecreasing;
        }

        previousAngleDiff = angleDiff;
        currentDate = currentDate.add(searchStep);
      } catch (e) {
        logger.d('計算會合時出錯: $e');
        currentDate = currentDate.add(searchStep);
      }
    }
  }

  /// 在指定範圍內精確尋找會合時間
  static Future<PlanetaryConjunction?> _findExactConjunctionInRange(
    HeavenlyBody planet1,
    HeavenlyBody planet2,
    ConjunctionType type,
    DateTime startDate,
    DateTime endDate,
    double latitude,
    double longitude,
  ) async {
    DateTime bestDate = startDate;
    double bestAngleDiff = double.infinity;

    // 在範圍內每天檢查，找到角度差最小的時間點
    DateTime currentDate = startDate;
    while (currentDate.isBefore(endDate)) {
      try {
        final julianDay = await JulianDateUtils.dateTimeToJulianDay(
          currentDate,
          latitude,
          longitude,
        );

        final planet1Result = _astrologyService.sweCalcUt(julianDay, planet1);
        final planet2Result = _astrologyService.sweCalcUt(julianDay, planet2);

        final angleDiff = _calculateAngleDifference(
          planet1Result.longitude,
          planet2Result.longitude,
        );

        if (angleDiff < bestAngleDiff) {
          bestAngleDiff = angleDiff;
          bestDate = currentDate;
        }

        currentDate = currentDate.add(const Duration(hours: 1));
      } catch (e) {
        logger.d('精確搜索會合時出錯: $e');
        currentDate = currentDate.add(const Duration(hours: 1));
      }
    }

    // 如果找到的最佳角度差在容許度內，創建會合對象
    if (bestAngleDiff <= _precisionThreshold) {
      try {
        final julianDay = await JulianDateUtils.dateTimeToJulianDay(
          bestDate,
          latitude,
          longitude,
        );

        final planet1Result = _astrologyService.sweCalcUt(julianDay, planet1);
        final zodiacSign = _getZodiacSign(planet1Result.longitude);
        final financialMeaning = _getFinancialMeaning(type, zodiacSign);
        final investmentAdvice = _getInvestmentAdvice(type, zodiacSign);

        logger.d('找到精確會合時間: $bestDate, 角度差: ${bestAngleDiff.toStringAsFixed(3)}°');

        return PlanetaryConjunction(
          type: type,
          dateTime: bestDate,
          longitude: planet1Result.longitude,
          zodiacSign: zodiacSign,
          exactness: bestAngleDiff,
          financialMeaning: financialMeaning,
          investmentAdvice: investmentAdvice,
          isHistorical: bestDate.isBefore(DateTime.now()),
        );
      } catch (e) {
        logger.d('創建會合對象時出錯: $e');
      }
    }

    return null;
  }

  /// 精確尋找會合時間
  static Future<PlanetaryConjunction?> _findExactConjunction(
    HeavenlyBody planet1,
    HeavenlyBody planet2,
    ConjunctionType type,
    DateTime approximateDate,
    double latitude,
    double longitude,
  ) async {
    DateTime start = approximateDate.subtract(const Duration(days: 15));
    DateTime end = approximateDate.add(const Duration(days: 15));
    double angleDiff = 5;

    // 使用二分法尋找精確時間
    for (int i = 0; i < 200; i++) {
      logger.d('精確搜索第$i次迭代: $start 到 $end');
      if (angleDiff < -1) {
        approximateDate = approximateDate.subtract(const Duration(days: 10));
      } else if (angleDiff < -0.5) {
        approximateDate = approximateDate.subtract(const Duration(days: 1));
      } else if (angleDiff < -0.1) {
        approximateDate = approximateDate.subtract(const Duration(hours: 6));
      } else {
        approximateDate = approximateDate.subtract(const Duration(hours: 1));
      }

      final mid = approximateDate;

      try {
        final julianDay = await JulianDateUtils.dateTimeToJulianDay(
          mid,
          latitude,
          longitude,
        );

        final planet1Result = _astrologyService.sweCalcUt(julianDay, planet1);
        final planet2Result = _astrologyService.sweCalcUt(julianDay, planet2);

        // 創建臨時的 PlanetPosition 對象用於 getAspectDirection 方法
        final planetPos1 = PlanetPosition(
          id: planet1.value,
          name: _getPlanetName(planet1),
          symbol: _getPlanetSymbol(planet1),
          longitude: planet1Result.longitude,
          latitude: planet1Result.latitude,
          distance: planet1Result.distance,
          longitudeSpeed: planet1Result.speedInLongitude,
          latitudeSpeed: planet1Result.speedInLatitude,
          distanceSpeed: planet1Result.speedInDistance,
          sign: _getZodiacSign(planet1Result.longitude),
          house: 1, // 臨時值，不影響相位方向計算
        );

        final planetPos2 = PlanetPosition(
          id: planet2.value,
          name: _getPlanetName(planet2),
          symbol: _getPlanetSymbol(planet2),
          longitude: planet2Result.longitude,
          latitude: planet2Result.latitude,
          distance: planet2Result.distance,
          longitudeSpeed: planet2Result.speedInLongitude,
          latitudeSpeed: planet2Result.speedInLatitude,
          distanceSpeed: planet2Result.speedInDistance,
          sign: _getZodiacSign(planet2Result.longitude),
          house: 1, // 臨時值，不影響相位方向計算
        );

        angleDiff = _calculateAngleDifference(
          planet2Result.longitude,
          planet1Result.longitude,
        );
        // 如果精度足夠，創建會合對象
        if (angleDiff <= _precisionThreshold && angleDiff >= 0) {
          final isApplying = await _isApplyingAspect(
            planetPos1,
            planetPos2,
            approximateDate,
            latitude,
            longitude,
          );

          logger.d('入相位檢查: $isApplying');
          // if (!isApplying) {
          final zodiacSign = _getZodiacSign(planet1Result.longitude);
          final financialMeaning = _getFinancialMeaning(type, zodiacSign);
          final investmentAdvice = _getInvestmentAdvice(type, zodiacSign);
          logger.d(
              '精確搜索第$i次迭代: $mid, ${planet1.value} 經度=${planet1Result.longitude}, ${planet2.value} 經度=${planet2Result.longitude}, 角度差=$angleDiff');
          logger.d('找到精確會合時間: $mid, zodiacSign: $zodiacSign');
          return PlanetaryConjunction(
            type: type,
            dateTime: mid,
            longitude: planet1Result.longitude,
            zodiacSign: zodiacSign,
            exactness: angleDiff,
            financialMeaning: financialMeaning,
            investmentAdvice: investmentAdvice,
            isHistorical: mid.isBefore(DateTime.now()),
          );
          // }
        }

        // 調整搜索範圍
        // final startJulianDay = await JulianDateUtils.dateTimeToJulianDay(
        //   start,
        //   latitude,
        //   longitude,
        // );
        // final startPlanet1 =
        //     _astrologyService.sweCalcUt(startJulianDay, planet1);
        // final startPlanet2 =
        //     _astrologyService.sweCalcUt(startJulianDay, planet2);
        // final startAngleDiff = _calculateAngleDifference(
        //   startPlanet2.longitude,
        //   startPlanet1.longitude,
        // );
        // if (startAngleDiff > angleDiff) {
        //   end = mid;
        // } else {
        //   start = mid;
        // }

        // 防止無限循環
        if (end.difference(start).inMilliseconds < 3600000) {
          // 1小時
          break;
        }
      } catch (e) {
        logger.d('精確搜索會合時出錯: $e');
        break;
      }
    }

    return null;
  }

  /// 判斷是否為入相位（applying aspect）
  ///
  /// 使用 AstrologyService.getAspectDirection 方法來判斷入相位
  /// 這確保了與其他相位計算的一致性
  static Future<bool> _isApplyingAspect(
    PlanetPosition planetPos1,
    PlanetPosition planetPos2,
    DateTime currentDate,
    double latitude,
    double longitude,
  ) async {
    try {
      // 使用 AstrologyService.getAspectDirection 判斷會合（0度相位）的方向
      final aspectDirection = _astrologyService.getAspectDirection(
        planetPos1,
        planetPos2,
        0.0, // 會合是0度相位
      );

      final isApplying = aspectDirection == AspectDirection.applying;

      return isApplying;
    } catch (e) {
      logger.d('判斷入相位時出錯: $e');
      return false;
    }
  }

  /// 計算角度差
  static double _calculateAngleDifference(double angle1, double angle2) {
    double diff = (angle1 - angle2).abs();
    if (diff > 180) {
      diff = 360 - diff;
    }
    return diff;
  }

  /// 獲取行星名稱
  static String _getPlanetName(HeavenlyBody body) {
    switch (body) {
      case HeavenlyBody.SE_JUPITER:
        return '木星';
      case HeavenlyBody.SE_SATURN:
        return '土星';
      case HeavenlyBody.SE_MARS:
        return '火星';
      default:
        return body.toString();
    }
  }

  /// 獲取行星符號
  static String _getPlanetSymbol(HeavenlyBody body) {
    switch (body) {
      case HeavenlyBody.SE_JUPITER:
        return '♃';
      case HeavenlyBody.SE_SATURN:
        return '♄';
      case HeavenlyBody.SE_MARS:
        return '♂';
      default:
        return '?';
    }
  }

  /// 獲取星座名稱
  static String _getZodiacSign(double longitude) {
    final signs = [
      '牡羊座',
      '金牛座',
      '雙子座',
      '巨蟹座',
      '獅子座',
      '處女座',
      '天秤座',
      '天蠍座',
      '射手座',
      '摩羯座',
      '水瓶座',
      '雙魚座'
    ];
    final signIndex = (longitude / 30).floor() % 12;
    return signs[signIndex];
  }

  /// 獲取財務意義
  static String _getFinancialMeaning(ConjunctionType type, String zodiacSign) {
    switch (type) {
      case ConjunctionType.jupiterSaturn:
        return '木土會合代表重要的經濟週期轉換點，在$zodiacSign發生時，'
            '象徵著長期投資機會的出現和經濟結構的重大調整。';
      case ConjunctionType.marsSaturn:
        return '火土會合代表行動與謹慎的平衡點，在$zodiacSign發生時，'
            '提醒投資者需要在積極進取與風險控制之間找到平衡。';
    }
  }

  /// 獲取投資建議
  static String _getInvestmentAdvice(ConjunctionType type, String zodiacSign) {
    switch (type) {
      case ConjunctionType.jupiterSaturn:
        return '建議關注長期價值投資機會，特別是與$zodiacSign相關的行業領域。'
            '這是制定20年投資規劃的重要時機。';
      case ConjunctionType.marsSaturn:
        return '建議採取謹慎的投資策略，避免過度冒險。'
            '適合進行風險評估和投資組合調整。';
    }
  }

  /// 生成分析摘要
  static String _generateAnalysisSummary(
      List<PlanetaryConjunction> conjunctions) {
    final jupiterSaturnCount = conjunctions
        .where((c) => c.type == ConjunctionType.jupiterSaturn)
        .length;
    final marsSaturnCount =
        conjunctions.where((c) => c.type == ConjunctionType.marsSaturn).length;

    return '在分析期間內，共發現 $jupiterSaturnCount 次木土會合和 $marsSaturnCount 次火土會合。'
        '木土會合標誌著重要的經濟週期，而火土會合則提醒我們注意投資風險控制。';
  }

  /// 生成投資建議摘要
  static String _generateInvestmentSummary(
    List<PlanetaryConjunction> conjunctions,
    PlanetaryConjunction? nearestConjunction,
  ) {
    if (nearestConjunction == null) {
      return '目前沒有即將到來的重要行星會合，建議維持現有投資策略。';
    }

    final timeDesc = nearestConjunction.getTimeDescription();
    return '最近的${nearestConjunction.typeName}將在$timeDesc發生，'
        '建議提前做好相應的投資準備和風險管理。';
  }
}
