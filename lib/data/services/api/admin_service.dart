import 'package:cloud_firestore/cloud_firestore.dart';

import '../../../core/constants/firebase_collections.dart';
import '../../../core/utils/logger_utils.dart';

/// 管理者服務
class AdminService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  
  // 管理者 UID 列表（可以從 Remote Config 或 Firestore 獲取）
  static const List<String> _adminUids = [
    // 在這裡添加管理者的 UID
    // 'admin_uid_1',
    // 'admin_uid_2',
  ];

  /// 檢查用戶是否為管理者
  static Future<bool> isUserAdmin(String uid) async {
    try {
      // 方法1：檢查硬編碼的管理者列表
      if (_adminUids.contains(uid)) {
        logger.i('用戶 $uid 在硬編碼管理者列表中');
        return true;
      }

      // 方法2：從 Firestore 檢查管理者權限
      try {
        final adminDoc = await _firestore
            .collection(FirebaseCollections.userProfiles)
            .doc(uid)
            .get();
        
        if (adminDoc.exists) {
          final data = adminDoc.data();
          // 檢查兩種可能的欄位名稱
          final isAdmin = data?['is_admin'] as bool? ?? data?['isAdmin'] as bool? ?? false;
          logger.i('用戶 $uid 在 Firestore 中的管理者狀態: $isAdmin');
          return isAdmin;
        }
      } catch (e) {
        logger.w('無法從 Firestore 檢查管理者狀態: $e');
      }

      // 方法3：檢查用戶的 custom claims（需要 Firebase Admin SDK）
      // 這個方法需要在後端實現，這裡只是示例
      
      logger.d('用戶 $uid 不是管理者');
      return false;
    } catch (e) {
      logger.e('檢查管理者狀態失敗: $e');
      return false;
    }
  }

  /// 設置用戶為管理者（需要管理者權限）
  static Future<bool> setUserAdmin(String uid, bool isAdmin) async {
    try {
      await _firestore
          .collection(FirebaseCollections.userProfiles)
          .doc(uid)
          .set({
        'isAdmin': isAdmin,
        'updatedAt': FieldValue.serverTimestamp(),
      }, SetOptions(merge: true));
      
      logger.i('用戶 $uid 的管理者狀態已更新為: $isAdmin');
      return true;
    } catch (e) {
      logger.e('設置管理者狀態失敗: $e');
      return false;
    }
  }

  /// 獲取所有管理者列表
  static Future<List<String>> getAllAdminUids() async {
    try {
      final querySnapshot = await _firestore
          .collection(FirebaseCollections.userProfiles)
          .where('is_admin', isEqualTo: true)
          .get();
      
      final adminUids = querySnapshot.docs.map((doc) => doc.id).toList();
      
      // 合併硬編碼的管理者列表
      final allAdmins = <String>{...adminUids, ..._adminUids}.toList();
      
      logger.i('找到 ${allAdmins.length} 個管理者');
      return allAdmins;
    } catch (e) {
      logger.e('獲取管理者列表失敗: $e');
      return _adminUids;
    }
  }

  /// 檢查當前用戶是否有管理者權限
  static Future<bool> checkCurrentUserAdminPermission() async {
    try {
      // 這裡需要獲取當前用戶的 UID
      // 由於這是一個靜態方法，我們需要從外部傳入或使用其他方式獲取
      // 暫時返回 false，實際使用時需要傳入當前用戶 UID
      return false;
    } catch (e) {
      logger.e('檢查當前用戶管理者權限失敗: $e');
      return false;
    }
  }

  /// 初始化管理者數據（僅在首次設置時使用）
  static Future<void> initializeAdminData() async {
    try {
      // 檢查是否已經初始化
      final initDoc = await _firestore
          .collection('system')
          .doc('admin_initialized')
          .get();
      
      if (initDoc.exists) {
        logger.i('管理者數據已初始化');
        return;
      }

      // 如果有硬編碼的管理者，將其添加到 Firestore
      for (final uid in _adminUids) {
        await _firestore
            .collection(FirebaseCollections.userProfiles)
            .doc(uid)
            .set({
          'isAdmin': true,
          'createdAt': FieldValue.serverTimestamp(),
          'source': 'hardcoded',
        });
      }

      // 標記為已初始化
      await _firestore
          .collection('system')
          .doc('admin_initialized')
          .set({
        'initialized': true,
        'initializedAt': FieldValue.serverTimestamp(),
      });

      logger.i('管理者數據初始化完成');
    } catch (e) {
      logger.e('初始化管理者數據失敗: $e');
    }
  }

  /// 記錄管理者操作日誌
  static Future<void> logAdminAction({
    required String adminUid,
    required String action,
    required String description,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      await _firestore
          .collection('admin_logs')
          .add({
        'adminUid': adminUid,
        'action': action,
        'description': description,
        'metadata': metadata ?? {},
        'timestamp': FieldValue.serverTimestamp(),
      });
      
      logger.i('管理者操作已記錄: $action');
    } catch (e) {
      logger.e('記錄管理者操作失敗: $e');
    }
  }

  /// 獲取管理者操作日誌
  static Future<List<Map<String, dynamic>>> getAdminLogs({
    int limit = 50,
    String? adminUid,
  }) async {
    try {
      Query query = _firestore
          .collection('admin_logs')
          .orderBy('timestamp', descending: true)
          .limit(limit);
      
      if (adminUid != null) {
        query = query.where('adminUid', isEqualTo: adminUid);
      }
      
      final querySnapshot = await query.get();
      
      return querySnapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        data['id'] = doc.id;
        return data;
      }).toList();
    } catch (e) {
      logger.e('獲取管理者日誌失敗: $e');
      return [];
    }
  }

  /// 清理舊的管理者日誌（保留最近30天）
  static Future<void> cleanupOldLogs() async {
    try {
      final cutoffDate = DateTime.now().subtract(const Duration(days: 30));
      
      final querySnapshot = await _firestore
          .collection('admin_logs')
          .where('timestamp', isLessThan: Timestamp.fromDate(cutoffDate))
          .get();
      
      final batch = _firestore.batch();
      for (final doc in querySnapshot.docs) {
        batch.delete(doc.reference);
      }
      
      await batch.commit();
      
      logger.i('清理了 ${querySnapshot.docs.length} 條舊的管理者日誌');
    } catch (e) {
      logger.e('清理管理者日誌失敗: $e');
    }
  }
}
