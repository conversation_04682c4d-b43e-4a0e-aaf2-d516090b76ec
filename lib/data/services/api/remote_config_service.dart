import 'dart:convert';
import 'dart:io';

import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/foundation.dart';

import '../../../core/utils/logger_utils.dart';

/// Remote Config 服務，用於管理 AI API Keys
class RemoteConfigService {
  static FirebaseRemoteConfig? _remoteConfig;
  static bool _isInitialized = false;
  
  // 默認配置值
  static const Map<String, dynamic> _defaultConfig = {
    'celebrity_examples': '''
{
  "examples": [
    {
      "name": "巴拉克・歐巴馬",
      "birthData": {
        "id": "celebrity_barack_obama",
        "name": "巴拉克・歐巴馬",
        "dateTime": "1961-08-04T19:24:00.000Z",
        "birthPlace": "檀香山，夏威夷",
        "latitude": 21.3099,
        "longitude": -157.8581,
        "notes": "第44任美國總統，有出生證書公開"
      },
      "description": "有出生證書公開，是現代政治人物中占星分析最多的案例之一。",
      "category": "politician",
      "recommendedTopics": ["領導力分析", "政治天賦", "公眾魅力", "演說能力"],
      "isPopular": true
    },
    {
      "name": "泰勒・斯威夫特",
      "birthData": {
        "id": "celebrity_taylor_swift",
        "name": "泰勒・斯威夫特",
        "dateTime": "1989-12-13T05:17:00.000Z",
        "birthPlace": "賓州，美國",
        "latitude": 40.2732,
        "longitude": -76.8839,
        "notes": "流行音樂天后，創作歌手"
      },
      "description": "情感生活與創作高度同步，適合戀愛線分析。",
      "category": "entertainment",
      "recommendedTopics": ["創作天賦", "感情模式", "音樂才華", "公眾形象"],
      "isPopular": true
    },
    {
      "name": "卡爾・榮格",
      "birthData": {
        "id": "celebrity_carl_jung",
        "name": "卡爾・榮格",
        "dateTime": "1875-07-26T19:32:00.000Z",
        "birthPlace": "瑞士",
        "latitude": 47.3769,
        "longitude": 8.5417,
        "notes": "心理學家，分析心理學創始人"
      },
      "description": "與占星有密切淵源，常用來分析心理占星。",
      "category": "scholar",
      "recommendedTopics": ["心理洞察", "集體無意識", "原型理論", "靈性探索"],
      "isPopular": true
    }
  ]
}''',
    'ai_api_keys': '''
{
  "selectedModelId": "",
  "OpenAIKey": {
    "ios": "",
    "android": "",
    "web": "",
    "macos": "",
    "windows": ""
  },
  "GroqAIKey": {
    "ios": "",
    "android": "",
    "web": "",
    "macos": "",
    "windows": ""
  },
  "GoogleGeminiKey": {
    "ios": "",
    "android": "",
    "web": "",
    "macos": "",
    "windows": ""
  }
}''',
    'ai_model_configs': '''
{
  "models": [
    {
      "id": "gpt-4.1",
      "name": "gpt-4.1",
      "provider": "openai",
      "endpoint": "https://api.openai.com/v1/chat/completions",
      "maxTokens": 16000,
      "temperature": 0.7,
      "enabled": true
    },
    {
      "id": "gpt-4o",
      "name": "gpt-4o",
      "provider": "openai",
      "endpoint": "https://api.openai.com/v1/chat/completions",
      "maxTokens": 16000,
      "temperature": 0.7,
      "enabled": true
    },
    {
      "id": "gemini-2.0-flash",
      "name": "Gemini 2.0 Flash",
      "provider": "gemini",
      "endpoint": "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent",
      "maxTokens": 16000,
      "temperature": 0.7,
      "enabled": true
    }
  ]
}''',
    'products_config': '''
{
  "interpretation_packages": [
    {
      "id": "single_1",
      "name": "單次解讀",
      "description": "購買 1 次解讀機會",
      "price": 99,
      "currency": "TWD",
      "count": 1,
      "type": "single",
      "popular": false,
      "enabled": false
    },
    {
      "id": "package_5",
      "name": "5次解讀包",
      "description": "購買 5 次解讀機會，比單次購買更優惠",
      "price": 399,
      "currency": "TWD",
      "count": 5,
      "type": "package",
      "popular": true,
      "enabled": false
    },
    {
      "id": "package_10",
      "name": "10次解讀包",
      "description": "購買 10 次解讀機會，最優惠的選擇",
      "price": 699,
      "currency": "TWD",
      "count": 10,
      "type": "package",
      "popular": false,
      "enabled": false
    }
  ],
  "subscription_plans": [
    {
      "id": "monthly",
      "name": "月度會員",
      "description": "無限次解讀，按月付費",
      "price": 299,
      "currency": "TWD",
      "duration": 1,
      "type": "subscription",
      "popular": false,
      "enabled": false
    },
    {
      "id": "yearly",
      "name": "年度會員",
      "description": "無限次解讀，按年付費，享受最大優惠",
      "price": 2999,
      "currency": "TWD",
      "duration": 12,
      "type": "subscription",
      "popular": true,
      "enabled": false
    }
  ]
}''',
    'youtube_videos': '''
{
  "categories": [
    {
      "id": "astrology_basics",
      "name": "占星基礎",
      "description": "占星學入門知識與基本概念",
      "videos": [
        {
          "id": "astro_basic_001",
          "title": "占星學入門：什麼是星盤？",
          "description": "了解星盤的基本構成要素，包括行星、星座、宮位的基本概念",
          "youtubeId": "dQw4w9WgXcQ",
          "thumbnailUrl": "https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg",
          "duration": "15:30",
          "publishedAt": "2024-01-15T10:00:00Z",
          "tags": ["入門", "星盤", "基礎"],
          "isPopular": true
        },
        {
          "id": "astro_basic_002",
          "title": "十二星座特質解析",
          "description": "深入了解十二星座的性格特質與象徵意義",
          "youtubeId": "dQw4w9WgXcQ",
          "thumbnailUrl": "https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg",
          "duration": "22:45",
          "publishedAt": "2024-01-20T14:30:00Z",
          "tags": ["星座", "性格", "特質"],
          "isPopular": false
        }
      ]
    },
    {
      "id": "chart_analysis",
      "name": "星盤解讀",
      "description": "實際星盤案例分析與解讀技巧",
      "videos": [
        {
          "id": "chart_001",
          "title": "如何解讀你的本命盤",
          "description": "step by step 教你解讀自己的本命盤，從行星位置到相位分析",
          "youtubeId": "dQw4w9WgXcQ",
          "thumbnailUrl": "https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg",
          "duration": "28:15",
          "publishedAt": "2024-02-01T16:00:00Z",
          "tags": ["本命盤", "解讀", "教學"],
          "isPopular": true
        }
      ]
    },
    {
      "id": "advanced_topics",
      "name": "進階主題",
      "description": "進階占星技巧與特殊主題探討",
      "videos": [
        {
          "id": "advanced_001",
          "title": "推運技巧：預測未來趨勢",
          "description": "學習使用推運技巧來預測個人未來的發展趨勢",
          "youtubeId": "dQw4w9WgXcQ",
          "thumbnailUrl": "https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg",
          "duration": "35:20",
          "publishedAt": "2024-02-15T18:00:00Z",
          "tags": ["推運", "預測", "進階"],
          "isPopular": false
        }
      ]
    }
  ],
  "lastUpdated": "2024-02-20T12:00:00Z"
}''',
    'current_events': '''
{
  "events": [
    {
      "id": "horary_001",
      "type": "horary",
      "title": "卜卦占星入門",
      "description": "學習如何使用卜卦占星來回答具體問題",
      "beginnerDescription": "卜卦占星是一種針對特定問題的占星技巧，當你有明確的疑問時，可以透過當下的星象來尋求指引。適合問感情、工作、失物等具體問題。",
      "eventDate": "2024-07-27T12:00:00Z",
      "iconPath": "assets/icons/horary.png",
      "keywords": ["卜卦", "問事", "占星", "指引"],
      "isActive": true
    },
    {
      "id": "solstice_spring_2024",
      "type": "solstice",
      "title": "2024春分能量分析",
      "description": "探索春分時刻的宇宙能量與個人影響",
      "beginnerDescription": "春分是一年中白天和黑夜等長的時刻，象徵新的開始、成長和希望。這是開始新計劃、種下新種子的最佳時機。",
      "eventDate": "2024-03-20T09:06:00Z",
      "iconPath": "assets/icons/solstice.png",
      "keywords": ["春分", "季節", "能量", "轉換", "新開始"],
      "isActive": false
    },
    {
      "id": "solstice_summer_2024",
      "type": "solstice",
      "title": "2024夏至能量高峰",
      "description": "分析夏至時刻的豐盛能量與創造力",
      "beginnerDescription": "夏至是一年中白天最長的日子，代表豐盛、活力和創造力的高峰。適合慶祝成果、展現才華的時刻。",
      "eventDate": "2024-06-21T04:51:00Z",
      "iconPath": "assets/icons/solstice.png",
      "keywords": ["夏至", "豐盛", "創造力", "活力"],
      "isActive": false
    },
    {
      "id": "eclipse_lunar_2024_09",
      "type": "eclipse",
      "title": "2024年9月月蝕影響",
      "description": "分析月蝕對個人和集體意識的深層影響",
      "beginnerDescription": "月蝕代表結束、釋放和轉化。這是一個讓我們放下不再需要的東西，為新的成長騰出空間的強大時刻。",
      "eventDate": "2024-09-18T02:34:00Z",
      "iconPath": "assets/icons/eclipse.png",
      "keywords": ["月蝕", "轉化", "釋放", "情感", "結束"],
      "isActive": true
    },
    {
      "id": "eclipse_solar_2024_10",
      "type": "eclipse",
      "title": "2024年10月日蝕新機",
      "description": "探索日蝕帶來的新開始與機會",
      "beginnerDescription": "日蝕象徵新的開始、機會和可能性。日蝕帶來的能量通常會在未來6個月內逐漸展現，是種下新願望的絕佳時機。",
      "eventDate": "2024-10-02T18:49:00Z",
      "iconPath": "assets/icons/eclipse.png",
      "keywords": ["日蝕", "新開始", "機會", "願望", "可能性"],
      "isActive": true
    }
  ],
  "lastUpdated": "2024-07-27T12:00:00Z"
}'''
  };
  
  /// 初始化 Remote Config
  static Future<void> initialize() async {
    try {
      logger.i('開始初始化 Remote Config...');

      // 檢查 Firebase 是否可用
      try {
        _remoteConfig = FirebaseRemoteConfig.instance;
        logger.i('Firebase Remote Config 實例創建成功');
      } catch (e) {
        logger.e('Firebase Remote Config 實例創建失敗: $e');
        _isInitialized = false;
        return;
      }

      // 設置配置設定（優化啟動速度）
      await _remoteConfig!.setConfigSettings(RemoteConfigSettings(
        fetchTimeout: const Duration(seconds: 5), // 減少超時時間
        minimumFetchInterval: kDebugMode
            ? const Duration(seconds: 1)   // 開發環境
            : const Duration(seconds: 5), // 生產環境
      ));

      // 設置默認值
      await _remoteConfig!.setDefaults(_defaultConfig);
      logger.i('Remote Config 默認值設置完成');

      logger.i('Remote Config 設定完成，開始獲取配置...');

      // 獲取並激活配置（帶重試機制）
      bool fetchSuccess = await _fetchAndActivateWithRetry();

      if (fetchSuccess) {
        // 檢查是否成功獲取到非空配置
        final configValue = _remoteConfig!.getString('ai_api_keys');
        if (configValue.isEmpty) {
          logger.w('Remote Config 獲取的配置為空，但初始化仍視為成功（將使用默認值）');
        } else {
          logger.i('成功獲取到 Remote Config 配置 (長度: ${configValue.length})');
        }
      } else {
        logger.w('Remote Config 獲取失敗，但初始化仍視為成功（將使用默認值）');
      }

      _isInitialized = true;
      logger.i('Remote Config 初始化完成');

    } catch (e) {
      logger.e('Remote Config 初始化失敗: $e');
      logger.e('錯誤堆疊: ${StackTrace.current}');

      // 即使初始化失敗，也設置為已初始化，這樣可以使用默認配置
      _isInitialized = true;
      logger.w('Remote Config 初始化失敗，但設置為已初始化以使用默認配置');
    }
  }

  /// 獲取並激活配置（帶重試機制，優化啟動速度）
  static Future<bool> _fetchAndActivateWithRetry({int maxRetries = 2}) async {
    for (int attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        logger.i('嘗試獲取 Remote Config (第 $attempt/$maxRetries 次)...');

        // 設置較短的超時時間
        bool success = await _fetchAndActivate().timeout(
          const Duration(seconds: 5),
          onTimeout: () {
            logger.w('Remote Config 獲取超時');
            return false;
          },
        );

        if (success) {
          logger.i('Remote Config 獲取成功 (第 $attempt 次嘗試)');
          return true;
        }

        if (attempt < maxRetries) {
          final delay = Duration(seconds: 1); // 減少延遲時間
          logger.w('第 $attempt 次獲取失敗，${delay.inSeconds}秒後重試...');
          await Future.delayed(delay);
        }
      } catch (e) {
        logger.e('第 $attempt 次獲取 Remote Config 失敗: $e');
        if (attempt < maxRetries) {
          final delay = Duration(seconds: 1);
          logger.w('${delay.inSeconds}秒後重試...');
          await Future.delayed(delay);
        }
      }
    }

    logger.w('Remote Config 獲取失敗，已嘗試 $maxRetries 次，將使用默認配置');
    return false;
  }

  /// 獲取並激活配置
  static Future<bool> _fetchAndActivate() async {
    try {
      logger.i('開始獲取 Remote Config...');

      // 檢查獲取前的狀態
      final beforeFetch = _remoteConfig!.lastFetchStatus;
      final beforeTime = _remoteConfig!.lastFetchTime;
      logger.i('獲取前狀態: $beforeFetch, 時間: $beforeTime');

      final bool updated = await _remoteConfig!.fetchAndActivate();

      // 檢查獲取後的狀態
      final afterFetch = _remoteConfig!.lastFetchStatus;
      final afterTime = _remoteConfig!.lastFetchTime;
      logger.i('獲取後狀態: $afterFetch, 時間: $afterTime');

      // 檢查實際配置內容
      final configValue = _remoteConfig!.getString('ai_api_keys');
      logger.i('獲取到的配置長度: ${configValue.length}');

      if (updated) {
        logger.i('Remote Config 配置已更新');
      } else {
        logger.i('Remote Config 配置無變化');

        // 如果沒有更新但配置為空，可能是首次獲取失敗
        if (configValue.isEmpty) {
          logger.w('配置無變化且為空，可能是獲取失敗');
        }
      }

      return updated;
    } catch (e) {
      logger.e('獲取 Remote Config 失敗: $e');
      logger.e('錯誤類型: ${e.runtimeType}');

      // 檢查是否是網路問題
      if (e.toString().contains('network') || e.toString().contains('timeout')) {
        logger.e('可能是網路連接問題');
      }

      return false;
    }
  }
  
  /// 手動刷新配置
  static Future<bool> refresh() async {
    if (!_isInitialized || _remoteConfig == null) {
      logger.w('Remote Config 未初始化，無法刷新');
      return false;
    }

    try {
      logger.i('手動刷新 Remote Config...');
      return await _fetchAndActivate();
    } catch (e) {
      logger.e('手動刷新 Remote Config 失敗: $e');
      return false;
    }
  }

  /// 強制獲取配置（忽略間隔限制）
  static Future<bool> forceRefresh() async {
    if (!_isInitialized || _remoteConfig == null) {
      logger.w('Remote Config 未初始化，無法強制刷新');
      return false;
    }

    try {
      logger.i('強制刷新 Remote Config（忽略間隔限制）...');

      // 直接調用 fetch 和 activate，忽略間隔限制
      await _remoteConfig!.fetch();
      await _remoteConfig!.activate();

      // 檢查結果
      final configValue = _remoteConfig!.getString('ai_api_keys');
      logger.i('強制刷新後配置長度: ${configValue.length}');

      if (configValue.isNotEmpty) {
        logger.i('強制刷新成功，獲取到配置');
        return true;
      } else {
        logger.w('強制刷新後配置仍為空');
        return false;
      }
    } catch (e) {
      logger.e('強制刷新 Remote Config 失敗: $e');
      return false;
    }
  }
  
  /// 獲取當前平台標識
  static String _getCurrentPlatform() {
    if (kIsWeb) {
      return 'web';
    } else if (Platform.isIOS) {
      return 'ios';
    } else if (Platform.isAndroid) {
      return 'android';
    } else if (Platform.isMacOS) {
      return 'macos';
    } else if (Platform.isWindows) {
      return 'windows';
    } else if (Platform.isLinux) {
      return 'linux';
    } else {
      return 'unknown';
    }
  }
  
  /// 獲取 AI API Keys 配置
  static Map<String, dynamic> getAIApiKeys() {
    logger.d('=== 獲取 AI API Keys 配置 ===');
    logger.d('Remote Config 初始化狀態: $_isInitialized');
    logger.d('Remote Config 實例: ${_remoteConfig != null}');

    if (!_isInitialized) {
      logger.i('Remote Config 未初始化，使用默認 AI API Keys 配置');
      try {
        final defaultConfig = jsonDecode(_defaultConfig['ai_api_keys'] as String) as Map<String, dynamic>;
        logger.d('成功載入默認 AI API Keys 配置');
        return defaultConfig;
      } catch (e) {
        logger.e('解析默認 AI API Keys 配置失敗: $e');
        return {};
      }
    }

    if (_remoteConfig == null) {
      logger.w('Remote Config 實例為空，使用默認 AI API Keys 配置');
      try {
        final defaultConfig = jsonDecode(_defaultConfig['ai_api_keys'] as String) as Map<String, dynamic>;
        logger.d('成功載入默認 AI API Keys 配置');
        return defaultConfig;
      } catch (e) {
        logger.e('解析默認 AI API Keys 配置失敗: $e');
        return {};
      }
    }

    try {
      final String configJson = _remoteConfig!.getString('ai_api_keys');
      logger.i('Remote Config 原始值長度: ${configJson.length}');
      logger.i('Remote Config 原始值前100字符: ${configJson.length > 100 ? configJson.substring(0, 100) : configJson}...');

      if (configJson.isEmpty) {
        logger.w('Remote Config 中 ai_api_keys 為空，使用默認配置');
        final defaultConfig = jsonDecode(_defaultConfig['ai_api_keys'] as String) as Map<String, dynamic>;
        logger.i('使用默認配置: ${defaultConfig.toString()}');
        return defaultConfig;
      }

      final Map<String, dynamic> config = jsonDecode(configJson) as Map<String, dynamic>;
      logger.i('成功解析 Remote Config，配置鍵: ${config.keys.toList()}');

      // 檢查 OpenAI 配置
      if (config.containsKey('OpenAIKey')) {
        final openAIConfig = config['OpenAIKey'];
        logger.i('OpenAI 配置類型: ${openAIConfig.runtimeType}');
        if (openAIConfig is Map) {
          logger.i('OpenAI 配置平台: ${openAIConfig.keys.toList()}');
          // 檢查 iOS 平台
          if (openAIConfig.containsKey('ios')) {
            final iosKey = openAIConfig['ios'];
            logger.i('iOS Key 類型: ${iosKey.runtimeType}');
            logger.i('iOS Key 長度: ${iosKey?.toString().length ?? 0}');
            logger.i('iOS Key 前20字符: ${iosKey?.toString().substring(0, iosKey.toString().length > 20 ? 20 : iosKey.toString().length)}...');
          } else {
            logger.w('OpenAI 配置中沒有 ios 平台');
          }
        }
      } else {
        logger.w('配置中沒有 OpenAIKey');
      }

      logger.i('=== AI API Keys 配置獲取完成 ===');
      return config;
    } catch (e) {
      logger.e('解析 AI API Keys 配置失敗: $e');
      logger.e('錯誤堆疊: ${StackTrace.current}');
      try {
        final defaultConfig = jsonDecode(_defaultConfig['ai_api_keys'] as String) as Map<String, dynamic>;
        logger.i('降級使用默認配置: ${defaultConfig.toString()}');
        return defaultConfig;
      } catch (e2) {
        logger.e('解析默認配置也失敗: $e2');
        return {};
      }
    }
  }
  
  /// 獲取 OpenAI API Key
  static String getOpenAIKey() {
    final config = getAIApiKeys();
    final platform = _getCurrentPlatform();

    // 詳細調試信息
    logger.i('=== OpenAI Key 獲取調試 ===');
    logger.i('當前平台: $platform');
    logger.i('Remote Config 初始化狀態: $_isInitialized');
    logger.i('配置內容: ${config.toString()}');

    try {
      final openAIConfig = config['OpenAIKey'] as Map<String, dynamic>?;
      if (openAIConfig == null) {
        logger.w('OpenAI 配置不存在');
        logger.i('可用的配置鍵: ${config.keys.toList()}');
        return '';
      }

      logger.i('OpenAI 配置: ${openAIConfig.toString()}');
      logger.i('可用的平台: ${openAIConfig.keys.toList()}');

      final key = openAIConfig[platform] as String? ?? '';
      logger.i('平台 $platform 的原始值: ${openAIConfig[platform]}');
      logger.i('轉換後的 Key 長度: ${key.length}');

      if (key.isEmpty) {
        logger.w('OpenAI Key 在平台 $platform 上不存在或為空');
        // 檢查是否有其他平台的 Key
        for (final entry in openAIConfig.entries) {
          final platformName = entry.key;
          final platformKey = entry.value;
          logger.d('平台 $platformName: ${platformKey?.toString().isNotEmpty == true ? '有值' : '空值'}');
        }
      } else {
        logger.d('成功獲取 OpenAI Key (平台: $platform, 長度: ${key.length})');
      }

      logger.d('=== OpenAI Key 獲取調試結束 ===');
      return key;
    } catch (e) {
      logger.e('獲取 OpenAI Key 失敗: $e');
      logger.e('錯誤堆疊: ${StackTrace.current}');
      return '';
    }
  }
  
  /// 獲取 Groq AI API Key
  static String getGroqAIKey() {
    final config = getAIApiKeys();
    final platform = _getCurrentPlatform();
    
    try {
      final groqConfig = config['GroqAIKey'] as Map<String, dynamic>?;
      if (groqConfig == null) {
        logger.w('GroqAI 配置不存在');
        return '';
      }
      
      final key = groqConfig[platform] as String? ?? '';
      if (key.isEmpty) {
        logger.w('GroqAI Key 在平台 $platform 上不存在或為空');
      } else {
        logger.d('成功獲取 GroqAI Key (平台: $platform)');
      }
      
      return key;
    } catch (e) {
      logger.e('獲取 GroqAI Key 失敗: $e');
      return '';
    }
  }
  
  /// 獲取 Google Gemini API Key
  static String getGoogleGeminiKey() {
    final config = getAIApiKeys();
    final platform = _getCurrentPlatform();

    try {
      final geminiConfig = config['GoogleGeminiKey'] as Map<String, dynamic>?;
      if (geminiConfig == null) {
        logger.w('Google Gemini 配置不存在');
        return '';
      }

      final key = geminiConfig[platform] as String? ?? '';
      if (key.isEmpty) {
        logger.w('Google Gemini Key 在平台 $platform 上不存在或為空');
      } else {
        logger.d('成功獲取 Google Gemini Key (平台: $platform)');
      }

      return key;
    } catch (e) {
      logger.e('獲取 Google Gemini Key 失敗: $e');
      return '';
    }
  }

  /// 獲取選擇的 AI 模型 ID
  static String getSelectedModelId() {
    final config = getAIApiKeys();

    try {
      final selectedModelId = config['selectedModelId'] as String? ?? '';

      if (selectedModelId.isEmpty) {
        logger.w('selectedModelId 為空，使用默認值');
        return '';
      }

      logger.d('成功獲取選擇的模型 ID: $selectedModelId');
      return selectedModelId;
    } catch (e) {
      logger.e('獲取選擇的模型 ID 失敗: $e，使用默認值');
      return '';
    }
  }

  /// 獲取 AI 模型配置
  static Map<String, dynamic> getAIModelConfigs() {
    logger.d('=== 獲取 AI 模型配置 ===');
    logger.d('Remote Config 初始化狀態: $_isInitialized');

    if (!_isInitialized || _remoteConfig == null) {
      logger.i('Remote Config 未初始化，使用默認 AI 模型配置');
      try {
        final defaultConfig = jsonDecode(_defaultConfig['ai_model_configs'] as String) as Map<String, dynamic>;
        final modelCount = (defaultConfig['models'] as List?)?.length ?? 0;
        logger.d('成功載入默認 AI 模型配置，包含 $modelCount 個模型');
        return defaultConfig;
      } catch (e) {
        logger.e('解析默認 AI 模型配置失敗: $e');
        return {'models': []};
      }
    }

    try {
      final String configJson = _remoteConfig!.getString('ai_model_configs');
      logger.d('Remote Config 模型配置原始值長度: ${configJson.length}');

      if (configJson.isEmpty) {
        logger.w('Remote Config 中 ai_model_configs 為空，使用默認配置');
        final defaultConfig = jsonDecode(_defaultConfig['ai_model_configs'] as String) as Map<String, dynamic>;
        logger.d('使用默認模型配置: ${defaultConfig.toString()}');
        return defaultConfig;
      }

      final Map<String, dynamic> config = jsonDecode(configJson) as Map<String, dynamic>;
      logger.d('成功解析 Remote Config 模型配置，模型數量: ${(config['models'] as List?)?.length ?? 0}');

      logger.d('=== AI 模型配置獲取完成 ===');
      return config;
    } catch (e) {
      logger.e('解析 AI 模型配置失敗: $e');
      logger.e('錯誤堆疊: ${StackTrace.current}');
      try {
        final defaultConfig = jsonDecode(_defaultConfig['ai_model_configs'] as String) as Map<String, dynamic>;
        logger.d('降級使用默認模型配置: ${defaultConfig.toString()}');
        return defaultConfig;
      } catch (e2) {
        logger.e('解析默認模型配置也失敗: $e2');
        return {'models': []};
      }
    }
  }

  /// 獲取啟用的 AI 模型列表
  static List<Map<String, dynamic>> getEnabledAIModels() {
    final config = getAIModelConfigs();
    final models = config['models'] as List<dynamic>? ?? [];

    return models
        .cast<Map<String, dynamic>>()
        .where((model) => model['enabled'] == true)
        .toList();
  }

  /// 獲取版本配置
  static Map<String, dynamic> getAllVersions() {
    if (!_isInitialized || _remoteConfig == null) {
      logger.w('Remote Config 未初始化，使用默認版本配置');
      return {};
    }

    try {
      final String configJson = _remoteConfig!.getString('all_versions');
      if (configJson.isEmpty) {
        logger.w('Remote Config 中 all_versions 為空');
        return {};
      }

      final Map<String, dynamic> config = jsonDecode(configJson) as Map<String, dynamic>;
      logger.d('成功獲取版本配置');
      return config;
    } catch (e) {
      logger.e('解析版本配置失敗: $e');
      return {};
    }
  }

  /// 獲取 YouTube 影片配置
  static Map<String, dynamic> getYouTubeVideosConfig() {
    logger.d('=== 獲取 YouTube 影片配置 ===');
    logger.d('Remote Config 初始化狀態: $_isInitialized');

    if (!_isInitialized || _remoteConfig == null) {
      logger.i('Remote Config 未初始化，使用默認 YouTube 影片配置');
      try {
        final defaultConfig = jsonDecode(_defaultConfig['youtube_videos'] as String) as Map<String, dynamic>;
        final categoryCount = (defaultConfig['categories'] as List?)?.length ?? 0;
        logger.d('成功載入默認 YouTube 影片配置，包含 $categoryCount 個分類');
        return defaultConfig;
      } catch (e) {
        logger.e('解析默認 YouTube 影片配置失敗: $e');
        return {'categories': [], 'lastUpdated': DateTime.now().toIso8601String()};
      }
    }

    try {
      final String configJson = _remoteConfig!.getString('youtube_videos');
      logger.d('Remote Config YouTube 影片配置原始值長度: ${configJson.length}');

      if (configJson.isEmpty) {
        logger.w('Remote Config 中 youtube_videos 為空，使用默認配置');
        final defaultConfig = jsonDecode(_defaultConfig['youtube_videos'] as String) as Map<String, dynamic>;
        logger.d('使用默認 YouTube 影片配置: ${defaultConfig.toString()}');
        return defaultConfig;
      }

      final Map<String, dynamic> config = jsonDecode(configJson) as Map<String, dynamic>;
      logger.d('成功解析 Remote Config YouTube 影片配置，分類數量: ${(config['categories'] as List?)?.length ?? 0}');

      logger.d('=== YouTube 影片配置獲取完成 ===');
      return config;
    } catch (e) {
      logger.e('解析 YouTube 影片配置失敗: $e');
      logger.e('錯誤堆疊: ${StackTrace.current}');
      try {
        final defaultConfig = jsonDecode(_defaultConfig['youtube_videos'] as String) as Map<String, dynamic>;
        logger.d('降級使用默認 YouTube 影片配置: ${defaultConfig.toString()}');
        return defaultConfig;
      } catch (e2) {
        logger.e('解析默認 YouTube 影片配置也失敗: $e2');
        return {'categories': [], 'lastUpdated': DateTime.now().toIso8601String()};
      }
    }
  }

  /// 獲取當前平台的版本信息
  static Map<String, dynamic>? getCurrentPlatformVersion() {
    final allVersions = getAllVersions();
    final platform = _getCurrentPlatform();

    try {
      final platformVersion = allVersions[platform] as Map<String, dynamic>?;
      if (platformVersion == null) {
        logger.w('平台 $platform 的版本配置不存在');
        return null;
      }

      logger.d('成功獲取平台 $platform 的版本配置');
      return platformVersion;
    } catch (e) {
      logger.e('獲取平台版本配置失敗: $e');
      return null;
    }
  }

  /// 獲取商品配置
  static Map<String, dynamic> getProductsConfig() {
    if (!_isInitialized || _remoteConfig == null) {
      logger.i('Remote Config 未初始化，使用默認商品配置');
      return _getDefaultProductsConfig();
    }

    try {
      String configJson = _remoteConfig!.getString('products_config');

      // 開發環境優先使用開發配置
      if (kDebugMode) {
        final devConfigJson = _remoteConfig!.getString('products_config_dev');
        if (devConfigJson.isNotEmpty) {
          configJson = devConfigJson;
          logger.d('開發環境：使用 products_config_dev 配置');
        } else {
          logger.d('開發環境：products_config_dev 為空，使用 products_config');
        }
      }

      if (configJson.isEmpty) {
        logger.i('Remote Config 中商品配置為空，使用默認配置');
        return _getDefaultProductsConfig();
      }

      final Map<String, dynamic> config = jsonDecode(configJson) as Map<String, dynamic>;
      logger.d('成功獲取 Remote Config 商品配置');
      return config;
    } catch (e) {
      logger.e('解析商品配置失敗: $e');
      logger.i('降級使用默認商品配置');
      return _getDefaultProductsConfig();
    }
  }

  /// 獲取默認商品配置
  static Map<String, dynamic> _getDefaultProductsConfig() {
    // 檢查是否為未發布環境（開發/測試環境）
    final bool isUnpublishedEnvironment = kDebugMode || _isTestEnvironment();

    return {
      'interpretation_packages': isUnpublishedEnvironment ? _getUnpublishedPackages() : _getPublishedPackages(),
      'subscription_plans': isUnpublishedEnvironment ? _getUnpublishedPlans() : _getPublishedPlans(),
      'fallback_options': _getFallbackOptions(), // 當商品為空時的替代方案
    };
  }

  /// 檢查是否為測試環境
  static bool _isTestEnvironment() {
    // 可以根據需要添加更多環境檢查邏輯
    return Platform.environment.containsKey('FLUTTER_TEST') ||
           Platform.environment['ENVIRONMENT'] == 'test';
  }

  /// 獲取已發布環境的商品包
  static List<Map<String, dynamic>> _getPublishedPackages() {
    return [
      {
        'id': 'single_1',
        'name': '單次解讀',
        'description': '購買 1 次 AI 解讀機會',
        'price': 99,
        'currency': 'TWD',
        'count': 1,
        'type': 'single',
        'popular': false,
        'enabled': true,
      },
      {
        'id': 'package_5',
        'name': '5次解讀包',
        'description': '購買 5 次 AI 解讀機會，比單次購買更優惠',
        'price': 399,
        'currency': 'TWD',
        'count': 5,
        'type': 'package',
        'popular': true,
        'enabled': true,
      },
      {
        'id': 'package_10',
        'name': '10次解讀包',
        'description': '購買 10 次 AI 解讀機會，最優惠的選擇',
        'price': 699,
        'currency': 'TWD',
        'count': 10,
        'type': 'package',
        'popular': false,
        'enabled': true,
      },
    ];
  }

  /// 獲取未發布環境的商品包（測試用）
  static List<Map<String, dynamic>> _getUnpublishedPackages() {
    return [
      {
        'id': 'test_single_1',
        'name': '測試單次解讀',
        'description': '測試環境：購買 1 次 AI 解讀機會',
        'price': 1,
        'currency': 'TWD',
        'count': 1,
        'type': 'single',
        'popular': false,
        'enabled': true,
      },
      {
        'id': 'test_package_5',
        'name': '測試5次解讀包',
        'description': '測試環境：購買 5 次 AI 解讀機會',
        'price': 5,
        'currency': 'TWD',
        'count': 5,
        'type': 'package',
        'popular': true,
        'enabled': true,
      },
      {
        'id': 'test_package_unlimited',
        'name': '測試無限解讀包',
        'description': '測試環境：無限次 AI 解讀機會',
        'price': 10,
        'currency': 'TWD',
        'count': 999,
        'type': 'package',
        'popular': false,
        'enabled': true,
      },
    ];
  }

  /// 獲取已發布環境的訂閱方案
  static List<Map<String, dynamic>> _getPublishedPlans() {
    return [
      {
        'id': 'monthly',
        'name': '月度會員',
        'description': '無限次解讀，按月付費',
        'price': 299,
        'currency': 'TWD',
        'duration': 1,
        'type': 'subscription',
        'popular': false,
        'enabled': true,
      },
      {
        'id': 'yearly',
        'name': '年度會員',
        'description': '無限次解讀，按年付費，享受最大優惠',
        'price': 2999,
        'currency': 'TWD',
        'duration': 12,
        'type': 'subscription',
        'popular': true,
        'enabled': true,
      },
    ];
  }

  /// 獲取未發布環境的訂閱方案（測試用）
  static List<Map<String, dynamic>> _getUnpublishedPlans() {
    return [
      {
        'id': 'test_monthly',
        'name': '測試月度會員',
        'description': '測試環境：無限次解讀，按月付費',
        'price': 1,
        'currency': 'TWD',
        'duration': 1,
        'type': 'subscription',
        'popular': false,
        'enabled': true,
      },
      {
        'id': 'test_yearly',
        'name': '測試年度會員',
        'description': '測試環境：無限次解讀，按年付費',
        'price': 10,
        'currency': 'TWD',
        'duration': 12,
        'type': 'subscription',
        'popular': true,
        'enabled': true,
      },
    ];
  }

  /// 獲取替代購買方案（當商品為空時使用）
  static List<Map<String, dynamic>> _getFallbackOptions() {
    return [
      {
        'id': 'fallback_free_trial',
        'name': '免費體驗',
        'description': '獲得 3 次免費解讀機會',
        'price': 0,
        'currency': 'TWD',
        'count': 3,
        'type': 'free_trial',
        'popular': true,
        'enabled': true,
      },
      {
        'id': 'fallback_contact_support',
        'name': '聯繫客服',
        'description': '聯繫客服獲得購買協助',
        'price': 0,
        'currency': 'TWD',
        'count': 0,
        'type': 'contact_support',
        'popular': false,
        'enabled': true,
      },
      {
        'id': 'fallback_web_purchase',
        'name': '網頁購買',
        'description': '前往官網進行購買',
        'price': 0,
        'currency': 'TWD',
        'count': 0,
        'type': 'web_purchase',
        'popular': false,
        'enabled': true,
      },
    ];
  }

  /// 獲取解讀次數包商品
  static List<Map<String, dynamic>> getInterpretationPackages() {
    final config = getProductsConfig();
    final packages = config['interpretation_packages'] as List<dynamic>? ?? [];

    return packages
        .cast<Map<String, dynamic>>()
        .where((package) => package['enabled'] == true)
        .toList();
  }

  /// 獲取訂閱方案
  static List<Map<String, dynamic>> getSubscriptionPlans() {
    final config = getProductsConfig();
    final plans = config['subscription_plans'] as List<dynamic>? ?? [];

    return plans
        .cast<Map<String, dynamic>>()
        .where((plan) => plan['enabled'] == true)
        .toList();
  }

  /// 獲取替代購買方案（當正常商品不可用時）
  static List<Map<String, dynamic>> getFallbackOptions() {
    final config = getProductsConfig();
    final fallbackOptions = config['fallback_options'] as List<dynamic>? ?? _getFallbackOptions();

    return fallbackOptions
        .cast<Map<String, dynamic>>()
        .where((option) => option['enabled'] == true)
        .toList();
  }

  /// 檢查是否為開發/測試環境
  static bool isUnpublishedEnvironment() {
    return kDebugMode || _isTestEnvironment();
  }
  
  /// 獲取所有 API Keys（用於調試）
  static Map<String, String> getAllApiKeys() {
    return {
      'openai': getOpenAIKey(),
      'groq': getGroqAIKey(),
      'gemini': getGoogleGeminiKey(),
    };
  }
  
  /// 獲取初始化狀態
  static bool get isInitialized => _isInitialized;

  /// 檢查 Remote Config 狀態
  static Map<String, dynamic> getStatus() {
    return {
      'isInitialized': _isInitialized,
      'hasInstance': _remoteConfig != null,
      'platform': _getCurrentPlatform(),
      'lastFetchTime': _remoteConfig?.lastFetchTime.toIso8601String(),
      'lastFetchStatus': _remoteConfig?.lastFetchStatus.toString(),
      'configSettings': {
        'fetchTimeout': _remoteConfig?.settings.fetchTimeout.inSeconds,
        'minimumFetchInterval': _remoteConfig?.settings.minimumFetchInterval.inHours,
      },
    };
  }

  /// 檢查 Remote Config 健康狀態
  static Map<String, dynamic> getHealthStatus() {
    final status = getStatus();
    final bool isHealthy = _isInitialized && _remoteConfig != null;

    Map<String, dynamic> healthInfo = {
      'isHealthy': isHealthy,
      'status': status,
      'configAvailability': {},
    };

    // 檢查各個配置的可用性
    try {
      final aiApiKeys = getAIApiKeys();
      healthInfo['configAvailability']['ai_api_keys'] = {
        'available': aiApiKeys.isNotEmpty,
        'keyCount': aiApiKeys.keys.length,
      };
    } catch (e) {
      healthInfo['configAvailability']['ai_api_keys'] = {
        'available': false,
        'error': e.toString(),
      };
    }

    try {
      final modelConfigs = getAIModelConfigs();
      final models = modelConfigs['models'] as List? ?? [];
      healthInfo['configAvailability']['ai_model_configs'] = {
        'available': models.isNotEmpty,
        'modelCount': models.length,
      };
    } catch (e) {
      healthInfo['configAvailability']['ai_model_configs'] = {
        'available': false,
        'error': e.toString(),
      };
    }

    try {
      final productsConfig = getProductsConfig();
      final packages = productsConfig['interpretation_packages'] as List? ?? [];
      healthInfo['configAvailability']['products_config'] = {
        'available': packages.isNotEmpty,
        'packageCount': packages.length,
      };
    } catch (e) {
      healthInfo['configAvailability']['products_config'] = {
        'available': false,
        'error': e.toString(),
      };
    }

    return healthInfo;
  }
  
  /// 獲取配置值（通用方法）
  static String getConfigValue(String key, {String defaultValue = ''}) {
    if (!_isInitialized || _remoteConfig == null) {
      logger.w('Remote Config 未初始化，返回默認值');
      return defaultValue;
    }
    
    try {
      final value = _remoteConfig!.getString(key);
      return value.isEmpty ? defaultValue : value;
    } catch (e) {
      logger.e('獲取配置值 $key 失敗: $e');
      return defaultValue;
    }
  }
  
  /// 獲取布爾配置值
  static bool getBoolValue(String key, {bool defaultValue = false}) {
    if (!_isInitialized || _remoteConfig == null) {
      return defaultValue;
    }
    
    try {
      return _remoteConfig!.getBool(key);
    } catch (e) {
      logger.e('獲取布爾配置值 $key 失敗: $e');
      return defaultValue;
    }
  }
  
  /// 獲取數字配置值
  static int getIntValue(String key, {int defaultValue = 0}) {
    if (!_isInitialized || _remoteConfig == null) {
      return defaultValue;
    }
    
    try {
      return _remoteConfig!.getInt(key);
    } catch (e) {
      logger.e('獲取數字配置值 $key 失敗: $e');
      return defaultValue;
    }
  }
  
  /// 獲取雙精度配置值
  static double getDoubleValue(String key, {double defaultValue = 0.0}) {
    if (!_isInitialized || _remoteConfig == null) {
      return defaultValue;
    }

    try {
      return _remoteConfig!.getDouble(key);
    } catch (e) {
      logger.e('獲取雙精度配置值 $key 失敗: $e');
      return defaultValue;
    }
  }

  /// 快速診斷 Remote Config 問題（用於調試）
  static void quickDiagnosis() {
    logger.i('=== Remote Config 快速診斷 ===');

    // 1. 基本狀態
    logger.i('初始化狀態: $_isInitialized');
    logger.i('Remote Config 實例: ${_remoteConfig != null}');
    logger.i('當前平台: ${_getCurrentPlatform()}');

    if (_remoteConfig != null) {
      logger.i('最後獲取時間: ${_remoteConfig!.lastFetchTime}');
      logger.i('最後獲取狀態: ${_remoteConfig!.lastFetchStatus}');
      logger.i('獲取間隔設定: ${_remoteConfig!.settings.minimumFetchInterval}');
    }

    // 2. 測試原始配置獲取
    try {
      final rawConfig = _remoteConfig?.getString('ai_api_keys') ?? '';
      logger.i('原始配置長度: ${rawConfig.length}');
      if (rawConfig.isNotEmpty) {
        logger.i('原始配置前100字符: ${rawConfig.substring(0, rawConfig.length > 100 ? 100 : rawConfig.length)}...');
      } else {
        logger.w('原始配置為空！');
      }
    } catch (e) {
      logger.e('獲取原始配置失敗: $e');
    }

    // 3. 測試解析後的配置
    try {
      final config = getAIApiKeys();
      logger.i('解析後配置鍵: ${config.keys.toList()}');

      if (config.containsKey('OpenAIKey')) {
        final openAIConfig = config['OpenAIKey'];
        if (openAIConfig is Map) {
          logger.i('OpenAI 平台: ${openAIConfig.keys.toList()}');
          final iosKey = openAIConfig['ios'];
          logger.i('iOS Key: ${iosKey != null ? '存在(長度:${iosKey.toString().length})' : '不存在'}');
        }
      }
    } catch (e) {
      logger.e('測試配置解析失敗: $e');
    }

    // 4. 測試實際獲取
    try {
      final openAIKey = getOpenAIKey();
      logger.i('實際獲取的 OpenAI Key: ${openAIKey.isNotEmpty ? '成功(長度:${openAIKey.length})' : '失敗(空值)'}');
    } catch (e) {
      logger.e('測試實際獲取失敗: $e');
    }

    logger.i('=== 快速診斷結束 ===');
  }

  /// 獲取 IAP 商品配置
  static Future<Map<String, dynamic>> getIAPProductConfig() async {
    try {
      if (!_isInitialized || _remoteConfig == null) {
        logger.w('Remote Config 未初始化，使用預設 IAP 商品配置');
        return {};
      }

      final configValue = _remoteConfig!.getString('iap_product_config');

      if (configValue.isNotEmpty) {
        final config = jsonDecode(configValue) as Map<String, dynamic>;
        logger.i('已獲取 IAP 商品配置');
        return config;
      } else {
        logger.w('IAP 商品配置為空，使用預設值');
        return {};
      }
    } catch (e) {
      logger.e('獲取 IAP 商品配置失敗: $e');
      return {};
    }
  }

  /// 獲取 Web 支付配置
  static Future<Map<String, dynamic>> getWebPaymentConfig() async {
    try {
      if (!_isInitialized || _remoteConfig == null) {
        logger.w('Remote Config 未初始化，使用預設 Web 支付配置');
        return {};
      }

      final configValue = _remoteConfig!.getString('web_payment_config');

      if (configValue.isNotEmpty) {
        final config = jsonDecode(configValue) as Map<String, dynamic>;
        logger.i('已獲取 Web 支付配置');
        return config;
      } else {
        logger.w('Web 支付配置為空，使用預設值');
        return {};
      }
    } catch (e) {
      logger.e('獲取 Web 支付配置失敗: $e');
      return {};
    }
  }

  /// 獲取 Web 商品配置
  static Future<Map<String, dynamic>> getWebProductConfig() async {
    try {
      if (!_isInitialized || _remoteConfig == null) {
        logger.w('Remote Config 未初始化，使用預設 Web 商品配置');
        return {};
      }

      final configValue = _remoteConfig!.getString('web_product_config');

      if (configValue.isNotEmpty) {
        final config = jsonDecode(configValue) as Map<String, dynamic>;
        logger.i('已獲取 Web 商品配置');
        return config;
      } else {
        logger.w('Web 商品配置為空，使用預設值');
        return {};
      }
    } catch (e) {
      logger.e('獲取 Web 商品配置失敗: $e');
      return {};
    }
  }

  /// 獲取時事分析配置
  static Map<String, dynamic> getCurrentEventsConfig() {
    logger.d('=== 獲取時事分析配置 ===');
    logger.d('Remote Config 初始化狀態: $_isInitialized');

    if (!_isInitialized || _remoteConfig == null) {
      logger.i('Remote Config 未初始化，使用預設時事分析配置');
      try {
        final defaultConfig = jsonDecode(_defaultConfig['current_events'] as String) as Map<String, dynamic>;
        final eventCount = (defaultConfig['events'] as List?)?.length ?? 0;
        logger.d('成功載入預設時事分析配置，包含 $eventCount 個事件');
        return defaultConfig;
      } catch (e) {
        logger.e('解析預設時事分析配置失敗: $e');
        return {'events': [], 'lastUpdated': DateTime.now().toIso8601String()};
      }
    }

    try {
      final String configJson = _remoteConfig!.getString('current_events');
      logger.d('Remote Config 時事分析配置原始值長度: ${configJson.length}');

      if (configJson.isEmpty) {
        logger.w('Remote Config 中 current_events 為空，使用預設配置');
        final defaultConfig = jsonDecode(_defaultConfig['current_events'] as String) as Map<String, dynamic>;
        logger.d('使用預設時事分析配置: ${defaultConfig.toString()}');
        return defaultConfig;
      }

      final Map<String, dynamic> config = jsonDecode(configJson) as Map<String, dynamic>;
      logger.d('成功解析 Remote Config 時事分析配置，事件數量: ${(config['events'] as List?)?.length ?? 0}');

      logger.d('=== 時事分析配置獲取完成 ===');
      return config;
    } catch (e) {
      logger.e('解析時事分析配置失敗: $e');
      logger.e('錯誤堆疊: ${StackTrace.current}');
      try {
        final defaultConfig = jsonDecode(_defaultConfig['current_events'] as String) as Map<String, dynamic>;
        logger.d('降級使用預設時事分析配置: ${defaultConfig.toString()}');
        return defaultConfig;
      } catch (e2) {
        logger.e('解析預設時事分析配置也失敗: $e2');
        return {'events': [], 'lastUpdated': DateTime.now().toIso8601String()};
      }
    }
  }
}
