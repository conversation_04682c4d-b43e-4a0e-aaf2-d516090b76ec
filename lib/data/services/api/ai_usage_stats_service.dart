import 'dart:convert';

import 'package:shared_preferences/shared_preferences.dart';

import '../../../core/utils/logger_utils.dart';
import '../../models/interpretation/ai_usage_stats.dart';
import 'ai_api_service.dart';

/// AI 使用統計服務
class AIUsageStatsService {
  static const String _statsKey = 'ai_usage_stats';
  static const String _lastResetDateKey = 'ai_usage_last_reset_date';

  /// 獲取今天的使用統計
  static Future<AIUsageStats> getTodayStats() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final today = AIUsageStats.getTodayDateString();
      
      // 檢查是否需要重置
      await _checkAndResetIfNeeded();
      
      final statsJson = prefs.getString(_statsKey);
      if (statsJson != null) {
        final stats = AIUsageStats.fromJson(jsonDecode(statsJson));
        
        // 如果日期不匹配，創建新的統計
        if (stats.shouldReset()) {
          final newStats = AIUsageStats.empty(today);
          await _saveStats(newStats);
          return newStats;
        }
        
        return stats;
      } else {
        // 創建新的統計
        final newStats = AIUsageStats.empty(today);
        await _saveStats(newStats);
        return newStats;
      }
    } catch (e) {
      logger.e("獲取今天統計失敗：$e");
      return AIUsageStats.empty(AIUsageStats.getTodayDateString());
    }
  }

  /// 記錄 AI 使用量
  static Future<void> recordUsage({
    required AIProvider provider,
    required int tokens,
  }) async {
    try {
      final currentStats = await getTodayStats();
      final updatedStats = currentStats.addUsage(
        provider: provider,
        tokens: tokens,
      );
      
      await _saveStats(updatedStats);
      logger.d("記錄 AI 使用量：${provider.displayName} - $tokens tokens");
    } catch (e) {
      logger.e("記錄 AI 使用量失敗：$e");
    }
  }

  /// 保存統計數據
  static Future<void> _saveStats(AIUsageStats stats) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_statsKey, jsonEncode(stats.toJson()));
      await prefs.setString(_lastResetDateKey, stats.date);
    } catch (e) {
      logger.e("保存統計數據失敗：$e");
    }
  }

  /// 檢查並重置統計（如果需要）
  static Future<void> _checkAndResetIfNeeded() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastResetDate = prefs.getString(_lastResetDateKey);
      final today = AIUsageStats.getTodayDateString();
      
      if (lastResetDate != today) {
        // 需要重置
        await resetStats();
        logger.i("AI 使用統計已重置，新日期：$today");
      }
    } catch (e) {
      logger.e("檢查重置失敗：$e");
    }
  }

  /// 手動重置統計
  static Future<void> resetStats() async {
    try {
      final today = AIUsageStats.getTodayDateString();
      final newStats = AIUsageStats.empty(today);
      await _saveStats(newStats);
      logger.i("AI 使用統計已手動重置");
    } catch (e) {
      logger.e("重置統計失敗：$e");
    }
  }

  /// 清除所有統計數據
  static Future<void> clearAllStats() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_statsKey);
      await prefs.remove(_lastResetDateKey);
      logger.i("所有 AI 使用統計已清除");
    } catch (e) {
      logger.e("清除統計數據失敗：$e");
    }
  }

  /// 獲取歷史統計數據（未來擴展用）
  static Future<List<AIUsageStats>> getHistoryStats() async {
    // 目前只保存當天數據，未來可以擴展為保存多天歷史
    final todayStats = await getTodayStats();
    return [todayStats];
  }

  /// 估算 token 數量（基於文本長度）
  static int estimateTokens(String text) {
    // 簡單估算：英文約 4 字符 = 1 token，中文約 1.5 字符 = 1 token
    // 這是一個粗略估算，實際 token 數量可能有差異
    final chineseCharCount = text.runes.where((rune) {
      return rune >= 0x4E00 && rune <= 0x9FFF; // 中文字符範圍
    }).length;
    
    final otherCharCount = text.length - chineseCharCount;
    
    // 中文字符：1.5 字符 ≈ 1 token
    // 其他字符：4 字符 ≈ 1 token
    final estimatedTokens = (chineseCharCount / 1.5 + otherCharCount / 4).ceil();
    
    return estimatedTokens;
  }

  /// 估算請求的總 token 數量（包括輸入和輸出）
  static int estimateRequestTokens({
    required String prompt,
    required String response,
  }) {
    final promptTokens = estimateTokens(prompt);
    final responseTokens = estimateTokens(response);
    
    // 總 token = 輸入 token + 輸出 token
    return promptTokens + responseTokens;
  }

  /// 獲取提供商的顏色
  static int getProviderColor(AIProvider provider) {
    switch (provider) {
      case AIProvider.openai:
        return 0xFF10A37F; // OpenAI 綠色
      case AIProvider.anthropic:
        return 0xFFD97706; // Anthropic 橙色
      case AIProvider.groq:
        return 0xFF8B5CF6; // Groq 紫色
      case AIProvider.gemini:
        return 0xFF3B82F6; // Gemini 藍色
    }
  }

  /// 獲取提供商的圖標
  static String getProviderIcon(AIProvider provider) {
    switch (provider) {
      case AIProvider.openai:
        return '🧠';
      case AIProvider.anthropic:
        return '🤖';
      case AIProvider.groq:
        return '⚡';
      case AIProvider.gemini:
        return '✨';
    }
  }
}
