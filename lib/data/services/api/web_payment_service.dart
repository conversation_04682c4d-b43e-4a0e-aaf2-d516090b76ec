import 'dart:convert';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:url_launcher/url_launcher.dart';

import '../../../core/utils/logger_utils.dart';
import 'auth_service.dart';
import 'remote_config_service.dart';

/// Web 支付服務
class WebPaymentService {
  static Map<String, dynamic> _paymentConfig = {};
  static Map<String, dynamic> _productConfig = {};

  /// 初始化 Web 支付服務
  static Future<bool> initialize() async {
    try {
      // 載入支付配置
      await _loadPaymentConfig();
      
      // 載入商品配置
      await _loadProductConfig();

      logger.i('Web 支付服務初始化成功');
      return true;
    } catch (e) {
      logger.e('Web 支付服務初始化失敗: $e');
      return false;
    }
  }

  /// 載入支付配置
  static Future<void> _loadPaymentConfig() async {
    try {
      _paymentConfig = await RemoteConfigService.getWebPaymentConfig();
      logger.i('已載入 Web 支付配置');
    } catch (e) {
      logger.e('載入 Web 支付配置失敗: $e');
      // 使用預設配置
      _paymentConfig = _getDefaultPaymentConfig();
    }
  }

  /// 載入商品配置
  static Future<void> _loadProductConfig() async {
    try {
      _productConfig = await RemoteConfigService.getWebProductConfig();
      logger.i('已載入 Web 商品配置');
    } catch (e) {
      logger.e('載入 Web 商品配置失敗: $e');
    }
  }

  /// 獲取預設支付配置
  static Map<String, dynamic> _getDefaultPaymentConfig() {
    return {
      'stripe': {
        'publishable_key': 'pk_test_...',
        'webhook_secret': 'whsec_...',
        'success_url': 'https://astreal.app/payment/success',
        'cancel_url': 'https://astreal.app/payment/cancel',
      },
      'paypal': {
        'client_id': 'your_paypal_client_id',
        'client_secret': 'your_paypal_client_secret',
        'sandbox': true,
      },
    };
  }

  /// 創建 Stripe 支付會話
  static Future<String?> createStripePaymentSession({
    required String productId,
    required String userId,
  }) async {
    try {
      final product = _getProduct(productId);
      if (product == null) {
        logger.e('找不到商品: $productId');
        return null;
      }

      final stripeConfig = _paymentConfig['stripe'] as Map<String, dynamic>? ?? {};
      final apiUrl = stripeConfig['api_url'] ?? 'https://api.stripe.com/v1/checkout/sessions';

      final response = await http.post(
        Uri.parse(apiUrl),
        headers: {
          'Authorization': 'Bearer ${stripeConfig['secret_key']}',
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: {
          'payment_method_types[]': 'card',
          'line_items[0][price_data][currency]': product['currency'],
          'line_items[0][price_data][product_data][name]': product['name'],
          'line_items[0][price_data][product_data][description]': product['description'],
          'line_items[0][price_data][unit_amount]': (product['price'] * 100).round().toString(),
          'line_items[0][quantity]': '1',
          'mode': product['type'] == 'subscription' ? 'subscription' : 'payment',
          'success_url': '${stripeConfig['success_url']}?session_id={CHECKOUT_SESSION_ID}&product_id=$productId&user_id=$userId',
          'cancel_url': stripeConfig['cancel_url'],
          'metadata[product_id]': productId,
          'metadata[user_id]': userId,
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final sessionId = data['id'];
        logger.i('Stripe 支付會話創建成功: $sessionId');
        return sessionId;
      } else {
        logger.e('創建 Stripe 支付會話失敗: ${response.statusCode} - ${response.body}');
        return null;
      }
    } catch (e) {
      logger.e('創建 Stripe 支付會話時出錯: $e');
      return null;
    }
  }

  /// 創建 PayPal 支付訂單
  static Future<String?> createPayPalPaymentOrder({
    required String productId,
    required String userId,
  }) async {
    try {
      final product = _getProduct(productId);
      if (product == null) {
        logger.e('找不到商品: $productId');
        return null;
      }

      final paypalConfig = _paymentConfig['paypal'] as Map<String, dynamic>? ?? {};
      final baseUrl = paypalConfig['sandbox'] == true 
          ? 'https://api.sandbox.paypal.com'
          : 'https://api.paypal.com';

      // 獲取 PayPal 訪問令牌
      final accessToken = await _getPayPalAccessToken();
      if (accessToken == null) {
        logger.e('獲取 PayPal 訪問令牌失敗');
        return null;
      }

      // 創建支付訂單
      final response = await http.post(
        Uri.parse('$baseUrl/v2/checkout/orders'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $accessToken',
        },
        body: jsonEncode({
          'intent': 'CAPTURE',
          'purchase_units': [
            {
              'amount': {
                'currency_code': product['currency'],
                'value': product['price'].toString(),
              },
              'description': product['description'],
              'custom_id': '$userId:$productId',
            }
          ],
          'application_context': {
            'return_url': 'https://astreal.app/payment/paypal/success',
            'cancel_url': 'https://astreal.app/payment/paypal/cancel',
          },
        }),
      );

      if (response.statusCode == 201) {
        final data = jsonDecode(response.body);
        final orderId = data['id'];
        logger.i('PayPal 支付訂單創建成功: $orderId');
        return orderId;
      } else {
        logger.e('創建 PayPal 支付訂單失敗: ${response.statusCode} - ${response.body}');
        return null;
      }
    } catch (e) {
      logger.e('創建 PayPal 支付訂單時出錯: $e');
      return null;
    }
  }

  /// 獲取 PayPal 訪問令牌
  static Future<String?> _getPayPalAccessToken() async {
    try {
      final paypalConfig = _paymentConfig['paypal'] as Map<String, dynamic>? ?? {};
      final baseUrl = paypalConfig['sandbox'] == true 
          ? 'https://api.sandbox.paypal.com'
          : 'https://api.paypal.com';

      final credentials = base64Encode(
        utf8.encode('${paypalConfig['client_id']}:${paypalConfig['client_secret']}')
      );

      final response = await http.post(
        Uri.parse('$baseUrl/v1/oauth2/token'),
        headers: {
          'Accept': 'application/json',
          'Accept-Language': 'en_US',
          'Authorization': 'Basic $credentials',
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'grant_type=client_credentials',
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return data['access_token'];
      } else {
        logger.e('獲取 PayPal 訪問令牌失敗: ${response.statusCode} - ${response.body}');
        return null;
      }
    } catch (e) {
      logger.e('獲取 PayPal 訪問令牌時出錯: $e');
      return null;
    }
  }

  /// 處理支付成功
  static Future<bool> handlePaymentSuccess({
    required String productId,
    required String userId,
    required String transactionId,
    required String paymentMethod,
  }) async {
    try {
      final product = _getProduct(productId);
      if (product == null) {
        logger.e('找不到商品: $productId');
        return false;
      }

      // 記錄支付到 Firebase
      logger.i('處理 Web 支付成功: $productId, 商品: $product');

      // TODO: 實現 Firebase 購買記錄功能
      // final isSubscription = product['type'] == 'subscription';
      // final expiryDate = isSubscription
      //     ? _calculateExpiryDate(product['period'])
      //     : DateTime.now().add(const Duration(days: 365));
      //
      // final paymentRecord = PaymentRecord(...);
      // await FirebasePaymentService.recordPayment(paymentRecord);

      logger.i('Web 支付成功記錄: $productId');
      return true;
    } catch (e) {
      logger.e('處理 Web 支付成功時出錯: $e');
      return false;
    }
  }

  /// 啟動 Web 支付流程
  static Future<bool> launchPayment({
    required String productId,
    required String paymentMethod,
  }) async {
    try {
      final currentUser = AuthService.getCurrentUser();
      if (currentUser == null) {
        logger.e('用戶未登入，無法進行支付');
        return false;
      }

      String? paymentUrl;

      switch (paymentMethod.toLowerCase()) {
        case 'stripe':
          final sessionId = await createStripePaymentSession(
            productId: productId,
            userId: currentUser.uid,
          );
          if (sessionId != null) {
            paymentUrl = 'https://checkout.stripe.com/pay/$sessionId';
          }
          break;

        case 'paypal':
          final orderId = await createPayPalPaymentOrder(
            productId: productId,
            userId: currentUser.uid,
          );
          if (orderId != null) {
            paymentUrl = 'https://www.paypal.com/checkoutnow?token=$orderId';
          }
          break;

        default:
          logger.e('不支援的支付方式: $paymentMethod');
          return false;
      }

      if (paymentUrl != null) {
        final uri = Uri.parse(paymentUrl);
        if (await canLaunchUrl(uri)) {
          await launchUrl(uri, mode: LaunchMode.externalApplication);
          logger.i('已啟動 Web 支付: $paymentMethod');
          return true;
        } else {
          logger.e('無法啟動支付 URL: $paymentUrl');
          return false;
        }
      } else {
        logger.e('創建支付 URL 失敗');
        return false;
      }
    } catch (e) {
      logger.e('啟動 Web 支付時出錯: $e');
      return false;
    }
  }

  /// 獲取商品資訊
  static Map<String, dynamic>? _getProduct(String productId) {
    final products = _productConfig['products'] as Map<String, dynamic>? ?? {};
    return products[productId] as Map<String, dynamic>?;
  }

  /// 獲取可用商品列表
  static List<Map<String, dynamic>> getAvailableProducts() {
    final products = _productConfig['products'] as Map<String, dynamic>? ?? {};
    return products.values.cast<Map<String, dynamic>>().toList();
  }

  /// 檢查 Web 支付是否可用
  static bool isAvailable() {
    return kIsWeb || (!Platform.isAndroid && !Platform.isIOS);
  }
}
