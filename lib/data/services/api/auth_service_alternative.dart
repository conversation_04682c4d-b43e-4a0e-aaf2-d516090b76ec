import 'dart:convert';

import 'package:crypto/crypto.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

import '../../../core/utils/logger_utils.dart';
import '../../models/user/app_user.dart';

/// 替代認證服務（不使用 Firebase）
/// 使用自建後端 API 或第三方認證服務
class AuthServiceAlternative {
  static const String _baseUrl = 'https://your-backend-api.com/auth';
  static const String _tokenKey = 'auth_token';
  static const String _userKey = 'user_data';
  
  static AppUser? _currentUser;
  static String? _authToken;

  /// 獲取當前用戶
  static AppUser? getCurrentUser() {
    return _currentUser;
  }

  /// 獲取認證狀態流
  static Stream<AppUser?> get authStateChanges {
    // 實作認證狀態變化流
    return Stream.periodic(const Duration(seconds: 1), (_) => _currentUser);
  }

  /// 初始化服務
  static Future<void> initialize() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _authToken = prefs.getString(_tokenKey);
      
      if (_authToken != null) {
        final userData = prefs.getString(_userKey);
        if (userData != null) {
          final userJson = json.decode(userData);
          _currentUser = AppUser.fromJson(userJson);
          logger.i('用戶會話已恢復: ${_currentUser?.email}');
        }
      }
    } catch (e) {
      logger.e('認證服務初始化失敗: $e');
    }
  }

  /// 用戶註冊
  static Future<AppUser?> registerWithEmailAndPassword({
    required String email,
    required String password,
    String? displayName,
  }) async {
    try {
      logger.i('開始用戶註冊: $email');

      // 驗證輸入
      if (!_isValidEmail(email)) {
        throw Exception('無效的電子郵件格式');
      }
      if (!_isValidPassword(password)) {
        throw Exception('密碼必須至少 6 個字符');
      }

      // 加密密碼
      final hashedPassword = _hashPassword(password);

      // 調用後端 API
      final response = await http.post(
        Uri.parse('$_baseUrl/register'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'email': email,
          'password': hashedPassword,
          'displayName': displayName,
        }),
      );

      if (response.statusCode == 201) {
        final data = json.decode(response.body);
        final user = AppUser.fromJson(data['user']);
        final token = data['token'];

        await _saveUserSession(user, token);
        logger.i('用戶註冊成功: ${user.uid}');
        return user;
      } else {
        final error = json.decode(response.body);
        throw Exception(error['message'] ?? '註冊失敗');
      }
    } catch (e) {
      logger.e('用戶註冊失敗: $e');
      rethrow;
    }
  }

  /// 用戶登入
  static Future<AppUser?> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      logger.i('開始用戶登入: $email');

      // 驗證輸入
      if (!_isValidEmail(email)) {
        throw Exception('無效的電子郵件格式');
      }
      if (password.isEmpty) {
        throw Exception('密碼不能為空');
      }

      // 加密密碼
      final hashedPassword = _hashPassword(password);

      // 調用後端 API
      final response = await http.post(
        Uri.parse('$_baseUrl/login'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'email': email,
          'password': hashedPassword,
        }),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final user = AppUser.fromJson(data['user']);
        final token = data['token'];

        await _saveUserSession(user, token);
        logger.i('用戶登入成功: ${user.uid}');
        return user;
      } else {
        final error = json.decode(response.body);
        throw Exception(error['message'] ?? '登入失敗');
      }
    } catch (e) {
      logger.e('用戶登入失敗: $e');
      rethrow;
    }
  }

  /// 用戶登出
  static Future<void> signOut() async {
    try {
      logger.i('用戶登出');

      // 清除本地會話
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_tokenKey);
      await prefs.remove(_userKey);

      _currentUser = null;
      _authToken = null;

      logger.i('用戶登出成功');
    } catch (e) {
      logger.e('用戶登出失敗: $e');
      rethrow;
    }
  }

  /// 發送密碼重置郵件
  static Future<void> sendPasswordResetEmail({required String email}) async {
    try {
      logger.i('發送密碼重置郵件: $email');

      if (!_isValidEmail(email)) {
        throw Exception('無效的電子郵件格式');
      }

      final response = await http.post(
        Uri.parse('$_baseUrl/reset-password'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({'email': email}),
      );

      if (response.statusCode == 200) {
        logger.i('密碼重置郵件發送成功');
      } else {
        final error = json.decode(response.body);
        throw Exception(error['message'] ?? '發送失敗');
      }
    } catch (e) {
      logger.e('發送密碼重置郵件失敗: $e');
      rethrow;
    }
  }

  /// 更新用戶資料
  static Future<AppUser?> updateUserProfile({
    String? displayName,
    String? photoURL,
  }) async {
    try {
      if (_currentUser == null || _authToken == null) {
        throw Exception('用戶未登入');
      }

      logger.i('更新用戶資料');

      final response = await http.put(
        Uri.parse('$_baseUrl/profile'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_authToken',
        },
        body: json.encode({
          'displayName': displayName,
          'photoURL': photoURL,
        }),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final updatedUser = AppUser.fromJson(data['user']);
        
        await _saveUserSession(updatedUser, _authToken!);
        logger.i('用戶資料更新成功');
        return updatedUser;
      } else {
        final error = json.decode(response.body);
        throw Exception(error['message'] ?? '更新失敗');
      }
    } catch (e) {
      logger.e('更新用戶資料失敗: $e');
      rethrow;
    }
  }

  /// 刪除用戶帳戶
  static Future<void> deleteUser() async {
    try {
      if (_currentUser == null || _authToken == null) {
        throw Exception('用戶未登入');
      }

      logger.i('刪除用戶帳戶: ${_currentUser!.uid}');

      final response = await http.delete(
        Uri.parse('$_baseUrl/account'),
        headers: {
          'Authorization': 'Bearer $_authToken',
        },
      );

      if (response.statusCode == 200) {
        await signOut();
        logger.i('用戶帳戶刪除成功');
      } else {
        final error = json.decode(response.body);
        throw Exception(error['message'] ?? '刪除失敗');
      }
    } catch (e) {
      logger.e('刪除用戶帳戶失敗: $e');
      rethrow;
    }
  }

  /// 保存用戶會話
  static Future<void> _saveUserSession(AppUser user, String token) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_tokenKey, token);
    await prefs.setString(_userKey, json.encode(user.toJson()));
    
    _currentUser = user;
    _authToken = token;
  }

  /// 驗證電子郵件格式
  static bool _isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  /// 驗證密碼強度
  static bool _isValidPassword(String password) {
    return password.length >= 6;
  }

  /// 加密密碼
  static String _hashPassword(String password) {
    final bytes = utf8.encode(password);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }
}
