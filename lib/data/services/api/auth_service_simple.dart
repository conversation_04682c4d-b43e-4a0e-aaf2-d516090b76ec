import 'dart:convert';

import 'package:shared_preferences/shared_preferences.dart';

import '../../../core/utils/logger_utils.dart';
import '../../models/user/app_user.dart';

/// 簡化的認證服務（不使用 Firebase）
/// 使用本地存儲模擬認證功能
class AuthServiceSimple {
  static const String _userKey = 'current_user';
  static const String _isLoggedInKey = 'is_logged_in';
  
  static AppUser? _currentUser;
  static bool _isLoggedIn = false;

  /// 獲取當前用戶
  static AppUser? getCurrentUser() {
    return _currentUser;
  }

  /// 獲取認證狀態流（簡化版）
  static Stream<AppUser?> get authStateChanges {
    return Stream.periodic(const Duration(seconds: 1), (_) => _currentUser);
  }

  /// 初始化服務
  static Future<void> initialize() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _isLoggedIn = prefs.getBool(_isLoggedInKey) ?? false;
      
      if (_isLoggedIn) {
        final userData = prefs.getString(_userKey);
        if (userData != null) {
          final userJson = json.decode(userData);
          _currentUser = AppUser.fromJson(userJson);
          logger.i('用戶會話已恢復: ${_currentUser?.email}');
        }
      }
    } catch (e) {
      logger.e('認證服務初始化失敗: $e');
    }
  }

  /// 用戶註冊（模擬）
  static Future<AppUser?> registerWithEmailAndPassword({
    required String email,
    required String password,
    String? displayName,
  }) async {
    try {
      logger.i('開始用戶註冊: $email');

      // 驗證輸入
      if (!_isValidEmail(email)) {
        throw Exception('無效的電子郵件格式');
      }
      if (!_isValidPassword(password)) {
        throw Exception('密碼必須至少 6 個字符');
      }

      // 模擬註冊延遲
      await Future.delayed(const Duration(seconds: 1));

      // 創建用戶
      final user = AppUser(
        uid: DateTime.now().millisecondsSinceEpoch.toString(),
        email: email,
        displayName: displayName ?? email.split('@')[0],
        emailVerified: true,
        createdAt: DateTime.now(),
        lastSignInAt: DateTime.now(),
      );

      await _saveUserSession(user);
      logger.i('用戶註冊成功: ${user.uid}');
      return user;
    } catch (e) {
      logger.e('用戶註冊失敗: $e');
      rethrow;
    }
  }

  /// 用戶登入（模擬）
  static Future<AppUser?> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      logger.i('開始用戶登入: $email');

      // 驗證輸入
      if (!_isValidEmail(email)) {
        throw Exception('無效的電子郵件格式');
      }
      if (password.isEmpty) {
        throw Exception('密碼不能為空');
      }

      // 模擬登入延遲
      await Future.delayed(const Duration(seconds: 1));

      // 模擬登入成功（在實際應用中，這裡應該驗證憑證）
      final user = AppUser(
        uid: DateTime.now().millisecondsSinceEpoch.toString(),
        email: email,
        displayName: email.split('@')[0],
        emailVerified: true,
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
        lastSignInAt: DateTime.now(),
      );

      await _saveUserSession(user);
      logger.i('用戶登入成功: ${user.uid}');
      return user;
    } catch (e) {
      logger.e('用戶登入失敗: $e');
      rethrow;
    }
  }

  /// 用戶登出
  static Future<void> signOut() async {
    try {
      logger.i('用戶登出');

      // 清除本地會話
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_userKey);
      await prefs.setBool(_isLoggedInKey, false);

      _currentUser = null;
      _isLoggedIn = false;

      logger.i('用戶登出成功');
    } catch (e) {
      logger.e('用戶登出失敗: $e');
      rethrow;
    }
  }

  /// 發送密碼重置郵件（模擬）
  static Future<void> sendPasswordResetEmail({required String email}) async {
    try {
      logger.i('發送密碼重置郵件: $email');

      if (!_isValidEmail(email)) {
        throw Exception('無效的電子郵件格式');
      }

      // 模擬發送延遲
      await Future.delayed(const Duration(seconds: 1));

      logger.i('密碼重置郵件發送成功（模擬）');
    } catch (e) {
      logger.e('發送密碼重置郵件失敗: $e');
      rethrow;
    }
  }

  /// 更新用戶資料
  static Future<AppUser?> updateUserProfile({
    String? displayName,
    String? photoURL,
  }) async {
    try {
      if (_currentUser == null) {
        throw Exception('用戶未登入');
      }

      logger.i('更新用戶資料');

      // 創建更新後的用戶
      final updatedUser = AppUser(
        uid: _currentUser!.uid,
        email: _currentUser!.email,
        displayName: displayName ?? _currentUser!.displayName,
        photoURL: photoURL ?? _currentUser!.photoURL,
        emailVerified: _currentUser!.emailVerified,
        createdAt: _currentUser!.createdAt,
        lastSignInAt: DateTime.now(),
      );
      
      await _saveUserSession(updatedUser);
      logger.i('用戶資料更新成功');
      return updatedUser;
    } catch (e) {
      logger.e('更新用戶資料失敗: $e');
      rethrow;
    }
  }

  /// 刪除用戶帳戶
  static Future<void> deleteUser() async {
    try {
      if (_currentUser == null) {
        throw Exception('用戶未登入');
      }

      logger.i('刪除用戶帳戶: ${_currentUser!.uid}');

      await signOut();
      logger.i('用戶帳戶刪除成功（模擬）');
    } catch (e) {
      logger.e('刪除用戶帳戶失敗: $e');
      rethrow;
    }
  }

  /// 保存用戶會話
  static Future<void> _saveUserSession(AppUser user) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_userKey, json.encode(user.toJson()));
    await prefs.setBool(_isLoggedInKey, true);
    
    _currentUser = user;
    _isLoggedIn = true;
  }

  /// 驗證電子郵件格式
  static bool _isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  /// 驗證密碼強度
  static bool _isValidPassword(String password) {
    return password.length >= 6;
  }
}
