import 'dart:convert';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_core/firebase_core.dart' show Firebase;
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;

import '../../../core/utils/logger_utils.dart';
import '../../../shared/utils/csv_helper.dart';
import 'birth_data_service.dart';
// 條件導入：只在 Web 平台導入 dart:html
import 'web_download_stub.dart'
    if (dart.library.html) 'web_download_web.dart' as web_download;

/// Firebase Storage 備份服務
/// 提供出生資料的雲端備份和還原功能
class FirebaseStorageBackupService {
  static final FirebaseStorage _storage = FirebaseStorage.instance;
  static const String _backupFolder = 'user_backups';
  static const String _backupFileName = 'birth_data_backup.csv';

  // 連接狀態快取
  static bool? _lastConnectionStatus;
  static DateTime? _lastConnectionCheck;
  static const Duration _connectionCacheTimeout = Duration(minutes: 5);

  /// 檢查 Firebase Storage 連接狀態
  static Future<bool> checkConnection() async {
    try {
      // 如果快取仍然有效，直接返回快取結果
      if (_lastConnectionCheck != null &&
          _lastConnectionStatus != null &&
          DateTime.now().difference(_lastConnectionCheck!) < _connectionCacheTimeout) {
        logger.d('使用快取的連接狀態: $_lastConnectionStatus');
        return _lastConnectionStatus!;
      }

      logger.d('檢查 Firebase Storage 連接狀態 - 平台: ${kIsWeb ? "Web" : "Mobile"}');

      // 檢查 Firebase 是否已初始化
      try {
        final apps = Firebase.apps;
        final hasDefaultApp = apps.any((app) => app.name == '[DEFAULT]');
        if (!hasDefaultApp) {
          logger.w('Firebase 未初始化，Storage 連接不可用');
          _lastConnectionStatus = false;
          _lastConnectionCheck = DateTime.now();
          return false;
        }
      } catch (e) {
        logger.w('Firebase 應用檢查失敗: $e');
        _lastConnectionStatus = false;
        _lastConnectionCheck = DateTime.now();
        return false;
      }

      // 檢查用戶是否已登入（Storage 需要認證）
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        logger.d('用戶未登入，跳過 Storage 連接檢查');
        _lastConnectionStatus = false;
        _lastConnectionCheck = DateTime.now();
        return false;
      }

      // 使用更輕量的方法測試連接
      if (kIsWeb) {
        // Web 平台：嘗試獲取一個測試檔案的下載 URL
        return await _checkConnectionWeb();
      } else {
        // 移動平台：使用 listAll 測試
        return await _checkConnectionMobile();
      }

    } catch (e) {
      final errorMessage = e.toString();
      logger.w('Firebase Storage 連接檢查失敗: $errorMessage');

      // 更詳細的錯誤分析
      if (errorMessage.contains('channel-error') ||
          errorMessage.contains('Unable to establish connection') ||
          errorMessage.contains('Failed to fetch') ||
          errorMessage.contains('NetworkError') ||
          errorMessage.contains('CORS')) {
        _lastConnectionStatus = false;
        _lastConnectionCheck = DateTime.now();
        logger.w('Firebase Storage 連接不可用 - 網路或 CORS 問題');
        return false;
      }

      if (errorMessage.contains('permission') ||
          errorMessage.contains('unauthorized') ||
          errorMessage.contains('access denied')) {
        logger.w('Firebase Storage 權限問題，但連接可用');
        _lastConnectionStatus = true;
        _lastConnectionCheck = DateTime.now();
        return true;
      }

      // 其他錯誤，假設連接可用但可能有其他問題
      _lastConnectionStatus = true;
      _lastConnectionCheck = DateTime.now();
      return true;
    }
  }

  /// Web 平台的連接檢查
  static Future<bool> _checkConnectionWeb() async {
    try {
      logger.d('執行 Web 平台 Storage 連接檢查...');

      // 嘗試創建一個測試引用並獲取其路徑
      final testRef = _storage.ref().child('connection_test');
      final path = testRef.fullPath;

      logger.d('Storage 引用創建成功: $path');

      _lastConnectionStatus = true;
      _lastConnectionCheck = DateTime.now();
      logger.d('Web 平台 Firebase Storage 連接正常');
      return true;

    } catch (e) {
      logger.w('Web 平台 Storage 連接檢查失敗: $e');
      _lastConnectionStatus = false;
      _lastConnectionCheck = DateTime.now();
      return false;
    }
  }

  /// 移動平台的連接檢查
  static Future<bool> _checkConnectionMobile() async {
    try {
      logger.d('執行移動平台 Storage 連接檢查...');

      // 使用更輕量的方法：嘗試獲取一個不存在檔案的 metadata
      // 這樣可以測試 API 連接而不需要列表權限
      final testRef = _storage.ref().child('connection_test_file_that_does_not_exist.txt');

      try {
        await testRef.getMetadata();
        // 如果沒有拋出異常，說明檔案存在（不太可能）
        _lastConnectionStatus = true;
        _lastConnectionCheck = DateTime.now();
        logger.d('移動平台 Firebase Storage 連接正常（檔案存在）');
        return true;
      } catch (metadataError) {
        final errorMessage = metadataError.toString();

        // 如果是 object-not-found 錯誤，說明 API 連接正常
        if (errorMessage.contains('object-not-found') ||
            errorMessage.contains('not-found') ||
            errorMessage.contains('does not exist')) {
          _lastConnectionStatus = true;
          _lastConnectionCheck = DateTime.now();
          logger.d('移動平台 Firebase Storage 連接正常（API 回應正常）');
          return true;
        }

        // 如果是權限錯誤，說明連接正常但沒有權限
        if (errorMessage.contains('permission') ||
            errorMessage.contains('unauthorized') ||
            errorMessage.contains('access denied')) {
          _lastConnectionStatus = true;
          _lastConnectionCheck = DateTime.now();
          logger.d('移動平台 Firebase Storage 連接正常（權限限制）');
          return true;
        }

        // 其他錯誤可能是連接問題
        throw metadataError;
      }

    } catch (e) {
      final errorMessage = e.toString();
      logger.w('移動平台 Storage 連接檢查失敗: $errorMessage');

      // 分析錯誤類型
      if (errorMessage.contains('channel-error') ||
          errorMessage.contains('Unable to establish connection') ||
          errorMessage.contains('Failed to fetch') ||
          errorMessage.contains('NetworkError')) {
        logger.w('檢測到網路連接問題');
        _lastConnectionStatus = false;
        _lastConnectionCheck = DateTime.now();
        return false;
      }

      // 對於其他錯誤，假設連接可用但可能有其他問題
      logger.i('假設連接可用，錯誤可能是其他原因');
      _lastConnectionStatus = true;
      _lastConnectionCheck = DateTime.now();
      return true;
    }
  }

  /// 備份出生資料到 Firebase Storage
  /// 返回備份是否成功
  static Future<bool> backupBirthData() async {
    try {
      // 檢查用戶是否已登入
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        logger.w('用戶未登入，無法進行雲端備份');
        return false;
      }

      logger.i('開始備份到 Firebase Storage - 平台: ${kIsWeb ? "Web" : "Mobile"}');

      // 獲取所有出生資料
      final birthDataService = BirthDataService();
      final birthDataList = await birthDataService.getAllBirthData();

      if (birthDataList.isEmpty) {
        logger.i('沒有出生資料需要備份');
        return true; // 空資料也算備份成功
      }

      // 使用 CsvHelper 生成 CSV 內容
      final csvContent = CsvHelper.generateCsvContent(birthDataList);

      // 轉換為 UTF-8 bytes
      final csvBytes = utf8.encode(csvContent);

      // 上傳到 Firebase Storage
      final ref = _storage.ref().child('$_backupFolder/${currentUser.uid}/$_backupFileName');

      final metadata = SettableMetadata(
        contentType: 'text/csv',
        customMetadata: {
          'backup_version': '1.0',
          'birth_data_count': birthDataList.length.toString(),
          'created_at': DateTime.now().toIso8601String(),
          'format': 'csv',
          'platform': kIsWeb ? 'web' : 'mobile',
        },
      );

      try {
        await ref.putData(Uint8List.fromList(csvBytes), metadata);
        logger.i('成功備份 ${birthDataList.length} 筆出生資料到 Firebase Storage');
        return true;
      } catch (e) {
        logger.e('上傳到 Firebase Storage 失敗: $e');
        // 避免將 ClientException 等異常傳遞到 UI 層
        return false;
      }

    } catch (e) {
      logger.e('Firebase Storage 備份失敗: $e');
      // 避免將異常傳遞到 UI 層
      return false;
    }
  }

  /// 從 Firebase Storage 下載備份檔案（Web 平台使用 Anchor 下載）
  /// 返回下載是否成功觸發
  static Future<bool> downloadBackupFile() async {
    try {
      // 檢查用戶是否已登入
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        logger.w('用戶未登入，無法進行雲端下載');
        return false;
      }

      logger.i('開始從 Firebase Storage 下載備份檔案 - 平台: ${kIsWeb ? "Web" : "Mobile"}');

      // 從 Firebase Storage 獲取備份檔案引用
      final ref = _storage.ref().child('$_backupFolder/${currentUser.uid}/$_backupFileName');
      logger.d('備份檔案路徑: ${ref.fullPath}');
      
      // 首先檢查檔案是否存在
      final backupExists = await _checkFileExists(ref);
      if (!backupExists) {
        logger.w('Firebase Storage 中沒有找到備份檔案: ${ref.fullPath}');
        logger.i('提示：請先執行備份操作，或檢查是否使用了正確的帳戶');
        return false;
      }
      
      logger.i('確認備份檔案存在，開始下載...');
      
      if (kIsWeb) {
        // Web 平台：使用 Anchor 下載
        try {
          final downloadUrl = await ref.getDownloadURL();
          web_download.downloadFileWithAnchor(downloadUrl, 'birth_data_backup.csv');
          logger.i('Web 平台：備份檔案下載已觸發，請檢查瀏覽器下載資料夾');
          return true;
        } catch (e) {
          logger.e('Web 平台下載觸發失敗: $e');
          return false;
        }
      } else {
        // 移動平台：直接下載並處理
        return await restoreBirthData();
      }

    } catch (e) {
      logger.e('Firebase Storage 下載失敗: $e');
      return false;
    }
  }

  /// 還原結果報告
  static Map<String, dynamic>? _lastRestoreReport;

  /// 獲取最後一次還原的詳細報告
  static Map<String, dynamic>? getLastRestoreReport() {
    return _lastRestoreReport;
  }

  /// 從 Firebase Storage 還原出生資料（主要用於移動平台）
  /// 返回還原是否成功
  static Future<bool> restoreBirthData() async {
    try {
      // 檢查用戶是否已登入
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        logger.w('用戶未登入，無法進行雲端還原');
        return false;
      }

      logger.i('開始從 Firebase Storage 還原資料 - 平台: ${kIsWeb ? "Web" : "Mobile"}');

      // 從 Firebase Storage 下載備份檔案
      final ref = _storage.ref().child('$_backupFolder/${currentUser.uid}/$_backupFileName');
      logger.d('備份檔案路徑: ${ref.fullPath}');
      
      // 首先檢查檔案是否存在
      final backupExists = await _checkFileExists(ref);
      if (!backupExists) {
        logger.w('Firebase Storage 中沒有找到備份檔案: ${ref.fullPath}');
        logger.i('提示：請先執行備份操作，或檢查是否使用了正確的帳戶');
        return false;
      }
      
      logger.i('確認備份檔案存在，開始下載...');
      
      Uint8List? data;
      try {
        if (kIsWeb) {
          // Web 平台使用不同的方法
          data = await _downloadFileWeb(ref);
        } else {
          // 移動平台使用標準方法
          data = await ref.getData();
        }
      } catch (e) {
        // 捕獲所有異常，包括 ClientException
        final errorMessage = e.toString();
        logger.e('下載備份檔案失敗: $errorMessage');
        
        // 檢查是否是 ClientException 相關錯誤
        if (errorMessage.contains('ClientException') || 
            errorMessage.contains('Failed to fetch') ||
            errorMessage.contains('JavaScriptObject') ||
            errorMessage.contains('channel-error')) {
          logger.w('檢測到 Web 平台相容性問題，嘗試替代方法...');
        } else {
          logger.i('嘗試使用替代下載方法...');
        }
        
        // 嘗試使用替代方法
        try {
          data = await _downloadFileAlternative(ref);
        } catch (altError) {
          final altErrorMessage = altError.toString();
          logger.e('替代下載方法也失敗: $altErrorMessage');
          
          // 如果是 Web 平台的 CORS 或網路問題，提供更具體的錯誤訊息
          if (kIsWeb && (altErrorMessage.contains('Failed to fetch') || 
                        altErrorMessage.contains('CORS') ||
                        altErrorMessage.contains('ClientException') ||
                        altErrorMessage.contains('channel-error'))) {
            logger.e('Web 平台網路請求失敗，可能是 CORS 或網路連接問題');
          }
          
          return false;
        }
      }

      if (data == null || data.isEmpty) {
        logger.w('下載的備份檔案為空');
        return false;
      }
      
      logger.i('成功下載備份檔案，大小: ${data.length} bytes');

      // 解析 CSV 資料
      final csvContent = utf8.decode(data);
      logger.d('CSV 內容預覽: ${csvContent.length > 500 ? csvContent.substring(0, 500) + '...' : csvContent}');

      // 使用 CsvHelper 解析 CSV 內容
      final birthDataList = await CsvHelper.importBirthData(csvContent);

      if (birthDataList.isEmpty) {
        logger.w('備份檔案中沒有有效的出生資料');
        logger.w('CSV 內容: $csvContent');
        return false;
      }

      logger.i('成功解析 ${birthDataList.length} 筆出生資料，開始還原...');

      // 記錄還原前的資料數量
      final birthDataService = BirthDataService();
      final beforeCount = (await birthDataService.getAllBirthData()).length;
      logger.i('還原前已有 $beforeCount 筆出生資料');

      // 還原出生資料，並記錄詳細過程
      int successCount = 0;
      int failCount = 0;
      List<String> successfulNames = [];

      for (int i = 0; i < birthDataList.length; i++) {
        try {
          final birthData = birthDataList[i];
          logger.d('正在還原第 ${i + 1} 筆: ${birthData.name}');
          await birthDataService.addBirthData(birthData);
          successCount++;
          successfulNames.add(birthData.name);
          logger.d('成功還原: ${birthData.name}');
        } catch (e) {
          failCount++;
          logger.e('還原第 ${i + 1} 筆資料失敗: ${birthDataList[i].name}, 錯誤: $e');
        }
      }

      // 記錄還原後的實際資料數量
      final afterCount = (await birthDataService.getAllBirthData()).length;
      final actualAddedCount = afterCount - beforeCount;
      logger.i('還原後共有 $afterCount 筆出生資料，實際新增 $actualAddedCount 筆');

      logger.i('還原完成 - 處理: $successCount 筆, 失敗: $failCount 筆, 總計: ${birthDataList.length} 筆, 實際新增: $actualAddedCount 筆');

      // 記錄詳細的還原報告
      _lastRestoreReport = {
        'timestamp': DateTime.now().toIso8601String(),
        'totalParsed': birthDataList.length,
        'processedCount': successCount,
        'actualAddedCount': actualAddedCount,
        'failCount': failCount,
        'beforeCount': beforeCount,
        'afterCount': afterCount,
        'csvSize': data.length,
        'csvPreview': csvContent.length > 200 ? csvContent.substring(0, 200) + '...' : csvContent,
        'successfulNames': successfulNames,
        'successfulItems': successfulNames.take(10).map((name) => {
          'name': name,
        }).toList(),
      };

      // 只要有成功處理的資料就算成功
      return successCount > 0;

    } catch (e) {
      logger.e('Firebase Storage 還原失敗: $e');
      // 避免將異常傳遞到 UI 層
      return false;
    }
  }

  /// 檢查檔案是否存在
  static Future<bool> _checkFileExists(Reference ref) async {
    try {
      logger.d('檢查檔案是否存在: ${ref.fullPath} - 平台: ${kIsWeb ? "Web" : "Mobile"}');

      // 方法1：嘗試獲取下載 URL（最安全的方法）
      try {
        final downloadUrl = await ref.getDownloadURL();
        logger.d('檔案存在（通過 download URL 確認）: ${downloadUrl.substring(0, 50)}...');

        // 在 Web 平台上，進一步驗證 URL 是否可訪問
        if (kIsWeb) {
          try {
            final response = await http.head(Uri.parse(downloadUrl));
            if (response.statusCode == 200) {
              logger.d('Web 平台 HTTP HEAD 確認檔案存在');
              return true;
            } else {
              logger.w('Web 平台 HTTP HEAD 失敗，狀態碼: ${response.statusCode}');
              return false;
            }
          } catch (headError) {
            logger.d('Web 平台 HTTP HEAD 失敗: $headError，但 URL 存在，假設檔案存在');
            return true; // URL 存在就假設檔案存在
          }
        } else {
          return true; // 移動平台上，能獲取 URL 就表示檔案存在
        }
      } catch (urlError) {
        logger.d('無法獲取下載 URL: $urlError');

        // 方法2：嘗試獲取 metadata（僅在非 Web 平台）
        if (!kIsWeb) {
          try {
            await ref.getMetadata();
            logger.d('檔案存在（通過 metadata 確認）');
            return true;
          } catch (metadataError) {
            logger.d('無法獲取 metadata: $metadataError');

            // 方法3：嘗試直接下載少量資料（僅在非 Web 平台）
            try {
              await ref.getData(1); // 只下載 1 byte 來測試
              logger.d('檔案存在（通過資料下載確認）');
              return true;
            } catch (dataError) {
              logger.d('無法下載資料: $dataError');
              return false;
            }
          }
        } else {
          logger.d('Web 平台跳過 metadata 和 getData 檢查以避免 ClientException');
          return false;
        }
      }
    } catch (e) {
      logger.w('檢查檔案存在時發生錯誤: $e');
      return false;
    }
  }

  /// Web 平台專用的檔案下載方法（使用 Anchor 下載）
  static Future<Uint8List?> _downloadFileWeb(Reference ref) async {
    try {
      logger.d('使用 Web 平台 Anchor 下載方法');

      // 獲取下載 URL
      final downloadUrl = await ref.getDownloadURL();
      logger.d('獲取到下載 URL，使用 Anchor 元素下載...');

      // 使用 Anchor 元素觸發下載
      if (kIsWeb) {
        web_download.downloadFileWithAnchor(downloadUrl, 'birth_data_backup.csv');
        logger.i('已觸發 Anchor 下載，檔案將由瀏覽器處理');

        // 對於 Anchor 下載，我們無法直接獲取檔案內容
        // 但可以嘗試用 HTTP 獲取內容用於後續處理
        try {
          final response = await http.get(Uri.parse(downloadUrl));
          if (response.statusCode == 200) {
            logger.d('HTTP 獲取檔案內容成功，大小: ${response.bodyBytes.length} bytes');
            return response.bodyBytes;
          } else {
            logger.w('HTTP 獲取檔案內容失敗，狀態碼: ${response.statusCode}');
            // Anchor 下載已觸發，即使無法獲取內容也算成功
            return Uint8List(0); // 返回空數據表示下載已觸發
          }
        } catch (httpError) {
          logger.w('HTTP 獲取檔案內容失敗: $httpError');
          // Anchor 下載已觸發，即使無法獲取內容也算成功
          return Uint8List(0); // 返回空數據表示下載已觸發
        }
      } else {
        // 非 Web 平台，使用標準方法
        return await ref.getData();
      }

    } catch (e) {
      logger.w('Web 平台 Anchor 下載失敗: $e');
      rethrow;
    }
  }

  /// 替代的檔案下載方法
  static Future<Uint8List?> _downloadFileAlternative(Reference ref) async {
    try {
      logger.d('使用替代方法下載檔案 - 平台: ${kIsWeb ? "Web" : "Mobile"}');

      // 在 Web 平台上，嘗試不同的方法順序
      if (kIsWeb) {
        // Web 平台方法1：嘗試使用較小的 maxSize 參數
        try {
          logger.d('Web 平台：嘗試使用限制大小的 getData()...');
          final data = await ref.getData(10 * 1024 * 1024); // 限制 10MB
          if (data != null && data.isNotEmpty) {
            logger.d('Web 平台限制大小下載成功，大小: ${data.length} bytes');
            return data;
          }
        } catch (limitedError) {
          logger.w('Web 平台限制大小下載失敗: $limitedError');
        }

        // Web 平台方法2：嘗試分段下載（如果支援）
        try {
          logger.d('Web 平台：嘗試獲取檔案 metadata...');
          final metadata = await ref.getMetadata();
          final fileSize = metadata.size ?? 0;

          if (fileSize > 0 && fileSize < 5 * 1024 * 1024) { // 小於 5MB 的檔案
            logger.d('Web 平台：檔案大小適中 ($fileSize bytes)，嘗試直接下載...');
            final data = await ref.getData();
            if (data != null && data.isNotEmpty) {
              logger.d('Web 平台直接下載成功，大小: ${data.length} bytes');
              return data;
            }
          } else {
            logger.w('Web 平台：檔案過大 ($fileSize bytes)，跳過直接下載');
          }
        } catch (metadataError) {
          logger.w('Web 平台獲取 metadata 失敗: $metadataError');
        }

      } else {
        // 移動平台：嘗試標準方法
        try {
          final data = await ref.getData();
          if (data != null && data.isNotEmpty) {
            logger.d('移動平台 getData() 替代方法下載成功，大小: ${data.length} bytes');
            return data;
          } else {
            logger.w('移動平台 getData() 替代方法下載的資料為空');
          }
        } catch (e) {
          logger.w('移動平台 getData() 替代方法失敗: $e');
        }
      }

      logger.w('所有替代下載方法都失敗');
      return null;

    } catch (e) {
      logger.w('替代下載方法失敗: $e');
      rethrow;
    }
  }

  /// 檢查是否存在雲端備份
  /// 返回備份資訊，如果不存在則返回 null
  static Future<Map<String, dynamic>?> getBackupInfo() async {
    try {
      // 檢查用戶是否已登入
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        return null;
      }

      logger.d('檢查 Firebase Storage 備份資訊 - 平台: ${kIsWeb ? "Web" : "Mobile"}');

      // 先檢查 Firebase Storage 連接狀態
      final isConnected = await checkConnection();
      if (!isConnected) {
        logger.w('Firebase Storage 連接不可用，跳過備份檢查');
        return {
          'exists': false,
          'size': 0,
          'timeCreated': null,
          'updated': null,
          'customMetadata': {},
          'contentType': 'text/csv',
          'note': 'firebase_storage_connection_unavailable',
        };
      }

      // 獲取 Firebase Storage 備份檔案的 metadata
      final ref = _storage.ref().child('$_backupFolder/${currentUser.uid}/$_backupFileName');

      // 使用重試機制處理 channel-error
      final result = await _getBackupInfoWithRetry(ref);

      // 如果結果包含連接問題的標記，記錄額外資訊
      if (result != null && result['note'] == 'connection_unavailable_assume_no_backup') {
        logger.w('Firebase Storage 連接問題，無法確認備份狀態');
        logger.i('建議：稍後再試，或檢查網路連接');
      }

      return result;

    } catch (e) {
      logger.d('獲取 Firebase Storage 備份資訊失敗: $e');
      // 避免將異常傳遞到 UI 層
      return null;
    }
  }

  /// 帶重試機制的備份資訊獲取
  static Future<Map<String, dynamic>?> _getBackupInfoWithRetry(Reference ref, {int maxRetries = 3}) async {
    for (int attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        logger.d('嘗試獲取備份資訊，第 $attempt 次');

        FullMetadata? metadata;
        try {
          metadata = await ref.getMetadata();
        } catch (e) {
          final errorMessage = e.toString();
          logger.d('獲取 metadata 失敗: $errorMessage');

          // 檢查是否是 channel-error
          if (errorMessage.contains('channel-error') ||
              errorMessage.contains('Unable to establish connection')) {
            logger.w('檢測到 channel-error，嘗試使用替代方法...');

            // 在 Web 平台上，如果檔案不存在，可能會拋出不同的異常
            if (kIsWeb && errorMessage.contains('object-not-found')) {
              return null;
            }

            // 嘗試其他方法檢查檔案是否存在
            try {
              await ref.getDownloadURL();
              logger.d('能獲取下載 URL，檔案存在但無法獲取 metadata');
              // 如果能獲取下載 URL，說明檔案存在，但無法獲取 metadata
              return {
                'exists': true,
                'size': 0,
                'timeCreated': null,
                'updated': null,
                'customMetadata': {},
                'contentType': 'text/csv',
                'note': 'metadata_unavailable_due_to_channel_error',
              };
            } catch (urlError) {
              final urlErrorMessage = urlError.toString();
              logger.d('檔案不存在: $urlErrorMessage');

              // 如果替代方法也是 channel-error，說明連接問題很嚴重
              if (urlErrorMessage.contains('channel-error') ||
                  urlErrorMessage.contains('Unable to establish connection')) {
                logger.w('替代方法也遇到 channel-error，Firebase Storage 連接問題嚴重');

                // 在最後一次嘗試時，提供一個假設性的回應
                if (attempt == maxRetries) {
                  logger.w('所有嘗試都失敗，但這可能是暫時的連接問題');
                  // 返回一個表示「可能存在但無法確認」的狀態
                  return {
                    'exists': false, // 保守起見，假設不存在
                    'size': 0,
                    'timeCreated': null,
                    'updated': null,
                    'customMetadata': {},
                    'contentType': 'text/csv',
                    'note': 'connection_unavailable_assume_no_backup',
                  };
                }
              } else if (urlErrorMessage.contains('object-not-found') ||
                        urlErrorMessage.contains('not-found')) {
                logger.d('確認檔案不存在');
                return null;
              }

              if (attempt < maxRetries) {
                logger.d('等待 ${attempt * 1500}ms 後重試...');
                await Future.delayed(Duration(milliseconds: attempt * 1500));
                continue;
              }
              return null;
            }
          } else if (errorMessage.contains('object-not-found') ||
                    errorMessage.contains('not-found')) {
            logger.d('確認檔案不存在');
            return null;
          } else {
            // 其他錯誤，直接重試
            if (attempt < maxRetries) {
              logger.d('等待 ${attempt * 1000}ms 後重試...');
              await Future.delayed(Duration(milliseconds: attempt * 1000));
              continue;
            }
            return null;
          }
        }

        // 成功獲取 metadata
        return {
          'exists': true,
          'size': metadata.size ?? 0,
          'timeCreated': metadata.timeCreated,
          'updated': metadata.updated,
          'customMetadata': metadata.customMetadata ?? {},
          'contentType': metadata.contentType,
        };

      } catch (e) {
        final errorMessage = e.toString();
        logger.w('第 $attempt 次嘗試失敗: $errorMessage');

        if (attempt < maxRetries &&
            (errorMessage.contains('channel-error') ||
             errorMessage.contains('Unable to establish connection'))) {
          logger.d('等待 ${attempt * 1500}ms 後重試...');
          await Future.delayed(Duration(milliseconds: attempt * 1500));
          continue;
        }

        // 最後一次嘗試失敗或非 channel-error
        return null;
      }
    }

    return null;
  }

  /// 刪除雲端備份
  /// 返回刪除是否成功
  static Future<bool> deleteBackup() async {
    try {
      // 檢查用戶是否已登入
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        logger.w('用戶未登入，無法刪除雲端備份');
        return false;
      }

      logger.i('刪除 Firebase Storage 備份 - 平台: ${kIsWeb ? "Web" : "Mobile"}');

      // 刪除 Firebase Storage 備份檔案
      final ref = _storage.ref().child('$_backupFolder/${currentUser.uid}/$_backupFileName');

      try {
        await ref.delete();
        logger.i('成功刪除 Firebase Storage 雲端備份');
        return true;
      } catch (e) {
        final errorMessage = e.toString();
        logger.w('刪除備份檔案失敗: $errorMessage');

        // 在某些情況下，檔案可能已經不存在
        if (errorMessage.contains('object-not-found') ||
            errorMessage.contains('not-found') ||
            errorMessage.contains('does not exist')) {
          logger.i('備份檔案已經不存在，視為刪除成功');
          return true;
        }

        // 如果是 channel-error，嘗試重試
        if (errorMessage.contains('channel-error') ||
            errorMessage.contains('Unable to establish connection')) {
          logger.w('檢測到 channel-error，嘗試重試刪除...');
          try {
            await Future.delayed(const Duration(milliseconds: 1000));
            await ref.delete();
            logger.i('重試刪除成功');
            return true;
          } catch (retryError) {
            logger.e('重試刪除也失敗: $retryError');
            return false;
          }
        }

        return false;
      }

    } catch (e) {
      logger.e('刪除 Firebase Storage 雲端備份失敗: $e');
      // 避免將異常傳遞到 UI 層
      return false;
    }
  }

  /// 格式化檔案大小
  static String formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  /// 格式化日期時間
  static String formatDateTime(DateTime? dateTime) {
    if (dateTime == null) return '未知';
    return '${dateTime.year}/${dateTime.month.toString().padLeft(2, '0')}/${dateTime.day.toString().padLeft(2, '0')} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
