import 'dart:io';

import 'package:flutter/foundation.dart';

import '../../../core/utils/logger_utils.dart';
import 'iap_service.dart';
import 'web_payment_service.dart';

/// 統一支付服務 - 根據平台自動選擇 IAP 或 Web 支付
class UnifiedPaymentService {
  static bool _isInitialized = false;

  /// 初始化統一支付服務
  static Future<bool> initialize() async {
    try {
      if (_isInitialized) {
        logger.i('統一支付服務已初始化');
        return true;
      }

      bool success = false;

      if (_shouldUseIAP()) {
        // Android/iOS 使用 IAP
        success = await IAPService.initialize();
        logger.i('使用 IAP 支付服務');
      } else {
        // 其他平台使用 Web 支付
        success = await WebPaymentService.initialize();
        logger.i('使用 Web 支付服務');
      }

      _isInitialized = success;
      return success;
    } catch (e) {
      logger.e('統一支付服務初始化失敗: $e');
      return false;
    }
  }

  /// 判斷是否應該使用 IAP
  static bool _shouldUseIAP() {
    if (kIsWeb) return false;
    
    try {
      return Platform.isAndroid || Platform.isIOS;
    } catch (e) {
      logger.w('無法檢測平台，使用 Web 支付: $e');
      return false;
    }
  }

  /// 獲取可用商品列表
  static Future<List<Map<String, dynamic>>> getAvailableProducts() async {
    try {
      if (!_isInitialized) {
        await initialize();
      }

      if (_shouldUseIAP()) {
        final products = IAPService.getAvailableProducts();
        return products.map((product) => {
          'id': product.id,
          'title': product.title,
          'description': product.description,
          'price': product.price,
          'currencyCode': product.currencyCode,
          'rawPrice': product.rawPrice,
        }).toList();
      } else {
        return WebPaymentService.getAvailableProducts();
      }
    } catch (e) {
      logger.e('獲取可用商品列表失敗: $e');
      return [];
    }
  }

  /// 購買商品
  static Future<bool> purchaseProduct(String productId) async {
    try {
      if (!_isInitialized) {
        await initialize();
      }

      if (_shouldUseIAP()) {
        // 根據商品類型選擇購買方法
        if (_isSubscriptionProduct(productId)) {
          return await IAPService.purchaseSubscription(productId);
        } else {
          return await IAPService.purchaseProduct(productId);
        }
      } else {
        // Web 支付需要指定支付方式
        return await _showWebPaymentOptions(productId);
      }
    } catch (e) {
      logger.e('購買商品失敗: $e');
      return false;
    }
  }

  /// 判斷是否為訂閱商品
  static bool _isSubscriptionProduct(String productId) {
    return productId.contains('monthly') || 
           productId.contains('yearly') || 
           productId.contains('premium');
  }

  /// 顯示 Web 支付選項
  static Future<bool> _showWebPaymentOptions(String productId) async {
    try {
      // 這裡可以顯示支付方式選擇對話框
      // 暫時使用 Stripe 作為預設支付方式
      return await WebPaymentService.launchPayment(
        productId: productId,
        paymentMethod: 'stripe',
      );
    } catch (e) {
      logger.e('顯示 Web 支付選項失敗: $e');
      return false;
    }
  }

  /// 恢復購買
  static Future<bool> restorePurchases() async {
    try {
      if (!_isInitialized) {
        await initialize();
      }

      if (_shouldUseIAP()) {
        // IAP 恢復購買功能暫時不可用
        logger.i('IAP 恢復購買功能暫時不可用');
        return true;
      } else {
        // Web 支付不需要恢復購買
        logger.i('Web 支付不需要恢復購買');
        return true;
      }
    } catch (e) {
      logger.e('恢復購買失敗: $e');
      return false;
    }
  }

  /// 檢查支付服務是否可用
  static bool isAvailable() {
    if (_shouldUseIAP()) {
      return IAPService.isAvailable();
    } else {
      return WebPaymentService.isAvailable();
    }
  }

  /// 獲取當前使用的支付方式
  static String getCurrentPaymentMethod() {
    if (_shouldUseIAP()) {
      if (kIsWeb) return 'web';
      try {
        if (Platform.isAndroid) return 'google_play';
        if (Platform.isIOS) return 'app_store';
      } catch (e) {
        logger.w('無法檢測平台: $e');
      }
      return 'iap';
    } else {
      return 'web';
    }
  }

  /// 處理支付成功回調（用於 Web 支付）
  static Future<bool> handlePaymentSuccess({
    required String productId,
    required String userId,
    required String transactionId,
    String? paymentMethod,
  }) async {
    try {
      if (_shouldUseIAP()) {
        // IAP 支付成功由 IAP 服務自動處理
        logger.i('IAP 支付成功由服務自動處理');
        return true;
      } else {
        // Web 支付需要手動處理
        return await WebPaymentService.handlePaymentSuccess(
          productId: productId,
          userId: userId,
          transactionId: transactionId,
          paymentMethod: paymentMethod ?? 'web',
        );
      }
    } catch (e) {
      logger.e('處理支付成功回調失敗: $e');
      return false;
    }
  }

  /// 獲取支付服務狀態資訊
  static Map<String, dynamic> getServiceStatus() {
    return {
      'isInitialized': _isInitialized,
      'paymentMethod': getCurrentPaymentMethod(),
      'isAvailable': isAvailable(),
      'shouldUseIAP': _shouldUseIAP(),
      'platform': _getPlatformInfo(),
    };
  }

  /// 獲取平台資訊
  static String _getPlatformInfo() {
    if (kIsWeb) return 'web';
    
    try {
      if (Platform.isAndroid) return 'android';
      if (Platform.isIOS) return 'ios';
      if (Platform.isWindows) return 'windows';
      if (Platform.isMacOS) return 'macos';
      if (Platform.isLinux) return 'linux';
    } catch (e) {
      logger.w('無法檢測平台: $e');
    }
    
    return 'unknown';
  }

  /// 清理資源
  static void dispose() {
    try {
      if (_shouldUseIAP()) {
        IAPService.dispose();
      }
      _isInitialized = false;
      logger.i('統一支付服務已清理');
    } catch (e) {
      logger.e('清理統一支付服務失敗: $e');
    }
  }

  /// 測試支付服務
  static Future<Map<String, dynamic>> testPaymentService() async {
    final result = <String, dynamic>{};
    
    try {
      // 測試初始化
      result['initialization'] = await initialize();
      
      // 測試獲取商品
      final products = await getAvailableProducts();
      result['products_count'] = products.length;
      result['products'] = products.take(3).toList(); // 只返回前3個商品
      
      // 測試服務狀態
      result['service_status'] = getServiceStatus();
      
      result['test_success'] = true;
    } catch (e) {
      result['test_success'] = false;
      result['error'] = e.toString();
    }
    
    return result;
  }
}
