import 'package:astreal/core/utils/logger_utils.dart';
import 'package:astreal/data/models/astrology/chart_data.dart';
import 'package:astreal/data/models/astrology/planet_position.dart';

import '../../models/astrology/chart_type.dart';
import '../../models/interpretation/financial_analysis.dart';
import '../../models/user/birth_data.dart';
import 'astrology_service.dart';
/// 財務分析服務
class FinancialAnalysisService {

  /// 分析個人財務星盤
  Future<FinancialAnalysis> analyzeFinancialProfile(BirthData birthData) async {
    logger.i('開始分析財務星盤：${birthData.name}');

    try {
      // 計算星盤數據
      final chartData = await AstrologyService().calculateChartData(
        ChartData(
          chartType: ChartType.natal,
          primaryPerson: birthData,
        ),
      );

      // 分析財務風格
      final financialStyle = await _analyzeFinancialStyle(chartData);

      // 分析宮位
      final houseAnalyses = await _analyzeFinancialHouses(chartData);

      // 分析行星影響
      final planetInfluences = await _analyzePlanetFinancialInfluences(chartData);

      // 生成年度財運預測
      final yearlyForecast = await _generateYearlyForecast(chartData);

      // 生成投資建議
      final investmentAdvice = await _generateInvestmentAdvice(chartData, financialStyle);

      // 生成摘要
      final summary = _generateOverallSummary(financialStyle, houseAnalyses, planetInfluences);
      final strengths = _identifyKeyStrengths(financialStyle, houseAnalyses);
      final challenges = _identifyPotentialChallenges(financialStyle, houseAnalyses);

      return FinancialAnalysis(
        personName: birthData.name,
        birthDate: birthData.dateTime,
        analysisDate: DateTime.now(),
        financialStyle: financialStyle,
        houseAnalyses: houseAnalyses,
        planetInfluences: planetInfluences,
        yearlyForecast: yearlyForecast,
        investmentAdvice: investmentAdvice,
        overallSummary: summary,
        keyStrengths: strengths,
        potentialChallenges: challenges,
      );
    } catch (e) {
      logger.e('財務分析失敗：$e');
      rethrow;
    }
  }

  /// 分析財務風格
  Future<FinancialStyle> _analyzeFinancialStyle(ChartData chartData) async {
    // 分析第2宮（財帛宮）、第5宮（投資宮）、第8宮（合夥財）
    final house2 = _getHousePlanets(chartData, 2);
    final house5 = _getHousePlanets(chartData, 5);
    final house8 = _getHousePlanets(chartData, 8);

    // 分析金星、木星、土星的位置
    final venus = _findPlanet(chartData, '金星');
    final jupiter = _findPlanet(chartData, '木星');
    final saturn = _findPlanet(chartData, '土星');

    // 計算風險承受度
    final riskTolerance = _calculateRiskTolerance(venus, jupiter, saturn, house5);

    // 分析收入方式
    final incomeStyle = _analyzeIncomeStyle(house2, venus, jupiter);

    // 分析金錢吸引力
    final moneyAttraction = _analyzeMoneyAttraction(venus, jupiter, house2);

    // 分析投資風格
    final investmentStyle = _analyzeInvestmentStyle(house5, jupiter, saturn);

    // 分析穩定性
    final stabilityLevel = _analyzeStabilityLevel(saturn, house2, house8);

    // 計算宮位影響力
    final houseInfluences = {
      '第2宮': _calculateHouseInfluence(house2),
      '第5宮': _calculateHouseInfluence(house5),
      '第8宮': _calculateHouseInfluence(house8),
    };

    return FinancialStyle(
      riskTolerance: riskTolerance,
      incomeStyle: incomeStyle,
      moneyAttraction: moneyAttraction,
      investmentStyle: investmentStyle,
      stabilityLevel: stabilityLevel,
      houseInfluences: houseInfluences,
    );
  }

  /// 分析財務相關宮位
  Future<List<HouseFinancialAnalysis>> _analyzeFinancialHouses(ChartData chartData) async {
    final analyses = <HouseFinancialAnalysis>[];

    // 分析第2宮（財帛宮）
    analyses.add(await _analyzeHouse(chartData, 2, '財帛宮', '個人收入、物質資源、價值觀'));

    // 分析第5宮（投資宮）
    analyses.add(await _analyzeHouse(chartData, 5, '投資宮', '投機、創意收入、風險投資'));

    // 分析第8宮（合夥財）
    analyses.add(await _analyzeHouse(chartData, 8, '合夥財', '他人資源、投資收益、遺產'));

    // 分析第10宮（事業宮）
    analyses.add(await _analyzeHouse(chartData, 10, '事業宮', '職業收入、社會地位、事業成就'));

    return analyses;
  }

  /// 分析單個宮位
  Future<HouseFinancialAnalysis> _analyzeHouse(
    ChartData chartData,
    int houseNumber,
    String houseName,
    String description,
  ) async {
    final planets = _getHousePlanets(chartData, houseNumber);
    final planetNames = planets.map((p) => p.name).toList();
    
    // 分析相位（簡化版）
    final aspects = <String>[];
    for (final planet in planets) {
      if (chartData.aspects != null) {
        final planetAspects = chartData.aspects!
            .where((a) => a.planet1.name == planet.name || a.planet2.name == planet.name)
            .map((a) => '${a.planet1.name}-${a.planet2.name} ${a.aspect}')
            .toList();
        aspects.addAll(planetAspects);
      }
    }

    final influence = _calculateHouseInfluence(planets);
    final interpretation = _interpretHouseFinancially(houseNumber, planets);

    return HouseFinancialAnalysis(
      houseNumber: houseNumber,
      houseName: houseName,
      description: description,
      planets: planetNames,
      aspects: aspects,
      influence: influence,
      interpretation: interpretation,
    );
  }

  /// 分析行星財務影響
  Future<List<PlanetFinancialInfluence>> _analyzePlanetFinancialInfluences(ChartData chartData) async {
    final influences = <PlanetFinancialInfluence>[];

    // 重點分析金星、木星、土星
    final importantPlanets = ['金星', '木星', '土星', '太陽', '月亮'];
    
    if (chartData.planets != null) {
      for (final planetName in importantPlanets) {
        final planet = _findPlanet(chartData, planetName);
        if (planet != null) {
          influences.add(await _analyzePlanetFinancialInfluence(chartData, planet));
        }
      }
    }

    return influences;
  }

  /// 分析單個行星的財務影響
  Future<PlanetFinancialInfluence> _analyzePlanetFinancialInfluence(
    ChartData chartData,
    PlanetPosition planet,
  ) async {
    final influence = _getPlanetFinancialInfluence(planet);
    final strength = _calculatePlanetFinancialStrength(planet);
    
    // 獲取相位
    final aspects = <String>[];
    if (chartData.aspects != null) {
      final planetAspects = chartData.aspects!
          .where((a) => a.planet1.name == planet.name || a.planet2.name == planet.name)
          .map((a) => a.aspect)
          .toList();
      aspects.addAll(planetAspects);
    }

    return PlanetFinancialInfluence(
      planetName: planet.name,
      sign: planet.sign,
      house: planet.house,
      influence: influence,
      strength: strength,
      aspects: aspects,
    );
  }

  /// 生成年度財運預測
  Future<YearlyFinancialForecast> _generateYearlyForecast(ChartData chartData) async {
    final currentYear = DateTime.now().year;
    final monthlyForecasts = <MonthlyForecast>[];

    // 生成12個月的預測
    for (int month = 1; month <= 12; month++) {
      monthlyForecasts.add(await _generateMonthlyForecast(chartData, currentYear, month));
    }

    final overallScore = monthlyForecasts.map((f) => f.fortuneScore).reduce((a, b) => a + b) / 12;
    final overallTrend = overallScore >= 70 ? '財運亨通' : overallScore >= 50 ? '平穩發展' : '需謹慎理財';

    return YearlyFinancialForecast(
      year: currentYear,
      overallScore: overallScore,
      overallTrend: overallTrend,
      monthlyForecasts: monthlyForecasts,
      majorTransits: ['木星進入第2宮', '土星三分金星'], // 示例
      planetaryInfluences: {
        '木星': '帶來財運機會',
        '土星': '要求謹慎理財',
        '金星': '增強金錢吸引力',
      },
    );
  }

  /// 生成月度財運預測
  Future<MonthlyForecast> _generateMonthlyForecast(
    ChartData chartData,
    int year,
    int month,
  ) async {
    // 簡化的月度預測邏輯
    final fortuneScore = 50.0 + (month % 3) * 15.0; // 示例計算
    final isPositive = fortuneScore >= 60;
    
    final monthNames = [
      '', '一月', '二月', '三月', '四月', '五月', '六月',
      '七月', '八月', '九月', '十月', '十一月', '十二月'
    ];

    return MonthlyForecast(
      month: month,
      monthName: monthNames[month],
      isPositive: isPositive,
      fortuneScore: fortuneScore,
      description: isPositive ? '財運順遂，適合投資' : '需謹慎理財，避免風險',
      keyEvents: ['重要財務決策', '投資機會'],
      planetaryInfluences: ['木星影響', '金星加持'],
    );
  }

  /// 生成投資建議
  Future<List<InvestmentAdvice>> _generateInvestmentAdvice(
    ChartData chartData,
    FinancialStyle financialStyle,
  ) async {
    final advice = <InvestmentAdvice>[];

    // 根據風險承受度提供建議
    switch (financialStyle.riskTolerance) {
      case RiskTolerance.conservative:
        advice.addAll(_getConservativeInvestmentAdvice());
        break;
      case RiskTolerance.moderate:
        advice.addAll(_getModerateInvestmentAdvice());
        break;
      case RiskTolerance.aggressive:
        advice.addAll(_getAggressiveInvestmentAdvice());
        break;
    }

    return advice;
  }

  // 輔助方法
  List<PlanetPosition> _getHousePlanets(ChartData chartData, int house) {
    if (chartData.planets == null) return [];
    return chartData.planets!.where((p) => p.house == house).toList();
  }

  PlanetPosition? _findPlanet(ChartData chartData, String planetName) {
    if (chartData.planets == null) return null;
    try {
      return chartData.planets!.firstWhere((p) => p.name == planetName);
    } catch (e) {
      return null;
    }
  }

  RiskTolerance _calculateRiskTolerance(
    PlanetPosition? venus,
    PlanetPosition? jupiter,
    PlanetPosition? saturn,
    List<PlanetPosition> house5,
  ) {
    // 簡化的風險承受度計算
    int score = 0;
    
    if (jupiter != null && house5.contains(jupiter)) score += 2;
    if (saturn != null && saturn.house == 2) score -= 1;
    if (house5.length > 2) score += 1;

    if (score >= 2) return RiskTolerance.aggressive;
    if (score >= 0) return RiskTolerance.moderate;
    return RiskTolerance.conservative;
  }

  String _analyzeIncomeStyle(List<PlanetPosition> house2, PlanetPosition? venus, PlanetPosition? jupiter) {
    if (house2.isEmpty) return '傳統薪資收入';
    if (house2.any((p) => p.name == '金星')) return '藝術創意收入';
    if (house2.any((p) => p.name == '木星')) return '多元化收入來源';
    return '穩定工作收入';
  }

  String _analyzeMoneyAttraction(PlanetPosition? venus, PlanetPosition? jupiter, List<PlanetPosition> house2) {
    if (venus != null && venus.house == 2) return '天生的金錢吸引力';
    if (jupiter != null && jupiter.house == 2) return '幸運的財富機會';
    return '需要努力累積財富';
  }

  String _analyzeInvestmentStyle(List<PlanetPosition> house5, PlanetPosition? jupiter, PlanetPosition? saturn) {
    if (house5.any((p) => p.name == '木星')) return '樂觀積極的投資風格';
    if (house5.any((p) => p.name == '土星')) return '謹慎保守的投資策略';
    return '平衡的投資方式';
  }

  String _analyzeStabilityLevel(PlanetPosition? saturn, List<PlanetPosition> house2, List<PlanetPosition> house8) {
    if (saturn != null && saturn.house == 2) return '財務穩定性高';
    if (house8.length > 1) return '財務波動較大';
    return '財務穩定性中等';
  }

  double _calculateHouseInfluence(List<PlanetPosition> planets) {
    return planets.length * 20.0; // 簡化計算
  }

  String _interpretHouseFinancially(int houseNumber, List<PlanetPosition> planets) {
    if (planets.isEmpty) return '此宮位對財務影響較小';
    
    switch (houseNumber) {
      case 2:
        return '影響個人收入和理財觀念';
      case 5:
        return '影響投資運氣和創意收入';
      case 8:
        return '影響投資收益和他人資源';
      case 10:
        return '影響事業收入和社會地位';
      default:
        return '對財務有間接影響';
    }
  }

  String _getPlanetFinancialInfluence(PlanetPosition planet) {
    switch (planet.name) {
      case '金星':
        return '增強金錢吸引力和藝術收入';
      case '木星':
        return '帶來財運機會和投資運氣';
      case '土星':
        return '要求謹慎理財和長期規劃';
      case '太陽':
        return '影響事業收入和領導能力';
      case '月亮':
        return '影響理財情緒和安全感';
      default:
        return '對財務有特定影響';
    }
  }

  double _calculatePlanetFinancialStrength(PlanetPosition planet) {
    // 簡化的強度計算
    double strength = 50.0;
    
    // 根據宮位調整
    if ([2, 5, 8, 10].contains(planet.house)) {
      strength += 20.0;
    }
    
    return strength.clamp(0.0, 100.0);
  }

  String _generateOverallSummary(
    FinancialStyle style,
    List<HouseFinancialAnalysis> houses,
    List<PlanetFinancialInfluence> planets,
  ) {
    return '根據星盤分析，您的財務風格偏向${_getRiskToleranceText(style.riskTolerance)}，'
           '建議採用${style.investmentStyle}的投資策略。';
  }

  List<String> _identifyKeyStrengths(FinancialStyle style, List<HouseFinancialAnalysis> houses) {
    return [
      '具有${_getRiskToleranceText(style.riskTolerance)}的投資特質',
      style.moneyAttraction,
      style.stabilityLevel,
    ];
  }

  List<String> _identifyPotentialChallenges(FinancialStyle style, List<HouseFinancialAnalysis> houses) {
    return [
      '需要注意風險控制',
      '建議多元化投資組合',
      '定期檢視財務目標',
    ];
  }

  String _getRiskToleranceText(RiskTolerance tolerance) {
    switch (tolerance) {
      case RiskTolerance.conservative:
        return '保守型';
      case RiskTolerance.moderate:
        return '穩健型';
      case RiskTolerance.aggressive:
        return '積極型';
    }
  }

  List<InvestmentAdvice> _getConservativeInvestmentAdvice() {
    return [
      InvestmentAdvice(
        type: InvestmentType.savings,
        name: '定期存款',
        description: '穩定的保本投資選擇',
        suitabilityScore: 90,
        riskLevel: RiskTolerance.conservative,
        reasoning: '適合保守型投資者的安全選擇',
        considerations: ['利率較低', '流動性受限'],
      ),
      InvestmentAdvice(
        type: InvestmentType.bonds,
        name: '政府債券',
        description: '低風險的固定收益投資',
        suitabilityScore: 85,
        riskLevel: RiskTolerance.conservative,
        reasoning: '政府信用背書，風險極低',
        considerations: ['收益率有限', '通脹風險'],
      ),
    ];
  }

  List<InvestmentAdvice> _getModerateInvestmentAdvice() {
    return [
      InvestmentAdvice(
        type: InvestmentType.etf,
        name: 'ETF基金',
        description: '分散風險的指數型投資',
        suitabilityScore: 80,
        riskLevel: RiskTolerance.moderate,
        reasoning: '平衡風險與收益的理想選擇',
        considerations: ['市場波動風險', '管理費用'],
      ),
      InvestmentAdvice(
        type: InvestmentType.stocks,
        name: '藍籌股',
        description: '穩健的股票投資',
        suitabilityScore: 75,
        riskLevel: RiskTolerance.moderate,
        reasoning: '大型企業股票相對穩定',
        considerations: ['股價波動', '需要研究分析'],
      ),
    ];
  }

  List<InvestmentAdvice> _getAggressiveInvestmentAdvice() {
    return [
      InvestmentAdvice(
        type: InvestmentType.stocks,
        name: '成長股',
        description: '高成長潛力的股票投資',
        suitabilityScore: 85,
        riskLevel: RiskTolerance.aggressive,
        reasoning: '適合追求高收益的積極投資者',
        considerations: ['高波動風險', '需要專業知識'],
      ),
      InvestmentAdvice(
        type: InvestmentType.crypto,
        name: '加密貨幣',
        description: '新興的數位資產投資',
        suitabilityScore: 70,
        riskLevel: RiskTolerance.aggressive,
        reasoning: '具有高成長潛力的新興市場',
        considerations: ['極高風險', '監管不確定性'],
      ),
    ];
  }
}
