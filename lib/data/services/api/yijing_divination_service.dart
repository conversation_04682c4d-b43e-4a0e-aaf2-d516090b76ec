import 'dart:math';

import '../../models/interpretation/yijing_hexagram.dart';
/// 周易卜卦服務
class YijingDivinationService {
  static final Random _random = Random();

  /// 執行周易卜卦
  static YijingDivinationResult performDivination(String question) {
    // 生成六爻
    final lines = <bool>[];
    final changingLines = <int>[];
    
    for (int i = 0; i < 6; i++) {
      // 模擬投擲三枚硬幣的過程
      final coins = [_random.nextBool(), _random.nextBool(), _random.nextBool()];
      final heads = coins.where((coin) => coin).length;
      
      switch (heads) {
        case 0: // 三個反面 - 老陰（變爻）
          lines.add(false);
          changingLines.add(i);
          break;
        case 1: // 一個正面 - 少陽
          lines.add(true);
          break;
        case 2: // 兩個正面 - 少陰
          lines.add(false);
          break;
        case 3: // 三個正面 - 老陽（變爻）
          lines.add(true);
          changingLines.add(i);
          break;
      }
    }

    // 根據六爻生成卦象
    final primaryHexagram = _getHexagramFromLines(lines);
    
    // 如果有變爻，生成變卦
    YijingHexagram? changingHexagram;
    if (changingLines.isNotEmpty) {
      final changingLinesCopy = List<bool>.from(lines);
      for (final lineIndex in changingLines) {
        changingLinesCopy[lineIndex] = !changingLinesCopy[lineIndex];
      }
      changingHexagram = _getHexagramFromLines(changingLinesCopy);
    }

    return YijingDivinationResult(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      question: question,
      divinationTime: DateTime.now(),
      primaryHexagram: primaryHexagram,
      changingHexagram: changingHexagram,
      changingLines: changingLines,
    );
  }

  /// 根據六爻生成卦象
  static YijingHexagram _getHexagramFromLines(List<bool> lines) {
    // 計算卦序號（1-64）
    int number = 0;
    for (int i = 0; i < 6; i++) {
      if (lines[i]) {
        number += (1 << i);
      }
    }
    number = (number % 64) + 1; // 確保在 1-64 範圍內

    // 確保索引在有效範圍內
    final index = (number - 1) % _hexagrams.length;
    return _hexagrams[index];
  }

  /// 64卦數據（簡化版本，實際應用中應該包含完整的卦辭和爻辭）
  static final List<YijingHexagram> _hexagrams = [
    // 乾卦
    YijingHexagram(
      number: 1,
      name: '乾',
      symbol: '☰',
      lines: [true, true, true, true, true, true],
      upperTrigram: '乾',
      lowerTrigram: '乾',
      judgment: '乾：元，亨，利，貞。',
      image: '天行健，君子以自強不息。',
      lineTexts: [
        '初九：潛龍勿用。',
        '九二：見龍在田，利見大人。',
        '九三：君子終日乾乾，夕惕若，厲無咎。',
        '九四：或躍在淵，無咎。',
        '九五：飛龍在天，利見大人。',
        '上九：亢龍有悔。',
      ],
    ),
    // 坤卦
    YijingHexagram(
      number: 2,
      name: '坤',
      symbol: '☷',
      lines: [false, false, false, false, false, false],
      upperTrigram: '坤',
      lowerTrigram: '坤',
      judgment: '坤：元，亨，利牝馬之貞。',
      image: '地勢坤，君子以厚德載物。',
      lineTexts: [
        '初六：履霜，堅冰至。',
        '六二：直，方，大，不習無不利。',
        '六三：含章可貞。或從王事，無成有終。',
        '六四：括囊；無咎，無譽。',
        '六五：黃裳，元吉。',
        '上六：龍戰於野，其血玄黃。',
      ],
    ),
    // 屯卦
    YijingHexagram(
      number: 3,
      name: '屯',
      symbol: '☳',
      lines: [true, false, false, false, true, false],
      upperTrigram: '坎',
      lowerTrigram: '震',
      judgment: '屯：元，亨，利，貞。勿用，有攸往，利建侯。',
      image: '雲雷，屯；君子以經綸。',
      lineTexts: [
        '初九：磐桓；利居貞，利建侯。',
        '六二：屯如邅如，乘馬班如。匪寇婚媾，女子貞不字，十年乃字。',
        '六三：即鹿無虞，惟入於林中，君子幾不如舍，往吝。',
        '六四：乘馬班如，求婚媾，往吉，無不利。',
        '九五：屯其膏，小貞吉，大貞凶。',
        '上六：乘馬班如，泣血漣如。',
      ],
    ),
    // 蒙卦
    YijingHexagram(
      number: 4,
      name: '蒙',
      symbol: '☶',
      lines: [false, true, false, false, false, true],
      upperTrigram: '艮',
      lowerTrigram: '坎',
      judgment: '蒙：亨。匪我求童蒙，童蒙求我。',
      image: '山下出泉，蒙；君子以果行育德。',
      lineTexts: [
        '初六：發蒙，利用刑人，用說桎梏，以往吝。',
        '九二：包蒙吉；納婦吉；子克家。',
        '六三：勿用娶女；見金夫，不有躬，無攸利。',
        '六四：困蒙，吝。',
        '六五：童蒙，吉。',
        '上九：擊蒙；不利為寇，利禦寇。',
      ],
    ),
    // 需卦
    YijingHexagram(
      number: 5,
      name: '需',
      symbol: '☰',
      lines: [true, true, true, false, true, false],
      upperTrigram: '乾',
      lowerTrigram: '坎',
      judgment: '需：有孚，光亨，貞吉。利涉大川。',
      image: '雲上於天，需；君子以飲食宴樂。',
      lineTexts: [
        '初九：需於郊。利用恆，無咎。',
        '九二：需於沙。小有言，終吉。',
        '九三：需於泥，致寇至。',
        '六四：需於血，出自穴。',
        '九五：需於酒食，貞吉。',
        '上六：入於穴，有不速之客三人來，敬之終吉。',
      ],
    ),
  ];

  /// 獲取所有卦象（用於測試或展示）
  static List<YijingHexagram> getAllHexagrams() {
    return List.unmodifiable(_hexagrams);
  }

  /// 根據卦序號獲取卦象
  static YijingHexagram? getHexagramByNumber(int number) {
    if (number < 1 || number > 64) return null;

    // 確保索引在有效範圍內
    final index = (number - 1) % _hexagrams.length;
    return _hexagrams[index];
  }
}
