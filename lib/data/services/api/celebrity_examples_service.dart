import 'dart:convert';

import '../../../core/utils/logger_utils.dart';
import '../../models/astrology/chart_category.dart';
import '../../models/interpretation/celebrity_example.dart';
import '../../models/user/birth_data.dart';
import 'remote_config_service.dart';

/// 名人解讀範例服務
class CelebrityExamplesService {
  static final CelebrityExamplesService _instance = CelebrityExamplesService._internal();
  factory CelebrityExamplesService() => _instance;
  CelebrityExamplesService._internal();

  List<CelebrityExample>? _cachedExamples;
  DateTime? _lastFetchTime;
  static const Duration _cacheExpiry = Duration(hours: 1);

  /// 獲取所有名人範例
  List<CelebrityExample> getAllExamples() {
    return _getExamplesFromRemoteConfig();
  }

  /// 根據類別獲取名人範例
  List<CelebrityExample> getExamplesByCategory(CelebrityCategory category) {
    final examples = _getExamplesFromRemoteConfig();
    return examples.where((example) => example.category == category).toList();
  }

  /// 獲取熱門名人範例
  List<CelebrityExample> getPopularExamples() {
    final examples = _getExamplesFromRemoteConfig();
    return examples.where((example) => example.isPopular).toList();
  }

  /// 搜尋名人範例
  List<CelebrityExample> searchExamples(String query) {
    final examples = _getExamplesFromRemoteConfig();
    final lowerQuery = query.toLowerCase();
    return examples.where((example) {
      return example.name.toLowerCase().contains(lowerQuery) ||
             example.description.toLowerCase().contains(lowerQuery) ||
             example.recommendedTopics.any((topic) => topic.toLowerCase().contains(lowerQuery));
    }).toList();
  }

  /// 從 Remote Config 獲取名人範例
  List<CelebrityExample> _getExamplesFromRemoteConfig() {
    try {
      // 檢查快取是否有效
      // if (_cachedExamples != null && _lastFetchTime != null) {
      //   final now = DateTime.now();
      //   if (now.difference(_lastFetchTime!) < _cacheExpiry) {
      //     logger.d('使用快取的名人範例，數量: ${_cachedExamples!.length}');
      //     return _cachedExamples!;
      //   }
      // }

      logger.d('從 Remote Config 獲取名人範例...');

      // 從 Remote Config 獲取配置
      final configValue = RemoteConfigService.getConfigValue('celebrity_examples');

      if (configValue.isNotEmpty) {
        logger.d('Remote Config 名人範例配置長度: ${configValue.length}');

        final Map<String, dynamic> config = jsonDecode(configValue);
        final List<dynamic> examplesData = config['examples'] as List<dynamic>? ?? [];

        logger.d('Remote Config 中的名人範例數量: ${examplesData.length}');

        final examples = examplesData.map((data) => _parseExampleFromJson(data)).where((example) => example != null).cast<CelebrityExample>().toList();

        // 更新快取
        _cachedExamples = examples;
        _lastFetchTime = DateTime.now();

        logger.i('成功從 Remote Config 載入 ${examples.length} 個名人範例');
        return examples;
      } else {
        logger.w('Remote Config 中沒有名人範例配置，使用預設範例');
        _cachedExamples = _getDefaultExamples();
        _lastFetchTime = DateTime.now();
        return _cachedExamples!;
      }
    } catch (e) {
      logger.e('從 Remote Config 獲取名人範例失敗: $e');
      logger.w('降級使用預設名人範例');
      _cachedExamples = _getDefaultExamples();
      _lastFetchTime = DateTime.now();
      return _cachedExamples!;
    }
  }

  /// 從 JSON 解析名人範例
  CelebrityExample? _parseExampleFromJson(Map<String, dynamic> data) {
    try {
      final birthDataJson = data['birthData'] as Map<String, dynamic>;
      final categoryString = data['category'] as String;

      // 解析類別
      CelebrityCategory category;
      switch (categoryString) {
        case 'politician':
          category = CelebrityCategory.politician;
          break;
        case 'entertainment':
          category = CelebrityCategory.entertainment;
          break;
        case 'scholar':
          category = CelebrityCategory.scholar;
          break;
        case 'business':
          category = CelebrityCategory.business;
          break;
        case 'artist':
          category = CelebrityCategory.artist;
        case 'athlete':
          category = CelebrityCategory.athlete;
        case 'writer':
          category = CelebrityCategory.writer;
        case 'scientist':
          category = CelebrityCategory.scientist;
        case 'other':
          category = CelebrityCategory.other;
          break;
        default:
          logger.w('未知的名人類別: $categoryString，使用預設類別');
          category = CelebrityCategory.other;
      }

      // 解析出生資料
      final birthData = BirthData(
        id: birthDataJson['id'] as String,
        name: birthDataJson['name'] as String,
        dateTime: DateTime.parse(birthDataJson['dateTime'] as String),
        birthPlace: birthDataJson['birthPlace'] as String,
        latitude: (birthDataJson['latitude'] as num).toDouble(),
        longitude: (birthDataJson['longitude'] as num).toDouble(),
        notes: birthDataJson['notes'] as String?,
        category: ChartCategory.celebrity,
      );

      // 解析推薦主題
      final recommendedTopics = (data['recommendedTopics'] as List<dynamic>?)
          ?.map((topic) => topic.toString())
          .toList() ?? [];

      return CelebrityExample(
        name: data['name'] as String,
        birthData: birthData,
        description: data['description'] as String,
        category: category,
        recommendedTopics: recommendedTopics,
        isPopular: data['isPopular'] as bool? ?? false,
      );
    } catch (e) {
      logger.e('解析名人範例失敗: $e, 資料: $data');
      return null;
    }
  }

  /// 獲取預設名人範例
  List<CelebrityExample> _getDefaultExamples() {
    return _defaultCelebrityExamples;
  }

  /// 強制刷新名人範例
  Future<List<CelebrityExample>> refreshExamples() async {
    try {
      logger.i('強制刷新名人範例...');

      // 清除快取
      _cachedExamples = null;
      _lastFetchTime = null;

      // 嘗試刷新 Remote Config
      await RemoteConfigService.refresh();

      // 重新獲取範例
      final examples = _getExamplesFromRemoteConfig();

      logger.i('名人範例刷新完成，數量: ${examples.length}');
      return examples;
    } catch (e) {
      logger.e('刷新名人範例失敗: $e');
      return _getDefaultExamples();
    }
  }

  /// 創建名人的出生資料
  static BirthData _createBirthData({
    required String name,
    required DateTime dateTime,
    required String birthPlace,
    required double latitude,
    required double longitude,
    String? notes,
  }) {
    return BirthData(
      id: 'celebrity_${name.replaceAll(' ', '_').toLowerCase()}',
      name: name,
      dateTime: dateTime,
      birthPlace: birthPlace,
      latitude: latitude,
      longitude: longitude,
      notes: notes,
      category: ChartCategory.celebrity,
    );
  }

  /// 預設的名人範例列表（當 Remote Config 不可用時使用）
  static final List<CelebrityExample> _defaultCelebrityExamples = [
    // 政治人物
    CelebrityExample(
      name: '巴拉克・歐巴馬',
      birthData: _createBirthData(
        name: '巴拉克・歐巴馬',
        dateTime: DateTime(1961, 8, 4, 19, 24),
        birthPlace: '檀香山，夏威夷',
        latitude: 21.3099,
        longitude: -157.8581,
        notes: '第44任美國總統，有出生證書公開',
      ),
      description: '有出生證書公開，是現代政治人物中占星分析最多的案例之一。',
      category: CelebrityCategory.politician,
      recommendedTopics: ['領導力分析', '政治天賦', '公眾魅力', '演說能力'],
      isPopular: true,
    ),
    
    CelebrityExample(
      name: '唐納・川普',
      birthData: _createBirthData(
        name: '唐納・川普',
        dateTime: DateTime(1946, 6, 14, 10, 54),
        birthPlace: '紐約，紐約州',
        latitude: 40.7128,
        longitude: -74.0060,
        notes: '第45任美國總統，商業大亨',
      ),
      description: '本命盤與流年盤對應事件豐富，例如選舉、彈劾等。',
      category: CelebrityCategory.politician,
      recommendedTopics: ['商業天賦', '媒體影響力', '爭議性格', '權力慾望'],
      isPopular: true,
    ),

    CelebrityExample(
      name: '溫斯頓・邱吉爾',
      birthData: _createBirthData(
        name: '溫斯頓・邱吉爾',
        dateTime: DateTime(1874, 11, 30, 1, 30),
        birthPlace: '牛津郡，英國',
        latitude: 51.7520,
        longitude: -1.2577,
        notes: '英國首相，二戰領袖',
      ),
      description: '經典歷史人物，占星學校常見教學案例。',
      category: CelebrityCategory.politician,
      recommendedTopics: ['戰時領導', '演說天賦', '歷史使命', '堅韌意志'],
      isPopular: false,
    ),

    // 娛樂明星
    CelebrityExample(
      name: '泰勒・斯威夫特',
      birthData: _createBirthData(
        name: '泰勒・斯威夫特',
        dateTime: DateTime(1989, 12, 13, 5, 17),
        birthPlace: '賓州，美國',
        latitude: 40.2732,
        longitude: -76.8839,
        notes: '流行音樂天后，創作歌手',
      ),
      description: '情感生活與創作高度同步，適合戀愛線分析。',
      category: CelebrityCategory.entertainment,
      recommendedTopics: ['創作天賦', '感情模式', '音樂才華', '公眾形象'],
      isPopular: true,
    ),

    CelebrityExample(
      name: '小甜甜布蘭妮',
      birthData: _createBirthData(
        name: '小甜甜布蘭妮',
        dateTime: DateTime(1981, 12, 2, 1, 30),
        birthPlace: '密西西比州，美國',
        latitude: 32.7767,
        longitude: -90.2070,
        notes: '流行音樂巨星',
      ),
      description: '推運技術與心理狀態連動明顯，是療癒與生命歷程分析範例。',
      category: CelebrityCategory.entertainment,
      recommendedTopics: ['心理健康', '成名壓力', '家庭關係', '重生轉化'],
      isPopular: true,
    ),

    CelebrityExample(
      name: '李奧納多・狄卡皮歐',
      birthData: _createBirthData(
        name: '李奧納多・狄卡皮歐',
        dateTime: DateTime(1974, 11, 11, 2, 47),
        birthPlace: '洛杉磯，加州',
        latitude: 34.0522,
        longitude: -118.2437,
        notes: '奧斯卡影帝，環保主義者',
      ),
      description: '金錢、權勢與環保主題皆可探討。',
      category: CelebrityCategory.entertainment,
      recommendedTopics: ['演藝天賦', '環保使命', '財富管理', '社會責任'],
      isPopular: true,
    ),

    // 學者專家
    CelebrityExample(
      name: '卡爾・榮格',
      birthData: _createBirthData(
        name: '卡爾・榮格',
        dateTime: DateTime(1875, 7, 26, 19, 32),
        birthPlace: '瑞士',
        latitude: 47.3769,
        longitude: 8.5417,
        notes: '心理學家，分析心理學創始人',
      ),
      description: '與占星有密切淵源，常用來分析心理占星。',
      category: CelebrityCategory.scholar,
      recommendedTopics: ['心理洞察', '集體無意識', '原型理論', '靈性探索'],
      isPopular: true,
    ),

    CelebrityExample(
      name: '艾倫・里奧',
      birthData: _createBirthData(
        name: '艾倫・里奧',
        dateTime: DateTime(1860, 8, 7, 8, 2),
        birthPlace: '倫敦，英國',
        latitude: 51.5074,
        longitude: -0.1278,
        notes: '現代西方占星學奠基者',
      ),
      description: '現代西方占星奠基者之一，可研究占星觀念演進。',
      category: CelebrityCategory.scholar,
      recommendedTopics: ['占星天賦', '教學能力', '理論創新', '傳承使命'],
      isPopular: false,
    ),
  ];
}
