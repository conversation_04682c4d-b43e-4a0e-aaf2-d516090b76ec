import 'dart:convert';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../astreal.dart';
import '../../models/admin/system_announcement.dart';
import 'admin_service.dart';
import 'hidden_announcement_service.dart';
import 'remote_config_service.dart';

/// 系統公告管理服務
class SystemAnnouncementService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static const String _collectionName = 'system_announcements';

  /// 獲取所有系統公告（優先從 Remote Config）
  static Future<List<SystemAnnouncementWithId>> getAllAnnouncements() async {
    try {
      logger.i('獲取所有系統公告...');

      // 首先嘗試從 Remote Config 獲取
      final remoteConfigAnnouncements = await _getAnnouncementsFromRemoteConfig();

      if (remoteConfigAnnouncements.isNotEmpty) {
        // 排序公告
        remoteConfigAnnouncements.sort((a, b) {
          // 先按置頂排序
          if (a.isSticky != b.isSticky) {
            return b.isSticky ? 1 : -1;
          }
          // 再按優先級排序
          if (a.priority != b.priority) {
            return b.priority.index.compareTo(a.priority.index);
          }
          // 最後按創建時間排序
          return b.createdAt.compareTo(a.createdAt);
        });

        final announcements = remoteConfigAnnouncements.map((announcement) =>
            SystemAnnouncementWithId(
              id: announcement.id,
              announcement: announcement,
            )).toList();

        logger.i('成功從 Remote Config 獲取 ${announcements.length} 個系統公告');
        return announcements;
      }

      // 如果 Remote Config 沒有資料，從 Firestore 獲取（備用）
      logger.i('Remote Config 沒有資料，從 Firestore 獲取...');
      return await _getAnnouncementsFromFirestore();

    } catch (e) {
      logger.e('獲取系統公告失敗: $e');
      return [];
    }
  }

  /// 從 Firestore 獲取系統公告（備用方法）
  static Future<List<SystemAnnouncementWithId>> _getAnnouncementsFromFirestore() async {
    try {
      final querySnapshot = await _firestore
          .collection(_collectionName)
          .orderBy('isSticky', descending: true)
          .orderBy('priority', descending: true)
          .orderBy('createdAt', descending: true)
          .get();

      final announcements = <SystemAnnouncementWithId>[];

      for (final doc in querySnapshot.docs) {
        try {
          final data = doc.data();
          data['id'] = doc.id;

          final announcement = SystemAnnouncement.fromJson(data);
          announcements.add(SystemAnnouncementWithId(
            id: doc.id,
            announcement: announcement,
          ));
        } catch (e) {
          logger.w('解析系統公告失敗 (${doc.id}): $e');
        }
      }

      logger.i('成功從 Firestore 獲取 ${announcements.length} 個系統公告');
      return announcements;
    } catch (e) {
      logger.e('從 Firestore 獲取系統公告失敗: $e');
      return [];
    }
  }

  /// 獲取活躍的系統公告（用於用戶顯示）
  static Future<List<SystemAnnouncementWithId>> getActiveAnnouncements({
    String userType = 'all',
  }) async {
    try {
      logger.i('獲取活躍系統公告 (用戶類型: $userType)...');

      // 從 Remote Config 獲取所有公告
      final allAnnouncements = await _getAnnouncementsFromRemoteConfig();

      // 獲取已隱藏的公告 ID 列表
      final hiddenAnnouncementIds = await HiddenAnnouncementService.getHiddenAnnouncementIds();

      final announcements = <SystemAnnouncementWithId>[];

      for (final announcement in allAnnouncements) {
        try {
          // 檢查公告是否已被用戶隱藏
          if (hiddenAnnouncementIds.contains(announcement.id)) {
            logger.d('跳過已隱藏的公告: ${announcement.id}');
            continue;
          }

          // 檢查是否應該顯示給該用戶類型
          if (announcement.shouldDisplay(userType: userType)) {
            announcements.add(SystemAnnouncementWithId(
              id: announcement.id,
              announcement: announcement,
            ));
          }
        } catch (e) {
          logger.w('解析活躍系統公告失敗 (${announcement.id}): $e');
        }
      }

      // 排序公告
      announcements.sort((a, b) {
        // 先按置頂排序
        if (a.announcement.isSticky != b.announcement.isSticky) {
          return b.announcement.isSticky ? 1 : -1;
        }
        // 再按優先級排序
        if (a.announcement.priority != b.announcement.priority) {
          return b.announcement.priority.index.compareTo(a.announcement.priority.index);
        }
        // 最後按創建時間排序
        return b.announcement.createdAt.compareTo(a.announcement.createdAt);
      });

      logger.i('成功從 Remote Config 獲取 ${announcements.length} 個活躍系統公告（已過濾 ${hiddenAnnouncementIds.length} 個隱藏公告）');
      return announcements;
    } catch (e) {
      logger.e('獲取活躍系統公告失敗: $e');
      return [];
    }
  }

  /// 根據 ID 獲取系統公告
  static Future<SystemAnnouncement?> getAnnouncementById(String id) async {
    try {
      final doc = await _firestore
          .collection(_collectionName)
          .doc(id)
          .get();

      if (!doc.exists) {
        logger.w('系統公告不存在: $id');
        return null;
      }

      final data = doc.data()!;
      data['id'] = doc.id;

      return SystemAnnouncement.fromJson(data);
    } catch (e) {
      logger.e('獲取系統公告失敗 ($id): $e');
      return null;
    }
  }

  /// 新增系統公告（使用 Remote Config）
  static Future<String?> addAnnouncement(
    SystemAnnouncement announcement,
    String adminUid,
  ) async {
    try {
      logger.i('新增系統公告到 Remote Config: ${announcement.title}');

      // 獲取現有的公告列表
      final existingAnnouncements = await _getAnnouncementsFromRemoteConfig();

      // 生成新的 ID
      final newId = DateTime.now().millisecondsSinceEpoch.toString();
      final now = DateTime.now();

      // 創建新公告
      final newAnnouncement = announcement.copyWith(
        id: newId,
        createdAt: now,
        updatedAt: now,
        createdBy: adminUid,
        updatedBy: adminUid,
      );

      // 添加到列表
      final updatedAnnouncements = [...existingAnnouncements, newAnnouncement];

      // 更新 Remote Config
      final success = await _updateRemoteConfigAnnouncements(updatedAnnouncements, adminUid);

      if (success) {
        // 記錄管理者操作
        await AdminService.logAdminAction(
          adminUid: adminUid,
          action: 'create_announcement_remote_config',
          description: '新增系統公告到 Remote Config: ${announcement.title}',
          metadata: {
            'announcementId': newId,
            'type': announcement.type.name,
            'priority': announcement.priority.name,
          },
        );

        logger.i('系統公告新增到 Remote Config 成功: $newId');
        return newId;
      } else {
        throw Exception('更新 Remote Config 失敗');
      }
    } catch (e) {
      logger.e('新增系統公告到 Remote Config 失敗: $e');
      return null;
    }
  }

  /// 更新系統公告（使用 Remote Config）
  static Future<bool> updateAnnouncement(
    String id,
    SystemAnnouncement announcement,
    String adminUid,
  ) async {
    try {
      logger.i('更新系統公告到 Remote Config: $id - ${announcement.title}');

      // 獲取現有的公告列表
      final existingAnnouncements = await _getAnnouncementsFromRemoteConfig();

      // 找到要更新的公告
      final index = existingAnnouncements.indexWhere((a) => a.id == id);
      if (index == -1) {
        logger.w('找不到要更新的系統公告: $id');
        return false;
      }

      // 更新公告
      final updatedAnnouncement = announcement.copyWith(
        id: id,
        createdAt: existingAnnouncements[index].createdAt,
        updatedAt: DateTime.now(),
        createdBy: existingAnnouncements[index].createdBy,
        updatedBy: adminUid,
      );

      // 替換列表中的公告
      final updatedAnnouncements = [...existingAnnouncements];
      updatedAnnouncements[index] = updatedAnnouncement;

      // 更新 Remote Config
      final success = await _updateRemoteConfigAnnouncements(updatedAnnouncements, adminUid);

      if (success) {
        // 記錄管理者操作
        await AdminService.logAdminAction(
          adminUid: adminUid,
          action: 'update_announcement_remote_config',
          description: '更新系統公告到 Remote Config: ${announcement.title}',
          metadata: {
            'announcementId': id,
            'type': announcement.type.name,
            'priority': announcement.priority.name,
          },
        );

        logger.i('系統公告更新到 Remote Config 成功: $id');
        return true;
      } else {
        throw Exception('更新 Remote Config 失敗');
      }
    } catch (e) {
      logger.e('更新系統公告到 Remote Config 失敗: $e');
      return false;
    }
  }

  /// 刪除系統公告（使用 Remote Config）
  static Future<bool> deleteAnnouncement(String id, String adminUid) async {
    try {
      logger.i('從 Remote Config 刪除系統公告: $id');

      // 獲取現有的公告列表
      final existingAnnouncements = await _getAnnouncementsFromRemoteConfig();

      // 找到要刪除的公告
      final announcementToDelete = existingAnnouncements.firstWhere(
        (a) => a.id == id,
        orElse: () => throw Exception('找不到要刪除的系統公告: $id'),
      );

      // 從列表中移除
      final updatedAnnouncements = existingAnnouncements.where((a) => a.id != id).toList();

      // 更新 Remote Config
      final success = await _updateRemoteConfigAnnouncements(updatedAnnouncements, adminUid);

      if (success) {
        // 記錄管理者操作
        await AdminService.logAdminAction(
          adminUid: adminUid,
          action: 'delete_announcement_remote_config',
          description: '從 Remote Config 刪除系統公告: ${announcementToDelete.title}',
          metadata: {
            'announcementId': id,
            'type': announcementToDelete.type.name,
            'priority': announcementToDelete.priority.name,
          },
        );

        logger.i('系統公告從 Remote Config 刪除成功: $id');
        return true;
      } else {
        throw Exception('更新 Remote Config 失敗');
      }
    } catch (e) {
      logger.e('從 Remote Config 刪除系統公告失敗: $e');
      return false;
    }
  }

  /// 切換公告啟用狀態
  static Future<bool> toggleAnnouncementStatus(
    String id,
    bool isActive,
    String adminUid,
  ) async {
    try {
      logger.i('切換系統公告狀態: $id -> $isActive');

      await _firestore
          .collection(_collectionName)
          .doc(id)
          .update({
        'isActive': isActive,
        'updatedAt': FieldValue.serverTimestamp(),
        'updatedBy': adminUid,
      });

      // 記錄管理者操作
      await AdminService.logAdminAction(
        adminUid: adminUid,
        action: 'toggle_announcement_status',
        description: '${isActive ? '啟用' : '停用'}系統公告: $id',
        metadata: {
          'announcementId': id,
          'isActive': isActive,
        },
      );

      logger.i('系統公告狀態切換成功: $id');
      return true;
    } catch (e) {
      logger.e('切換系統公告狀態失敗: $e');
      return false;
    }
  }

  /// 獲取系統公告統計
  static Future<Map<String, int>> getAnnouncementStats() async {
    try {
      final querySnapshot = await _firestore
          .collection(_collectionName)
          .get();

      int totalCount = 0;
      int activeCount = 0;
      int stickyCount = 0;
      int urgentCount = 0;

      for (final doc in querySnapshot.docs) {
        try {
          final data = doc.data();
          totalCount++;

          if (data['isActive'] == true) {
            activeCount++;
          }

          if (data['isSticky'] == true) {
            stickyCount++;
          }

          if (data['priority'] == 'urgent') {
            urgentCount++;
          }
        } catch (e) {
          logger.w('解析公告統計失敗 (${doc.id}): $e');
        }
      }

      return {
        'total': totalCount,
        'active': activeCount,
        'sticky': stickyCount,
        'urgent': urgentCount,
      };
    } catch (e) {
      logger.e('獲取系統公告統計失敗: $e');
      return {
        'total': 0,
        'active': 0,
        'sticky': 0,
        'urgent': 0,
      };
    }
  }

  /// 批量更新公告狀態
  static Future<bool> batchUpdateAnnouncementStatus(
    List<String> ids,
    bool isActive,
    String adminUid,
  ) async {
    try {
      logger.i('批量更新系統公告狀態: ${ids.length} 個公告 -> $isActive');

      final batch = _firestore.batch();

      for (final id in ids) {
        final docRef = _firestore.collection(_collectionName).doc(id);
        batch.update(docRef, {
          'isActive': isActive,
          'updatedAt': FieldValue.serverTimestamp(),
          'updatedBy': adminUid,
        });
      }

      await batch.commit();

      // 記錄管理者操作
      await AdminService.logAdminAction(
        adminUid: adminUid,
        action: 'batch_update_announcement_status',
        description: '批量${isActive ? '啟用' : '停用'}系統公告: ${ids.length} 個',
        metadata: {
          'announcementIds': ids,
          'isActive': isActive,
          'count': ids.length,
        },
      );

      logger.i('批量更新系統公告狀態成功');
      return true;
    } catch (e) {
      logger.e('批量更新系統公告狀態失敗: $e');
      return false;
    }
  }

  /// 從 Remote Config 獲取系統公告列表（優先使用本地快取和 Firestore）
  static Future<List<SystemAnnouncement>> _getAnnouncementsFromRemoteConfig() async {
    try {
      // 1. 優先從本地快取獲取
      // final cachedAnnouncements = await _getAnnouncementsFromCache();
      // if (cachedAnnouncements.isNotEmpty) {
      //   logger.d('從本地快取獲取到 ${cachedAnnouncements.length} 個系統公告');
      //   return cachedAnnouncements;
      // }
      //
      // // 2. 從 Firestore 獲取
      // final firestoreAnnouncements = await _getAnnouncementsFromFirestoreConfig();
      // if (firestoreAnnouncements.isNotEmpty) {
      //   logger.d('從 Firestore 獲取到 ${firestoreAnnouncements.length} 個系統公告');
      //   // 更新本地快取
      //   final configData = {
      //     'announcements': firestoreAnnouncements.map((a) => a.toJson()).toList(),
      //     'lastUpdated': DateTime.now().toIso8601String(),
      //     'totalCount': firestoreAnnouncements.length,
      //   };
      //   await _updateLocalAnnouncementCache(json.encode(configData));
      //   return firestoreAnnouncements;
      // }

      // 3. 等待 Remote Config 初始化並獲取
      await _ensureRemoteConfigInitialized();

      final configValue = RemoteConfigService.getConfigValue('system_announcements');
      if (configValue.isNotEmpty) {
        final Map<String, dynamic> configData = json.decode(configValue);
        final List<dynamic> announcementsList = configData['announcements'] ?? [];

        final announcements = <SystemAnnouncement>[];
        for (final announcementData in announcementsList) {
          try {
            final announcement = SystemAnnouncement.fromJson(announcementData);
            announcements.add(announcement);
          } catch (e) {
            logger.w('解析系統公告失敗: $e');
          }
        }

        logger.d('從 Remote Config 獲取到 ${announcements.length} 個系統公告');

        // 更新本地快取
        if (announcements.isNotEmpty) {
          final configData = {
            'announcements': announcements.map((a) => a.toJson()).toList(),
            'lastUpdated': DateTime.now().toIso8601String(),
            'totalCount': announcements.length,
          };
          await _updateLocalAnnouncementCache(json.encode(configData));
        }

        return announcements;
      }

      logger.d('所有來源都沒有找到系統公告');
      return [];
    } catch (e) {
      logger.e('獲取系統公告失敗: $e');
      return [];
    }
  }

  /// 確保 Remote Config 已初始化
  static Future<void> _ensureRemoteConfigInitialized() async {
    try {
      // 檢查是否已初始化
      if (RemoteConfigService.isInitialized) {
        return;
      }

      logger.i('Remote Config 未初始化，嘗試重新初始化...');

      // 等待最多 5 秒讓 Remote Config 初始化
      for (int i = 0; i < 10; i++) {
        if (RemoteConfigService.isInitialized) {
          logger.i('Remote Config 已初始化');
          return;
        }

        await Future.delayed(const Duration(milliseconds: 500));
        logger.d('等待 Remote Config 初始化... (${i + 1}/10)');
      }

      // 如果還是沒有初始化，嘗試手動初始化
      if (!RemoteConfigService.isInitialized) {
        logger.w('Remote Config 初始化超時，嘗試手動初始化');
        await RemoteConfigService.initialize();
      }
    } catch (e) {
      logger.e('確保 Remote Config 初始化失敗: $e');
    }
  }



  /// 從本地快取獲取系統公告
  static Future<List<SystemAnnouncement>> _getAnnouncementsFromCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cachedJson = prefs.getString('cached_system_announcements');
      final cacheTimestamp = prefs.getInt('announcements_cache_timestamp') ?? 0;

      if (cachedJson == null || cachedJson.isEmpty) {
        return [];
      }

      // 檢查快取是否過期（1小時）
      final now = DateTime.now().millisecondsSinceEpoch;
      final cacheAge = now - cacheTimestamp;
      const maxCacheAge = 60 * 60 * 1000; // 1小時

      if (cacheAge > maxCacheAge) {
        logger.d('本地快取已過期，清除快取');
        await prefs.remove('cached_system_announcements');
        await prefs.remove('announcements_cache_timestamp');
        return [];
      }

      final Map<String, dynamic> configData = json.decode(cachedJson);
      final List<dynamic> announcementsList = configData['announcements'] ?? [];

      final announcements = <SystemAnnouncement>[];
      for (final announcementData in announcementsList) {
        try {
          final announcement = SystemAnnouncement.fromJson(announcementData);
          announcements.add(announcement);
        } catch (e) {
          logger.w('解析快取的系統公告失敗: $e');
        }
      }

      return announcements;
    } catch (e) {
      logger.e('從本地快取獲取系統公告失敗: $e');
      return [];
    }
  }

  /// 更新本地系統公告快取
  static Future<void> _updateLocalAnnouncementCache(String configJson) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('cached_system_announcements', configJson);
      await prefs.setInt('announcements_cache_timestamp', DateTime.now().millisecondsSinceEpoch);
      logger.d('本地系統公告快取已更新');
    } catch (e) {
      logger.e('更新本地系統公告快取失敗: $e');
    }
  }

  /// 從 Firestore 配置獲取系統公告
  static Future<List<SystemAnnouncement>> _getAnnouncementsFromFirestoreConfig() async {
    try {
      final doc = await _firestore
          .collection('system')
          .doc('announcements_remote_config')
          .get();

      if (!doc.exists) {
        return [];
      }

      final data = doc.data()!;
      final configJson = data['configData'] as String?;

      if (configJson == null || configJson.isEmpty) {
        return [];
      }

      final Map<String, dynamic> configData = json.decode(configJson);
      final List<dynamic> announcementsList = configData['announcements'] ?? [];

      final announcements = <SystemAnnouncement>[];
      for (final announcementData in announcementsList) {
        try {
          final announcement = SystemAnnouncement.fromJson(announcementData);
          announcements.add(announcement);
        } catch (e) {
          logger.w('解析 Firestore 配置的系統公告失敗: $e');
        }
      }

      return announcements;
    } catch (e) {
      logger.e('從 Firestore 配置獲取系統公告失敗: $e');
      return [];
    }
  }

  /// 更新 Remote Config 中的系統公告
  static Future<bool> _updateRemoteConfigAnnouncements(
    List<SystemAnnouncement> announcements,
    String adminUid,
  ) async {
    try {
      logger.i('更新系統公告到本地快取和 Firestore，共 ${announcements.length} 個');

      // 構建配置資料
      final configData = {
        'announcements': announcements.map((a) => a.toJson()).toList(),
        'lastUpdated': DateTime.now().toIso8601String(),
        'updatedBy': adminUid,
        'totalCount': announcements.length,
        'version': DateTime.now().millisecondsSinceEpoch,
      };

      final configJson = json.encode(configData);

      // 保存到 Firestore 作為主要儲存
      await _firestore
          .collection('system')
          .doc('announcements_remote_config')
          .set({
        'configData': configJson,
        'updatedAt': FieldValue.serverTimestamp(),
        'updatedBy': adminUid,
        'status': 'active',
        'totalCount': announcements.length,
      });

      // 同時保存到本地快取（模擬 Remote Config 更新）
      await _updateLocalAnnouncementCache(configJson);

      logger.i('系統公告已更新到 Firestore 和本地快取');
      return true;
    } catch (e) {
      logger.e('更新系統公告失敗: $e');
      return false;
    }
  }

  /// 同步系統公告到 Remote Config（管理員手動觸發）
  static Future<bool> syncAnnouncementsToRemoteConfig(String adminUid) async {
    try {
      logger.i('開始同步系統公告到 Remote Config...');

      // 獲取所有公告（從 Firestore 或當前來源）
      final announcements = await _getAnnouncementsFromRemoteConfig();

      if (announcements.isEmpty) {
        logger.w('沒有找到要同步的系統公告');
        return false;
      }

      // 更新 Remote Config
      final success = await _updateRemoteConfigAnnouncements(announcements, adminUid);

      if (success) {
        // 記錄管理者操作
        await AdminService.logAdminAction(
          adminUid: adminUid,
          action: 'sync_announcements_to_remote_config',
          description: '同步系統公告到 Remote Config',
          metadata: {
            'totalCount': announcements.length,
            'syncTime': DateTime.now().toIso8601String(),
          },
        );

        logger.i('系統公告同步到 Remote Config 成功');
        return true;
      } else {
        throw Exception('同步失敗');
      }
    } catch (e) {
      logger.e('同步系統公告到 Remote Config 失敗: $e');
      return false;
    }
  }
}
