import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// 解讀指引配置服務
class InterpretationGuidanceService {
  static const String _guidanceKey = 'interpretation_guidance';
  static const String _customGuidanceKey = 'custom_interpretation_guidance';
  static const String _useCustomGuidanceKey = 'use_custom_interpretation_guidance';
  
  /// 預設解讀指引（專業模式）
  static const String defaultGuidance = '''請根據上述星盤資訊進行專業占星解讀，並遵守以下指引：
- 回答請使用繁體中文
- 結合行星、星座與宮位的整體影響進行解釋
- 禁用以下內容：
開場白（如「你好，讓我們來看看你的星盤」）
表情符號（如🌞、🌙 等）''';

  /// 初心者模式解讀指引
  static const String beginnerGuidance = '''請根據上述星盤資訊進行友善易懂的解讀，並遵守以下指引：
- 回答請使用繁體中文
- 避免使用專業術語，如「宮位」、「行星」、「星座」、「相位」等
- 禁用以下內容：
開場白（如「你好，讓我們來看看你的星盤」）
表情符號（如🌞、🌙 等）
複雜的占星術語和理論解釋''';

  /// 獲取解讀指引
  static Future<String> getGuidance() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final useCustom = prefs.getBool(_useCustomGuidanceKey) ?? false;

      if (useCustom) {
        // 使用自定義指引
        final customGuidance = prefs.getString(_customGuidanceKey);
        if (customGuidance != null && customGuidance.isNotEmpty) {
          return customGuidance;
        }
      }

      // 檢查用戶模式
      final userMode = prefs.getString('user_mode') ?? 'starmaster';

      // 嘗試從 Remote Config 獲取
      try {
        final remoteConfig = FirebaseRemoteConfig.instance;
        await remoteConfig.fetchAndActivate();

        // 根據用戶模式獲取不同的指引
        String remoteGuidanceKey = _guidanceKey;
        if (userMode == 'starlight') {
          remoteGuidanceKey = 'interpretation_guidance_beginner';
        }

        final remoteGuidance = remoteConfig.getString(remoteGuidanceKey);

        if (remoteGuidance.isNotEmpty) {
          return remoteGuidance;
        }
      } catch (e) {
        // Remote Config 失敗時使用預設值
        debugPrint('Failed to fetch guidance from Remote Config: $e');
      }

      // 根據用戶模式返回對應的預設指引
      if (userMode == 'starlight') {
        return beginnerGuidance;
      } else {
        return defaultGuidance;
      }
    } catch (e) {
      debugPrint('Error getting interpretation guidance: $e');
      // 發生錯誤時，嘗試獲取用戶模式並返回對應指引
      try {
        final prefs = await SharedPreferences.getInstance();
        final userMode = prefs.getString('user_mode') ?? 'starmaster';
        return userMode == 'starlight' ? beginnerGuidance : defaultGuidance;
      } catch (e2) {
        return defaultGuidance;
      }
    }
  }

  /// 獲取 Remote Config 指引
  static Future<String> getRemoteGuidance({bool isBeginner = false}) async {
    try {
      final remoteConfig = FirebaseRemoteConfig.instance;
      await remoteConfig.fetchAndActivate();

      String guidanceKey = isBeginner ? 'interpretation_guidance_beginner' : _guidanceKey;
      final remoteGuidance = remoteConfig.getString(guidanceKey);

      if (remoteGuidance.isNotEmpty) {
        return remoteGuidance;
      }
      return isBeginner ? beginnerGuidance : defaultGuidance;
    } catch (e) {
      debugPrint('Error getting remote guidance: $e');
      return isBeginner ? beginnerGuidance : defaultGuidance;
    }
  }

  /// 獲取初心者模式指引
  static Future<String> getBeginnerGuidance() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final useCustom = prefs.getBool(_useCustomGuidanceKey) ?? false;

      if (useCustom) {
        // 使用自定義指引
        final customGuidance = prefs.getString(_customGuidanceKey);
        if (customGuidance != null && customGuidance.isNotEmpty) {
          return customGuidance;
        }
      }

      // 嘗試從 Remote Config 獲取初心者指引
      return await getRemoteGuidance(isBeginner: true);
    } catch (e) {
      debugPrint('Error getting beginner guidance: $e');
      return beginnerGuidance;
    }
  }

  /// 獲取專業模式指引
  static Future<String> getProfessionalGuidance() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final useCustom = prefs.getBool(_useCustomGuidanceKey) ?? false;

      if (useCustom) {
        // 使用自定義指引
        final customGuidance = prefs.getString(_customGuidanceKey);
        if (customGuidance != null && customGuidance.isNotEmpty) {
          return customGuidance;
        }
      }

      // 嘗試從 Remote Config 獲取專業指引
      return await getRemoteGuidance(isBeginner: false);
    } catch (e) {
      debugPrint('Error getting professional guidance: $e');
      return defaultGuidance;
    }
  }

  /// 獲取自定義指引
  static Future<String> getCustomGuidance() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(_customGuidanceKey) ?? '';
    } catch (e) {
      debugPrint('Error getting custom guidance: $e');
      return '';
    }
  }

  /// 保存自定義指引
  static Future<bool> saveCustomGuidance(String guidance) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return await prefs.setString(_customGuidanceKey, guidance);
    } catch (e) {
      debugPrint('Error saving custom guidance: $e');
      return false;
    }
  }

  /// 獲取是否使用自定義指引
  static Future<bool> getUseCustomGuidance() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_useCustomGuidanceKey) ?? false;
    } catch (e) {
      debugPrint('Error getting use custom guidance setting: $e');
      return false;
    }
  }

  /// 設置是否使用自定義指引
  static Future<bool> setUseCustomGuidance(bool useCustom) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return await prefs.setBool(_useCustomGuidanceKey, useCustom);
    } catch (e) {
      debugPrint('Error setting use custom guidance: $e');
      return false;
    }
  }

  /// 重置為預設指引
  static Future<bool> resetToDefault() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_customGuidanceKey);
      await prefs.setBool(_useCustomGuidanceKey, false);
      return true;
    } catch (e) {
      debugPrint('Error resetting guidance: $e');
      return false;
    }
  }

  /// 從 Remote Config 同步指引到本地
  static Future<bool> syncFromRemoteConfig() async {
    try {
      // 強制從 Remote Config 獲取最新指引
      await getRemoteGuidance();

      // 同步操作主要是觸發 Remote Config 的 fetch，實際使用時會自動獲取最新值
      return true;
    } catch (e) {
      debugPrint('Error syncing guidance from Remote Config: $e');
      return false;
    }
  }

  /// 獲取指引來源描述
  static Future<String> getGuidanceSource() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final useCustom = prefs.getBool(_useCustomGuidanceKey) ?? false;
      final userMode = prefs.getString('user_mode') ?? 'starmaster';

      if (useCustom) {
        final customGuidance = prefs.getString(_customGuidanceKey);
        if (customGuidance != null && customGuidance.isNotEmpty) {
          return '自定義指引';
        }
      }

      try {
        final remoteConfig = FirebaseRemoteConfig.instance;
        String guidanceKey = userMode == 'starlight' ? 'interpretation_guidance_beginner' : _guidanceKey;
        final remoteGuidance = remoteConfig.getString(guidanceKey);
        if (remoteGuidance.isNotEmpty) {
          return userMode == 'starlight' ? 'Remote Config (初心者)' : 'Remote Config (專業)';
        }
      } catch (e) {
        // Ignore Remote Config errors
      }

      return userMode == 'starlight' ? '預設指引 (初心者)' : '預設指引 (專業)';
    } catch (e) {
      return '預設指引';
    }
  }

  /// 驗證指引格式
  static bool validateGuidance(String guidance) {
    if (guidance.trim().isEmpty) {
      return false;
    }
    
    // 檢查是否包含基本的指引結構
    final lines = guidance.split('\n');
    return lines.length >= 3; // 至少要有3行內容
  }

  /// 獲取指引預覽（前100個字符）
  static String getGuidancePreview(String guidance) {
    if (guidance.length <= 100) {
      return guidance;
    }
    return '${guidance.substring(0, 100)}...';
  }

  /// 獲取當前用戶模式的指引預覽
  static Future<String> getCurrentGuidancePreview() async {
    try {
      final guidance = await getGuidance();
      return getGuidancePreview(guidance);
    } catch (e) {
      debugPrint('Error getting current guidance preview: $e');
      return getGuidancePreview(defaultGuidance);
    }
  }

  /// 檢查是否為初心者模式
  static Future<bool> isBeginnerMode() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userMode = prefs.getString('user_mode') ?? 'starmaster';
      return userMode == 'starlight';
    } catch (e) {
      debugPrint('Error checking user mode: $e');
      return false;
    }
  }

  /// 獲取模式友善名稱
  static Future<String> getModeFriendlyName() async {
    try {
      final isBeginnerModeActive = await isBeginnerMode();
      return isBeginnerModeActive ? 'Starlight (初心者模式)' : 'Starmaster (專業模式)';
    } catch (e) {
      debugPrint('Error getting mode friendly name: $e');
      return 'Starmaster (專業模式)';
    }
  }
}
