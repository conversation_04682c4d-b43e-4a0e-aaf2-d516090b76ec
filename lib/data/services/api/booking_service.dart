import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../core/utils/logger_utils.dart';
import '../../models/payment/booking_model.dart';
import 'email_service.dart';
import 'firebase_service.dart';

/// 預約服務類，處理預約資料的儲存和讀取
class BookingService {
  static const String _bookingsKey = 'astreal_bookings';

  /// 獲取所有預約
  static Future<List<BookingModel>> getAllBookings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final bookingsJson = prefs.getStringList(_bookingsKey) ?? [];

      return bookingsJson
          .map((json) => BookingModel.fromJson(jsonDecode(json)))
          .toList();
    } catch (e) {
      logger.e('獲取預約列表時出錯: $e');
      return [];
    }
  }

  /// 儲存預約
  static Future<bool> saveBooking(BookingModel booking) async {
    try {
      // 儲存到本地 SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      final bookings = await getAllBookings();

      // 檢查是否已存在相同 ID 的預約
      final existingIndex = bookings.indexWhere((b) => b.id == booking.id);

      if (existingIndex >= 0) {
        // 更新現有預約
        bookings[existingIndex] = booking;
      } else {
        // 新增預約
        bookings.add(booking);
      }

      // 將預約列表轉換為 JSON 字串列表
      final bookingsJson = bookings
          .map((booking) => jsonEncode(booking.toJson()))
          .toList();

      // 儲存到 SharedPreferences
      await prefs.setStringList(_bookingsKey, bookingsJson);

      // 儲存到 Firebase
      bool firebaseSaveSuccess = false;
      try {
        firebaseSaveSuccess = await FirebaseService.saveBooking(booking);
        if (!firebaseSaveSuccess) {
          logger.w('儲存預約到 Firebase 失敗，但已儲存到本地');
        }
      } catch (e) {
        logger.e('儲存預約到 Firebase 時出錯: $e');
        // 如果 Firebase 儲存失敗，但本地儲存成功，我們仍然返回成功
      }

      // 發送電子郵件通知
      bool emailSendSuccess = false;
      try {
        emailSendSuccess = await EmailService.sendBookingNotification(booking);
        if (!emailSendSuccess) {
          logger.w('發送預約通知郵件失敗，但已儲存預約');
        }
      } catch (e) {
        logger.e('發送預約通知郵件時出錯: $e');
        // 如果郵件發送失敗，但儲存成功，我們仍然返回成功
      }

      return true;
    } catch (e) {
      logger.e('儲存預約時出錯: $e');
      return false;
    }
  }

  /// 刪除預約
  static Future<bool> deleteBooking(String id) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final bookings = await getAllBookings();

      // 移除指定 ID 的預約
      bookings.removeWhere((booking) => booking.id == id);

      // 將預約列表轉換為 JSON 字串列表
      final bookingsJson = bookings
          .map((booking) => jsonEncode(booking.toJson()))
          .toList();

      // 儲存到 SharedPreferences
      await prefs.setStringList(_bookingsKey, bookingsJson);
      return true;
    } catch (e) {
      logger.e('刪除預約時出錯: $e');
      return false;
    }
  }

  /// 確認預約
  static Future<bool> confirmBooking(String id) async {
    try {
      final bookings = await getAllBookings();
      final bookingIndex = bookings.indexWhere((b) => b.id == id);

      if (bookingIndex < 0) {
        return false;
      }

      // 更新預約狀態為已確認
      final updatedBooking = bookings[bookingIndex].copyWith(isConfirmed: true);
      bookings[bookingIndex] = updatedBooking;

      // 將預約列表轉換為 JSON 字串列表
      final prefs = await SharedPreferences.getInstance();
      final bookingsJson = bookings
          .map((booking) => jsonEncode(booking.toJson()))
          .toList();

      // 儲存到 SharedPreferences
      await prefs.setStringList(_bookingsKey, bookingsJson);
      return true;
    } catch (e) {
      logger.e('確認預約時出錯: $e');
      return false;
    }
  }

  /// 檢查指定日期和時間是否已有預約
  static Future<bool> isTimeSlotAvailable(DateTime date, TimeOfDay time) async {
    try {
      final bookings = await getAllBookings();

      // 檢查是否有相同日期和時間的預約
      return !bookings.any((booking) =>
        booking.bookingDate.year == date.year &&
        booking.bookingDate.month == date.month &&
        booking.bookingDate.day == date.day &&
        booking.bookingTime.hour == time.hour &&
        booking.bookingTime.minute == time.minute
      );
    } catch (e) {
      logger.e('檢查時段可用性時出錯: $e');
      return false;
    }
  }

  /// 獲取指定日期的所有預約時段
  static Future<List<TimeOfDay>> getBookedTimeSlots(DateTime date) async {
    try {
      final bookings = await getAllBookings();

      // 篩選出指定日期的預約
      final bookingsOnDate = bookings.where((booking) =>
        booking.bookingDate.year == date.year &&
        booking.bookingDate.month == date.month &&
        booking.bookingDate.day == date.day
      );

      // 返回已預約的時段
      return bookingsOnDate.map((booking) => booking.bookingTime).toList();
    } catch (e) {
      logger.e('獲取已預約時段時出錯: $e');
      return [];
    }
  }
}
