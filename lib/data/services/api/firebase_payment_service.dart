import 'dart:convert';
import 'dart:io';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

import '../../../core/config/firebase_config_windows.dart';
import '../../../core/constants/firebase_collections.dart';
import '../../../core/utils/logger_utils.dart';
import '../../models/payment/payment_record.dart';
import 'auth_service.dart';

/// Firebase 支付服務類，處理支付記錄的雲端同步
class FirebasePaymentService {
  
  /// 檢查是否應該使用 REST API
  static bool _shouldUseRestApi() {
    try {
      if (!kIsWeb && Platform.isWindows) {
        return true;
      }
    } catch (e) {
      logger.w('無法檢測平台，使用 REST API: $e');
      return true;
    }
    return false;
  }

  /// 獲取當前用戶 ID
  static String? _getCurrentUserId() {
    final user = AuthService.getCurrentUser();
    return user?.uid;
  }

  /// 保存支付記錄到 Firebase
  static Future<bool> savePaymentRecord(PaymentRecord payment) async {
    final userId = _getCurrentUserId();
    if (userId == null) {
      logger.w('用戶未登入，無法保存支付記錄到雲端');
      return false;
    }

    try {
      if (_shouldUseRestApi()) {
        return await _savePaymentRecordViaRestApi(userId, payment);
      } else {
        return await _savePaymentRecordViaSDK(userId, payment);
      }
    } catch (e) {
      logger.e('保存支付記錄到 Firebase 失敗: $e');
      return false;
    }
  }

  /// 通過 REST API 保存支付記錄
  static Future<bool> _savePaymentRecordViaRestApi(String userId, PaymentRecord payment) async {
    try {
      final url = '${FirebaseConfigWindows.firestoreBaseUrl}/${FirebaseCollections.userPayments}/$userId/${FirebaseCollections.payments}';
      
      final firestoreData = {
        'fields': _convertToFirestoreFields(payment.toJson()),
      };

      final response = await http.post(
        Uri.parse('$url?documentId=${payment.id}'),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode(firestoreData),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        logger.i('支付記錄已保存到 Firebase: ${payment.id}');
        return true;
      } else {
        logger.e('保存支付記錄失敗: ${response.statusCode} - ${response.body}');
        return false;
      }
    } catch (e) {
      logger.e('REST API 保存支付記錄失敗: $e');
      return false;
    }
  }

  /// 通過 Firebase SDK 保存支付記錄
  static Future<bool> _savePaymentRecordViaSDK(String userId, PaymentRecord payment) async {
    try {
      final firestore = FirebaseFirestore.instance;

      // 準備數據
      final data = payment.toJson();
      data['user_id'] = userId;
      data['created_at'] = FieldValue.serverTimestamp();
      data['updated_at'] = FieldValue.serverTimestamp();

      // 保存到主要的 payments 集合
      final docRef = await firestore.collection(FirebaseCollections.payments).add(data);

      // 同時保存到用戶專屬的支付記錄集合
      await firestore
          .collection(FirebaseCollections.userPayments)
          .doc(userId)
          .collection('payments')
          .doc(docRef.id)
          .set(data);

      logger.i('通過 SDK 成功保存支付記錄，用戶: $userId，文檔 ID: ${docRef.id}');
      return true;
    } catch (e) {
      logger.e('通過 SDK 保存支付記錄失敗: $e');
      return false;
    }
  }

  /// 從 Firebase 獲取用戶的支付記錄
  static Future<List<PaymentRecord>> getUserPaymentRecords() async {
    final userId = _getCurrentUserId();
    if (userId == null) {
      logger.w('用戶未登入，無法獲取雲端支付記錄');
      return [];
    }

    try {
      if (_shouldUseRestApi()) {
        return await _getUserPaymentRecordsViaRestApi(userId);
      } else {
        return await _getUserPaymentRecordsViaSDK(userId);
      }
    } catch (e) {
      logger.e('從 Firebase 獲取支付記錄失敗: $e');
      return [];
    }
  }

  /// 通過 REST API 獲取支付記錄
  static Future<List<PaymentRecord>> _getUserPaymentRecordsViaRestApi(String userId) async {
    try {
      final url = '${FirebaseConfigWindows.firestoreBaseUrl}/${FirebaseCollections.userPayments}/$userId/${FirebaseCollections.payments}';
      
      final response = await http.get(
        Uri.parse(url),
        headers: {
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final documents = data['documents'] as List<dynamic>? ?? [];
        
        final payments = <PaymentRecord>[];
        for (final doc in documents) {
          try {
            final fields = doc['fields'] as Map<String, dynamic>;
            final paymentData = _convertFromFirestoreFields(fields);
            payments.add(PaymentRecord.fromJson(paymentData));
          } catch (e) {
            logger.w('解析支付記錄失敗: $e');
          }
        }
        
        logger.i('從 Firebase 獲取到 ${payments.length} 條支付記錄');
        return payments;
      } else {
        logger.e('獲取支付記錄失敗: ${response.statusCode} - ${response.body}');
        return [];
      }
    } catch (e) {
      logger.e('REST API 獲取支付記錄失敗: $e');
      return [];
    }
  }

  /// 通過 Firebase SDK 獲取支付記錄
  static Future<List<PaymentRecord>> _getUserPaymentRecordsViaSDK(String userId) async {
    try {
      final firestore = FirebaseFirestore.instance;

      // 從用戶專屬的支付記錄集合獲取
      final querySnapshot = await firestore
          .collection(FirebaseCollections.userPayments)
          .doc(userId)
          .collection('payments')
          .orderBy('created_at', descending: true)
          .get();

      final records = <PaymentRecord>[];
      for (final doc in querySnapshot.docs) {
        try {
          final data = doc.data();
          data['id'] = doc.id; // 添加文檔 ID
          final record = PaymentRecord.fromJson(data);
          records.add(record);
        } catch (e) {
          logger.w('解析支付記錄失敗，文檔 ID: ${doc.id}，錯誤: $e');
        }
      }

      logger.i('通過 SDK 獲取用戶 $userId 的支付記錄，共 ${records.length} 筆');
      return records;
    } catch (e) {
      logger.e('通過 SDK 獲取支付記錄失敗: $e');
      return [];
    }
  }

  /// 同步本地支付記錄到 Firebase
  static Future<bool> syncLocalPaymentsToFirebase() async {
    final userId = _getCurrentUserId();
    if (userId == null) {
      logger.w('用戶未登入，無法同步支付記錄');
      return false;
    }

    try {
      // 獲取本地支付記錄
      final prefs = await SharedPreferences.getInstance();
      final paymentsJson = prefs.getStringList('astreal_payments') ?? [];
      
      if (paymentsJson.isEmpty) {
        logger.i('沒有本地支付記錄需要同步');
        return true;
      }

      final localPayments = paymentsJson
          .map((json) => PaymentRecord.fromJson(jsonDecode(json)))
          .toList();

      // 獲取雲端支付記錄
      final cloudPayments = await getUserPaymentRecords();
      final cloudPaymentIds = cloudPayments.map((p) => p.id).toSet();

      // 同步本地記錄到雲端
      int syncedCount = 0;
      for (final payment in localPayments) {
        if (!cloudPaymentIds.contains(payment.id)) {
          final success = await savePaymentRecord(payment);
          if (success) {
            syncedCount++;
          }
        }
      }

      logger.i('同步了 $syncedCount 條支付記錄到 Firebase');
      return true;
    } catch (e) {
      logger.e('同步支付記錄到 Firebase 失敗: $e');
      return false;
    }
  }

  /// 從 Firebase 同步支付記錄到本地
  static Future<bool> syncFirebasePaymentsToLocal() async {
    final userId = _getCurrentUserId();
    if (userId == null) {
      logger.w('用戶未登入，無法同步支付記錄');
      return false;
    }

    try {
      // 獲取雲端支付記錄
      final cloudPayments = await getUserPaymentRecords();
      
      if (cloudPayments.isEmpty) {
        logger.i('雲端沒有支付記錄需要同步');
        return true;
      }

      // 獲取本地支付記錄
      final prefs = await SharedPreferences.getInstance();
      final paymentsJson = prefs.getStringList('astreal_payments') ?? [];
      final localPayments = paymentsJson
          .map((json) => PaymentRecord.fromJson(jsonDecode(json)))
          .toList();

      final localPaymentIds = localPayments.map((p) => p.id).toSet();

      // 合併雲端記錄到本地
      final mergedPayments = List<PaymentRecord>.from(localPayments);
      int syncedCount = 0;
      
      for (final payment in cloudPayments) {
        if (!localPaymentIds.contains(payment.id)) {
          mergedPayments.add(payment);
          syncedCount++;
        }
      }

      // 保存合併後的記錄
      if (syncedCount > 0) {
        final mergedPaymentsJson = mergedPayments
            .map((payment) => jsonEncode(payment.toJson()))
            .toList();
        
        await prefs.setStringList('astreal_payments', mergedPaymentsJson);
        logger.i('從 Firebase 同步了 $syncedCount 條支付記錄到本地');
      }

      return true;
    } catch (e) {
      logger.e('從 Firebase 同步支付記錄失敗: $e');
      return false;
    }
  }

  /// 更新用戶訂閱狀態到 Firebase
  static Future<bool> updateUserSubscriptionStatus(Map<String, dynamic> subscriptionData) async {
    final userId = _getCurrentUserId();
    if (userId == null) {
      logger.w('用戶未登入，無法更新訂閱狀態');
      return false;
    }

    try {
      if (_shouldUseRestApi()) {
        return await _updateSubscriptionStatusViaRestApi(userId, subscriptionData);
      } else {
        return await _updateSubscriptionStatusViaSDK(userId, subscriptionData);
      }
    } catch (e) {
      logger.e('更新訂閱狀態到 Firebase 失敗: $e');
      return false;
    }
  }

  /// 通過 REST API 更新訂閱狀態
  static Future<bool> _updateSubscriptionStatusViaRestApi(String userId, Map<String, dynamic> data) async {
    try {
      final url = '${FirebaseConfigWindows.firestoreBaseUrl}/${FirebaseCollections.subscriptions}/$userId';
      
      final firestoreData = {
        'fields': _convertToFirestoreFields(data),
      };

      final response = await http.patch(
        Uri.parse(url),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode(firestoreData),
      );

      if (response.statusCode == 200) {
        logger.i('用戶訂閱狀態已更新到 Firebase');
        return true;
      } else {
        logger.e('更新訂閱狀態失敗: ${response.statusCode} - ${response.body}');
        return false;
      }
    } catch (e) {
      logger.e('REST API 更新訂閱狀態失敗: $e');
      return false;
    }
  }

  /// 通過 Firebase SDK 更新訂閱狀態
  static Future<bool> _updateSubscriptionStatusViaSDK(String userId, Map<String, dynamic> data) async {
    try {
      final firestore = FirebaseFirestore.instance;

      // 準備數據
      final updateData = Map<String, dynamic>.from(data);
      updateData['updated_at'] = FieldValue.serverTimestamp();

      // 更新用戶的訂閱狀態
      await firestore
          .collection(FirebaseCollections.subscriptions)
          .doc(userId)
          .set(updateData, SetOptions(merge: true));

      logger.i('通過 SDK 成功更新用戶 $userId 的訂閱狀態');
      return true;
    } catch (e) {
      logger.e('通過 SDK 更新訂閱狀態失敗: $e');
      return false;
    }
  }

  /// 轉換為 Firestore 字段格式
  static Map<String, dynamic> _convertToFirestoreFields(Map<String, dynamic> data) {
    final fields = <String, dynamic>{};
    
    for (final entry in data.entries) {
      final key = entry.key;
      final value = entry.value;
      
      if (value is String) {
        fields[key] = {'stringValue': value};
      } else if (value is int) {
        fields[key] = {'integerValue': value.toString()};
      } else if (value is double) {
        fields[key] = {'doubleValue': value};
      } else if (value is bool) {
        fields[key] = {'booleanValue': value};
      } else if (value is DateTime) {
        fields[key] = {'timestampValue': value.toIso8601String()};
      } else {
        fields[key] = {'stringValue': value.toString()};
      }
    }
    
    return fields;
  }

  /// 從 Firestore 字段格式轉換
  static Map<String, dynamic> _convertFromFirestoreFields(Map<String, dynamic> fields) {
    final data = <String, dynamic>{};
    
    for (final entry in fields.entries) {
      final key = entry.key;
      final fieldData = entry.value as Map<String, dynamic>;
      
      if (fieldData.containsKey('stringValue')) {
        data[key] = fieldData['stringValue'];
      } else if (fieldData.containsKey('integerValue')) {
        data[key] = int.parse(fieldData['integerValue']);
      } else if (fieldData.containsKey('doubleValue')) {
        data[key] = fieldData['doubleValue'];
      } else if (fieldData.containsKey('booleanValue')) {
        data[key] = fieldData['booleanValue'];
      } else if (fieldData.containsKey('timestampValue')) {
        data[key] = DateTime.parse(fieldData['timestampValue']);
      }
    }
    
    return data;
  }

  /// 記錄購買到 Firebase
  static Future<bool> recordPayment(dynamic paymentRecord) async {
    try {
      if (_shouldUseRestApi()) {
        return await _recordPaymentViaRestApi(paymentRecord);
      } else {
        return await _recordPaymentViaSDK(paymentRecord);
      }
    } catch (e) {
      logger.e('記錄購買失敗: $e');
      return false;
    }
  }

  /// 通過 REST API 記錄購買
  static Future<bool> _recordPaymentViaRestApi(dynamic paymentRecord) async {
    try {
      const projectId = FirebaseConfigWindows.projectId;
      final url = 'https://firestore.googleapis.com/v1/projects/$projectId/databases/(default)/documents/payment_records/${paymentRecord.id}';

      final data = {
        'fields': {
          'id': {'stringValue': paymentRecord.id},
          'planType': {'stringValue': paymentRecord.planType},
          'amount': {'doubleValue': paymentRecord.amount},
          'currency': {'stringValue': paymentRecord.currency},
          'paymentDate': {'timestampValue': paymentRecord.paymentDate.toUtc().toIso8601String()},
          'expiryDate': {'timestampValue': paymentRecord.expiryDate.toUtc().toIso8601String()},
          'isValid': {'booleanValue': paymentRecord.isValid},
          'paymentMethod': {'stringValue': paymentRecord.paymentMethod},
          'transactionId': {'stringValue': paymentRecord.transactionId},
          'createdAt': {'timestampValue': DateTime.now().toUtc().toIso8601String()},
        }
      };

      final response = await http.patch(
        Uri.parse(url),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(data),
      );

      if (response.statusCode == 200) {
        logger.i('成功記錄購買到 Firebase: ${paymentRecord.id}');
        return true;
      } else {
        logger.w('記錄購買失敗，狀態碼: ${response.statusCode}');
        return false;
      }
    } catch (e) {
      logger.e('通過 REST API 記錄購買失敗: $e');
      return false;
    }
  }

  /// 通過 SDK 記錄購買（非 Windows 平台）
  static Future<bool> _recordPaymentViaSDK(dynamic paymentRecord) async {
    try {
      final firestore = FirebaseFirestore.instance;

      // 將 paymentRecord 轉換為 Map
      Map<String, dynamic> data;
      if (paymentRecord is PaymentRecord) {
        data = paymentRecord.toJson();
      } else if (paymentRecord is Map<String, dynamic>) {
        data = paymentRecord;
      } else {
        logger.e('不支援的 paymentRecord 類型: ${paymentRecord.runtimeType}');
        return false;
      }

      // 添加伺服器時間戳
      data['created_at'] = FieldValue.serverTimestamp();
      data['updated_at'] = FieldValue.serverTimestamp();

      // 記錄到 payments 集合
      final docRef = await firestore.collection(FirebaseCollections.payments).add(data);

      // 同時記錄到用戶專屬的支付記錄集合
      final userId = data['user_id'] as String?;
      if (userId != null && userId.isNotEmpty) {
        await firestore
            .collection(FirebaseCollections.userPayments)
            .doc(userId)
            .collection('payments')
            .doc(docRef.id)
            .set(data);
      }

      logger.i('通過 SDK 成功記錄購買，文檔 ID: ${docRef.id}');
      return true;
    } catch (e) {
      logger.e('通過 SDK 記錄購買失敗: $e');
      return false;
    }
  }

  /// 獲取剩餘解讀次數（從 user_profiles 集合）
  static Future<int> getRemainingSinglePurchases() async {
    final userId = _getCurrentUserId();
    if (userId == null) {
      logger.w('用戶未登入，無法從 Firebase 獲取解讀次數');
      return -1; // 返回 -1 表示獲取失敗
    }

    try {
      if (_shouldUseRestApi()) {
        return await _getInterpretationCreditsViaRestApi(userId);
      } else {
        return await _getInterpretationCreditsViaSDK(userId);
      }
    } catch (e) {
      logger.e('從 Firebase 獲取解讀次數失敗: $e');
      return -1;
    }
  }

  /// 通過 REST API 獲取解讀次數（從 user_profiles）
  static Future<int> _getInterpretationCreditsViaRestApi(String userId) async {
    try {
      final url = Uri.parse(
        '${FirebaseConfigWindows.firestoreBaseUrl}/projects/${FirebaseConfigWindows.projectId}/databases/(default)/documents/${FirebaseCollections.userProfiles}/$userId'
      );

      final response = await http.get(url);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final interpretationCredits = data['fields']?['interpretation_credits']?['integerValue'];
        final count = interpretationCredits != null ? int.parse(interpretationCredits.toString()) : 0;
        logger.i('通過 REST API 獲取解讀次數: $count');
        return count;
      } else if (response.statusCode == 404) {
        // 文檔不存在，返回 0
        logger.i('用戶檔案不存在，返回 0');
        return 0;
      } else {
        logger.e('REST API 獲取解讀次數失敗，狀態碼: ${response.statusCode}');
        return -1;
      }
    } catch (e) {
      logger.e('通過 REST API 獲取解讀次數時出錯: $e');
      return -1;
    }
  }

  /// 通過 SDK 獲取解讀次數（從 user_profiles）
  static Future<int> _getInterpretationCreditsViaSDK(String userId) async {
    try {
      final doc = await FirebaseFirestore.instance
          .collection(FirebaseCollections.userProfiles)
          .doc(userId)
          .get();

      if (doc.exists) {
        final data = doc.data() as Map<String, dynamic>;
        final count = data['interpretation_credits'] as int? ?? 0;
        logger.i('通過 SDK 獲取解讀次數: $count');
        return count;
      } else {
        logger.i('用戶檔案不存在，返回 0');
        return 0;
      }
    } catch (e) {
      logger.e('通過 SDK 獲取解讀次數時出錯: $e');
      return -1;
    }
  }

  /// 添加解讀次數（更新到 user_profiles）
  static Future<bool> addSinglePurchase(int count) async {
    final userId = _getCurrentUserId();
    if (userId == null) {
      logger.w('用戶未登入，無法添加解讀次數到 Firebase');
      return false;
    }

    try {
      if (_shouldUseRestApi()) {
        return await _addInterpretationCreditsViaRestApi(userId, count);
      } else {
        return await _addInterpretationCreditsViaSDK(userId, count);
      }
    } catch (e) {
      logger.e('添加解讀次數到 Firebase 失敗: $e');
      return false;
    }
  }

  /// 通過 REST API 添加解讀次數（更新到 user_profiles）
  static Future<bool> _addInterpretationCreditsViaRestApi(String userId, int count) async {
    try {
      // 先獲取當前次數
      final currentCount = await _getInterpretationCreditsViaRestApi(userId);
      final newCount = (currentCount >= 0 ? currentCount : 0) + count;

      final url = Uri.parse(
        '${FirebaseConfigWindows.firestoreBaseUrl}/projects/${FirebaseConfigWindows.projectId}/databases/(default)/documents/${FirebaseCollections.userProfiles}/$userId'
      );

      final data = {
        'fields': {
          'interpretation_credits': {'integerValue': newCount.toString()},
          'credits_last_updated': {'timestampValue': DateTime.now().toUtc().toIso8601String()},
          'updated_at': {'timestampValue': DateTime.now().toUtc().toIso8601String()},
        }
      };

      final response = await http.patch(
        url,
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(data),
      );

      if (response.statusCode == 200) {
        logger.i('通過 REST API 添加解讀次數成功: +$count，總計: $newCount');
        return true;
      } else {
        logger.e('REST API 添加解讀次數失敗，狀態碼: ${response.statusCode}');
        return false;
      }
    } catch (e) {
      logger.e('通過 REST API 添加解讀次數時出錯: $e');
      return false;
    }
  }

  /// 通過 SDK 添加解讀次數（更新到 user_profiles）
  static Future<bool> _addInterpretationCreditsViaSDK(String userId, int count) async {
    try {
      final docRef = FirebaseFirestore.instance
          .collection(FirebaseCollections.userProfiles)
          .doc(userId);

      await docRef.set({
        'interpretation_credits': FieldValue.increment(count),
        'credits_last_updated': FieldValue.serverTimestamp(),
        'updated_at': FieldValue.serverTimestamp(),
      }, SetOptions(merge: true));

      logger.i('通過 SDK 添加解讀次數成功: +$count');
      return true;
    } catch (e) {
      logger.e('通過 SDK 添加解讀次數時出錯: $e');
      return false;
    }
  }

  /// 使用一次解讀次數（從 user_profiles 扣除）
  static Future<bool> useSinglePurchaseAttempt() async {
    final userId = _getCurrentUserId();
    if (userId == null) {
      logger.w('用戶未登入，無法使用解讀次數');
      return false;
    }

    try {
      if (_shouldUseRestApi()) {
        return await _useInterpretationCreditViaRestApi(userId);
      } else {
        return await _useInterpretationCreditViaSDK(userId);
      }
    } catch (e) {
      logger.e('使用解讀次數失敗: $e');
      return false;
    }
  }

  /// 通過 REST API 使用一次解讀次數（從 user_profiles 扣除）
  static Future<bool> _useInterpretationCreditViaRestApi(String userId) async {
    try {
      // 先獲取當前次數
      final currentCount = await _getInterpretationCreditsViaRestApi(userId);
      if (currentCount <= 0) {
        logger.w('沒有剩餘的解讀次數');
        return false;
      }

      final newCount = currentCount - 1;

      final url = Uri.parse(
        '${FirebaseConfigWindows.firestoreBaseUrl}/projects/${FirebaseConfigWindows.projectId}/databases/(default)/documents/${FirebaseCollections.userProfiles}/$userId'
      );

      final data = {
        'fields': {
          'interpretation_credits': {'integerValue': newCount.toString()},
          'credits_last_updated': {'timestampValue': DateTime.now().toUtc().toIso8601String()},
          'updated_at': {'timestampValue': DateTime.now().toUtc().toIso8601String()},
        }
      };

      final response = await http.patch(
        url,
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(data),
      );

      if (response.statusCode == 200) {
        logger.i('通過 REST API 使用解讀次數成功，剩餘: $newCount');
        return true;
      } else {
        logger.e('REST API 使用解讀次數失敗，狀態碼: ${response.statusCode}');
        return false;
      }
    } catch (e) {
      logger.e('通過 REST API 使用解讀次數時出錯: $e');
      return false;
    }
  }

  /// 通過 SDK 使用一次解讀次數（從 user_profiles 扣除）
  static Future<bool> _useInterpretationCreditViaSDK(String userId) async {
    try {
      final docRef = FirebaseFirestore.instance
          .collection(FirebaseCollections.userProfiles)
          .doc(userId);

      // 使用事務確保原子性
      return await FirebaseFirestore.instance.runTransaction((transaction) async {
        final doc = await transaction.get(docRef);

        if (!doc.exists) {
          logger.w('用戶檔案不存在');
          return false;
        }

        final data = doc.data() as Map<String, dynamic>;
        final currentCount = data['interpretation_credits'] as int? ?? 0;

        if (currentCount <= 0) {
          logger.w('沒有剩餘的解讀次數');
          return false;
        }

        final newCount = currentCount - 1;
        transaction.update(docRef, {
          'interpretation_credits': newCount,
          'credits_last_updated': FieldValue.serverTimestamp(),
          'updated_at': FieldValue.serverTimestamp(),
        });

        logger.i('通過 SDK 使用解讀次數成功，剩餘: $newCount');
        return true;
      });
    } catch (e) {
      logger.e('通過 SDK 使用解讀次數時出錯: $e');
      return false;
    }
  }

}
