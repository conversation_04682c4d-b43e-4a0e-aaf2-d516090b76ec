import '../../../core/utils/logger_utils.dart';
import 'remote_config_service.dart';

/// AI 服務示例，展示如何使用 Remote Config 獲取 API Keys
class AIServiceExample {
  
  /// 使用 OpenAI API
  static Future<String> callOpenAI(String prompt) async {
    try {
      final apiKey = RemoteConfigService.getOpenAIKey();
      
      if (apiKey.isEmpty) {
        throw Exception('OpenAI API Key 未設定');
      }
      
      logger.i('使用 OpenAI API Key (長度: ${apiKey.length})');
      
      // 這裡是實際的 OpenAI API 調用
      // 示例代碼，實際使用時需要替換為真實的 HTTP 請求
      /*
      final response = await http.post(
        Uri.parse('https://api.openai.com/v1/chat/completions'),
        headers: {
          'Authorization': 'Bearer $apiKey',
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'model': 'gpt-3.5-turbo',
          'messages': [
            {'role': 'user', 'content': prompt}
          ],
        }),
      );
      
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return data['choices'][0]['message']['content'];
      } else {
        throw Exception('OpenAI API 調用失敗: ${response.statusCode}');
      }
      */
      
      // 模擬回應
      return 'OpenAI 回應: $prompt (使用 API Key: ${apiKey.substring(0, 10)}...)';
      
    } catch (e) {
      logger.e('OpenAI API 調用失敗: $e');
      rethrow;
    }
  }
  
  /// 使用 Groq AI API
  static Future<String> callGroqAI(String prompt) async {
    try {
      final apiKey = RemoteConfigService.getGroqAIKey();
      
      if (apiKey.isEmpty) {
        throw Exception('Groq AI API Key 未設定');
      }
      
      logger.i('使用 Groq AI API Key (長度: ${apiKey.length})');
      
      // 這裡是實際的 Groq AI API 調用
      // 示例代碼，實際使用時需要替換為真實的 HTTP 請求
      /*
      final response = await http.post(
        Uri.parse('https://api.groq.com/openai/v1/chat/completions'),
        headers: {
          'Authorization': 'Bearer $apiKey',
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'model': 'mixtral-8x7b-32768',
          'messages': [
            {'role': 'user', 'content': prompt}
          ],
        }),
      );
      
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return data['choices'][0]['message']['content'];
      } else {
        throw Exception('Groq AI API 調用失敗: ${response.statusCode}');
      }
      */
      
      // 模擬回應
      return 'Groq AI 回應: $prompt (使用 API Key: ${apiKey.substring(0, 10)}...)';
      
    } catch (e) {
      logger.e('Groq AI API 調用失敗: $e');
      rethrow;
    }
  }
  
  /// 使用 Google Gemini API
  static Future<String> callGoogleGemini(String prompt) async {
    try {
      final apiKey = RemoteConfigService.getGoogleGeminiKey();
      
      if (apiKey.isEmpty) {
        throw Exception('Google Gemini API Key 未設定');
      }
      
      logger.i('使用 Google Gemini API Key (長度: ${apiKey.length})');
      
      // 這裡是實際的 Google Gemini API 調用
      // 示例代碼，實際使用時需要替換為真實的 HTTP 請求
      /*
      final response = await http.post(
        Uri.parse('https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=$apiKey'),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'contents': [
            {
              'parts': [
                {'text': prompt}
              ]
            }
          ]
        }),
      );
      
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return data['candidates'][0]['content']['parts'][0]['text'];
      } else {
        throw Exception('Google Gemini API 調用失敗: ${response.statusCode}');
      }
      */
      
      // 模擬回應
      return 'Google Gemini 回應: $prompt (使用 API Key: ${apiKey.substring(0, 10)}...)';
      
    } catch (e) {
      logger.e('Google Gemini API 調用失敗: $e');
      rethrow;
    }
  }
  
  /// 智能選擇 AI 服務
  static Future<String> callBestAvailableAI(String prompt) async {
    final allKeys = RemoteConfigService.getAllApiKeys();
    
    // 按優先級嘗試不同的 AI 服務
    if (allKeys['openai']?.isNotEmpty == true) {
      try {
        return await callOpenAI(prompt);
      } catch (e) {
        logger.w('OpenAI 調用失敗，嘗試其他服務: $e');
      }
    }
    
    if (allKeys['groq']?.isNotEmpty == true) {
      try {
        return await callGroqAI(prompt);
      } catch (e) {
        logger.w('Groq AI 調用失敗，嘗試其他服務: $e');
      }
    }
    
    if (allKeys['gemini']?.isNotEmpty == true) {
      try {
        return await callGoogleGemini(prompt);
      } catch (e) {
        logger.w('Google Gemini 調用失敗: $e');
      }
    }
    
    throw Exception('沒有可用的 AI 服務 API Key');
  }
  
  /// 檢查 API Keys 可用性
  static Map<String, bool> checkAPIKeysAvailability() {
    final allKeys = RemoteConfigService.getAllApiKeys();
    
    return {
      'openai': allKeys['openai']?.isNotEmpty == true,
      'groq': allKeys['groq']?.isNotEmpty == true,
      'gemini': allKeys['gemini']?.isNotEmpty == true,
    };
  }
  
  /// 獲取推薦的 AI 服務
  static String getRecommendedAIService() {
    final availability = checkAPIKeysAvailability();
    
    if (availability['openai'] == true) {
      return 'OpenAI';
    } else if (availability['groq'] == true) {
      return 'Groq AI';
    } else if (availability['gemini'] == true) {
      return 'Google Gemini';
    } else {
      return '無可用服務';
    }
  }
  
  /// 測試所有 AI 服務
  static Future<Map<String, dynamic>> testAllAIServices() async {
    final results = <String, dynamic>{};
    const testPrompt = '你好，這是一個測試訊息';
    
    // 測試 OpenAI
    try {
      final openAIResult = await callOpenAI(testPrompt);
      results['openai'] = {
        'success': true,
        'response': openAIResult,
      };
    } catch (e) {
      results['openai'] = {
        'success': false,
        'error': e.toString(),
      };
    }
    
    // 測試 Groq AI
    try {
      final groqResult = await callGroqAI(testPrompt);
      results['groq'] = {
        'success': true,
        'response': groqResult,
      };
    } catch (e) {
      results['groq'] = {
        'success': false,
        'error': e.toString(),
      };
    }
    
    // 測試 Google Gemini
    try {
      final geminiResult = await callGoogleGemini(testPrompt);
      results['gemini'] = {
        'success': true,
        'response': geminiResult,
      };
    } catch (e) {
      results['gemini'] = {
        'success': false,
        'error': e.toString(),
      };
    }
    
    return results;
  }
}
