import 'package:sweph/sweph.dart';

import '../../../core/constants/astrology_constants.dart';
import '../../../core/utils/logger_utils.dart';
import '../../models/astrology/profection_data.dart';
import '../../models/user/birth_data.dart';

/// 小限法計算服務
/// 
/// 小限法（Profection）是古典占星中的年度預測技術
/// 核心原理：每過一歲，上升點會順行一宮位
/// 一週期為12年，形成一個輪迴
class ProfectionService {
  /// 宮位主題描述
  static const Map<int, String> _houseThemes = {
    1: '自我、身體、外貌、個性',
    2: '金錢、價值觀、物質資源',
    3: '溝通、學習、兄弟姊妹、短途旅行',
    4: '家庭、根基、房地產、內在安全感',
    5: '創造、娛樂、戀愛、子女',
    6: '工作、健康、日常習慣、服務',
    7: '伴侶、合作、公開敵人、一對一關係',
    8: '轉化、共同資源、死亡重生、神秘學',
    9: '哲學、高等教育、長途旅行、宗教',
    10: '事業、聲譽、社會地位、公眾形象',
    11: '朋友、團體、希望、社會理想',
    12: '潛意識、犧牲、隱藏敵人、靈性修行',
  };

  /// 宮位關鍵字
  static const Map<int, List<String>> _houseKeywords = {
    1: ['自我認同', '新開始', '個人形象', '身體健康'],
    2: ['財務管理', '價值重估', '物質安全', '自我價值'],
    3: ['學習成長', '溝通表達', '人際網絡', '資訊交流'],
    4: ['家庭關係', '居住環境', '情感根基', '內在安全'],
    5: ['創意表達', '戀愛關係', '娛樂休閒', '子女教育'],
    6: ['工作效率', '健康管理', '日常規律', '服務他人'],
    7: ['伴侶關係', '合作夥伴', '公開競爭', '平衡協調'],
    8: ['深度轉化', '共同財務', '心理探索', '危機處理'],
    9: ['視野拓展', '高等學習', '文化交流', '精神追求'],
    10: ['事業發展', '社會地位', '公眾認知', '權威建立'],
    11: ['友誼網絡', '團體活動', '未來規劃', '社會參與'],
    12: ['內在探索', '靈性成長', '隱藏議題', '犧牲奉獻'],
  };

  /// 計算小限法數據
  ///
  /// [birthData] 出生數據
  /// [currentDate] 當前日期（用於判斷當前年齡和小限年）
  /// [housesData] 本命盤宮位數據（用於獲取正確的宮位星座）
  ///
  /// 返回小限法數據
  Future<ProfectionData> calculateProfection(
    BirthData birthData, {
    DateTime? currentDate,
    HouseCuspData? housesData,
  }) async {
    logger.i('開始計算小限法數據');
    
    // 如果沒有指定當前日期，則使用現在的日期
    currentDate ??= DateTime.now();
    
    // 計算當前年齡
    final currentAge = _calculateAge(birthData.dateTime, currentDate);
    logger.d('當前年齡: $currentAge');
    
    // 計算小限宮位（年齡 mod 12 + 1）
    final profectionHouse = (currentAge % 12) + 1;
    logger.d('小限宮位: 第${profectionHouse}宮');
    
    // 獲取小限宮位對應的星座（根據本命盤宮位數據計算）
    final profectionSign = _getProfectionSign(profectionHouse, housesData);

    // 獲取小限宮位的主星（Time Lord）
    final timeLordInfo = _getTimeLordPlanet(profectionSign);
    
    // 計算小限年的開始和結束日期
    final profectionYearStart = DateTime(
      birthData.dateTime.year + currentAge,
      birthData.dateTime.month,
      birthData.dateTime.day,
      birthData.dateTime.hour,
      birthData.dateTime.minute,
    );
    
    final profectionYearEnd = DateTime(
      birthData.dateTime.year + currentAge + 1,
      birthData.dateTime.month,
      birthData.dateTime.day,
      birthData.dateTime.hour,
      birthData.dateTime.minute,
    ).subtract(const Duration(days: 1));
    
    // 判斷是否為當前小限年
    final isCurrent = (currentDate.isAfter(profectionYearStart) ||
                      currentDate.isAtSameMomentAs(profectionYearStart)) &&
                     currentDate.isBefore(profectionYearEnd.add(const Duration(days: 1)));
    
    // 獲取宮位主題和關鍵字
    final houseTheme = _houseThemes[profectionHouse] ?? '未知主題';
    final keywords = _houseKeywords[profectionHouse] ?? [];
    
    // 生成描述
    final description = _generateProfectionDescription(
      currentAge, 
      profectionHouse, 
      profectionSign, 
      timeLordInfo['name']!
    );
    
    final profectionData = ProfectionData(
      currentAge: currentAge,
      profectionHouse: profectionHouse,
      profectionSign: profectionSign,
      timeLordPlanetId: timeLordInfo['id']!,
      timeLordPlanetName: timeLordInfo['name']!,
      timeLordPlanetSymbol: timeLordInfo['symbol']!,
      timeLordPlanetColor: timeLordInfo['color']!,
      houseTheme: houseTheme,
      planetsInHouse: [], // 這裡需要根據實際星盤數據填入
      profectionYearStart: profectionYearStart,
      profectionYearEnd: profectionYearEnd,
      isCurrent: isCurrent,
      description: description,
      keywords: keywords,
    );
    
    logger.i('小限法數據計算完成: $profectionData');
    return profectionData;
  }

  /// 計算小限法完整時間軸
  ///
  /// [birthData] 出生數據
  /// [currentDate] 當前日期
  /// [yearsRange] 計算年份範圍（預設前後各5年）
  /// [housesData] 本命盤宮位數據（用於獲取正確的宮位星座）
  ///
  /// 返回小限法時間軸結果
  Future<ProfectionTimelineResult> calculateProfectionTimeline(
    BirthData birthData, {
    DateTime? currentDate,
    int yearsRange = 5,
    HouseCuspData? housesData,
  }) async {
    logger.i('開始計算小限法時間軸');

    currentDate ??= DateTime.now();
    final currentAge = _calculateAge(birthData.dateTime, currentDate);

    final timeline = <ProfectionData>[];
    ProfectionData? currentProfection;

    // 計算過去、現在、未來的小限法數據
    for (int ageOffset = -yearsRange; ageOffset <= yearsRange; ageOffset++) {
      final targetAge = currentAge + ageOffset;
      if (targetAge < 0) continue; // 跳過負年齡

      // 計算該年齡的小限法數據
      final profectionHouse = (targetAge % 12) + 1;
      final profectionSign = _getProfectionSign(profectionHouse, housesData);
      final timeLordInfo = _getTimeLordPlanet(profectionSign);

      // 手動設定年齡和相關數據（因為我們需要計算特定年齡的數據）
      final adjustedProfectionData = ProfectionData(
        currentAge: targetAge,
        profectionHouse: profectionHouse,
        profectionSign: profectionSign,
        timeLordPlanetId: timeLordInfo['id']!,
        timeLordPlanetName: timeLordInfo['name']!,
        timeLordPlanetSymbol: timeLordInfo['symbol']!,
        timeLordPlanetColor: timeLordInfo['color']!,
        houseTheme: _houseThemes[profectionHouse] ?? '未知主題',
        planetsInHouse: [], // 這裡需要根據實際星盤數據填入
        profectionYearStart: DateTime(
          birthData.dateTime.year + targetAge,
          birthData.dateTime.month,
          birthData.dateTime.day,
          birthData.dateTime.hour,
          birthData.dateTime.minute,
        ),
        profectionYearEnd: DateTime(
          birthData.dateTime.year + targetAge + 1,
          birthData.dateTime.month,
          birthData.dateTime.day,
          birthData.dateTime.hour,
          birthData.dateTime.minute,
        ).subtract(const Duration(days: 1)),
        isCurrent: targetAge == currentAge, // 基於年齡判斷是否為當前年
        description: _generateProfectionDescription(
          targetAge,
          profectionHouse,
          profectionSign,
          timeLordInfo['name']!,
        ),
        keywords: _houseKeywords[profectionHouse] ?? [],
      );

      timeline.add(adjustedProfectionData);

      // 如果這是當前年齡對應的數據，記錄為當前小限法
      if (targetAge == currentAge) {
        currentProfection = adjustedProfectionData;
      }
    }

    // 如果沒有找到當前小限法數據，重新計算一個
    if (currentProfection == null) {
      logger.w('未找到當前年齡 $currentAge 的小限法數據，重新計算');
      currentProfection = await calculateProfection(birthData, currentDate: currentDate, housesData: housesData);
    }

    final result = ProfectionTimelineResult(
      currentProfection: currentProfection,
      timeline: timeline,
      birthDateTime: birthData.dateTime,
      ascendantLongitude: 0.0, // 這裡需要根據實際星盤數據填入
      latitude: birthData.latitude,
      longitude: birthData.longitude,
    );

    logger.i('小限法時間軸計算完成，共 ${timeline.length} 年，當前年齡: $currentAge');
    return result;
  }

  /// 計算年齡
  int _calculateAge(DateTime birthDate, DateTime currentDate) {
    int age = currentDate.year - birthDate.year;
    
    // 如果還沒到生日，年齡減1
    if (currentDate.month < birthDate.month ||
        (currentDate.month == birthDate.month && currentDate.day < birthDate.day)) {
      age--;
    }
    
    return age;
  }

  /// 獲取小限宮位對應的星座
  /// 根據本命盤宮位數據計算正確的宮位星座
  String _getProfectionSign(int houseNumber, HouseCuspData? housesData) {
    if (housesData != null && housesData.cusps.isNotEmpty) {
      // 使用真實的宮位數據計算星座
      try {
        final houseCuspLongitude = housesData.cusps[houseNumber];
        return _getZodiacSignFromLongitude(houseCuspLongitude);
      } catch (e) {
        logger.w('無法獲取第${houseNumber}宮的宮位數據，使用簡化計算: $e');
      }
    }

    // 如果沒有宮位數據，使用簡化處理：假設第1宮為白羊座
    logger.w('沒有宮位數據，使用簡化計算');
    final signIndex = (houseNumber - 1) % 12;
    return AstrologyConstants.ZODIAC_SIGNS[signIndex];
  }

  /// 根據經度計算星座
  String _getZodiacSignFromLongitude(double longitude) {
    final signIndex = (longitude / 30).floor() % 12;
    return AstrologyConstants.ZODIAC_SIGNS[signIndex];
  }

  /// 獲取星座的主星信息
  Map<String, dynamic> _getTimeLordPlanet(String sign) {
    // 星座主星對應表（使用完整星座名稱）
    const signRulers = {
      '牡羊座': {'id': 4, 'name': '火星', 'symbol': '♂', 'color': 'FF0000'},
      '金牛座': {'id': 3, 'name': '金星', 'symbol': '♀', 'color': '00FF00'},
      '雙子座': {'id': 2, 'name': '水星', 'symbol': '☿', 'color': 'FFFF00'},
      '巨蟹座': {'id': 1, 'name': '月亮', 'symbol': '☽', 'color': 'C0C0C0'},
      '獅子座': {'id': 0, 'name': '太陽', 'symbol': '☉', 'color': 'FFA500'},
      '處女座': {'id': 2, 'name': '水星', 'symbol': '☿', 'color': 'FFFF00'},
      '天秤座': {'id': 3, 'name': '金星', 'symbol': '♀', 'color': '00FF00'},
      '天蠍座': {'id': 4, 'name': '火星', 'symbol': '♂', 'color': 'FF0000'},
      '射手座': {'id': 5, 'name': '木星', 'symbol': '♃', 'color': '0000FF'},
      '摩羯座': {'id': 6, 'name': '土星', 'symbol': '♄', 'color': '800080'},
      '水瓶座': {'id': 6, 'name': '土星', 'symbol': '♄', 'color': '800080'},
      '雙魚座': {'id': 5, 'name': '木星', 'symbol': '♃', 'color': '0000FF'},
    };

    final result = signRulers[sign];
    if (result == null) {
      logger.w('找不到星座 $sign 的主星，使用預設太陽');
      return {'id': 0, 'name': '太陽', 'symbol': '☉', 'color': 'FFA500'};
    }

    logger.d('星座 $sign 的主星: ${result['name']}');
    return result;
  }

  /// 生成小限法描述
  String _generateProfectionDescription(int age, int house, String sign, String timeLord) {
    return '在您的第${age}歲小限年中，小限上升點落在第${house}宮（${sign}座），'
           '由${timeLord}作為該年的時間主星（Time Lord）。'
           '這一年的主要焦點將圍繞${_houseThemes[house]}展開。';
  }
}
