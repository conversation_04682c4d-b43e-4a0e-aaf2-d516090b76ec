import 'package:firebase_auth/firebase_auth.dart';

import '../../../core/utils/logger_utils.dart';
import '../../models/user/app_user.dart';
import 'firebase_auth_service.dart';

/// 認證服務（使用 Firebase 認證）
class AuthService {
  // 初始化服務
  static Future<void> initialize() async {
    await FirebaseAuthService.initialize();
  }

  /// 獲取當前用戶
  static AppUser? getCurrentUser() {
    return FirebaseAuthService.getCurrentUser();
  }

  /// 認證狀態變化流
  static Stream<AppUser?> get authStateChanges => FirebaseAuthService.authStateChanges;

  /// 測試 Firebase 連接
  static Future<bool> testFirebaseConnection() async {
    try {
      logger.i('測試 Firebase 認證服務連接...');
      await FirebaseAuthService.initialize();
      logger.i('Firebase 認證服務連接成功');
      return true;
    } catch (e) {
      logger.e('Firebase 認證服務連接測試失敗: $e');
      return false;
    }
  }

  /// 使用電子郵件和密碼註冊
  static Future<AppUser?> registerWithEmailAndPassword({
    required String email,
    required String password,
    String? displayName,
  }) async {
    return await FirebaseAuthService.registerWithEmailAndPassword(
      email: email,
      password: password,
      displayName: displayName,
    );
  }

  /// 使用電子郵件和密碼登入
  static Future<AppUser?> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    final user = await FirebaseAuthService.signInWithEmailAndPassword(
      email: email,
      password: password,
    );

    // 登入成功後同步免費試用記錄
    if (user != null) {
      await _syncUserDataAfterSignIn();
    }

    return user;
  }

  /// Google 登入
  static Future<AppUser?> signInWithGoogle() async {
    final user = await FirebaseAuthService.signInWithGoogle();

    // 登入成功後同步免費試用記錄
    if (user != null) {
      await _syncUserDataAfterSignIn();
    }

    return user;
  }

  /// Apple 登入
  static Future<AppUser?> signInWithApple() async {
    final user = await FirebaseAuthService.signInWithApple();

    // 登入成功後同步免費試用記錄
    if (user != null) {
      await _syncUserDataAfterSignIn();
    }

    return user;
  }

  /// 匿名登入
  static Future<AppUser?> signInAnonymously() async {
    return await FirebaseAuthService.signInAnonymously();
  }

  /// 登出
  static Future<void> signOut() async {
    await FirebaseAuthService.signOut();
  }

  /// 發送密碼重設電子郵件
  static Future<void> sendPasswordResetEmail({required String email}) async {
    await FirebaseAuthService.sendPasswordResetEmail(email: email);
  }

  /// 發送電子郵件驗證
  static Future<void> sendEmailVerification() async {
    await FirebaseAuthService.sendEmailVerification();
  }

  /// 更新用戶資料
  static Future<AppUser?> updateUserProfile({
    String? displayName,
    String? photoURL,
  }) async {
    return await FirebaseAuthService.updateUserProfile(
      displayName: displayName,
      photoURL: photoURL,
    );
  }

  /// 刪除用戶帳戶（已棄用，請使用 AccountDeletionService.deleteUserAccount）
  @Deprecated('請使用 AccountDeletionService.deleteUserAccount 進行完整的帳戶刪除')
  static Future<void> deleteUser() async {
    final firebaseUser = FirebaseAuth.instance.currentUser;
    if (firebaseUser != null) {
      await firebaseUser.delete();
    }
  }

  /// 檢查用戶是否已登入
  static bool isSignedIn() {
    return getCurrentUser() != null;
  }

  /// 獲取當前用戶 ID
  static String? getCurrentUserId() {
    return getCurrentUser()?.uid;
  }

  /// 檢查用戶是否為匿名用戶
  static bool isAnonymous() {
    return getCurrentUser()?.isAnonymous ?? false;
  }

  /// 登入成功後同步用戶資料
  static Future<void> _syncUserDataAfterSignIn() async {
    try {
      final currentUser = getCurrentUser();
      if (currentUser != null) {
        // 免費試用功能已移除，不再需要初始化免費試用記錄
        logger.i('用戶 ${currentUser.uid} 登入成功');
      }

      logger.i('用戶登入後資料同步完成');
    } catch (e) {
      logger.e('用戶登入後資料同步失敗: $e');
    }
  }
}
