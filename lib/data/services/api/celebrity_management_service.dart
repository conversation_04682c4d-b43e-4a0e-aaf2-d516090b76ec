import 'dart:convert';

import 'package:cloud_firestore/cloud_firestore.dart';

import '../../../astreal.dart';
import 'admin_service.dart';
import 'remote_config_service.dart';

/// 名人資料模型（包含 ID）
class CelebrityWithId {
  final String id;
  final CelebrityExample celebrity;

  const CelebrityWithId({
    required this.id,
    required this.celebrity,
  });
}

/// 名人資料管理服務
/// 提供完整的名人資料 CRUD 操作和 RemoteConfig 同步功能
class CelebrityManagementService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static const String _collectionName = 'celebrity_management';
  static const String _remoteConfigKey = 'celebrity_examples';

  /// 獲取所有名人資料（優先從 Remote Config，備用 Firestore）
  static Future<List<CelebrityWithId>> getAllCelebrities() async {
    try {
      logger.i('獲取所有名人資料...');

      // 首先嘗試從 Remote Config 獲取
      final remoteConfigCelebrities = await _getCelebritiesFromRemoteConfig();
      if (remoteConfigCelebrities.isNotEmpty) {
        logger.i('成功從 Remote Config 獲取 ${remoteConfigCelebrities.length} 個名人資料');
        return remoteConfigCelebrities;
      }

      // 如果 Remote Config 沒有資料，從 Firestore 獲取
      logger.i('Remote Config 沒有資料，從 Firestore 獲取...');
      return await _getCelebritiesFromFirestore();

    } catch (e) {
      logger.e('獲取名人資料失敗: $e');
      return [];
    }
  }

  /// 從 Remote Config 獲取名人資料
  static Future<List<CelebrityWithId>> _getCelebritiesFromRemoteConfig() async {
    try {
      // 從 Remote Config 獲取配置
      final configValue = RemoteConfigService.getConfigValue('celebrity_examples');

      if (configValue.isEmpty) {
        logger.d('Remote Config 中沒有找到名人資料');
        return [];
      }

      // 解析 JSON 資料
      final List<dynamic> jsonList = json.decode(configValue);
      final celebrities = <CelebrityWithId>[];

      for (int i = 0; i < jsonList.length; i++) {
        try {
          final Map<String, dynamic> data = jsonList[i];
          final celebrity = CelebrityExample.fromJson(data);

          celebrities.add(CelebrityWithId(
            id: 'remote_config_$i', // 使用索引作為 ID
            celebrity: celebrity,
          ));
        } catch (e) {
          logger.w('解析名人資料失敗 (索引 $i): $e');
        }
      }

      return celebrities;
    } catch (e) {
      logger.e('從 Remote Config 獲取名人資料失敗: $e');
      return [];
    }
  }

  /// 從 Firestore 獲取名人資料（管理功能使用）
  static Future<List<CelebrityWithId>> _getCelebritiesFromFirestore() async {
    try {
      final querySnapshot = await _firestore
          .collection(_collectionName)
          .orderBy('createdAt', descending: true)
          .get();

      final celebrities = <CelebrityWithId>[];

      for (final doc in querySnapshot.docs) {
        try {
          final data = doc.data();

          final celebrity = _parseCelebrityFromFirestore(data);
          if (celebrity != null) {
            celebrities.add(CelebrityWithId(
              id: doc.id,
              celebrity: celebrity,
            ));
          }
        } catch (e) {
          logger.w('解析名人資料失敗 (${doc.id}): $e');
        }
      }

      logger.i('成功從 Firestore 獲取 ${celebrities.length} 個名人資料');
      return celebrities;
    } catch (e) {
      logger.e('從 Firestore 獲取名人資料失敗: $e');
      return [];
    }
  }

  /// 根據 ID 獲取名人資料
  static Future<CelebrityExample?> getCelebrityById(String id) async {
    try {
      final doc = await _firestore
          .collection(_collectionName)
          .doc(id)
          .get();

      if (!doc.exists) {
        logger.w('名人資料不存在: $id');
        return null;
      }

      final data = doc.data()!;

      return _parseCelebrityFromFirestore(data);
    } catch (e) {
      logger.e('獲取名人資料失敗 ($id): $e');
      return null;
    }
  }

  /// 新增名人資料
  static Future<String?> addCelebrity(CelebrityExample celebrity, String adminUid) async {
    try {
      logger.i('新增名人資料: ${celebrity.name}');

      final data = _celebrityToFirestoreData(celebrity);
      data['createdAt'] = FieldValue.serverTimestamp();
      data['createdBy'] = adminUid;
      data['updatedAt'] = FieldValue.serverTimestamp();
      data['updatedBy'] = adminUid;

      final docRef = await _firestore
          .collection(_collectionName)
          .add(data);

      // 記錄管理者操作
      await AdminService.logAdminAction(
        adminUid: adminUid,
        action: 'add_celebrity',
        description: '新增名人資料: ${celebrity.name}',
        metadata: {
          'celebrity_id': docRef.id,
          'celebrity_name': celebrity.name,
          'category': celebrity.category.name,
        },
      );

      logger.i('名人資料新增成功: ${docRef.id}');
      return docRef.id;
    } catch (e) {
      logger.e('新增名人資料失敗: $e');
      return null;
    }
  }

  /// 更新名人資料
  static Future<bool> updateCelebrity(String id, CelebrityExample celebrity, String adminUid) async {
    try {
      logger.i('更新名人資料: $id - ${celebrity.name}');

      final data = _celebrityToFirestoreData(celebrity);
      data['updatedAt'] = FieldValue.serverTimestamp();
      data['updatedBy'] = adminUid;

      await _firestore
          .collection(_collectionName)
          .doc(id)
          .update(data);

      // 記錄管理者操作
      await AdminService.logAdminAction(
        adminUid: adminUid,
        action: 'update_celebrity',
        description: '更新名人資料: ${celebrity.name}',
        metadata: {
          'celebrity_id': id,
          'celebrity_name': celebrity.name,
          'category': celebrity.category.name,
        },
      );

      logger.i('名人資料更新成功: $id');
      return true;
    } catch (e) {
      logger.e('更新名人資料失敗: $e');
      return false;
    }
  }

  /// 刪除名人資料
  static Future<bool> deleteCelebrity(String id, String adminUid) async {
    try {
      logger.i('刪除名人資料: $id');

      // 先獲取資料以記錄日誌
      final celebrity = await getCelebrityById(id);
      
      await _firestore
          .collection(_collectionName)
          .doc(id)
          .delete();

      // 記錄管理者操作
      await AdminService.logAdminAction(
        adminUid: adminUid,
        action: 'delete_celebrity',
        description: '刪除名人資料: ${celebrity?.name ?? 'Unknown'}',
        metadata: {
          'celebrity_id': id,
          'celebrity_name': celebrity?.name ?? 'Unknown',
          'category': celebrity?.category.name ?? 'Unknown',
        },
      );

      logger.i('名人資料刪除成功: $id');
      return true;
    } catch (e) {
      logger.e('刪除名人資料失敗: $e');
      return false;
    }
  }

  /// 同步到 RemoteConfig
  static Future<bool> syncToRemoteConfig(String adminUid) async {
    try {
      logger.i('開始同步名人資料到 RemoteConfig...');

      // 從 Firestore 獲取所有名人資料進行同步
      final celebrities = await _getCelebritiesFromFirestore();
      
      if (celebrities.isEmpty) {
        logger.w('沒有名人資料可同步');
        return false;
      }

      // 轉換為 RemoteConfig 格式
      final configData = {
        'examples': celebrities.map((celebrity) => _celebrityToRemoteConfigData(celebrity.celebrity)).toList(),
        'lastUpdated': DateTime.now().toIso8601String(),
        'updatedBy': adminUid,
        'totalCount': celebrities.length,
      };

      final configJson = jsonEncode(configData);
      
      // 這裡需要使用 Firebase Admin SDK 來更新 RemoteConfig
      // 由於客戶端無法直接更新 RemoteConfig，我們先保存到 Firestore
      await _firestore
          .collection('system')
          .doc('celebrity_remote_config')
          .set({
        'configData': configJson,
        'updatedAt': FieldValue.serverTimestamp(),
        'updatedBy': adminUid,
        'status': 'pending_sync',
      });

      // 記錄管理者操作
      await AdminService.logAdminAction(
        adminUid: adminUid,
        action: 'sync_to_remote_config',
        description: '同步名人資料到 RemoteConfig',
        metadata: {
          'celebrity_count': celebrities.length,
          'config_size': configJson.length,
        },
      );

      logger.i('名人資料已準備同步到 RemoteConfig (${celebrities.length} 個)');
      return true;
    } catch (e) {
      logger.e('同步到 RemoteConfig 失敗: $e');
      return false;
    }
  }

  /// 從 RemoteConfig 導入資料
  static Future<bool> importFromRemoteConfig(String adminUid) async {
    try {
      logger.i('從 RemoteConfig 導入名人資料...');

      final configValue = RemoteConfigService.getConfigValue(_remoteConfigKey);
      
      if (configValue.isEmpty) {
        logger.w('RemoteConfig 中沒有名人資料');
        return false;
      }

      final configData = jsonDecode(configValue) as Map<String, dynamic>;
      final examplesData = configData['examples'] as List<dynamic>? ?? [];

      int importCount = 0;
      
      for (final data in examplesData) {
        try {
          final celebrity = _parseRemoteConfigToCelebrity(data);
          if (celebrity != null) {
            // 檢查是否已存在（根據名稱）
            final existing = await _findCelebrityByName(celebrity.name);
            
            if (existing == null) {
              final id = await addCelebrity(celebrity, adminUid);
              if (id != null) {
                importCount++;
              }
            } else {
              logger.d('名人資料已存在，跳過: ${celebrity.name}');
            }
          }
        } catch (e) {
          logger.w('導入單個名人資料失敗: $e');
        }
      }

      // 記錄管理者操作
      await AdminService.logAdminAction(
        adminUid: adminUid,
        action: 'import_from_remote_config',
        description: '從 RemoteConfig 導入名人資料',
        metadata: {
          'imported_count': importCount,
          'total_in_config': examplesData.length,
        },
      );

      logger.i('從 RemoteConfig 導入完成: $importCount 個新資料');
      return true;
    } catch (e) {
      logger.e('從 RemoteConfig 導入失敗: $e');
      return false;
    }
  }

  /// 根據名稱查找名人資料
  static Future<CelebrityExample?> _findCelebrityByName(String name) async {
    try {
      final querySnapshot = await _firestore
          .collection(_collectionName)
          .where('name', isEqualTo: name)
          .limit(1)
          .get();

      if (querySnapshot.docs.isNotEmpty) {
        final data = querySnapshot.docs.first.data();
        data['id'] = querySnapshot.docs.first.id;
        return _parseCelebrityFromFirestore(data);
      }
      
      return null;
    } catch (e) {
      logger.e('查找名人資料失敗: $e');
      return null;
    }
  }

  /// 將 CelebrityExample 轉換為 Firestore 資料格式
  static Map<String, dynamic> _celebrityToFirestoreData(CelebrityExample celebrity) {
    return {
      'name': celebrity.name,
      'birthData': celebrity.birthData.toJson(),
      'description': celebrity.description,
      'category': celebrity.category.name,
      'recommendedTopics': celebrity.recommendedTopics,
      'isPopular': celebrity.isPopular,
      'imageUrl': celebrity.imageUrl,
    };
  }

  /// 將 CelebrityExample 轉換為 RemoteConfig 資料格式
  static Map<String, dynamic> _celebrityToRemoteConfigData(CelebrityExample celebrity) {
    return {
      'name': celebrity.name,
      'birthData': celebrity.birthData.toJson(),
      'description': celebrity.description,
      'category': celebrity.category.name,
      'recommendedTopics': celebrity.recommendedTopics,
      'isPopular': celebrity.isPopular,
      'imageUrl': celebrity.imageUrl,
    };
  }

  /// 從 Firestore 資料解析 CelebrityExample
  static CelebrityExample? _parseCelebrityFromFirestore(Map<String, dynamic> data) {
    try {
      return CelebrityExample(
        name: data['name'] as String,
        birthData: BirthData.fromJson(data['birthData'] as Map<String, dynamic>),
        description: data['description'] as String,
        category: CelebrityCategory.values.firstWhere(
          (c) => c.name == data['category'],
          orElse: () => CelebrityCategory.other,
        ),
        recommendedTopics: List<String>.from(data['recommendedTopics'] as List),
        isPopular: data['isPopular'] as bool? ?? false,
        imageUrl: data['imageUrl'] as String?,
      );
    } catch (e) {
      logger.e('解析 Firestore 名人資料失敗: $e');
      return null;
    }
  }

  /// 從 RemoteConfig 資料解析 CelebrityExample
  static CelebrityExample? _parseRemoteConfigToCelebrity(dynamic data) {
    try {
      final dataMap = data as Map<String, dynamic>;
      return CelebrityExample(
        name: dataMap['name'] as String,
        birthData: BirthData.fromJson(dataMap['birthData'] as Map<String, dynamic>),
        description: dataMap['description'] as String,
        category: CelebrityCategory.values.firstWhere(
          (c) => c.name == dataMap['category'],
          orElse: () => CelebrityCategory.other,
        ),
        recommendedTopics: List<String>.from(dataMap['recommendedTopics'] as List),
        isPopular: dataMap['isPopular'] as bool? ?? false,
        imageUrl: dataMap['imageUrl'] as String?,
      );
    } catch (e) {
      logger.e('解析 RemoteConfig 名人資料失敗: $e');
      return null;
    }
  }
}
