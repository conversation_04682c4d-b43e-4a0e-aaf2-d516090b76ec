import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:package_info_plus/package_info_plus.dart';

import '../../../core/config/app_version.dart';
import '../../../core/utils/logger_utils.dart';
import 'remote_config_service.dart';

/// 基於 Remote Config 的版本檢查服務
class RemoteConfigVersionService {
  
  /// 檢查版本更新
  static Future<RemoteConfigVersionCheckResult> checkForUpdates() async {
    try {
      // logger.i('開始檢查版本更新（使用 Remote Config）...');
      
      // 獲取當前應用版本信息
      final packageInfo = await PackageInfo.fromPlatform();
      final currentVersion = packageInfo.version;
      final currentBuildNumber = int.tryParse(packageInfo.buildNumber) ?? 1;
      
      // logger.i('當前版本: $currentVersion ($currentBuildNumber)');
      
      // 從 Remote Config 獲取版本配置
      final platformVersion = RemoteConfigService.getCurrentPlatformVersion();
      
      if (platformVersion == null) {
        logger.w('無法獲取平台版本配置');
        return const RemoteConfigVersionCheckResult(
          isSuccess: false,
          errorMessage: '無法獲取版本配置',
        );
      }

      // 解析版本信息
      final versionInfo = _parseVersionInfo(platformVersion);
      if (versionInfo == null) {
        logger.w('版本配置格式錯誤');
        return RemoteConfigVersionCheckResult(
          isSuccess: false,
          errorMessage: '版本配置格式錯誤',
        );
      }
      
      // logger.i('遠端版本: ${versionInfo.version} (${versionInfo.buildNumber})');
      
      // 檢查是否需要更新
      final needsUpdate = _compareVersions(
        currentVersion: currentVersion,
        currentBuildNumber: currentBuildNumber,
        remoteVersion: versionInfo.version,
        remoteBuildNumber: versionInfo.buildNumber,
      );
      
      // 檢查是否強制更新
      final isForceUpdate = _checkForceUpdate(
        currentVersion: currentVersion,
        currentBuildNumber: currentBuildNumber,
        minRequiredVersion: versionInfo.minRequiredVersion,
        minRequiredBuildNumber: versionInfo.minRequiredBuildNumber,
        forceUpdate: versionInfo.forceUpdate,
      );
      
      // logger.i('需要更新: $needsUpdate, 強制更新: $isForceUpdate');

      return RemoteConfigVersionCheckResult(
        isSuccess: true,
        needsUpdate: needsUpdate,
        isForceUpdate: isForceUpdate,
        latestVersion: needsUpdate ? versionInfo : null,
      );

    } catch (e) {
      logger.e('版本檢查失敗: $e');
      return RemoteConfigVersionCheckResult(
        isSuccess: false,
        errorMessage: e.toString(),
      );
    }
  }
  
  /// 解析版本信息
  static AppVersion? _parseVersionInfo(Map<String, dynamic> config) {
    try {
      final releaseDateStr = config['releaseDate'] as String? ?? DateTime.now().toIso8601String();
      final releaseDate = DateTime.tryParse(releaseDateStr) ?? DateTime.now();

      return AppVersion(
        version: config['version'] as String? ?? '1.0.0',
        buildNumber: config['buildNumber'] as int? ?? 1,
        minRequiredVersion: config['minRequiredVersion'] as String? ?? '1.0.0',
        minRequiredBuildNumber: config['minRequiredBuildNumber'] as int? ?? 1,
        forceUpdate: config['forceUpdate'] as bool? ?? false,
        updateMessage: config['updateMessage'] as String? ?? '有新版本可用',
        updateUrl: config['updateUrl'] as String? ?? '',
        releaseDate: releaseDate,
        features: (config['features'] as List<dynamic>?)?.cast<String>() ?? [],
        isActive: config['isActive'] as bool? ?? true,
      );
    } catch (e) {
      logger.e('解析版本信息失敗: $e');
      return null;
    }
  }
  
  /// 比較版本號
  static bool _compareVersions({
    required String currentVersion,
    required int currentBuildNumber,
    required String remoteVersion,
    required int remoteBuildNumber,
  }) {
    try {
      // 比較版本號
      final currentParts = currentVersion.split('.').map(int.parse).toList();
      final remoteParts = remoteVersion.split('.').map(int.parse).toList();
      
      // 確保版本號長度一致
      while (currentParts.length < 3) currentParts.add(0);
      while (remoteParts.length < 3) remoteParts.add(0);
      
      // 比較主版本號
      for (int i = 0; i < 3; i++) {
        if (remoteParts[i] > currentParts[i]) {
          return true; // 遠端版本更新
        } else if (remoteParts[i] < currentParts[i]) {
          return false; // 當前版本更新
        }
      }
      
      // 版本號相同，比較 build number
      return remoteBuildNumber > currentBuildNumber;
      
    } catch (e) {
      logger.e('版本比較失敗: $e');
      // 如果比較失敗，保守起見認為不需要更新
      return false;
    }
  }
  
  /// 檢查是否強制更新
  static bool _checkForceUpdate({
    required String currentVersion,
    required int currentBuildNumber,
    required String minRequiredVersion,
    required int minRequiredBuildNumber,
    required bool forceUpdate,
  }) {
    try {
      // 如果配置中設定強制更新，直接返回 true
      if (forceUpdate) {
        return true;
      }
      
      // 檢查是否低於最低要求版本
      final currentParts = currentVersion.split('.').map(int.parse).toList();
      final minRequiredParts = minRequiredVersion.split('.').map(int.parse).toList();
      
      // 確保版本號長度一致
      while (currentParts.length < 3) currentParts.add(0);
      while (minRequiredParts.length < 3) minRequiredParts.add(0);
      
      // 比較最低要求版本
      for (int i = 0; i < 3; i++) {
        if (currentParts[i] < minRequiredParts[i]) {
          return true; // 當前版本低於最低要求
        } else if (currentParts[i] > minRequiredParts[i]) {
          return false; // 當前版本高於最低要求
        }
      }
      
      // 版本號相同，比較 build number
      return currentBuildNumber < minRequiredBuildNumber;
      
    } catch (e) {
      logger.e('強制更新檢查失敗: $e');
      // 如果檢查失敗，保守起見不強制更新
      return false;
    }
  }
  
  /// 獲取當前平台標識
  static String getCurrentPlatform() {
    if (kIsWeb) {
      return 'web';
    } else if (Platform.isIOS) {
      return 'ios';
    } else if (Platform.isAndroid) {
      return 'android';
    } else if (Platform.isMacOS) {
      return 'macos';
    } else if (Platform.isWindows) {
      return 'windows';
    } else {
      return 'unknown';
    }
  }
  
  /// 獲取版本配置摘要（用於調試）
  static Map<String, dynamic> getVersionSummary() {
    try {
      final allVersions = RemoteConfigService.getAllVersions();
      final currentPlatform = getCurrentPlatform();
      final platformVersion = allVersions[currentPlatform];
      
      return {
        'currentPlatform': currentPlatform,
        'hasVersionConfig': allVersions.isNotEmpty,
        'hasPlatformConfig': platformVersion != null,
        'availablePlatforms': allVersions.keys.toList(),
        'platformVersion': platformVersion,
      };
    } catch (e) {
      return {
        'error': e.toString(),
      };
    }
  }
}

/// Remote Config 版本檢查結果
class RemoteConfigVersionCheckResult {
  final bool isSuccess;
  final bool needsUpdate;
  final bool isForceUpdate;
  final AppVersion? latestVersion;
  final String? errorMessage;

  const RemoteConfigVersionCheckResult({
    required this.isSuccess,
    this.needsUpdate = false,
    this.isForceUpdate = false,
    this.latestVersion,
    this.errorMessage,
  });

  @override
  String toString() {
    return 'RemoteConfigVersionCheckResult(isSuccess: $isSuccess, needsUpdate: $needsUpdate, '
           'isForceUpdate: $isForceUpdate, errorMessage: $errorMessage)';
  }
}
