import 'dart:convert';
import 'dart:math';

import 'package:shared_preferences/shared_preferences.dart';

import '../../models/user/birth_data.dart';

/// 出生資料管理服務
class BirthDataService {
  static const String _birthDataListKey = 'birthDataList';
  static final Random _random = Random();
  static const String _selectedBirthDataIdKey = 'selectedBirthDataId';

  /// 獲取所有出生資料
  Future<List<BirthData>> getAllBirthData() async {
    final prefs = await SharedPreferences.getInstance();
    final birthDataListJson = prefs.getString(_birthDataListKey);
    
    if (birthDataListJson == null) return [];
    
    try {
      final List<dynamic> decodedData = jsonDecode(birthDataListJson);
      return decodedData
          .map((item) => BirthData.fromJson(item))
          .toList();
    } catch (e) {
      return [];
    }
  }

  /// 保存所有出生資料
  Future<void> saveAllBirthData(List<BirthData> birthDataList) async {
    final prefs = await SharedPreferences.getInstance();
    final List<Map<String, dynamic>> encodedData = 
        birthDataList.map((data) => data.toJson()).toList();
    await prefs.setString(_birthDataListKey, jsonEncode(encodedData));
  }

  /// 添加新的出生資料
  Future<void> addBirthData(BirthData birthData) async {
    final birthDataList = await getAllBirthData();

    // 如果 ID 為空或已存在，生成新的 ID
    String finalId = birthData.id;
    if (finalId.isEmpty || birthDataList.any((data) => data.id == finalId)) {
      finalId = _generateUniqueId();
      // 確保生成的 ID 是唯一的
      while (birthDataList.any((data) => data.id == finalId)) {
        finalId = _generateUniqueId();
      }
    }

    // 創建新的 BirthData 實例，使用確定唯一的 ID
    final newBirthData = BirthData(
      id: finalId,
      name: birthData.name,
      dateTime: birthData.dateTime,
      birthPlace: birthData.birthPlace,
      notes: birthData.notes,
      latitude: birthData.latitude,
      longitude: birthData.longitude,
      createdAt: birthData.createdAt,
      category: birthData.category,
      gender: birthData.gender,
      lastAccessedAt: birthData.lastAccessedAt,
    );

    // 檢查是否已存在相同ID的資料（應該不會，但保險起見）
    final existingIndex = birthDataList.indexWhere((data) => data.id == finalId);
    if (existingIndex != -1) {
      // 更新現有資料
      birthDataList[existingIndex] = newBirthData;
    } else {
      // 添加新資料
      birthDataList.add(newBirthData);
    }

    await saveAllBirthData(birthDataList);
  }

  /// 更新出生資料
  Future<void> updateBirthData(BirthData updatedData) async {
    final birthDataList = await getAllBirthData();
    final index = birthDataList.indexWhere((data) => data.id == updatedData.id);
    
    if (index != -1) {
      birthDataList[index] = updatedData;
      await saveAllBirthData(birthDataList);
    }
  }

  /// 刪除出生資料
  Future<void> deleteBirthData(String id) async {
    final birthDataList = await getAllBirthData();
    birthDataList.removeWhere((data) => data.id == id);
    await saveAllBirthData(birthDataList);
  }

  /// 根據ID獲取出生資料
  Future<BirthData?> getBirthDataById(String id) async {
    final birthDataList = await getAllBirthData();
    try {
      return birthDataList.firstWhere((data) => data.id == id);
    } catch (e) {
      return null;
    }
  }

  /// 獲取當前選中的出生資料
  Future<BirthData?> getSelectedBirthData() async {
    final prefs = await SharedPreferences.getInstance();
    final selectedId = prefs.getString(_selectedBirthDataIdKey);
    
    if (selectedId == null) return null;
    
    return await getBirthDataById(selectedId);
  }

  /// 設置選中的出生資料
  Future<void> setSelectedBirthData(String id) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_selectedBirthDataIdKey, id);
  }

  /// 更新最後訪問時間
  Future<void> updateLastAccessedTime(String id) async {
    final birthData = await getBirthDataById(id);
    if (birthData != null) {
      final updatedData = birthData.copyWith(
        lastAccessedAt: DateTime.now(),
      );
      await updateBirthData(updatedData);
    }
  }

  /// 搜索出生資料
  Future<List<BirthData>> searchBirthData(String query) async {
    if (query.isEmpty) return getAllBirthData();
    
    final allData = await getAllBirthData();
    final lowerQuery = query.toLowerCase();
    
    return allData.where((data) {
      return data.name.toLowerCase().contains(lowerQuery) ||
             data.birthPlace.toLowerCase().contains(lowerQuery) ||
             (data.notes?.toLowerCase().contains(lowerQuery) ?? false);
    }).toList();
  }

  /// 按類別篩選
  Future<List<BirthData>> filterByCategory(String category) async {
    final allData = await getAllBirthData();
    return allData.where((data) => data.category.displayName == category).toList();
  }

  /// 獲取最近訪問的出生資料
  Future<List<BirthData>> getRecentBirthData({int limit = 10}) async {
    final allData = await getAllBirthData();

    // 按最後訪問時間排序
    allData.sort((a, b) {
      final aTime = a.lastAccessedAt ?? a.createdAt;
      final bTime = b.lastAccessedAt ?? b.createdAt;
      return bTime.compareTo(aTime);
    });

    return allData.take(limit).toList();
  }

  /// 清除所有出生資料（用於帳戶刪除）
  Future<void> clearAllBirthData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_birthDataListKey);
    await prefs.remove(_selectedBirthDataIdKey);
  }

  /// 生成唯一的 ID
  String _generateUniqueId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final randomPart = _random.nextInt(999999).toString().padLeft(6, '0');
    return '${timestamp}_$randomPart';
  }
}
