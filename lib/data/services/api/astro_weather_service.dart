import 'package:astreal/core/utils/logger_utils.dart';

import '../../models/astrology/aspect_info.dart';
import '../../models/astrology/chart_data.dart';
import '../../models/astrology/chart_type.dart';
import '../../models/user/birth_data.dart';
import 'astrology_service.dart';

/// 占星天氣預報等級
enum AstroWeatherLevel {
  stable('☀️', '穩定期', '無明顯強行運，適合做日常安排'),
  lightFluctuation('🌤', '輕度波動', '溫和的行運影響，情感/人際良好'),
  emotionalDisturbance('🌧', '情緒干擾', '容易情緒波動，需要注意控制'),
  dramaticChange('⛈', '劇烈變動', '重大轉化期，人生可能有重要變化'),
  destinyStorm('🌪', '命運風暴', '多重強力行運，命運轉折的關鍵時期');

  const AstroWeatherLevel(this.emoji, this.name, this.description);

  final String emoji;
  final String name;
  final String description;
}

/// 占星天氣預報結果
class AstroWeatherForecast {
  final AstroWeatherLevel level;
  final String summary;
  final List<String> highlights;
  final List<AspectInfo> keyAspects;
  final double intensity; // 0.0 - 1.0

  AstroWeatherForecast({
    required this.level,
    required this.summary,
    required this.highlights,
    required this.keyAspects,
    required this.intensity,
  });
}

/// 占星天氣預報服務
class AstroWeatherService {
  static final AstroWeatherService _instance = AstroWeatherService._internal();
  factory AstroWeatherService() => _instance;
  AstroWeatherService._internal();

  /// 生成今日占星天氣預報
  Future<AstroWeatherForecast> generateTodayForecast({
    required BirthData person,
    DateTime? targetDate,
  }) async {
    try {
      final date = targetDate ?? DateTime.now();
      
      // 創建行運盤數據
      final transitChartData = ChartData(
        chartType: ChartType.transit,
        primaryPerson: person,
        specificDate: date,
      );

      // 計算行運盤
      final calculatedData = await AstrologyService().calculateChartData(
        transitChartData,
      );

      // 分析相位
      final aspects = calculatedData.aspects ?? [];
      
      // 計算天氣等級和強度
      final weatherAnalysis = _analyzeWeatherLevel(aspects);
      
      // 生成預報摘要
      final summary = _generateSummary(weatherAnalysis, aspects);
      
      // 提取重點星象
      final highlights = _extractHighlights(aspects);

      return AstroWeatherForecast(
        level: weatherAnalysis['level'],
        summary: summary,
        highlights: highlights,
        keyAspects: weatherAnalysis['keyAspects'],
        intensity: weatherAnalysis['intensity'],
      );
    } catch (e) {
      logger.e('生成占星天氣預報失敗: $e');
      rethrow;
    }
  }

  /// 分析天氣等級
  Map<String, dynamic> _analyzeWeatherLevel(List<AspectInfo> aspects) {
    double totalIntensity = 0.0;
    List<AspectInfo> keyAspects = [];
    
    // 重要行星權重
    final planetWeights = {
      '太陽': 5.0,
      '月亮': 4.5,
      '上升': 4.0,
      '中天': 4.0,
      '火星': 3.5,
      '土星': 3.5,
      '冥王星': 4.5,
      '天王星': 3.0,
      '海王星': 2.5,
      '木星': 2.0,
      '金星': 2.0,
      '水星': 1.5,
    };

    // 相位強度權重
    final aspectWeights = {
      '合相': 1.0,
      '對分相': 0.9,
      '四分相': 0.8,
      '三分相': 0.6,
      '六分相': 0.4,
    };

    for (final aspect in aspects) {
      // 計算行星權重
      final planet1Weight = planetWeights[aspect.planet1.name] ?? 1.0;
      final planet2Weight = planetWeights[aspect.planet2.name] ?? 1.0;
      final maxPlanetWeight = planet1Weight > planet2Weight ? planet1Weight : planet2Weight;
      
      // 計算相位權重
      final aspectWeight = aspectWeights[aspect.aspect] ?? 0.3;
      
      // 計算容許度影響（容許度越小，影響越強）
      final orbFactor = 1.0 - (aspect.orb / 10.0).clamp(0.0, 1.0);
      
      // 計算總強度
      final aspectIntensity = maxPlanetWeight * aspectWeight * orbFactor;
      totalIntensity += aspectIntensity;
      
      // 收集重要相位
      if (aspectIntensity > 2.0) {
        keyAspects.add(aspect);
      }
    }

    // 標準化強度
    final normalizedIntensity = (totalIntensity / 20.0).clamp(0.0, 1.0);
    
    // 確定天氣等級
    AstroWeatherLevel level;
    if (normalizedIntensity < 0.2) {
      level = AstroWeatherLevel.stable;
    } else if (normalizedIntensity < 0.4) {
      level = AstroWeatherLevel.lightFluctuation;
    } else if (normalizedIntensity < 0.6) {
      level = AstroWeatherLevel.emotionalDisturbance;
    } else if (normalizedIntensity < 0.8) {
      level = AstroWeatherLevel.dramaticChange;
    } else {
      level = AstroWeatherLevel.destinyStorm;
    }

    // 按重要性排序關鍵相位
    keyAspects.sort((a, b) {
      final aWeight = (planetWeights[a.planet1.name] ?? 1.0) + (planetWeights[a.planet2.name] ?? 1.0);
      final bWeight = (planetWeights[b.planet1.name] ?? 1.0) + (planetWeights[b.planet2.name] ?? 1.0);
      return bWeight.compareTo(aWeight);
    });

    return {
      'level': level,
      'intensity': normalizedIntensity,
      'keyAspects': keyAspects.take(3).toList(), // 最多取前3個重要相位
    };
  }

  /// 生成預報摘要
  String _generateSummary(Map<String, dynamic> analysis, List<AspectInfo> aspects) {
    final level = analysis['level'] as AstroWeatherLevel;
    final keyAspects = analysis['keyAspects'] as List<AspectInfo>;
    
    String summary = '今天屬於${level.emoji} ${level.name}';
    
    if (keyAspects.isNotEmpty) {
      final mainAspect = keyAspects.first;
      final aspectMeaning = _getAspectMeaning(mainAspect);
      summary += '，${mainAspect.planet1.name}${_getAspectSymbol(mainAspect.aspect)}${mainAspect.planet2.name}，$aspectMeaning';
    } else {
      summary += '，${level.description}';
    }
    
    return '$summary。';
  }

  /// 提取重點星象
  List<String> _extractHighlights(List<AspectInfo> aspects) {
    List<String> highlights = [];
    
    // 按重要性分組
    final importantAspects = aspects.where((aspect) => 
      ['合相', '對分相', '四分相'].contains(aspect.aspect) &&
      ['太陽', '月亮', '火星', '土星', '冥王星'].any((planet) => 
        aspect.planet1.name == planet || aspect.planet2.name == planet)
    ).take(3);
    
    for (final aspect in importantAspects) {
      final meaning = _getAspectMeaning(aspect);
      highlights.add('${aspect.planet1.name}${_getAspectSymbol(aspect.aspect)}${aspect.planet2.name}：$meaning');
    }
    
    return highlights;
  }

  /// 獲取相位含義
  String _getAspectMeaning(AspectInfo aspect) {
    final planet1 = aspect.planet1.name;
    final planet2 = aspect.planet2.name;
    final aspectType = aspect.aspect;
    
    // 基於行星組合和相位類型的含義
    if (planet1 == '金星' || planet2 == '金星') {
      switch (aspectType) {
        case '合相':
        case '三分相':
        case '六分相':
          return '感情順利，人際和諧';
        case '對分相':
        case '四分相':
          return '感情波動，需要平衡';
      }
    }
    
    if (planet1 == '火星' || planet2 == '火星') {
      switch (aspectType) {
        case '合相':
          return '行動力強，注意衝動';
        case '對分相':
        case '四分相':
          return '易怒易躁，控制情緒';
        case '三分相':
        case '六分相':
          return '積極進取，行動順利';
      }
    }
    
    if (planet1 == '土星' || planet2 == '土星') {
      switch (aspectType) {
        case '合相':
        case '對分相':
        case '四分相':
          return '面臨考驗，需要耐心';
        case '三分相':
        case '六分相':
          return '穩定發展，收穫成果';
      }
    }
    
    if (planet1 == '木星' || planet2 == '木星') {
      switch (aspectType) {
        case '合相':
        case '三分相':
        case '六分相':
          return '機會增加，運勢提升';
        case '對分相':
        case '四分相':
          return '過度樂觀，注意分寸';
      }
    }
    
    // 默認含義
    switch (aspectType) {
      case '合相':
        return '能量集中，影響強烈';
      case '對分相':
        return '需要平衡，可能有衝突';
      case '四分相':
        return '面臨挑戰，需要調整';
      case '三分相':
        return '和諧順利，發展良好';
      case '六分相':
        return '小幅改善，溫和影響';
      default:
        return '星象影響';
    }
  }

  /// 獲取相位符號
  String _getAspectSymbol(String aspect) {
    switch (aspect) {
      case '合相':
        return '合';
      case '對分相':
        return '沖';
      case '四分相':
        return '刑';
      case '三分相':
        return '拱';
      case '六分相':
        return '六合';
      default:
        return aspect;
    }
  }
}
