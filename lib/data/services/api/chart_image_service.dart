import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:gal/gal.dart';
import 'package:logger/logger.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';

/// 星盤圖片儲存服務
/// 
/// 提供以下功能：
/// 1. 將星盤 Widget 轉換為圖片
/// 2. 儲存圖片到相簿
/// 3. 分享圖片
/// 4. 處理權限請求
class ChartImageService {
  static final Logger _logger = Logger();

  /// 將 Widget 轉換為圖片
  ///
  /// [repaintBoundaryKey] RepaintBoundary 的 GlobalKey
  /// [pixelRatio] 像素比例，預設為 2.0（高解析度）
  ///
  /// 返回 Uint8List 格式的 PNG 圖片數據
  static Future<Uint8List?> captureFromKey(
    GlobalKey repaintBoundaryKey, {
    double pixelRatio = 2.0,
  }) async {
    try {
      _logger.i('開始從 RepaintBoundary 擷取圖片');

      // 獲取 RenderRepaintBoundary
      final RenderRepaintBoundary boundary = repaintBoundaryKey.currentContext!
          .findRenderObject() as RenderRepaintBoundary;

      // 轉換為圖片
      final ui.Image image = await boundary.toImage(pixelRatio: pixelRatio);
      final ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);

      if (byteData == null) {
        _logger.e('無法將圖片轉換為 ByteData');
        return null;
      }

      final Uint8List imageBytes = byteData.buffer.asUint8List();
      _logger.i('圖片擷取成功，圖片大小: ${imageBytes.length} bytes');

      return imageBytes;
    } catch (e, stackTrace) {
      _logger.e('圖片擷取失敗: $e');
      _logger.e('堆疊追蹤: $stackTrace');
      return null;
    }
  }

  /// 請求儲存權限
  ///
  /// 返回是否獲得權限
  static Future<bool> requestStoragePermission() async {
    try {
      _logger.i('請求儲存權限');

      // 使用 Gal 套件的內建權限檢查
      final hasAccess = await Gal.hasAccess();
      _logger.i('當前相簿存取權限狀態: $hasAccess');

      if (!hasAccess) {
        // 請求權限
        final granted = await Gal.requestAccess();
        _logger.i('請求後相簿存取權限狀態: $granted');
        return granted;
      }

      return hasAccess;
    } catch (e) {
      _logger.e('請求儲存權限失敗: $e');
      return false;
    }
  }

  /// 儲存圖片到相簿
  /// 
  /// [imageBytes] 圖片數據
  /// [fileName] 檔案名稱，不包含副檔名
  /// 
  /// 返回是否儲存成功
  static Future<bool> saveToGallery(
    Uint8List imageBytes, {
    String fileName = 'astreal_chart',
  }) async {
    try {
      _logger.i('開始儲存圖片到相簿，檔案名: $fileName');

      // 請求權限
      final hasPermission = await requestStoragePermission();
      if (!hasPermission) {
        _logger.w('沒有儲存權限，無法儲存圖片');
        return false;
      }

      // 儲存到相簿
      await Gal.putImageBytes(
        imageBytes,
        name: '$fileName.png',
      );

      _logger.i('圖片儲存成功: $fileName.png');
      return true;
    } catch (e, stackTrace) {
      _logger.e('儲存圖片到相簿失敗: $e');
      _logger.e('堆疊追蹤: $stackTrace');
      return false;
    }
  }

  /// 分享圖片
  /// 
  /// [imageBytes] 圖片數據
  /// [fileName] 檔案名稱，不包含副檔名
  /// [subject] 分享主題
  /// [text] 分享文字
  /// 
  /// 返回是否分享成功
  static Future<bool> shareImage(
    Uint8List imageBytes, {
    String fileName = 'astreal_chart',
    String? subject,
    String? text,
  }) async {
    try {
      _logger.i('開始分享圖片，檔案名: $fileName');

      // 獲取臨時目錄
      final tempDir = await getTemporaryDirectory();
      final filePath = '${tempDir.path}/$fileName.png';

      // 寫入臨時檔案
      final file = File(filePath);
      await file.writeAsBytes(imageBytes);

      // 分享檔案
      await Share.shareXFiles(
        [XFile(filePath)],
        subject: subject ?? '星盤圖',
        text: text ?? '來自 AstReal 的星盤圖',
      );

      _logger.i('圖片分享成功');
      return true;
    } catch (e, stackTrace) {
      _logger.e('分享圖片失敗: $e');
      _logger.e('堆疊追蹤: $stackTrace');
      return false;
    }
  }

  /// 生成檔案名稱
  /// 
  /// [personName] 人物姓名
  /// [chartType] 星盤類型
  /// [timestamp] 時間戳，預設為當前時間
  /// 
  /// 返回格式化的檔案名稱
  static String generateFileName({
    String? personName,
    String? chartType,
    DateTime? timestamp,
  }) {
    final now = timestamp ?? DateTime.now();
    final dateStr = '${now.year}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}';
    final timeStr = '${now.hour.toString().padLeft(2, '0')}${now.minute.toString().padLeft(2, '0')}';
    
    String fileName = 'astreal_chart_$dateStr$timeStr';
    
    if (personName != null && personName.isNotEmpty) {
      // 移除特殊字符，只保留字母、數字和中文
      final cleanName = personName.replaceAll(RegExp(r'[^\w\u4e00-\u9fff]'), '');
      if (cleanName.isNotEmpty) {
        fileName = '${cleanName}_$fileName';
      }
    }
    
    if (chartType != null && chartType.isNotEmpty) {
      fileName = '${fileName}_$chartType';
    }
    
    return fileName;
  }

  /// 獲取圖片儲存路徑（僅用於顯示）
  ///
  /// 返回圖片在相簿中的預期路徑
  static Future<String> getGalleryPath() async {
    if (Platform.isAndroid) {
      return '/storage/emulated/0/Pictures/AstReal';
    } else if (Platform.isIOS) {
      return '相簿 > AstReal';
    }
    return '相簿';
  }

  /// 開啟應用設定頁面
  ///
  /// 返回是否成功開啟設定
  static Future<bool> openSettings() async {
    try {
      _logger.i('開啟應用設定頁面');
      // 使用 Gal 套件的內建設定開啟功能
      await Gal.open();
      return true;
    } catch (e) {
      _logger.e('開啟應用設定失敗: $e');
      return false;
    }
  }

  /// 檢查權限狀態並提供建議
  ///
  /// 返回權限狀態描述
  static Future<String> getPermissionStatusMessage() async {
    try {
      final hasAccess = await Gal.hasAccess();
      if (hasAccess) {
        return '相簿存取權限已開啟';
      } else {
        return '相簿存取權限被拒絕，請重新授權或到設定中手動開啟';
      }
    } catch (e) {
      _logger.e('檢查權限狀態失敗: $e');
      return '檢查權限狀態失敗';
    }
  }
}
