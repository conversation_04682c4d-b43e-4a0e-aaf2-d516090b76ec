import 'package:firebase_messaging/firebase_messaging.dart';

import '../../../astreal.dart';
import '../../models/notification/notification_model.dart';
import 'local_notification_service.dart';

/// 背景通知處理器
/// 處理應用在背景或已關閉時收到的推播通知
class BackgroundNotificationHandler {
  /// 處理背景通知的頂層函數
  /// 必須是頂層函數或靜態方法，因為它會在獨立的 isolate 中運行
  @pragma('vm:entry-point')
  static Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
    try {
      // 在背景 isolate 中初始化 Firebase（如果需要）
      // await Firebase.initializeApp();

      print('收到背景通知: ${message.notification?.title}');

      // 創建通知模型
      final notification = _createNotificationFromRemoteMessage(message);

      // 顯示本地通知
      await _showLocalNotification(notification);

      // 保存通知到本地存儲（如果需要）
      await _saveNotificationLocally(notification);

      print('背景通知處理完成');
    } catch (e) {
      print('處理背景通知失敗: $e');
    }
  }

  /// 從 RemoteMessage 創建通知模型
  static NotificationModel _createNotificationFromRemoteMessage(RemoteMessage message) {
    final data = message.data;
    final notification = message.notification;
    
    return NotificationModel.fromRemoteMessage(
      data['id'] ?? DateTime.now().millisecondsSinceEpoch.toString(),
      {
        'title': notification?.title ?? data['title'] ?? '新通知',
        'body': notification?.body ?? data['body'] ?? '',
        'type': data['type'] ?? 'general',
        'priority': data['priority'] ?? '1',
        'imageUrl': notification?.android?.imageUrl ?? data['imageUrl'],
        'actionUrl': data['actionUrl'],
        'userId': data['userId'],
        ...data,
      },
    );
  }

  /// 顯示本地通知
  static Future<void> _showLocalNotification(NotificationModel notification) async {
    try {
      // 初始化本地通知服務（如果尚未初始化）
      await LocalNotificationService.initialize();
      
      // 顯示通知
      await LocalNotificationService.showNotification(notification);
      
      logger.i('背景本地通知顯示成功: ${notification.title}');
    } catch (e) {
      logger.e('顯示背景本地通知失敗: $e');
    }
  }

  /// 保存通知到本地存儲
  static Future<void> _saveNotificationLocally(NotificationModel notification) async {
    try {
      // 這裡可以實現將通知保存到本地數據庫或 SharedPreferences
      // 以便應用啟動時能夠顯示歷史通知
      
      // 由於在背景 isolate 中，我們需要使用簡單的存儲方式
      // 實際實現可能需要使用 Hive 或其他支持 isolate 的存儲方案
      
      logger.i('通知已保存到本地: ${notification.id}');
    } catch (e) {
      logger.e('保存通知到本地失敗: $e');
    }
  }

  /// 註冊背景通知處理器（已移至 main.dart）
  @Deprecated('背景通知處理器現在在 main.dart 中註冊')
  static void registerBackgroundHandler() {
    logger.w('背景通知處理器註冊已移至 main.dart，此方法已棄用');
  }
}

/// 通知導航處理器
/// 處理通知點擊後的導航邏輯
class NotificationNavigationHandler {
  /// 處理通知點擊導航
  static void handleNotificationNavigation(String notificationId, {
    required Function(String) navigateToNotificationPage,
    Function(String)? navigateToSpecificPage,
  }) {
    try {
      logger.i('處理通知導航: $notificationId');
      
      // 默認導航到通知頁面
      navigateToNotificationPage(notificationId);
      
      // 如果有特定的導航邏輯，可以在這裡實現
      // 例如根據通知類型或 actionUrl 導航到不同頁面
      
    } catch (e) {
      logger.e('處理通知導航失敗: $e');
    }
  }

  /// 根據通知類型獲取導航路由
  static String? getNavigationRoute(NotificationModel notification) {
    switch (notification.type) {
      case NotificationType.systemAnnouncement:
        return '/system-announcement';
      case NotificationType.featureUpdate:
        return '/feature-update';
      case NotificationType.promotion:
        return '/promotion';
      case NotificationType.astroEvent:
        return '/astro-event';
      case NotificationType.personalReminder:
        return '/personal-reminder';
      default:
        return '/notifications';
    }
  }

  /// 解析動作 URL
  static Map<String, String>? parseActionUrl(String? actionUrl) {
    if (actionUrl == null || actionUrl.isEmpty) return null;
    
    try {
      final uri = Uri.parse(actionUrl);
      return {
        'scheme': uri.scheme,
        'host': uri.host,
        'path': uri.path,
        'query': uri.query,
      };
    } catch (e) {
      logger.e('解析動作 URL 失敗: $e');
      return null;
    }
  }
}

/// 通知權限管理器
class NotificationPermissionManager {
  /// 檢查並請求通知權限
  static Future<bool> checkAndRequestPermissions() async {
    try {
      // 檢查本地通知權限
      final localPermission = await LocalNotificationService.areNotificationsEnabled();
      
      if (!localPermission) {
        logger.w('本地通知權限未授權');
        return false;
      }
      
      // 檢查 Firebase 通知權限
      final messaging = FirebaseMessaging.instance;
      final settings = await messaging.getNotificationSettings();
      
      if (settings.authorizationStatus != AuthorizationStatus.authorized &&
          settings.authorizationStatus != AuthorizationStatus.provisional) {
        logger.w('Firebase 通知權限未授權: ${settings.authorizationStatus}');
        
        // 嘗試請求權限
        final newSettings = await messaging.requestPermission();
        return newSettings.authorizationStatus == AuthorizationStatus.authorized ||
               newSettings.authorizationStatus == AuthorizationStatus.provisional;
      }
      
      return true;
    } catch (e) {
      logger.e('檢查通知權限失敗: $e');
      return false;
    }
  }

  /// 獲取權限狀態描述
  static String getPermissionStatusDescription(AuthorizationStatus status) {
    switch (status) {
      case AuthorizationStatus.authorized:
        return '已授權';
      case AuthorizationStatus.denied:
        return '已拒絕';
      case AuthorizationStatus.notDetermined:
        return '未決定';
      case AuthorizationStatus.provisional:
        return '臨時授權';
    }
  }

  /// 打開系統設定頁面
  static Future<bool> openSystemSettings() async {
    try {
      if (LocalNotificationService.canOpenNotificationSettings) {
        return await LocalNotificationService.openNotificationSettings();
      } else {
        logger.w('當前平台不支援打開系統設定');
        return false;
      }
    } catch (e) {
      logger.e('打開系統設定失敗: $e');
      return false;
    }
  }
}

/// 通知統計管理器
class NotificationStatsManager {
  /// 記錄通知統計
  static Future<void> recordNotificationStats({
    required String notificationId,
    required String action, // 'received', 'opened', 'dismissed'
    Map<String, dynamic>? metadata,
  }) async {
    try {
      // 這裡可以實現通知統計記錄
      // 例如記錄到 Firebase Analytics 或本地數據庫
      
      logger.i('記錄通知統計: $notificationId - $action');
      
      // 實際實現可能包括：
      // - 記錄通知接收率
      // - 記錄通知點擊率
      // - 記錄用戶行為模式
      // - 優化通知發送策略
      
    } catch (e) {
      logger.e('記錄通知統計失敗: $e');
    }
  }

  /// 獲取通知統計數據
  static Future<Map<String, dynamic>> getNotificationStats() async {
    try {
      // 返回通知統計數據
      return {
        'totalReceived': 0,
        'totalOpened': 0,
        'openRate': 0.0,
        'lastUpdated': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      logger.e('獲取通知統計失敗: $e');
      return {};
    }
  }
}
