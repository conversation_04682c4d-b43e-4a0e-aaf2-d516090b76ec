import 'package:cloud_firestore/cloud_firestore.dart';

import '../../../astreal.dart';
import '../../../core/constants/firebase_collections.dart';
import '../../models/admin/system_announcement.dart';
import '../../models/notification/notification_model.dart';

/// 通知管理服務
/// 用於管理官方公告和系統通知的發送
class NotificationManagementService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// 發送系統公告通知
  static Future<bool> sendSystemAnnouncementNotification({
    required SystemAnnouncement announcement,
    List<String>? targetUserIds, // null 表示發送給所有用戶
  }) async {
    try {
      logger.i('發送系統公告通知: ${announcement.title}');

      final notification = NotificationModel(
        id: 'announcement_${announcement.id}_${DateTime.now().millisecondsSinceEpoch}',
        title: announcement.title,
        body: announcement.content.length > 100 
            ? '${announcement.content.substring(0, 100)}...'
            : announcement.content,
        type: NotificationType.systemAnnouncement,
        priority: _getNotificationPriority(announcement.priority),
        createdAt: DateTime.now(),
        data: {
          'announcementId': announcement.id,
          'announcementType': announcement.type.name,
          'priority': announcement.priority.name,
        },
        actionUrl: '/system-announcement/${announcement.id}',
      );

      if (targetUserIds == null) {
        // 發送給所有用戶
        return await _sendGlobalNotification(notification);
      } else {
        // 發送給特定用戶
        return await _sendNotificationToUsers(notification, targetUserIds);
      }
    } catch (e) {
      logger.e('發送系統公告通知失敗: $e');
      return false;
    }
  }

  /// 發送功能更新通知
  static Future<bool> sendFeatureUpdateNotification({
    required String title,
    required String description,
    required String version,
    List<String>? targetUserIds,
  }) async {
    try {
      logger.i('發送功能更新通知: $title');

      final notification = NotificationModel(
        id: 'feature_update_${DateTime.now().millisecondsSinceEpoch}',
        title: title,
        body: description,
        type: NotificationType.featureUpdate,
        priority: NotificationPriority.normal,
        createdAt: DateTime.now(),
        data: {
          'version': version,
          'updateType': 'feature',
        },
        actionUrl: '/feature-update/$version',
      );

      if (targetUserIds == null) {
        return await _sendGlobalNotification(notification);
      } else {
        return await _sendNotificationToUsers(notification, targetUserIds);
      }
    } catch (e) {
      logger.e('發送功能更新通知失敗: $e');
      return false;
    }
  }

  /// 發送促銷活動通知
  static Future<bool> sendPromotionNotification({
    required String title,
    required String description,
    required String promotionId,
    List<String>? targetUserIds,
  }) async {
    try {
      logger.i('發送促銷活動通知: $title');

      final notification = NotificationModel(
        id: 'promotion_${promotionId}_${DateTime.now().millisecondsSinceEpoch}',
        title: title,
        body: description,
        type: NotificationType.promotion,
        priority: NotificationPriority.high,
        createdAt: DateTime.now(),
        data: {
          'promotionId': promotionId,
        },
        actionUrl: '/promotion/$promotionId',
      );

      if (targetUserIds == null) {
        return await _sendGlobalNotification(notification);
      } else {
        return await _sendNotificationToUsers(notification, targetUserIds);
      }
    } catch (e) {
      logger.e('發送促銷活動通知失敗: $e');
      return false;
    }
  }

  /// 發送占星事件通知
  static Future<bool> sendAstroEventNotification({
    required String title,
    required String description,
    required DateTime eventDate,
    List<String>? targetUserIds,
  }) async {
    try {
      logger.i('發送占星事件通知: $title');

      final notification = NotificationModel(
        id: 'astro_event_${eventDate.millisecondsSinceEpoch}',
        title: title,
        body: description,
        type: NotificationType.astroEvent,
        priority: NotificationPriority.normal,
        createdAt: DateTime.now(),
        scheduledAt: eventDate,
        data: {
          'eventDate': eventDate.toIso8601String(),
          'eventType': 'astro',
        },
        actionUrl: '/astro-event/${eventDate.millisecondsSinceEpoch}',
      );

      if (targetUserIds == null) {
        return await _sendGlobalNotification(notification);
      } else {
        return await _sendNotificationToUsers(notification, targetUserIds);
      }
    } catch (e) {
      logger.e('發送占星事件通知失敗: $e');
      return false;
    }
  }

  /// 發送個人提醒通知
  static Future<bool> sendPersonalReminderNotification({
    required String userId,
    required String title,
    required String description,
    DateTime? scheduledAt,
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      logger.i('發送個人提醒通知: $title');

      final notification = NotificationModel(
        id: 'reminder_${userId}_${DateTime.now().millisecondsSinceEpoch}',
        title: title,
        body: description,
        type: NotificationType.personalReminder,
        priority: NotificationPriority.normal,
        createdAt: DateTime.now(),
        scheduledAt: scheduledAt,
        userId: userId,
        data: additionalData,
        actionUrl: '/personal-reminder',
      );

      return await _sendNotificationToUsers(notification, [userId]);
    } catch (e) {
      logger.e('發送個人提醒通知失敗: $e');
      return false;
    }
  }

  /// 發送全域通知
  static Future<bool> _sendGlobalNotification(NotificationModel notification) async {
    try {
      // 保存到全域通知集合
      await _firestore
          .collection('global_notifications')
          .doc(notification.id)
          .set(notification.toJson());

      // 這裡可以整合 Firebase Cloud Messaging 發送推播通知
      // 由於需要 FCM 服務器密鑰，實際實現可能需要後端支援

      logger.i('全域通知已保存: ${notification.id}');
      return true;
    } catch (e) {
      logger.e('發送全域通知失敗: $e');
      return false;
    }
  }

  /// 發送通知給特定用戶
  static Future<bool> _sendNotificationToUsers(
    NotificationModel notification,
    List<String> userIds,
  ) async {
    try {
      final batch = _firestore.batch();

      for (final userId in userIds) {
        final userNotificationRef = _firestore
            .collection(FirebaseCollections.userNotifications)
            .doc(userId)
            .collection('notifications')
            .doc(notification.id);

        batch.set(userNotificationRef, notification.toJson());
      }

      await batch.commit();

      logger.i('通知已發送給 ${userIds.length} 個用戶: ${notification.id}');
      return true;
    } catch (e) {
      logger.e('發送用戶通知失敗: $e');
      return false;
    }
  }

  /// 獲取通知優先級
  static NotificationPriority _getNotificationPriority(AnnouncementPriority priority) {
    switch (priority) {
      case AnnouncementPriority.low:
        return NotificationPriority.low;
      case AnnouncementPriority.normal:
        return NotificationPriority.normal;
      case AnnouncementPriority.high:
        return NotificationPriority.high;
      case AnnouncementPriority.urgent:
        return NotificationPriority.urgent;
    }
  }

  /// 獲取所有用戶的 FCM Token（用於推播通知）
  static Future<List<String>> getAllUserFCMTokens() async {
    try {
      final querySnapshot = await _firestore
          .collection(FirebaseCollections.userProfiles)
          .where('fcmToken', isNotEqualTo: null)
          .get();

      final tokens = <String>[];
      for (final doc in querySnapshot.docs) {
        final data = doc.data();
        final token = data['fcmToken'] as String?;
        if (token != null && token.isNotEmpty) {
          tokens.add(token);
        }
      }

      logger.i('獲取到 ${tokens.length} 個 FCM Token');
      return tokens;
    } catch (e) {
      logger.e('獲取 FCM Token 失敗: $e');
      return [];
    }
  }

  /// 獲取特定用戶的 FCM Token
  static Future<List<String>> getUserFCMTokens(List<String> userIds) async {
    try {
      final tokens = <String>[];

      for (final userId in userIds) {
        final doc = await _firestore
            .collection(FirebaseCollections.userProfiles)
            .doc(userId)
            .get();

        if (doc.exists) {
          final data = doc.data();
          final token = data?['fcmToken'] as String?;
          if (token != null && token.isNotEmpty) {
            tokens.add(token);
          }
        }
      }

      logger.i('獲取到 ${tokens.length} 個用戶 FCM Token');
      return tokens;
    } catch (e) {
      logger.e('獲取用戶 FCM Token 失敗: $e');
      return [];
    }
  }

  /// 清理過期通知
  static Future<void> cleanupExpiredNotifications({
    int daysToKeep = 30,
  }) async {
    try {
      final cutoffDate = DateTime.now().subtract(Duration(days: daysToKeep));
      
      // 清理全域通知
      final globalQuery = await _firestore
          .collection('global_notifications')
          .where('createdAt', isLessThan: cutoffDate.toIso8601String())
          .get();

      final batch = _firestore.batch();
      for (final doc in globalQuery.docs) {
        batch.delete(doc.reference);
      }

      await batch.commit();

      logger.i('已清理 ${globalQuery.docs.length} 個過期的全域通知');
    } catch (e) {
      logger.e('清理過期通知失敗: $e');
    }
  }

  /// 獲取通知統計
  static Future<Map<String, dynamic>> getNotificationStats() async {
    try {
      // 獲取全域通知數量
      final globalCount = await _firestore
          .collection('global_notifications')
          .count()
          .get();

      // 獲取今日發送的通知數量
      final today = DateTime.now();
      final todayStart = DateTime(today.year, today.month, today.day);
      
      final todayCount = await _firestore
          .collection('global_notifications')
          .where('createdAt', isGreaterThanOrEqualTo: todayStart.toIso8601String())
          .count()
          .get();

      return {
        'totalNotifications': globalCount.count ?? 0,
        'todayNotifications': todayCount.count ?? 0,
        'lastUpdated': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      logger.e('獲取通知統計失敗: $e');
      return {
        'totalNotifications': 0,
        'todayNotifications': 0,
        'lastUpdated': DateTime.now().toIso8601String(),
      };
    }
  }
}
