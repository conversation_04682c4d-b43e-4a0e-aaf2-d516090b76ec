import 'dart:convert';
import 'dart:io';

import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:timezone/timezone.dart' as tz;

import '../../../astreal.dart';
import '../../../presentation/pages/daily_astrology_page.dart';
import '../../../shared/services/navigation_service.dart';
import '../../../shared/utils/user_preferences.dart';
import '../../models/astrology/daily_astrology.dart';
import '../api/firebase_auth_service.dart';
import '../astrology/daily_astrology_service.dart';

/// 每日星相推播服務
class DailyAstrologyNotificationService {
  static const int _dailyNotificationId = 1001;
  static const String _channelId = 'daily_astrology';
  static const String _channelName = '每日星相';
  static const String _channelDescription = '每日星相提醒和個人化占星資訊';

  static FlutterLocalNotificationsPlugin? _notificationsPlugin;

  /// 初始化推播服務
  static Future<void> initialize() async {
    try {
      _notificationsPlugin = FlutterLocalNotificationsPlugin();

      // Android 設定
      const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
      
      // iOS 設定
      const iosSettings = DarwinInitializationSettings(
        requestAlertPermission: true,
        requestBadgePermission: true,
        requestSoundPermission: true,
        requestCriticalPermission: false,
        requestProvisionalPermission: false,
      );

      const initSettings = InitializationSettings(
        android: androidSettings,
        iOS: iosSettings,
      );

      await _notificationsPlugin!.initialize(
        initSettings,
        onDidReceiveNotificationResponse: _onNotificationTapped,
      );

      // 創建通知頻道（Android）
      if (Platform.isAndroid) {
        await _createNotificationChannel();
      }

      logger.i('每日星相推播服務初始化完成');
    } catch (e) {
      logger.e('初始化每日星相推播服務失敗: $e');
    }
  }

  /// 創建通知頻道
  static Future<void> _createNotificationChannel() async {
    try {
      const androidChannel = AndroidNotificationChannel(
        _channelId,
        _channelName,
        description: _channelDescription,
        importance: Importance.high,
        playSound: true,
        enableVibration: true,
      );

      final androidPlugin = _notificationsPlugin!.resolvePlatformSpecificImplementation<
          AndroidFlutterLocalNotificationsPlugin>();

      await androidPlugin?.createNotificationChannel(androidChannel);
      logger.d('創建 Android 通知頻道成功');
    } catch (e) {
      logger.e('創建 Android 通知頻道失敗: $e');
    }
  }

  /// 請求推播權限
  static Future<bool> requestPermissions() async {
    try {
      if (_notificationsPlugin == null) {
        await initialize();
      }

      if (Platform.isAndroid) {
        final androidPlugin = _notificationsPlugin!.resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>();
        
        final granted = await androidPlugin?.requestNotificationsPermission();
        logger.d('Android 推播權限請求結果: $granted');
        return granted ?? false;
      }

      if (Platform.isIOS) {
        final iosPlugin = _notificationsPlugin!.resolvePlatformSpecificImplementation<
            IOSFlutterLocalNotificationsPlugin>();

        final granted = await iosPlugin?.requestPermissions(
          alert: true,
          badge: true,
          sound: true,
          critical: false,
          provisional: false,
        );
        logger.d('iOS 推播權限請求結果: $granted');
        return granted ?? false;
      }

      return true;
    } catch (e) {
      logger.e('請求推播權限失敗: $e');
      return false;
    }
  }

  /// 排程每日星相推播
  static Future<void> scheduleDailyNotification() async {
    try {
      if (_notificationsPlugin == null) {
        await initialize();
      }

      // 檢查是否啟用推播
      final isEnabled = await UserPreferences.getDailyAstrologyNotificationEnabled();
      if (!isEnabled) {
        logger.d('每日星相推播已停用');
        return;
      }

      // 取消現有的推播
      await cancelDailyNotification();

      // 獲取推播時間設定
      final timeString = await UserPreferences.getDailyAstrologyNotificationTime();
      final timeParts = timeString.split(':');
      final hour = int.parse(timeParts[0]);
      final minute = int.parse(timeParts[1]);

      // 計算下次推播時間
      final now = DateTime.now();
      var scheduledDate = DateTime(now.year, now.month, now.day, hour, minute);
      
      // 如果今天的時間已過，排程到明天
      if (scheduledDate.isBefore(now)) {
        scheduledDate = scheduledDate.add(const Duration(days: 1));
      }

      // 獲取今日星相資料
      final dailyAstrology = await DailyAstrologyService.getTodayAstrology();
      
      // 生成推播內容
      final notificationContent = await _generateNotificationContent(dailyAstrology);

      // 排程推播
      await _notificationsPlugin!.zonedSchedule(
        _dailyNotificationId,
        notificationContent['title']!,
        notificationContent['body']!,
        tz.TZDateTime.from(scheduledDate, tz.local),
        const NotificationDetails(
          android: AndroidNotificationDetails(
            _channelId,
            _channelName,
            channelDescription: _channelDescription,
            importance: Importance.high,
            priority: Priority.high,
            icon: '@mipmap/ic_launcher',
            largeIcon: DrawableResourceAndroidBitmap('@mipmap/ic_launcher'),
            styleInformation: BigTextStyleInformation(''),
          ),
          iOS: DarwinNotificationDetails(
            presentAlert: true,
            presentBadge: true,
            presentSound: true,
          ),
        ),
        androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
        uiLocalNotificationDateInterpretation: UILocalNotificationDateInterpretation.absoluteTime,
        matchDateTimeComponents: DateTimeComponents.time, // 每天重複
        payload: jsonEncode({
          'type': 'daily_astrology',
          'date': DateTime.now().toIso8601String(),
        }),
      );

      logger.i('每日星相推播已排程: ${scheduledDate.toString()}');
    } catch (e) {
      logger.e('排程每日星相推播失敗: $e');
    }
  }

  /// 取消每日星相推播
  static Future<void> cancelDailyNotification() async {
    try {
      if (_notificationsPlugin == null) {
        await initialize();
      }

      await _notificationsPlugin!.cancel(_dailyNotificationId);
      logger.d('取消每日星相推播');
    } catch (e) {
      logger.e('取消每日星相推播失敗: $e');
    }
  }

  /// 立即發送測試推播
  static Future<void> sendTestNotification() async {
    try {
      if (_notificationsPlugin == null) {
        await initialize();
      }

      // 獲取今日星相資料
      final dailyAstrology = await DailyAstrologyService.getTodayAstrology();
      
      // 生成推播內容
      final notificationContent = await _generateNotificationContent(dailyAstrology);

      await _notificationsPlugin!.show(
        999, // 測試推播使用不同的 ID
        '${notificationContent['title']} (測試)',
        notificationContent['body'],
        const NotificationDetails(
          android: AndroidNotificationDetails(
            _channelId,
            _channelName,
            channelDescription: _channelDescription,
            importance: Importance.high,
            priority: Priority.high,
            icon: '@mipmap/ic_launcher',
          ),
          iOS: DarwinNotificationDetails(
            presentAlert: true,
            presentBadge: true,
            presentSound: true,
          ),
        ),
        payload: jsonEncode({
          'type': 'daily_astrology_test',
          'date': DateTime.now().toIso8601String(),
        }),
      );

      logger.i('測試推播已發送');
    } catch (e) {
      logger.e('發送測試推播失敗: $e');
    }
  }

  /// 生成推播內容
  static Future<Map<String, String>> _generateNotificationContent(
    DailyAstrologyData? dailyAstrology,
  ) async {
    try {
      // 檢查是否啟用個人化
      final isPersonalized = await UserPreferences.getDailyAstrologyPersonalizedEnabled();
      final currentUser = FirebaseAuthService.getCurrentUser();

      if (isPersonalized && currentUser != null) {
        // 生成個人化內容
        final personalizedAstrology = await DailyAstrologyService.getPersonalizedDailyAstrology(
          currentUser.uid,
          DateTime.now(),
        );

        if (personalizedAstrology != null) {
          return {
            'title': '你的今日星象',
            'body': personalizedAstrology.personalizedMessage,
          };
        }
      }

      // 生成一般內容
      if (dailyAstrology != null && dailyAstrology.events.isNotEmpty) {
        final mostImportantEvent = dailyAstrology.mostImportantEvent!;
        
        return {
          'title': '每日星象',
          'body': '${mostImportantEvent.title}：${mostImportantEvent.description}',
        };
      }

      // 預設內容
      return {
        'title': '每日星象',
        'body': '今天是平靜的一天，適合內省和休息。保持開放的心態迎接宇宙的能量。',
      };
    } catch (e) {
      logger.e('生成推播內容失敗: $e');
      return {
        'title': '每日星象',
        'body': '今天是特別的一天，記得關注內在的聲音和直覺的指引。',
      };
    }
  }

  /// 處理推播點擊事件
  static void _onNotificationTapped(NotificationResponse response) {
    try {
      logger.d('推播被點擊: ${response.payload}');
      
      if (response.payload != null) {
        final payload = jsonDecode(response.payload!);
        final type = payload['type'] as String?;
        
        if (type == 'daily_astrology' || type == 'daily_astrology_test') {
          // 導航到每日星相頁面
          _navigateToDailyAstrologyPage();
        }
      }
    } catch (e) {
      logger.e('處理推播點擊事件失敗: $e');
    }
  }

  /// 導航到每日星相頁面
  static void _navigateToDailyAstrologyPage() {
    try {
      NavigationService.navigateTo(const DailyAstrologyPage());
      logger.d('成功導航到每日星相頁面');
    } catch (e) {
      logger.e('導航到每日星相頁面失敗: $e');
    }
  }

  /// 檢查推播權限狀態
  static Future<bool> checkPermissionStatus() async {
    try {
      if (_notificationsPlugin == null) {
        await initialize();
      }

      if (Platform.isAndroid) {
        final androidPlugin = _notificationsPlugin!.resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>();

        final granted = await androidPlugin?.areNotificationsEnabled();
        return granted ?? false;
      }

      if (Platform.isIOS) {
        final iosPlugin = _notificationsPlugin!.resolvePlatformSpecificImplementation<
            IOSFlutterLocalNotificationsPlugin>();

        final settings = await iosPlugin?.checkPermissions();
        return settings?.isEnabled ?? false;
      }

      return true;
    } catch (e) {
      logger.e('檢查推播權限狀態失敗: $e');
      return false;
    }
  }

  /// 更新推播設定
  static Future<void> updateNotificationSettings() async {
    try {
      final isEnabled = await UserPreferences.getDailyAstrologyNotificationEnabled();
      
      if (isEnabled) {
        // 重新排程推播
        await scheduleDailyNotification();
      } else {
        // 取消推播
        await cancelDailyNotification();
      }
      
      logger.d('推播設定已更新: $isEnabled');
    } catch (e) {
      logger.e('更新推播設定失敗: $e');
    }
  }
}
