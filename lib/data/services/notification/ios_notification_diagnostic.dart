import 'dart:io';

import 'package:firebase_messaging/firebase_messaging.dart';

import '../../../astreal.dart';

/// iOS 通知診斷工具
/// 專門用於診斷 iOS 平台的推播通知問題
class IOSNotificationDiagnostic {
  
  /// 執行 iOS 通知診斷
  static Future<Map<String, dynamic>> runIOSDiagnostic() async {
    if (!Platform.isIOS) {
      return {
        'error': '此診斷工具僅適用於 iOS 平台',
        'platform': Platform.operatingSystem,
      };
    }
    
    final results = <String, dynamic>{};
    
    try {
      logger.i('開始 iOS 通知診斷...');
      
      // 1. 檢查 Firebase Messaging 初始化
      results['firebaseMessaging'] = await _checkFirebaseMessaging();
      
      // 2. 檢查通知權限
      results['permissions'] = await _checkNotificationPermissions();
      
      // 3. 檢查 APNs Token
      results['apnsToken'] = await _checkAPNsToken();
      
      // 4. 檢查 FCM Token
      results['fcmToken'] = await _checkFCMToken();
      
      // 5. 檢查背景模式配置
      results['backgroundModes'] = _checkBackgroundModes();
      
      // 6. 生成診斷報告
      results['summary'] = _generateIOSSummary(results);
      
      logger.i('iOS 通知診斷完成');
      return results;
    } catch (e) {
      logger.e('iOS 通知診斷失敗: $e');
      results['error'] = e.toString();
      return results;
    }
  }
  
  /// 檢查 Firebase Messaging
  static Future<Map<String, dynamic>> _checkFirebaseMessaging() async {
    try {
      final messaging = FirebaseMessaging.instance;
      
      return {
        'isAvailable': messaging != null,
        'status': 'initialized',
      };
    } catch (e) {
      return {
        'isAvailable': false,
        'error': e.toString(),
      };
    }
  }
  
  /// 檢查通知權限
  static Future<Map<String, dynamic>> _checkNotificationPermissions() async {
    try {
      final messaging = FirebaseMessaging.instance;
      final settings = await messaging.getNotificationSettings();
      
      return {
        'authorizationStatus': settings.authorizationStatus.toString(),
        'isAuthorized': settings.authorizationStatus == AuthorizationStatus.authorized ||
                       settings.authorizationStatus == AuthorizationStatus.provisional,
        'alert': settings.alert.toString(),
        'badge': settings.badge.toString(),
        'sound': settings.sound.toString(),
        'announcement': settings.announcement.toString(),
        'carPlay': settings.carPlay.toString(),
        'criticalAlert': settings.criticalAlert.toString(),
        // 'provisional' 屬性在某些版本中可能不存在
        // 'timeSensitive' 屬性在某些版本中可能不存在
      };
    } catch (e) {
      return {
        'error': e.toString(),
      };
    }
  }
  
  /// 檢查 APNs Token
  static Future<Map<String, dynamic>> _checkAPNsToken() async {
    try {
      final messaging = FirebaseMessaging.instance;
      
      // 嘗試獲取 APNs Token
      String? apnsToken;
      for (int i = 0; i < 5; i++) {
        apnsToken = await messaging.getAPNSToken();
        if (apnsToken != null) break;
        await Future.delayed(const Duration(seconds: 1));
      }
      
      return {
        'hasToken': apnsToken != null,
        'tokenPreview': apnsToken != null ? '${apnsToken.substring(0, 20)}...' : null,
        'tokenLength': apnsToken?.length ?? 0,
        'attempts': 5,
      };
    } catch (e) {
      return {
        'hasToken': false,
        'error': e.toString(),
      };
    }
  }
  
  /// 檢查 FCM Token
  static Future<Map<String, dynamic>> _checkFCMToken() async {
    try {
      final messaging = FirebaseMessaging.instance;
      final token = await messaging.getToken();
      
      return {
        'hasToken': token != null,
        'tokenPreview': token != null ? '${token.substring(0, 20)}...' : null,
        'tokenLength': token?.length ?? 0,
      };
    } catch (e) {
      return {
        'hasToken': false,
        'error': e.toString(),
      };
    }
  }
  
  /// 檢查背景模式配置
  static Map<String, dynamic> _checkBackgroundModes() {
    // 這裡無法直接檢查 Info.plist 配置
    // 只能提供配置建議
    return {
      'configured': 'unknown',
      'requiredModes': ['remote-notification'],
      'note': '需要在 Info.plist 中配置 UIBackgroundModes',
    };
  }
  
  /// 生成 iOS 診斷摘要
  static Map<String, dynamic> _generateIOSSummary(Map<String, dynamic> results) {
    final issues = <String>[];
    final recommendations = <String>[];
    
    // 檢查 Firebase Messaging
    final firebaseMessaging = results['firebaseMessaging'] as Map<String, dynamic>?;
    if (firebaseMessaging?['isAvailable'] != true) {
      issues.add('Firebase Messaging 未正確初始化');
      recommendations.add('檢查 Firebase 配置和初始化');
    }
    
    // 檢查權限
    final permissions = results['permissions'] as Map<String, dynamic>?;
    if (permissions?['isAuthorized'] != true) {
      issues.add('通知權限未授權');
      recommendations.add('請求用戶授權通知權限');
    }
    
    // 檢查 APNs Token
    final apnsToken = results['apnsToken'] as Map<String, dynamic>?;
    if (apnsToken?['hasToken'] != true) {
      issues.add('APNs Token 獲取失敗');
      recommendations.add('檢查 APNs 證書配置和網路連接');
    }
    
    // 檢查 FCM Token
    final fcmToken = results['fcmToken'] as Map<String, dynamic>?;
    if (fcmToken?['hasToken'] != true) {
      issues.add('FCM Token 獲取失敗');
      recommendations.add('確保 APNs Token 已獲取後再獲取 FCM Token');
    }
    
    return {
      'overallStatus': issues.isEmpty ? 'healthy' : 'issues_found',
      'issuesCount': issues.length,
      'issues': issues,
      'recommendations': recommendations,
      'criticalIssues': _identifyCriticalIssues(results),
    };
  }
  
  /// 識別關鍵問題
  static List<String> _identifyCriticalIssues(Map<String, dynamic> results) {
    final criticalIssues = <String>[];
    
    final permissions = results['permissions'] as Map<String, dynamic>?;
    final apnsToken = results['apnsToken'] as Map<String, dynamic>?;
    final fcmToken = results['fcmToken'] as Map<String, dynamic>?;
    
    if (permissions?['authorizationStatus']?.toString().contains('denied') == true) {
      criticalIssues.add('用戶拒絕了通知權限，需要引導用戶到設定中開啟');
    }
    
    if (apnsToken?['hasToken'] != true) {
      criticalIssues.add('APNs Token 獲取失敗，這是 iOS 推播通知的前提條件');
    }
    
    if (fcmToken?['hasToken'] != true && apnsToken?['hasToken'] == true) {
      criticalIssues.add('APNs Token 已獲取但 FCM Token 獲取失敗，可能是 Firebase 配置問題');
    }
    
    return criticalIssues;
  }
  
  /// 嘗試修復 iOS 通知問題
  static Future<Map<String, dynamic>> attemptIOSFix() async {
    if (!Platform.isIOS) {
      return {
        'success': false,
        'error': '此修復工具僅適用於 iOS 平台',
      };
    }
    
    final fixes = <String, dynamic>{};
    
    try {
      logger.i('嘗試修復 iOS 通知問題...');
      
      // 1. 重新請求權限
      fixes['permissions'] = await _fixIOSPermissions();
      
      // 2. 重新獲取 APNs Token
      fixes['apnsToken'] = await _fixAPNsToken();
      
      // 3. 重新獲取 FCM Token
      fixes['fcmToken'] = await _fixFCMToken();
      
      logger.i('iOS 通知修復完成');
      return fixes;
    } catch (e) {
      logger.e('iOS 通知修復失敗: $e');
      fixes['error'] = e.toString();
      return fixes;
    }
  }
  
  /// 修復 iOS 權限
  static Future<Map<String, dynamic>> _fixIOSPermissions() async {
    try {
      final messaging = FirebaseMessaging.instance;
      final settings = await messaging.requestPermission(
        alert: true,
        badge: true,
        sound: true,
        announcement: false,
        carPlay: false,
        criticalAlert: false,
        // provisional: false, // 某些版本可能不支援此參數
      );
      
      return {
        'success': settings.authorizationStatus == AuthorizationStatus.authorized ||
                  settings.authorizationStatus == AuthorizationStatus.provisional,
        'status': settings.authorizationStatus.toString(),
      };
    } catch (e) {
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }
  
  /// 修復 APNs Token
  static Future<Map<String, dynamic>> _fixAPNsToken() async {
    try {
      final messaging = FirebaseMessaging.instance;
      
      // 等待 APNs Token，最多等待 10 秒
      String? apnsToken;
      for (int i = 0; i < 10; i++) {
        apnsToken = await messaging.getAPNSToken();
        if (apnsToken != null) break;
        await Future.delayed(const Duration(seconds: 1));
      }
      
      return {
        'success': apnsToken != null,
        'hasToken': apnsToken != null,
        'tokenPreview': apnsToken != null ? '${apnsToken.substring(0, 20)}...' : null,
      };
    } catch (e) {
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }
  
  /// 修復 FCM Token
  static Future<Map<String, dynamic>> _fixFCMToken() async {
    try {
      final messaging = FirebaseMessaging.instance;
      
      // 刪除舊 Token
      await messaging.deleteToken();
      
      // 等待一下再獲取新 Token
      await Future.delayed(const Duration(seconds: 2));
      
      // 獲取新 Token
      final token = await messaging.getToken();
      
      return {
        'success': token != null,
        'hasToken': token != null,
        'tokenPreview': token != null ? '${token.substring(0, 20)}...' : null,
      };
    } catch (e) {
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }
}
