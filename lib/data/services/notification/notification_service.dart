import 'dart:convert';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../astreal.dart';
import '../../../core/constants/firebase_collections.dart';
import '../../../core/services/navigation_service.dart';
import '../../models/notification/notification_model.dart';
import 'firebase_notification_service.dart';
import 'local_notification_service.dart';

/// 核心通知服務
/// 整合 Firebase 推播通知和本地通知功能
class NotificationService {
  static bool _isInitialized = false;
  static final List<NotificationModel> _cachedNotifications = [];
  static Function(NotificationModel)? _onNotificationReceived;
  static Function(String)? _onNotificationTapped;

  /// 初始化通知服務
  static Future<void> initialize({
    Function(NotificationModel)? onNotificationReceived,
    Function(String)? onNotificationTapped,
  }) async {
    if (_isInitialized) return;

    try {
      logger.i('初始化通知服務...');
      
      _onNotificationReceived = onNotificationReceived;
      _onNotificationTapped = onNotificationTapped;

      // 初始化本地通知服務
      await LocalNotificationService.initialize(
        onNotificationTapped: _handleNotificationTapped,
      );

      // 初始化 Firebase 通知服務
      await FirebaseNotificationService.initialize(
        onNotificationReceived: _handleNotificationReceived,
        onNotificationOpened: _handleNotificationOpened,
      );

      // 載入本地快取的通知
      await _loadCachedNotifications();

      _isInitialized = true;
      logger.i('通知服務初始化成功');
    } catch (e) {
      logger.e('通知服務初始化失敗: $e');
      _isInitialized = false;
    }
  }

  /// 處理收到的通知
  static void _handleNotificationReceived(NotificationModel notification) {
    logger.i('收到新通知: ${notification.title}');
    
    // 添加到快取
    _addNotificationToCache(notification);
    
    // 顯示本地通知（如果應用在前景）
    LocalNotificationService.showNotification(notification);
    
    // 觸發回調
    _onNotificationReceived?.call(notification);
  }

  /// 處理通知被點擊
  static void _handleNotificationOpened(NotificationModel notification) {
    logger.i('通知被點擊: ${notification.title}');

    // 標記為已讀
    markNotificationAsRead(notification.id);

    // 使用導航服務處理通知導航
    NavigationService.handleNotificationNavigation(
      notificationId: notification.id,
      actionUrl: notification.actionUrl,
      data: notification.data,
    );

    // 觸發回調
    _onNotificationTapped?.call(notification.id);
  }

  /// 處理本地通知被點擊
  static void _handleNotificationTapped(String notificationId) {
    logger.i('本地通知被點擊: $notificationId');

    // 使用導航服務處理通知導航
    NavigationService.navigateToNotificationCenter(notificationId: notificationId);

    _onNotificationTapped?.call(notificationId);
  }

  /// 添加通知到快取
  static void _addNotificationToCache(NotificationModel notification) {
    // 檢查是否已存在
    final existingIndex = _cachedNotifications.indexWhere((n) => n.id == notification.id);
    
    if (existingIndex != -1) {
      // 更新現有通知
      _cachedNotifications[existingIndex] = notification;
    } else {
      // 添加新通知到開頭
      _cachedNotifications.insert(0, notification);
      
      // 限制快取數量
      if (_cachedNotifications.length > 100) {
        _cachedNotifications.removeRange(100, _cachedNotifications.length);
      }
    }
    
    // 保存到本地存儲
    _saveCachedNotifications();
  }

  /// 載入本地快取的通知
  static Future<void> _loadCachedNotifications() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cachedJson = prefs.getString('cached_notifications');
      
      if (cachedJson != null) {
        final List<dynamic> notificationsList = json.decode(cachedJson);
        _cachedNotifications.clear();
        
        for (final notificationData in notificationsList) {
          try {
            final notification = NotificationModel.fromJson(notificationData);
            _cachedNotifications.add(notification);
          } catch (e) {
            logger.w('解析快取通知失敗: $e');
          }
        }
        
        logger.i('載入 ${_cachedNotifications.length} 個快取通知');
      }
    } catch (e) {
      logger.e('載入快取通知失敗: $e');
    }
  }

  /// 保存快取通知到本地存儲
  static Future<void> _saveCachedNotifications() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final notificationsJson = _cachedNotifications.map((n) => n.toJson()).toList();
      await prefs.setString('cached_notifications', json.encode(notificationsJson));
    } catch (e) {
      logger.e('保存快取通知失敗: $e');
    }
  }

  /// 獲取所有通知（結合快取和遠端）
  static Future<List<NotificationModel>> getAllNotifications({
    bool forceRefresh = false,
  }) async {
    try {
      if (forceRefresh || _cachedNotifications.isEmpty) {
        // 從 Firebase 獲取最新通知
        final firebaseNotifications = await FirebaseNotificationService.getUserNotifications();
        
        // 合併到快取中
        for (final notification in firebaseNotifications) {
          _addNotificationToCache(notification);
        }
      }
      
      // 按時間排序（最新的在前面）
      _cachedNotifications.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      
      return List.from(_cachedNotifications);
    } catch (e) {
      logger.e('獲取所有通知失敗: $e');
      return List.from(_cachedNotifications);
    }
  }

  /// 獲取未讀通知數量
  static Future<int> getUnreadNotificationCount() async {
    try {
      // 優先從 Firebase 獲取準確數量
      final firebaseCount = await FirebaseNotificationService.getUnreadNotificationCount();
      if (firebaseCount > 0) {
        return firebaseCount;
      }
      
      // 備用：從快取計算
      return _cachedNotifications.where((n) => !n.isRead).length;
    } catch (e) {
      logger.e('獲取未讀通知數量失敗: $e');
      return _cachedNotifications.where((n) => !n.isRead).length;
    }
  }

  /// 標記通知為已讀
  static Future<void> markNotificationAsRead(String notificationId) async {
    try {
      // 更新快取
      final index = _cachedNotifications.indexWhere((n) => n.id == notificationId);
      if (index != -1) {
        _cachedNotifications[index] = _cachedNotifications[index].copyWith(isRead: true);
        _saveCachedNotifications();
      }
      
      // 更新 Firebase - 直接調用 Firestore 操作
      await _markNotificationAsReadInFirestore(notificationId);
      
      logger.i('通知已標記為已讀: $notificationId');
    } catch (e) {
      logger.e('標記通知為已讀失敗: $e');
    }
  }

  /// 標記所有通知為已讀
  static Future<void> markAllNotificationsAsRead() async {
    try {
      // 更新快取
      for (int i = 0; i < _cachedNotifications.length; i++) {
        _cachedNotifications[i] = _cachedNotifications[i].copyWith(isRead: true);
      }
      _saveCachedNotifications();
      
      // 更新 Firebase
      await FirebaseNotificationService.markAllNotificationsAsRead();
      
      logger.i('所有通知已標記為已讀');
    } catch (e) {
      logger.e('標記所有通知為已讀失敗: $e');
    }
  }

  /// 刪除通知
  static Future<void> deleteNotification(String notificationId) async {
    try {
      // 從快取中移除
      _cachedNotifications.removeWhere((n) => n.id == notificationId);
      _saveCachedNotifications();
      
      // 從 Firebase 刪除
      await FirebaseNotificationService.deleteNotification(notificationId);
      
      // 取消本地通知
      await LocalNotificationService.cancelNotification(notificationId);
      
      logger.i('通知已刪除: $notificationId');
    } catch (e) {
      logger.e('刪除通知失敗: $e');
    }
  }

  /// 清除所有通知
  static Future<void> clearAllNotifications() async {
    try {
      // 清除快取
      _cachedNotifications.clear();
      _saveCachedNotifications();
      
      // 取消所有本地通知
      await LocalNotificationService.cancelAllNotifications();
      
      logger.i('所有通知已清除');
    } catch (e) {
      logger.e('清除所有通知失敗: $e');
    }
  }

  /// 檢查通知權限
  static Future<bool> areNotificationsEnabled() async {
    return await LocalNotificationService.areNotificationsEnabled();
  }

  /// 檢查是否支援打開通知設定
  static bool get canOpenNotificationSettings {
    return LocalNotificationService.canOpenNotificationSettings;
  }

  /// 打開通知設定
  /// 返回 true 表示成功打開或引導用戶，false 表示不支援或失敗
  static Future<bool> openNotificationSettings() async {
    return await LocalNotificationService.openNotificationSettings();
  }

  /// 獲取 FCM Token
  static String? get fcmToken => FirebaseNotificationService.fcmToken;

  /// 檢查服務是否已初始化
  static bool get isInitialized => _isInitialized;

  /// 獲取快取的通知數量
  static int get cachedNotificationCount => _cachedNotifications.length;

  /// 標記通知為已讀（Firestore 操作）
  static Future<void> _markNotificationAsReadInFirestore(String notificationId) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) return;

      await FirebaseFirestore.instance
          .collection(FirebaseCollections.userNotifications)
          .doc(user.uid)
          .collection('notifications')
          .doc(notificationId)
          .update({'isRead': true});

      logger.i('通知已在 Firestore 中標記為已讀: $notificationId');
    } catch (e) {
      logger.e('在 Firestore 中標記通知為已讀失敗: $e');
    }
  }
}
