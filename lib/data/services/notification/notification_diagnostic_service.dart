import 'dart:io';

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';

import '../../../astreal.dart';
import 'firebase_notification_service.dart';
import 'local_notification_service.dart';
import 'notification_service.dart';

/// 通知診斷服務
/// 用於診斷和修復通知系統問題
class NotificationDiagnosticService {
  /// 執行完整的通知系統診斷
  static Future<Map<String, dynamic>> runFullDiagnostic() async {
    final results = <String, dynamic>{};
    
    try {
      logger.i('開始通知系統診斷...');
      
      // 1. 檢查平台支援
      results['platform'] = await _checkPlatformSupport();
      
      // 2. 檢查 Firebase 初始化
      results['firebase'] = await _checkFirebaseInitialization();
      
      // 3. 檢查通知權限
      results['permissions'] = await _checkNotificationPermissions();
      
      // 4. 檢查 FCM Token
      results['fcmToken'] = await _checkFCMToken();
      
      // 5. 檢查服務初始化狀態
      results['services'] = await _checkServicesStatus();
      
      // 6. 檢查本地通知功能
      results['localNotifications'] = await _checkLocalNotifications();
      
      // 7. 生成診斷報告
      results['summary'] = _generateDiagnosticSummary(results);
      
      logger.i('通知系統診斷完成');
      return results;
    } catch (e) {
      logger.e('通知系統診斷失敗: $e');
      results['error'] = e.toString();
      return results;
    }
  }

  /// 檢查平台支援
  static Future<Map<String, dynamic>> _checkPlatformSupport() async {
    bool isMobile = false;

    if (!kIsWeb) {
      try {
        isMobile = Platform.isAndroid || Platform.isIOS;
      } catch (e) {
        // Web 平台會拋出異常，isMobile 保持 false
        isMobile = false;
      }
    }

    return {
      'platform': _getPlatformName(),
      'isWeb': kIsWeb,
      'isMobile': isMobile,
      'supportsBackgroundNotifications': !kIsWeb,
    };
  }

  /// 檢查 Firebase 初始化
  static Future<Map<String, dynamic>> _checkFirebaseInitialization() async {
    try {
      // 檢查 Firebase Messaging 是否可用
      final messaging = FirebaseMessaging.instance;
      final isAvailable = messaging != null;
      
      return {
        'isInitialized': isAvailable,
        'error': null,
      };
    } catch (e) {
      return {
        'isInitialized': false,
        'error': e.toString(),
      };
    }
  }

  /// 檢查通知權限
  static Future<Map<String, dynamic>> _checkNotificationPermissions() async {
    try {
      final messaging = FirebaseMessaging.instance;
      final settings = await messaging.getNotificationSettings();
      
      // 檢查本地通知權限
      final localPermission = await LocalNotificationService.areNotificationsEnabled();
      
      return {
        'firebase': {
          'authorizationStatus': settings.authorizationStatus.toString(),
          'isAuthorized': settings.authorizationStatus == AuthorizationStatus.authorized ||
                         settings.authorizationStatus == AuthorizationStatus.provisional,
          'alert': settings.alert.toString(),
          'badge': settings.badge.toString(),
          'sound': settings.sound.toString(),
        },
        'local': {
          'isEnabled': localPermission,
        },
      };
    } catch (e) {
      return {
        'error': e.toString(),
      };
    }
  }

  /// 檢查 FCM Token
  static Future<Map<String, dynamic>> _checkFCMToken() async {
    try {
      final messaging = FirebaseMessaging.instance;
      final token = await messaging.getToken();

      // 檢查 APNs Token（僅 iOS）
      String? apnsToken;
      if (Platform.isIOS) {
        try {
          apnsToken = await messaging.getAPNSToken();
        } catch (e) {
          logger.w('獲取 APNs Token 失敗: $e');
        }
      }

      return {
        'hasToken': token != null,
        'tokenLength': token?.length ?? 0,
        'tokenPreview': token != null ? '${token.substring(0, 20)}...' : null,
        'cachedToken': FirebaseNotificationService.fcmToken,
        'apnsToken': apnsToken != null ? '${apnsToken.substring(0, 20)}...' : null,
        'hasApnsToken': apnsToken != null,
      };
    } catch (e) {
      return {
        'hasToken': false,
        'error': e.toString(),
      };
    }
  }

  /// 檢查服務初始化狀態
  static Future<Map<String, dynamic>> _checkServicesStatus() async {
    return {
      'notificationService': {
        'isInitialized': NotificationService.isInitialized,
        'cachedNotificationCount': NotificationService.cachedNotificationCount,
      },
      'firebaseNotificationService': {
        'isInitialized': FirebaseNotificationService.isInitialized,
      },
    };
  }

  /// 檢查本地通知功能
  static Future<Map<String, dynamic>> _checkLocalNotifications() async {
    try {
      // 獲取待處理的通知
      final pendingNotifications = await LocalNotificationService.getPendingNotifications();
      
      return {
        'pendingNotificationsCount': pendingNotifications.length,
        'canShowNotifications': await LocalNotificationService.areNotificationsEnabled(),
      };
    } catch (e) {
      return {
        'error': e.toString(),
      };
    }
  }

  /// 生成診斷摘要
  static Map<String, dynamic> _generateDiagnosticSummary(Map<String, dynamic> results) {
    final issues = <String>[];
    final recommendations = <String>[];
    
    // 檢查平台支援
    final platform = results['platform'] as Map<String, dynamic>?;
    if (platform?['isWeb'] == true) {
      issues.add('Web 平台對推播通知支援有限');
      recommendations.add('建議在移動設備上測試推播通知功能');
    }
    
    // 檢查 Firebase 初始化
    final firebase = results['firebase'] as Map<String, dynamic>?;
    if (firebase?['isInitialized'] != true) {
      issues.add('Firebase 未正確初始化');
      recommendations.add('檢查 Firebase 配置和網路連接');
    }
    
    // 檢查權限
    final permissions = results['permissions'] as Map<String, dynamic>?;
    final firebasePerms = permissions?['firebase'] as Map<String, dynamic>?;
    if (firebasePerms?['isAuthorized'] != true) {
      issues.add('Firebase 通知權限未授權');
      recommendations.add('請求用戶授權通知權限');
    }
    
    final localPerms = permissions?['local'] as Map<String, dynamic>?;
    if (localPerms?['isEnabled'] != true) {
      issues.add('本地通知權限未啟用');
      recommendations.add('在系統設定中啟用通知權限');
    }
    
    // 檢查 FCM Token
    final fcmToken = results['fcmToken'] as Map<String, dynamic>?;
    if (fcmToken?['hasToken'] != true) {
      issues.add('FCM Token 獲取失敗');
      recommendations.add('檢查網路連接和 Firebase 配置');
    }
    
    // 檢查服務狀態
    final services = results['services'] as Map<String, dynamic>?;
    final notificationService = services?['notificationService'] as Map<String, dynamic>?;
    if (notificationService?['isInitialized'] != true) {
      issues.add('通知服務未初始化');
      recommendations.add('確保應用啟動時正確初始化通知服務');
    }
    
    return {
      'overallStatus': issues.isEmpty ? 'healthy' : 'issues_found',
      'issuesCount': issues.length,
      'issues': issues,
      'recommendations': recommendations,
    };
  }

  /// 獲取平台名稱
  static String _getPlatformName() {
    if (kIsWeb) return 'web';

    try {
      if (Platform.isAndroid) return 'android';
      if (Platform.isIOS) return 'ios';
      if (Platform.isMacOS) return 'macos';
      if (Platform.isWindows) return 'windows';
      if (Platform.isLinux) return 'linux';
    } catch (e) {
      // Web 平台會拋出異常，直接返回 'web'
      return 'web';
    }

    return 'unknown';
  }

  /// 嘗試修復常見問題
  static Future<Map<String, dynamic>> attemptAutoFix() async {
    final fixes = <String, dynamic>{};
    
    try {
      logger.i('嘗試自動修復通知問題...');
      
      // 1. 重新請求權限
      fixes['permissions'] = await _fixPermissions();
      
      // 2. 重新獲取 FCM Token
      fixes['fcmToken'] = await _fixFCMToken();
      
      // 3. 重新初始化服務
      fixes['services'] = await _fixServices();
      
      logger.i('自動修復完成');
      return fixes;
    } catch (e) {
      logger.e('自動修復失敗: $e');
      fixes['error'] = e.toString();
      return fixes;
    }
  }

  /// 修復權限問題
  static Future<Map<String, dynamic>> _fixPermissions() async {
    try {
      final messaging = FirebaseMessaging.instance;
      final settings = await messaging.requestPermission(
        alert: true,
        badge: true,
        sound: true,
      );
      
      return {
        'success': settings.authorizationStatus == AuthorizationStatus.authorized ||
                  settings.authorizationStatus == AuthorizationStatus.provisional,
        'status': settings.authorizationStatus.toString(),
      };
    } catch (e) {
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// 修復 FCM Token 問題
  static Future<Map<String, dynamic>> _fixFCMToken() async {
    try {
      final messaging = FirebaseMessaging.instance;
      await messaging.deleteToken(); // 刪除舊 Token
      final newToken = await messaging.getToken(); // 獲取新 Token
      
      return {
        'success': newToken != null,
        'hasNewToken': newToken != null,
        'tokenPreview': newToken != null ? '${newToken.substring(0, 20)}...' : null,
      };
    } catch (e) {
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// 修復服務問題
  static Future<Map<String, dynamic>> _fixServices() async {
    try {
      // 重新初始化通知服務
      await NotificationService.initialize();
      
      return {
        'success': NotificationService.isInitialized,
        'isInitialized': NotificationService.isInitialized,
      };
    } catch (e) {
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// 發送測試通知
  static Future<Map<String, dynamic>> sendTestNotification() async {
    try {
      logger.i('發送測試通知...');
      
      // 這裡應該調用實際的通知發送邏輯
      // 由於我們沒有後端服務，這裡只是模擬
      
      return {
        'success': true,
        'message': '測試通知發送請求已提交',
        'timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }
}
