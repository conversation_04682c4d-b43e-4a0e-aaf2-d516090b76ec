import '../../models/notification/notification_model.dart';

/// 通知測試數據
class NotificationTestData {
  /// 生成測試通知數據
  static List<NotificationModel> generateTestNotifications() {
    return [
      // 系統通知（有圖片）
      NotificationModel(
        id: 'test_001',
        title: '歡迎使用 AstReal 星真占星',
        body: '感謝您下載 AstReal！我們為您準備了完整的占星分析功能，包括本命盤解析、流年運勢、合盤分析等。立即開始您的占星之旅吧！',
        type: NotificationType.systemAnnouncement,
        priority: NotificationPriority.high,
        isRead: false,
        createdAt: DateTime.now().subtract(const Duration(minutes: 5)),
        imageUrl: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=300&fit=crop',
        actionUrl: '/welcome',
      ),

      // 促銷活動（有圖片）
      NotificationModel(
        id: 'test_002',
        title: '🎉 新年特惠活動開始！',
        body: '新年期間，所有 AI 深入剖析功能享受 7 折優惠！包括感情分析、事業運勢、財運預測等。活動期間有限，把握機會！',
        type: NotificationType.promotion,
        priority: NotificationPriority.high,
        isRead: false,
        createdAt: DateTime.now().subtract(const Duration(hours: 2)),
        imageUrl: 'https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?w=400&h=300&fit=crop',
        actionUrl: '/promotion/new-year',
      ),

      // 功能更新（有圖片）
      NotificationModel(
        id: 'test_003',
        title: '✨ 新功能上線：星盤篩選器',
        body: '我們新增了強大的星盤篩選功能！您可以根據行星星座、行星宮位、宮位星座等條件快速篩選出生資料，讓占星研究更加高效。',
        type: NotificationType.featureUpdate,
        priority: NotificationPriority.normal,
        isRead: true,
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
        imageUrl: 'https://images.unsplash.com/photo-1446776877081-d282a0f896e2?w=400&h=300&fit=crop',
        actionUrl: '/features/chart-filter',
      ),

      // 個人提醒（無圖片）
      NotificationModel(
        id: 'test_004',
        title: '今日運勢提醒',
        body: '根據您的本命盤分析，今天是適合進行重要決策的日子。水星與木星的和諧相位為您帶來清晰的思維和良好的判斷力。',
        type: NotificationType.personalReminder,
        priority: NotificationPriority.normal,
        isRead: false,
        createdAt: DateTime.now().subtract(const Duration(hours: 8)),
        actionUrl: '/daily-horoscope',
      ),

      // 重要通知（無圖片）
      NotificationModel(
        id: 'test_005',
        title: '🔒 帳戶安全提醒',
        body: '我們檢測到您的帳戶在新設備上登入。如果這不是您的操作，請立即更改密碼並檢查帳戶安全設定。',
        type: NotificationType.important,
        priority: NotificationPriority.urgent,
        isRead: false,
        createdAt: DateTime.now().subtract(const Duration(hours: 12)),
        actionUrl: '/security/settings',
      ),

      // 一般通知（有圖片）
      NotificationModel(
        id: 'test_006',
        title: '💬 有人回覆了您的討論',
        body: '用戶「星座愛好者」在「水星逆行的影響」討論中回覆了您的留言。快來看看大家的精彩觀點吧！',
        type: NotificationType.general,
        priority: NotificationPriority.low,
        isRead: true,
        createdAt: DateTime.now().subtract(const Duration(days: 2)),
        imageUrl: 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=400&h=300&fit=crop',
        actionUrl: '/community/discussion/mercury-retrograde',
      ),

      // 功能更新（有圖片）
      NotificationModel(
        id: 'test_007',
        title: '📚 新的學習內容：月亮相位解析',
        body: '我們為您準備了關於月亮相位的詳細解析文章。了解月亮與其他行星的相位如何影響您的情緒和直覺反應。',
        type: NotificationType.featureUpdate,
        priority: NotificationPriority.low,
        isRead: false,
        createdAt: DateTime.now().subtract(const Duration(days: 3)),
        imageUrl: 'https://images.unsplash.com/photo-1502134249126-9f3755a50d78?w=400&h=300&fit=crop',
        actionUrl: '/learn/moon-aspects',
      ),

      // 系統公告（無圖片）
      NotificationModel(
        id: 'test_008',
        title: '🔧 系統維護通知',
        body: '為了提供更好的服務體驗，我們將在今晚 23:00-01:00 進行系統維護。維護期間部分功能可能暫時無法使用，敬請諒解。',
        type: NotificationType.systemAnnouncement,
        priority: NotificationPriority.normal,
        isRead: true,
        createdAt: DateTime.now().subtract(const Duration(days: 5)),
        actionUrl: '/maintenance/info',
      ),

      // 個人提醒（有圖片）
      NotificationModel(
        id: 'test_009',
        title: '🎂 生日快樂！',
        body: '今天是您的生日！根據太陽返照盤分析，新的一年將為您帶來成長和機遇。祝您生日快樂，願新的一歲充滿喜悅和成功！',
        type: NotificationType.personalReminder,
        priority: NotificationPriority.high,
        isRead: false,
        createdAt: DateTime.now().subtract(const Duration(days: 7)),
        imageUrl: 'https://images.unsplash.com/photo-1464349095431-e9a21285b5f3?w=400&h=300&fit=crop',
        actionUrl: '/birthday/solar-return',
      ),

      // 占星事件（有圖片）
      NotificationModel(
        id: 'test_010',
        title: '🌟 重要占星事件：水星逆行開始',
        body: '水星將於明天開始逆行，持續約三週時間。這段期間建議謹慎處理溝通、旅行和電子設備相關事務。我們為您準備了詳細的應對指南。',
        type: NotificationType.astroEvent,
        priority: NotificationPriority.high,
        isRead: false,
        createdAt: DateTime.now().subtract(const Duration(days: 10)),
        imageUrl: 'https://images.unsplash.com/photo-1419242902214-272b3f66ee7a?w=400&h=300&fit=crop',
        actionUrl: '/events/mercury-retrograde',
      ),
    ];
  }

  /// 生成簡單測試通知（用於快速測試）
  static List<NotificationModel> generateSimpleTestNotifications() {
    return [
      NotificationModel(
        id: 'simple_001',
        title: '測試通知（有圖片）',
        body: '這是一個包含圖片的測試通知，用於驗證圖片顯示功能是否正常工作。',
        type: NotificationType.systemAnnouncement,
        priority: NotificationPriority.normal,
        isRead: false,
        createdAt: DateTime.now(),
        imageUrl: 'https://picsum.photos/400/300?random=1',
      ),
      
      NotificationModel(
        id: 'simple_002',
        title: '測試通知（無圖片）',
        body: '這是一個不包含圖片的測試通知，將顯示預設的類型圖標。',
        type: NotificationType.personalReminder,
        priority: NotificationPriority.low,
        isRead: true,
        createdAt: DateTime.now().subtract(const Duration(hours: 1)),
      ),
    ];
  }

  /// 生成大量測試數據（用於效能測試）
  static List<NotificationModel> generateLargeTestDataset({int count = 100}) {
    final List<NotificationModel> notifications = [];
    final types = NotificationType.values;
    final priorities = NotificationPriority.values;
    
    for (int i = 0; i < count; i++) {
      final type = types[i % types.length];
      final priority = priorities[i % priorities.length];
      final hasImage = i % 3 == 0; // 每三個通知有一個包含圖片
      
      notifications.add(
        NotificationModel(
          id: 'bulk_${i.toString().padLeft(3, '0')}',
          title: '測試通知 #${i + 1}',
          body: '這是第 ${i + 1} 個測試通知，用於測試大量數據的顯示效能和滾動流暢度。',
          type: type,
          priority: priority,
          isRead: i % 4 != 0, // 每四個通知有一個未讀
          createdAt: DateTime.now().subtract(Duration(minutes: i * 5)),
          imageUrl: hasImage ? 'https://picsum.photos/400/300?random=${i + 10}' : null,
          actionUrl: hasImage ? '/test/action/$i' : null,
        ),
      );
    }
    
    return notifications;
  }
}
