import 'dart:io' show Platform;

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';

import '../../../astreal.dart';
import '../../../core/constants/firebase_collections.dart';
import '../../models/notification/notification_model.dart';

/// Firebase 通知服務
class FirebaseNotificationService {
  static final FirebaseMessaging _firebaseMessaging =
      FirebaseMessaging.instance;
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  static bool _isInitialized = false;
  static String? _fcmToken;
  static Function(NotificationModel)? _onNotificationReceived;
  static Function(NotificationModel)? _onNotificationOpened;

  /// 初始化 Firebase 通知服務
  static Future<void> initialize({
    Function(NotificationModel)? onNotificationReceived,
    Function(NotificationModel)? onNotificationOpened,
  }) async {
    if (_isInitialized) return;

    try {
      logger.i('初始化 Firebase 通知服務...');

      _onNotificationReceived = onNotificationReceived;
      _onNotificationOpened = onNotificationOpened;

      // 請求通知權限
      await _requestPermission();

      // 獲取 FCM Token
      await _getFCMToken();

      // 設定前景通知處理
      FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

      // 設定背景通知處理
      FirebaseMessaging.onMessageOpenedApp.listen(_handleNotificationOpened);

      // 處理應用關閉時的通知
      await _handleInitialMessage();

      // 監聽 Token 刷新
      FirebaseMessaging.instance.onTokenRefresh.listen(_onTokenRefresh);

      _isInitialized = true;
      logger.i('Firebase 通知服務初始化成功');
    } catch (e) {
      logger.e('Firebase 通知服務初始化失敗: $e');
      _isInitialized = false;
    }
  }

  /// 請求通知權限
  static Future<void> _requestPermission() async {
    try {
      // Web 平台需要特殊處理
      if (kIsWeb) {
        logger.i('Web 平台請求通知權限...');

        // 檢查瀏覽器是否支援通知
        if (!kIsWeb || !identical(0, 0.0)) {
          // 這是一個檢查是否在瀏覽器環境的技巧
          logger.w('當前環境不支援 Web 通知');
          return;
        }

        final settings = await _firebaseMessaging.requestPermission(
          alert: true,
          badge: true,
          sound: true,
        );

        logger.i('Web 通知權限狀態: ${settings.authorizationStatus}');

        if (settings.authorizationStatus == AuthorizationStatus.authorized) {
          logger.i('Web 用戶已授權通知權限');
        } else if (settings.authorizationStatus == AuthorizationStatus.denied) {
          logger.w('Web 用戶拒絕通知權限');
        } else {
          logger.w('Web 通知權限狀態未知: ${settings.authorizationStatus}');
        }

        return;
      }

      // 移動平台處理
      final settings = await _firebaseMessaging.requestPermission(
        alert: true,
        announcement: false,
        badge: true,
        carPlay: false,
        criticalAlert: false,
        // provisional: false, // 某些版本可能不支援此參數
        sound: true,
      );

      logger.i('通知權限狀態: ${settings.authorizationStatus}');

      if (settings.authorizationStatus == AuthorizationStatus.authorized) {
        logger.i('用戶已授權通知權限');

        // iOS 需要等待 APNs Token
        if (!kIsWeb && Platform.isIOS) {
          await _waitForAPNsToken();
        }
      } else if (settings.authorizationStatus ==
          AuthorizationStatus.provisional) {
        logger.i('用戶已授權臨時通知權限');

        // iOS 需要等待 APNs Token
        if (!kIsWeb && Platform.isIOS) {
          await _waitForAPNsToken();
        }
      } else {
        logger.w('用戶拒絕通知權限');
      }
    } catch (e) {
      logger.e('請求通知權限失敗: $e');
    }
  }

  /// 等待 APNs Token（僅 iOS）
  static Future<void> _waitForAPNsToken() async {
    if (kIsWeb || !Platform.isIOS) return;

    try {
      // 等待 APNs Token，最多等待 10 秒
      for (int i = 0; i < 10; i++) {
        final apnsToken = await _firebaseMessaging.getAPNSToken();
        if (apnsToken != null) {
          logger.i('APNs Token 已獲取: ${apnsToken.substring(0, 20)}...');
          return;
        }
        await Future.delayed(const Duration(seconds: 1));
      }
      logger.w('等待 APNs Token 超時');
    } catch (e) {
      logger.e('等待 APNs Token 失敗: $e');
    }
  }

  /// 獲取 FCM Token
  static Future<String?> _getFCMToken() async {
    try {
      // Web 平台需要 VAPID key
      if (kIsWeb) {
        logger.i('Web 平台正在獲取 FCM Token...');
        _fcmToken = await FirebaseMessaging.instance.getToken(
          vapidKey:
          "BDXI78bC3YqFM5iqf4OmRirP8KxZ_KC5XUv2Hzl2_PmARvsB1PXjAbwb23y7dl53fckVN4PJKi5nkz1sokpYtRs",
        );
      } else {
        // 移動平台處理
        logger.i('移動平台正在獲取 FCM Token...');
        // iOS 需要先等待 APNs Token（只在非 Web 平台執行）
        try {
          if (Platform.isIOS) {
            await _waitForAPNsToken();
          }
        } catch (e) {
          logger.w('檢查 iOS 平台失敗，跳過 APNs Token 等待: $e');
        }

        // 獲取 FCM Token
        _fcmToken = await _firebaseMessaging.getToken();
      }

      if (_fcmToken != null) {
        logger.i('FCM Token 獲取成功: $_fcmToken');
        await _saveFCMTokenToFirestore(_fcmToken!);
      } else {
        logger.w('FCM Token 獲取失敗');
      }
      return _fcmToken;
    } catch (e) {
      logger.e('獲取 FCM Token 失敗: $e');
      return null;
    }
  }

  /// 保存 FCM Token 到 Firestore
  static Future<void> _saveFCMTokenToFirestore(String token) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        logger.w('用戶未登入，無法保存 FCM Token');
        return;
      }

      await _firestore
          .collection(FirebaseCollections.userProfiles)
          .doc(user.uid)
          .update({
        'fcmToken': token,
        'fcmTokenUpdatedAt': FieldValue.serverTimestamp(),
        'platform': _getPlatformName(),
      });

      logger.i('FCM Token 已保存到 Firestore');
    } catch (e) {
      logger.e('保存 FCM Token 到 Firestore 失敗: $e');
    }
  }

  /// 獲取平台名稱
  static String _getPlatformName() {
    if (kIsWeb) return 'web';

    // 在 Web 平台上不能使用 Platform 類別
    try {
      if (Platform.isAndroid) return 'android';
      if (Platform.isIOS) return 'ios';
      if (Platform.isMacOS) return 'macos';
      if (Platform.isWindows) return 'windows';
      if (Platform.isLinux) return 'linux';
    } catch (e) {
      // Web 平台會拋出異常，直接返回 'web'
      return 'web';
    }

    return 'unknown';
  }

  /// 處理前景通知
  static void _handleForegroundMessage(RemoteMessage message) {
    logger.i('收到前景通知: ${message.notification?.title}');

    final notification = _createNotificationFromRemoteMessage(message);

    // 保存通知到本地存儲
    _saveNotificationToFirestore(notification);

    // 觸發回調
    _onNotificationReceived?.call(notification);
  }

  /// 處理通知點擊事件
  static void _handleNotificationOpened(RemoteMessage message) {
    logger.i('用戶點擊通知: ${message.notification?.title}');

    final notification = _createNotificationFromRemoteMessage(message);

    // 標記通知為已讀
    _markNotificationAsRead(notification.id);

    // 保存通知到本地存儲
    _saveNotificationToFirestore(notification);

    // 觸發回調
    _onNotificationOpened?.call(notification);
  }

  /// 處理應用關閉時的初始通知
  static Future<void> _handleInitialMessage() async {
    try {
      final initialMessage = await _firebaseMessaging.getInitialMessage();
      if (initialMessage != null) {
        logger.i('處理初始通知: ${initialMessage.notification?.title}');
        _handleNotificationOpened(initialMessage);
      }
    } catch (e) {
      logger.e('處理初始通知失敗: $e');
    }
  }

  /// 處理 Token 刷新
  static void _onTokenRefresh(String token) {
    logger.i('FCM Token 已刷新');
    _fcmToken = token;
    _saveFCMTokenToFirestore(token);
  }

  /// 從 RemoteMessage 創建通知模型
  static NotificationModel _createNotificationFromRemoteMessage(
      RemoteMessage message) {
    final data = message.data;
    final notification = message.notification;

    return NotificationModel.fromRemoteMessage(
      data['id'] ?? DateTime.now().millisecondsSinceEpoch.toString(),
      {
        'title': notification?.title ?? data['title'] ?? '新通知',
        'body': notification?.body ?? data['body'] ?? '',
        'type': data['type'] ?? 'general',
        'priority': data['priority'] ?? '1',
        'imageUrl': notification?.android?.imageUrl ?? data['imageUrl'],
        'actionUrl': data['actionUrl'],
        'userId': data['userId'],
        ...data,
      },
    );
  }

  /// 保存通知到 Firestore
  static Future<void> _saveNotificationToFirestore(
      NotificationModel notification) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) return;

      await _firestore
          .collection(FirebaseCollections.userNotifications)
          .doc(user.uid)
          .collection('notifications')
          .doc(notification.id)
          .set(notification.toJson());

      logger.i('通知已保存到 Firestore: ${notification.id}');
    } catch (e) {
      logger.e('保存通知到 Firestore 失敗: $e');
    }
  }

  /// 標記通知為已讀
  static Future<void> _markNotificationAsRead(String notificationId) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) return;

      await _firestore
          .collection(FirebaseCollections.userNotifications)
          .doc(user.uid)
          .collection('notifications')
          .doc(notificationId)
          .update({'isRead': true});

      logger.i('通知已標記為已讀: $notificationId');
    } catch (e) {
      logger.e('標記通知為已讀失敗: $e');
    }
  }

  /// 獲取用戶的所有通知
  static Future<List<NotificationModel>> getUserNotifications({
    int limit = 50,
    DocumentSnapshot? startAfter,
  }) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        logger.w('用戶未登入，無法獲取通知');
        return [];
      }

      Query query = _firestore
          .collection(FirebaseCollections.userNotifications)
          .doc(user.uid)
          .collection('notifications')
          .orderBy('createdAt', descending: true)
          .limit(limit);

      if (startAfter != null) {
        query = query.startAfterDocument(startAfter);
      }

      final querySnapshot = await query.get();

      return querySnapshot.docs
          .map((doc) => NotificationModel.fromJson({
                ...doc.data() as Map<String, dynamic>,
                'id': doc.id,
              }))
          .toList();
    } catch (e) {
      logger.e('獲取用戶通知失敗: $e');
      return [];
    }
  }

  /// 獲取未讀通知數量
  static Future<int> getUnreadNotificationCount() async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) return 0;

      final querySnapshot = await _firestore
          .collection(FirebaseCollections.userNotifications)
          .doc(user.uid)
          .collection('notifications')
          .where('isRead', isEqualTo: false)
          .count()
          .get();

      return querySnapshot.count ?? 0;
    } catch (e) {
      logger.e('獲取未讀通知數量失敗: $e');
      return 0;
    }
  }

  /// 標記所有通知為已讀
  static Future<void> markAllNotificationsAsRead() async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) return;

      final batch = _firestore.batch();

      final querySnapshot = await _firestore
          .collection(FirebaseCollections.userNotifications)
          .doc(user.uid)
          .collection('notifications')
          .where('isRead', isEqualTo: false)
          .get();

      for (final doc in querySnapshot.docs) {
        batch.update(doc.reference, {'isRead': true});
      }

      await batch.commit();
      logger.i('所有通知已標記為已讀');
    } catch (e) {
      logger.e('標記所有通知為已讀失敗: $e');
    }
  }

  /// 刪除通知
  static Future<void> deleteNotification(String notificationId) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) return;

      await _firestore
          .collection(FirebaseCollections.userNotifications)
          .doc(user.uid)
          .collection('notifications')
          .doc(notificationId)
          .delete();

      logger.i('通知已刪除: $notificationId');
    } catch (e) {
      logger.e('刪除通知失敗: $e');
    }
  }

  /// 獲取當前 FCM Token
  static String? get fcmToken => _fcmToken;

  /// 檢查服務是否已初始化
  static bool get isInitialized => _isInitialized;
}
