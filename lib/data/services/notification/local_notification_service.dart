import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';

import '../../../astreal.dart';
import '../../models/notification/notification_model.dart';

/// 本地通知服務
class LocalNotificationService {
  static final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();
  
  static bool _isInitialized = false;
  static Function(String)? _onNotificationTapped;

  /// 初始化本地通知服務
  static Future<void> initialize({
    Function(String)? onNotificationTapped,
  }) async {
    if (_isInitialized) return;

    try {
      logger.i('初始化本地通知服務...');
      
      _onNotificationTapped = onNotificationTapped;

      // Android 初始化設定
      const AndroidInitializationSettings initializationSettingsAndroid =
          AndroidInitializationSettings('@mipmap/ic_launcher');

      // iOS 初始化設定
      const DarwinInitializationSettings initializationSettingsIOS =
          DarwinInitializationSettings(
        requestAlertPermission: true,
        requestBadgePermission: true,
        requestSoundPermission: true,
      );

      // macOS 初始化設定
      const DarwinInitializationSettings initializationSettingsMacOS =
          DarwinInitializationSettings(
        requestAlertPermission: true,
        requestBadgePermission: true,
        requestSoundPermission: true,
      );

      const InitializationSettings initializationSettings =
          InitializationSettings(
        android: initializationSettingsAndroid,
        iOS: initializationSettingsIOS,
        macOS: initializationSettingsMacOS,
      );

      await _flutterLocalNotificationsPlugin.initialize(
        initializationSettings,
        onDidReceiveNotificationResponse: _onDidReceiveNotificationResponse,
      );

      // 請求權限
      await _requestPermissions();

      _isInitialized = true;
      logger.i('本地通知服務初始化成功');
    } catch (e) {
      logger.e('本地通知服務初始化失敗: $e');
      _isInitialized = false;
    }
  }

  /// 處理通知點擊事件
  static void _onDidReceiveNotificationResponse(NotificationResponse response) {
    final payload = response.payload;
    if (payload != null && _onNotificationTapped != null) {
      logger.i('用戶點擊通知，payload: $payload');
      _onNotificationTapped!(payload);
    }
  }

  /// 請求通知權限
  static Future<bool> _requestPermissions() async {
    try {
      if (Platform.isIOS || Platform.isMacOS) {
        final bool? result = await _flutterLocalNotificationsPlugin
            .resolvePlatformSpecificImplementation<
                IOSFlutterLocalNotificationsPlugin>()
            ?.requestPermissions(
              alert: true,
              badge: true,
              sound: true,
            );
        return result ?? false;
      } else if (Platform.isAndroid) {
        final AndroidFlutterLocalNotificationsPlugin? androidImplementation =
            _flutterLocalNotificationsPlugin.resolvePlatformSpecificImplementation<
                AndroidFlutterLocalNotificationsPlugin>();

        final bool? grantedNotificationPermission =
            await androidImplementation?.requestNotificationsPermission();
        return grantedNotificationPermission ?? false;
      }
      return true;
    } catch (e) {
      logger.e('請求通知權限失敗: $e');
      return false;
    }
  }

  /// 顯示本地通知
  static Future<void> showNotification(NotificationModel notification) async {
    if (!_isInitialized) {
      logger.w('本地通知服務未初始化，無法顯示通知');
      return;
    }

    try {
      // 根據通知類型設定不同的通知詳情
      final notificationDetails = _getNotificationDetails(notification);

      await _flutterLocalNotificationsPlugin.show(
        notification.id.hashCode, // 使用 ID 的 hashCode 作為通知 ID
        notification.title,
        notification.body,
        notificationDetails,
        payload: notification.id, // 使用通知 ID 作為 payload
      );

      logger.i('顯示本地通知成功: ${notification.title}');
    } catch (e) {
      logger.e('顯示本地通知失敗: $e');
    }
  }

  /// 獲取通知詳情設定
  static NotificationDetails _getNotificationDetails(NotificationModel notification) {
    // Android 通知詳情
    final androidDetails = AndroidNotificationDetails(
      'astreal_notifications', // 頻道 ID
      '星真占星通知', // 頻道名稱
      channelDescription: '星真占星應用的通知',
      importance: _getAndroidImportance(notification.priority),
      priority: _getAndroidPriority(notification.priority),
      icon: '@mipmap/ic_launcher',
      color: notification.type.color,
      enableVibration: notification.isHighPriority,
      playSound: true,
      styleInformation: notification.body.length > 50
          ? BigTextStyleInformation(notification.body)
          : null,
    );

    // iOS/macOS 通知詳情
    final iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
      sound: notification.isUrgent ? 'default' : null,
      badgeNumber: null,
      subtitle: notification.type.displayName,
    );

    return NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
      macOS: iosDetails,
    );
  }

  /// 獲取 Android 重要性等級
  static Importance _getAndroidImportance(NotificationPriority priority) {
    switch (priority) {
      case NotificationPriority.low:
        return Importance.low;
      case NotificationPriority.normal:
        return Importance.defaultImportance;
      case NotificationPriority.high:
        return Importance.high;
      case NotificationPriority.urgent:
        return Importance.max;
    }
  }

  /// 獲取 Android 優先級
  static Priority _getAndroidPriority(NotificationPriority priority) {
    switch (priority) {
      case NotificationPriority.low:
        return Priority.low;
      case NotificationPriority.normal:
        return Priority.defaultPriority;
      case NotificationPriority.high:
        return Priority.high;
      case NotificationPriority.urgent:
        return Priority.max;
    }
  }

  /// 取消特定通知
  static Future<void> cancelNotification(String notificationId) async {
    if (!_isInitialized) return;

    try {
      await _flutterLocalNotificationsPlugin.cancel(notificationId.hashCode);
      logger.i('取消通知成功: $notificationId');
    } catch (e) {
      logger.e('取消通知失敗: $e');
    }
  }

  /// 取消所有通知
  static Future<void> cancelAllNotifications() async {
    if (!_isInitialized) return;

    try {
      await _flutterLocalNotificationsPlugin.cancelAll();
      logger.i('取消所有通知成功');
    } catch (e) {
      logger.e('取消所有通知失敗: $e');
    }
  }

  /// 獲取待處理的通知
  static Future<List<PendingNotificationRequest>> getPendingNotifications() async {
    if (!_isInitialized) return [];

    try {
      return await _flutterLocalNotificationsPlugin.pendingNotificationRequests();
    } catch (e) {
      logger.e('獲取待處理通知失敗: $e');
      return [];
    }
  }

  /// 檢查通知權限狀態
  static Future<bool> areNotificationsEnabled() async {
    if (!_isInitialized) return false;

    // Web 平台不支援本地通知
    if (kIsWeb) return true;

    try {
      if (Platform.isAndroid) {
        final AndroidFlutterLocalNotificationsPlugin? androidImplementation =
            _flutterLocalNotificationsPlugin.resolvePlatformSpecificImplementation<
                AndroidFlutterLocalNotificationsPlugin>();
        return await androidImplementation?.areNotificationsEnabled() ?? false;
      } else if (Platform.isIOS || Platform.isMacOS) {
        final IOSFlutterLocalNotificationsPlugin? iosImplementation =
            _flutterLocalNotificationsPlugin.resolvePlatformSpecificImplementation<
                IOSFlutterLocalNotificationsPlugin>();
        final permissions = await iosImplementation?.checkPermissions();
        return permissions?.isEnabled == true;
      }
      return true;
    } catch (e) {
      logger.e('檢查通知權限失敗: $e');
      return false;
    }
  }

  /// 檢查是否支援打開通知設定
  static bool get canOpenNotificationSettings {
    if (kIsWeb) return false; // Web 平台不支援

    try {
      // 只有 Android 和 iOS 支援打開系統設定
      return Platform.isAndroid || Platform.isIOS;
    } catch (e) {
      return false;
    }
  }

  /// 打開系統通知設定
  static Future<bool> openNotificationSettings() async {
    if (!canOpenNotificationSettings) {
      logger.w('當前平台不支援打開通知設定');
      return false;
    }

    try {
      if (Platform.isAndroid) {
        final AndroidFlutterLocalNotificationsPlugin? androidImplementation =
            _flutterLocalNotificationsPlugin.resolvePlatformSpecificImplementation<
                AndroidFlutterLocalNotificationsPlugin>();

        if (androidImplementation != null) {
          // 嘗試使用新版本的方法
          try {
            await androidImplementation.requestNotificationsPermission();
            logger.i('Android 通知權限請求成功');
            return true;
          } catch (e) {
            logger.w('Android 通知權限請求失敗，請手動前往系統設定: $e');
            return false;
          }
        }
      } else if (Platform.isIOS) {
        // iOS 平台可以通過 URL Scheme 打開設定
        try {
          // 這裡需要使用 url_launcher 套件
          logger.i('iOS 平台請手動前往「設定 > 通知 > AstReal」開啟通知權限');
          return true;
        } catch (e) {
          logger.e('iOS 打開通知設定失敗: $e');
          return false;
        }
      }

      return false;
    } catch (e) {
      logger.e('打開通知設定失敗: $e');
      return false;
    }
  }
}
