import 'dart:convert';
import 'dart:io';

import 'package:http/http.dart' as http;
import 'package:sweph/sweph.dart';

import '../../../astreal.dart';
import 'firebase_notification_service.dart';

/// 測試通知服務
/// 用於發送測試通知（需要後端支援或使用 Firebase Admin SDK）
class TestNotificationService {
  
  /// 發送測試通知到當前設備
  static Future<Map<String, dynamic>> sendTestNotificationToCurrentDevice({
    String title = '測試通知',
    String body = '這是一個測試通知，用於驗證推播通知功能是否正常工作。',
    Map<String, dynamic>? data,
  }) async {
    try {
      final fcmToken = FirebaseNotificationService.fcmToken;
      if (fcmToken == null) {
        return {
          'success': false,
          'error': 'FCM Token 未獲取，無法發送通知',
        };
      }

      logger.i('準備發送測試通知到當前設備...');
      
      // 這裡需要後端 API 來發送通知
      // 由於我們沒有後端，這裡只是模擬
      return await _simulateNotificationSend(
        token: fcmToken,
        title: title,
        body: body,
        data: data,
      );
    } catch (e) {
      logger.e('發送測試通知失敗: $e');
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// 模擬通知發送（實際需要後端支援）
  static Future<Map<String, dynamic>> _simulateNotificationSend({
    required String token,
    required String title,
    required String body,
    Map<String, dynamic>? data,
  }) async {
    try {
      // 這裡應該調用後端 API 或使用 Firebase Admin SDK
      // 由於我們在客戶端，無法直接發送 FCM 通知
      
      logger.i('模擬發送通知:');
      logger.i('- Token: ${token.substring(0, 20)}...');
      logger.i('- Title: $title');
      logger.i('- Body: $body');
      logger.i('- Data: $data');
      
      // 模擬網路請求延遲
      await Future.delayed(const Duration(seconds: 1));
      
      return {
        'success': true,
        'message': '測試通知發送請求已提交（模擬）',
        'token': '${token.substring(0, 20)}...',
        'timestamp': DateTime.now().toIso8601String(),
        'note': '實際發送需要後端支援或 Firebase Admin SDK',
      };
    } catch (e) {
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// 使用 Firebase Functions 發送通知（如果有配置）
  static Future<Map<String, dynamic>> sendNotificationViaCloudFunction({
    String title = '測試通知',
    String body = '這是通過 Cloud Function 發送的測試通知。',
    Map<String, dynamic>? data,
  }) async {
    try {
      final fcmToken = FirebaseNotificationService.fcmToken;
      if (fcmToken == null) {
        return {
          'success': false,
          'error': 'FCM Token 未獲取',
        };
      }

      // 這裡應該調用您的 Firebase Cloud Function
      // 例如：https://your-project.cloudfunctions.net/sendNotification
      
      const cloudFunctionUrl = 'YOUR_CLOUD_FUNCTION_URL';
      
      final response = await http.post(
        Uri.parse(cloudFunctionUrl),
        headers: {
          'Content-Type': 'application/json',
        },
        body: json.encode({
          'token': fcmToken,
          'title': title,
          'body': body,
          'data': data ?? {},
        }),
      );

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        return {
          'success': true,
          'response': responseData,
        };
      } else {
        return {
          'success': false,
          'error': 'HTTP ${response.statusCode}: ${response.body}',
        };
      }
    } catch (e) {
      logger.e('通過 Cloud Function 發送通知失敗: $e');
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// 獲取發送通知所需的信息
  static Map<String, dynamic> getNotificationSendInfo() {
    final fcmToken = FirebaseNotificationService.fcmToken;
    
    return {
      'hasToken': fcmToken != null,
      'token': fcmToken != null ? '${fcmToken.substring(0, 20)}...' : null,
      'fullToken': fcmToken, // 僅用於調試，生產環境不應暴露完整 Token
      'instructions': [
        '1. 複製完整的 FCM Token',
        '2. 使用 Firebase 控制台的 "Cloud Messaging" 功能',
        '3. 選擇 "發送測試消息"',
        '4. 貼上 FCM Token',
        '5. 填寫通知標題和內容',
        '6. 點擊 "測試" 按鈕發送',
      ],
      'firebaseConsoleUrl': 'https://console.firebase.google.com/project/YOUR_PROJECT_ID/messaging',
    };
  }

  /// 創建用於 Firebase 控制台測試的通知負載
  static Map<String, dynamic> createTestNotificationPayload({
    String title = '星真占星測試通知',
    String body = '這是一個測試通知，用於驗證推播通知功能。',
    String? imageUrl,
    Map<String, String>? customData,
  }) {
    return {
      'notification': {
        'title': title,
        'body': body,
        if (imageUrl != null) 'image': imageUrl,
      },
      'data': {
        'type': 'test',
        'timestamp': DateTime.now().millisecondsSinceEpoch.toString(),
        'actionUrl': '/notifications',
        ...?customData,
      },
      'android': {
        'notification': {
          'icon': '@mipmap/ic_launcher',
          'color': '#3F51B5',
          'channel_id': 'astreal_notifications',
          'priority': 'high',
        },
      },
      'apns': {
        'payload': {
          'aps': {
            'alert': {
              'title': title,
              'body': body,
            },
            'badge': 1,
            'sound': 'default',
          },
        },
      },
    };
  }

  /// 驗證通知配置
  static Future<Map<String, dynamic>> validateNotificationSetup() async {
    final results = <String, dynamic>{};
    
    try {
      // 檢查 FCM Token
      final fcmToken = FirebaseNotificationService.fcmToken;
      results['fcmToken'] = {
        'available': fcmToken != null,
        'preview': fcmToken != null ? '${fcmToken.substring(0, 20)}...' : null,
      };
      
      // 檢查服務初始化狀態
      results['services'] = {
        'firebaseNotificationService': FirebaseNotificationService.isInitialized,
      };
      
      // 檢查平台特定配置
      results['platform'] = {
        'isAndroid': Platform.isAndroid,
        'isIOS': Platform.isIOS,
        'isWeb': kIsWeb,
      };
      
      // 生成建議
      final suggestions = <String>[];
      
      if (fcmToken == null) {
        suggestions.add('FCM Token 未獲取，檢查網路連接和 Firebase 配置');
      }
      
      if (!FirebaseNotificationService.isInitialized) {
        suggestions.add('Firebase 通知服務未初始化');
      }
      
      if (Platform.isIOS) {
        suggestions.add('iOS 需要正確配置 APNs 證書');
        suggestions.add('確保在 Firebase 控制台上傳了 APNs 認證密鑰');
      }
      
      results['suggestions'] = suggestions;
      results['isReady'] = fcmToken != null && FirebaseNotificationService.isInitialized;
      
      return results;
    } catch (e) {
      results['error'] = e.toString();
      return results;
    }
  }
}
