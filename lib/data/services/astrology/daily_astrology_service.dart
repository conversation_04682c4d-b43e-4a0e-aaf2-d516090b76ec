import 'package:cloud_firestore/cloud_firestore.dart';

import '../../../astreal.dart';
import '../../../data/services/api/astrology_service.dart';
import '../../models/astrology/daily_astrology.dart';
import '../user_birth_data_service.dart';
import 'daily_astrology_calculator.dart';

/// 每日星相服務
class DailyAstrologyService {
  static const String _dailyAstrologyCollection = 'daily_astrology';
  static const String _personalizedAstrologyCollection =
      'personalized_daily_astrology';
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// 獲取指定日期的每日星相
  static Future<DailyAstrologyData?> getDailyAstrology(DateTime date) async {
    try {
      final dateId = _formatDateId(date);
      logger.d('獲取每日星相: $dateId');

      // final doc = await _firestore
      //     .collection(_dailyAstrologyCollection)
      //     .doc(dateId)
      //     .get();
      //
      // if (doc.exists && doc.data() != null) {
      //   final data = doc.data()!;
      //   data['id'] = doc.id;
      //   return DailyAstrologyData.fromJson(data);
      // }

      // 如果不存在，計算並保存
      logger.d('計算新的每日星相: $dateId');
      final calculatedData =
          await DailyAstrologyCalculator.calculateDailyAstrology(date);
      // await _saveDailyAstrology(calculatedData);
      return calculatedData;
    } catch (e) {
      logger.e('獲取每日星相失敗: $e');
      return null;
    }
  }

  /// 獲取今日星相
  static Future<DailyAstrologyData?> getTodayAstrology() async {
    return await getDailyAstrology(DateTime.now());
  }

  /// 獲取指定日期範圍的星相
  static Future<List<DailyAstrologyData>> getDailyAstrologyRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      final startId = _formatDateId(startDate);
      final endId = _formatDateId(endDate);

      logger.d('獲取星相範圍: $startId 到 $endId');

      final query = await _firestore
          .collection(_dailyAstrologyCollection)
          .where(FieldPath.documentId, isGreaterThanOrEqualTo: startId)
          .where(FieldPath.documentId, isLessThanOrEqualTo: endId)
          .orderBy(FieldPath.documentId)
          .get();

      final results = <DailyAstrologyData>[];
      final existingDates = <String>{};

      // 處理已存在的資料
      for (final doc in query.docs) {
        if (doc.exists && doc.data().isNotEmpty) {
          final data = doc.data();
          data['id'] = doc.id;
          results.add(DailyAstrologyData.fromJson(data));
          existingDates.add(doc.id);
        }
      }

      // 計算缺失的日期
      final missingDates = <DateTime>[];
      var currentDate =
          DateTime(startDate.year, startDate.month, startDate.day);
      final endDateTime = DateTime(endDate.year, endDate.month, endDate.day);

      while (currentDate.isBefore(endDateTime) ||
          currentDate.isAtSameMomentAs(endDateTime)) {
        final dateId = _formatDateId(currentDate);
        if (!existingDates.contains(dateId)) {
          missingDates.add(currentDate);
        }
        currentDate = currentDate.add(const Duration(days: 1));
      }

      // 計算並保存缺失的資料
      for (final missingDate in missingDates) {
        try {
          final calculatedData =
              await DailyAstrologyCalculator.calculateDailyAstrology(
                  missingDate);
          await _saveDailyAstrology(calculatedData);
          results.add(calculatedData);
        } catch (e) {
          logger.w('計算缺失日期星相失敗: ${_formatDateId(missingDate)}, $e');
        }
      }

      // 按日期排序
      results.sort((a, b) => a.date.compareTo(b.date));

      logger.d('獲取星相範圍完成，共 ${results.length} 天');
      return results;
    } catch (e) {
      logger.e('獲取星相範圍失敗: $e');
      return [];
    }
  }

  /// 保存每日星相到 Firestore
  static Future<bool> _saveDailyAstrology(DailyAstrologyData data) async {
    try {
      await _firestore
          .collection(_dailyAstrologyCollection)
          .doc(data.id)
          .set(data.toFirestoreJson());

      logger.d('保存每日星相成功: ${data.id}');
      return true;
    } catch (e) {
      logger.e('保存每日星相失敗: $e');
      return false;
    }
  }

  /// 獲取用戶的個人化每日星相
  static Future<PersonalizedDailyAstrology?> getPersonalizedDailyAstrology(
    String userId,
    DateTime date,
  ) async {
    try {
      final dateId = _formatDateId(date);
      logger.d('獲取個人化每日星相: $userId, $dateId');

      // 如果不存在，生成個人化分析
      return await _generatePersonalizedAstrology(userId, date);
    } catch (e) {
      logger.e('獲取個人化每日星相失敗: $e');
      return null;
    }
  }

  /// 生成個人化每日星相
  static Future<PersonalizedDailyAstrology?> _generatePersonalizedAstrology(
    String userId,
    DateTime date,
  ) async {
    try {
      logger.d('生成個人化每日星相: $userId, ${_formatDateId(date)}');

      // 獲取用戶的出生資料
      final userBirthData =
          await UserBirthDataService.getSelectedBirthData(userId);
      if (userBirthData == null) {
        logger.w('用戶沒有出生資料，無法生成個人化星相');
        return null;
      }

      // 獲取當日的一般星相
      final dailyAstrology = await getDailyAstrology(date);
      if (dailyAstrology == null) {
        logger.w('無法獲取當日星相資料');
        return null;
      }

      // 生成個人化訊息
      final personalizedMessage = await _generatePersonalizedMessage(
        userBirthData.birthData,
        dailyAstrology,
      );

      // 分析個人相位
      final personalAspects = await _analyzePersonalAspects(
        userBirthData.birthData,
        date,
      );

      // 生成個人建議
      final recommendations = _generatePersonalRecommendations(
        userBirthData.birthData,
        dailyAstrology,
        personalAspects,
      );

      final personalizedAstrology = PersonalizedDailyAstrology(
        id: '${userId}_${_formatDateId(date)}',
        userId: userId,
        date: date,
        personalizedMessage: personalizedMessage,
        personalAspects: personalAspects,
        recommendations: recommendations,
        birthDataSnapshot: userBirthData.birthData.toJson(),
        createdAt: DateTime.now(),
      );

      return personalizedAstrology;
    } catch (e) {
      logger.e('生成個人化每日星相失敗: $e');
      return null;
    }
  }

  /// 生成個人化訊息
  static Future<String> _generatePersonalizedMessage(
    BirthData birthData,
    DailyAstrologyData dailyAstrology,
  ) async {
    try {
      // 計算行運盤來生成個人化訊息
      final transitChartData = ChartData(
        chartType: ChartType.transit,
        primaryPerson: birthData,
        specificDate: dailyAstrology.date,
      );

      final chartSettings = await ChartSettings.loadFromPrefs();
      final calculatedTransitChart = await AstrologyService().calculateChartData(
        transitChartData,
        chartSettings: chartSettings,
      );

      // 分析行運盤的重要特徵
      final transitAspects = calculatedTransitChart.aspects;
      if (transitAspects != null && transitAspects.isNotEmpty) {
        // 找到最重要的行運相位
        final importantAspect = transitAspects.firstWhere(
          (aspect) => aspect.aspect == '合相' || aspect.aspect == '對分相',
          orElse: () => transitAspects.first,
        );

        return _generatePersonalizedMessageFromAspect(importantAspect, birthData);
      }

      // 如果沒有重要相位，基於出生資料生成個人化訊息
      return _generatePersonalizedMessageFromBirthData(birthData, dailyAstrology);

    } catch (e) {
      logger.w('生成個人化訊息失敗: $e');
      return _generatePersonalizedMessageFromBirthData(birthData, dailyAstrology);
    }
  }

  /// 基於行運相位生成個人化訊息
  static String _generatePersonalizedMessageFromAspect(AspectInfo aspect, BirthData birthData) {
    try {
      final planet1 = aspect.planet1.name;
      final planet2 = aspect.planet2.name;
      final aspectType = aspect.aspect;

      switch (aspectType) {
        case '合相':
          return '今天行運${planet2}與你的本命${planet1}合相，這是一個新開始的能量，特別適合在相關領域展開新的行動。';
        case '對分相':
          return '今天行運${planet2}與你的本命${planet1}對分，需要在不同的能量之間尋求平衡，這是整合與成長的機會。';
        case '三分相':
          return '今天行運${planet2}與你的本命${planet1}形成和諧相位，能量流動順暢，是推進計畫的好時機。';
        case '四分相':
          return '今天行運${planet2}與你的本命${planet1}形成挑戰相位，雖有阻力但也是突破成長的契機。';
        default:
          return '今天的宇宙能量與你的本命盤產生特殊共鳴，保持敏感度去感受這些微妙的變化。';
      }
    } catch (e) {
      return '今天的行運能量與你的本命盤產生獨特的互動，適合深入觀察內在的變化。';
    }
  }

  /// 基於出生資料生成個人化訊息
  static String _generatePersonalizedMessageFromBirthData(BirthData birthData, DailyAstrologyData dailyAstrology) {
    try {
      final birthMonth = birthData.dateTime.month;
      final currentMonth = DateTime.now().month;

      // 生日月份特殊訊息
      if (birthMonth == currentMonth) {
        return '這是你的生日月份，宇宙能量特別支持你的個人成長和新目標的設定，是重新定義自己的好時機。';
      }

      // 基於一般星象但個人化的訊息
      final mostImportantEvent = dailyAstrology.mostImportantEvent;
      if (mostImportantEvent != null) {
        switch (mostImportantEvent.type) {
          case DailyAstrologyEventType.newMoon:
            return '新月能量與你的本命盤共鳴，這是為個人目標注入新意圖的絕佳時機，特別關注內心真正渴望的方向。';
          case DailyAstrologyEventType.fullMoon:
            return '滿月照亮你內在的情感世界，是檢視個人成長成果和釋放舊模式的重要時刻。';
          case DailyAstrologyEventType.moonSignChange:
            return '月亮換座會特別影響你的情緒節奏，今天適合調整個人的能量狀態和直覺感受。';
          default:
            return '今天的宇宙能量與你的個人星圖產生微妙的互動，保持開放的心態去感受這些變化。';
        }
      }

      return '今天是屬於你的特別日子，宇宙邀請你更深入地連結自己的內在智慧和直覺指引。';
    } catch (e) {
      return '今天適合專注於個人的內在成長，相信你的直覺會為你指引正確的方向。';
    }
  }

  /// 分析個人相位
  static Future<List<String>> _analyzePersonalAspects(
    BirthData birthData,
    DateTime date,
  ) async {
    final aspects = <String>[];

    try {
      // 創建行運盤數據
      final transitChartData = ChartData(
        chartType: ChartType.transit,
        primaryPerson: birthData,
        specificDate: date,
      );

      // 計算行運盤
      final chartSettings = await ChartSettings.loadFromPrefs();
      chartSettings.planetVisibility = planetVisibilityHome;
      chartSettings.aspectOrbs = aspectOrbsHome;
      final calculatedTransitChart =
          await AstrologyService().calculateChartData(
        transitChartData,
        chartSettings: chartSettings,
      );

      // 獲取行運相位（本命盤與行運盤的相位）
      final transitAspects = calculatedTransitChart.aspects;

      if (transitAspects != null && transitAspects.isNotEmpty) {
        // 篩選重要的行運相位
        final importantAspects = transitAspects
            .where((aspect) {
              // 只顯示重要相位：合相、對分相、三分相、四分相
              return aspect.aspect == '合相' ||
                  aspect.aspect == '對分相' ||
                  aspect.aspect == '三分相' ||
                  aspect.aspect == '四分相' &&
                      aspect.direction == AspectDirection.applying;
            })
            .take(50)
            .toList(); // 最多顯示5個重要相位

        for (final aspect in importantAspects) {
          final aspectDescription = _formatTransitAspectDescription(aspect);
          if (aspectDescription.isNotEmpty) {
            aspects.add(aspectDescription);
          }
        }
      }

      // 如果沒有重要相位，提供一般性提醒
      if (aspects.isEmpty) {
        aspects.add('今日行星能量平穩，適合日常事務處理');
        aspects.add('保持內心平靜，觀察細微的能量變化');
      }
    } catch (e) {
      logger.w('分析個人相位失敗: $e');
      // 提供備用信息
      aspects.add('今日宇宙能量與你的本命盤產生共鳴');
      aspects.add('注意直覺和情感的微妙變化');
    }

    return aspects;
  }

  /// 格式化行運相位描述
  static String _formatTransitAspectDescription(AspectInfo aspect) {
    try {
      final planet1Name = aspect.planet1.name;
      final planet2Name = aspect.planet2.name;
      final aspectName = aspect.aspect;

      // 判斷哪個是行運行星，哪個是本命行星
      // 通常行運行星的名稱會包含"行運"或者可以通過其他方式判斷
      // 這裡簡化處理，假設第二個行星是行運行星
      final transitPlanet = planet2Name;
      final natalPlanet = planet1Name;

      // 根據相位類型提供不同的描述
      switch (aspectName) {
        case '合相':
          return '行運$transitPlanet與本命$natalPlanet合相 - 新的開始與能量融合';
        case '對分相':
          return '行運$transitPlanet與本命$natalPlanet對分 - 需要平衡與整合的能量';
        case '三分相':
          return '行運$transitPlanet與本命$natalPlanet三分 - 和諧流暢的正面能量';
        case '四分相':
          return '行運$transitPlanet與本命$natalPlanet四分 - 挑戰與成長的機會';
        default:
          return '行運$transitPlanet與本命$natalPlanet形成$aspectName相位';
      }
    } catch (e) {
      logger.w('格式化相位描述失敗: $e');
      return '';
    }
  }

  /// 生成個人建議
  static List<String> _generatePersonalRecommendations(
    BirthData birthData,
    DailyAstrologyData dailyAstrology,
    List<String> personalAspects,
  ) {
    final recommendations = <String>[];
    final usedCategories = <String>{};

    try {
      // 基於個人相位生成建議（避免重複）
      if (personalAspects.isNotEmpty) {
        final aspectTypes = <String>{};
        for (final aspect in personalAspects) {
          String? aspectType;
          String? recommendation;

          if (aspect.contains('合相') && !aspectTypes.contains('合相')) {
            aspectType = '合相';
            recommendation = '把握新的開始機會，整合內在能量，開啟新的人生篇章';
          } else if (aspect.contains('對分') && !aspectTypes.contains('對分')) {
            aspectType = '對分';
            recommendation = '在對立中尋求平衡，學習整合不同面向的自己';
          } else if (aspect.contains('三分') && !aspectTypes.contains('三分')) {
            aspectType = '三分';
            recommendation = '善用和諧流暢的能量，是推進重要計畫的絕佳時機';
          } else if (aspect.contains('四分') && !aspectTypes.contains('四分')) {
            aspectType = '四分';
            recommendation = '將挑戰視為成長契機，突破限制展現更強大的自己';
          }

          if (aspectType != null && recommendation != null) {
            aspectTypes.add(aspectType);
            recommendations.add(recommendation);
            usedCategories.add('aspect');
          }
        }
      }

      // 基於出生資料的個人化建議
      final birthMonth = birthData.dateTime.month;
      final currentMonth = DateTime.now().month;

      if (birthMonth == currentMonth && !usedCategories.contains('birthday')) {
        recommendations.add('生日月份的特殊能量支持你重新定義自己，設定全新的人生方向');
        usedCategories.add('birthday');
      }

      // 基於星象的個人化建議（避免與相位建議重複）
      final mostImportantEvent = dailyAstrology.mostImportantEvent;
      if (mostImportantEvent != null && !usedCategories.contains('lunar')) {
        switch (mostImportantEvent.type) {
          case DailyAstrologyEventType.newMoon:
            recommendations.add('新月許願的力量特別強大，為心中最重要的願望注入意圖');
            usedCategories.add('lunar');
            break;
          case DailyAstrologyEventType.fullMoon:
            recommendations.add('滿月照亮內心深處，是釋放舊模式、收穫成果的關鍵時刻');
            usedCategories.add('lunar');
            break;
          case DailyAstrologyEventType.moonSignChange:
            recommendations.add('月亮換座帶來情緒節奏的轉換，順應內在的自然流動');
            usedCategories.add('lunar');
            break;
          default:
            break;
        }
      }

      // 補充實用的生活建議（如果建議不足）
      if (recommendations.length < 3) {
        final additionalRecommendations = [
          '今日特別適合深度冥想，連結內在的智慧指引',
          '關注身體的訊號，給予自己需要的滋養和休息',
          '與信任的朋友分享內心感受，獲得情感支持',
          '記錄今日的直覺感受，這些訊息對未來很重要',
          '進行創意表達，讓內在能量通過藝術形式流動',
        ];

        for (final rec in additionalRecommendations) {
          if (recommendations.length >= 4) break;
          recommendations.add(rec);
        }
      }

      // 如果仍然沒有建議，提供基本建議
      if (recommendations.isEmpty) {
        recommendations.addAll([
          '今日適合深入內省，觀察內在的微妙變化',
          '保持心靈的開放狀態，接收宇宙的訊息',
          '信任你的直覺判斷，它會為你指引正確方向',
        ]);
      }

    } catch (e) {
      logger.w('生成個人建議失敗: $e');
      recommendations.addAll([
        '保持內心的平靜與專注，專注於當下的體驗',
        '相信自己的內在智慧，它會為你帶來正確的指引',
        '以開放而好奇的心態迎接今日的所有可能性',
      ]);
    }

    // 確保建議數量適中且無重複
    final uniqueRecommendations = recommendations.toSet().toList();
    return uniqueRecommendations.take(4).toList();
  }

  /// 格式化日期 ID
  static String _formatDateId(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }
}
