import 'package:sweph/sweph.dart';

import '../../../astreal.dart';
import '../../../features/astrology/astrology_service.dart';
import '../../../features/astrology/ephemeris/julian_date_converter.dart';
import '../../../features/astrology/ephemeris/swiss_ephemeris_wrapper.dart';
import '../../models/astrology/daily_astrology.dart';

/// 每日星相計算服務
class DailyAstrologyCalculator {
  /// 計算指定日期的每日星相
  static Future<DailyAstrologyData> calculateDailyAstrology(DateTime date) async {
    try {
      logger.i('開始計算每日星相: ${date.toIso8601String()}');
      
      // 標準化日期（只保留年月日）
      final targetDate = DateTime(date.year, date.month, date.day);
      
      // 計算各種星相事件
      final events = <DailyAstrologyEvent>[];
      
      // 1. 計算月亮換星座
      final moonSignEvents = await _calculateMoonSignChanges(targetDate);
      events.addAll(moonSignEvents);
      
      // 2. 計算行星相位
      final aspectEvents = await _calculatePlanetaryAspects(targetDate);
      events.addAll(aspectEvents);
      
      // 3. 計算月相事件
      final lunarEvents = await _calculateLunarPhases(targetDate);
      events.addAll(lunarEvents);
      
      // 4. 計算行星換星座
      final planetSignEvents = await _calculatePlanetSignChanges(targetDate);
      events.addAll(planetSignEvents);
      
      // 5. 計算逆行事件
      final retrogradeEvents = await _calculateRetrogradeEvents(targetDate);
      events.addAll(retrogradeEvents);

      // 6. 計算月亮空亡
      final voidMoonEvents = await _calculateVoidOfCourseMoon(targetDate);
      events.addAll(voidMoonEvents);

      // 按優先級和時間排序
      events.sort((a, b) {
        final priorityCompare = b.priority.compareTo(a.priority);
        if (priorityCompare != 0) return priorityCompare;
        return a.eventTime.compareTo(b.eventTime);
      });
      
      // 生成總體訊息
      final generalMessage = _generateGeneralMessage(events);
      
      // 生成各星座訊息
      final signMessages = await _generateSignMessages(targetDate, events);
      
      final dailyData = DailyAstrologyData(
        id: '${targetDate.year}-${targetDate.month.toString().padLeft(2, '0')}-${targetDate.day.toString().padLeft(2, '0')}',
        date: targetDate,
        events: events,
        generalMessage: generalMessage,
        signMessages: signMessages,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      
      logger.i('每日星相計算完成，共 ${events.length} 個事件');
      return dailyData;
    } catch (e) {
      logger.e('計算每日星相失敗: $e');
      rethrow;
    }
  }
  
  /// 計算月亮換星座事件
  static Future<List<DailyAstrologyEvent>> _calculateMoonSignChanges(DateTime date) async {
    final events = <DailyAstrologyEvent>[];
    
    try {
      // 計算當天和前一天的月亮位置
      final todayJulian = await JulianDateConverter.dateTimeToJulianDay(date, 0, 0);
      final yesterdayJulian = todayJulian - 1;
      
      final todayMoon = SwissEphemerisWrapper.calculatePlanetPosition(
        todayJulian, 
        HeavenlyBody.SE_MOON,
      );
      final yesterdayMoon = SwissEphemerisWrapper.calculatePlanetPosition(
        yesterdayJulian, 
        HeavenlyBody.SE_MOON,
      );
      
      if (todayMoon != null && yesterdayMoon != null) {
        final todaySign = _getZodiacSignFromLongitude(todayMoon.longitude);
        final yesterdaySign = _getZodiacSignFromLongitude(yesterdayMoon.longitude);
        
        if (todaySign != yesterdaySign) {
          final signName = _getZodiacSignName(todaySign);
          events.add(DailyAstrologyEvent(
            id: 'moon_sign_change_${date.millisecondsSinceEpoch}',
            type: DailyAstrologyEventType.moonSignChange,
            title: '月亮進入$signName',
            description: '月亮進入$signName，帶來新的情感能量和直覺感受。',
            eventTime: date,
            eventData: {
              'from_sign': yesterdaySign,
              'to_sign': todaySign,
              'moon_longitude': todayMoon.longitude,
            },
            priority: 4,
            tags: ['情感', '直覺', '日常'],
          ));
        }
      }
    } catch (e) {
      logger.w('計算月亮換星座失敗: $e');
    }
    
    return events;
  }
  
  /// 計算行星相位事件
  static Future<List<DailyAstrologyEvent>> _calculatePlanetaryAspects(DateTime date) async {
    final events = <DailyAstrologyEvent>[];
    
    try {
      // 計算當天的行星位置
      final planets = await AstrologyService.calculatePlanetPositions(
        date,
        0, // 緯度
        0, // 經度
      );
      
      // 檢查重要相位
      for (int i = 0; i < planets.length; i++) {
        for (int j = i + 1; j < planets.length; j++) {
          final planet1 = planets[i];
          final planet2 = planets[j];
          
          // 只檢查重要行星間的相位
          if (_isImportantPlanet(planet1.name) && _isImportantPlanet(planet2.name)) {
            final aspect = _calculateAspect(planet1.longitude, planet2.longitude);
            
            if (aspect != null) {
              events.add(DailyAstrologyEvent(
                id: 'aspect_${planet1.name}_${planet2.name}_${date.millisecondsSinceEpoch}',
                type: DailyAstrologyEventType.planetaryAspect,
                title: '${planet1.name}${aspect['symbol']}${planet2.name}',
                description: '${planet1.name}與${planet2.name}形成${aspect['name']}相位，${aspect['description']}',
                eventTime: date,
                eventData: {
                  'planet1': planet1.name,
                  'planet2': planet2.name,
                  'aspect': aspect['name'],
                  'angle': aspect['angle'],
                  'orb': aspect['orb'],
                },
                priority: aspect['priority'] as int,
                tags: aspect['tags'] as List<String>,
              ));
            }
          }
        }
      }
    } catch (e) {
      logger.w('計算行星相位失敗: $e');
    }
    
    return events;
  }
  
  /// 計算月相事件
  static Future<List<DailyAstrologyEvent>> _calculateLunarPhases(DateTime date) async {
    final events = <DailyAstrologyEvent>[];
    
    try {
      final julianDay = await JulianDateConverter.dateTimeToJulianDay(date, 0, 0);
      final moonPhase = SwissEphemerisWrapper.calculateMoonPhase(julianDay);
      
      if (moonPhase != null) {
        // 檢查是否為新月或滿月（容許度 ±2 度）
        if ((moonPhase >= 358 || moonPhase <= 2)) {
          // 新月
          events.add(DailyAstrologyEvent(
            id: 'new_moon_${date.millisecondsSinceEpoch}',
            type: DailyAstrologyEventType.newMoon,
            title: '新月',
            description: '新月時刻，適合許願、設定目標和開始新計畫。',
            eventTime: date,
            eventData: {
              'phase_angle': moonPhase,
              'phase_name': '新月',
            },
            priority: 5,
            tags: ['新開始', '許願', '目標'],
          ));
        } else if (moonPhase >= 178 && moonPhase <= 182) {
          // 滿月
          events.add(DailyAstrologyEvent(
            id: 'full_moon_${date.millisecondsSinceEpoch}',
            type: DailyAstrologyEventType.fullMoon,
            title: '滿月',
            description: '滿月時刻，能量達到高峰，適合收穫成果和釋放不需要的事物。',
            eventTime: date,
            eventData: {
              'phase_angle': moonPhase,
              'phase_name': '滿月',
            },
            priority: 5,
            tags: ['收穫', '釋放', '高峰'],
          ));
        }
      }
    } catch (e) {
      logger.w('計算月相事件失敗: $e');
    }
    
    return events;
  }
  
  /// 計算行星換星座事件
  static Future<List<DailyAstrologyEvent>> _calculatePlanetSignChanges(DateTime date) async {
    final events = <DailyAstrologyEvent>[];
    
    try {
      // 檢查重要行星的星座變化
      final importantPlanets = ['太陽', '水星', '金星', '火星', '木星', '土星'];
      
      for (final planetName in importantPlanets) {
        final todayPlanets = await AstrologyService.calculatePlanetPositions(date, 0, 0);
        final yesterdayPlanets = await AstrologyService.calculatePlanetPositions(
          date.subtract(const Duration(days: 1)), 0, 0);
        
        final todayPlanet = todayPlanets.firstWhere((p) => p.name == planetName, orElse: () => todayPlanets.first);
        final yesterdayPlanet = yesterdayPlanets.firstWhere((p) => p.name == planetName, orElse: () => yesterdayPlanets.first);
        
        final todaySign = _getZodiacSignFromLongitude(todayPlanet.longitude);
        final yesterdaySign = _getZodiacSignFromLongitude(yesterdayPlanet.longitude);
        
        if (todaySign != yesterdaySign) {
          final signName = _getZodiacSignName(todaySign);
          events.add(DailyAstrologyEvent(
            id: '${planetName}_sign_change_${date.millisecondsSinceEpoch}',
            type: DailyAstrologyEventType.planetSignChange,
            title: '$planetName進入$signName',
            description: '$planetName進入$signName，帶來新的能量和影響。',
            eventTime: date,
            eventData: {
              'planet': planetName,
              'from_sign': yesterdaySign,
              'to_sign': todaySign,
              'longitude': todayPlanet.longitude,
            },
            priority: _getPlanetPriority(planetName),
            tags: _getPlanetTags(planetName),
          ));
        }
      }
    } catch (e) {
      logger.w('計算行星換星座失敗: $e');
    }
    
    return events;
  }
  
  /// 計算逆行事件
  static Future<List<DailyAstrologyEvent>> _calculateRetrogradeEvents(DateTime date) async {
    final events = <DailyAstrologyEvent>[];
    
    try {
      // 檢查可能逆行的行星
      final retroPlanets = ['水星', '金星', '火星', '木星', '土星'];
      
      for (final planetName in retroPlanets) {
        final todayPlanets = await AstrologyService.calculatePlanetPositions(date, 0, 0);
        final yesterdayPlanets = await AstrologyService.calculatePlanetPositions(
          date.subtract(const Duration(days: 1)), 0, 0);
        
        final todayPlanet = todayPlanets.firstWhere((p) => p.name == planetName, orElse: () => todayPlanets.first);
        final yesterdayPlanet = yesterdayPlanets.firstWhere((p) => p.name == planetName, orElse: () => yesterdayPlanets.first);
        
        final todayRetro = todayPlanet.longitudeSpeed < 0;
        final yesterdayRetro = yesterdayPlanet.longitudeSpeed < 0;
        
        if (todayRetro != yesterdayRetro) {
          if (todayRetro) {
            // 開始逆行
            events.add(DailyAstrologyEvent(
              id: '${planetName}_retro_start_${date.millisecondsSinceEpoch}',
              type: DailyAstrologyEventType.retrogradeStart,
              title: '$planetName開始逆行',
              description: '$planetName開始逆行，需要重新檢視相關領域的事務。',
              eventTime: date,
              eventData: {
                'planet': planetName,
                'longitude': todayPlanet.longitude,
                'speed': todayPlanet.longitudeSpeed,
              },
              priority: _getRetrogradePriority(planetName),
              tags: ['逆行', '反思', '重新檢視'],
            ));
          } else {
            // 結束逆行
            events.add(DailyAstrologyEvent(
              id: '${planetName}_retro_end_${date.millisecondsSinceEpoch}',
              type: DailyAstrologyEventType.retrogradeEnd,
              title: '$planetName結束逆行',
              description: '$planetName結束逆行，相關事務可以重新向前推進。',
              eventTime: date,
              eventData: {
                'planet': planetName,
                'longitude': todayPlanet.longitude,
                'speed': todayPlanet.longitudeSpeed,
              },
              priority: _getRetrogradePriority(planetName),
              tags: ['逆行結束', '前進', '新開始'],
            ));
          }
        }
      }
    } catch (e) {
      logger.w('計算逆行事件失敗: $e');
    }
    
    return events;
  }
  
  /// 生成總體訊息
  static String _generateGeneralMessage(List<DailyAstrologyEvent> events) {
    if (events.isEmpty) {
      return '今天是平靜的一天，適合休息和內省。';
    }
    
    final mostImportant = events.first;
    final eventCount = events.length;
    
    String message = '今天有 $eventCount 個重要星象事件。';
    
    switch (mostImportant.type) {
      case DailyAstrologyEventType.newMoon:
        message += '新月能量帶來新的開始，適合設定目標和許願。';
        break;
      case DailyAstrologyEventType.fullMoon:
        message += '滿月能量達到高峰，適合收穫成果和釋放舊有模式。';
        break;
      case DailyAstrologyEventType.moonSignChange:
        message += '月亮換座帶來情感能量的轉換，留意內心的變化。';
        break;
      case DailyAstrologyEventType.planetaryAspect:
        message += '重要行星相位影響今日能量，注意人際關係和決策。';
        break;
      case DailyAstrologyEventType.retrogradeStart:
        message += '行星開始逆行，適合反思和重新檢視相關事務。';
        break;
      case DailyAstrologyEventType.retrogradeEnd:
        message += '逆行結束，可以重新向前推進計畫。';
        break;
      case DailyAstrologyEventType.planetSignChange:
        message += '行星換座帶來新的能量轉換，留意生活節奏的變化。';
        break;
      case DailyAstrologyEventType.moonVoidOfCourse:
        message += '月亮空亡期間，不宜做重要決定，適合休息和內省。';
        break;
      default:
        message += '宇宙能量帶來特殊的影響，保持開放的心態。';
    }
    
    return message;
  }
  
  /// 生成各星座訊息
  static Future<Map<String, String>> _generateSignMessages(
    DateTime date, 
    List<DailyAstrologyEvent> events,
  ) async {
    final messages = <String, String>{};
    
    // 為每個星座生成基本訊息
    final signs = ['牡羊座', '金牛座', '雙子座', '巨蟹座', '獅子座', '處女座',
                  '天秤座', '天蠍座', '射手座', '摩羯座', '水瓶座', '雙魚座'];
    
    for (final sign in signs) {
      messages[sign] = _generateSignSpecificMessage(sign, events);
    }
    
    return messages;
  }
  
  /// 生成星座專屬訊息
  static String _generateSignSpecificMessage(String sign, List<DailyAstrologyEvent> events) {
    // 基於星座特質和當日事件生成訊息
    final relevantEvents = events.where((event) => 
      event.tags.any((tag) => _isRelevantForSign(sign, tag))
    ).toList();
    
    if (relevantEvents.isNotEmpty) {
      final event = relevantEvents.first;
      return '${sign}今日重點：${event.description}';
    }
    
    // 預設訊息
    return '${sign}今日適合保持平常心，專注於當下的事務。';
  }
  
  // 輔助方法
  static int _getZodiacSignFromLongitude(double longitude) {
    return (longitude / 30).floor();
  }
  
  static String _getZodiacSignName(int signIndex) {
    const signs = ['牡羊座', '金牛座', '雙子座', '巨蟹座', '獅子座', '處女座',
                  '天秤座', '天蠍座', '射手座', '摩羯座', '水瓶座', '雙魚座'];
    return signs[signIndex % 12];
  }
  
  static bool _isImportantPlanet(String planetName) {
    const important = ['太陽', '月亮', '水星', '金星', '火星', '木星', '土星'];
    return important.contains(planetName);
  }
  
  static Map<String, dynamic>? _calculateAspect(double longitude1, double longitude2) {
    final diff = (longitude2 - longitude1).abs();
    final angle = diff > 180 ? 360 - diff : diff;
    
    // 檢查主要相位
    if ((angle - 0).abs() <= 3) {
      return {
        'name': '合相',
        'symbol': '☌',
        'angle': 0,
        'orb': (angle - 0).abs(),
        'description': '能量融合，影響強烈',
        'priority': 5,
        'tags': ['融合', '強化', '新開始'],
      };
    } else if ((angle - 60).abs() <= 3) {
      return {
        'name': '六分相',
        'symbol': '⚹',
        'angle': 60,
        'orb': (angle - 60).abs(),
        'description': '和諧能量，帶來機會',
        'priority': 3,
        'tags': ['機會', '和諧', '合作'],
      };
    } else if ((angle - 90).abs() <= 3) {
      return {
        'name': '四分相',
        'symbol': '□',
        'angle': 90,
        'orb': (angle - 90).abs(),
        'description': '挑戰能量，需要調整',
        'priority': 4,
        'tags': ['挑戰', '調整', '成長'],
      };
    } else if ((angle - 120).abs() <= 3) {
      return {
        'name': '三分相',
        'symbol': '△',
        'angle': 120,
        'orb': (angle - 120).abs(),
        'description': '流暢能量，事情順利',
        'priority': 4,
        'tags': ['順利', '流暢', '天賦'],
      };
    } else if ((angle - 180).abs() <= 3) {
      return {
        'name': '對沖相',
        'symbol': '☍',
        'angle': 180,
        'orb': (angle - 180).abs(),
        'description': '對立能量，需要平衡',
        'priority': 5,
        'tags': ['對立', '平衡', '整合'],
      };
    }
    
    return null;
  }
  
  static int _getPlanetPriority(String planetName) {
    switch (planetName) {
      case '太陽': return 5;
      case '月亮': return 4;
      case '水星': case '金星': case '火星': return 3;
      case '木星': case '土星': return 4;
      default: return 2;
    }
  }
  
  static List<String> _getPlanetTags(String planetName) {
    switch (planetName) {
      case '太陽': return ['自我', '活力', '領導'];
      case '月亮': return ['情感', '直覺', '家庭'];
      case '水星': return ['溝通', '思考', '學習'];
      case '金星': return ['愛情', '美感', '金錢'];
      case '火星': return ['行動', '勇氣', '競爭'];
      case '木星': return ['擴展', '幸運', '智慧'];
      case '土星': return ['責任', '限制', '成熟'];
      default: return ['變化'];
    }
  }
  
  static int _getRetrogradePriority(String planetName) {
    switch (planetName) {
      case '水星': return 4;
      case '金星': case '火星': return 3;
      case '木星': case '土星': return 2;
      default: return 1;
    }
  }
  
  static bool _isRelevantForSign(String sign, String tag) {
    // 簡化的星座標籤關聯
    final signTags = {
      '牡羊座': ['行動', '勇氣', '新開始'],
      '金牛座': ['金錢', '美感', '穩定'],
      '雙子座': ['溝通', '學習', '變化'],
      '巨蟹座': ['情感', '家庭', '直覺'],
      '獅子座': ['自我', '創造', '領導'],
      '處女座': ['分析', '服務', '完美'],
      '天秤座': ['和諧', '合作', '美感'],
      '天蠍座': ['深度', '轉化', '神秘'],
      '射手座': ['擴展', '智慧', '冒險'],
      '摩羯座': ['責任', '成就', '結構'],
      '水瓶座': ['創新', '友誼', '自由'],
      '雙魚座': ['直覺', '同情', '靈性'],
    };
    
    return signTags[sign]?.contains(tag) ?? false;
  }

  /// 計算月亮空亡事件
  static Future<List<DailyAstrologyEvent>> _calculateVoidOfCourseMoon(DateTime date) async {
    final events = <DailyAstrologyEvent>[];

    try {
      // 計算當天的月亮位置和其他行星位置
      final planets = await AstrologyService.calculatePlanetPositions(date, 0, 0);
      final moon = planets.firstWhere((p) => p.name == '月亮', orElse: () => planets.first);

      if (moon.name != '月亮') return events;

      // 檢查月亮是否在空亡狀態
      // 月亮空亡是指月亮在當前星座中不再與任何行星形成主要相位
      final moonSign = _getZodiacSignFromLongitude(moon.longitude);
      final nextSignBoundary = (moonSign + 1) * 30.0; // 下一個星座的起始度數

      // 檢查月亮到下一個星座之前是否還會與其他行星形成相位
      bool hasMoreAspects = false;
      final otherPlanets = planets.where((p) => p.name != '月亮').toList();

      for (final planet in otherPlanets) {
        // 檢查是否會形成主要相位（合相、六分相、四分相、三分相、對分相）
        final aspectAngles = [0, 60, 90, 120, 180]; // 主要相位角度

        for (final aspectAngle in aspectAngles) {
          // 計算月亮需要移動多少度才能與該行星形成相位
          final targetLongitude = (planet.longitude + aspectAngle) % 360;

          // 檢查這個相位是否會在月亮離開當前星座之前發生
          if (_isAspectWithinSign(moon.longitude, targetLongitude, nextSignBoundary)) {
            hasMoreAspects = true;
            break;
          }
        }

        if (hasMoreAspects) break;
      }

      // 如果月亮在當前星座中不再形成主要相位，則為空亡
      if (!hasMoreAspects) {
        final signName = _getZodiacSignName(moonSign);
        final voidStartTime = date; // 簡化處理，實際應該計算精確時間

        events.add(DailyAstrologyEvent(
          id: 'void_moon_${date.millisecondsSinceEpoch}',
          type: DailyAstrologyEventType.moonVoidOfCourse,
          title: '月亮空亡',
          description: '月亮在$signName座空亡，不宜做重要決定，適合休息和反思。',
          eventTime: voidStartTime,
          eventData: {
            'moon_sign': moonSign,
            'moon_longitude': moon.longitude,
            'sign_name': signName,
            'next_sign_boundary': nextSignBoundary,
          },
          priority: 3,
          tags: ['謹慎', '反思', '等待'],
        ));
      }
    } catch (e) {
      logger.w('計算月亮空亡失敗: $e');
    }

    return events;
  }

  /// 檢查相位是否會在月亮離開當前星座之前發生
  static bool _isAspectWithinSign(double moonLongitude, double targetLongitude, double signBoundary) {
    // 簡化計算：檢查目標經度是否在月亮當前位置和星座邊界之間
    if (targetLongitude > moonLongitude && targetLongitude < signBoundary) {
      return true;
    }

    // 處理跨越0度的情況
    if (signBoundary < moonLongitude) {
      if (targetLongitude > moonLongitude || targetLongitude < signBoundary) {
        return true;
      }
    }

    return false;
  }
}
