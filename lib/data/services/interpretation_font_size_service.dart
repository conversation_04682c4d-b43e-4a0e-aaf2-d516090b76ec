import 'package:shared_preferences/shared_preferences.dart';

import '../../core/utils/logger_utils.dart';

/// 字體大小選項
enum InterpretationFontSize {
  small('小', 'small'),
  medium('中', 'medium'),
  large('大', 'large');

  const InterpretationFontSize(this.displayName, this.value);

  final String displayName;
  final String value;

  /// 從字串值轉換為枚舉
  static InterpretationFontSize fromValue(String value) {
    switch (value) {
      case 'small':
        return InterpretationFontSize.small;
      case 'medium':
        return InterpretationFontSize.medium;
      case 'large':
        return InterpretationFontSize.large;
      default:
        return InterpretationFontSize.medium; // 預設為中等大小
    }
  }
}

/// 解讀內容字體大小服務
/// 管理用戶對解讀內容字體大小的偏好設定
class InterpretationFontSizeService {
  static const String _fontSizeKey = 'interpretation_font_size';
  
  /// 字體大小配置
  static const Map<InterpretationFontSize, Map<String, double>> _fontSizeConfig = {
    InterpretationFontSize.small: {
      'paragraph': 13.0,
      'h1': 18.0,
      'h2': 16.0,
      'h3': 14.0,
      'blockquote': 12.0,
      'code': 11.0,
      'listBullet': 13.0,
      'lineHeight': 1.5,
    },
    InterpretationFontSize.medium: {
      'paragraph': 15.0,
      'h1': 20.0,
      'h2': 18.0,
      'h3': 16.0,
      'blockquote': 14.0,
      'code': 13.0,
      'listBullet': 15.0,
      'lineHeight': 1.6,
    },
    InterpretationFontSize.large: {
      'paragraph': 17.0,
      'h1': 22.0,
      'h2': 20.0,
      'h3': 18.0,
      'blockquote': 16.0,
      'code': 15.0,
      'listBullet': 17.0,
      'lineHeight': 1.7,
    },
  };
  
  /// 獲取當前字體大小設定
  static Future<InterpretationFontSize> getCurrentFontSize() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final value = prefs.getString(_fontSizeKey) ?? InterpretationFontSize.medium.value;
      final fontSize = InterpretationFontSize.fromValue(value);
      logger.d('獲取解讀內容字體大小: ${fontSize.displayName}');
      return fontSize;
    } catch (e) {
      logger.e('獲取解讀內容字體大小失敗: $e');
      return InterpretationFontSize.medium;
    }
  }

  /// 設定字體大小
  static Future<bool> setFontSize(InterpretationFontSize fontSize) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final result = await prefs.setString(_fontSizeKey, fontSize.value);
      logger.i('設定解讀內容字體大小: ${fontSize.displayName}');
      return result;
    } catch (e) {
      logger.e('設定解讀內容字體大小失敗: $e');
      return false;
    }
  }

  /// 獲取字體大小配置
  static Map<String, double> getFontSizeConfig(InterpretationFontSize fontSize) {
    return _fontSizeConfig[fontSize] ?? _fontSizeConfig[InterpretationFontSize.medium]!;
  }
  
  /// 獲取段落字體大小
  static double getParagraphFontSize(InterpretationFontSize fontSize) {
    return getFontSizeConfig(fontSize)['paragraph'] ?? 15.0;
  }

  /// 獲取標題1字體大小
  static double getH1FontSize(InterpretationFontSize fontSize) {
    return getFontSizeConfig(fontSize)['h1'] ?? 20.0;
  }

  /// 獲取標題2字體大小
  static double getH2FontSize(InterpretationFontSize fontSize) {
    return getFontSizeConfig(fontSize)['h2'] ?? 18.0;
  }

  /// 獲取標題3字體大小
  static double getH3FontSize(InterpretationFontSize fontSize) {
    return getFontSizeConfig(fontSize)['h3'] ?? 16.0;
  }

  /// 獲取引用字體大小
  static double getBlockquoteFontSize(InterpretationFontSize fontSize) {
    return getFontSizeConfig(fontSize)['blockquote'] ?? 14.0;
  }

  /// 獲取程式碼字體大小
  static double getCodeFontSize(InterpretationFontSize fontSize) {
    return getFontSizeConfig(fontSize)['code'] ?? 13.0;
  }

  /// 獲取列表項目字體大小
  static double getListBulletFontSize(InterpretationFontSize fontSize) {
    return getFontSizeConfig(fontSize)['listBullet'] ?? 15.0;
  }

  /// 獲取行高
  static double getLineHeight(InterpretationFontSize fontSize) {
    return getFontSizeConfig(fontSize)['lineHeight'] ?? 1.6;
  }

  /// 獲取所有可用的字體大小選項
  static List<InterpretationFontSize> getAllFontSizes() {
    return InterpretationFontSize.values;
  }

  /// 獲取下一個字體大小（循環）
  static InterpretationFontSize getNextFontSize(InterpretationFontSize currentSize) {
    final sizes = InterpretationFontSize.values;
    final currentIndex = sizes.indexOf(currentSize);
    final nextIndex = (currentIndex + 1) % sizes.length;
    return sizes[nextIndex];
  }

  /// 重置為預設字體大小
  static Future<bool> resetToDefault() async {
    return await setFontSize(InterpretationFontSize.medium);
  }
}
