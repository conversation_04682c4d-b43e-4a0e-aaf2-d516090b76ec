import 'package:cloud_firestore/cloud_firestore.dart';

import '../../astreal.dart';
import '../models/user/user_analysis.dart';

/// 用戶分析服務
class UserAnalysisService {
  static const String _userProfilesCollection = 'user_profiles';
  static const String _userAnalysisSubCollection = 'user_analysis';
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// 獲取用戶的分析結果
  static Future<UserAnalysis?> getUserAnalysis(String userId, String birthDataId) async {
    try {
      final querySnapshot = await _firestore
          .collection(_userProfilesCollection)
          .doc(userId)
          .collection(_userAnalysisSubCollection)
          .where('birth_data_id', isEqualTo: birthDataId)
          .orderBy('created_at', descending: true)
          .limit(1)
          .get();

      if (querySnapshot.docs.isNotEmpty) {
        final doc = querySnapshot.docs.first;
        return UserAnalysis.fromJson({
          'id': doc.id,
          ...doc.data(),
        });
      }

      return null;
    } catch (e) {
      logger.e('獲取用戶分析結果失敗: $e');
      return null;
    }
  }

  /// 獲取用戶的所有分析結果
  static Future<List<UserAnalysis>> getUserAnalysisList(String userId) async {
    try {
      final querySnapshot = await _firestore
          .collection(_userProfilesCollection)
          .doc(userId)
          .collection(_userAnalysisSubCollection)
          .orderBy('created_at', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => UserAnalysis.fromJson({
                'id': doc.id,
                ...doc.data(),
              }))
          .toList();
    } catch (e) {
      logger.e('獲取用戶分析結果列表失敗: $e');
      return [];
    }
  }

  /// 保存分析結果
  static Future<String?> saveAnalysis(UserAnalysis analysis) async {
    try {
      final docRef = await _firestore
          .collection(_userProfilesCollection)
          .doc(analysis.userId)
          .collection(_userAnalysisSubCollection)
          .add(analysis.toFirestoreJson());

      logger.i('分析結果保存成功: ${docRef.id}');
      return docRef.id;
    } catch (e) {
      logger.e('保存分析結果失敗: $e');
      return null;
    }
  }

  /// 更新分析結果
  static Future<bool> updateAnalysis(String userId, String analysisId, UserAnalysis analysis) async {
    try {
      await _firestore
          .collection(_userProfilesCollection)
          .doc(userId)
          .collection(_userAnalysisSubCollection)
          .doc(analysisId)
          .update(analysis.toFirestoreJson(isUpdate: true));

      logger.i('更新分析結果成功: $analysisId');
      return true;
    } catch (e) {
      logger.e('更新分析結果失敗: $e');
      return false;
    }
  }

  /// 創建新的分析結果
  static Future<String?> createAnalysis({
    required String userId,
    required String birthDataId,
    required String selfIntroduction,
    required String strengths,
    required String weaknesses,
    required String favoriteTopics,
    required String socialPreferences,
    String analysisVersion = '1.0',
  }) async {
    try {
      final analysis = UserAnalysis(
        id: '', // Firestore 會自動生成
        userId: userId,
        birthDataId: birthDataId,
        selfIntroduction: selfIntroduction,
        strengths: strengths,
        weaknesses: weaknesses,
        favoriteTopics: favoriteTopics,
        socialPreferences: socialPreferences,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        analysisVersion: analysisVersion,
      );

      return await saveAnalysis(analysis);
    } catch (e) {
      logger.e('創建分析結果失敗: $e');
      return null;
    }
  }

  /// 刪除分析結果
  static Future<bool> deleteAnalysis(String userId, String analysisId) async {
    try {
      await _firestore
          .collection(_userProfilesCollection)
          .doc(userId)
          .collection(_userAnalysisSubCollection)
          .doc(analysisId)
          .delete();

      logger.i('刪除分析結果成功: $analysisId');
      return true;
    } catch (e) {
      logger.e('刪除分析結果失敗: $e');
      return false;
    }
  }

  /// 刪除用戶的所有分析結果
  static Future<bool> deleteUserAnalyses(String userId) async {
    try {
      final querySnapshot = await _firestore
          .collection(_userProfilesCollection)
          .doc(userId)
          .collection(_userAnalysisSubCollection)
          .get();

      final batch = _firestore.batch();
      for (final doc in querySnapshot.docs) {
        batch.delete(doc.reference);
      }

      await batch.commit();
      logger.i('刪除用戶所有分析結果成功: $userId');
      return true;
    } catch (e) {
      logger.e('刪除用戶所有分析結果失敗: $e');
      return false;
    }
  }

  /// 檢查是否存在分析結果
  static Future<bool> hasAnalysis(String userId, String birthDataId) async {
    try {
      final querySnapshot = await _firestore
          .collection(_userProfilesCollection)
          .doc(userId)
          .collection(_userAnalysisSubCollection)
          .where('birth_data_id', isEqualTo: birthDataId)
          .limit(1)
          .get();

      return querySnapshot.docs.isNotEmpty;
    } catch (e) {
      logger.e('檢查分析結果存在性失敗: $e');
      return false;
    }
  }

  /// 獲取用戶分析結果數量
  static Future<int> getUserAnalysisCount(String userId) async {
    try {
      final querySnapshot = await _firestore
          .collection(_userProfilesCollection)
          .doc(userId)
          .collection(_userAnalysisSubCollection)
          .count()
          .get();

      return querySnapshot.count ?? 0;
    } catch (e) {
      logger.e('獲取用戶分析結果數量失敗: $e');
      return 0;
    }
  }

  /// 根據出生資料 ID 刪除相關分析結果（需要遍歷所有用戶）
  static Future<bool> deleteAnalysesByBirthDataId(String birthDataId) async {
    try {
      // 由於現在分析結果存在用戶子集合中，需要遍歷所有用戶
      // 這個方法的效率較低，建議在實際使用中考慮其他方案
      logger.w('deleteAnalysesByBirthDataId 方法在新架構下效率較低，建議重新設計');

      // 暫時返回 true，實際實作需要根據具體需求調整
      return true;
    } catch (e) {
      logger.e('根據出生資料 ID 刪除分析結果失敗: $e');
      return false;
    }
  }
}
