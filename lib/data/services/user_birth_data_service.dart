import 'package:cloud_firestore/cloud_firestore.dart';

import '../../astreal.dart';
import '../models/user/user_birth_data.dart';

/// 用戶出生資料服務
class UserBirthDataService {
  static const String _collectionName = 'user_birth_data';
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// 獲取用戶的所有出生資料
  static Future<List<UserBirthData>> getUserBirthDataList(String userId) async {
    try {
      final querySnapshot = await _firestore
          .collection(_collectionName)
          .where('user_id', isEqualTo: userId)
          .orderBy('created_at', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => UserBirthData.fromJson({
                'id': doc.id,
                ...doc.data(),
              }))
          .toList();
    } catch (e) {
      logger.e('獲取用戶出生資料列表失敗: $e');
      return [];
    }
  }

  /// 獲取用戶當前選中的出生資料
  static Future<UserBirthData?> getSelectedBirthData(String userId) async {
    try {
      final querySnapshot = await _firestore
          .collection(_collectionName)
          .where('user_id', isEqualTo: userId)
          .where('is_selected', isEqualTo: true)
          .limit(1)
          .get();

      if (querySnapshot.docs.isNotEmpty) {
        final doc = querySnapshot.docs.first;
        return UserBirthData.fromJson({
          'id': doc.id,
          ...doc.data(),
        });
      }

      return null;
    } catch (e) {
      logger.e('獲取用戶選中的出生資料失敗: $e');
      return null;
    }
  }

  /// 根據 ID 獲取出生資料
  static Future<UserBirthData?> getBirthDataById(String birthDataId) async {
    try {
      final doc = await _firestore
          .collection(_collectionName)
          .doc(birthDataId)
          .get();

      if (doc.exists) {
        return UserBirthData.fromJson({
          'id': doc.id,
          ...doc.data()!,
        });
      }

      return null;
    } catch (e) {
      logger.e('根據 ID 獲取出生資料失敗: $e');
      return null;
    }
  }

  /// 保存出生資料到用戶集合
  static Future<String?> saveBirthData(String userId, BirthData birthData) async {
    try {
      final userBirthData = UserBirthData(
        id: '', // Firestore 會自動生成
        userId: userId,
        birthData: birthData,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        isSelected: false, // 預設不選中
      );

      final docRef = await _firestore
          .collection(_collectionName)
          .add(userBirthData.toFirestoreJson());

      logger.i('出生資料保存成功: ${docRef.id}');
      return docRef.id;
    } catch (e) {
      logger.e('保存出生資料失敗: $e');
      return null;
    }
  }

  /// 設定選中的出生資料
  static Future<bool> setSelectedBirthData(String userId, String birthDataId) async {
    try {
      final batch = _firestore.batch();

      // 1. 先將該用戶的所有出生資料設為未選中
      final userBirthDataQuery = await _firestore
          .collection(_collectionName)
          .where('user_id', isEqualTo: userId)
          .get();

      for (final doc in userBirthDataQuery.docs) {
        batch.update(doc.reference, {'is_selected': false});
      }

      // 2. 將指定的出生資料設為選中
      final selectedDocRef = _firestore
          .collection(_collectionName)
          .doc(birthDataId);
      
      batch.update(selectedDocRef, {
        'is_selected': true,
        'updated_at': FieldValue.serverTimestamp(),
      });

      await batch.commit();
      logger.i('設定選中的出生資料成功: $birthDataId');
      return true;
    } catch (e) {
      logger.e('設定選中的出生資料失敗: $e');
      return false;
    }
  }

  /// 更新出生資料
  static Future<bool> updateBirthData(String birthDataId, BirthData birthData) async {
    try {
      await _firestore
          .collection(_collectionName)
          .doc(birthDataId)
          .update({
        'birth_data': birthData.toJson(),
        'updated_at': FieldValue.serverTimestamp(),
      });

      logger.i('更新出生資料成功: $birthDataId');
      return true;
    } catch (e) {
      logger.e('更新出生資料失敗: $e');
      return false;
    }
  }

  /// 刪除出生資料
  static Future<bool> deleteBirthData(String birthDataId) async {
    try {
      await _firestore
          .collection(_collectionName)
          .doc(birthDataId)
          .delete();

      logger.i('刪除出生資料成功: $birthDataId');
      return true;
    } catch (e) {
      logger.e('刪除出生資料失敗: $e');
      return false;
    }
  }

  /// 從本地出生資料創建用戶出生資料
  static Future<String?> createFromLocalBirthData(String userId, BirthData localBirthData) async {
    try {
      // 創建新的用戶出生資料
      final birthDataId = await saveBirthData(userId, localBirthData);
      
      if (birthDataId != null) {
        // 設為選中狀態
        await setSelectedBirthData(userId, birthDataId);
        return birthDataId;
      }
      
      return null;
    } catch (e) {
      logger.e('從本地出生資料創建用戶出生資料失敗: $e');
      return null;
    }
  }

  /// 獲取用戶出生資料數量
  static Future<int> getUserBirthDataCount(String userId) async {
    try {
      final querySnapshot = await _firestore
          .collection(_collectionName)
          .where('user_id', isEqualTo: userId)
          .count()
          .get();

      return querySnapshot.count ?? 0;
    } catch (e) {
      logger.e('獲取用戶出生資料數量失敗: $e');
      return 0;
    }
  }
}
