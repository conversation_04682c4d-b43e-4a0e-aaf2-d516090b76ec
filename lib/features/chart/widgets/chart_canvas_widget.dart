import 'dart:math' as math;

import 'package:flutter/material.dart';

import '../core/chart_display_controller.dart';

/// 星盤畫布 Widget
/// 
/// 負責顯示星盤圖，支援縮放、平移等互動功能
class ChartCanvasWidget extends StatefulWidget {
  final ChartDisplayController controller;

  const ChartCanvasWidget({
    super.key,
    required this.controller,
  });

  @override
  State<ChartCanvasWidget> createState() => _ChartCanvasWidgetState();
}

class _ChartCanvasWidgetState extends State<ChartCanvasWidget> {
  final TransformationController _transformationController = TransformationController();

  @override
  void initState() {
    super.initState();
    _updateTransformation();
  }

  @override
  void didUpdateWidget(ChartCanvasWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.controller != oldWidget.controller) {
      _updateTransformation();
    }
  }

  @override
  void dispose() {
    _transformationController.dispose();
    super.dispose();
  }

  /// 更新變換矩陣
  void _updateTransformation() {
    final zoom = widget.controller.zoomLevel;
    final pan = widget.controller.panOffset;
    
    _transformationController.value = Matrix4.identity()
      ..translate(pan.dx, pan.dy)
      ..scale(zoom);
  }

  @override
  Widget build(BuildContext context) {
    final controller = widget.controller;
    final settings = controller.settings.chartCanvas;
    
    return Container(
      width: double.infinity,
      height: double.infinity,
      padding: controller.theme.padding,
      child: _buildChartContent(controller, settings),
    );
  }

  /// 構建星盤內容
  Widget _buildChartContent(ChartDisplayController controller, dynamic settings) {
    Widget chartWidget = _buildChart(controller);
    
    // 如果啟用縮放或平移，包裝在 InteractiveViewer 中
    if (controller.config.enableZoom || controller.config.enableDrag) {
      chartWidget = InteractiveViewer(
        transformationController: _transformationController,
        minScale: 0.5,
        maxScale: 3.0,
        onInteractionUpdate: (details) {
          _handleInteraction(controller, details);
        },
        child: chartWidget,
      );
    }
    
    return Center(
      child: AspectRatio(
        aspectRatio: 1.0,
        child: chartWidget,
      ),
    );
  }

  /// 構建星盤圖
  Widget _buildChart(ChartDisplayController controller) {
    // 使用現有的 ChartViewWidget 作為基礎
    // 這裡需要創建一個適配器來橋接新舊系統
    return _ChartViewAdapter(
      controller: controller,
      onPlanetTap: (planet) => controller.handlePlanetTap(planet),
    );
  }

  /// 處理互動事件
  void _handleInteraction(ChartDisplayController controller, ScaleUpdateDetails details) {
    // 更新控制器的縮放和平移狀態
    final matrix = _transformationController.value;
    final zoom = matrix.getMaxScaleOnAxis();
    final translation = matrix.getTranslation();
    
    controller.setZoomLevel(zoom);
    controller.setPanOffset(Offset(translation.x, translation.y));
  }
}

/// 星盤視圖適配器
/// 
/// 將現有的 ChartViewWidget 適配到新的模組系統中
class _ChartViewAdapter extends StatelessWidget {
  final ChartDisplayController controller;
  final Function(dynamic planet)? onPlanetTap;

  const _ChartViewAdapter({
    required this.controller,
    this.onPlanetTap,
  });

  @override
  Widget build(BuildContext context) {
    // 這裡需要創建一個簡化的星盤視圖
    // 暫時使用佔位符，實際實現時需要整合現有的繪製邏輯
    return Container(
      decoration: BoxDecoration(
        border: Border.all(
          color: controller.theme.borderColor,
          width: 2,
        ),
        borderRadius: BorderRadius.circular(controller.theme.borderRadius),
        color: Colors.white, // 強制使用白色背景
      ),
      child: CustomPaint(
        painter: _SimplifiedChartPainter(
          controller: controller,
          onPlanetTap: onPlanetTap,
        ),
        child: Container(),
      ),
    );
  }
}

/// 簡化的星盤繪製器
/// 
/// 提供基本的星盤繪製功能，可以根據需要擴展
class _SimplifiedChartPainter extends CustomPainter {
  final ChartDisplayController controller;
  final Function(dynamic planet)? onPlanetTap;

  _SimplifiedChartPainter({
    required this.controller,
    this.onPlanetTap,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2 - 20;
    
    // 繪製外圓
    _drawOuterCircle(canvas, center, radius);
    
    // 繪製宮位線
    _drawHouseLines(canvas, center, radius);
    
    // 繪製行星
    _drawPlanets(canvas, center, radius);
    
    // 繪製相位線
    if (controller.settings.chartCanvas.showAspectLines) {
      _drawAspectLines(canvas, center, radius);
    }
  }

  /// 繪製外圓
  void _drawOuterCircle(Canvas canvas, Offset center, double radius) {
    final paint = Paint()
      ..color = controller.theme.borderColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;
    
    canvas.drawCircle(center, radius, paint);
  }

  /// 繪製宮位線
  void _drawHouseLines(Canvas canvas, Offset center, double radius) {
    final paint = Paint()
      ..color = controller.theme.dividerColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1;
    
    // 繪製 12 條宮位線
    for (int i = 0; i < 12; i++) {
      final angle = (i * 30) * (3.14159 / 180); // 轉換為弧度
      final startPoint = Offset(
        center.dx + (radius * 0.8) * math.cos(angle),
        center.dy + (radius * 0.8) * math.sin(angle),
      );
      final endPoint = Offset(
        center.dx + radius * math.cos(angle),
        center.dy + radius * math.sin(angle),
      );
      
      canvas.drawLine(startPoint, endPoint, paint);
      
      // 繪製宮位數字
      if (controller.settings.chartCanvas.showHouseNumbers) {
        _drawHouseNumber(canvas, center, radius, i + 1, angle);
      }
    }
  }

  /// 繪製宮位數字
  void _drawHouseNumber(Canvas canvas, Offset center, double radius, int houseNumber, double angle) {
    final textPainter = TextPainter(
      text: TextSpan(
        text: houseNumber.toString(),
        style: controller.theme.labelTextStyle,
      ),
      textDirection: TextDirection.ltr,
    );
    
    textPainter.layout();
    
    final textCenter = Offset(
      center.dx + (radius * 0.9) * math.cos(angle) - textPainter.width / 2,
      center.dy + (radius * 0.9) * math.sin(angle) - textPainter.height / 2,
    );
    
    textPainter.paint(canvas, textCenter);
  }

  /// 繪製行星
  void _drawPlanets(Canvas canvas, Offset center, double radius) {
    final planets = controller.displayData.planets;
    
    for (int i = 0; i < planets.length; i++) {
      final planet = planets[i];
      final angle = (planet.longitude) * (3.14159 / 180);
      
      // 行星位置
      final planetCenter = Offset(
        center.dx + (radius * 0.7) * math.cos(angle),
        center.dy + (radius * 0.7) * math.sin(angle),
      );
      
      // 繪製行星圓點
      final planetPaint = Paint()
        ..color = controller.theme.primaryColor
        ..style = PaintingStyle.fill;
      
      canvas.drawCircle(planetCenter, 6, planetPaint);
      
      // 繪製行星符號或名稱
      if (controller.settings.chartCanvas.showPlanetSymbols || 
          controller.settings.chartCanvas.showPlanetNames) {
        _drawPlanetLabel(canvas, planetCenter, planet);
      }
    }
  }

  /// 繪製行星標籤
  void _drawPlanetLabel(Canvas canvas, Offset planetCenter, dynamic planet) {
    final text = controller.settings.chartCanvas.showPlanetNames 
        ? planet.name 
        : planet.symbol ?? planet.name;
    
    final textPainter = TextPainter(
      text: TextSpan(
        text: text,
        style: controller.theme.labelTextStyle,
      ),
      textDirection: TextDirection.ltr,
    );
    
    textPainter.layout();
    
    final textPosition = Offset(
      planetCenter.dx - textPainter.width / 2,
      planetCenter.dy + 10,
    );
    
    textPainter.paint(canvas, textPosition);
  }

  /// 繪製相位線
  void _drawAspectLines(Canvas canvas, Offset center, double radius) {
    final aspects = controller.displayData.aspects;

    if (aspects.isEmpty) return;

    final aspectPaint = Paint()
      ..color = controller.theme.secondaryColor.withValues(alpha: 0.5)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1;

    // 使用 aspectPaint 繪製相位線
    // 這裡需要根據實際的相位數據結構來繪製相位線
    // 暫時跳過具體實現，但保留 aspectPaint 的使用
    canvas.drawLine(
      Offset(center.dx - radius * 0.1, center.dy),
      Offset(center.dx + radius * 0.1, center.dy),
      aspectPaint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}
