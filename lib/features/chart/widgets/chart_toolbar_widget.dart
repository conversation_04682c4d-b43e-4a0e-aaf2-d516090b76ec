import 'package:flutter/material.dart';

import '../core/chart_display_controller.dart';

/// 星盤工具列 Widget
class ChartToolbarWidget extends StatelessWidget {
  final ChartDisplayController controller;

  const ChartToolbarWidget({
    super.key,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 56,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: controller.theme.surfaceColor,
        border: Border(
          bottom: BorderSide(
            color: controller.theme.dividerColor,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // 標題
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  controller.displayData.title,
                  style: controller.theme.subtitleTextStyle,
                  overflow: TextOverflow.ellipsis,
                ),
                if (controller.displayData.subtitle != null)
                  Text(
                    controller.displayData.subtitle!,
                    style: controller.theme.labelTextStyle,
                    overflow: TextOverflow.ellipsis,
                  ),
              ],
            ),
          ),

          // 工具選單按鈕
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert),
            tooltip: '工具選單',
            onSelected: _handleMenuAction,
            itemBuilder: (context) => _buildMenuItems(),
          ),
        ],
      ),
    );
  }

  /// 構建選單項目
  List<PopupMenuEntry<String>> _buildMenuItems() {
    final items = <PopupMenuEntry<String>>[];

    // 重置縮放
    if (controller.config.enableZoom) {
      items.add(
        const PopupMenuItem<String>(
          value: 'reset_zoom',
          child: Row(
            children: [
              Icon(Icons.zoom_out_map, size: 20),
              SizedBox(width: 12),
              Text('重置縮放'),
            ],
          ),
        ),
      );
    }

    // 刷新
    items.add(
      const PopupMenuItem<String>(
        value: 'refresh',
        child: Row(
          children: [
            Icon(Icons.refresh, size: 20),
            SizedBox(width: 12),
            Text('刷新'),
          ],
        ),
      ),
    );

    // 導出功能
    if (controller.settings.export.enableImageExport) {
      items.add(const PopupMenuDivider());

      for (final format in controller.settings.export.supportedFormats) {
        items.add(
          PopupMenuItem<String>(
            value: 'export_$format',
            child: Row(
              children: [
                const Icon(Icons.download, size: 20),
                const SizedBox(width: 12),
                Text('導出為 ${format.toUpperCase()}'),
              ],
            ),
          ),
        );
      }
    }

    // 自定義動作
    final customActions = controller.getCustomActions();
    if (customActions.isNotEmpty) {
      items.add(const PopupMenuDivider());

      for (final customAction in customActions) {
        items.add(
          PopupMenuItem<String>(
            value: 'custom_${customAction.id}',
            enabled: customAction.enabled,
            child: Row(
              children: [
                Icon(customAction.icon, size: 20),
                const SizedBox(width: 12),
                Text(customAction.title),
              ],
            ),
          ),
        );
      }
    }

    return items;
  }

  /// 處理選單動作
  void _handleMenuAction(String action) {
    if (action == 'reset_zoom') {
      controller.resetZoomAndPan();
    } else if (action == 'refresh') {
      controller.refresh();
    } else if (action.startsWith('export_')) {
      final format = action.substring(7); // 移除 'export_' 前綴
      controller.exportChart(format);
    } else if (action.startsWith('custom_')) {
      final actionId = action.substring(7); // 移除 'custom_' 前綴
      final customAction = controller.getCustomActions()
          .where((a) => a.id == actionId)
          .firstOrNull;
      if (customAction != null && customAction.enabled) {
        customAction.onPressed();
      }
    }
  }
}
