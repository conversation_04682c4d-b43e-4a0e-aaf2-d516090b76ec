import 'package:flutter/material.dart';

import '../core/chart_display_controller.dart';

/// 星盤資訊面板 Widget
class ChartInfoPanel extends StatelessWidget {
  final ChartDisplayController controller;

  const ChartInfoPanel({
    super.key,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: controller.theme.padding,
      decoration: BoxDecoration(
        color: controller.theme.surfaceColor,
        border: Border(
          top: BorderSide(
            color: controller.theme.dividerColor,
            width: 1,
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // 基本資訊
          _buildBasicInfo(),
          
          // 統計資訊
          if (controller.displayData.isValid) ...[
            const SizedBox(height: 8),
            _buildStatistics(),
          ],
          
          // 描述
          if (controller.displayData.description != null) ...[
            const SizedBox(height: 8),
            Text(
              controller.displayData.description!,
              style: controller.theme.bodyTextStyle,
            ),
          ],
        ],
      ),
    );
  }

  /// 構建基本資訊
  Widget _buildBasicInfo() {
    return Row(
      children: [
        // 星盤類型圖標
        Icon(
          _getChartTypeIcon(),
          color: controller.theme.primaryColor,
          size: 20,
        ),
        const SizedBox(width: 8),
        
        // 星盤類型名稱
        Text(
          controller.displayData.chartType.name,
          style: controller.theme.labelTextStyle.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        
        const Spacer(),
        
        // 狀態指示器
        _buildStatusIndicator(),
      ],
    );
  }

  /// 構建統計資訊
  Widget _buildStatistics() {
    final planets = controller.displayData.planets;
    final aspects = controller.displayData.aspects;
    
    return Row(
      children: [
        _buildStatItem('行星', planets.length.toString()),
        const SizedBox(width: 16),
        _buildStatItem('相位', aspects.length.toString()),
        const SizedBox(width: 16),
        _buildStatItem('宮位', '12'),
      ],
    );
  }

  /// 構建統計項目
  Widget _buildStatItem(String label, String value) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          label,
          style: controller.theme.labelTextStyle,
        ),
        const SizedBox(width: 4),
        Text(
          value,
          style: controller.theme.labelTextStyle.copyWith(
            fontWeight: FontWeight.w600,
            color: controller.theme.primaryColor,
          ),
        ),
      ],
    );
  }

  /// 構建狀態指示器
  Widget _buildStatusIndicator() {
    if (controller.isLoading) {
      return SizedBox(
        width: 16,
        height: 16,
        child: CircularProgressIndicator(
          strokeWidth: 2,
          color: controller.theme.primaryColor,
        ),
      );
    }
    
    if (controller.error != null) {
      return Icon(
        Icons.error_outline,
        color: controller.theme.errorColor,
        size: 16,
      );
    }
    
    if (controller.displayData.isValid) {
      return Icon(
        Icons.check_circle_outline,
        color: controller.theme.successColor,
        size: 16,
      );
    }
    
    return Icon(
      Icons.help_outline,
      color: controller.theme.secondaryTextColor,
      size: 16,
    );
  }

  /// 獲取星盤類型圖標
  IconData _getChartTypeIcon() {
    switch (controller.displayData.chartType.name) {
      case '本命盤':
        return Icons.person;
      case '合盤':
        return Icons.favorite;
      case '推運盤':
        return Icons.timeline;
      case '法達盤':
        return Icons.hourglass_full;
      default:
        return Icons.circle_outlined;
    }
  }
}
