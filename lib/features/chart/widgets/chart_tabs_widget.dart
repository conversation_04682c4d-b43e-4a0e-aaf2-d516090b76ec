import 'package:flutter/material.dart';

import '../core/chart_display_controller.dart';

/// 星盤標籤頁 Widget
class ChartTabsWidget extends StatefulWidget {
  final ChartDisplayController controller;
  final TabController tabController;

  const ChartTabsWidget({
    super.key,
    required this.controller,
    required this.tabController,
  });

  @override
  State<ChartTabsWidget> createState() => _ChartTabsWidgetState();
}

class _ChartTabsWidgetState extends State<ChartTabsWidget> {
  int? _previousIndex;

  @override
  void initState() {
    super.initState();
    _previousIndex = widget.tabController.index;
    widget.tabController.addListener(_onTabChanged);
  }

  @override
  void dispose() {
    widget.tabController.removeListener(_onTabChanged);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: widget.controller.theme.primaryColor,
        border: Border(
          bottom: BorderSide(
            color: widget.controller.theme.dividerColor,
            width: 1,
          ),
        ),
      ),
      child: Tab<PERSON><PERSON>(
        controller: widget.tabController,
        isScrollable: true,
        labelColor: widget.controller.theme.surfaceColor,
        unselectedLabelColor: widget.controller.theme.unselectedLabelColor,
        indicatorColor: widget.controller.theme.secondaryColor,
        labelStyle: widget.controller.theme.labelTextStyle,
        tabs: widget.controller.enabledTabs.map((tabType) {
          return Tab(
            icon: Icon(tabType.icon),
            text: tabType.title,
          );
        }).toList(),
      ),
    );
  }

  /// 處理標籤頁切換
  void _onTabChanged() {
    if (!widget.tabController.indexIsChanging) return;

    final newIndex = widget.tabController.index;

    // 檢查出生時間是否不確定
    if (_hasTimeUncertainty()) {
      // 阻止切換，恢復到之前的標籤頁
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (_previousIndex != null && mounted) {
          widget.tabController.animateTo(_previousIndex!);
        }
      });

      // 顯示警告訊息
      _showTimeUncertaintyWarning(context);
      return;
    }

    _previousIndex = newIndex;
  }

  /// 檢查是否有時間不確定的情況
  bool _hasTimeUncertainty() {
    final chartViewModel = widget.controller.config.chartViewModel;
    if (chartViewModel == null) return false;

    final primaryUncertain = chartViewModel.primaryPerson.isTimeUncertain;
    final secondaryUncertain = chartViewModel.secondaryPerson?.isTimeUncertain ?? false;
    return primaryUncertain || secondaryUncertain;
  }

  /// 顯示時間不確定警告對話框
  void _showTimeUncertaintyWarning(BuildContext context) {
    final chartViewModel = widget.controller.config.chartViewModel;
    if (chartViewModel == null) return;

    final primaryUncertain = chartViewModel.primaryPerson.isTimeUncertain;
    final secondaryUncertain = chartViewModel.secondaryPerson?.isTimeUncertain ?? false;

    String warningText;
    if (primaryUncertain && secondaryUncertain) {
      warningText = '兩位人物的出生時間都不確定，無法切換分析標籤。\n請先確認出生時間後再進行切換。';
    } else if (primaryUncertain) {
      warningText = '主要人物的出生時間不確定，無法切換分析標籤。\n請先確認出生時間後再進行切換。';
    } else {
      warningText = '次要人物的出生時間不確定，無法切換分析標籤。\n請先確認出生時間後再進行切換。';
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              Icons.warning_amber_rounded,
              color: Colors.orange[700],
              size: 24,
            ),
            const SizedBox(width: 8),
            const Text('時間不確定'),
          ],
        ),
        content: Text(
          warningText,
          style: const TextStyle(fontSize: 16),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('了解'),
          ),
        ],
      ),
    );
  }
}
