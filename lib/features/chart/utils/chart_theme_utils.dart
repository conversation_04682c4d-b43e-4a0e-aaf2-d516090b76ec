import 'package:flutter/material.dart';

import '../models/chart_display_theme.dart';

/// 星盤主題工具類
class ChartThemeUtils {
  /// 從 Flutter 主題創建星盤主題
  static ChartDisplayTheme fromFlutterTheme(ThemeData flutterTheme) {
    final colorScheme = flutterTheme.colorScheme;
    final textTheme = flutterTheme.textTheme;
    
    return ChartDisplayTheme(
      primaryColor: colorScheme.primary,
      secondaryColor: colorScheme.secondary,
      backgroundColor: colorScheme.surface,
      surfaceColor: colorScheme.surface,
      textColor: colorScheme.onSurface,
      secondaryTextColor: colorScheme.onSurface.withValues(alpha: 0.6),
      borderColor: colorScheme.outline,
      dividerColor: colorScheme.outline.withValues(alpha: 0.5),
      unselectedLabelColor: Colors.white70,
      errorColor: colorScheme.error,
      successColor: Colors.green,
      warningColor: Colors.orange,
      titleTextStyle: textTheme.headlineSmall ?? const TextStyle(),
      subtitleTextStyle: textTheme.titleMedium ?? const TextStyle(),
      bodyTextStyle: textTheme.bodyMedium ?? const TextStyle(),
      labelTextStyle: textTheme.labelMedium ?? const TextStyle(),
      borderRadius: 12.0,
      shadows: [
        BoxShadow(
          color: Colors.black.withValues(alpha: 0.1),
          blurRadius: 8,
          offset: const Offset(0, 2),
        ),
      ],
      padding: const EdgeInsets.all(16.0),
      margin: const EdgeInsets.all(8.0),
    );
  }

  /// 檢查主題是否為深色主題
  static bool isDarkTheme(ChartDisplayTheme theme) {
    return theme.backgroundColor.computeLuminance() < 0.5;
  }

  /// 獲取適合的文字顏色
  static Color getTextColorForBackground(Color backgroundColor) {
    return backgroundColor.computeLuminance() > 0.5 
        ? Colors.black 
        : Colors.white;
  }
}
