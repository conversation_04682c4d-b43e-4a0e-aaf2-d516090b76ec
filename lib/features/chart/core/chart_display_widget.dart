import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../data/models/astrology/chart_data.dart';
import '../../../presentation/viewmodels/chart_viewmodel.dart';
import '../../../presentation/widgets/chart/aspect_table_widget.dart';
import '../../../presentation/widgets/chart/chart_view_widget.dart';
import '../../../presentation/widgets/chart/firdaria_widget.dart';
import '../../../presentation/widgets/chart/houses_widget.dart';
import '../../../presentation/widgets/chart/planet_list_widget.dart';
import '../../../shared/widgets/chart_elements_widget.dart';
import '../../../shared/widgets/classical_astrology_widget.dart';
import '../models/chart_display_settings.dart';
import '../widgets/chart_tabs_widget.dart';
import 'chart_display_config.dart';
import 'chart_display_controller.dart';

/// 星盤顯示主要 Widget
/// 
/// 這是星盤顯示模組的入口點，提供完整的星盤顯示功能
class ChartDisplayWidget extends StatefulWidget {
  final ChartData chartData;
  /// 配置
  final ChartDisplayConfig config;
  
  /// 控制器（可選，如果不提供會自動創建）
  final ChartDisplayController? controller;

  const ChartDisplayWidget({
    super.key,
    required this.chartData,
    required this.config,
    this.controller,
  });

  @override
  State<ChartDisplayWidget> createState() => _ChartDisplayWidgetState();
}

class _ChartDisplayWidgetState extends State<ChartDisplayWidget>
    with TickerProviderStateMixin {
  late ChartDisplayController _controller;
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    
    // 初始化控制器
    _controller = widget.controller ?? ChartDisplayController(config: widget.config);
    
    // 初始化標籤頁控制器
    _initTabController();
  }

  @override
  void didUpdateWidget(ChartDisplayWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // 如果配置變更，更新控制器
    if (widget.config != oldWidget.config) {
      _controller.updateConfig(widget.config);
    }
    
    // 如果標籤頁數量變更，重新初始化標籤頁控制器
    if (_controller.enabledTabs.length != _tabController.length) {
      _tabController.dispose();
      _initTabController();
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    if (widget.controller == null) {
      _controller.dispose();
    }
    super.dispose();
  }

  /// 初始化標籤頁控制器
  void _initTabController() {
    _tabController = TabController(
      length: _controller.enabledTabs.length,
      vsync: this,
      initialIndex: _controller.currentTabIndex,
    );
    
    _tabController.addListener(() {
      if (_tabController.indexIsChanging) {
        _controller.switchTab(_tabController.index);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    bool isTimeUncertain = _hasTimeUncertainty(widget.chartData);
    return ChangeNotifierProvider.value(
      value: _controller,
      child: Consumer<ChartDisplayController>(
        builder: (context, controller, child) {
          return Container(
            decoration: BoxDecoration(
              color: controller.theme.backgroundColor,
              borderRadius: BorderRadius.circular(controller.theme.borderRadius),
            ),
            child: Column(
              children: [

                // 時間不確定警告
                if (isTimeUncertain)
                  _buildTimeUncertaintyWarning(controller, widget.chartData),

                // 標籤頁
                if (!isTimeUncertain && controller.config.showTabs && controller.enabledTabs.length > 1)
                  ChartTabsWidget(
                    controller: controller,
                    tabController: _tabController,
                  ),

                // 主要內容區域
                Expanded(
                  child: _buildContent(controller),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  /// 構建內容區域
  Widget _buildContent(ChartDisplayController controller) {
    if (controller.isLoading) {
      return _buildLoadingState(controller);
    }
    
    if (controller.error != null) {
      return _buildErrorState(controller);
    }
    
    if (!controller.displayData.isValid) {
      return _buildEmptyState(controller);
    }
    
    if (controller.config.showTabs && controller.enabledTabs.length > 1) {
      return TabBarView(
        controller: _tabController,
        children: controller.enabledTabs.map((tabType) {
          return _buildTabContent(controller, tabType);
        }).toList(),
      );
    } else {
      // 如果只有一個標籤頁或不顯示標籤頁，直接顯示內容
      final tabType = controller.enabledTabs.isNotEmpty 
          ? controller.enabledTabs.first 
          : ChartDisplayTabType.chart;
      return _buildTabContent(controller, tabType);
    }
  }

  /// 構建標籤頁內容
  Widget _buildTabContent(ChartDisplayController controller, ChartDisplayTabType tabType) {
    // 從配置中獲取 ChartViewModel
    final chartViewModel = _getChartViewModel(controller);

    if (chartViewModel == null) {
      return _buildPlaceholderContent(controller, '無法獲取星盤數據', Icons.error);
    }

    switch (tabType) {
      case ChartDisplayTabType.chart:
        return ChartViewWidget(viewModel: chartViewModel);

      case ChartDisplayTabType.firdaria:
        return FirdariaWidget(viewModel: chartViewModel);

      case ChartDisplayTabType.planets:
        return PlanetListWidget(viewModel: chartViewModel);

      case ChartDisplayTabType.houses:
        return HousesWidget(viewModel: chartViewModel);

      case ChartDisplayTabType.aspects:
        return AspectTableWidget(viewModel: chartViewModel);

      case ChartDisplayTabType.elements:
        return ChartElementsWidget(viewModel: chartViewModel);

      case ChartDisplayTabType.classical:
        return ClassicalAstrologyWidget(viewModel: chartViewModel);
    }
  }

  /// 從控制器配置中獲取 ChartViewModel
  ChartViewModel? _getChartViewModel(ChartDisplayController controller) {
    // 如果配置中包含 ChartViewModel，直接返回
    if (controller.config.chartViewModel != null) {
      return controller.config.chartViewModel;
    }

    // 嘗試從 Provider 中獲取
    try {
      return Provider.of<ChartViewModel>(context, listen: false);
    } catch (e) {
      // 如果無法從 Provider 獲取，創建一個臨時的 ViewModel
      return _createTemporaryViewModel(controller);
    }
  }

  /// 創建臨時的 ChartViewModel
  ChartViewModel? _createTemporaryViewModel(ChartDisplayController controller) {
    if (controller.config.chartData == null) {
      return null;
    }

    // 創建一個臨時的 ChartViewModel 來顯示數據
    final viewModel = ChartViewModel();
    // 使用 updateChartData 方法來設置數據
    viewModel.updateChartData(controller.config.chartData!);
    return viewModel;
  }

  /// 構建佔位符內容（暫時用於演示）
  Widget _buildPlaceholderContent(ChartDisplayController controller, String title, IconData icon) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 64,
            color: controller.theme.secondaryTextColor,
          ),
          const SizedBox(height: 16),
          Text(
            title,
            style: controller.theme.titleTextStyle,
          ),
          const SizedBox(height: 8),
          Text(
            '此功能正在開發中',
            style: controller.theme.bodyTextStyle,
          ),
        ],
      ),
    );
  }

  /// 構建載入狀態
  Widget _buildLoadingState(ChartDisplayController controller) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            color: controller.theme.primaryColor,
          ),
          const SizedBox(height: 16),
          Text(
            '正在載入星盤數據...',
            style: controller.theme.bodyTextStyle,
          ),
        ],
      ),
    );
  }

  /// 構建錯誤狀態
  Widget _buildErrorState(ChartDisplayController controller) {
    return Center(
      child: Padding(
        padding: controller.theme.padding,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: controller.theme.errorColor,
            ),
            const SizedBox(height: 16),
            Text(
              '載入失敗',
              style: controller.theme.titleTextStyle.copyWith(
                color: controller.theme.errorColor,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              controller.error ?? '未知錯誤',
              style: controller.theme.bodyTextStyle,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => controller.refresh(),
              style: ElevatedButton.styleFrom(
                backgroundColor: controller.theme.primaryColor,
              ),
              child: const Text('重試'),
            ),
          ],
        ),
      ),
    );
  }

  /// 構建空狀態
  Widget _buildEmptyState(ChartDisplayController controller) {
    return Center(
      child: Padding(
        padding: controller.theme.padding,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.circle_outlined,
              size: 64,
              color: controller.theme.secondaryTextColor,
            ),
            const SizedBox(height: 16),
            Text(
              '暫無星盤數據',
              style: controller.theme.titleTextStyle.copyWith(
                color: controller.theme.secondaryTextColor,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '請提供有效的星盤數據',
              style: controller.theme.bodyTextStyle,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// 檢查是否有時間不確定的情況
  bool _hasTimeUncertainty(ChartData chartData) {
    final primaryUncertain = chartData.primaryPerson.isTimeUncertain;
    final secondaryUncertain = chartData.secondaryPerson?.isTimeUncertain ?? false;
    return primaryUncertain || secondaryUncertain;
  }

  /// 構建時間不確定警告
  Widget _buildTimeUncertaintyWarning(ChartDisplayController controller, ChartData chartData) {
    final primaryUncertain = chartData.primaryPerson.isTimeUncertain;
    final secondaryUncertain = chartData.secondaryPerson?.isTimeUncertain ?? false;

    String warningText;
    if (primaryUncertain && secondaryUncertain) {
      warningText = '兩位人物的出生時間都不確定，部分功能已限制使用';
    } else if (primaryUncertain) {
      warningText = '主要人物的出生時間不確定，部分功能已限制使用';
    } else {
      warningText = '次要人物的出生時間不確定，部分功能已限制使用';
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.orange.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.orange.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.warning_amber_rounded,
            color: Colors.orange[700],
            size: 16,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              warningText,
              style: TextStyle(
                fontSize: 12,
                color: Colors.orange[800],
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          GestureDetector(
            onTap: () => _showTimeUncertaintyInfo(context, controller),
            child: Container(
              padding: const EdgeInsets.all(2),
              decoration: BoxDecoration(
                color: Colors.orange.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Icon(
                Icons.info_outline,
                size: 12,
                color: Colors.orange[700],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 顯示時間不確定詳細說明
  void _showTimeUncertaintyInfo(BuildContext context, ChartDisplayController controller) {
    final chartViewModel = controller.config.chartViewModel;
    if (chartViewModel == null) return;

    final primaryUncertain = chartViewModel.primaryPerson.isTimeUncertain;
    final secondaryUncertain = chartViewModel.secondaryPerson?.isTimeUncertain ?? false;

    String detailText;
    if (primaryUncertain && secondaryUncertain) {
      detailText = '兩位人物的出生時間都不確定，以下功能已被限制：\n\n'
          '• 無法切換星盤類型\n'
          '• 無法切換分析標籤\n'
          '• 星盤分析可能不夠準確\n\n'
          '建議先確認準確的出生時間後再進行詳細分析。';
    } else if (primaryUncertain) {
      detailText = '主要人物的出生時間不確定，以下功能已被限制：\n\n'
          '• 無法切換星盤類型\n'
          '• 無法切換分析標籤\n'
          '• 星盤分析可能不夠準確\n\n'
          '建議先確認準確的出生時間後再進行詳細分析。';
    } else {
      detailText = '次要人物的出生時間不確定，以下功能已被限制：\n\n'
          '• 無法切換星盤類型\n'
          '• 無法切換分析標籤\n'
          '• 星盤分析可能不夠準確\n\n'
          '建議先確認準確的出生時間後再進行詳細分析。';
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              Icons.warning_amber_rounded,
              color: Colors.orange[700],
              size: 24,
            ),
            const SizedBox(width: 8),
            const Text('時間不確定說明'),
          ],
        ),
        content: Text(
          detailText,
          style: const TextStyle(fontSize: 14, height: 1.5),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('了解'),
          ),
        ],
      ),
    );
  }
}
