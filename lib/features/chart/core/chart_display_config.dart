import 'package:flutter/material.dart';

import '../../../data/models/astrology/chart_data.dart';
import '../../../data/models/astrology/chart_type.dart';
import '../../../presentation/viewmodels/chart_viewmodel.dart';
import '../models/chart_display_settings.dart';
import '../models/chart_display_theme.dart';

/// 星盤顯示模組的配置類
/// 
/// 包含所有可配置的選項，讓其他專案可以自定義星盤顯示的行為和外觀
class ChartDisplayConfig {
  /// 星盤數據
  final ChartData? chartData;

  /// 星盤視圖模型（可選，如果提供則優先使用）
  final ChartViewModel? chartViewModel;

  /// 顯示主題
  final ChartDisplayTheme theme;
  
  /// 顯示設定
  final ChartDisplaySettings settings;
  
  /// 是否顯示工具列
  final bool showToolbar;
  
  /// 是否顯示標籤頁
  final bool showTabs;
  
  /// 是否顯示資訊面板
  final bool showInfoPanel;
  
  /// 是否啟用縮放功能
  final bool enableZoom;
  
  /// 是否啟用拖拽功能
  final bool enableDrag;
  
  /// 是否啟用行星點擊事件
  final bool enablePlanetTap;
  
  /// 自定義標籤頁列表
  final List<ChartDisplayTab>? customTabs;
  
  /// 自定義工具列動作
  final List<ChartToolbarAction>? customActions;
  
  /// 行星點擊回調
  final Function(dynamic planet)? onPlanetTap;
  
  /// 星盤類型變更回調
  final Function(ChartType chartType)? onChartTypeChanged;
  
  /// 設定變更回調
  final Function(ChartDisplaySettings settings)? onSettingsChanged;
  
  /// 導出完成回調
  final Function(String filePath, String format)? onExportCompleted;
  
  /// 錯誤處理回調
  final Function(String error)? onError;

  const ChartDisplayConfig({
    this.chartData,
    this.chartViewModel,
    required this.theme,
    required this.settings,
    this.showToolbar = true,
    this.showTabs = true,
    this.showInfoPanel = true,
    this.enableZoom = true,
    this.enableDrag = true,
    this.enablePlanetTap = true,
    this.customTabs,
    this.customActions,
    this.onPlanetTap,
    this.onChartTypeChanged,
    this.onSettingsChanged,
    this.onExportCompleted,
    this.onError,
  }) : assert(chartData != null || chartViewModel != null,
         'Either chartData or chartViewModel must be provided');

  /// 創建預設配置
  factory ChartDisplayConfig.defaultConfig({
    ChartData? chartData,
    ChartViewModel? chartViewModel,
  }) {
    return ChartDisplayConfig(
      chartData: chartData,
      chartViewModel: chartViewModel,
      theme: ChartDisplayTheme.defaultTheme(),
      settings: ChartDisplaySettings.defaultSettings(),
    );
  }

  /// 從 ChartViewModel 創建配置
  factory ChartDisplayConfig.fromViewModel({
    required ChartViewModel chartViewModel,
    ChartDisplayTheme? theme,
    ChartDisplaySettings? settings,
    bool showToolbar = true,
    bool showTabs = true,
    bool showInfoPanel = true,
    List<ChartToolbarAction>? customActions,
    Function(dynamic planet)? onPlanetTap,
    Function(ChartType chartType)? onChartTypeChanged,
  }) {
    return ChartDisplayConfig(
      chartViewModel: chartViewModel,
      theme: theme ?? ChartDisplayTheme.defaultTheme(),
      settings: settings ?? ChartDisplaySettings.defaultSettings(),
      showToolbar: showToolbar,
      showTabs: showTabs,
      showInfoPanel: showInfoPanel,
      customActions: customActions,
      onPlanetTap: onPlanetTap,
      onChartTypeChanged: onChartTypeChanged,
    );
  }

  /// 創建簡化配置（只顯示星盤圖）
  factory ChartDisplayConfig.minimal({
    required ChartData chartData,
    ChartDisplayTheme? theme,
  }) {
    return ChartDisplayConfig(
      chartData: chartData,
      theme: theme ?? ChartDisplayTheme.minimal(),
      settings: ChartDisplaySettings.minimal(),
      showToolbar: false,
      showTabs: false,
      showInfoPanel: false,
    );
  }

  /// 創建完整配置（包含所有功能）
  factory ChartDisplayConfig.full({
    required ChartData chartData,
    ChartDisplayTheme? theme,
    List<ChartDisplayTab>? customTabs,
    List<ChartToolbarAction>? customActions,
    Function(dynamic planet)? onPlanetTap,
    Function(ChartType chartType)? onChartTypeChanged,
  }) {
    return ChartDisplayConfig(
      chartData: chartData,
      theme: theme ?? ChartDisplayTheme.defaultTheme(),
      settings: ChartDisplaySettings.full(),
      customTabs: customTabs,
      customActions: customActions,
      onPlanetTap: onPlanetTap,
      onChartTypeChanged: onChartTypeChanged,
    );
  }

  /// 複製配置並更新指定屬性
  ChartDisplayConfig copyWith({
    ChartData? chartData,
    ChartDisplayTheme? theme,
    ChartDisplaySettings? settings,
    bool? showToolbar,
    bool? showTabs,
    bool? showInfoPanel,
    bool? enableZoom,
    bool? enableDrag,
    bool? enablePlanetTap,
    List<ChartDisplayTab>? customTabs,
    List<ChartToolbarAction>? customActions,
    Function(dynamic planet)? onPlanetTap,
    Function(ChartType chartType)? onChartTypeChanged,
    Function(ChartDisplaySettings settings)? onSettingsChanged,
    Function(String filePath, String format)? onExportCompleted,
    Function(String error)? onError,
  }) {
    return ChartDisplayConfig(
      chartData: chartData ?? this.chartData,
      theme: theme ?? this.theme,
      settings: settings ?? this.settings,
      showToolbar: showToolbar ?? this.showToolbar,
      showTabs: showTabs ?? this.showTabs,
      showInfoPanel: showInfoPanel ?? this.showInfoPanel,
      enableZoom: enableZoom ?? this.enableZoom,
      enableDrag: enableDrag ?? this.enableDrag,
      enablePlanetTap: enablePlanetTap ?? this.enablePlanetTap,
      customTabs: customTabs ?? this.customTabs,
      customActions: customActions ?? this.customActions,
      onPlanetTap: onPlanetTap ?? this.onPlanetTap,
      onChartTypeChanged: onChartTypeChanged ?? this.onChartTypeChanged,
      onSettingsChanged: onSettingsChanged ?? this.onSettingsChanged,
      onExportCompleted: onExportCompleted ?? this.onExportCompleted,
      onError: onError ?? this.onError,
    );
  }
}

/// 自定義標籤頁定義
class ChartDisplayTab {
  final String id;
  final String title;
  final IconData icon;
  final Widget content;
  final bool enabled;

  const ChartDisplayTab({
    required this.id,
    required this.title,
    required this.icon,
    required this.content,
    this.enabled = true,
  });
}

/// 自定義工具列動作定義
class ChartToolbarAction {
  final String id;
  final String title;
  final IconData icon;
  final VoidCallback onPressed;
  final bool enabled;
  final String? tooltip;

  const ChartToolbarAction({
    required this.id,
    required this.title,
    required this.icon,
    required this.onPressed,
    this.enabled = true,
    this.tooltip,
  });
}
