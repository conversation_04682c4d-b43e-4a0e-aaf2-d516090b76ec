import 'package:flutter/material.dart';

import '../../../data/models/astrology/chart_data.dart';
import '../../../data/models/astrology/chart_type.dart';
import '../models/chart_display_data.dart';
import '../models/chart_display_settings.dart';
import '../models/chart_display_theme.dart';
import 'chart_display_config.dart';

/// 星盤顯示控制器
/// 
/// 管理星盤顯示的狀態和邏輯
class ChartDisplayController extends ChangeNotifier {
  /// 配置
  ChartDisplayConfig _config;
  
  /// 顯示數據
  ChartDisplayData _displayData;
  
  /// 當前選中的標籤頁索引
  int _currentTabIndex = 0;
  
  /// 是否正在載入
  bool _isLoading = false;
  
  /// 錯誤信息
  String? _error;
  
  /// 縮放比例
  double _zoomLevel = 1.0;
  
  /// 平移偏移
  Offset _panOffset = Offset.zero;

  ChartDisplayController({
    required ChartDisplayConfig config,
  }) : _config = config,
       _displayData = ChartDisplayData.fromChartData(
         config.chartViewModel?.chartData ?? config.chartData!
       );

  // Getters
  ChartDisplayConfig get config => _config;
  ChartDisplayData get displayData => _displayData;
  int get currentTabIndex => _currentTabIndex;
  bool get isLoading => _isLoading;
  String? get error => _error;
  double get zoomLevel => _zoomLevel;
  Offset get panOffset => _panOffset;

  /// 獲取當前主題
  ChartDisplayTheme get theme => _config.theme;

  /// 獲取當前設定
  ChartDisplaySettings get settings => _config.settings;

  /// 獲取啟用的標籤頁列表
  List<ChartDisplayTabType> get enabledTabs => _config.settings.enabledTabs;

  /// 更新配置
  void updateConfig(ChartDisplayConfig newConfig) {
    _config = newConfig;
    final chartData = newConfig.chartViewModel?.chartData ?? newConfig.chartData;
    if (chartData != null) {
      _displayData = ChartDisplayData.fromChartData(chartData);
    }
    notifyListeners();
  }

  /// 更新星盤數據
  void updateChartData(ChartData chartData) {
    _config = _config.copyWith(chartData: chartData);
    _displayData = ChartDisplayData.fromChartData(chartData);
    notifyListeners();
  }

  /// 更新主題
  void updateTheme(ChartDisplayTheme theme) {
    _config = _config.copyWith(theme: theme);
    notifyListeners();
  }

  /// 更新設定
  void updateSettings(ChartDisplaySettings settings) {
    _config = _config.copyWith(settings: settings);
    _config.onSettingsChanged?.call(settings);
    notifyListeners();
  }

  /// 切換標籤頁
  void switchTab(int index) {
    if (index >= 0 && index < enabledTabs.length) {
      _currentTabIndex = index;
      notifyListeners();
    }
  }

  /// 切換到指定類型的標籤頁
  void switchToTabType(ChartDisplayTabType tabType) {
    final index = enabledTabs.indexOf(tabType);
    if (index >= 0) {
      switchTab(index);
    }
  }

  /// 設置載入狀態
  void setLoading(bool loading) {
    _isLoading = loading;
    if (loading) {
      _error = null;
    }
    notifyListeners();
  }

  /// 設置錯誤
  void setError(String? error) {
    _error = error;
    _isLoading = false;
    _config.onError?.call(error ?? '');
    notifyListeners();
  }

  /// 清除錯誤
  void clearError() {
    _error = null;
    notifyListeners();
  }

  /// 重置縮放和平移
  void resetZoomAndPan() {
    _zoomLevel = 1.0;
    _panOffset = Offset.zero;
    notifyListeners();
  }

  /// 設置縮放比例
  void setZoomLevel(double zoom) {
    if (_config.enableZoom) {
      _zoomLevel = zoom.clamp(0.5, 3.0);
      notifyListeners();
    }
  }

  /// 設置平移偏移
  void setPanOffset(Offset offset) {
    if (_config.enableDrag) {
      _panOffset = offset;
      notifyListeners();
    }
  }

  /// 處理行星點擊事件
  void handlePlanetTap(dynamic planet) {
    if (_config.enablePlanetTap && _config.onPlanetTap != null) {
      _config.onPlanetTap!(planet);
    }
  }

  /// 處理星盤類型變更
  void handleChartTypeChange(ChartType chartType) {
    _config.onChartTypeChanged?.call(chartType);
  }

  /// 導出星盤
  Future<void> exportChart(String format) async {
    try {
      setLoading(true);
      
      // 這裡應該調用實際的導出服務
      // 暫時模擬導出過程
      await Future.delayed(const Duration(seconds: 2));
      
      final fileName = 'chart_${DateTime.now().millisecondsSinceEpoch}.$format';
      _config.onExportCompleted?.call(fileName, format);
      
      setLoading(false);
    } catch (e) {
      setError('導出失敗: $e');
    }
  }

  /// 刷新星盤數據
  Future<void> refresh() async {
    try {
      setLoading(true);
      
      // 這裡應該重新計算星盤數據
      // 暫時模擬刷新過程
      await Future.delayed(const Duration(seconds: 1));
      
      setLoading(false);
    } catch (e) {
      setError('刷新失敗: $e');
    }
  }

  /// 檢查是否支援指定的標籤頁類型
  bool supportsTabType(ChartDisplayTabType tabType) {
    switch (tabType) {
      case ChartDisplayTabType.firdaria:
        return _displayData.isFirdariaChart;
      default:
        return true;
    }
  }

  /// 獲取標籤頁標題
  String getTabTitle(ChartDisplayTabType tabType) {
    return tabType.title;
  }

  /// 獲取標籤頁圖標
  IconData getTabIcon(ChartDisplayTabType tabType) {
    return tabType.icon;
  }

  /// 檢查功能是否啟用
  bool isFeatureEnabled(String feature) {
    switch (feature) {
      case 'zoom':
        return _config.enableZoom;
      case 'drag':
        return _config.enableDrag;
      case 'planet_tap':
        return _config.enablePlanetTap;
      case 'toolbar':
        return _config.showToolbar;
      case 'tabs':
        return _config.showTabs;
      case 'info_panel':
        return _config.showInfoPanel;
      default:
        return false;
    }
  }

  /// 獲取自定義標籤頁
  List<ChartDisplayTab> getCustomTabs() {
    return _config.customTabs ?? [];
  }

  /// 獲取自定義工具列動作
  List<ChartToolbarAction> getCustomActions() {
    return _config.customActions ?? [];
  }
}
