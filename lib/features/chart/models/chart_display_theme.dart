import 'package:flutter/material.dart';

/// 星盤顯示主題配置
///
/// 定義星盤顯示的視覺風格，包括顏色、字體、間距等
class ChartDisplayTheme {
  /// 主要顏色
  final Color primaryColor;

  /// 次要顏色
  final Color secondaryColor;

  /// 背景顏色
  final Color backgroundColor;

  /// 表面顏色
  final Color surfaceColor;

  /// 文字顏色
  final Color textColor;

  /// 次要文字顏色
  final Color secondaryTextColor;

  /// 邊框顏色
  final Color borderColor;

  /// 分隔線顏色
  final Color unselectedLabelColor;

  /// 分隔線顏色
  final Color dividerColor;

  /// 錯誤顏色
  final Color errorColor;

  /// 成功顏色
  final Color successColor;

  /// 警告顏色
  final Color warningColor;

  /// 標題字體樣式
  final TextStyle titleTextStyle;

  /// 副標題字體樣式
  final TextStyle subtitleTextStyle;

  /// 內容字體樣式
  final TextStyle bodyTextStyle;

  /// 標籤字體樣式
  final TextStyle labelTextStyle;

  /// 圓角半徑
  final double borderRadius;

  /// 陰影
  final List<BoxShadow> shadows;

  /// 間距
  final EdgeInsets padding;

  /// 邊距
  final EdgeInsets margin;

  const ChartDisplayTheme({
    required this.primaryColor,
    required this.secondaryColor,
    required this.backgroundColor,
    required this.surfaceColor,
    required this.textColor,
    required this.secondaryTextColor,
    required this.borderColor,
    required this.unselectedLabelColor,
    required this.dividerColor,
    required this.errorColor,
    required this.successColor,
    required this.warningColor,
    required this.titleTextStyle,
    required this.subtitleTextStyle,
    required this.bodyTextStyle,
    required this.labelTextStyle,
    required this.borderRadius,
    required this.shadows,
    required this.padding,
    required this.margin,
  });

  /// 預設主題
  factory ChartDisplayTheme.defaultTheme() {
    return ChartDisplayTheme(
      primaryColor: const Color(0xFF6366F1),
      // Indigo
      secondaryColor: const Color(0xFFF59E0B),
      // Amber
      backgroundColor: const Color(0xFFF8FAFC),
      // Slate 50
      surfaceColor: Colors.white,
      textColor: const Color(0xFF1E293B),
      // Slate 800
      secondaryTextColor: const Color(0xFF64748B),
      // Slate 500
      borderColor: const Color(0xFFE2E8F0),
      // Slate 200
      dividerColor: const Color(0xFFF59E0B),
      // Amber
      unselectedLabelColor: const Color(0xB3FFFFFF),
      // Slate 500
      errorColor: const Color(0xFFEF4444),
      // Red 500
      successColor: const Color(0xFF10B981),
      // Emerald 500
      warningColor: const Color(0xFFF59E0B),
      // Amber 500
      titleTextStyle: const TextStyle(
        fontSize: 24,
        fontWeight: FontWeight.bold,
        color: Color(0xFF1E293B),
      ),
      subtitleTextStyle: const TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.w600,
        color: Color(0xFF374151),
      ),
      bodyTextStyle: const TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.normal,
        color: Color(0xFF4B5563),
      ),
      labelTextStyle: const TextStyle(
        fontSize: 12,
        fontWeight: FontWeight.w500,
        color: Color(0xFF6B7280),
      ),
      borderRadius: 12.0,
      shadows: [
        BoxShadow(
          color: Colors.black.withValues(alpha: 0.1),
          blurRadius: 8,
          offset: const Offset(0, 2),
        ),
      ],
      padding: const EdgeInsets.all(16.0),
      margin: const EdgeInsets.all(8.0),
    );
  }

  /// 深色主題
  factory ChartDisplayTheme.darkTheme() {
    return ChartDisplayTheme(
      primaryColor: const Color(0xFF818CF8),
      // Indigo 400
      secondaryColor: const Color(0xFFFBBF24),
      // Amber 400
      backgroundColor: const Color(0xFF0F172A),
      // Slate 900
      surfaceColor: const Color(0xFF1E293B),
      // Slate 800
      textColor: const Color(0xFFF1F5F9),
      // Slate 100
      secondaryTextColor: const Color(0xFF94A3B8),
      // Slate 400
      borderColor: const Color(0xFF334155),
      // Slate 700
      dividerColor: const Color(0xFF334155),
      unselectedLabelColor: const Color(0xB3FFFFFF),
      // Slate 700
      errorColor: const Color(0xFFF87171),
      // Red 400
      successColor: const Color(0xFF34D399),
      // Emerald 400
      warningColor: const Color(0xFFFBBF24),
      // Amber 400
      titleTextStyle: const TextStyle(
        fontSize: 24,
        fontWeight: FontWeight.bold,
        color: Color(0xFFF1F5F9),
      ),
      subtitleTextStyle: const TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.w600,
        color: Color(0xFFE2E8F0),
      ),
      bodyTextStyle: const TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.normal,
        color: Color(0xFFCBD5E1),
      ),
      labelTextStyle: const TextStyle(
        fontSize: 12,
        fontWeight: FontWeight.w500,
        color: Color(0xFF94A3B8),
      ),
      borderRadius: 12.0,
      shadows: [
        BoxShadow(
          color: Colors.black.withValues(alpha: 0.3),
          blurRadius: 8,
          offset: const Offset(0, 2),
        ),
      ],
      padding: const EdgeInsets.all(16.0),
      margin: const EdgeInsets.all(8.0),
    );
  }

  /// 簡化主題（用於嵌入式顯示）
  factory ChartDisplayTheme.minimal() {
    return ChartDisplayTheme(
      primaryColor: const Color(0xFF6366F1),
      secondaryColor: const Color(0xFFF59E0B),
      backgroundColor: Colors.transparent,
      surfaceColor: Colors.transparent,
      textColor: const Color(0xFF1E293B),
      secondaryTextColor: const Color(0xFF64748B),
      borderColor: const Color(0xFFE2E8F0),
      dividerColor: const Color(0xFFE2E8F0),
      unselectedLabelColor: const Color(0xB3FFFFFF),
      errorColor: const Color(0xFFEF4444),
      successColor: const Color(0xFF10B981),
      warningColor: const Color(0xFFF59E0B),
      titleTextStyle: const TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.w600,
        color: Color(0xFF1E293B),
      ),
      subtitleTextStyle: const TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w500,
        color: Color(0xFF374151),
      ),
      bodyTextStyle: const TextStyle(
        fontSize: 12,
        fontWeight: FontWeight.normal,
        color: Color(0xFF4B5563),
      ),
      labelTextStyle: const TextStyle(
        fontSize: 10,
        fontWeight: FontWeight.w500,
        color: Color(0xFF6B7280),
      ),
      borderRadius: 8.0,
      shadows: [],
      padding: const EdgeInsets.all(8.0),
      margin: const EdgeInsets.all(4.0),
    );
  }

  /// 複製主題並更新指定屬性
  ChartDisplayTheme copyWith({
    Color? primaryColor,
    Color? secondaryColor,
    Color? backgroundColor,
    Color? surfaceColor,
    Color? textColor,
    Color? secondaryTextColor,
    Color? borderColor,
    Color? dividerColor,
    Color? errorColor,
    Color? successColor,
    Color? warningColor,
    TextStyle? titleTextStyle,
    TextStyle? subtitleTextStyle,
    TextStyle? bodyTextStyle,
    TextStyle? labelTextStyle,
    double? borderRadius,
    List<BoxShadow>? shadows,
    EdgeInsets? padding,
    EdgeInsets? margin,
  }) {
    return ChartDisplayTheme(
      primaryColor: primaryColor ?? this.primaryColor,
      secondaryColor: secondaryColor ?? this.secondaryColor,
      backgroundColor: backgroundColor ?? this.backgroundColor,
      surfaceColor: surfaceColor ?? this.surfaceColor,
      textColor: textColor ?? this.textColor,
      secondaryTextColor: secondaryTextColor ?? this.secondaryTextColor,
      borderColor: borderColor ?? this.borderColor,
      dividerColor: dividerColor ?? this.dividerColor,
      unselectedLabelColor: unselectedLabelColor,
      errorColor: errorColor ?? this.errorColor,
      successColor: successColor ?? this.successColor,
      warningColor: warningColor ?? this.warningColor,
      titleTextStyle: titleTextStyle ?? this.titleTextStyle,
      subtitleTextStyle: subtitleTextStyle ?? this.subtitleTextStyle,
      bodyTextStyle: bodyTextStyle ?? this.bodyTextStyle,
      labelTextStyle: labelTextStyle ?? this.labelTextStyle,
      borderRadius: borderRadius ?? this.borderRadius,
      shadows: shadows ?? this.shadows,
      padding: padding ?? this.padding,
      margin: margin ?? this.margin,
    );
  }
}
