import 'package:flutter/material.dart';

/// 星盤顯示設定
/// 
/// 控制星盤顯示的各種功能選項
class ChartDisplaySettings {
  /// 顯示的標籤頁列表
  final List<ChartDisplayTabType> enabledTabs;
  
  /// 星盤圖設定
  final ChartCanvasSettings chartCanvas;
  
  /// 行星列表設定
  final PlanetListSettings planetList;
  
  /// 宮位設定
  final HouseSettings houses;
  
  /// 相位表設定
  final AspectTableSettings aspectTable;
  
  /// 統計設定
  final ChartElementsSettings elements;
  
  /// 古典占星設定
  final ClassicalAstrologySettings classical;
  
  /// 法達盤設定
  final FirdariaSettings firdaria;
  
  /// 導出設定
  final ExportSettings export;

  const ChartDisplaySettings({
    required this.enabledTabs,
    required this.chartCanvas,
    required this.planetList,
    required this.houses,
    required this.aspectTable,
    required this.elements,
    required this.classical,
    required this.firdaria,
    required this.export,
  });

  /// 預設設定
  factory ChartDisplaySettings.defaultSettings() {
    return ChartDisplaySettings(
      enabledTabs: [
        ChartDisplayTabType.chart,
        ChartDisplayTabType.planets,
        ChartDisplayTabType.houses,
        ChartDisplayTabType.aspects,
        ChartDisplayTabType.elements,
        ChartDisplayTabType.classical,
      ],
      chartCanvas: ChartCanvasSettings.defaultSettings(),
      planetList: PlanetListSettings.defaultSettings(),
      houses: HouseSettings.defaultSettings(),
      aspectTable: AspectTableSettings.defaultSettings(),
      elements: ChartElementsSettings.defaultSettings(),
      classical: ClassicalAstrologySettings.defaultSettings(),
      firdaria: FirdariaSettings.defaultSettings(),
      export: ExportSettings.defaultSettings(),
    );
  }

  /// 簡化設定（只顯示星盤圖）
  factory ChartDisplaySettings.minimal() {
    return ChartDisplaySettings(
      enabledTabs: [ChartDisplayTabType.chart],
      chartCanvas: ChartCanvasSettings.minimal(),
      planetList: PlanetListSettings.minimal(),
      houses: HouseSettings.minimal(),
      aspectTable: AspectTableSettings.minimal(),
      elements: ChartElementsSettings.minimal(),
      classical: ClassicalAstrologySettings.minimal(),
      firdaria: FirdariaSettings.minimal(),
      export: ExportSettings.minimal(),
    );
  }

  /// 完整設定（包含所有功能）
  factory ChartDisplaySettings.full() {
    return ChartDisplaySettings(
      enabledTabs: ChartDisplayTabType.values,
      chartCanvas: ChartCanvasSettings.full(),
      planetList: PlanetListSettings.full(),
      houses: HouseSettings.full(),
      aspectTable: AspectTableSettings.full(),
      elements: ChartElementsSettings.full(),
      classical: ClassicalAstrologySettings.full(),
      firdaria: FirdariaSettings.full(),
      export: ExportSettings.full(),
    );
  }
}

/// 標籤頁類型
enum ChartDisplayTabType {
  chart('星盤圖', Icons.circle_outlined),
  firdaria('法達盤', Icons.hourglass_full),
  planets('行星位置', Icons.list),
  houses('宮位', Icons.home_outlined),
  aspects('相位表', Icons.grid_on),
  elements('統計', Icons.pie_chart),
  classical('古典占星', Icons.auto_awesome);

  const ChartDisplayTabType(this.title, this.icon);
  
  final String title;
  final IconData icon;
}

/// 星盤畫布設定
class ChartCanvasSettings {
  final bool showPlanetNames;
  final bool showPlanetSymbols;
  final bool showAspectLines;
  final bool showHouseNumbers;
  final bool showDegreeMarks;
  final bool enableZoom;
  final bool enablePan;
  final double initialZoom;
  final Size? fixedSize;

  const ChartCanvasSettings({
    required this.showPlanetNames,
    required this.showPlanetSymbols,
    required this.showAspectLines,
    required this.showHouseNumbers,
    required this.showDegreeMarks,
    required this.enableZoom,
    required this.enablePan,
    required this.initialZoom,
    this.fixedSize,
  });

  factory ChartCanvasSettings.defaultSettings() {
    return const ChartCanvasSettings(
      showPlanetNames: true,
      showPlanetSymbols: true,
      showAspectLines: true,
      showHouseNumbers: true,
      showDegreeMarks: true,
      enableZoom: true,
      enablePan: true,
      initialZoom: 1.0,
    );
  }

  factory ChartCanvasSettings.minimal() {
    return const ChartCanvasSettings(
      showPlanetNames: false,
      showPlanetSymbols: true,
      showAspectLines: false,
      showHouseNumbers: false,
      showDegreeMarks: false,
      enableZoom: false,
      enablePan: false,
      initialZoom: 1.0,
      fixedSize: Size(300, 300),
    );
  }

  factory ChartCanvasSettings.full() {
    return const ChartCanvasSettings(
      showPlanetNames: true,
      showPlanetSymbols: true,
      showAspectLines: true,
      showHouseNumbers: true,
      showDegreeMarks: true,
      enableZoom: true,
      enablePan: true,
      initialZoom: 1.0,
    );
  }
}

/// 行星列表設定
class PlanetListSettings {
  final bool showDegrees;
  final bool showSigns;
  final bool showHouses;
  final bool showRetrograde;
  final bool enableSorting;

  const PlanetListSettings({
    required this.showDegrees,
    required this.showSigns,
    required this.showHouses,
    required this.showRetrograde,
    required this.enableSorting,
  });

  factory PlanetListSettings.defaultSettings() {
    return const PlanetListSettings(
      showDegrees: true,
      showSigns: true,
      showHouses: true,
      showRetrograde: true,
      enableSorting: true,
    );
  }

  factory PlanetListSettings.minimal() {
    return const PlanetListSettings(
      showDegrees: false,
      showSigns: true,
      showHouses: false,
      showRetrograde: false,
      enableSorting: false,
    );
  }

  factory PlanetListSettings.full() {
    return const PlanetListSettings(
      showDegrees: true,
      showSigns: true,
      showHouses: true,
      showRetrograde: true,
      enableSorting: true,
    );
  }
}

/// 宮位設定
class HouseSettings {
  final bool showCusps;
  final bool showRulers;
  final bool showElements;

  const HouseSettings({
    required this.showCusps,
    required this.showRulers,
    required this.showElements,
  });

  factory HouseSettings.defaultSettings() {
    return const HouseSettings(
      showCusps: true,
      showRulers: true,
      showElements: true,
    );
  }

  factory HouseSettings.minimal() {
    return const HouseSettings(
      showCusps: true,
      showRulers: false,
      showElements: false,
    );
  }

  factory HouseSettings.full() {
    return const HouseSettings(
      showCusps: true,
      showRulers: true,
      showElements: true,
    );
  }
}

/// 相位表設定
class AspectTableSettings {
  final bool showOrbs;
  final bool showApplying;
  final bool colorCodeAspects;

  const AspectTableSettings({
    required this.showOrbs,
    required this.showApplying,
    required this.colorCodeAspects,
  });

  factory AspectTableSettings.defaultSettings() {
    return const AspectTableSettings(
      showOrbs: true,
      showApplying: true,
      colorCodeAspects: true,
    );
  }

  factory AspectTableSettings.minimal() {
    return const AspectTableSettings(
      showOrbs: false,
      showApplying: false,
      colorCodeAspects: false,
    );
  }

  factory AspectTableSettings.full() {
    return const AspectTableSettings(
      showOrbs: true,
      showApplying: true,
      colorCodeAspects: true,
    );
  }
}

/// 統計設定
class ChartElementsSettings {
  final bool showElementDistribution;
  final bool showQualityDistribution;
  final bool showPolarityDistribution;

  const ChartElementsSettings({
    required this.showElementDistribution,
    required this.showQualityDistribution,
    required this.showPolarityDistribution,
  });

  factory ChartElementsSettings.defaultSettings() {
    return const ChartElementsSettings(
      showElementDistribution: true,
      showQualityDistribution: true,
      showPolarityDistribution: true,
    );
  }

  factory ChartElementsSettings.minimal() {
    return const ChartElementsSettings(
      showElementDistribution: true,
      showQualityDistribution: false,
      showPolarityDistribution: false,
    );
  }

  factory ChartElementsSettings.full() {
    return const ChartElementsSettings(
      showElementDistribution: true,
      showQualityDistribution: true,
      showPolarityDistribution: true,
    );
  }
}

/// 古典占星設定
class ClassicalAstrologySettings {
  final bool showDignities;
  final bool showReceptions;
  final bool showArabicParts;

  const ClassicalAstrologySettings({
    required this.showDignities,
    required this.showReceptions,
    required this.showArabicParts,
  });

  factory ClassicalAstrologySettings.defaultSettings() {
    return const ClassicalAstrologySettings(
      showDignities: true,
      showReceptions: true,
      showArabicParts: true,
    );
  }

  factory ClassicalAstrologySettings.minimal() {
    return const ClassicalAstrologySettings(
      showDignities: false,
      showReceptions: false,
      showArabicParts: false,
    );
  }

  factory ClassicalAstrologySettings.full() {
    return const ClassicalAstrologySettings(
      showDignities: true,
      showReceptions: true,
      showArabicParts: true,
    );
  }
}

/// 法達盤設定
class FirdariaSettings {
  final bool showTimeline;
  final bool showCurrentPeriod;
  final bool enablePeriodSelection;

  const FirdariaSettings({
    required this.showTimeline,
    required this.showCurrentPeriod,
    required this.enablePeriodSelection,
  });

  factory FirdariaSettings.defaultSettings() {
    return const FirdariaSettings(
      showTimeline: true,
      showCurrentPeriod: true,
      enablePeriodSelection: true,
    );
  }

  factory FirdariaSettings.minimal() {
    return const FirdariaSettings(
      showTimeline: false,
      showCurrentPeriod: true,
      enablePeriodSelection: false,
    );
  }

  factory FirdariaSettings.full() {
    return const FirdariaSettings(
      showTimeline: true,
      showCurrentPeriod: true,
      enablePeriodSelection: true,
    );
  }
}

/// 導出設定
class ExportSettings {
  final List<String> supportedFormats;
  final bool enableImageExport;
  final bool enablePdfExport;
  final bool enableDataExport;

  const ExportSettings({
    required this.supportedFormats,
    required this.enableImageExport,
    required this.enablePdfExport,
    required this.enableDataExport,
  });

  factory ExportSettings.defaultSettings() {
    return const ExportSettings(
      supportedFormats: ['png', 'jpg', 'pdf'],
      enableImageExport: true,
      enablePdfExport: true,
      enableDataExport: false,
    );
  }

  factory ExportSettings.minimal() {
    return const ExportSettings(
      supportedFormats: ['png'],
      enableImageExport: true,
      enablePdfExport: false,
      enableDataExport: false,
    );
  }

  factory ExportSettings.full() {
    return const ExportSettings(
      supportedFormats: ['png', 'jpg', 'pdf', 'svg', 'json'],
      enableImageExport: true,
      enablePdfExport: true,
      enableDataExport: true,
    );
  }
}
