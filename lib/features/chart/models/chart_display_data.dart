import '../../../data/models/astrology/aspect_info.dart';
import '../../../data/models/astrology/chart_data.dart';
import '../../../data/models/astrology/chart_type.dart';
import '../../../data/models/astrology/planet_position.dart';
import '../../../data/models/user/birth_data.dart';

/// 星盤顯示數據包裝器
/// 
/// 將原始的 ChartData 包裝成適合顯示模組使用的格式
class ChartDisplayData {
  /// 原始星盤數據
  final ChartData chartData;
  
  /// 顯示標題
  final String title;
  
  /// 顯示副標題
  final String? subtitle;
  
  /// 顯示描述
  final String? description;
  
  /// 是否為載入狀態
  final bool isLoading;
  
  /// 錯誤信息
  final String? error;
  
  /// 額外的元數據
  final Map<String, dynamic>? metadata;

  const ChartDisplayData({
    required this.chartData,
    required this.title,
    this.subtitle,
    this.description,
    this.isLoading = false,
    this.error,
    this.metadata,
  });

  /// 從 ChartData 創建顯示數據
  factory ChartDisplayData.fromChartData(ChartData chartData) {
    return ChartDisplayData(
      chartData: chartData,
      title: _generateTitle(chartData),
      subtitle: _generateSubtitle(chartData),
      description: _generateDescription(chartData),
    );
  }

  /// 創建載入狀態的顯示數據
  factory ChartDisplayData.loading({
    required String title,
    String? subtitle,
  }) {
    return ChartDisplayData(
      chartData: ChartData(
        chartType: ChartType.natal,
        primaryPerson: BirthData(
          id: '',
          name: '',
          dateTime: DateTime.now(),
          birthPlace: '',
          latitude: 0.0,
          longitude: 0.0,
        ),
      ),
      title: title,
      subtitle: subtitle,
      isLoading: true,
    );
  }

  /// 創建錯誤狀態的顯示數據
  factory ChartDisplayData.error({
    required String title,
    required String error,
    String? subtitle,
  }) {
    return ChartDisplayData(
      chartData: ChartData(
        chartType: ChartType.natal,
        primaryPerson: BirthData(
          id: '',
          name: '',
          dateTime: DateTime.now(),
          birthPlace: '',
          latitude: 0.0,
          longitude: 0.0,
        ),
      ),
      title: title,
      subtitle: subtitle,
      error: error,
    );
  }

  /// 生成標題
  static String _generateTitle(ChartData chartData) {
    if (chartData.chartType.requiresTwoPersons && chartData.secondaryPerson != null) {
      return '${chartData.primaryPerson.name} 與 ${chartData.secondaryPerson!.name} 的${chartData.chartType.name}';
    }
    return '${chartData.primaryPerson.name}的${chartData.chartType.name}';
  }

  /// 生成副標題
  static String? _generateSubtitle(ChartData chartData) {
    final dateTime = chartData.specificDate ?? chartData.primaryPerson.dateTime;
    return '${dateTime.year}年${dateTime.month}月${dateTime.day}日 ${_formatTime(dateTime)}';
  }

  /// 生成描述
  static String? _generateDescription(ChartData chartData) {
    final location = chartData.primaryPerson.birthPlace;
    if (location.isNotEmpty) {
      return '出生地：$location';
    }
    return null;
  }

  /// 格式化時間
  static String _formatTime(DateTime dateTime) {
    final hour = dateTime.hour.toString().padLeft(2, '0');
    final minute = dateTime.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }

  /// 獲取行星數據
  List<PlanetPosition> get planets => chartData.planets ?? [];

  /// 獲取相位數據
  List<AspectInfo> get aspects => chartData.aspects ?? [];

  /// 獲取宮位數據
  dynamic get houses => chartData.houses;

  /// 獲取阿拉伯點數據
  List<PlanetPosition> get arabicPoints => chartData.arabicPoints ?? [];

  /// 獲取法達盤數據
  List<dynamic> get firdariaData => chartData.firdariaData ?? [];

  /// 獲取星盤類型
  ChartType get chartType => chartData.chartType;

  /// 獲取主要人物
  BirthData get primaryPerson => chartData.primaryPerson;

  /// 獲取次要人物
  BirthData? get secondaryPerson => chartData.secondaryPerson;

  /// 獲取特定日期
  DateTime? get specificDate => chartData.specificDate;

  /// 是否有效
  bool get isValid => !isLoading && error == null && planets.isNotEmpty;

  /// 是否為雙人星盤
  bool get isTwoPersonChart => chartData.chartType.requiresTwoPersons;

  /// 是否為推運盤
  bool get isProgressionChart => chartData.chartType.name.contains('推運');

  /// 是否為法達盤
  bool get isFirdariaChart => chartData.chartType == ChartType.firdaria;

  /// 複製並更新數據
  ChartDisplayData copyWith({
    ChartData? chartData,
    String? title,
    String? subtitle,
    String? description,
    bool? isLoading,
    String? error,
    Map<String, dynamic>? metadata,
  }) {
    return ChartDisplayData(
      chartData: chartData ?? this.chartData,
      title: title ?? this.title,
      subtitle: subtitle ?? this.subtitle,
      description: description ?? this.description,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
      metadata: metadata ?? this.metadata,
    );
  }

  /// 轉換為 JSON
  Map<String, dynamic> toJson() {
    return {
      'chartData': chartData.toJson(),
      'title': title,
      'subtitle': subtitle,
      'description': description,
      'isLoading': isLoading,
      'error': error,
      'metadata': metadata,
    };
  }

  /// 從 JSON 創建
  factory ChartDisplayData.fromJson(Map<String, dynamic> json) {
    return ChartDisplayData(
      chartData: ChartData.fromJson(json['chartData']),
      title: json['title'],
      subtitle: json['subtitle'],
      description: json['description'],
      isLoading: json['isLoading'] ?? false,
      error: json['error'],
      metadata: json['metadata'],
    );
  }

  @override
  String toString() {
    return 'ChartDisplayData(title: $title, isLoading: $isLoading, error: $error)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ChartDisplayData &&
        other.title == title &&
        other.subtitle == subtitle &&
        other.description == description &&
        other.isLoading == isLoading &&
        other.error == error;
  }

  @override
  int get hashCode {
    return Object.hash(
      title,
      subtitle,
      description,
      isLoading,
      error,
    );
  }
}
