# 星盤顯示模組遷移指南

本指南說明如何將現有的 ChartPage 遷移到新的星盤顯示模組。

## 遷移概述

新的星盤顯示模組提供了更好的：
- **模組化設計**：可重用的組件
- **配置靈活性**：豐富的自定義選項
- **主題系統**：統一的視覺風格
- **功能擴展性**：易於添加新功能

## 遷移步驟

### 1. 導入新模組

```dart
// 舊的導入
import '../ui/pages/chart_page.dart';

// 新的導入
import '../chart_display_module/chart_display_module.dart';
```

### 2. 替換 ChartPage

#### 舊的用法：
```dart
class MyChartPage extends StatelessWidget {
  final ChartData chartData;
  
  @override
  Widget build(BuildContext context) {
    return ChartPage(chartData: chartData);
  }
}
```

#### 新的用法：
```dart
class MyChartPage extends StatelessWidget {
  final ChartData chartData;
  
  @override
  Widget build(BuildContext context) {
    final config = ChartDisplayConfig.defaultConfig(
      chartData: chartData,
    );
    
    return Scaffold(
      appBar: AppBar(title: Text('星盤')),
      body: ChartDisplayWidget(config: config),
    );
  }
}
```

### 3. 配置主題

#### 使用 App 主題：
```dart
ChartDisplayTheme createAppTheme() {
  return ChartDisplayTheme(
    primaryColor: AppColors.royalIndigo,
    secondaryColor: AppColors.solarAmber,
    backgroundColor: AppColors.indigoSurface,
    // ... 其他配置
  );
}

final config = ChartDisplayConfig(
  chartData: chartData,
  theme: createAppTheme(),
  settings: ChartDisplaySettings.defaultSettings(),
);
```

### 4. 添加互動功能

```dart
final config = ChartDisplayConfig.full(
  chartData: chartData,
  onPlanetTap: (planet) {
    // 處理行星點擊
    showPlanetDetails(planet);
  },
  onChartTypeChanged: (chartType) {
    // 處理星盤類型變更
    updateChartType(chartType);
  },
  customActions: [
    ChartToolbarAction(
      id: 'ai_interpretation',
      title: 'AI 解讀',
      icon: Icons.psychology,
      onPressed: () => startAIInterpretation(),
    ),
  ],
);
```

### 5. 響應式設計

```dart
Widget buildResponsiveChart() {
  return LayoutBuilder(
    builder: (context, constraints) {
      final isTablet = constraints.maxWidth > 600;
      
      final config = ChartDisplayConfig(
        chartData: chartData,
        theme: isTablet 
            ? ChartDisplayTheme.defaultTheme()
            : ChartDisplayTheme.minimal(),
        settings: isTablet
            ? ChartDisplaySettings.full()
            : ChartDisplaySettings.minimal(),
        showToolbar: isTablet,
        showTabs: isTablet,
        showInfoPanel: isTablet,
      );
      
      return ChartDisplayWidget(config: config);
    },
  );
}
```

## 功能對應表

| 舊功能 | 新模組對應 | 說明 |
|--------|------------|------|
| ChartPage | ChartDisplayWidget | 主要顯示組件 |
| 星盤圖顯示 | ChartCanvasWidget | 星盤畫布 |
| 標籤頁 | ChartTabsWidget | 可配置的標籤頁 |
| 工具列 | ChartToolbarWidget | 可自定義的工具列 |
| 主題設定 | ChartDisplayTheme | 統一的主題系統 |
| 行星點擊 | onPlanetTap 回調 | 行星互動事件 |
| 導出功能 | ChartExportService | 多格式導出 |

## 配置選項

### 顯示模式

```dart
// 簡化模式（只顯示星盤圖）
ChartDisplayConfig.minimal(chartData: chartData)

// 標準模式
ChartDisplayConfig.defaultConfig(chartData: chartData)

// 完整模式（所有功能）
ChartDisplayConfig.full(chartData: chartData)
```

### 自定義標籤頁

```dart
final config = ChartDisplayConfig(
  chartData: chartData,
  settings: ChartDisplaySettings(
    enabledTabs: [
      ChartDisplayTabType.chart,
      ChartDisplayTabType.planets,
      ChartDisplayTabType.aspects,
      // 只顯示需要的標籤頁
    ],
    // ... 其他設定
  ),
);
```

### 自定義工具列

```dart
final config = ChartDisplayConfig(
  chartData: chartData,
  customActions: [
    ChartToolbarAction(
      id: 'custom_action',
      title: '自定義功能',
      icon: Icons.star,
      onPressed: () {
        // 自定義邏輯
      },
    ),
  ],
);
```

## 最佳實踐

### 1. 控制器管理

```dart
class MyChartPage extends StatefulWidget {
  @override
  _MyChartPageState createState() => _MyChartPageState();
}

class _MyChartPageState extends State<MyChartPage> {
  late ChartDisplayController _controller;
  
  @override
  void initState() {
    super.initState();
    final config = ChartDisplayConfig.defaultConfig(
      chartData: widget.chartData,
    );
    _controller = ChartDisplayController(config: config);
  }
  
  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return ChartDisplayWidget(
      config: _controller.config,
      controller: _controller,
    );
  }
}
```

### 2. 錯誤處理

```dart
final config = ChartDisplayConfig(
  chartData: chartData,
  onError: (error) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('錯誤: $error')),
    );
  },
);
```

### 3. 載入狀態

```dart
// 顯示載入狀態
final loadingData = ChartDisplayData.loading(
  title: '正在載入星盤...',
);

// 顯示錯誤狀態
final errorData = ChartDisplayData.error(
  title: '載入失敗',
  error: '無法載入星盤數據',
);
```

## 常見問題

### Q: 如何保持現有的 UI 風格？
A: 使用 `ChartDisplayTheme` 配置顏色和字體，確保與 App 主題一致。

### Q: 如何添加自定義功能？
A: 使用 `customActions` 添加工具列按鈕，或使用 `customTabs` 添加自定義標籤頁。

### Q: 如何處理不同螢幕尺寸？
A: 使用 `LayoutBuilder` 根據螢幕尺寸選擇不同的配置。

### Q: 如何整合現有的 AI 解讀功能？
A: 在 `onPlanetTap` 或 `customActions` 中調用現有的解讀服務。

## 注意事項

1. **向後兼容性**：新模組不會影響現有功能，可以逐步遷移
2. **性能考慮**：大型星盤建議使用簡化模式
3. **記憶體管理**：記得釋放控制器資源
4. **測試**：遷移後進行充分測試，確保功能正常

## 支援

如果在遷移過程中遇到問題，請參考：
- [README.md](./README.md) - 詳細使用說明
- [examples/](./examples/) - 使用示例
- [integration/](./integration/) - 整合示例
