# 星盤顯示模組 (Chart Display Module)

一個可重用的 Flutter 星盤顯示模組，提供完整的星盤視覺化功能，可以輕鬆整合到任何占星應用中。

## 功能特性

### 🎨 靈活的主題系統
- 預設主題、深色主題、簡化主題
- 完全可自定義的顏色、字體、間距
- 支援動態主題切換

### 📊 多種顯示模式
- **星盤圖**：完整的星盤視覺化
- **法達盤**：時間軸顯示
- **行星位置**：詳細的行星列表
- **宮位資訊**：宮位分析
- **相位表**：相位關係表格
- **統計分析**：元素、性質分佈
- **古典占星**：尊貴、互容分析

### 🔧 高度可配置
- 模組化的標籤頁系統
- 可選的工具列和資訊面板
- 自定義動作和回調
- 靈活的設定選項

### 📱 互動功能
- 縮放和平移支援
- 行星點擊事件
- 導出功能（PNG、JPG、PDF）
- 響應式設計

## 快速開始

### 1. 基本使用

```dart
import 'package:your_app/chart_display_module/chart_display_module.dart';

// 創建基本的星盤顯示
Widget buildChart() {
  final config = ChartDisplayConfig.defaultConfig(
    chartData: yourChartData,
  );
  
  return ChartDisplayWidget(config: config);
}
```

### 2. 簡化模式（只顯示星盤圖）

```dart
Widget buildMinimalChart() {
  final config = ChartDisplayConfig.minimal(
    chartData: yourChartData,
    theme: ChartDisplayTheme.minimal(),
  );
  
  return ChartDisplayWidget(config: config);
}
```

### 3. 完整功能模式

```dart
Widget buildFullChart() {
  final config = ChartDisplayConfig.full(
    chartData: yourChartData,
    onPlanetTap: (planet) {
      print('點擊了行星: ${planet.name}');
    },
    onChartTypeChanged: (chartType) {
      print('切換到: ${chartType.name}');
    },
  );
  
  return ChartDisplayWidget(config: config);
}
```

### 4. 自定義配置

```dart
Widget buildCustomChart() {
  final config = ChartDisplayConfig(
    chartData: yourChartData,
    theme: ChartDisplayTheme.darkTheme(),
    settings: ChartDisplaySettings(
      enabledTabs: [
        ChartDisplayTabType.chart,
        ChartDisplayTabType.planets,
        ChartDisplayTabType.aspects,
      ],
      chartCanvas: ChartCanvasSettings(
        showPlanetNames: true,
        showAspectLines: true,
        enableZoom: true,
        enablePan: true,
        initialZoom: 1.2,
      ),
      // ... 其他設定
    ),
    showToolbar: true,
    showTabs: true,
    showInfoPanel: true,
    customActions: [
      ChartToolbarAction(
        id: 'custom_action',
        title: '自定義動作',
        icon: Icons.star,
        onPressed: () {
          // 自定義邏輯
        },
      ),
    ],
    onPlanetTap: (planet) {
      // 處理行星點擊
    },
    onError: (error) {
      // 處理錯誤
    },
  );
  
  return ChartDisplayWidget(config: config);
}
```

## 主要組件

### ChartDisplayWidget
主要的顯示組件，包含完整的星盤顯示功能。

### ChartDisplayConfig
配置類，定義所有可配置的選項：
- `chartData`: 星盤數據
- `theme`: 顯示主題
- `settings`: 功能設定
- `showToolbar`: 是否顯示工具列
- `showTabs`: 是否顯示標籤頁
- `showInfoPanel`: 是否顯示資訊面板
- 各種回調函數

### ChartDisplayTheme
主題配置類，控制視覺外觀：
- 顏色方案
- 字體樣式
- 間距和邊距
- 陰影效果

### ChartDisplaySettings
功能設定類，控制顯示內容：
- 啟用的標籤頁
- 星盤畫布設定
- 各個功能模組的設定

## 進階用法

### 1. 使用控制器

```dart
class MyChartPage extends StatefulWidget {
  @override
  _MyChartPageState createState() => _MyChartPageState();
}

class _MyChartPageState extends State<MyChartPage> {
  late ChartDisplayController _controller;
  
  @override
  void initState() {
    super.initState();
    
    final config = ChartDisplayConfig.defaultConfig(
      chartData: widget.chartData,
    );
    
    _controller = ChartDisplayController(config: config);
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('星盤顯示'),
        actions: [
          IconButton(
            icon: Icon(Icons.settings),
            onPressed: () => _showSettings(),
          ),
        ],
      ),
      body: ChartDisplayWidget(
        config: _controller.config,
        controller: _controller,
      ),
    );
  }
  
  void _showSettings() {
    // 顯示設定對話框
    showDialog(
      context: context,
      builder: (context) => _buildSettingsDialog(),
    );
  }
  
  Widget _buildSettingsDialog() {
    return AlertDialog(
      title: Text('顯示設定'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SwitchListTile(
            title: Text('顯示行星名稱'),
            value: _controller.settings.chartCanvas.showPlanetNames,
            onChanged: (value) {
              final newSettings = _controller.settings.copyWith(
                chartCanvas: _controller.settings.chartCanvas.copyWith(
                  showPlanetNames: value,
                ),
              );
              _controller.updateSettings(newSettings);
            },
          ),
          // 更多設定選項...
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: Text('關閉'),
        ),
      ],
    );
  }
  
  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
}
```

### 2. 主題自定義

```dart
// 創建自定義主題
final customTheme = ChartDisplayTheme.defaultTheme().copyWith(
  primaryColor: Colors.purple,
  backgroundColor: Colors.grey[100],
  borderRadius: 16.0,
  titleTextStyle: TextStyle(
    fontSize: 20,
    fontWeight: FontWeight.bold,
    color: Colors.purple,
  ),
);

final config = ChartDisplayConfig(
  chartData: chartData,
  theme: customTheme,
  settings: ChartDisplaySettings.defaultSettings(),
);
```

### 3. 響應式設計

```dart
Widget buildResponsiveChart() {
  return LayoutBuilder(
    builder: (context, constraints) {
      final isTablet = constraints.maxWidth > 600;
      
      final config = ChartDisplayConfig(
        chartData: chartData,
        theme: isTablet 
            ? ChartDisplayTheme.defaultTheme()
            : ChartDisplayTheme.minimal(),
        settings: isTablet
            ? ChartDisplaySettings.full()
            : ChartDisplaySettings.minimal(),
        showToolbar: isTablet,
        showTabs: isTablet,
        showInfoPanel: isTablet,
      );
      
      return ChartDisplayWidget(config: config);
    },
  );
}
```

## 導出功能

模組支援多種格式的導出：

```dart
// 在控制器中導出
await controller.exportChart('png');
await controller.exportChart('pdf');

// 或在配置中設定導出回調
final config = ChartDisplayConfig(
  chartData: chartData,
  onExportCompleted: (filePath, format) {
    print('導出完成: $filePath ($format)');
    // 顯示成功訊息或分享文件
  },
);
```

## 錯誤處理

```dart
final config = ChartDisplayConfig(
  chartData: chartData,
  onError: (error) {
    // 處理錯誤
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('錯誤: $error')),
    );
  },
);
```

## 最佳實踐

1. **性能優化**：對於大量數據，使用 `ChartDisplaySettings.minimal()` 減少渲染負擔
2. **記憶體管理**：及時釋放控制器資源
3. **響應式設計**：根據螢幕大小調整顯示模式
4. **錯誤處理**：始終提供錯誤回調處理異常情況
5. **用戶體驗**：提供載入狀態和進度指示

## 依賴要求

- Flutter SDK: >=3.0.0
- Dart SDK: >=3.0.0
- provider: ^6.0.0

## 授權

MIT License
