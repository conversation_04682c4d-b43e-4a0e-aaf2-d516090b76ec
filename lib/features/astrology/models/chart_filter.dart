import 'package:flutter/foundation.dart';

/// 篩選條件類型枚舉
enum FilterType {
  planetInSign,    // 行星在星座
  planetInHouse,   // 行星在宮位
  houseInSign,     // 宮位在星座
  planetAspect,    // 行星相位
}

/// 篩選操作符枚舉
enum FilterOperator {
  equals,          // 等於
  notEquals,       // 不等於
  contains,        // 包含
  notContains,     // 不包含
}

/// 邏輯操作符枚舉
enum LogicalOperator {
  and,             // 且
  or,              // 或
}

/// 單一篩選條件
class FilterCondition {
  final String id;
  final FilterType type;
  final FilterOperator operator;
  final String? planetName;     // 行星名稱（當type為planetInSign或planetInHouse時使用）
  final String? signName;       // 星座名稱
  final int? houseNumber;       // 宮位號碼（1-12）
  final List<String>? signNames; // 多個星座名稱（用於元素篩選等）
  final List<int>? houseNumbers; // 多個宮位號碼
  final String? planet2Name;    // 第二個行星名稱（用於相位篩選）
  final String? aspectType;     // 相位類型（合相、對分相等）
  final List<String>? aspectTypes; // 多個相位類型

  FilterCondition({
    required this.id,
    required this.type,
    required this.operator,
    this.planetName,
    this.signName,
    this.houseNumber,
    this.signNames,
    this.houseNumbers,
    this.planet2Name,
    this.aspectType,
    this.aspectTypes,
  });

  /// 從JSON創建篩選條件
  factory FilterCondition.fromJson(Map<String, dynamic> json) {
    return FilterCondition(
      id: json['id'] as String,
      type: FilterType.values[json['type'] as int],
      operator: FilterOperator.values[json['operator'] as int],
      planetName: json['planetName'] as String?,
      signName: json['signName'] as String?,
      houseNumber: json['houseNumber'] as int?,
      signNames: json['signNames'] != null
          ? List<String>.from(json['signNames'] as List)
          : null,
      houseNumbers: json['houseNumbers'] != null
          ? List<int>.from(json['houseNumbers'] as List)
          : null,
      planet2Name: json['planet2Name'] as String?,
      aspectType: json['aspectType'] as String?,
      aspectTypes: json['aspectTypes'] != null
          ? List<String>.from(json['aspectTypes'] as List)
          : null,
    );
  }

  /// 轉換為JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.index,
      'operator': operator.index,
      'planetName': planetName,
      'signName': signName,
      'houseNumber': houseNumber,
      'signNames': signNames,
      'houseNumbers': houseNumbers,
      'planet2Name': planet2Name,
      'aspectType': aspectType,
      'aspectTypes': aspectTypes,
    };
  }

  /// 創建副本
  FilterCondition copyWith({
    String? id,
    FilterType? type,
    FilterOperator? operator,
    String? planetName,
    String? signName,
    int? houseNumber,
    List<String>? signNames,
    List<int>? houseNumbers,
    String? planet2Name,
    String? aspectType,
    List<String>? aspectTypes,
  }) {
    return FilterCondition(
      id: id ?? this.id,
      type: type ?? this.type,
      operator: operator ?? this.operator,
      planetName: planetName ?? this.planetName,
      signName: signName ?? this.signName,
      houseNumber: houseNumber ?? this.houseNumber,
      signNames: signNames ?? this.signNames,
      houseNumbers: houseNumbers ?? this.houseNumbers,
      planet2Name: planet2Name ?? this.planet2Name,
      aspectType: aspectType ?? this.aspectType,
      aspectTypes: aspectTypes ?? this.aspectTypes,
    );
  }

  /// 獲取條件描述文字
  String getDescription() {
    switch (type) {
      case FilterType.planetInSign:
        if (signNames != null && signNames!.isNotEmpty) {
          final signText = signNames!.join('、');
          return '$planetName${_getOperatorText()}$signText';
        }
        return '$planetName${_getOperatorText()}$signName';

      case FilterType.planetInHouse:
        if (houseNumbers != null && houseNumbers!.isNotEmpty) {
          final houseText = houseNumbers!.map((h) => '第${h}宮').join('、');
          return '$planetName${_getOperatorText()}$houseText';
        }
        return '$planetName${_getOperatorText()}第${houseNumber}宮';

      case FilterType.houseInSign:
        return '第${houseNumber}宮${_getOperatorText()}$signName';

      case FilterType.planetAspect:
        if (aspectTypes != null && aspectTypes!.isNotEmpty) {
          final aspectText = aspectTypes!.join('、');
          return '$planetName與$planet2Name${_getOperatorText()}$aspectText';
        }
        return '$planetName與$planet2Name${_getOperatorText()}$aspectType';
    }
  }

  /// 獲取操作符文字
  String _getOperatorText() {
    switch (operator) {
      case FilterOperator.equals:
        return type == FilterType.planetAspect ? '形成' : '在';
      case FilterOperator.notEquals:
        return type == FilterType.planetAspect ? '不形成' : '不在';
      case FilterOperator.contains:
        return type == FilterType.planetAspect ? '包含' : '包含';
      case FilterOperator.notContains:
        return type == FilterType.planetAspect ? '不包含' : '不包含';
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is FilterCondition &&
        other.id == id &&
        other.type == type &&
        other.operator == operator &&
        other.planetName == planetName &&
        other.signName == signName &&
        other.houseNumber == houseNumber &&
        listEquals(other.signNames, signNames) &&
        listEquals(other.houseNumbers, houseNumbers);
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      type,
      operator,
      planetName,
      signName,
      houseNumber,
      signNames,
      houseNumbers,
    );
  }
}

/// 篩選條件組
class FilterGroup {
  final String id;
  final List<FilterCondition> conditions;
  final LogicalOperator logicalOperator;

  FilterGroup({
    required this.id,
    required this.conditions,
    this.logicalOperator = LogicalOperator.and,
  });

  /// 從JSON創建篩選條件組
  factory FilterGroup.fromJson(Map<String, dynamic> json) {
    return FilterGroup(
      id: json['id'] as String,
      conditions: (json['conditions'] as List)
          .map((c) => FilterCondition.fromJson(c as Map<String, dynamic>))
          .toList(),
      logicalOperator: LogicalOperator.values[json['logicalOperator'] as int],
    );
  }

  /// 轉換為JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'conditions': conditions.map((c) => c.toJson()).toList(),
      'logicalOperator': logicalOperator.index,
    };
  }

  /// 創建副本
  FilterGroup copyWith({
    String? id,
    List<FilterCondition>? conditions,
    LogicalOperator? logicalOperator,
  }) {
    return FilterGroup(
      id: id ?? this.id,
      conditions: conditions ?? this.conditions,
      logicalOperator: logicalOperator ?? this.logicalOperator,
    );
  }

  /// 添加條件
  FilterGroup addCondition(FilterCondition condition) {
    return copyWith(
      conditions: [...conditions, condition],
    );
  }

  /// 移除條件
  FilterGroup removeCondition(String conditionId) {
    return copyWith(
      conditions: conditions.where((c) => c.id != conditionId).toList(),
    );
  }

  /// 更新條件
  FilterGroup updateCondition(FilterCondition condition) {
    final updatedConditions = conditions.map((c) {
      return c.id == condition.id ? condition : c;
    }).toList();
    
    return copyWith(conditions: updatedConditions);
  }

  /// 獲取組描述文字
  String getDescription() {
    if (conditions.isEmpty) return '無篩選條件';
    
    final operatorText = logicalOperator == LogicalOperator.and ? ' 且 ' : ' 或 ';
    return conditions.map((c) => c.getDescription()).join(operatorText);
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is FilterGroup &&
        other.id == id &&
        listEquals(other.conditions, conditions) &&
        other.logicalOperator == logicalOperator;
  }

  @override
  int get hashCode {
    return Object.hash(id, conditions, logicalOperator);
  }
}

/// 完整的篩選器配置
class ChartFilter {
  final String id;
  final String name;
  final List<FilterGroup> groups;
  final LogicalOperator groupLogicalOperator;
  final DateTime createdAt;
  final DateTime? updatedAt;

  ChartFilter({
    required this.id,
    required this.name,
    required this.groups,
    this.groupLogicalOperator = LogicalOperator.and,
    DateTime? createdAt,
    this.updatedAt,
  }) : createdAt = createdAt ?? DateTime.now();

  /// 從JSON創建篩選器
  factory ChartFilter.fromJson(Map<String, dynamic> json) {
    return ChartFilter(
      id: json['id'] as String,
      name: json['name'] as String,
      groups: (json['groups'] as List)
          .map((g) => FilterGroup.fromJson(g as Map<String, dynamic>))
          .toList(),
      groupLogicalOperator: LogicalOperator.values[json['groupLogicalOperator'] as int],
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] != null 
          ? DateTime.parse(json['updatedAt'] as String)
          : null,
    );
  }

  /// 轉換為JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'groups': groups.map((g) => g.toJson()).toList(),
      'groupLogicalOperator': groupLogicalOperator.index,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  /// 創建副本
  ChartFilter copyWith({
    String? id,
    String? name,
    List<FilterGroup>? groups,
    LogicalOperator? groupLogicalOperator,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ChartFilter(
      id: id ?? this.id,
      name: name ?? this.name,
      groups: groups ?? this.groups,
      groupLogicalOperator: groupLogicalOperator ?? this.groupLogicalOperator,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }

  /// 添加篩選組
  ChartFilter addGroup(FilterGroup group) {
    return copyWith(
      groups: [...groups, group],
      updatedAt: DateTime.now(),
    );
  }

  /// 移除篩選組
  ChartFilter removeGroup(String groupId) {
    return copyWith(
      groups: groups.where((g) => g.id != groupId).toList(),
      updatedAt: DateTime.now(),
    );
  }

  /// 更新篩選組
  ChartFilter updateGroup(FilterGroup group) {
    final updatedGroups = groups.map((g) {
      return g.id == group.id ? group : g;
    }).toList();
    
    return copyWith(
      groups: updatedGroups,
      updatedAt: DateTime.now(),
    );
  }

  /// 獲取篩選器描述文字
  String getDescription() {
    if (groups.isEmpty) return '無篩選條件';
    
    final operatorText = groupLogicalOperator == LogicalOperator.and ? ' 且 ' : ' 或 ';
    return groups.map((g) => '(${g.getDescription()})').join(operatorText);
  }

  /// 檢查是否為空篩選器
  bool get isEmpty {
    return groups.isEmpty || groups.every((g) => g.conditions.isEmpty);
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ChartFilter &&
        other.id == id &&
        other.name == name &&
        listEquals(other.groups, groups) &&
        other.groupLogicalOperator == groupLogicalOperator;
  }

  @override
  int get hashCode {
    return Object.hash(id, name, groups, groupLogicalOperator);
  }
}
