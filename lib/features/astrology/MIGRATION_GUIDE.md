# 占星計算核心功能遷移指南

本指南說明如何將現有的占星功能遷移到新的模組化結構中。

## 📋 遷移清單

### ✅ 已完成的模組

#### 1. 核心計算模組 (`/calculations`)
- ✅ `planet_calculator.dart` - 行星位置計算
- ✅ `house_calculator.dart` - 宮位計算
- ✅ `aspect_calculator.dart` - 相位計算
- ✅ `arabic_parts_calculator.dart` - 阿拉伯點計算
- ✅ `progression_calculator.dart` - 推運計算

#### 2. 星曆表模組 (`/ephemeris`)
- ✅ `julian_date_converter.dart` - 儒略日轉換
- ✅ `timezone_calculator.dart` - 時區計算
- ✅ `swiss_ephemeris_wrapper.dart` - Swiss Ephemeris 包裝

#### 3. 工具模組 (`/utilities`)
- ✅ `angle_calculator.dart` - 角度計算工具
- ✅ `zodiac_calculator.dart` - 星座計算工具
- ✅ `coordinate_converter.dart` - 座標轉換
- ✅ `data_validator.dart` - 數據驗證

#### 4. 常數定義 (`/constants`)
- ✅ `astrology_constants.dart` - 占星常數
- ✅ `aspect_definitions.dart` - 相位定義
- ✅ `planet_definitions.dart` - 行星定義
- ✅ `zodiac_definitions.dart` - 星座定義
- ✅ `house_definitions.dart` - 宮位定義

#### 5. 分析模組 (`/analysis`)
- ✅ `dignity_analyzer.dart` - 行星尊貴力量分析
- ✅ `reception_analyzer.dart` - 互容接納分析

#### 6. 主要服務
- ✅ `astrology_service.dart` - 統一的占星服務 API
- ✅ `astrology.dart` - 模組導出文件

### ⏳ 待遷移的功能

#### 1. 需要遷移的現有文件

**從 `/services` 遷移到 `/astrology/calculations`：**
- `astrology_service.dart` → 已重構為新的統一服務
- `conjunction_cache_service.dart` → 需要整合到相位計算中
- `equinox_solstice_service.dart` → 需要遷移到天文計算模組
- `firdaria_service.dart` → 需要遷移到推運計算模組
- `profection_service.dart` → 需要遷移到推運計算模組

**從 `/utils` 遷移到 `/astrology/utilities`：**
- 角度計算相關的工具函數
- 座標轉換相關的工具函數
- 數據驗證相關的工具函數

**從 `/widgets/painter` 遷移到 `/astrology/rendering`：**
- `base_chart_painter.dart` → 基礎星盤繪製
- `chart_painter.dart` → 星盤繪製器
- `dual_chart_painter.dart` → 雙重星盤繪製
- `firdaria_chart_painter.dart` → Firdaria 圖表繪製

#### 2. 需要創建的新模組

**分析模組 (`/analysis`)：**
- `dignity_analyzer.dart` - 行星尊貴力量分析
- `reception_analyzer.dart` - 互容接納分析
- `classical_analyzer.dart` - 古典占星分析
- `modern_analyzer.dart` - 現代占星分析

**繪製模組 (`/rendering`)：**
- `chart_renderer.dart` - 星盤繪製引擎
- `planet_renderer.dart` - 行星繪製器
- `aspect_renderer.dart` - 相位繪製器
- `house_renderer.dart` - 宮位繪製器

## 🔄 遷移步驟

### 第一階段：核心計算功能遷移

1. **更新現有服務以使用新的計算模組**
   ```dart
   // 舊的方式
   import '../services/astrology_service.dart';
   
   // 新的方式
   import '../astrology/astrology.dart';
   ```

2. **替換直接的 Swiss Ephemeris 調用**
   ```dart
   // 舊的方式
   final result = sweCalcUt(julianDay, HeavenlyBody.SE_SUN);
   
   // 新的方式
   final planets = await PlanetCalculator.calculateAllPlanets(
     dateTime: dateTime,
     latitude: latitude,
     longitude: longitude,
   );
   ```

3. **使用統一的占星服務 API**
   ```dart
   // 新的統一 API
   final chartData = await AstrologyService.calculateChartData(
     chartData,
     planetVisibility: planetVisibility,
     aspectOrbs: aspectOrbs,
     houseSystem: houseSystem,
   );
   ```

### 第二階段：UI 組件更新

1. **更新星盤繪製組件**
   - 使用新的繪製模組
   - 統一繪製邏輯
   - 改善性能

2. **更新數據顯示組件**
   - 使用新的常數定義
   - 統一樣式和顏色
   - 改善用戶體驗

### 第三階段：服務整合

1. **整合現有服務**
   - 將特殊計算功能整合到新模組
   - 保持向後兼容性
   - 逐步淘汰舊的服務

2. **優化性能**
   - 實作計算結果緩存
   - 優化計算算法
   - 減少重複計算

## 📝 代碼示例

### 使用新的占星服務

```dart
import 'package:astreal/astrology/astrology.dart';

class ChartCalculationExample {
  Future<void> calculateChart() async {
    // 創建出生數據
    final birthData = BirthData(
      name: '測試用戶',
      dateTime: DateTime(1990, 1, 1, 12, 0),
      latitude: 25.0330,
      longitude: 121.5654,
      location: '台北',
    );
    
    // 創建星盤數據
    final chartData = ChartData(
      chartType: ChartType.natal,
      primaryPerson: birthData,
    );
    
    // 計算完整星盤
    final calculatedChart = await AstrologyService.calculateChartData(
      chartData,
      houseSystem: 'Placidus',
      includeMinorAspects: false,
    );
    
    // 使用計算結果
    print('行星數量: ${calculatedChart.planets.length}');
    print('相位數量: ${calculatedChart.aspects.length}');
  }
}
```

### 使用新的工具函數

```dart
import 'package:astreal/astrology/utilities/angle_calculator.dart';
import 'package:astreal/astrology/utilities/zodiac_calculator.dart';

class UtilityExample {
  void demonstrateUtilities() {
    // 角度計算
    final angle1 = 45.0;
    final angle2 = 315.0;
    final difference = AngleCalculator.calculateAngleDifference(angle1, angle2);
    print('角度差: $difference 度');
    
    // 星座計算
    final longitude = 125.5;
    final signInfo = ZodiacCalculator.getZodiacSign(longitude);
    print('星座: ${signInfo['name']}');
    print('位置: ${signInfo['positionDMS']}');
  }
}
```

## ⚠️ 注意事項

1. **向後兼容性**
   - 在遷移過程中保持現有 API 的可用性
   - 逐步標記舊 API 為 deprecated
   - 提供遷移路徑和文檔

2. **測試覆蓋**
   - 為新模組編寫完整的單元測試
   - 確保計算結果的準確性
   - 驗證性能改善

3. **文檔更新**
   - 更新 API 文檔
   - 提供使用示例
   - 編寫遷移指南

## 🎯 預期效益

1. **代碼組織**
   - 更清晰的模組結構
   - 更好的代碼重用性
   - 更容易維護和擴展

2. **性能改善**
   - 更高效的計算算法
   - 更好的緩存機制
   - 減少重複計算

3. **開發體驗**
   - 統一的 API 介面
   - 更好的類型安全
   - 更清晰的錯誤處理

4. **測試能力**
   - 更容易進行單元測試
   - 更好的測試覆蓋率
   - 更可靠的代碼質量
