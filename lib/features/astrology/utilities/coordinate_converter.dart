import 'dart:math' as math;

import 'angle_calculator.dart';

/// 座標轉換工具
/// 
/// 提供各種天文座標系統之間的轉換功能，包括：
/// - 黃道座標系 ↔ 赤道座標系
/// - 赤道座標系 ↔ 地平座標系
/// - 極坐標 ↔ 直角座標
/// - 地理座標轉換
class CoordinateConverter {
  /// 黃道傾角（度）- 2000.0 年元
  static const double obliquityOfEcliptic = 23.4392911;
  
  /// 將黃道座標轉換為赤道座標
  /// 
  /// [eclipticLongitude] 黃經（度）
  /// [eclipticLatitude] 黃緯（度）
  /// [obliquity] 黃道傾角（度，可選）
  /// 
  /// 返回 {'rightAscension': 赤經, 'declination': 赤緯}
  static Map<String, double> eclipticToEquatorial(
    double eclipticLongitude,
    double eclipticLatitude, {
    double? obliquity,
  }) {
    final obl = obliquity ?? obliquityOfEcliptic;
    
    // 轉換為弧度
    final lambda = AngleCalculator.degreesToRadians(eclipticLongitude);
    final beta = AngleCalculator.degreesToRadians(eclipticLatitude);
    final epsilon = AngleCalculator.degreesToRadians(obl);
    
    // 計算赤經
    final rightAscension = math.atan2(
      math.sin(lambda) * math.cos(epsilon) - math.tan(beta) * math.sin(epsilon),
      math.cos(lambda),
    );
    
    // 計算赤緯
    final declination = math.asin(
      math.sin(beta) * math.cos(epsilon) + 
      math.cos(beta) * math.sin(epsilon) * math.sin(lambda),
    );
    
    return {
      'rightAscension': AngleCalculator.normalizeAngle(
        AngleCalculator.radiansToDegrees(rightAscension),
      ),
      'declination': AngleCalculator.radiansToDegrees(declination),
    };
  }
  
  /// 將赤道座標轉換為黃道座標
  /// 
  /// [rightAscension] 赤經（度）
  /// [declination] 赤緯（度）
  /// [obliquity] 黃道傾角（度，可選）
  /// 
  /// 返回 {'longitude': 黃經, 'latitude': 黃緯}
  static Map<String, double> equatorialToEcliptic(
    double rightAscension,
    double declination, {
    double? obliquity,
  }) {
    final obl = obliquity ?? obliquityOfEcliptic;
    
    // 轉換為弧度
    final alpha = AngleCalculator.degreesToRadians(rightAscension);
    final delta = AngleCalculator.degreesToRadians(declination);
    final epsilon = AngleCalculator.degreesToRadians(obl);
    
    // 計算黃經
    final longitude = math.atan2(
      math.sin(alpha) * math.cos(epsilon) + math.tan(delta) * math.sin(epsilon),
      math.cos(alpha),
    );
    
    // 計算黃緯
    final latitude = math.asin(
      math.sin(delta) * math.cos(epsilon) - 
      math.cos(delta) * math.sin(epsilon) * math.sin(alpha),
    );
    
    return {
      'longitude': AngleCalculator.normalizeAngle(
        AngleCalculator.radiansToDegrees(longitude),
      ),
      'latitude': AngleCalculator.radiansToDegrees(latitude),
    };
  }
  
  /// 將赤道座標轉換為地平座標
  /// 
  /// [rightAscension] 赤經（度）
  /// [declination] 赤緯（度）
  /// [latitude] 觀測地緯度（度）
  /// [localSiderealTime] 當地恆星時（度）
  /// 
  /// 返回 {'azimuth': 方位角, 'altitude': 高度角}
  static Map<String, double> equatorialToHorizontal(
    double rightAscension,
    double declination,
    double latitude,
    double localSiderealTime,
  ) {
    // 計算時角
    final hourAngle = localSiderealTime - rightAscension;
    
    // 轉換為弧度
    final h = AngleCalculator.degreesToRadians(hourAngle);
    final delta = AngleCalculator.degreesToRadians(declination);
    final phi = AngleCalculator.degreesToRadians(latitude);
    
    // 計算高度角
    final altitude = math.asin(
      math.sin(phi) * math.sin(delta) + 
      math.cos(phi) * math.cos(delta) * math.cos(h),
    );
    
    // 計算方位角
    final azimuth = math.atan2(
      -math.sin(h),
      math.tan(delta) * math.cos(phi) - math.sin(phi) * math.cos(h),
    );
    
    return {
      'azimuth': AngleCalculator.normalizeAngle(
        AngleCalculator.radiansToDegrees(azimuth),
      ),
      'altitude': AngleCalculator.radiansToDegrees(altitude),
    };
  }
  
  /// 將地平座標轉換為赤道座標
  /// 
  /// [azimuth] 方位角（度）
  /// [altitude] 高度角（度）
  /// [latitude] 觀測地緯度（度）
  /// [localSiderealTime] 當地恆星時（度）
  /// 
  /// 返回 {'rightAscension': 赤經, 'declination': 赤緯}
  static Map<String, double> horizontalToEquatorial(
    double azimuth,
    double altitude,
    double latitude,
    double localSiderealTime,
  ) {
    // 轉換為弧度
    final a = AngleCalculator.degreesToRadians(azimuth);
    final alt = AngleCalculator.degreesToRadians(altitude);
    final phi = AngleCalculator.degreesToRadians(latitude);
    
    // 計算赤緯
    final declination = math.asin(
      math.sin(phi) * math.sin(alt) + 
      math.cos(phi) * math.cos(alt) * math.cos(a),
    );
    
    // 計算時角
    final hourAngle = math.atan2(
      -math.sin(a),
      math.tan(alt) * math.cos(phi) - math.sin(phi) * math.cos(a),
    );
    
    // 計算赤經
    final rightAscension = localSiderealTime - 
        AngleCalculator.radiansToDegrees(hourAngle);
    
    return {
      'rightAscension': AngleCalculator.normalizeAngle(rightAscension),
      'declination': AngleCalculator.radiansToDegrees(declination),
    };
  }
  
  /// 將極坐標轉換為直角座標
  /// 
  /// [radius] 半徑
  /// [angle] 角度（度）
  /// 
  /// 返回 {'x': x座標, 'y': y座標}
  static Map<String, double> polarToCartesian(double radius, double angle) {
    final angleRad = AngleCalculator.degreesToRadians(angle);
    
    return {
      'x': radius * math.cos(angleRad),
      'y': radius * math.sin(angleRad),
    };
  }
  
  /// 將直角座標轉換為極坐標
  /// 
  /// [x] x座標
  /// [y] y座標
  /// 
  /// 返回 {'radius': 半徑, 'angle': 角度}
  static Map<String, double> cartesianToPolar(double x, double y) {
    final radius = math.sqrt(x * x + y * y);
    final angle = AngleCalculator.normalizeAngle(
      AngleCalculator.radiansToDegrees(math.atan2(y, x)),
    );
    
    return {
      'radius': radius,
      'angle': angle,
    };
  }
  
  /// 計算兩點間的大圓距離
  /// 
  /// [lat1] 第一點緯度（度）
  /// [lon1] 第一點經度（度）
  /// [lat2] 第二點緯度（度）
  /// [lon2] 第二點經度（度）
  /// 
  /// 返回距離（度）
  static double calculateGreatCircleDistance(
    double lat1,
    double lon1,
    double lat2,
    double lon2,
  ) {
    // 轉換為弧度
    final phi1 = AngleCalculator.degreesToRadians(lat1);
    final phi2 = AngleCalculator.degreesToRadians(lat2);
    final deltaLambda = AngleCalculator.degreesToRadians(lon2 - lon1);
    
    // 使用 Haversine 公式
    final a = math.sin((phi2 - phi1) / 2) * math.sin((phi2 - phi1) / 2) +
        math.cos(phi1) * math.cos(phi2) *
        math.sin(deltaLambda / 2) * math.sin(deltaLambda / 2);
    
    final c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a));
    
    return AngleCalculator.radiansToDegrees(c);
  }
  
  /// 計算方位角
  /// 
  /// [lat1] 起點緯度（度）
  /// [lon1] 起點經度（度）
  /// [lat2] 終點緯度（度）
  /// [lon2] 終點經度（度）
  /// 
  /// 返回方位角（度，從北方順時針測量）
  static double calculateBearing(
    double lat1,
    double lon1,
    double lat2,
    double lon2,
  ) {
    // 轉換為弧度
    final phi1 = AngleCalculator.degreesToRadians(lat1);
    final phi2 = AngleCalculator.degreesToRadians(lat2);
    final deltaLambda = AngleCalculator.degreesToRadians(lon2 - lon1);
    
    final y = math.sin(deltaLambda) * math.cos(phi2);
    final x = math.cos(phi1) * math.sin(phi2) -
        math.sin(phi1) * math.cos(phi2) * math.cos(deltaLambda);
    
    final bearing = math.atan2(y, x);
    
    return AngleCalculator.normalizeAngle(
      AngleCalculator.radiansToDegrees(bearing),
    );
  }
  
  /// 將地理座標轉換為投影座標（簡化的墨卡托投影）
  /// 
  /// [latitude] 緯度（度）
  /// [longitude] 經度（度）
  /// 
  /// 返回 {'x': x座標, 'y': y座標}
  static Map<String, double> geographicToMercator(
    double latitude,
    double longitude,
  ) {
    final x = longitude;
    final y = AngleCalculator.radiansToDegrees(
      math.log(math.tan(math.pi / 4 + AngleCalculator.degreesToRadians(latitude) / 2)),
    );
    
    return {'x': x, 'y': y};
  }
  
  /// 將投影座標轉換為地理座標（簡化的墨卡托投影）
  /// 
  /// [x] x座標
  /// [y] y座標
  /// 
  /// 返回 {'latitude': 緯度, 'longitude': 經度}
  static Map<String, double> mercatorToGeographic(double x, double y) {
    final longitude = x;
    final latitude = AngleCalculator.radiansToDegrees(
      2 * math.atan(math.exp(AngleCalculator.degreesToRadians(y))) - math.pi / 2,
    );
    
    return {'latitude': latitude, 'longitude': longitude};
  }
  
  /// 計算視差修正
  /// 
  /// [altitude] 高度角（度）
  /// [distance] 距離（天文單位）
  /// 
  /// 返回視差修正（度）
  static double calculateParallax(double altitude, double distance) {
    // 地球半徑（天文單位）
    const earthRadius = 4.2635e-5;
    
    final altitudeRad = AngleCalculator.degreesToRadians(altitude);
    final parallax = math.asin(earthRadius * math.cos(altitudeRad) / distance);
    
    return AngleCalculator.radiansToDegrees(parallax);
  }
  
  /// 計算大氣折射修正
  /// 
  /// [apparentAltitude] 視高度（度）
  /// [temperature] 溫度（攝氏度，可選）
  /// [pressure] 氣壓（毫巴，可選）
  /// 
  /// 返回折射修正（度）
  static double calculateRefraction(
    double apparentAltitude, {
    double temperature = 10.0,
    double pressure = 1010.0,
  }) {
    if (apparentAltitude < -2.0) return 0.0;
    
    final h = apparentAltitude;
    final t = temperature;
    final p = pressure;
    
    // 簡化的折射公式
    double refraction;
    
    if (h >= 15.0) {
      refraction = 0.00452 * p / ((273 + t) * math.tan(AngleCalculator.degreesToRadians(h)));
    } else if (h >= -0.575) {
      final h0 = h + 7.31 / (h + 4.4);
      refraction = 0.0167 * p / ((273 + t) * math.tan(AngleCalculator.degreesToRadians(h0)));
    } else {
      refraction = 0.0;
    }
    
    return refraction;
  }
}
