
import '../../../data/models/astrology/aspect_info.dart';
import '../../../data/models/astrology/planet_position.dart';
import '../../../data/models/user/birth_data.dart';

/// 數據驗證工具
/// 
/// 提供各種占星數據的驗證功能，包括：
/// - 出生數據驗證
/// - 行星位置驗證
/// - 相位數據驗證
/// - 角度範圍驗證
class DataValidator {
  /// 驗證出生數據
  /// 
  /// [birthData] 出生數據
  /// 
  /// 返回驗證結果和錯誤信息
  static ValidationResult validateBirthData(BirthData birthData) {
    final errors = <String>[];
    
    // 驗證姓名
    if (birthData.name.trim().isEmpty) {
      errors.add('姓名不能為空');
    }
    
    if (birthData.name.length > 50) {
      errors.add('姓名長度不能超過 50 個字符');
    }
    
    // 驗證日期時間
    final dateValidation = validateDateTime(birthData.dateTime);
    if (!dateValidation.isValid) {
      errors.addAll(dateValidation.errors);
    }
    
    // 驗證地理座標
    final coordinateValidation = validateCoordinates(
      birthData.latitude,
      birthData.longitude,
    );
    if (!coordinateValidation.isValid) {
      errors.addAll(coordinateValidation.errors);
    }
    
    // 驗證地點名稱
    if (birthData.birthPlace.trim().isEmpty) {
      errors.add('出生地點不能為空');
    }

    if (birthData.birthPlace.length > 100) {
      errors.add('出生地點名稱長度不能超過 100 個字符');
    }
    
    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
    );
  }
  
  /// 驗證日期時間
  /// 
  /// [dateTime] 日期時間
  /// 
  /// 返回驗證結果
  static ValidationResult validateDateTime(DateTime dateTime) {
    final errors = <String>[];
    final now = DateTime.now();
    
    // 檢查日期範圍（1900年到未來100年）
    final minDate = DateTime(1900, 1, 1);
    final maxDate = now.add(const Duration(days: 36525)); // 100年
    
    if (dateTime.isBefore(minDate)) {
      errors.add('出生日期不能早於 1900年1月1日');
    }
    
    if (dateTime.isAfter(maxDate)) {
      errors.add('出生日期不能晚於 ${maxDate.year}年');
    }
    
    // 檢查未來日期警告
    if (dateTime.isAfter(now)) {
      errors.add('警告：出生日期在未來');
    }
    
    // 檢查時間合理性
    if (dateTime.hour < 0 || dateTime.hour > 23) {
      errors.add('小時必須在 0-23 之間');
    }
    
    if (dateTime.minute < 0 || dateTime.minute > 59) {
      errors.add('分鐘必須在 0-59 之間');
    }
    
    if (dateTime.second < 0 || dateTime.second > 59) {
      errors.add('秒數必須在 0-59 之間');
    }
    
    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
    );
  }
  
  /// 驗證地理座標
  /// 
  /// [latitude] 緯度
  /// [longitude] 經度
  /// 
  /// 返回驗證結果
  static ValidationResult validateCoordinates(double latitude, double longitude) {
    final errors = <String>[];
    
    // 驗證緯度範圍
    if (latitude < -90.0 || latitude > 90.0) {
      errors.add('緯度必須在 -90° 到 90° 之間');
    }
    
    // 驗證經度範圍
    if (longitude < -180.0 || longitude > 180.0) {
      errors.add('經度必須在 -180° 到 180° 之間');
    }
    
    // 檢查是否為有效數值
    if (latitude.isNaN || latitude.isInfinite) {
      errors.add('緯度必須是有效的數值');
    }
    
    if (longitude.isNaN || longitude.isInfinite) {
      errors.add('經度必須是有效的數值');
    }
    
    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
    );
  }
  
  /// 驗證行星位置數據
  /// 
  /// [planet] 行星位置
  /// 
  /// 返回驗證結果
  static ValidationResult validatePlanetPosition(PlanetPosition planet) {
    final errors = <String>[];
    
    // 驗證行星名稱
    if (planet.name.trim().isEmpty) {
      errors.add('行星名稱不能為空');
    }
    
    // 驗證黃經範圍
    if (planet.longitude < 0.0 || planet.longitude >= 360.0) {
      errors.add('黃經必須在 0° 到 360° 之間');
    }
    
    // 驗證黃緯範圍
    if (planet.latitude < -90.0 || planet.latitude > 90.0) {
      errors.add('黃緯必須在 -90° 到 90° 之間');
    }
    
    // 驗證距離（應為正數）
    if (planet.distance < 0.0) {
      errors.add('距離不能為負數');
    }
    
    // 驗證宮位範圍
    if (planet.house < 0 || planet.house > 12) {
      errors.add('宮位必須在 0 到 12 之間');
    }
    
    // 驗證星座名稱
    if (planet.sign.trim().isEmpty) {
      errors.add('星座名稱不能為空');
    }
    
    // 檢查數值有效性
    if (planet.longitude.isNaN || planet.longitude.isInfinite) {
      errors.add('黃經必須是有效的數值');
    }
    
    if (planet.latitude.isNaN || planet.latitude.isInfinite) {
      errors.add('黃緯必須是有效的數值');
    }
    
    if (planet.distance.isNaN || planet.distance.isInfinite) {
      errors.add('距離必須是有效的數值');
    }
    
    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
    );
  }
  
  /// 驗證相位數據
  /// 
  /// [aspect] 相位信息
  /// 
  /// 返回驗證結果
  static ValidationResult validateAspectInfo(AspectInfo aspect) {
    final errors = <String>[];
    
    // 驗證行星名稱
    if (aspect.planet1.name.trim().isEmpty) {
      errors.add('第一個行星名稱不能為空');
    }
    
    if (aspect.planet2.name.trim().isEmpty) {
      errors.add('第二個行星名稱不能為空');
    }
    
    // 驗證相位名稱
    if (aspect.aspect.trim().isEmpty) {
      errors.add('相位名稱不能為空');
    }
    
    // 驗證角度範圍
    if (aspect.angle < 0 || aspect.angle > 360) {
      errors.add('相位角度必須在 0° 到 360° 之間');
    }
    
    // 驗證容許度
    if (aspect.orb < 0.0 || aspect.orb > 15.0) {
      errors.add('容許度必須在 0° 到 15° 之間');
    }
    
    // 檢查數值有效性
    if (aspect.orb.isNaN || aspect.orb.isInfinite) {
      errors.add('容許度必須是有效的數值');
    }
    
    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
    );
  }
  
  /// 驗證角度範圍
  /// 
  /// [angle] 角度
  /// [min] 最小值（可選）
  /// [max] 最大值（可選）
  /// 
  /// 返回驗證結果
  static ValidationResult validateAngle(
    double angle, {
    double min = 0.0,
    double max = 360.0,
  }) {
    final errors = <String>[];
    
    if (angle < min || angle > max) {
      errors.add('角度必須在 $min° 到 $max° 之間');
    }
    
    if (angle.isNaN || angle.isInfinite) {
      errors.add('角度必須是有效的數值');
    }
    
    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
    );
  }
  
  /// 驗證宮位系統名稱
  /// 
  /// [houseSystem] 宮位系統名稱
  /// 
  /// 返回驗證結果
  static ValidationResult validateHouseSystem(String houseSystem) {
    final errors = <String>[];
    
    const validSystems = {
      'Placidus',
      'Koch',
      'Equal House',
      'Whole Sign',
      'Campanus',
      'Regiomontanus',
      'Topocentric',
      'Alcabitus',
      'Morinus',
      'Krusinski',
      'Porphyrius',
      'Meridian',
      'Azimuthal',
      'Horizontal',
      'Carter Poli Equatorial',
      'Pullen SD',
      'Pullen SR',
      'Sunshine',
      'Savard-A',
      'Polich/Page',
      'Vehlow Equal',
      'Axial Rotation',
    };
    
    if (!validSystems.contains(houseSystem)) {
      errors.add('不支援的宮位系統: $houseSystem');
    }
    
    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
    );
  }
  
  /// 驗證行星可見性設定
  /// 
  /// [planetVisibility] 行星可見性設定
  /// 
  /// 返回驗證結果
  static ValidationResult validatePlanetVisibility(
    Map<String, bool> planetVisibility,
  ) {
    final errors = <String>[];
    
    const validPlanets = {
      '太陽',
      '月亮',
      '水星',
      '金星',
      '火星',
      '木星',
      '土星',
      '天王星',
      '海王星',
      '冥王星',
      '北交點',
      '南交點',
      '凱龍星',
    };
    
    for (final planetName in planetVisibility.keys) {
      if (!validPlanets.contains(planetName)) {
        errors.add('未知的行星名稱: $planetName');
      }
    }
    
    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
    );
  }
  
  /// 驗證相位容許度設定
  /// 
  /// [aspectOrbs] 相位容許度設定
  /// 
  /// 返回驗證結果
  static ValidationResult validateAspectOrbs(Map<String, double> aspectOrbs) {
    final errors = <String>[];
    
    const validAspects = {
      '合相',
      '對沖',
      '三分相',
      '四分相',
      '六分相',
      '半四分相',
      '半三分相',
      '五分相',
      '八分相',
    };
    
    for (final entry in aspectOrbs.entries) {
      final aspectName = entry.key;
      final orb = entry.value;
      
      if (!validAspects.contains(aspectName)) {
        errors.add('未知的相位名稱: $aspectName');
      }
      
      if (orb < 0.0 || orb > 15.0) {
        errors.add('$aspectName 的容許度必須在 0° 到 15° 之間');
      }
      
      if (orb.isNaN || orb.isInfinite) {
        errors.add('$aspectName 的容許度必須是有效的數值');
      }
    }
    
    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
    );
  }
}

/// 驗證結果類
class ValidationResult {
  final bool isValid;
  final List<String> errors;
  
  const ValidationResult({
    required this.isValid,
    required this.errors,
  });
  
  /// 獲取第一個錯誤信息
  String? get firstError => errors.isNotEmpty ? errors.first : null;
  
  /// 獲取所有錯誤信息的字符串
  String get errorMessage => errors.join(', ');
  
  /// 檢查是否有特定類型的錯誤
  bool hasErrorContaining(String keyword) {
    return errors.any((error) => error.contains(keyword));
  }
  
  @override
  String toString() {
    return 'ValidationResult(isValid: $isValid, errors: $errors)';
  }
}
