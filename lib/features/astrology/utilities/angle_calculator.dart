import 'dart:math' as math;

/// 角度計算工具類
/// 
/// 提供各種角度計算功能，包括：
/// - 角度標準化
/// - 角度差計算
/// - 角度轉換
/// - 三角函數計算
class AngleCalculator {
  /// 將角度標準化到 0-360 度範圍
  /// 
  /// [angle] 輸入角度
  /// 
  /// 返回標準化後的角度（0 <= angle < 360）
  static double normalizeAngle(double angle) {
    double normalized = angle % 360;
    if (normalized < 0) {
      normalized += 360;
    }
    return normalized;
  }
  
  /// 計算兩個角度之間的最小差值
  /// 
  /// [angle1] 第一個角度
  /// [angle2] 第二個角度
  /// 
  /// 返回角度差（0-180度）
  static double calculateAngleDifference(double angle1, double angle2) {
    final normalizedAngle1 = normalizeAngle(angle1);
    final normalizedAngle2 = normalizeAngle(angle2);
    
    double difference = (normalizedAngle1 - normalizedAngle2).abs();
    
    // 取較小的角度差
    if (difference > 180) {
      difference = 360 - difference;
    }
    
    return difference;
  }
  
  /// 計算從第一個角度到第二個角度的有向角度差
  /// 
  /// [fromAngle] 起始角度
  /// [toAngle] 目標角度
  /// 
  /// 返回有向角度差（-180 到 180 度）
  /// 正值表示順時針方向，負值表示逆時針方向
  static double calculateDirectedAngleDifference(double fromAngle, double toAngle) {
    final normalizedFrom = normalizeAngle(fromAngle);
    final normalizedTo = normalizeAngle(toAngle);
    
    double difference = normalizedTo - normalizedFrom;
    
    // 調整到 -180 到 180 度範圍
    if (difference > 180) {
      difference -= 360;
    } else if (difference < -180) {
      difference += 360;
    }
    
    return difference;
  }
  
  /// 檢查角度是否在指定範圍內
  /// 
  /// [angle] 要檢查的角度
  /// [startAngle] 範圍起始角度
  /// [endAngle] 範圍結束角度
  /// 
  /// 返回是否在範圍內
  static bool isAngleInRange(double angle, double startAngle, double endAngle) {
    final normalizedAngle = normalizeAngle(angle);
    final normalizedStart = normalizeAngle(startAngle);
    final normalizedEnd = normalizeAngle(endAngle);
    
    if (normalizedStart <= normalizedEnd) {
      // 正常情況：範圍不跨越 0 度
      return normalizedAngle >= normalizedStart && normalizedAngle <= normalizedEnd;
    } else {
      // 特殊情況：範圍跨越 0 度
      return normalizedAngle >= normalizedStart || normalizedAngle <= normalizedEnd;
    }
  }
  
  /// 計算角度的中點
  /// 
  /// [angle1] 第一個角度
  /// [angle2] 第二個角度
  /// 
  /// 返回中點角度
  static double calculateMidpoint(double angle1, double angle2) {
    final normalizedAngle1 = normalizeAngle(angle1);
    final normalizedAngle2 = normalizeAngle(angle2);
    
    double midpoint;
    
    if ((normalizedAngle1 - normalizedAngle2).abs() <= 180) {
      // 直接計算中點
      midpoint = (normalizedAngle1 + normalizedAngle2) / 2;
    } else {
      // 跨越 0 度的情況
      midpoint = (normalizedAngle1 + normalizedAngle2 + 360) / 2;
      if (midpoint >= 360) {
        midpoint -= 360;
      }
    }
    
    return normalizeAngle(midpoint);
  }
  
  /// 將度數轉換為度分秒格式
  /// 
  /// [degrees] 度數
  /// 
  /// 返回度分秒字符串
  static String degreesToDMS(double degrees) {
    final normalizedDegrees = normalizeAngle(degrees);
    
    final deg = normalizedDegrees.floor();
    final minutes = ((normalizedDegrees - deg) * 60);
    final min = minutes.floor();
    final seconds = ((minutes - min) * 60);
    final sec = seconds.round();
    
    return '${deg}°${min.toString().padLeft(2, '0')}\'${sec.toString().padLeft(2, '0')}"';
  }
  
  /// 將度分秒格式轉換為度數
  /// 
  /// [degrees] 度
  /// [minutes] 分
  /// [seconds] 秒
  /// 
  /// 返回度數
  static double dmsToDegrees(int degrees, int minutes, int seconds) {
    return degrees + minutes / 60.0 + seconds / 3600.0;
  }
  
  /// 將度數轉換為弧度
  /// 
  /// [degrees] 度數
  /// 
  /// 返回弧度
  static double degreesToRadians(double degrees) {
    return degrees * math.pi / 180.0;
  }
  
  /// 將弧度轉換為度數
  /// 
  /// [radians] 弧度
  /// 
  /// 返回度數
  static double radiansToDegrees(double radians) {
    return radians * 180.0 / math.pi;
  }
  
  /// 計算正弦值（輸入為度數）
  /// 
  /// [degrees] 度數
  /// 
  /// 返回正弦值
  static double sinDegrees(double degrees) {
    return math.sin(degreesToRadians(degrees));
  }
  
  /// 計算餘弦值（輸入為度數）
  /// 
  /// [degrees] 度數
  /// 
  /// 返回餘弦值
  static double cosDegrees(double degrees) {
    return math.cos(degreesToRadians(degrees));
  }
  
  /// 計算正切值（輸入為度數）
  /// 
  /// [degrees] 度數
  /// 
  /// 返回正切值
  static double tanDegrees(double degrees) {
    return math.tan(degreesToRadians(degrees));
  }
  
  /// 計算反正弦值（返回度數）
  /// 
  /// [value] 輸入值
  /// 
  /// 返回度數
  static double asinDegrees(double value) {
    return radiansToDegrees(math.asin(value));
  }
  
  /// 計算反餘弦值（返回度數）
  /// 
  /// [value] 輸入值
  /// 
  /// 返回度數
  static double acosDegrees(double value) {
    return radiansToDegrees(math.acos(value));
  }
  
  /// 計算反正切值（返回度數）
  /// 
  /// [value] 輸入值
  /// 
  /// 返回度數
  static double atanDegrees(double value) {
    return radiansToDegrees(math.atan(value));
  }
  
  /// 計算兩點間的角度（atan2，返回度數）
  /// 
  /// [y] Y 座標
  /// [x] X 座標
  /// 
  /// 返回角度（度數）
  static double atan2Degrees(double y, double x) {
    return normalizeAngle(radiansToDegrees(math.atan2(y, x)));
  }
  
  /// 檢查角度是否接近（在指定容差範圍內）
  /// 
  /// [angle1] 第一個角度
  /// [angle2] 第二個角度
  /// [tolerance] 容差（度數）
  /// 
  /// 返回是否接近
  static bool areAnglesClose(double angle1, double angle2, double tolerance) {
    final difference = calculateAngleDifference(angle1, angle2);
    return difference <= tolerance;
  }
  
  /// 將角度四捨五入到指定精度
  /// 
  /// [angle] 角度
  /// [precision] 精度（小數位數）
  /// 
  /// 返回四捨五入後的角度
  static double roundAngle(double angle, int precision) {
    final factor = math.pow(10, precision);
    return (normalizeAngle(angle) * factor).round() / factor;
  }
  
  /// 計算角度的象限
  /// 
  /// [angle] 角度
  /// 
  /// 返回象限（1-4）
  static int getQuadrant(double angle) {
    final normalizedAngle = normalizeAngle(angle);
    
    if (normalizedAngle >= 0 && normalizedAngle < 90) {
      return 1;
    } else if (normalizedAngle >= 90 && normalizedAngle < 180) {
      return 2;
    } else if (normalizedAngle >= 180 && normalizedAngle < 270) {
      return 3;
    } else {
      return 4;
    }
  }
  
  /// 計算角度在圓周上的對稱點
  /// 
  /// [angle] 原始角度
  /// [centerAngle] 對稱中心角度（預設為 0）
  /// 
  /// 返回對稱點角度
  static double getSymmetricAngle(double angle, {double centerAngle = 0}) {
    final normalizedAngle = normalizeAngle(angle);
    final normalizedCenter = normalizeAngle(centerAngle);
    
    final symmetricAngle = 2 * normalizedCenter - normalizedAngle;
    return normalizeAngle(symmetricAngle);
  }
}
