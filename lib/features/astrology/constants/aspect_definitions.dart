import 'package:flutter/material.dart';

/// 相位定義
/// 
/// 包含所有相位的角度、容許度、符號等信息
class AspectDefinitions {
  /// 主要相位定義
  static const List<Map<String, dynamic>> majorAspects = [
    {
      'name': '合相',
      'englishName': 'Conjunction',
      'symbol': '☌',
      'shortZh': '合',
      'angle': 0.0,
      'orb': 8.0,
      'color': Color(0xFFFF6B6B),
      'nature': '中性',
      'strength': 'very_strong',
      'keywords': ['融合', '結合', '強化', '統一'],
      'description': '兩行星能量完全融合，產生強烈的聯合效應',
    },
    {
      'name': '對沖',
      'englishName': 'Opposition',
      'symbol': '☍',
      'shortZh': '沖',
      'angle': 180.0,
      'orb': 8.0,
      'color': Color(0xFFE74C3C),
      'nature': '困難',
      'strength': 'very_strong',
      'keywords': ['對立', '平衡', '緊張', '整合'],
      'description': '兩行星能量對立，需要尋求平衡和整合',
    },
    {
      'name': '三分相',
      'englishName': 'Trine',
      'symbol': '△',
      'shortZh': '拱',
      'angle': 120.0,
      'orb': 6.0,
      'color': Color(0xFF2ECC71),
      'nature': '和諧',
      'strength': 'strong',
      'keywords': ['和諧', '流動', '天賦', '機會'],
      'description': '兩行星能量和諧流動，帶來天賦和機會',
    },
    {
      'name': '四分相',
      'englishName': 'Square',
      'symbol': '□',
      'shortZh': '刑',
      'angle': 90.0,
      'orb': 6.0,
      'color': Color(0xFFF39C12),
      'nature': '困難',
      'strength': 'strong',
      'keywords': ['挑戰', '衝突', '成長', '動力'],
      'description': '兩行星能量衝突，帶來挑戰和成長機會',
    },
    {
      'name': '六分相',
      'englishName': 'Sextile',
      'symbol': '⚹',
      'shortZh': '六',
      'angle': 60.0,
      'orb': 4.0,
      'color': Color(0xFF3498DB),
      'nature': '和諧',
      'strength': 'moderate',
      'keywords': ['合作', '機會', '溝通', '支持'],
      'description': '兩行星能量輕鬆合作，帶來機會和支持',
    },
  ];
  
  /// 次要相位定義
  static const List<Map<String, dynamic>> minorAspects = [
    {
      'name': '半四分相',
      'englishName': 'Semi-Square',
      'symbol': '∠',
      'shortZh': '半刑',
      'angle': 45.0,
      'orb': 2.0,
      'color': Color(0xFFE67E22),
      'nature': '困難',
      'strength': 'weak',
      'keywords': ['摩擦', '調整', '輕微緊張'],
      'description': '輕微的緊張和摩擦，需要調整',
    },
    {
      'name': '半三分相',
      'englishName': 'Semi-Sextile',
      'symbol': '⚺',
      'shortZh': '半六',
      'angle': 30.0,
      'orb': 2.0,
      'color': Color(0xFF9B59B6),
      'nature': '中性',
      'strength': 'weak',
      'keywords': ['連接', '學習', '適應'],
      'description': '微妙的連接，帶來學習和適應的機會',
    },
    {
      'name': '五分相',
      'englishName': 'Quintile',
      'symbol': 'Q',
      'shortZh': '五分',
      'angle': 72.0,
      'orb': 2.0,
      'color': Color(0xFF1ABC9C),
      'nature': '創造',
      'strength': 'weak',
      'keywords': ['創造', '天賦', '藝術'],
      'description': '創造性的天賦和藝術能力',
    },
    {
      'name': '八分相',
      'englishName': 'Sesquiquadrate',
      'symbol': '⚼',
      'shortZh': '八分',
      'angle': 135.0,
      'orb': 2.0,
      'color': Color(0xFFD35400),
      'nature': '困難',
      'strength': 'weak',
      'keywords': ['壓力', '調整', '釋放'],
      'description': '需要調整和釋放的壓力',
    },
    {
      'name': '十二分相',
      'englishName': 'Quincunx',
      'symbol': '⚻',
      'shortZh': '補十二',
      'angle': 150.0,
      'orb': 2.0,
      'color': Color(0xFF8E44AD),
      'nature': '調整',
      'strength': 'moderate',
      'keywords': ['調整', '適應', '重新定向'],
      'description': '需要調整和重新定向的能量',
    },
  ];
  
  /// 所有相位（主要 + 次要）
  static List<Map<String, dynamic>> get allAspects => [...majorAspects, ...minorAspects];
  
  /// 根據相位名稱獲取定義
  static Map<String, dynamic>? getAspectDefinition(String aspectName) {
    return allAspects.firstWhere(
      (aspect) => aspect['name'] == aspectName,
      orElse: () => {},
    );
  }
  
  /// 根據角度獲取相位
  static Map<String, dynamic>? getAspectByAngle(double angle, {double tolerance = 1.0}) {
    for (final aspect in allAspects) {
      final aspectAngle = aspect['angle'] as double;
      if ((angle - aspectAngle).abs() <= tolerance) {
        return aspect;
      }
    }
    return null;
  }
  
  /// 獲取預設容許度
  static double getDefaultOrb(String aspectName) {
    final aspect = getAspectDefinition(aspectName);
    return aspect?['orb'] ?? 2.0;
  }
  
  /// 獲取相位顏色
  static Color getAspectColor(String aspectName) {
    final aspect = getAspectDefinition(aspectName);
    return aspect?['color'] ?? Colors.grey;
  }
  
  /// 獲取相位符號
  static String getAspectSymbol(String aspectName) {
    final aspect = getAspectDefinition(aspectName);
    return aspect?['symbol'] ?? '';
  }
  
  /// 獲取相位簡稱
  static String getAspectShortName(String aspectName) {
    final aspect = getAspectDefinition(aspectName);
    return aspect?['shortZh'] ?? '';
  }
  
  /// 檢查是否為主要相位
  static bool isMajorAspect(String aspectName) {
    return majorAspects.any((aspect) => aspect['name'] == aspectName);
  }
  
  /// 檢查是否為次要相位
  static bool isMinorAspect(String aspectName) {
    return minorAspects.any((aspect) => aspect['name'] == aspectName);
  }
  
  /// 獲取和諧相位列表
  static List<String> getHarmoniousAspects() {
    return allAspects
        .where((aspect) => aspect['nature'] == '和諧')
        .map((aspect) => aspect['name'] as String)
        .toList();
  }
  
  /// 獲取困難相位列表
  static List<String> getChallengingAspects() {
    return allAspects
        .where((aspect) => aspect['nature'] == '困難')
        .map((aspect) => aspect['name'] as String)
        .toList();
  }
  
  /// 獲取中性相位列表
  static List<String> getNeutralAspects() {
    return allAspects
        .where((aspect) => aspect['nature'] == '中性' || aspect['nature'] == '創造' || aspect['nature'] == '調整')
        .map((aspect) => aspect['name'] as String)
        .toList();
  }
  
  /// 根據強度獲取相位
  static List<String> getAspectsByStrength(String strength) {
    return allAspects
        .where((aspect) => aspect['strength'] == strength)
        .map((aspect) => aspect['name'] as String)
        .toList();
  }
  
  /// 獲取相位的詳細信息
  static Map<String, dynamic> getAspectInfo(String aspectName) {
    final aspect = getAspectDefinition(aspectName);
    if (aspect == null || aspect.isEmpty) {
      return {
        'name': aspectName,
        'description': '未知相位',
        'keywords': [],
        'nature': '未知',
        'strength': '未知',
      };
    }
    
    return {
      'name': aspect['name'],
      'englishName': aspect['englishName'],
      'symbol': aspect['symbol'],
      'shortZh': aspect['shortZh'],
      'angle': aspect['angle'],
      'orb': aspect['orb'],
      'color': aspect['color'],
      'nature': aspect['nature'],
      'strength': aspect['strength'],
      'keywords': aspect['keywords'],
      'description': aspect['description'],
    };
  }
  
  /// 計算相位強度權重
  static double getStrengthWeight(String strength) {
    switch (strength) {
      case 'very_strong':
        return 1.0;
      case 'strong':
        return 0.8;
      case 'moderate':
        return 0.6;
      case 'weak':
        return 0.4;
      default:
        return 0.2;
    }
  }
  
  /// 獲取相位的影響性質
  static String getAspectNature(String aspectName) {
    final aspect = getAspectDefinition(aspectName);
    return aspect?['nature'] ?? '未知';
  }
  
  /// 檢查相位是否為精確相位（容許度很小）
  static bool isExactAspect(double orb, {double threshold = 1.0}) {
    return orb <= threshold;
  }
  
  /// 獲取相位的建議解釋
  static String getAspectInterpretation(String aspectName, String planet1, String planet2) {
    final aspect = getAspectDefinition(aspectName);
    if (aspect == null) return '未知相位組合';
    
    final nature = aspect['nature'] as String;
    final description = aspect['description'] as String;
    
    switch (nature) {
      case '和諧':
        return '$planet1 與 $planet2 形成 $aspectName，$description，這是一個有利的組合。';
      case '困難':
        return '$planet1 與 $planet2 形成 $aspectName，$description，需要努力整合這些能量。';
      case '中性':
        return '$planet1 與 $planet2 形成 $aspectName，$description，影響相對中性。';
      case '創造':
        return '$planet1 與 $planet2 形成 $aspectName，$description，帶來創造性的能量。';
      case '調整':
        return '$planet1 與 $planet2 形成 $aspectName，$description，需要調整和適應。';
      default:
        return '$planet1 與 $planet2 形成 $aspectName，$description。';
    }
  }
}
