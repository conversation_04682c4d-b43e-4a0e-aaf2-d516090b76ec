import 'package:flutter/material.dart';

/// 宮位定義常數
/// 
/// 包含 12 宮位的詳細定義和屬性
class HouseDefinitions {
  /// 12 宮位定義
  static const List<Map<String, dynamic>> houses = [
    {
      'number': 1,
      'name': '第一宮',
      'englishName': 'First House',
      'traditionalName': '命宮',
      'modernName': '自我宮',
      'naturalSign': '牡羊座',
      'naturalRuler': '火星',
      'element': '火',
      'quality': '基本',
      'polarity': '陽性',
      'color': Color(0xFFFF6B6B),
      'keywords': ['自我', '個性', '外貌', '第一印象', '生命力', '身體'],
      'themes': ['個人身份', '外在形象', '自我表達', '生命活力', '個性特質'],
      'bodyParts': ['頭部', '臉部', '大腦'],
      'lifeAreas': ['個人形象', '自我認知', '生命力', '外貌', '個性展現'],
      'positiveManifestations': ['自信', '活力', '領導力', '獨立', '開創性'],
      'negativeManifestations': ['自私', '衝動', '自我中心', '急躁', '魯莽'],
      'description': '第一宮代表自我、個性和外在形象，是個人身份的展現',
      'detailedDescription': '第一宮是個人星盤的起點，代表自我意識、個性特質和外在形象。這個宮位影響一個人如何展現自己，以及他人對你的第一印象。它也與身體健康、生命力和個人的自然反應模式有關。',
    },
    {
      'number': 2,
      'name': '第二宮',
      'englishName': 'Second House',
      'traditionalName': '財帛宮',
      'modernName': '價值宮',
      'naturalSign': '金牛座',
      'naturalRuler': '金星',
      'element': '土',
      'quality': '固定',
      'polarity': '陰性',
      'color': Color(0xFF4ECDC4),
      'keywords': ['金錢', '物質', '價值觀', '自我價值', '資源', '才能'],
      'themes': ['物質財富', '個人價值', '資源管理', '才能發展', '安全感'],
      'bodyParts': ['頸部', '喉嚨', '甲狀腺'],
      'lifeAreas': ['收入', '財產', '投資', '消費習慣', '物質享受'],
      'positiveManifestations': ['財務穩定', '實用技能', '價值觀清晰', '資源豐富'],
      'negativeManifestations': ['物質主義', '貪婪', '固執', '過度消費'],
      'description': '第二宮代表金錢、物質資源和個人價值觀',
      'detailedDescription': '第二宮關注物質世界和個人資源。它不僅代表金錢和財產，還包括個人的才能、技能和價值觀。這個宮位顯示一個人如何賺錢、花錢，以及對物質安全的需求。它也反映了自我價值感和對美好事物的欣賞能力。',
    },
    {
      'number': 3,
      'name': '第三宮',
      'englishName': 'Third House',
      'traditionalName': '兄弟宮',
      'modernName': '溝通宮',
      'naturalSign': '雙子座',
      'naturalRuler': '水星',
      'element': '風',
      'quality': '變動',
      'polarity': '陽性',
      'color': Color(0xFFFFE66D),
      'keywords': ['溝通', '學習', '兄弟姊妹', '短途旅行', '思維', '資訊'],
      'themes': ['溝通能力', '學習方式', '鄰里關係', '日常交流', '思維模式'],
      'bodyParts': ['手臂', '手部', '肺部', '神經系統'],
      'lifeAreas': ['教育', '寫作', '媒體', '交通', '鄰居關係'],
      'positiveManifestations': ['溝通能力強', '學習快速', '適應力佳', '思維敏捷'],
      'negativeManifestations': ['膚淺', '八卦', '不專注', '言行不一'],
      'description': '第三宮代表溝通、學習和日常交流',
      'detailedDescription': '第三宮掌管溝通、學習和日常生活中的交流。它影響一個人的思維方式、學習能力和表達技巧。這個宮位也與兄弟姊妹、鄰居和短途旅行有關。它顯示了個人如何處理資訊、與他人交流，以及在日常環境中的適應能力。',
    },
    {
      'number': 4,
      'name': '第四宮',
      'englishName': 'Fourth House',
      'traditionalName': '田宅宮',
      'modernName': '家庭宮',
      'naturalSign': '巨蟹座',
      'naturalRuler': '月亮',
      'element': '水',
      'quality': '基本',
      'polarity': '陰性',
      'color': Color(0xFF95E1D3),
      'keywords': ['家庭', '根源', '房地產', '情感基礎', '傳統', '安全'],
      'themes': ['家庭關係', '情感根基', '私人空間', '傳統價值', '內在安全'],
      'bodyParts': ['胸部', '胃部', '子宮'],
      'lifeAreas': ['家庭生活', '房地產', '家族傳統', '情感支持', '私人領域'],
      'positiveManifestations': ['家庭和睦', '情感穩定', '傳統智慧', '安全感'],
      'negativeManifestations': ['過度依賴', '情緒化', '封閉', '懷舊'],
      'description': '第四宮代表家庭、根源和情感基礎',
      'detailedDescription': '第四宮是星盤的底部，代表個人的根基和情感基礎。它與家庭、家族傳統、房地產和私人生活有關。這個宮位顯示了一個人的情感需求、對安全感的渴望，以及與父母（特別是母親）的關係。它也反映了個人的內在世界和最私密的感受。',
    },
    {
      'number': 5,
      'name': '第五宮',
      'englishName': 'Fifth House',
      'traditionalName': '子女宮',
      'modernName': '創造宮',
      'naturalSign': '獅子座',
      'naturalRuler': '太陽',
      'element': '火',
      'quality': '固定',
      'polarity': '陽性',
      'color': Color(0xFFFFB74D),
      'keywords': ['創造', '娛樂', '戀愛', '子女', '投機', '表現'],
      'themes': ['創造力', '自我表達', '娛樂活動', '浪漫愛情', '藝術才能'],
      'bodyParts': ['心臟', '背部', '脊椎'],
      'lifeAreas': ['藝術創作', '娛樂', '戀愛關係', '子女教育', '投機投資'],
      'positiveManifestations': ['創造力豐富', '表現力強', '樂觀開朗', '藝術天賦'],
      'negativeManifestations': ['自戀', '戲劇化', '賭博成癮', '不負責任'],
      'description': '第五宮代表創造力、娛樂和自我表達',
      'detailedDescription': '第五宮是創造力和自我表達的宮位。它與藝術、娛樂、戀愛和子女有關。這個宮位顯示了一個人如何表達創造力、尋求樂趣，以及在愛情中的表現。它也與投機、賭博和所有形式的創造性活動相關，包括生育子女這一終極創造行為。',
    },
    {
      'number': 6,
      'name': '第六宮',
      'englishName': 'Sixth House',
      'traditionalName': '奴僕宮',
      'modernName': '服務宮',
      'naturalSign': '處女座',
      'naturalRuler': '水星',
      'element': '土',
      'quality': '變動',
      'polarity': '陰性',
      'color': Color(0xFF81C784),
      'keywords': ['工作', '健康', '服務', '日常習慣', '完美', '分析'],
      'themes': ['工作環境', '健康管理', '服務精神', '日常例行', '技能提升'],
      'bodyParts': ['腹部', '腸道', '消化系統'],
      'lifeAreas': ['職場', '健康保健', '寵物', '日常工作', '服務他人'],
      'positiveManifestations': ['工作勤奮', '健康意識', '服務精神', '注重細節'],
      'negativeManifestations': ['完美主義', '過度焦慮', '挑剔', '工作狂'],
      'description': '第六宮代表工作、健康和日常服務',
      'detailedDescription': '第六宮關注日常工作、健康和服務。它影響一個人的工作態度、健康習慣和對完美的追求。這個宮位也與寵物、下屬和日常例行公事有關。它顯示了個人如何管理日常生活、維護健康，以及在工作中的表現和服務他人的能力。',
    },
    {
      'number': 7,
      'name': '第七宮',
      'englishName': 'Seventh House',
      'traditionalName': '夫妻宮',
      'modernName': '伴侶宮',
      'naturalSign': '天秤座',
      'naturalRuler': '金星',
      'element': '風',
      'quality': '基本',
      'polarity': '陽性',
      'color': Color(0xFFFFAB91),
      'keywords': ['伴侶', '合作', '公開敵人', '法律', '平衡', '關係'],
      'themes': ['婚姻關係', '商業夥伴', '法律事務', '公眾關係', '合作精神'],
      'bodyParts': ['腰部', '腎臟', '下背部'],
      'lifeAreas': ['婚姻', '商業合作', '法律糾紛', '公眾形象', '一對一關係'],
      'positiveManifestations': ['合作能力強', '外交手腕', '關係和諧', '公正'],
      'negativeManifestations': ['依賴他人', '猶豫不決', '避免衝突', '失去自我'],
      'description': '第七宮代表伴侶關係、合作和法律事務',
      'detailedDescription': '第七宮是關係的宮位，特別是一對一的重要關係。它與婚姻、商業夥伴、公開敵人和法律事務有關。這個宮位顯示了一個人如何與他人建立平等的夥伴關係、處理衝突，以及在公眾場合的表現。它也反映了個人對和諧與公正的需求。',
    },
    {
      'number': 8,
      'name': '第八宮',
      'englishName': 'Eighth House',
      'traditionalName': '疾厄宮',
      'modernName': '轉化宮',
      'naturalSign': '天蠍座',
      'naturalRuler': '冥王星',
      'element': '水',
      'quality': '固定',
      'polarity': '陰性',
      'color': Color(0xFF8E24AA),
      'keywords': ['轉化', '死亡', '他人資源', '神秘學', '重生', '深度'],
      'themes': ['生死議題', '共同財產', '心理轉化', '神秘探索', '深層治療'],
      'bodyParts': ['生殖器官', '排泄系統', '內分泌'],
      'lifeAreas': ['遺產', '保險', '稅務', '心理治療', '神秘學研究'],
      'positiveManifestations': ['洞察力深', '轉化能力', '心理治療', '神秘智慧'],
      'negativeManifestations': ['執著', '報復心', '極端', '控制慾'],
      'description': '第八宮代表轉化、他人資源和深層心理',
      'detailedDescription': '第八宮是轉化和重生的宮位。它與死亡、他人的資源、共同財產和深層心理有關。這個宮位顯示了一個人如何處理生命中的重大轉變、與他人共享資源的能力，以及對神秘和隱藏事物的興趣。它也與心理治療、調查研究和所有形式的深層探索相關。',
    },
    {
      'number': 9,
      'name': '第九宮',
      'englishName': 'Ninth House',
      'traditionalName': '遷移宮',
      'modernName': '哲學宮',
      'naturalSign': '射手座',
      'naturalRuler': '木星',
      'element': '火',
      'quality': '變動',
      'polarity': '陽性',
      'color': Color(0xFF7986CB),
      'keywords': ['哲學', '宗教', '高等教育', '長途旅行', '法律', '出版'],
      'themes': ['人生哲學', '宗教信仰', '高等學府', '國際事務', '精神追求'],
      'bodyParts': ['大腿', '臀部', '肝臟'],
      'lifeAreas': ['大學教育', '宗教活動', '國外旅行', '出版業', '法律職業'],
      'positiveManifestations': ['智慧深邃', '視野開闊', '樂觀進取', '哲學思維'],
      'negativeManifestations': ['教條主義', '盲目樂觀', '不切實際', '傲慢'],
      'description': '第九宮代表哲學、宗教和高等教育',
      'detailedDescription': '第九宮是智慧和精神追求的宮位。它與哲學、宗教、高等教育和長途旅行有關。這個宮位顯示了一個人的世界觀、精神信仰和對真理的追求。它也與法律、出版、教學和所有擴展心智視野的活動相關。第九宮代表了人類對意義和目的的永恆探索。',
    },
    {
      'number': 10,
      'name': '第十宮',
      'englishName': 'Tenth House',
      'traditionalName': '官祿宮',
      'modernName': '事業宮',
      'naturalSign': '摩羯座',
      'naturalRuler': '土星',
      'element': '土',
      'quality': '基本',
      'polarity': '陰性',
      'color': Color(0xFF78909C),
      'keywords': ['事業', '聲譽', '社會地位', '權威', '成就', '責任'],
      'themes': ['職業發展', '公眾形象', '社會責任', '權威地位', '人生目標'],
      'bodyParts': ['膝蓋', '骨骼', '皮膚'],
      'lifeAreas': ['職業生涯', '社會地位', '公眾聲譽', '政府關係', '權威職位'],
      'positiveManifestations': ['事業成功', '領導能力', '社會責任', '權威地位'],
      'negativeManifestations': ['野心過度', '冷酷無情', '權力慾', '工作狂'],
      'description': '第十宮代表事業、聲譽和社會地位',
      'detailedDescription': '第十宮是星盤的頂點，代表事業成就和社會地位。它與職業、聲譽、權威和公眾形象有關。這個宮位顯示了一個人的職業目標、對成功的定義，以及在社會中的地位。它也反映了與父母（特別是父親）的關係，以及個人對責任和權威的態度。',
    },
    {
      'number': 11,
      'name': '第十一宮',
      'englishName': 'Eleventh House',
      'traditionalName': '福德宮',
      'modernName': '友誼宮',
      'naturalSign': '水瓶座',
      'naturalRuler': '天王星',
      'element': '風',
      'quality': '固定',
      'polarity': '陽性',
      'color': Color(0xFF4DD0E1),
      'keywords': ['朋友', '團體', '希望', '社會理想', '人道主義', '創新'],
      'themes': ['友誼關係', '團體活動', '社會理想', '未來願景', '集體意識'],
      'bodyParts': ['小腿', '腳踝', '循環系統'],
      'lifeAreas': ['社交圈', '社團組織', '慈善活動', '科技創新', '社會改革'],
      'positiveManifestations': ['友誼深厚', '團隊合作', '人道主義', '創新思維'],
      'negativeManifestations': ['疏離感', '反叛', '不切實際', '固執己見'],
      'description': '第十一宮代表朋友、團體和社會理想',
      'detailedDescription': '第十一宮是友誼和集體意識的宮位。它與朋友、團體、社會理想和未來希望有關。這個宮位顯示了一個人如何與朋友相處、參與團體活動，以及對社會改革的態度。它也反映了個人的人道主義精神和對未來的願景。第十一宮代表了人類的集體智慧和社會進步。',
    },
    {
      'number': 12,
      'name': '第十二宮',
      'englishName': 'Twelfth House',
      'traditionalName': '玄秘宮',
      'modernName': '靈性宮',
      'naturalSign': '雙魚座',
      'naturalRuler': '海王星',
      'element': '水',
      'quality': '變動',
      'polarity': '陰性',
      'color': Color(0xFFAB47BC),
      'keywords': ['潛意識', '犧牲', '隱藏', '精神修行', '業力', '同情'],
      'themes': ['靈性修行', '潛意識探索', '犧牲奉獻', '隱藏敵人', '業力清理'],
      'bodyParts': ['腳部', '淋巴系統', '松果體'],
      'lifeAreas': ['冥想修行', '慈善工作', '醫院監獄', '隱居生活', '心理治療'],
      'positiveManifestations': ['靈性智慧', '同情心', '直覺敏銳', '犧牲精神'],
      'negativeManifestations': ['逃避現實', '自我欺騙', '受害者心態', '混亂'],
      'description': '第十二宮代表潛意識、犧牲和精神修行',
      'detailedDescription': '第十二宮是靈性和潛意識的宮位。它與隱藏的事物、犧牲、精神修行和業力有關。這個宮位顯示了一個人的靈性傾向、潛意識模式和對超越物質世界的渴望。它也與慈善、服務和所有形式的犧牲奉獻相關。第十二宮代表了人類靈魂的最深層面和與宇宙的連接。',
    },
  ];
  
  /// 根據宮位號碼獲取宮位定義
  static Map<String, dynamic>? getHouseByNumber(int number) {
    try {
      return houses.firstWhere((house) => house['number'] == number);
    } catch (e) {
      return null;
    }
  }
  
  /// 根據宮位名稱獲取宮位定義
  static Map<String, dynamic>? getHouseByName(String name) {
    try {
      return houses.firstWhere((house) => 
          house['name'] == name || 
          house['traditionalName'] == name || 
          house['modernName'] == name);
    } catch (e) {
      return null;
    }
  }
  
  /// 根據元素獲取宮位
  static List<Map<String, dynamic>> getHousesByElement(String element) {
    return houses.where((house) => house['element'] == element).toList();
  }
  
  /// 根據性質獲取宮位
  static List<Map<String, dynamic>> getHousesByQuality(String quality) {
    return houses.where((house) => house['quality'] == quality).toList();
  }
  
  /// 根據極性獲取宮位
  static List<Map<String, dynamic>> getHousesByPolarity(String polarity) {
    return houses.where((house) => house['polarity'] == polarity).toList();
  }
  
  /// 獲取對宮
  static Map<String, dynamic> getOppositeHouse(int houseNumber) {
    int oppositeNumber = houseNumber + 6;
    if (oppositeNumber > 12) {
      oppositeNumber -= 12;
    }
    return houses[oppositeNumber - 1];
  }
  
  /// 獲取三分相宮位
  static List<Map<String, dynamic>> getTrineHouses(int houseNumber) {
    final trineNumbers = [
      (houseNumber + 4 - 1) % 12 + 1,
      (houseNumber + 8 - 1) % 12 + 1,
    ];
    return trineNumbers.map((number) => houses[number - 1]).toList();
  }
  
  /// 獲取四分相宮位
  static List<Map<String, dynamic>> getSquareHouses(int houseNumber) {
    final squareNumbers = [
      (houseNumber + 3 - 1) % 12 + 1,
      (houseNumber + 9 - 1) % 12 + 1,
    ];
    return squareNumbers.map((number) => houses[number - 1]).toList();
  }
  
  /// 獲取六分相宮位
  static List<Map<String, dynamic>> getSextileHouses(int houseNumber) {
    final sextileNumbers = [
      (houseNumber + 2 - 1) % 12 + 1,
      (houseNumber + 10 - 1) % 12 + 1,
    ];
    return sextileNumbers.map((number) => houses[number - 1]).toList();
  }
  
  /// 獲取角宮（基本宮位）
  static List<Map<String, dynamic>> getAngularHouses() {
    return [houses[0], houses[3], houses[6], houses[9]]; // 1, 4, 7, 10
  }
  
  /// 獲取續宮（固定宮位）
  static List<Map<String, dynamic>> getSuccedentHouses() {
    return [houses[1], houses[4], houses[7], houses[10]]; // 2, 5, 8, 11
  }
  
  /// 獲取果宮（變動宮位）
  static List<Map<String, dynamic>> getCadentHouses() {
    return [houses[2], houses[5], houses[8], houses[11]]; // 3, 6, 9, 12
  }
}
