import 'package:flutter/material.dart';

import '../../../data/models/astrology/zodiac_sign.dart';

/// 星座定義常數
/// 
/// 包含 12 星座的詳細定義和屬性
class ZodiacDefinitions {
  /// 12 星座定義
  static const List<Map<String, dynamic>> zodiacSigns = [
    {
      'id': 1,
      'name': '牡羊座',
      'englishName': 'Aries',
      'symbol': '♈',
      'unicode': '\u2648',
      'element': '火',
      'quality': '基本',
      'polarity': '陽性',
      'ruler': '火星',
      'exaltationRuler': '太陽',
      'detrimentRuler': '金星',
      'fallRuler': '土星',
      'startDegree': 0.0,
      'endDegree': 30.0,
      'color': Colors.red,
      'season': '春分',
      'bodyPart': ['頭部', '臉部'],
      'keywords': ['主動', '衝動', '領導', '開創', '勇敢', '直接'],
      'positiveTraits': ['勇敢', '熱情', '獨立', '自信', '開拓精神'],
      'negativeTraits': ['衝動', '自私', '急躁', '好鬥', '缺乏耐心'],
      'description': '牡羊座是黃道第一宮，象徵新的開始和原始的生命力。白羊座的人通常充滿活力、勇敢直接，喜歡挑戰和冒險。',
      'mythology': '代表金羊毛的公羊，象徵勇氣和犧牲精神',
    },
    {
      'id': 2,
      'name': '金牛座',
      'englishName': 'Taurus',
      'symbol': '♉',
      'unicode': '\u2649',
      'element': '土',
      'quality': '固定',
      'polarity': '陰性',
      'ruler': '金星',
      'exaltationRuler': '月亮',
      'detrimentRuler': '火星',
      'fallRuler': '天王星',
      'startDegree': 30.0,
      'endDegree': 60.0,
      'color': Color(0xFFCA9833),
      'season': '晚春',
      'bodyPart': ['頸部', '喉嚨'],
      'keywords': ['穩定', '實際', '感官', '持久', '固執', '美感'],
      'positiveTraits': ['穩定', '可靠', '實際', '有耐心', '藝術天賦'],
      'negativeTraits': ['固執', '物質主義', '懶惰', '佔有慾', '抗拒改變'],
      'description': '金牛座重視穩定和安全感，喜歡美好的事物和感官享受。他們通常很實際，有耐心，但有時也會顯得固執。',
      'mythology': '代表宙斯化身的公牛，象徵力量和豐饒',
    },
    {
      'id': 3,
      'name': '雙子座',
      'englishName': 'Gemini',
      'symbol': '♊',
      'unicode': '\u264A',
      'element': '風',
      'quality': '變動',
      'polarity': '陽性',
      'ruler': '水星',
      'exaltationRuler': '北交點',
      'detrimentRuler': '木星',
      'fallRuler': '南交點',
      'startDegree': 60.0,
      'endDegree': 90.0,
      'color': Color(0xFF00673F),
      'season': '初夏',
      'bodyPart': ['手臂', '肺部'],
      'keywords': ['溝通', '多變', '好奇', '靈活', '雙重性', '學習'],
      'positiveTraits': ['聰明', '適應力強', '溝通能力佳', '好奇心', '機智'],
      'negativeTraits': ['善變', '膚淺', '不專注', '緊張', '言行不一'],
      'description': '雙子座好奇心強，善於溝通和學習。他們思維敏捷，適應力強，但有時會顯得不夠專注或深入。',
      'mythology': '代表雙胞胎兄弟卡斯托和波呂克斯，象徵雙重性格',
    },
    {
      'id': 4,
      'name': '巨蟹座',
      'englishName': 'Cancer',
      'symbol': '♋',
      'unicode': '\u264B',
      'element': '水',
      'quality': '基本',
      'polarity': '陰性',
      'ruler': '月亮',
      'exaltationRuler': '木星',
      'detrimentRuler': '土星',
      'fallRuler': '火星',
      'startDegree': 90.0,
      'endDegree': 120.0,
      'color': Color(0xFF0A0AFD),
      'season': '夏至',
      'bodyPart': ['胸部', '胃部'],
      'keywords': ['情感', '保護', '家庭', '直覺', '敏感', '母性'],
      'positiveTraits': ['同情心', '直覺敏銳', '忠誠', '保護性', '家庭觀念'],
      'negativeTraits': ['情緒化', '過度敏感', '防禦性', '懷舊', '依賴'],
      'description': '巨蟹座情感豐富，重視家庭和安全感。他們直覺敏銳，善於照顧他人，但有時會過於敏感或情緒化。',
      'mythology': '代表被赫拉派去攻擊海克力斯的巨蟹，象徵保護和犧牲',
    },
    {
      'id': 5,
      'name': '獅子座',
      'englishName': 'Leo',
      'symbol': '♌',
      'unicode': '\u264C',
      'element': '火',
      'quality': '固定',
      'polarity': '陽性',
      'ruler': '太陽',
      'exaltationRuler': '海王星',
      'detrimentRuler': '天王星',
      'fallRuler': '水星',
      'startDegree': 120.0,
      'endDegree': 150.0,
      'color': Colors.red,
      'season': '盛夏',
      'bodyPart': ['心臟', '背部'],
      'keywords': ['創造', '表現', '自信', '慷慨', '戲劇性', '領導'],
      'positiveTraits': ['自信', '慷慨', '創造力', '領導能力', '忠誠'],
      'negativeTraits': ['自負', '戲劇化', '固執', '愛面子', '專制'],
      'description': '獅子座自信大方，喜歡成為注意的焦點。他們有創造力和領導能力，慷慨大方，但有時會顯得自負。',
      'mythology': '代表尼米亞獅子，象徵勇氣和王者風範',
    },
    {
      'id': 6,
      'name': '處女座',
      'englishName': 'Virgo',
      'symbol': '♍',
      'unicode': '\u264D',
      'element': '土',
      'quality': '變動',
      'polarity': '陰性',
      'ruler': '水星',
      'exaltationRuler': '水星',
      'detrimentRuler': '木星',
      'fallRuler': '金星',
      'startDegree': 150.0,
      'endDegree': 180.0,
      'color': Color(0xFFCA9833),
      'season': '晚夏',
      'bodyPart': ['腹部', '腸道'],
      'keywords': ['分析', '完美', '服務', '實用', '細節', '健康'],
      'positiveTraits': ['細心', '實用', '可靠', '分析能力', '服務精神'],
      'negativeTraits': ['挑剔', '完美主義', '焦慮', '保守', '過度分析'],
      'description': '處女座注重細節和完美，善於分析和組織。他們實用可靠，樂於服務他人，但有時會過於挑剔。',
      'mythology': '代表正義女神阿斯特拉亞，象徵純潔和完美',
    },
    {
      'id': 7,
      'name': '天秤座',
      'englishName': 'Libra',
      'symbol': '♎',
      'unicode': '\u264E',
      'element': '風',
      'quality': '基本',
      'polarity': '陽性',
      'ruler': '金星',
      'exaltationRuler': '土星',
      'detrimentRuler': '火星',
      'fallRuler': '太陽',
      'startDegree': 180.0,
      'endDegree': 210.0,
      'color': Color(0xFF00673F),
      'season': '秋分',
      'bodyPart': ['腰部', '腎臟'],
      'keywords': ['平衡', '和諧', '美感', '合作', '外交', '關係'],
      'positiveTraits': ['公正', '外交手腕', '合作精神', '美感', '和諧'],
      'negativeTraits': ['猶豫不決', '依賴他人', '膚淺', '避免衝突', '懶惰'],
      'description': '天秤座追求和諧與平衡，重視美感和公正。他們善於合作和外交，但有時會猶豫不決。',
      'mythology': '代表正義女神的天秤，象徵公正和平衡',
    },
    {
      'id': 8,
      'name': '天蠍座',
      'englishName': 'Scorpio',
      'symbol': '♏',
      'unicode': '\u264F',
      'element': '水',
      'quality': '固定',
      'polarity': '陰性',
      'ruler': '冥王星',
      'exaltationRuler': '天王星',
      'detrimentRuler': '金星',
      'fallRuler': '月亮',
      'startDegree': 210.0,
      'endDegree': 240.0,
      'color': Color(0xFF0A0AFD),
      'season': '深秋',
      'bodyPart': ['生殖器', '排泄器官'],
      'keywords': ['轉化', '深度', '神秘', '強烈', '重生', '權力'],
      'positiveTraits': ['深刻', '忠誠', '勇敢', '直覺', '變革能力'],
      'negativeTraits': ['嫉妒', '報復心', '極端', '控制慾', '神秘'],
      'description': '天蠍座深沉神秘，情感強烈。他們有洞察力和轉化能力，忠誠專一，但有時會顯得佔有慾強。',
      'mythology': '代表殺死獵戶座的天蠍，象徵死亡和重生',
    },
    {
      'id': 9,
      'name': '射手座',
      'englishName': 'Sagittarius',
      'symbol': '♐',
      'unicode': '\u2650',
      'element': '火',
      'quality': '變動',
      'polarity': '陽性',
      'ruler': '木星',
      'exaltationRuler': '南交點',
      'detrimentRuler': '水星',
      'fallRuler': '北交點',
      'startDegree': 240.0,
      'endDegree': 270.0,
      'color': Colors.red,
      'season': '初冬',
      'bodyPart': ['大腿', '肝臟'],
      'keywords': ['探索', '哲學', '自由', '樂觀', '冒險', '真理'],
      'positiveTraits': ['樂觀', '哲學思維', '冒險精神', '誠實', '自由'],
      'negativeTraits': ['魯莽', '不負責任', '誇大', '不耐煩', '盲目樂觀'],
      'description': '射手座熱愛自由和探索，樂觀開朗。他們有哲學思維和冒險精神，但有時會缺乏耐心。',
      'mythology': '代表半人馬射手，象徵智慧和野性的結合',
    },
    {
      'id': 10,
      'name': '摩羯座',
      'englishName': 'Capricorn',
      'symbol': '♑',
      'unicode': '\u2651',
      'element': '土',
      'quality': '基本',
      'polarity': '陰性',
      'ruler': '土星',
      'exaltationRuler': '火星',
      'detrimentRuler': '月亮',
      'fallRuler': '木星',
      'startDegree': 270.0,
      'endDegree': 300.0,
      'color': Color(0xFFCA9833),
      'season': '冬至',
      'bodyPart': ['膝蓋', '骨骼'],
      'keywords': ['責任', '成就', '結構', '耐心', '野心', '傳統'],
      'positiveTraits': ['負責任', '有野心', '實際', '有耐力', '可靠'],
      'negativeTraits': ['悲觀', '固執', '冷漠', '過度嚴肅', '物質主義'],
      'description': '摩羯座有責任感和野心，重視成就和地位。他們實際可靠，有耐力，但有時會顯得過於嚴肅。',
      'mythology': '代表山羊魚，象徵攀登高峰的決心',
    },
    {
      'id': 11,
      'name': '水瓶座',
      'englishName': 'Aquarius',
      'symbol': '♒',
      'unicode': '\u2652',
      'element': '風',
      'quality': '固定',
      'polarity': '陽性',
      'ruler': '天王星',
      'exaltationRuler': '冥王星',
      'detrimentRuler': '太陽',
      'fallRuler': '海王星',
      'startDegree': 300.0,
      'endDegree': 330.0,
      'color': Color(0xFF00673F),
      'season': '深冬',
      'bodyPart': ['小腿', '循環系統'],
      'keywords': ['創新', '獨立', '人道', '未來', '友誼', '革命'],
      'positiveTraits': ['獨立', '創新', '人道主義', '友善', '前瞻性'],
      'negativeTraits': ['疏離', '固執', '反叛', '不切實際', '冷漠'],
      'description': '水瓶座獨立創新，重視友誼和人道主義。他們思想前衛，但有時會顯得疏離或固執己見。',
      'mythology': '代表為人類帶來水的水瓶手，象徵知識和啟蒙',
    },
    {
      'id': 12,
      'name': '雙魚座',
      'englishName': 'Pisces',
      'symbol': '♓',
      'unicode': '\u2653',
      'element': '水',
      'quality': '變動',
      'polarity': '陰性',
      'ruler': '海王星',
      'exaltationRuler': '金星',
      'detrimentRuler': '水星',
      'fallRuler': '水星',
      'startDegree': 330.0,
      'endDegree': 360.0,
      'color': Color(0xFF0A0AFD),
      'season': '晚冬',
      'bodyPart': ['腳部', '淋巴系統'],
      'keywords': ['直覺', '同情', '想像', '靈性', '犧牲', '藝術'],
      'positiveTraits': ['同情心', '直覺', '想像力', '藝術天賦', '靈性'],
      'negativeTraits': ['逃避現實', '過度敏感', '優柔寡斷', '自憐', '混亂'],
      'description': '雙魚座富有想像力和同情心，直覺敏銳。他們善解人意，有藝術天賦，但有時會逃避現實。',
      'mythology': '代表阿芙羅狄蒂和厄洛斯化身的雙魚，象徵愛與美',
    },
  ];

  // ========== 新的類型安全方法 ==========

  /// 根據名稱獲取星座定義（類型安全）
  static ZodiacSign? getSignByNameTyped(String name) {
    try {
      final signData = zodiacSigns.firstWhere((sign) => sign['name'] == name);
      return ZodiacSign.fromMap(signData);
    } catch (e) {
      return null;
    }
  }

  /// 根據 ID 獲取星座定義（類型安全）
  static ZodiacSign? getSignByIdTyped(int id) {
    try {
      final signData = zodiacSigns.firstWhere((sign) => sign['id'] == id);
      return ZodiacSign.fromMap(signData);
    } catch (e) {
      return null;
    }
  }

  /// 根據度數獲取星座（類型安全）
  static ZodiacSign getSignByDegreeTyped(double degree) {
    final normalizedDegree = degree % 360;

    for (final sign in zodiacSigns) {
      final startDegree = sign['startDegree'] as double;
      final endDegree = sign['endDegree'] as double;

      if (endDegree == 360.0) {
        // 雙魚座的特殊情況
        if (normalizedDegree >= startDegree || normalizedDegree < 0.0001) {
          return ZodiacSign.fromMap(sign);
        }
      } else {
        if (normalizedDegree >= startDegree && normalizedDegree < endDegree) {
          return ZodiacSign.fromMap(sign);
        }
      }
    }

    // 預設返回白羊座
    return ZodiacSign.fromMap(zodiacSigns[0]);
  }

  /// 根據元素獲取星座（類型安全）
  static List<ZodiacSign> getSignsByElementTyped(String element) {
    return zodiacSigns
        .where((sign) => sign['element'] == element)
        .map((sign) => ZodiacSign.fromMap(sign))
        .toList();
  }

  /// 根據性質獲取星座（類型安全）
  static List<ZodiacSign> getSignsByQualityTyped(String quality) {
    return zodiacSigns
        .where((sign) => sign['quality'] == quality)
        .map((sign) => ZodiacSign.fromMap(sign))
        .toList();
  }

  /// 根據極性獲取星座（類型安全）
  static List<ZodiacSign> getSignsByPolarityTyped(String polarity) {
    return zodiacSigns
        .where((sign) => sign['polarity'] == polarity)
        .map((sign) => ZodiacSign.fromMap(sign))
        .toList();
  }

  /// 獲取對宮星座（類型安全）
  static ZodiacSign getOppositeSignTyped(int signId) {
    int oppositeId = signId + 6;
    if (oppositeId > 12) {
      oppositeId -= 12;
    }
    return ZodiacSign.fromMap(zodiacSigns[oppositeId - 1]);
  }

  /// 獲取三分相星座（類型安全）
  static List<ZodiacSign> getTrineSignsTyped(int signId) {
    final trineIds = [
      (signId + 4 - 1) % 12 + 1,
      (signId + 8 - 1) % 12 + 1,
    ];
    return trineIds
        .map((id) => ZodiacSign.fromMap(zodiacSigns[id - 1]))
        .toList();
  }

  /// 獲取四分相星座（類型安全）
  static List<ZodiacSign> getSquareSignsTyped(int signId) {
    final squareIds = [
      (signId + 3 - 1) % 12 + 1,
      (signId + 9 - 1) % 12 + 1,
    ];
    return squareIds
        .map((id) => ZodiacSign.fromMap(zodiacSigns[id - 1]))
        .toList();
  }

  /// 獲取六分相星座（類型安全）
  static List<ZodiacSign> getSextileSignsTyped(int signId) {
    final sextileIds = [
      (signId + 2 - 1) % 12 + 1,
      (signId + 10 - 1) % 12 + 1,
    ];
    return sextileIds
        .map((id) => ZodiacSign.fromMap(zodiacSigns[id - 1]))
        .toList();
  }

  /// 獲取所有星座（類型安全）
  static List<ZodiacSign> getAllSignsTyped() {
    return zodiacSigns.map((sign) => ZodiacSign.fromMap(sign)).toList();
  }

  // ========== 向後兼容的 Map 方法 ==========

  /// 根據名稱獲取星座定義
  static Map<String, dynamic>? getSignByName(String name) {
    try {
      return zodiacSigns.firstWhere((sign) => sign['name'] == name);
    } catch (e) {
      return null;
    }
  }
  
  /// 根據 ID 獲取星座定義
  static Map<String, dynamic>? getSignById(int id) {
    try {
      return zodiacSigns.firstWhere((sign) => sign['id'] == id);
    } catch (e) {
      return null;
    }
  }
  
  /// 根據度數獲取星座
  static Map<String, dynamic> getSignByDegree(double degree) {
    final normalizedDegree = degree % 360;
    
    for (final sign in zodiacSigns) {
      final startDegree = sign['startDegree'] as double;
      final endDegree = sign['endDegree'] as double;
      
      if (endDegree == 360.0) {
        // 雙魚座的特殊情況
        if (normalizedDegree >= startDegree || normalizedDegree < 0.0001) {
          return sign;
        }
      } else {
        if (normalizedDegree >= startDegree && normalizedDegree < endDegree) {
          return sign;
        }
      }
    }
    
    return zodiacSigns[0]; // 預設返回白羊座
  }
  
  /// 根據元素獲取星座
  static List<Map<String, dynamic>> getSignsByElement(String element) {
    return zodiacSigns.where((sign) => sign['element'] == element).toList();
  }
  
  /// 根據性質獲取星座
  static List<Map<String, dynamic>> getSignsByQuality(String quality) {
    return zodiacSigns.where((sign) => sign['quality'] == quality).toList();
  }
  
  /// 根據極性獲取星座
  static List<Map<String, dynamic>> getSignsByPolarity(String polarity) {
    return zodiacSigns.where((sign) => sign['polarity'] == polarity).toList();
  }
  
  /// 獲取對宮星座
  static Map<String, dynamic> getOppositeSign(int signId) {
    int oppositeId = signId + 6;
    if (oppositeId > 12) {
      oppositeId -= 12;
    }
    return zodiacSigns[oppositeId - 1];
  }
  
  /// 獲取三分相星座
  static List<Map<String, dynamic>> getTrineSigns(int signId) {
    final trineIds = [
      (signId + 4 - 1) % 12 + 1,
      (signId + 8 - 1) % 12 + 1,
    ];
    return trineIds.map((id) => zodiacSigns[id - 1]).toList();
  }
  
  /// 獲取四分相星座
  static List<Map<String, dynamic>> getSquareSigns(int signId) {
    final squareIds = [
      (signId + 3 - 1) % 12 + 1,
      (signId + 9 - 1) % 12 + 1,
    ];
    return squareIds.map((id) => zodiacSigns[id - 1]).toList();
  }
  
  /// 獲取六分相星座
  static List<Map<String, dynamic>> getSextileSigns(int signId) {
    final sextileIds = [
      (signId + 2 - 1) % 12 + 1,
      (signId + 10 - 1) % 12 + 1,
    ];
    return sextileIds.map((id) => zodiacSigns[id - 1]).toList();
  }
}
