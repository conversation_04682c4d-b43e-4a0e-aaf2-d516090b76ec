# 占星計算核心功能模組

本模組包含 Astreal 應用程式的所有占星計算核心功能，按功能分類組織。

## 📁 資料夾結構

### 🧮 `/calculations` - 核心計算引擎
包含所有基礎占星計算功能：
- **行星位置計算**：計算各行星在黃道上的位置
- **宮位計算**：計算 12 宮位的界線和位置
- **相位計算**：計算行星間的角度關係
- **阿拉伯點計算**：計算各種阿拉伯點位置
- **推運計算**：各種推運技法的計算

### 🎯 `/ephemeris` - 星曆表功能
包含星曆表相關的計算和數據處理：
- **Swiss Ephemeris 整合**：與 Swiss Ephemeris 引擎的介面
- **儒略日轉換**：日期時間與儒略日的轉換
- **時區處理**：時區計算和轉換
- **天文計算**：基礎天文計算功能

### 📊 `/analysis` - 分析和解釋
包含占星分析和解釋相關功能：
- **尊貴力量分析**：行星尊貴力量的計算
- **互容接納分析**：行星間互容接納關係
- **古典占星分析**：古典占星技法
- **現代占星分析**：現代占星技法

### 🎨 `/rendering` - 星盤繪製
包含星盤視覺化和繪製功能：
- **星盤繪製引擎**：基礎繪製功能
- **主題和樣式**：不同的星盤主題
- **圖表元素**：行星、星座、宮位等元素的繪製
- **自定義繪製**：可自定義的繪製選項

### 🔧 `/utilities` - 工具函數
包含占星計算相關的工具函數：
- **角度計算**：角度相關的數學計算
- **星座工具**：星座相關的工具函數
- **數據轉換**：各種數據格式轉換
- **驗證工具**：數據驗證和檢查

### 📋 `/constants` - 常數定義
包含占星計算所需的常數和配置：
- **行星定義**：行星的基本資訊和屬性
- **星座定義**：12 星座的基本資訊
- **相位定義**：各種相位的角度和容許度
- **宮位系統**：不同宮位系統的定義

## 🚀 使用方式

### 基本星盤計算
```dart
import 'package:astreal/astrology/astrology.dart';

// 創建占星服務實例
final astrologyService = AstrologyService();

// 計算本命盤
final chartData = await astrologyService.calculateChartData(
  ChartData(
    chartType: ChartType.natal,
    primaryPerson: birthData,
  ),
);
```

### 行星位置計算
```dart
// 計算特定時間的行星位置
final planets = await astrologyService.calculatePlanetPositions(
  DateTime.now(),
  latitude: 25.0330,
  longitude: 121.5654,
);
```

### 相位分析
```dart
// 計算行星間的相位
final aspects = astrologyService.calculateAspects(planets);
```

## 🔗 依賴關係

- **Swiss Ephemeris**：核心天文計算引擎
- **Timezone**：時區處理
- **Flutter**：UI 框架和基礎功能

## 📝 開發指南

### 添加新的計算功能
1. 在對應的 `/calculations` 子資料夾中創建新文件
2. 實作計算邏輯
3. 在 `AstrologyService` 中添加對應方法
4. 添加單元測試

### 添加新的分析功能
1. 在 `/analysis` 資料夾中創建分析類別
2. 實作分析邏輯
3. 整合到主要服務中
4. 添加文檔和測試

### 自定義星盤繪製
1. 在 `/rendering` 資料夾中創建新的繪製器
2. 繼承基礎繪製類別
3. 實作自定義繪製邏輯
4. 註冊到繪製系統中

## 🧪 測試

每個模組都應該包含對應的單元測試：
```
test/
├── astrology/
│   ├── calculations/
│   ├── ephemeris/
│   ├── analysis/
│   └── rendering/
```

## 📚 文檔

詳細的 API 文檔請參考各個模組的 README 文件。
