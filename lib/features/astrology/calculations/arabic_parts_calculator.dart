import '../../../data/models/astrology/planet_position.dart';
import '../utilities/angle_calculator.dart';
import '../utilities/zodiac_calculator.dart';

/// 阿拉伯點計算器
/// 
/// 計算各種阿拉伯點（Arabic Parts），包括：
/// - 幸運點（Part of Fortune）
/// - 精神點（Part of Spirit）
/// - 愛情點（Part of Love）
/// - 其他傳統阿拉伯點
class ArabicPartsCalculator {
  /// 計算所有阿拉伯點
  /// 
  /// [planets] 行星位置列表
  /// [ascendant] 上升點度數
  /// [isDaytime] 是否為白天出生
  /// 
  /// 返回阿拉伯點位置列表
  static List<PlanetPosition> calculateAllArabicParts(
    List<PlanetPosition> planets,
    double ascendant,
    bool isDaytime,
  ) {
    final arabicParts = <PlanetPosition>[];
    
    // 獲取主要行星位置
    final sun = _findPlanet(planets, '太陽');
    final moon = _findPlanet(planets, '月亮');
    final mercury = _findPlanet(planets, '水星');
    final venus = _findPlanet(planets, '金星');
    final mars = _findPlanet(planets, '火星');
    final jupiter = _findPlanet(planets, '木星');
    final saturn = _findPlanet(planets, '土星');
    
    if (sun != null && moon != null) {
      // 幸運點 (Part of Fortune)
      final fortunePart = calculatePartOfFortune(
        ascendant,
        sun.longitude,
        moon.longitude,
        isDaytime,
      );
      arabicParts.add(fortunePart);
      
      // 精神點 (Part of Spirit)
      final spiritPart = calculatePartOfSpirit(
        ascendant,
        sun.longitude,
        moon.longitude,
        isDaytime,
      );
      arabicParts.add(spiritPart);
    }
    
    if (sun != null && venus != null) {
      // 愛情點 (Part of Love)
      final lovePart = calculatePartOfLove(
        ascendant,
        sun.longitude,
        venus.longitude,
      );
      arabicParts.add(lovePart);
    }
    
    if (sun != null && mercury != null) {
      // 商業點 (Part of Commerce)
      final commercePart = calculatePartOfCommerce(
        ascendant,
        sun.longitude,
        mercury.longitude,
      );
      arabicParts.add(commercePart);
    }
    
    if (sun != null && mars != null) {
      // 勇氣點 (Part of Courage)
      final couragePart = calculatePartOfCourage(
        ascendant,
        sun.longitude,
        mars.longitude,
        isDaytime,
      );
      arabicParts.add(couragePart);
    }
    
    if (sun != null && jupiter != null) {
      // 信仰點 (Part of Faith)
      final faithPart = calculatePartOfFaith(
        ascendant,
        sun.longitude,
        jupiter.longitude,
      );
      arabicParts.add(faithPart);
    }
    
    if (sun != null && saturn != null) {
      // 災難點 (Part of Catastrophe)
      final catastrophePart = calculatePartOfCatastrophe(
        ascendant,
        sun.longitude,
        saturn.longitude,
      );
      arabicParts.add(catastrophePart);
    }
    
    return arabicParts;
  }
  
  /// 計算幸運點 (Part of Fortune)
  /// 
  /// 公式：
  /// - 白天：ASC + Moon - Sun
  /// - 夜晚：ASC + Sun - Moon
  static PlanetPosition calculatePartOfFortune(
    double ascendant,
    double sunLongitude,
    double moonLongitude,
    bool isDaytime,
  ) {
    double longitude;
    
    if (isDaytime) {
      // 白天公式：ASC + Moon - Sun
      longitude = AngleCalculator.normalizeAngle(
        ascendant + moonLongitude - sunLongitude,
      );
    } else {
      // 夜晚公式：ASC + Sun - Moon
      longitude = AngleCalculator.normalizeAngle(
        ascendant + sunLongitude - moonLongitude,
      );
    }
    
    final signInfo = ZodiacCalculator.getZodiacSign(longitude);
    
    return PlanetPosition(
      id: 50,
      name: '幸運點',
      symbol: '⊕',
      longitude: longitude,
      latitude: 0.0,
      distance: 0.0,
      longitudeSpeed: 0.0,
      latitudeSpeed: 0.0,
      distanceSpeed: 0.0,
      sign: signInfo['name'] as String,
      house: 0, // 需要後續計算
    );
  }
  
  /// 計算精神點 (Part of Spirit)
  /// 
  /// 公式：
  /// - 白天：ASC + Sun - Moon
  /// - 夜晚：ASC + Moon - Sun
  static PlanetPosition calculatePartOfSpirit(
    double ascendant,
    double sunLongitude,
    double moonLongitude,
    bool isDaytime,
  ) {
    double longitude;
    
    if (isDaytime) {
      // 白天公式：ASC + Sun - Moon
      longitude = AngleCalculator.normalizeAngle(
        ascendant + sunLongitude - moonLongitude,
      );
    } else {
      // 夜晚公式：ASC + Moon - Sun
      longitude = AngleCalculator.normalizeAngle(
        ascendant + moonLongitude - sunLongitude,
      );
    }
    
    final signInfo = ZodiacCalculator.getZodiacSign(longitude);
    
    return PlanetPosition(
      id: 51,
      name: '精神點',
      symbol: '⊗',
      longitude: longitude,
      latitude: 0.0,
      distance: 0.0,
      longitudeSpeed: 0.0,
      latitudeSpeed: 0.0,
      distanceSpeed: 0.0,
      sign: signInfo['name'] as String,
      house: 0,
    );
  }
  
  /// 計算愛情點 (Part of Love)
  /// 
  /// 公式：ASC + Venus - Sun
  static PlanetPosition calculatePartOfLove(
    double ascendant,
    double sunLongitude,
    double venusLongitude,
  ) {
    final longitude = AngleCalculator.normalizeAngle(
      ascendant + venusLongitude - sunLongitude,
    );
    
    final signInfo = ZodiacCalculator.getZodiacSign(longitude);
    
    return PlanetPosition(
      id: 52,
      name: '愛情點',
      symbol: '♡',
      longitude: longitude,
      latitude: 0.0,
      distance: 0.0,
      longitudeSpeed: 0.0,
      latitudeSpeed: 0.0,
      distanceSpeed: 0.0,
      sign: signInfo['name'] as String,
      house: 0,
    );
  }
  
  /// 計算商業點 (Part of Commerce)
  /// 
  /// 公式：ASC + Mercury - Sun
  static PlanetPosition calculatePartOfCommerce(
    double ascendant,
    double sunLongitude,
    double mercuryLongitude,
  ) {
    final longitude = AngleCalculator.normalizeAngle(
      ascendant + mercuryLongitude - sunLongitude,
    );
    
    final signInfo = ZodiacCalculator.getZodiacSign(longitude);
    
    return PlanetPosition(
      id: 53,
      name: '商業點',
      symbol: '☿',
      longitude: longitude,
      latitude: 0.0,
      distance: 0.0,
      longitudeSpeed: 0.0,
      latitudeSpeed: 0.0,
      distanceSpeed: 0.0,
      sign: signInfo['name'] as String,
      house: 0,
    );
  }
  
  /// 計算勇氣點 (Part of Courage)
  /// 
  /// 公式：
  /// - 白天：ASC + Mars - Sun
  /// - 夜晚：ASC + Sun - Mars
  static PlanetPosition calculatePartOfCourage(
    double ascendant,
    double sunLongitude,
    double marsLongitude,
    bool isDaytime,
  ) {
    double longitude;
    
    if (isDaytime) {
      longitude = AngleCalculator.normalizeAngle(
        ascendant + marsLongitude - sunLongitude,
      );
    } else {
      longitude = AngleCalculator.normalizeAngle(
        ascendant + sunLongitude - marsLongitude,
      );
    }
    
    final signInfo = ZodiacCalculator.getZodiacSign(longitude);
    
    return PlanetPosition(
      id: 54,
      name: '勇氣點',
      symbol: '♂',
      longitude: longitude,
      latitude: 0.0,
      distance: 0.0,
      longitudeSpeed: 0.0,
      latitudeSpeed: 0.0,
      distanceSpeed: 0.0,
      sign: signInfo['name'] as String,
      house: 0,
    );
  }
  
  /// 計算信仰點 (Part of Faith)
  /// 
  /// 公式：ASC + Jupiter - Sun
  static PlanetPosition calculatePartOfFaith(
    double ascendant,
    double sunLongitude,
    double jupiterLongitude,
  ) {
    final longitude = AngleCalculator.normalizeAngle(
      ascendant + jupiterLongitude - sunLongitude,
    );
    
    final signInfo = ZodiacCalculator.getZodiacSign(longitude);
    
    return PlanetPosition(
      id: 55,
      name: '信仰點',
      symbol: '♃',
      longitude: longitude,
      latitude: 0.0,
      distance: 0.0,
      longitudeSpeed: 0.0,
      latitudeSpeed: 0.0,
      distanceSpeed: 0.0,
      sign: signInfo['name'] as String,
      house: 0,
    );
  }
  
  /// 計算災難點 (Part of Catastrophe)
  /// 
  /// 公式：ASC + Saturn - Sun
  static PlanetPosition calculatePartOfCatastrophe(
    double ascendant,
    double sunLongitude,
    double saturnLongitude,
  ) {
    final longitude = AngleCalculator.normalizeAngle(
      ascendant + saturnLongitude - sunLongitude,
    );
    
    final signInfo = ZodiacCalculator.getZodiacSign(longitude);
    
    return PlanetPosition(
      id: 56,
      name: '災難點',
      symbol: '♄',
      longitude: longitude,
      latitude: 0.0,
      distance: 0.0,
      longitudeSpeed: 0.0,
      latitudeSpeed: 0.0,
      distanceSpeed: 0.0,
      sign: signInfo['name'] as String,
      house: 0,
    );
  }
  
  /// 查找指定名稱的行星
  static PlanetPosition? _findPlanet(List<PlanetPosition> planets, String name) {
    try {
      return planets.firstWhere((planet) => planet.name == name);
    } catch (e) {
      return null;
    }
  }
  
  /// 獲取阿拉伯點的描述
  static String getArabicPartDescription(String partName) {
    const descriptions = {
      '幸運點': '代表物質財富、健康和整體幸福感的指標',
      '精神點': '代表精神力量、意志和生命目標的指標',
      '愛情點': '代表愛情關係、美感和吸引力的指標',
      '商業點': '代表商業才能、溝通能力和交易的指標',
      '勇氣點': '代表勇氣、行動力和戰鬥精神的指標',
      '信仰點': '代表信仰、智慧和哲學思考的指標',
      '災難點': '代表困難、限制和需要克服的挑戰',
    };
    
    return descriptions[partName] ?? '特殊的阿拉伯點';
  }
}
