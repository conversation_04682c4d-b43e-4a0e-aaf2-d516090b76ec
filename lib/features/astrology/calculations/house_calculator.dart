import 'package:flutter/material.dart';
import 'package:sweph/sweph.dart';

import '../ephemeris/julian_date_converter.dart';
import '../utilities/angle_calculator.dart';

/// 宮位計算器
/// 
/// 負責計算 12 宮位的界線和相關數據，支援多種宮位系統：
/// - Placidus（普拉西德斯）
/// - <PERSON>（科赫）
/// - Equal House（等分宮位）
/// - Whole Sign（整宮制）
/// - Campanus（坎帕努斯）
/// - Regiomontanus（雷格蒙塔努斯）
class HouseCalculator {
  /// 支援的宮位系統
  static const Map<String, String> houseSystems = {
    'Placidus': 'P',
    'Koch': 'K',
    'Equal House': 'E',
    'Whole Sign': 'W',
    'Campanus': 'C',
    'Regiomontanus': 'R',
    'Topocentric': 'T',
    'Alcabitus': 'B',
    'Morinus': 'M',
    '<PERSON><PERSON><PERSON>ki': 'U',
    'Porphyrius': 'O',
    'Meridian': 'X',
    'Azimuthal': 'H',
    'Polich/Page': 'F',
    'Vehlow Equal': 'V',
    'Axial Rotation': 'A',
    'Horizontal': 'D',
    'Carter Poli Equatorial': 'N',
    'Pullen SD': 'G',
    'Pullen SR': 'I',
    'Sunshine': 'S',
    'Savard-A': 'L',
  };
  
  /// 計算宮位數據
  /// 
  /// [dateTime] 計算時間
  /// [latitude] 觀測地緯度
  /// [longitude] 觀測地經度
  /// [houseSystem] 宮位系統（預設為 Placidus）
  static Future<HouseCuspData?> calculateHouses({
    required DateTime dateTime,
    required double latitude,
    required double longitude,
    String houseSystem = 'Placidus',
  }) async {
    try {
      // 轉換為儒略日
      final julianDay = await JulianDateConverter.dateTimeToJulianDay(
        dateTime,
        latitude,
        longitude,
      );
      
      // 獲取宮位系統代碼
      final hsys = _getHouseSystemCode(houseSystem);
      
      // 使用 Swiss Ephemeris 計算宮位
      final houses = Sweph.swe_houses(
        julianDay,
        latitude,
        longitude,
        hsys,
      );
      
      debugPrint('宮位計算成功，系統: $houseSystem');
      return houses;
    } catch (e) {
      debugPrint('計算宮位時出錯: $e');
      return null;
    }
  }
  
  /// 計算等分宮位（備用方法）
  /// 
  /// 當 Swiss Ephemeris 不可用時使用的簡化計算方法
  static HouseCuspData? calculateEqualHouses({
    required double ascendantDegree,
  }) {
    try {
      final cusps = List<double>.filled(13, 0.0);
      final ascmc = List<double>.filled(10, 0.0);
      
      // 計算 12 個宮位界線（每個宮位 30 度）
      for (int i = 1; i <= 12; i++) {
        cusps[i] = AngleCalculator.normalizeAngle(ascendantDegree + (i - 1) * 30);
      }
      
      // 設定四個軸點
      ascmc[0] = ascendantDegree; // ASC
      ascmc[1] = AngleCalculator.normalizeAngle(ascendantDegree + 90); // MC
      ascmc[2] = AngleCalculator.normalizeAngle(ascendantDegree + 180); // DSC
      ascmc[3] = AngleCalculator.normalizeAngle(ascendantDegree + 270); // IC
      
      return HouseCuspData(cusps, ascmc);
    } catch (e) {
      debugPrint('計算等分宮位時出錯: $e');
      return null;
    }
  }
  
  /// 計算整宮制宮位
  /// 
  /// 整宮制中，每個星座對應一個完整的宮位
  static HouseCuspData? calculateWholeSignHouses({
    required double ascendantDegree,
  }) {
    try {
      final cusps = List<double>.filled(13, 0.0);
      final ascmc = List<double>.filled(10, 0.0);
      
      // 上升點所在星座的起始度數
      final ascendantSign = (ascendantDegree / 30).floor();
      final firstHouseStart = ascendantSign * 30.0;
      
      // 計算 12 個宮位界線（每個星座 30 度）
      for (int i = 1; i <= 12; i++) {
        cusps[i] = AngleCalculator.normalizeAngle(firstHouseStart + (i - 1) * 30);
      }
      
      // 設定四個軸點（保持原始度數）
      ascmc[0] = ascendantDegree; // ASC
      ascmc[1] = AngleCalculator.normalizeAngle(ascendantDegree + 90); // MC（簡化計算）
      ascmc[2] = AngleCalculator.normalizeAngle(ascendantDegree + 180); // DSC
      ascmc[3] = AngleCalculator.normalizeAngle(ascendantDegree + 270); // IC（簡化計算）
      
      return HouseCuspData(cusps, ascmc);
    } catch (e) {
      debugPrint('計算整宮制宮位時出錯: $e');
      return null;
    }
  }
  
  /// 獲取宮位系統代碼
  static Hsys _getHouseSystemCode(String houseSystem) {
    final systemCode = houseSystems[houseSystem] ?? 'P';

    switch (systemCode) {
      case 'P':
        return Hsys.P;
      case 'K':
        return Hsys.K;
      case 'E':
        return Hsys.E;
      case 'W':
        return Hsys.W;
      case 'C':
        return Hsys.C;
      case 'R':
        return Hsys.R;
      case 'T':
        return Hsys.T;
      case 'B':
        return Hsys.B;
      case 'M':
        return Hsys.M;
      case 'U':
        return Hsys.U;
      case 'O':
        return Hsys.O;
      case 'X':
        return Hsys.X;
      case 'H':
        return Hsys.H;
      case 'D':
        return Hsys.D;
      case 'N':
        return Hsys.N;
      case 'G':
        return Hsys.G;
      case 'I':
        return Hsys.I;
      case 'S':
        return Hsys.S;
      case 'L':
        return Hsys.L;
      case 'F':
        return Hsys.F;
      case 'V':
        return Hsys.V;
      case 'A':
        return Hsys.A;
      default:
        return Hsys.P; // 預設為 Placidus
    }
  }
  
  /// 驗證宮位數據的有效性
  static bool validateHousesData(HouseCuspData? houses) {
    if (houses == null) return false;
    
    // 檢查宮位界線數據
    if (houses.cusps.length < 13) return false;
    
    // 檢查軸點數據
    if (houses.ascmc.length < 4) return false;
    
    // 檢查角度範圍
    for (int i = 1; i <= 12; i++) {
      final cusp = houses.cusps[i];
      if (cusp < 0 || cusp >= 360) return false;
    }
    
    return true;
  }
  
  /// 獲取宮位名稱
  static String getHouseName(int houseNumber) {
    const houseNames = {
      1: '第一宮（命宮）',
      2: '第二宮（財帛宮）',
      3: '第三宮（兄弟宮）',
      4: '第四宮（田宅宮）',
      5: '第五宮（子女宮）',
      6: '第六宮（奴僕宮）',
      7: '第七宮（夫妻宮）',
      8: '第八宮（疾厄宮）',
      9: '第九宮（遷移宮）',
      10: '第十宮（官祿宮）',
      11: '第十一宮（福德宮）',
      12: '第十二宮（玄秘宮）',
    };
    
    return houseNames[houseNumber] ?? '未知宮位';
  }
  
  /// 獲取宮位簡稱
  static String getHouseShortName(int houseNumber) {
    const houseShortNames = {
      1: '命宮',
      2: '財帛',
      3: '兄弟',
      4: '田宅',
      5: '子女',
      6: '奴僕',
      7: '夫妻',
      8: '疾厄',
      9: '遷移',
      10: '官祿',
      11: '福德',
      12: '玄秘',
    };
    
    return houseShortNames[houseNumber] ?? '未知';
  }
  
  /// 獲取宮位主題
  static String getHouseTheme(int houseNumber) {
    const houseThemes = {
      1: '自我、個性、外貌、第一印象',
      2: '金錢、物質、價值觀、自我價值',
      3: '溝通、學習、兄弟姊妹、短途旅行',
      4: '家庭、根源、情感基礎、房地產',
      5: '創造、娛樂、戀愛、子女、投機',
      6: '工作、健康、服務、日常習慣',
      7: '伴侶、合作、公開敵人、法律事務',
      8: '轉化、死亡、他人資源、神秘學',
      9: '哲學、宗教、高等教育、長途旅行',
      10: '事業、聲譽、社會地位、權威',
      11: '朋友、團體、希望、社會理想',
      12: '潛意識、犧牲、隱藏、精神修行',
    };
    
    return houseThemes[houseNumber] ?? '未知主題';
  }
  
  /// 計算宮位大小（度數）
  static double getHouseSize(int houseNumber, HouseCuspData houses) {
    if (!validateHousesData(houses)) return 30.0; // 預設 30 度
    
    final currentCusp = houses.cusps[houseNumber];
    final nextCusp = houses.cusps[houseNumber == 12 ? 1 : houseNumber + 1];
    
    double size = nextCusp - currentCusp;
    if (size < 0) size += 360; // 處理跨越 0 度的情況
    
    return size;
  }
  
  /// 獲取宮位中點
  static double getHouseMidpoint(int houseNumber, HouseCuspData houses) {
    if (!validateHousesData(houses)) return 0.0;
    
    final currentCusp = houses.cusps[houseNumber];
    final houseSize = getHouseSize(houseNumber, houses);
    
    return AngleCalculator.normalizeAngle(currentCusp + houseSize / 2);
  }
}
