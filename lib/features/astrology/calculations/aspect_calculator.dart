import 'dart:math' as math;

import '../../../data/models/astrology/aspect_info.dart';
import '../../../data/models/astrology/planet_position.dart';
import '../constants/aspect_definitions.dart';
import '../utilities/angle_calculator.dart';

/// 相位計算器
/// 
/// 負責計算行星間的角度關係（相位），包括：
/// - 主要相位：合相、對沖、三分相、四分相、六分相
/// - 次要相位：半四分相、半三分相、五分相等
/// - 容許度計算
/// - 入相/出相判斷
class AspectCalculator {
  /// 計算所有行星間的相位
  /// 
  /// [planets] 行星位置列表
  /// [aspectOrbs] 自定義容許度設定
  /// [includeMinorAspects] 是否包含次要相位
  static List<AspectInfo> calculateAllAspects(
    List<PlanetPosition> planets, {
    Map<String, double>? aspectOrbs,
    bool includeMinorAspects = false,
  }) {
    final List<AspectInfo> aspects = [];
    
    // 遍歷所有行星組合
    for (int i = 0; i < planets.length; i++) {
      for (int j = i + 1; j < planets.length; j++) {
        final planet1 = planets[i];
        final planet2 = planets[j];
        
        // 計算兩行星間的相位
        final aspectInfo = calculateAspectBetweenPlanets(
          planet1,
          planet2,
          aspectOrbs: aspectOrbs,
          includeMinorAspects: includeMinorAspects,
        );
        
        if (aspectInfo != null) {
          aspects.add(aspectInfo);
        }
      }
    }
    
    return aspects;
  }
  
  /// 計算兩個行星間的相位
  /// 
  /// [planet1] 第一個行星
  /// [planet2] 第二個行星
  /// [aspectOrbs] 自定義容許度設定
  /// [includeMinorAspects] 是否包含次要相位
  static AspectInfo? calculateAspectBetweenPlanets(
    PlanetPosition planet1,
    PlanetPosition planet2, {
    Map<String, double>? aspectOrbs,
    bool includeMinorAspects = false,
  }) {
    // 計算兩行星間的角度差
    final angleDifference = AngleCalculator.calculateAngleDifference(
      planet1.longitude,
      planet2.longitude,
    );
    
    // 獲取相位定義列表
    final aspectDefinitions = includeMinorAspects 
        ? AspectDefinitions.allAspects 
        : AspectDefinitions.majorAspects;
    
    // 檢查每個相位定義
    for (final aspectDef in aspectDefinitions) {
      final aspectAngle = aspectDef['angle'] as double;
      final defaultOrb = aspectDef['orb'] as double;
      final aspectName = aspectDef['name'] as String;
      
      // 獲取實際容許度
      final actualOrb = aspectOrbs?[aspectName] ?? defaultOrb;
      
      // 檢查是否在容許度範圍內
      if (_isWithinOrb(angleDifference, aspectAngle, actualOrb)) {
        // 計算精確度（距離完美相位的度數）
        final exactness = _calculateExactness(angleDifference, aspectAngle);
        
        // 判斷入相或出相
        final direction = _calculateAspectDirection(
          planet1.longitude,
          planet2.longitude,
          planet1.longitudeSpeed,
          planet2.longitudeSpeed,
          aspectAngle,
        );
        
        return AspectInfo(
          planet1: planet1,
          planet2: planet2,
          aspect: aspectName,
          shortZh: aspectDef['shortZh'] as String,
          symbol: aspectDef['symbol'] as String,
          angle: aspectAngle.round(),
          orb: exactness,
          direction: direction,
        );
      }
    }
    
    return null; // 沒有找到相位
  }
  
  /// 檢查角度是否在容許度範圍內
  static bool _isWithinOrb(double angleDifference, double aspectAngle, double orb) {
    final difference = (angleDifference - aspectAngle).abs();
    return difference <= orb || (360 - difference) <= orb;
  }
  
  /// 計算相位的精確度（距離完美相位的度數）
  static double _calculateExactness(double angleDifference, double aspectAngle) {
    final difference = (angleDifference - aspectAngle).abs();
    return math.min(difference, 360 - difference);
  }
  
  /// 計算相位方向（入相或出相）
  ///
  /// 入相：兩行星正在接近完美相位
  /// 出相：兩行星正在遠離完美相位
  static AspectDirection? _calculateAspectDirection(
    double longitude1,
    double longitude2,
    double speed1,
    double speed2,
    double aspectAngle,
  ) {
    // 如果沒有速度數據，返回 null
    if (speed1 == 0 && speed2 == 0) return null;

    // 計算當前角度差
    final currentAngle = AngleCalculator.calculateAngleDifference(longitude1, longitude2);

    // 計算一天後的角度差
    final futureAngle1 = AngleCalculator.normalizeAngle(longitude1 + speed1);
    final futureAngle2 = AngleCalculator.normalizeAngle(longitude2 + speed2);
    final futureAngleDiff = AngleCalculator.calculateAngleDifference(futureAngle1, futureAngle2);

    // 計算當前和未來與完美相位的距離
    final currentDistance = _calculateExactness(currentAngle, aspectAngle);
    final futureDistance = _calculateExactness(futureAngleDiff, aspectAngle);

    if (futureDistance < currentDistance) {
      return AspectDirection.applying; // 正在接近
    } else if (futureDistance > currentDistance) {
      return AspectDirection.separating; // 正在遠離
    } else {
      return null; // 距離相同，無法判斷
    }
  }
  
  /// 計算相位強度
  /// 
  /// 基於容許度和行星重要性計算相位的影響強度
  static double calculateAspectStrength(
    AspectInfo aspect,
    List<PlanetPosition> planets,
  ) {
    // 獲取行星重要性權重
    final planet1Weight = _getPlanetWeight(aspect.planet1.name);
    final planet2Weight = _getPlanetWeight(aspect.planet2.name);
    
    // 獲取相位類型權重
    final aspectWeight = _getAspectWeight(aspect.aspect);
    
    // 計算容許度因子（越接近完美相位，強度越高）
    final maxOrb = AspectDefinitions.getDefaultOrb(aspect.aspect);
    final orbFactor = maxOrb > 0 ? (maxOrb - aspect.orb) / maxOrb : 1.0;
    
    // 綜合計算強度
    final strength = (planet1Weight + planet2Weight) * aspectWeight * orbFactor;
    
    return math.max(0.0, math.min(1.0, strength)); // 限制在 0-1 範圍內
  }
  
  /// 獲取行星重要性權重
  static double _getPlanetWeight(String planetName) {
    const planetWeights = {
      '太陽': 1.0,
      '月亮': 1.0,
      '水星': 0.8,
      '金星': 0.8,
      '火星': 0.8,
      '木星': 0.9,
      '土星': 0.9,
      '天王星': 0.6,
      '海王星': 0.6,
      '冥王星': 0.6,
      '上升點': 0.9,
      '中天': 0.8,
      '下降點': 0.7,
      '天底': 0.7,
    };
    
    return planetWeights[planetName] ?? 0.5;
  }
  
  /// 獲取相位類型權重
  static double _getAspectWeight(String aspectName) {
    const aspectWeights = {
      '合相': 1.0,
      '對沖': 0.9,
      '三分相': 0.8,
      '四分相': 0.8,
      '六分相': 0.6,
      '半四分相': 0.4,
      '半三分相': 0.4,
      '五分相': 0.3,
      '八分相': 0.3,
    };
    
    return aspectWeights[aspectName] ?? 0.2;
  }
  
  /// 獲取相位的詳細描述
  static String getAspectDescription(AspectInfo aspect) {
    final descriptions = {
      '合相': '兩行星能量融合，產生強烈的聯合效應',
      '對沖': '兩行星能量對立，需要尋求平衡',
      '三分相': '兩行星能量和諧流動，帶來機會和天賦',
      '四分相': '兩行星能量衝突，帶來挑戰和成長機會',
      '六分相': '兩行星能量輕鬆合作，帶來小的機會',
      '半四分相': '輕微的緊張和摩擦',
      '半三分相': '創造性的機會和靈感',
      '五分相': '創新和突破的能量',
      '八分相': '調整和適應的需要',
    };
    
    return descriptions[aspect.aspect] ?? '特殊的能量互動';
  }
  
  /// 按強度排序相位
  static List<AspectInfo> sortAspectsByStrength(
    List<AspectInfo> aspects,
    List<PlanetPosition> planets,
  ) {
    final aspectsWithStrength = aspects.map((aspect) {
      final strength = calculateAspectStrength(aspect, planets);
      return {'aspect': aspect, 'strength': strength};
    }).toList();
    
    // 按強度降序排列
    aspectsWithStrength.sort((a, b) => 
        (b['strength'] as double).compareTo(a['strength'] as double));
    
    return aspectsWithStrength
        .map((item) => item['aspect'] as AspectInfo)
        .toList();
  }
  
  /// 過濾弱相位
  static List<AspectInfo> filterWeakAspects(
    List<AspectInfo> aspects,
    List<PlanetPosition> planets, {
    double minimumStrength = 0.3,
  }) {
    return aspects.where((aspect) {
      final strength = calculateAspectStrength(aspect, planets);
      return strength >= minimumStrength;
    }).toList();
  }
}
