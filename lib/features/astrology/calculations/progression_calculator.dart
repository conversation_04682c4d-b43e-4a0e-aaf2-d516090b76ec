import '../../../data/models/astrology/planet_position.dart';
import '../../../data/models/user/birth_data.dart';
import '../utilities/angle_calculator.dart';
import 'planet_calculator.dart';

/// 推運計算器
/// 
/// 計算各種推運技法，包括：
/// - 次限推運（Secondary Progression）
/// - 太陽弧推運（Solar Arc Progression）
/// - 三限推運（Tertiary Progression）
/// - 小限法（Profection）
class ProgressionCalculator {
  /// 計算次限推運
  /// 
  /// 使用「一日等於一年」的原則
  /// 
  /// [birthData] 出生數據
  /// [targetDate] 目標日期
  /// 
  /// 返回推運後的行星位置
  static Future<List<PlanetPosition>> calculateSecondaryProgression(
    BirthData birthData,
    DateTime targetDate,
  ) async {
    // 計算年齡（以天為單位）
    final ageInDays = targetDate.difference(birthData.dateTime).inDays;
    
    // 推運日期 = 出生日期 + 年齡天數
    final progressedDate = birthData.dateTime.add(Duration(days: ageInDays));
    
    // 計算推運後的行星位置
    final progressedPlanets = await PlanetCalculator.calculateAllPlanets(
      dateTime: progressedDate,
      latitude: birthData.latitude,
      longitude: birthData.longitude,
    );
    
    return progressedPlanets;
  }
  
  /// 計算太陽弧推運
  /// 
  /// 所有行星都按照太陽的推運速度移動
  /// 
  /// [birthData] 出生數據
  /// [targetDate] 目標日期
  /// [natalPlanets] 本命行星位置
  /// 
  /// 返回太陽弧推運後的行星位置
  static Future<List<PlanetPosition>> calculateSolarArcProgression(
    BirthData birthData,
    DateTime targetDate,
    List<PlanetPosition> natalPlanets,
  ) async {
    // 計算年齡（以年為單位）
    final ageInYears = _calculateAgeInYears(birthData.dateTime, targetDate);
    
    // 太陽弧 = 年齡 × 太陽平均移動速度（約 0.9856 度/天）
    final solarArc = ageInYears * 0.9856;
    
    final progressedPlanets = <PlanetPosition>[];
    
    for (final planet in natalPlanets) {
      final progressedLongitude = AngleCalculator.normalizeAngle(
        planet.longitude + solarArc,
      );
      
      final progressedPlanet = PlanetPosition(
        id: planet.id,
        name: planet.name,
        symbol: planet.symbol,
        longitude: progressedLongitude,
        latitude: planet.latitude,
        distance: planet.distance,
        longitudeSpeed: planet.longitudeSpeed,
        latitudeSpeed: planet.latitudeSpeed,
        distanceSpeed: planet.distanceSpeed,
        sign: planet.sign, // 需要重新計算
        house: planet.house, // 需要重新計算
      );
      
      progressedPlanets.add(progressedPlanet);
    }
    
    return progressedPlanets;
  }
  
  /// 計算三限推運
  /// 
  /// 使用「一個月等於一年」的原則
  /// 
  /// [birthData] 出生數據
  /// [targetDate] 目標日期
  /// 
  /// 返回三限推運後的行星位置
  static Future<List<PlanetPosition>> calculateTertiaryProgression(
    BirthData birthData,
    DateTime targetDate,
  ) async {
    // 計算年齡（以月為單位）
    final ageInMonths = _calculateAgeInMonths(birthData.dateTime, targetDate);
    
    // 推運日期 = 出生日期 + 年齡月數
    final progressedDate = DateTime(
      birthData.dateTime.year,
      birthData.dateTime.month + ageInMonths,
      birthData.dateTime.day,
      birthData.dateTime.hour,
      birthData.dateTime.minute,
    );
    
    // 計算推運後的行星位置
    final progressedPlanets = await PlanetCalculator.calculateAllPlanets(
      dateTime: progressedDate,
      latitude: birthData.latitude,
      longitude: birthData.longitude,
    );
    
    return progressedPlanets;
  }
  
  /// 計算小限法（Profection）
  /// 
  /// 古典占星的年運技法，每年激活一個宮位
  /// 
  /// [birthData] 出生數據
  /// [targetDate] 目標日期
  /// 
  /// 返回小限法信息
  static Map<String, dynamic> calculateProfection(
    BirthData birthData,
    DateTime targetDate,
  ) {
    // 計算年齡
    final age = _calculateAgeInYears(birthData.dateTime, targetDate).floor();
    
    // 小限宮位 = (年齡 % 12) + 1
    final profectionHouse = (age % 12) + 1;
    
    // 計算小限主星
    final profectionLord = _getProfectionLord(profectionHouse);
    
    // 計算小限年的開始和結束日期
    final profectionStart = DateTime(
      birthData.dateTime.year + age,
      birthData.dateTime.month,
      birthData.dateTime.day,
    );
    
    final profectionEnd = DateTime(
      birthData.dateTime.year + age + 1,
      birthData.dateTime.month,
      birthData.dateTime.day,
    ).subtract(const Duration(days: 1));
    
    return {
      'age': age,
      'house': profectionHouse,
      'lord': profectionLord,
      'startDate': profectionStart,
      'endDate': profectionEnd,
      'description': _getProfectionDescription(profectionHouse),
    };
  }
  
  /// 計算年齡（以年為單位）
  static double _calculateAgeInYears(DateTime birthDate, DateTime targetDate) {
    final difference = targetDate.difference(birthDate);
    return difference.inDays / 365.25;
  }
  
  /// 計算年齡（以月為單位）
  static int _calculateAgeInMonths(DateTime birthDate, DateTime targetDate) {
    int months = (targetDate.year - birthDate.year) * 12;
    months += targetDate.month - birthDate.month;
    
    if (targetDate.day < birthDate.day) {
      months--;
    }
    
    return months;
  }
  
  /// 獲取小限主星
  static String _getProfectionLord(int house) {
    // 傳統宮位主星對應
    const houseLords = {
      1: '火星',      // 白羊座
      2: '金星',      // 金牛座
      3: '水星',      // 雙子座
      4: '月亮',      // 巨蟹座
      5: '太陽',      // 獅子座
      6: '水星',      // 處女座
      7: '金星',      // 天秤座
      8: '火星',      // 天蠍座
      9: '木星',      // 射手座
      10: '土星',     // 摩羯座
      11: '土星',     // 水瓶座
      12: '木星',     // 雙魚座
    };
    
    return houseLords[house] ?? '未知';
  }
  
  /// 獲取小限宮位描述
  static String _getProfectionDescription(int house) {
    const descriptions = {
      1: '自我與個性年：關注個人形象、健康和新的開始',
      2: '財富與價值年：關注金錢、物質資源和個人價值',
      3: '溝通與學習年：關注溝通、學習、兄弟姊妹和短途旅行',
      4: '家庭與根源年：關注家庭、房地產和情感基礎',
      5: '創造與娛樂年：關注創造力、戀愛、子女和娛樂',
      6: '工作與健康年：關注工作、健康、服務和日常習慣',
      7: '伴侶與合作年：關注伴侶關係、合作和公開敵人',
      8: '轉化與神秘年：關注轉化、他人資源和深層心理',
      9: '哲學與遠行年：關注哲學、宗教、高等教育和長途旅行',
      10: '事業與聲譽年：關注事業、社會地位和公眾形象',
      11: '朋友與理想年：關注朋友、團體、希望和社會理想',
      12: '潛意識與靈性年：關注潛意識、犧牲、隱藏和精神修行',
    };
    
    return descriptions[house] ?? '未知宮位年';
  }
  
  /// 計算推運月相
  /// 
  /// [birthData] 出生數據
  /// [targetDate] 目標日期
  /// 
  /// 返回推運月相信息
  static Future<Map<String, dynamic>> calculateProgressedLunation(
    BirthData birthData,
    DateTime targetDate,
  ) async {
    // 計算推運後的太陽和月亮位置
    final progressedPlanets = await calculateSecondaryProgression(
      birthData,
      targetDate,
    );
    
    final progressedSun = progressedPlanets.firstWhere(
      (p) => p.name == '太陽',
    );
    final progressedMoon = progressedPlanets.firstWhere(
      (p) => p.name == '月亮',
    );
    
    // 計算月相角度
    final phaseAngle = AngleCalculator.calculateAngleDifference(
      progressedMoon.longitude,
      progressedSun.longitude,
    );
    
    // 確定月相類型
    final phaseType = _getLunationPhase(phaseAngle);
    
    return {
      'sunLongitude': progressedSun.longitude,
      'moonLongitude': progressedMoon.longitude,
      'phaseAngle': phaseAngle,
      'phaseType': phaseType,
      'description': _getLunationDescription(phaseType),
    };
  }
  
  /// 確定月相類型
  static String _getLunationPhase(double angle) {
    if (angle < 45) return '新月';
    if (angle < 90) return '上弦月';
    if (angle < 135) return '盈凸月';
    if (angle < 180) return '滿月';
    if (angle < 225) return '虧凸月';
    if (angle < 270) return '下弦月';
    if (angle < 315) return '殘月';
    return '新月';
  }
  
  /// 獲取月相描述
  static String _getLunationDescription(String phaseType) {
    const descriptions = {
      '新月': '新的開始，種下意圖的種子',
      '上弦月': '行動和決定的時期',
      '盈凸月': '調整和完善的階段',
      '滿月': '成果顯現，情感高漲',
      '虧凸月': '感恩和分享的時期',
      '下弦月': '釋放和放下的階段',
      '殘月': '反思和準備新循環',
    };
    
    return descriptions[phaseType] ?? '未知月相';
  }
}
