import 'dart:math' as math;

import 'package:timezone/timezone.dart' as tz;

import '../../../core/utils/logger_utils.dart';

/// 時區計算器
/// 
/// 提供精確的時區計算功能，考慮：
/// - 地理位置的時區
/// - 夏令時調整
/// - 歷史時區變更
/// - 特殊地區的時區規則
class TimezoneCalculator {
  /// 計算指定位置和時間的時區偏移
  /// 
  /// [latitude] 緯度
  /// [longitude] 經度
  /// [dateTime] 日期時間
  /// 
  /// 返回相對於 UTC 的小時偏移（正數表示東時區，負數表示西時區）
  static Future<double> calculateTimezoneOffset(
    double latitude,
    double longitude,
    DateTime dateTime,
  ) async {
    try {
      // 首先嘗試根據經度估算時區
      final estimatedOffset = _estimateTimezoneFromLongitude(longitude);
      
      // 嘗試獲取精確的時區信息
      final preciseOffset = await _getPreciseTimezoneOffset(
        latitude,
        longitude,
        dateTime,
      );
      
      // 如果獲取到精確信息，使用精確值；否則使用估算值
      return preciseOffset ?? estimatedOffset;
    } catch (e) {
      logger.w('計算時區偏移時出錯，使用估算值: $e');
      return _estimateTimezoneFromLongitude(longitude);
    }
  }
  
  /// 根據經度估算時區偏移
  /// 
  /// 這是一個簡化的計算方法，每 15 度經度對應 1 小時時差
  static double _estimateTimezoneFromLongitude(double longitude) {
    // 將經度標準化到 -180 到 180 度範圍
    double normalizedLongitude = longitude;
    while (normalizedLongitude > 180) normalizedLongitude -= 360;
    while (normalizedLongitude < -180) normalizedLongitude += 360;
    
    // 每 15 度對應 1 小時
    final offset = normalizedLongitude / 15.0;
    
    // 四捨五入到最近的半小時
    return (offset * 2).round() / 2.0;
  }
  
  /// 獲取精確的時區偏移
  /// 
  /// 嘗試使用時區數據庫獲取精確的時區信息
  static Future<double?> _getPreciseTimezoneOffset(
    double latitude,
    double longitude,
    DateTime dateTime,
  ) async {
    try {
      // 根據座標查找時區
      final timezoneName = _findTimezoneByCoordinates(latitude, longitude);
      
      if (timezoneName != null) {
        final location = tz.getLocation(timezoneName);
        final zonedDateTime = tz.TZDateTime.from(dateTime, location);
        
        // 計算偏移（以小時為單位）
        final offsetInMilliseconds = zonedDateTime.timeZoneOffset.inMilliseconds;
        final offsetInHours = offsetInMilliseconds / (1000 * 60 * 60);
        
        logger.d('找到精確時區: $timezoneName, 偏移: $offsetInHours 小時');
        return offsetInHours;
      }
    } catch (e) {
      logger.w('獲取精確時區信息失敗: $e');
    }
    
    return null;
  }
  
  /// 根據座標查找時區名稱
  /// 
  /// 這是一個簡化的實現，實際應用中可能需要更複雜的地理時區數據庫
  static String? _findTimezoneByCoordinates(double latitude, double longitude) {
    // 主要城市和地區的時區映射
    final timezoneRegions = [
      // 亞洲
      {'name': 'Asia/Taipei', 'lat': 25.0330, 'lng': 121.5654, 'radius': 2.0},
      {'name': 'Asia/Shanghai', 'lat': 31.2304, 'lng': 121.4737, 'radius': 10.0},
      {'name': 'Asia/Tokyo', 'lat': 35.6762, 'lng': 139.6503, 'radius': 5.0},
      {'name': 'Asia/Seoul', 'lat': 37.5665, 'lng': 126.9780, 'radius': 3.0},
      {'name': 'Asia/Hong_Kong', 'lat': 22.3193, 'lng': 114.1694, 'radius': 1.0},
      {'name': 'Asia/Singapore', 'lat': 1.3521, 'lng': 103.8198, 'radius': 1.0},
      {'name': 'Asia/Bangkok', 'lat': 13.7563, 'lng': 100.5018, 'radius': 3.0},
      {'name': 'Asia/Jakarta', 'lat': -6.2088, 'lng': 106.8456, 'radius': 5.0},
      {'name': 'Asia/Manila', 'lat': 14.5995, 'lng': 120.9842, 'radius': 3.0},
      {'name': 'Asia/Kuala_Lumpur', 'lat': 3.1390, 'lng': 101.6869, 'radius': 2.0},
      
      // 歐洲
      {'name': 'Europe/London', 'lat': 51.5074, 'lng': -0.1278, 'radius': 5.0},
      {'name': 'Europe/Paris', 'lat': 48.8566, 'lng': 2.3522, 'radius': 5.0},
      {'name': 'Europe/Berlin', 'lat': 52.5200, 'lng': 13.4050, 'radius': 5.0},
      {'name': 'Europe/Rome', 'lat': 41.9028, 'lng': 12.4964, 'radius': 3.0},
      {'name': 'Europe/Madrid', 'lat': 40.4168, 'lng': -3.7038, 'radius': 3.0},
      {'name': 'Europe/Moscow', 'lat': 55.7558, 'lng': 37.6176, 'radius': 10.0},
      
      // 北美洲
      {'name': 'America/New_York', 'lat': 40.7128, 'lng': -74.0060, 'radius': 10.0},
      {'name': 'America/Los_Angeles', 'lat': 34.0522, 'lng': -118.2437, 'radius': 10.0},
      {'name': 'America/Chicago', 'lat': 41.8781, 'lng': -87.6298, 'radius': 10.0},
      {'name': 'America/Denver', 'lat': 39.7392, 'lng': -104.9903, 'radius': 8.0},
      {'name': 'America/Toronto', 'lat': 43.6532, 'lng': -79.3832, 'radius': 5.0},
      {'name': 'America/Vancouver', 'lat': 49.2827, 'lng': -123.1207, 'radius': 3.0},
      
      // 大洋洲
      {'name': 'Australia/Sydney', 'lat': -33.8688, 'lng': 151.2093, 'radius': 5.0},
      {'name': 'Australia/Melbourne', 'lat': -37.8136, 'lng': 144.9631, 'radius': 3.0},
      {'name': 'Australia/Perth', 'lat': -31.9505, 'lng': 115.8605, 'radius': 3.0},
      {'name': 'Pacific/Auckland', 'lat': -36.8485, 'lng': 174.7633, 'radius': 2.0},
    ];
    
    // 查找最近的時區
    String? closestTimezone;
    double minDistance = double.infinity;
    
    for (final region in timezoneRegions) {
      final distance = _calculateDistance(
        latitude,
        longitude,
        region['lat'] as double,
        region['lng'] as double,
      );
      
      final radius = region['radius'] as double;
      
      if (distance <= radius && distance < minDistance) {
        minDistance = distance;
        closestTimezone = region['name'] as String;
      }
    }
    
    return closestTimezone;
  }
  
  /// 計算兩點間的距離（度數）
  static double _calculateDistance(
    double lat1,
    double lng1,
    double lat2,
    double lng2,
  ) {
    final deltaLat = lat2 - lat1;
    final deltaLng = lng2 - lng1;
    return math.sqrt(deltaLat * deltaLat + deltaLng * deltaLng);
  }
  
  /// 檢查指定日期是否為夏令時
  ///
  /// [timezoneName] 時區名稱
  /// [dateTime] 日期時間
  ///
  /// 返回是否為夏令時
  static bool isDaylightSavingTime(String timezoneName, DateTime dateTime) {
    try {
      final location = tz.getLocation(timezoneName);
      final zonedDateTime = tz.TZDateTime.from(dateTime, location);

      // 檢查時區偏移是否包含夏令時調整
      final timeZone = location.timeZone(dateTime.millisecondsSinceEpoch);
      final standardOffset = timeZone.offset;
      return zonedDateTime.timeZoneOffset != Duration(milliseconds: standardOffset);
    } catch (e) {
      logger.w('檢查夏令時狀態失敗: $e');
      return false;
    }
  }
  
  /// 獲取時區的標準偏移（不包含夏令時）
  /// 
  /// [timezoneName] 時區名稱
  /// 
  /// 返回標準偏移小時數
  static double getStandardOffset(String timezoneName) {
    try {
      final location = tz.getLocation(timezoneName);
      
      // 使用一月份的日期來獲取標準時間（通常不是夏令時）
      final januaryDate = DateTime(2024, 1, 15);
      final zonedDateTime = tz.TZDateTime.from(januaryDate, location);
      
      final offsetInMilliseconds = zonedDateTime.timeZoneOffset.inMilliseconds;
      return offsetInMilliseconds / (1000 * 60 * 60);
    } catch (e) {
      logger.w('獲取標準時區偏移失敗: $e');
      return 0.0;
    }
  }
  
  /// 轉換時間到指定時區
  /// 
  /// [dateTime] 原始日期時間
  /// [fromTimezone] 原始時區
  /// [toTimezone] 目標時區
  /// 
  /// 返回轉換後的日期時間
  static DateTime convertTimezone(
    DateTime dateTime,
    String fromTimezone,
    String toTimezone,
  ) {
    try {
      final fromLocation = tz.getLocation(fromTimezone);
      final toLocation = tz.getLocation(toTimezone);
      
      final fromZoned = tz.TZDateTime.from(dateTime, fromLocation);
      final toZoned = tz.TZDateTime.from(fromZoned, toLocation);
      
      return toZoned;
    } catch (e) {
      logger.w('時區轉換失敗: $e');
      return dateTime;
    }
  }
  
  /// 獲取所有可用的時區列表
  static List<String> getAvailableTimezones() {
    return tz.timeZoneDatabase.locations.keys.toList()..sort();
  }
  
  /// 根據國家代碼獲取主要時區
  /// 
  /// [countryCode] 國家代碼（如 'TW', 'US', 'CN'）
  /// 
  /// 返回該國家的主要時區列表
  static List<String> getTimezonesByCountry(String countryCode) {
    final countryTimezones = {
      'TW': ['Asia/Taipei'],
      'CN': ['Asia/Shanghai'],
      'JP': ['Asia/Tokyo'],
      'KR': ['Asia/Seoul'],
      'HK': ['Asia/Hong_Kong'],
      'SG': ['Asia/Singapore'],
      'TH': ['Asia/Bangkok'],
      'ID': ['Asia/Jakarta'],
      'PH': ['Asia/Manila'],
      'MY': ['Asia/Kuala_Lumpur'],
      'US': [
        'America/New_York',
        'America/Chicago',
        'America/Denver',
        'America/Los_Angeles',
        'America/Anchorage',
        'Pacific/Honolulu',
      ],
      'GB': ['Europe/London'],
      'FR': ['Europe/Paris'],
      'DE': ['Europe/Berlin'],
      'IT': ['Europe/Rome'],
      'ES': ['Europe/Madrid'],
      'RU': ['Europe/Moscow'],
      'AU': [
        'Australia/Sydney',
        'Australia/Melbourne',
        'Australia/Perth',
        'Australia/Adelaide',
        'Australia/Brisbane',
        'Australia/Darwin',
      ],
      'NZ': ['Pacific/Auckland'],
    };
    
    return countryTimezones[countryCode.toUpperCase()] ?? [];
  }
}
