import 'package:sweph/sweph.dart';

import '../../../core/utils/logger_utils.dart';

/// Swiss Ephemeris 包裝器
/// 
/// 提供對 Swiss Ephemeris 引擎的統一封裝，包括：
/// - 初始化和配置
/// - 行星位置計算
/// - 宮位計算
/// - 錯誤處理和日誌記錄
class SwissEphemerisWrapper {
  static bool _isInitialized = false;
  static String? _version;
  
  /// 初始化 Swiss Ephemeris
  static Future<bool> initialize() async {
    if (_isInitialized) {
      return true;
    }
    
    try {
      // 獲取版本信息
      _version = Sweph.swe_version();
      logger.i('Swiss Ephemeris 初始化成功，版本: $_version');
      
      _isInitialized = true;
      return true;
    } catch (e) {
      logger.e('Swiss Ephemeris 初始化失敗: $e');
      return false;
    }
  }
  
  /// 獲取版本信息
  static String? getVersion() {
    return _version;
  }
  
  /// 檢查是否已初始化
  static bool get isInitialized => _isInitialized;
  
  /// 計算行星位置
  ///
  /// [julianDay] 儒略日
  /// [planet] 行星/天體
  /// [flags] 計算標誌
  ///
  /// 返回計算結果
  static CoordinatesWithSpeed? calculatePlanetPosition(
    double julianDay,
    HeavenlyBody planet, {
    SwephFlag flags = SwephFlag.SEFLG_SWIEPH,
  }) {
    if (!_isInitialized) {
      logger.w('Swiss Ephemeris 尚未初始化');
      return null;
    }
    
    try {
      final result = Sweph.swe_calc_ut(julianDay, planet, flags);
      
      logger.d('計算行星位置成功: $planet');
      logger.d('經度: ${result.longitude.toStringAsFixed(6)}°');
      logger.d('緯度: ${result.latitude.toStringAsFixed(6)}°');
      logger.d('距離: ${result.distance.toStringAsFixed(6)} AU');
      
      return result;
    } catch (e) {
      logger.e('計算行星位置失敗: $planet, 錯誤: $e');
      return null;
    }
  }
  
  /// 計算宮位
  /// 
  /// [julianDay] 儒略日
  /// [latitude] 緯度
  /// [longitude] 經度
  /// [houseSystem] 宮位系統
  /// 
  /// 返回宮位數據
  static HouseCuspData? calculateHouses(
    double julianDay,
    double latitude,
    double longitude,
    Hsys houseSystem,
  ) {
    if (!_isInitialized) {
      logger.w('Swiss Ephemeris 尚未初始化');
      return null;
    }
    
    try {
      final houses = Sweph.swe_houses(
        julianDay,
        latitude,
        longitude,
        houseSystem,
      );
      
      logger.d('計算宮位成功: $houseSystem');
      logger.d('上升點: ${houses.ascmc[0].toStringAsFixed(6)}°');
      logger.d('中天: ${houses.ascmc[1].toStringAsFixed(6)}°');
      
      return houses;
    } catch (e) {
      logger.e('計算宮位失敗: $houseSystem, 錯誤: $e');
      return null;
    }
  }
  
  /// 轉換儒略日為日曆日期
  ///
  /// [julianDay] 儒略日
  /// [calendarType] 日曆類型
  ///
  /// 返回日期時間
  static DateTime? julianDayToDateTime(
    double julianDay, {
    CalendarType calendarType = CalendarType.SE_GREG_CAL,
  }) {
    if (!_isInitialized) {
      logger.w('Swiss Ephemeris 尚未初始化');
      return null;
    }

    try {
      final dateTime = Sweph.swe_jdut1_to_utc(julianDay, calendarType);

      logger.d('儒略日轉換成功: $julianDay -> ${dateTime.year}-${dateTime.month}-${dateTime.day}');

      // 轉換為 Dart DateTime
      return DateTime.utc(
        dateTime.year,
        dateTime.month,
        dateTime.day,
        dateTime.hour,
        dateTime.minute,
        dateTime.second,
      );
    } catch (e) {
      logger.e('儒略日轉換失敗: $julianDay, 錯誤: $e');
      return null;
    }
  }
  
  /// 轉換日曆日期為儒略日
  /// 
  /// [year] 年
  /// [month] 月
  /// [day] 日
  /// [hour] 小時（包含分鐘和秒的小數部分）
  /// [calendarType] 日曆類型
  /// 
  /// 返回儒略日
  static double? dateTimeToJulianDay(
    int year,
    int month,
    int day,
    double hour, {
    CalendarType calendarType = CalendarType.SE_GREG_CAL,
  }) {
    if (!_isInitialized) {
      logger.w('Swiss Ephemeris 尚未初始化');
      return null;
    }
    
    try {
      final julianDay = Sweph.swe_julday(year, month, day, hour, calendarType);
      
      logger.d('日期轉換成功: $year-$month-$day $hour -> $julianDay');
      
      return julianDay;
    } catch (e) {
      logger.e('日期轉換失敗: $year-$month-$day $hour, 錯誤: $e');
      return null;
    }
  }
  
  /// 計算恆星時
  /// 
  /// [julianDay] 儒略日
  /// [longitude] 經度
  /// 
  /// 返回恆星時（小時）
  static double? calculateSiderealTime(double julianDay, double longitude) {
    if (!_isInitialized) {
      logger.w('Swiss Ephemeris 尚未初始化');
      return null;
    }
    
    try {
      final siderealTime = Sweph.swe_sidtime(julianDay) + longitude / 15.0;
      
      logger.d('恆星時計算成功: ${siderealTime.toStringAsFixed(6)} 小時');
      
      return siderealTime;
    } catch (e) {
      logger.e('恆星時計算失敗: $e');
      return null;
    }
  }
  
  /// 計算日出日落時間（簡化版本）
  ///
  /// [julianDay] 儒略日
  /// [latitude] 緯度
  /// [longitude] 經度
  ///
  /// 返回日出日落時間
  static Map<String, double>? calculateSunriseSunset(
    double julianDay,
    double latitude,
    double longitude,
  ) {
    if (!_isInitialized) {
      logger.w('Swiss Ephemeris 尚未初始化');
      return null;
    }

    try {
      // 簡化實現，返回預設值
      logger.d('日出日落計算（簡化版本）');

      return {
        'sunrise': julianDay + 0.25, // 6:00 AM
        'sunset': julianDay + 0.75,  // 6:00 PM
      };
    } catch (e) {
      logger.e('日出日落計算失敗: $e');
      return null;
    }
  }
  
  /// 計算月相
  /// 
  /// [julianDay] 儒略日
  /// 
  /// 返回月相角度（度）
  static double? calculateMoonPhase(double julianDay) {
    if (!_isInitialized) {
      logger.w('Swiss Ephemeris 尚未初始化');
      return null;
    }
    
    try {
      // 計算太陽位置
      final sunResult = calculatePlanetPosition(julianDay, HeavenlyBody.SE_SUN);
      if (sunResult == null) return null;
      
      // 計算月亮位置
      final moonResult = calculatePlanetPosition(julianDay, HeavenlyBody.SE_MOON);
      if (moonResult == null) return null;
      
      // 計算月相角度
      double phaseAngle = moonResult.longitude - sunResult.longitude;
      if (phaseAngle < 0) phaseAngle += 360;
      
      logger.d('月相計算成功: ${phaseAngle.toStringAsFixed(2)}°');
      
      return phaseAngle;
    } catch (e) {
      logger.e('月相計算失敗: $e');
      return null;
    }
  }
  
  /// 獲取支援的行星列表
  static List<HeavenlyBody> getSupportedPlanets() {
    return [
      HeavenlyBody.SE_SUN,
      HeavenlyBody.SE_MOON,
      HeavenlyBody.SE_MERCURY,
      HeavenlyBody.SE_VENUS,
      HeavenlyBody.SE_MARS,
      HeavenlyBody.SE_JUPITER,
      HeavenlyBody.SE_SATURN,
      HeavenlyBody.SE_URANUS,
      HeavenlyBody.SE_NEPTUNE,
      HeavenlyBody.SE_PLUTO,
      HeavenlyBody.SE_TRUE_NODE,
      HeavenlyBody.SE_CHIRON,
      HeavenlyBody.SE_CERES,
      HeavenlyBody.SE_PALLAS,
      HeavenlyBody.SE_JUNO,
      HeavenlyBody.SE_VESTA,
      HeavenlyBody.SE_MEAN_APOG, // 莉莉絲
    ];
  }
  
  /// 獲取支援的宮位系統列表
  static List<Hsys> getSupportedHouseSystems() {
    return [
      Hsys.P, // Placidus
      Hsys.K, // Koch
      Hsys.E, // Equal House
      Hsys.W, // Whole Sign
      Hsys.C, // Campanus
      Hsys.R, // Regiomontanus
      Hsys.T, // Topocentric
      Hsys.B, // Alcabitus
      Hsys.M, // Morinus
      Hsys.U, // Krusinski
      Hsys.O, // Porphyrius
      Hsys.X, // Meridian
      Hsys.H, // Azimuthal
      Hsys.D, // Horizontal
      Hsys.N, // Carter Poli Equatorial
      Hsys.G, // Pullen SD
      Hsys.I, // Pullen SR
      Hsys.S, // Sunshine
      Hsys.L, // Savard-A
      Hsys.F, // Polich/Page
      Hsys.V, // Vehlow Equal
      Hsys.A, // Axial Rotation
    ];
  }
  
  /// 清理資源
  static void dispose() {
    if (_isInitialized) {
      try {
        // Swiss Ephemeris 通常不需要顯式清理
        logger.i('Swiss Ephemeris 資源清理完成');
      } catch (e) {
        logger.w('Swiss Ephemeris 資源清理時出現警告: $e');
      }
      
      _isInitialized = false;
      _version = null;
    }
  }
}
