import 'package:sweph/sweph.dart';
import 'package:timezone/data/latest.dart' as tz;

import '../../../core/utils/logger_utils.dart';
import 'timezone_calculator.dart';

/// 儒略日轉換器
/// 
/// 提供日期時間與儒略日之間的精確轉換功能，
/// 考慮時區、夏令時等因素，確保天文計算的準確性。
class JulianDateConverter {
  static bool _timezoneInitialized = false;
  
  /// 初始化時區數據
  static Future<void> _initializeTimezone() async {
    if (!_timezoneInitialized) {
      tz.initializeTimeZones();
      _timezoneInitialized = true;
    }
  }
  
  /// 將日期時間轉換為儒略日
  /// 
  /// [dateTime] 要轉換的日期時間（本地時間）
  /// [latitude] 緯度（用於計算時區）
  /// [longitude] 經度（用於計算時區）
  /// 
  /// 返回對應的儒略日數值（UTC 時間）
  static Future<double> dateTimeToJulianDay(
    DateTime dateTime,
    double latitude,
    double longitude,
  ) async {
    logger.d('開始轉換日期時間為儒略日');
    logger.d('輸入: ${dateTime.toString()}, 緯度: $latitude, 經度: $longitude');
    
    try {
      await _initializeTimezone();
      
      // 計算時區偏移
      final offset = await TimezoneCalculator.calculateTimezoneOffset(
        latitude,
        longitude,
        dateTime,
      );
      
      logger.d('時區偏移: $offset 小時');
      
      // 轉換為 UTC 時間
      final utcDateTime = dateTime.subtract(Duration(
        hours: offset.floor(),
        minutes: ((offset - offset.floor()) * 60).round(),
      ));
      
      logger.d('UTC 時間: ${utcDateTime.toString()}');
      
      // 計算小時（包含分鐘和秒的小數部分）
      final hours = utcDateTime.hour + 
                   utcDateTime.minute / 60.0 + 
                   utcDateTime.second / 3600.0 +
                   utcDateTime.millisecond / 3600000.0;
      
      // 使用 Swiss Ephemeris 轉換為儒略日
      final julianDay = Sweph.swe_julday(
        utcDateTime.year,
        utcDateTime.month,
        utcDateTime.day,
        hours,
        CalendarType.SE_GREG_CAL,
      );
      return julianDay;
    } catch (e) {
      logger.e('轉換日期時間為儒略日時出錯: $e');
      throw Exception('轉換日期時間為儒略日時出錯: $e');
    }
  }
  
  /// 將儒略日轉換為日期時間
  /// 
  /// [julianDay] 儒略日數值
  /// [latitude] 緯度（用於計算時區）
  /// [longitude] 經度（用於計算時區）
  /// 
  /// 返回對應的本地日期時間
  static Future<DateTime> julianDayToDateTime(
    double julianDay,
    double latitude,
    double longitude,
  ) async {
    logger.d('開始轉換儒略日為日期時間');
    logger.d('輸入: $julianDay, 緯度: $latitude, 經度: $longitude');
    
    try {
      await _initializeTimezone();
      
      // 使用 Swiss Ephemeris 轉換為 UTC 日期時間
      final utcDateTime = Sweph.swe_jdut1_to_utc(
        julianDay, 
        CalendarType.SE_GREG_CAL,
      );
      
      logger.d('UTC 日期時間: $utcDateTime');
      
      // 創建 DateTime 對象
      final utcDt = DateTime.utc(
        utcDateTime.year,
        utcDateTime.month,
        utcDateTime.day,
        utcDateTime.hour,
        utcDateTime.minute,
        utcDateTime.second,
      );
      
      // 計算時區偏移
      final offset = await TimezoneCalculator.calculateTimezoneOffset(
        latitude,
        longitude,
        utcDt,
      );
      
      // 轉換為本地時間
      final localDateTime = utcDt.add(Duration(
        hours: offset.floor(),
        minutes: ((offset - offset.floor()) * 60).round(),
      ));
      
      logger.d('本地日期時間: ${localDateTime.toString()}');
      return localDateTime;
    } catch (e) {
      logger.e('轉換儒略日為日期時間時出錯: $e');
      throw Exception('轉換儒略日為日期時間時出錯: $e');
    }
  }
  
  /// 計算兩個日期之間的儒略日差值
  /// 
  /// [startDate] 開始日期
  /// [endDate] 結束日期
  /// [latitude] 緯度
  /// [longitude] 經度
  /// 
  /// 返回儒略日差值
  static Future<double> calculateJulianDayDifference(
    DateTime startDate,
    DateTime endDate,
    double latitude,
    double longitude,
  ) async {
    final startJD = await dateTimeToJulianDay(startDate, latitude, longitude);
    final endJD = await dateTimeToJulianDay(endDate, latitude, longitude);
    
    return endJD - startJD;
  }
  
  /// 在儒略日基礎上添加天數
  /// 
  /// [julianDay] 原始儒略日
  /// [days] 要添加的天數（可以是小數）
  /// 
  /// 返回新的儒略日
  static double addDaysToJulianDay(double julianDay, double days) {
    return julianDay + days;
  }
  
  /// 在儒略日基礎上添加年數
  /// 
  /// [julianDay] 原始儒略日
  /// [years] 要添加的年數（可以是小數）
  /// [latitude] 緯度
  /// [longitude] 經度
  /// 
  /// 返回新的儒略日
  static Future<double> addYearsToJulianDay(
    double julianDay,
    double years,
    double latitude,
    double longitude,
  ) async {
    // 轉換為日期時間
    final originalDate = await julianDayToDateTime(julianDay, latitude, longitude);
    
    // 添加年數
    final newDate = DateTime(
      originalDate.year + years.floor(),
      originalDate.month,
      originalDate.day,
      originalDate.hour,
      originalDate.minute,
      originalDate.second,
      originalDate.millisecond,
    );
    
    // 處理小數年份（轉換為天數）
    final fractionalYears = years - years.floor();
    final daysInYear = _isLeapYear(newDate.year) ? 366 : 365;
    final additionalDays = fractionalYears * daysInYear;
    
    final finalDate = newDate.add(Duration(
      days: additionalDays.floor(),
      hours: ((additionalDays - additionalDays.floor()) * 24).floor(),
    ));
    
    // 轉換回儒略日
    return await dateTimeToJulianDay(finalDate, latitude, longitude);
  }
  
  /// 檢查是否為閏年
  static bool _isLeapYear(int year) {
    return (year % 4 == 0 && year % 100 != 0) || (year % 400 == 0);
  }
  
  /// 獲取當前時間的儒略日
  /// 
  /// [latitude] 緯度
  /// [longitude] 經度
  /// 
  /// 返回當前時間的儒略日
  static Future<double> getCurrentJulianDay(
    double latitude,
    double longitude,
  ) async {
    return await dateTimeToJulianDay(DateTime.now(), latitude, longitude);
  }
  
  /// 計算儒略日對應的星期幾
  /// 
  /// [julianDay] 儒略日
  /// 
  /// 返回星期幾（1=星期一, 7=星期日）
  static int getWeekdayFromJulianDay(double julianDay) {
    // 儒略日 0.5 對應公元前 4713 年 1 月 1 日星期一
    // 所以 (julianDay + 1.5) % 7 可以得到星期幾
    final weekday = ((julianDay + 1.5) % 7).floor() + 1;
    return weekday == 0 ? 7 : weekday;
  }
  
  /// 驗證儒略日的有效性
  /// 
  /// [julianDay] 要驗證的儒略日
  /// 
  /// 返回是否有效
  static bool isValidJulianDay(double julianDay) {
    // 儒略日的有效範圍大約是 -1930000 到 2930000
    // 對應大約公元前 5000 年到公元 5000 年
    return julianDay >= -1930000 && julianDay <= 2930000;
  }
  
  /// 格式化儒略日為可讀字符串
  /// 
  /// [julianDay] 儒略日
  /// [precision] 小數位數
  /// 
  /// 返回格式化的字符串
  static String formatJulianDay(double julianDay, {int precision = 6}) {
    return julianDay.toStringAsFixed(precision);
  }
}
