/// 行星尊貴力量分析器
/// 
/// 分析行星在不同星座和宮位中的尊貴力量，包括：
/// - 廟旺（Exaltation）
/// - 入廟（Domicile/Rulership）
/// - 失勢（Detriment）
/// - 落陷（Fall）
class DignityAnalyzer {
  /// 計算行星的尊貴力量分數
  /// 
  /// [planetName] 行星名稱
  /// [signName] 所在星座名稱
  /// [houseNumber] 所在宮位號碼
  /// 
  /// 返回尊貴力量分數（-5 到 +5）
  static int calculateDignityScore(
    String planetName,
    String signName,
    int houseNumber,
  ) {
    int score = 0;
    
    // 星座尊貴力量
    score += _getSignDignityScore(planetName, signName);
    
    // 宮位尊貴力量
    score += _getHouseDignityScore(planetName, houseNumber);
    
    return score;
  }
  
  /// 獲取行星在星座中的尊貴力量分數
  static int _getSignDignityScore(String planetName, String signName) {
    // 行星在星座中的尊貴力量定義
    const dignityMap = {
      '太陽': {
        '獅子座': 5,  // 入廟
        '牡羊座': 4,  // 廟旺
        '水瓶座': -4, // 失勢
        '天秤座': -5, // 落陷
      },
      '月亮': {
        '巨蟹座': 5,  // 入廟
        '金牛座': 4,  // 廟旺
        '摩羯座': -4, // 失勢
        '天蠍座': -5, // 落陷
      },
      '水星': {
        '雙子座': 5,  // 入廟
        '處女座': 5,  // 入廟
        '水瓶座': 4,  // 廟旺
        '射手座': -4, // 失勢
        '雙魚座': -4, // 失勢
        '獅子座': -5, // 落陷
      },
      '金星': {
        '金牛座': 5,  // 入廟
        '天秤座': 5,  // 入廟
        '雙魚座': 4,  // 廟旺
        '牡羊座': -4, // 失勢
        '天蠍座': -4, // 失勢
        '處女座': -5, // 落陷
      },
      '火星': {
        '牡羊座': 5,  // 入廟
        '天蠍座': 5,  // 入廟
        '摩羯座': 4,  // 廟旺
        '金牛座': -4, // 失勢
        '天秤座': -4, // 失勢
        '巨蟹座': -5, // 落陷
      },
      '木星': {
        '射手座': 5,  // 入廟
        '雙魚座': 5,  // 入廟
        '巨蟹座': 4,  // 廟旺
        '雙子座': -4, // 失勢
        '處女座': -4, // 失勢
        '摩羯座': -5, // 落陷
      },
      '土星': {
        '摩羯座': 5,  // 入廟
        '水瓶座': 5,  // 入廟
        '天秤座': 4,  // 廟旺
        '巨蟹座': -4, // 失勢
        '獅子座': -4, // 失勢
        '牡羊座': -5, // 落陷
      },
    };
    
    return dignityMap[planetName]?[signName] ?? 0;
  }
  
  /// 獲取行星在宮位中的尊貴力量分數
  static int _getHouseDignityScore(String planetName, int houseNumber) {
    // 行星在宮位中的喜好程度
    const housePreferences = {
      '太陽': {1: 3, 5: 2, 9: 2, 10: 3, 11: 1},
      '月亮': {1: 2, 4: 3, 7: 1, 10: 2},
      '水星': {1: 2, 3: 3, 6: 2, 10: 2},
      '金星': {2: 2, 5: 3, 7: 3, 11: 2},
      '火星': {1: 3, 6: 2, 8: 2, 10: 2},
      '木星': {1: 2, 5: 2, 9: 3, 11: 3},
      '土星': {6: 2, 8: 1, 10: 3, 12: 1},
    };
    
    return housePreferences[planetName]?[houseNumber] ?? 0;
  }
  
  /// 獲取尊貴力量的文字描述
  static String getDignityDescription(int score) {
    if (score >= 4) {
      return '極強';
    } else if (score >= 2) {
      return '強';
    } else if (score >= 1) {
      return '中等偏強';
    } else if (score == 0) {
      return '中性';
    } else if (score >= -1) {
      return '中等偏弱';
    } else if (score >= -3) {
      return '弱';
    } else {
      return '極弱';
    }
  }
}
