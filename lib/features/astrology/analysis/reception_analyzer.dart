/// 互容接納分析器
/// 
/// 分析行星間的互容接納關係，包括：
/// - 互容（Mutual Reception）
/// - 接納（Reception）
/// - 混合接納（Mixed Reception）
class ReceptionAnalyzer {
  /// 分析兩個行星間的互容接納關係
  /// 
  /// [planet1Name] 第一個行星名稱
  /// [planet1Sign] 第一個行星所在星座
  /// [planet2Name] 第二個行星名稱
  /// [planet2Sign] 第二個行星所在星座
  /// 
  /// 返回互容接納類型和描述
  static Map<String, dynamic> analyzeReception(
    String planet1Name,
    String planet2Name,
    String planet1Sign,
    String planet2Sign,
  ) {
    // 行星守護星座對應表
    const rulerships = {
      '太陽': ['獅子座'],
      '月亮': ['巨蟹座'],
      '水星': ['雙子座', '處女座'],
      '金星': ['金牛座', '天秤座'],
      '火星': ['牡羊座', '天蠍座'],
      '木星': ['射手座', '雙魚座'],
      '土星': ['摩羯座', '水瓶座'],
      '天王星': ['水瓶座'],
      '海王星': ['雙魚座'],
      '冥王星': ['天蠍座'],
    };
    
    // 檢查第一個行星是否守護第二個行星所在的星座
    final planet1Rules = rulerships[planet1Name] ?? [];
    final planet1RulesPlanet2Sign = planet1Rules.contains(planet2Sign);
    
    // 檢查第二個行星是否守護第一個行星所在的星座
    final planet2Rules = rulerships[planet2Name] ?? [];
    final planet2RulesPlanet1Sign = planet2Rules.contains(planet1Sign);
    
    if (planet1RulesPlanet2Sign && planet2RulesPlanet1Sign) {
      return {
        'type': 'mutual_reception',
        'name': '互容',
        'description': '$planet1Name 和 $planet2Name 形成互容關係',
        'strength': 'strong',
      };
    } else if (planet1RulesPlanet2Sign) {
      return {
        'type': 'reception',
        'name': '接納',
        'description': '$planet1Name 接納 $planet2Name',
        'strength': 'moderate',
      };
    } else if (planet2RulesPlanet1Sign) {
      return {
        'type': 'reception',
        'name': '接納',
        'description': '$planet2Name 接納 $planet1Name',
        'strength': 'moderate',
      };
    } else {
      return {
        'type': 'none',
        'name': '無',
        'description': '無互容接納關係',
        'strength': 'none',
      };
    }
  }
  
  /// 獲取行星的守護星座
  static List<String> getRuledSigns(String planetName) {
    const rulerships = {
      '太陽': ['獅子座'],
      '月亮': ['巨蟹座'],
      '水星': ['雙子座', '處女座'],
      '金星': ['金牛座', '天秤座'],
      '火星': ['牡羊座', '天蠍座'],
      '木星': ['射手座', '雙魚座'],
      '土星': ['摩羯座', '水瓶座'],
      '天王星': ['水瓶座'],
      '海王星': ['雙魚座'],
      '冥王星': ['天蠍座'],
    };
    
    return rulerships[planetName] ?? [];
  }
}
