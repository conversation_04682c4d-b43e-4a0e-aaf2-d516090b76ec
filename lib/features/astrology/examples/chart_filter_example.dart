import 'package:flutter/material.dart';

import '../../../data/models/astrology/chart_data.dart';
import '../../../presentation/pages/chart_filter_page.dart';
import '../models/chart_filter.dart';
import '../services/chart_filter_service.dart';

/// 星盤篩選器使用範例
/// 
/// 展示如何在實際應用中整合和使用多重篩選器功能
class ChartFilterExample {
  
  /// 範例1：在出生資料列表頁面添加篩選功能
  static Widget buildFilterButton(BuildContext context, List<ChartData> charts) {
    return FloatingActionButton(
      onPressed: () => _openFilterPage(context, charts),
      backgroundColor: Colors.blue,
      child: const Icon(Icons.filter_list, color: Colors.white),
      tooltip: '篩選星盤',
    );
  }

  /// 開啟篩選器頁面
  static void _openFilterPage(BuildContext context, List<ChartData> charts) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ChartFilterPage(
          initialCharts: charts,
        ),
      ),
    );
  }

  /// 範例2：創建快速篩選按鈕組
  static Widget buildQuickFilterButtons(
    BuildContext context,
    List<ChartData> charts,
    ValueChanged<List<ChartData>> onFiltered,
  ) {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: [
        _buildQuickFilterChip(
          '火象太陽',
          () => _applyQuickFilter(charts, _createFireSunFilter(), onFiltered),
        ),
        _buildQuickFilterChip(
          '水象月亮',
          () => _applyQuickFilter(charts, _createWaterMoonFilter(), onFiltered),
        ),
        _buildQuickFilterChip(
          '風象水星',
          () => _applyQuickFilter(charts, _createAirMercuryFilter(), onFiltered),
        ),
        _buildQuickFilterChip(
          '土象金星',
          () => _applyQuickFilter(charts, _createEarthVenusFilter(), onFiltered),
        ),
        _buildQuickFilterChip(
          '清除篩選',
          () => onFiltered(charts),
          color: Colors.grey,
        ),
      ],
    );
  }

  static Widget _buildQuickFilterChip(
    String label,
    VoidCallback onPressed, {
    Color color = Colors.blue,
  }) {
    return ActionChip(
      label: Text(label, style: const TextStyle(fontSize: 12)),
      onPressed: onPressed,
      backgroundColor: color.withOpacity(0.1),
      side: BorderSide(color: color),
    );
  }

  static void _applyQuickFilter(
    List<ChartData> charts,
    ChartFilter filter,
    ValueChanged<List<ChartData>> onFiltered,
  ) {
    final filteredCharts = ChartFilterService.applyFilter(charts, filter);
    onFiltered(filteredCharts);
  }

  /// 範例3：創建預設篩選器

  /// 火象太陽篩選器
  static ChartFilter _createFireSunFilter() {
    return ChartFilter(
      id: 'fire_sun',
      name: '火象太陽',
      groups: [
        FilterGroup(
          id: 'group1',
          conditions: [
            FilterCondition(
              id: 'cond1',
              type: FilterType.planetInSign,
              operator: FilterOperator.contains,
              planetName: '太陽',
              signNames: ChartFilterService.getSignsByElement('火'),
            ),
          ],
        ),
      ],
    );
  }

  /// 水象月亮篩選器
  static ChartFilter _createWaterMoonFilter() {
    return ChartFilter(
      id: 'water_moon',
      name: '水象月亮',
      groups: [
        FilterGroup(
          id: 'group1',
          conditions: [
            FilterCondition(
              id: 'cond1',
              type: FilterType.planetInSign,
              operator: FilterOperator.contains,
              planetName: '月亮',
              signNames: ChartFilterService.getSignsByElement('水'),
            ),
          ],
        ),
      ],
    );
  }

  /// 風象水星篩選器
  static ChartFilter _createAirMercuryFilter() {
    return ChartFilter(
      id: 'air_mercury',
      name: '風象水星',
      groups: [
        FilterGroup(
          id: 'group1',
          conditions: [
            FilterCondition(
              id: 'cond1',
              type: FilterType.planetInSign,
              operator: FilterOperator.contains,
              planetName: '水星',
              signNames: ChartFilterService.getSignsByElement('風'),
            ),
          ],
        ),
      ],
    );
  }

  /// 土象金星篩選器
  static ChartFilter _createEarthVenusFilter() {
    return ChartFilter(
      id: 'earth_venus',
      name: '土象金星',
      groups: [
        FilterGroup(
          id: 'group1',
          conditions: [
            FilterCondition(
              id: 'cond1',
              type: FilterType.planetInSign,
              operator: FilterOperator.contains,
              planetName: '金星',
              signNames: ChartFilterService.getSignsByElement('土'),
            ),
          ],
        ),
      ],
    );
  }

  /// 範例4：複雜篩選器範例

  /// 創建事業導向篩選器
  static ChartFilter createCareerOrientedFilter() {
    return ChartFilter(
      id: 'career_oriented',
      name: '事業導向配置',
      groups: [
        // 組1：太陽在摩羯座或第十宮
        FilterGroup(
          id: 'group1',
          conditions: [
            FilterCondition(
              id: 'cond1',
              type: FilterType.planetInSign,
              operator: FilterOperator.equals,
              planetName: '太陽',
              signName: '摩羯座',
            ),
            FilterCondition(
              id: 'cond2',
              type: FilterType.planetInHouse,
              operator: FilterOperator.equals,
              planetName: '太陽',
              houseNumber: 10,
            ),
          ],
          logicalOperator: LogicalOperator.or,
        ),
        // 組2：第十宮在基本星座
        FilterGroup(
          id: 'group2',
          conditions: [
            FilterCondition(
              id: 'cond3',
              type: FilterType.houseInSign,
              operator: FilterOperator.contains,
              houseNumber: 10,
              signNames: ChartFilterService.getSignsByQuality('基本'),
            ),
          ],
        ),
      ],
      groupLogicalOperator: LogicalOperator.and,
    );
  }

  /// 創建溝通天賦篩選器
  static ChartFilter createCommunicationTalentFilter() {
    return ChartFilter(
      id: 'communication_talent',
      name: '溝通天賦配置',
      groups: [
        FilterGroup(
          id: 'group1',
          conditions: [
            // 水星在風象星座
            FilterCondition(
              id: 'cond1',
              type: FilterType.planetInSign,
              operator: FilterOperator.contains,
              planetName: '水星',
              signNames: ChartFilterService.getSignsByElement('風'),
            ),
            // 水星在第3或第9宮
            FilterCondition(
              id: 'cond2',
              type: FilterType.planetInHouse,
              operator: FilterOperator.contains,
              planetName: '水星',
              houseNumbers: [3, 9],
            ),
          ],
          logicalOperator: LogicalOperator.and,
        ),
      ],
    );
  }

  /// 創建藝術天賦篩選器
  static ChartFilter createArtisticTalentFilter() {
    return ChartFilter(
      id: 'artistic_talent',
      name: '藝術天賦配置',
      groups: [
        FilterGroup(
          id: 'group1',
          conditions: [
            // 金星在第5宮或第12宮
            FilterCondition(
              id: 'cond1',
              type: FilterType.planetInHouse,
              operator: FilterOperator.contains,
              planetName: '金星',
              houseNumbers: [5, 12],
            ),
            // 海王星在第1、5或12宮
            FilterCondition(
              id: 'cond2',
              type: FilterType.planetInHouse,
              operator: FilterOperator.contains,
              planetName: '海王星',
              houseNumbers: [1, 5, 12],
            ),
          ],
          logicalOperator: LogicalOperator.or,
        ),
      ],
    );
  }

  /// 範例5：篩選器組合使用

  /// 應用多個篩選器並取交集
  static List<ChartData> applyMultipleFiltersIntersection(
    List<ChartData> charts,
    List<ChartFilter> filters,
  ) {
    List<ChartData> result = charts;
    
    for (final filter in filters) {
      result = ChartFilterService.applyFilter(result, filter);
    }
    
    return result;
  }

  /// 應用多個篩選器並取聯集
  static List<ChartData> applyMultipleFiltersUnion(
    List<ChartData> charts,
    List<ChartFilter> filters,
  ) {
    final Set<ChartData> resultSet = {};
    
    for (final filter in filters) {
      final filtered = ChartFilterService.applyFilter(charts, filter);
      resultSet.addAll(filtered);
    }
    
    return resultSet.toList();
  }

  /// 範例6：篩選統計分析

  /// 獲取篩選統計資訊
  static Map<String, dynamic> getFilterStatistics(
    List<ChartData> originalCharts,
    List<ChartData> filteredCharts,
    ChartFilter filter,
  ) {
    final filterRate = originalCharts.isEmpty 
        ? 0.0 
        : filteredCharts.length / originalCharts.length;

    return {
      'filterName': filter.name,
      'originalCount': originalCharts.length,
      'filteredCount': filteredCharts.length,
      'filterRate': filterRate,
      'filterRatePercentage': '${(filterRate * 100).toStringAsFixed(1)}%',
      'groupsCount': filter.groups.length,
      'conditionsCount': filter.groups
          .map((g) => g.conditions.length)
          .fold(0, (sum, count) => sum + count),
      'filterDescription': filter.getDescription(),
    };
  }

  /// 比較多個篩選器的效果
  static List<Map<String, dynamic>> compareFilters(
    List<ChartData> charts,
    List<ChartFilter> filters,
  ) {
    return filters.map((filter) {
      final filtered = ChartFilterService.applyFilter(charts, filter);
      return getFilterStatistics(charts, filtered, filter);
    }).toList();
  }

  /// 範例7：篩選器匯出和匯入

  /// 匯出篩選器配置
  static Map<String, dynamic> exportFilterConfiguration(ChartFilter filter) {
    return {
      'version': '1.0',
      'exportTime': DateTime.now().toIso8601String(),
      'filter': filter.toJson(),
    };
  }

  /// 匯入篩選器配置
  static ChartFilter? importFilterConfiguration(Map<String, dynamic> config) {
    try {
      if (config['version'] == '1.0' && config['filter'] != null) {
        return ChartFilter.fromJson(config['filter'] as Map<String, dynamic>);
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// 範例8：自訂篩選器建構器

  /// 建構器模式創建篩選器
  static ChartFilterBuilder builder(String name) {
    return ChartFilterBuilder(name);
  }
}

/// 篩選器建構器
class ChartFilterBuilder {
  final String _name;
  final List<FilterGroup> _groups = [];
  LogicalOperator _groupLogicalOperator = LogicalOperator.and;

  ChartFilterBuilder(this._name);

  /// 設定組間邏輯操作符
  ChartFilterBuilder groupLogic(LogicalOperator operator) {
    _groupLogicalOperator = operator;
    return this;
  }

  /// 添加篩選組
  ChartFilterBuilder addGroup(FilterGroup group) {
    _groups.add(group);
    return this;
  }

  /// 添加行星在星座條件
  ChartFilterBuilder planetInSign(
    String planetName,
    String signName, {
    FilterOperator operator = FilterOperator.equals,
  }) {
    final condition = FilterCondition(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      type: FilterType.planetInSign,
      operator: operator,
      planetName: planetName,
      signName: signName,
    );

    final group = FilterGroup(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      conditions: [condition],
    );

    return addGroup(group);
  }

  /// 添加行星在宮位條件
  ChartFilterBuilder planetInHouse(
    String planetName,
    int houseNumber, {
    FilterOperator operator = FilterOperator.equals,
  }) {
    final condition = FilterCondition(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      type: FilterType.planetInHouse,
      operator: operator,
      planetName: planetName,
      houseNumber: houseNumber,
    );

    final group = FilterGroup(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      conditions: [condition],
    );

    return addGroup(group);
  }

  /// 建構篩選器
  ChartFilter build() {
    return ChartFilter(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: _name,
      groups: _groups,
      groupLogicalOperator: _groupLogicalOperator,
    );
  }
}

/// 使用範例
void exampleUsage() {
  // 使用建構器創建篩選器
  final filter = ChartFilterExample.builder('火象太陽土象月亮')
      .planetInSign('太陽', '牡羊座')
      .planetInSign('月亮', '金牛座')
      .groupLogic(LogicalOperator.and)
      .build();

  // 使用預設篩選器
  final careerFilter = ChartFilterExample.createCareerOrientedFilter();
  
  // 篩選器比較
  // final charts = getCharts(); // 假設有獲取星盤的方法
  // final comparison = ChartFilterExample.compareFilters(charts, [filter, careerFilter]);
}
