import 'package:sweph/sweph.dart';

import '../../core/utils/logger_utils.dart';
import '../../data/models/astrology/aspect_info.dart';
import '../../data/models/astrology/chart_data.dart';
import '../../data/models/astrology/planet_position.dart';
import '../../data/models/user/birth_data.dart';
import 'calculations/aspect_calculator.dart';
import 'calculations/house_calculator.dart';
// 計算模組
import 'calculations/planet_calculator.dart';
// 星曆表模組
import 'ephemeris/julian_date_converter.dart';
import 'ephemeris/timezone_calculator.dart';
// 工具模組
import 'utilities/angle_calculator.dart';
import 'utilities/zodiac_calculator.dart';

/// 占星服務主類
/// 
/// 整合所有占星計算功能，提供統一的 API 介面
/// 包括行星位置計算、宮位計算、相位分析等核心功能
class AstrologyService {
  static final AstrologyService _instance = AstrologyService._internal();
  factory AstrologyService() => _instance;
  AstrologyService._internal();

  /// 計算完整的星盤數據
  /// 
  /// [chartData] 星盤基本數據
  /// [planetVisibility] 行星可見性設定
  /// [aspectOrbs] 自定義相位容許度
  /// [houseSystem] 宮位系統
  /// [includeMinorAspects] 是否包含次要相位
  /// 
  /// 返回計算完成的星盤數據
  static Future<ChartData> calculateChartData(
    ChartData chartData, {
    Map<String, bool>? planetVisibility,
    Map<String, double>? aspectOrbs,
    String houseSystem = 'Placidus',
    bool includeMinorAspects = false,
  }) async {
    logger.i('開始計算星盤數據');
    
    try {
      final birthData = chartData.primaryPerson;
      
      // 1. 計算宮位數據
      logger.d('計算宮位數據');
      final housesData = await HouseCalculator.calculateHouses(
        dateTime: birthData.dateTime,
        latitude: birthData.latitude,
        longitude: birthData.longitude,
        houseSystem: houseSystem,
      );
      
      if (housesData == null) {
        throw Exception('宮位計算失敗');
      }
      
      // 2. 計算行星位置
      logger.d('計算行星位置');
      final planets = await PlanetCalculator.calculateAllPlanets(
        dateTime: birthData.dateTime,
        latitude: birthData.latitude,
        longitude: birthData.longitude,
        housesData: housesData,
        planetVisibility: planetVisibility,
      );
      
      // 3. 計算特殊點位
      logger.d('計算特殊點位');
      final specialPoints = PlanetCalculator.calculateSpecialPoints(housesData);
      final allPlanets = [...planets, ...specialPoints];
      
      // 4. 計算相位
      logger.d('計算相位');
      final aspects = AspectCalculator.calculateAllAspects(
        allPlanets,
        aspectOrbs: aspectOrbs,
        includeMinorAspects: includeMinorAspects,
      );
      
      // 5. 更新星盤數據
      final updatedChartData = chartData.copyWith(
        planets: allPlanets,
        aspects: aspects,
        houses: housesData,
      );
      
      logger.i('星盤數據計算完成');
      return updatedChartData;
      
    } catch (e) {
      logger.e('計算星盤數據時出錯: $e');
      throw Exception('計算星盤數據時出錯: $e');
    }
  }
  
  /// 計算行星位置
  /// 
  /// [dateTime] 計算時間
  /// [latitude] 緯度
  /// [longitude] 經度
  /// [planetVisibility] 行星可見性設定
  /// 
  /// 返回行星位置列表
  static Future<List<PlanetPosition>> calculatePlanetPositions(
    DateTime dateTime,
    double latitude,
    double longitude, {
    Map<String, bool>? planetVisibility,
  }) async {
    logger.d('計算行星位置');
    
    return await PlanetCalculator.calculateAllPlanets(
      dateTime: dateTime,
      latitude: latitude,
      longitude: longitude,
      planetVisibility: planetVisibility,
    );
  }
  
  /// 計算宮位數據
  /// 
  /// [dateTime] 計算時間
  /// [latitude] 緯度
  /// [longitude] 經度
  /// [houseSystem] 宮位系統
  /// 
  /// 返回宮位數據
  static Future<HouseCuspData?> calculateHouses(
    DateTime dateTime,
    double latitude,
    double longitude, {
    String houseSystem = 'Placidus',
  }) async {
    logger.d('計算宮位數據');
    
    return await HouseCalculator.calculateHouses(
      dateTime: dateTime,
      latitude: latitude,
      longitude: longitude,
      houseSystem: houseSystem,
    );
  }
  
  /// 計算相位
  /// 
  /// [planets] 行星位置列表
  /// [aspectOrbs] 自定義相位容許度
  /// [includeMinorAspects] 是否包含次要相位
  /// 
  /// 返回相位列表
  static List<AspectInfo> calculateAspects(
    List<PlanetPosition> planets, {
    Map<String, double>? aspectOrbs,
    bool includeMinorAspects = false,
  }) {
    logger.d('計算相位');
    
    return AspectCalculator.calculateAllAspects(
      planets,
      aspectOrbs: aspectOrbs,
      includeMinorAspects: includeMinorAspects,
    );
  }
  
  /// 計算時區偏移
  /// 
  /// [latitude] 緯度
  /// [longitude] 經度
  /// [dateTime] 日期時間
  /// 
  /// 返回時區偏移小時數
  static Future<double> calculateTimezoneOffset(
    double latitude,
    double longitude,
    DateTime dateTime,
  ) async {
    return await TimezoneCalculator.calculateTimezoneOffset(
      latitude,
      longitude,
      dateTime,
    );
  }
  
  /// 轉換日期時間為儒略日
  /// 
  /// [dateTime] 日期時間
  /// [latitude] 緯度
  /// [longitude] 經度
  /// 
  /// 返回儒略日
  static Future<double> dateTimeToJulianDay(
    DateTime dateTime,
    double latitude,
    double longitude,
  ) async {
    return await JulianDateConverter.dateTimeToJulianDay(
      dateTime,
      latitude,
      longitude,
    );
  }
  
  /// 轉換儒略日為日期時間
  /// 
  /// [julianDay] 儒略日
  /// [latitude] 緯度
  /// [longitude] 經度
  /// 
  /// 返回日期時間
  static Future<DateTime> julianDayToDateTime(
    double julianDay,
    double latitude,
    double longitude,
  ) async {
    return await JulianDateConverter.julianDayToDateTime(
      julianDay,
      latitude,
      longitude,
    );
  }
  
  /// 計算角度差
  /// 
  /// [angle1] 第一個角度
  /// [angle2] 第二個角度
  /// 
  /// 返回角度差（0-180度）
  static double calculateAngleDifference(double angle1, double angle2) {
    return AngleCalculator.calculateAngleDifference(angle1, angle2);
  }
  
  /// 標準化角度
  /// 
  /// [angle] 角度
  /// 
  /// 返回標準化後的角度（0-360度）
  static double normalizeAngle(double angle) {
    return AngleCalculator.normalizeAngle(angle);
  }
  
  /// 獲取星座信息
  /// 
  /// [longitude] 黃經度數
  /// 
  /// 返回星座信息
  static Map<String, dynamic> getZodiacSign(double longitude) {
    return ZodiacCalculator.getZodiacSign(longitude);
  }
  
  /// 驗證出生數據
  /// 
  /// [birthData] 出生數據
  /// 
  /// 返回驗證結果
  static bool validateBirthData(BirthData birthData) {
    // 檢查日期範圍
    final now = DateTime.now();
    final minDate = DateTime(1900);
    final maxDate = now.add(const Duration(days: 365));
    
    if (birthData.dateTime.isBefore(minDate) || 
        birthData.dateTime.isAfter(maxDate)) {
      return false;
    }
    
    // 檢查緯度範圍
    if (birthData.latitude < -90 || birthData.latitude > 90) {
      return false;
    }
    
    // 檢查經度範圍
    if (birthData.longitude < -180 || birthData.longitude > 180) {
      return false;
    }
    
    return true;
  }
  
  /// 獲取支援的宮位系統列表
  static List<String> getSupportedHouseSystems() {
    return HouseCalculator.houseSystems.keys.toList();
  }
  
  /// 獲取可用的時區列表
  static List<String> getAvailableTimezones() {
    return TimezoneCalculator.getAvailableTimezones();
  }
  
  /// 根據國家獲取時區
  /// 
  /// [countryCode] 國家代碼
  /// 
  /// 返回該國家的時區列表
  static List<String> getTimezonesByCountry(String countryCode) {
    return TimezoneCalculator.getTimezonesByCountry(countryCode);
  }
}
