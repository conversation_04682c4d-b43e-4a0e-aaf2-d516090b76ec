// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyBCTw5KZQC10-xYmciTb6VLdYkCkrfH6m0',
    appId: '1:470077449550:web:18e235ee5adb571396aa1f',
    messagingSenderId: '470077449550',
    projectId: 'astreal-d3f70',
    authDomain: 'astreal-d3f70.firebaseapp.com',
    databaseURL: 'https://astreal-d3f70-default-rtdb.asia-southeast1.firebasedatabase.app',
    storageBucket: 'astreal-d3f70.firebasestorage.app',
    measurementId: 'G-WC18X2QD4Z',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyAeCMo-vuea1Z6nS1_EwOygN8TOY3ncmMc',
    appId: '1:470077449550:android:4971c9e15686127296aa1f',
    messagingSenderId: '470077449550',
    projectId: 'astreal-d3f70',
    databaseURL: 'https://astreal-d3f70-default-rtdb.asia-southeast1.firebasedatabase.app',
    storageBucket: 'astreal-d3f70.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyBD-jSb1JUoJExk8x-f63Z-2iKmxXS8UB8',
    appId: '1:470077449550:ios:dcbc192966cde49096aa1f',
    messagingSenderId: '470077449550',
    projectId: 'astreal-d3f70',
    databaseURL: 'https://astreal-d3f70-default-rtdb.asia-southeast1.firebasedatabase.app',
    storageBucket: 'astreal-d3f70.firebasestorage.app',
    androidClientId: '470077449550-t115dcdl5leqafgfblel0smmsaj990g8.apps.googleusercontent.com',
    iosClientId: '470077449550-fcp6o33e5vrofn70q5efdi0daobp0hlc.apps.googleusercontent.com',
    iosBundleId: 'com.one.astreal',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyBD-jSb1JUoJExk8x-f63Z-2iKmxXS8UB8',
    appId: '1:470077449550:ios:5de969696d97c30b96aa1f',
    messagingSenderId: '470077449550',
    projectId: 'astreal-d3f70',
    databaseURL: 'https://astreal-d3f70-default-rtdb.asia-southeast1.firebasedatabase.app',
    storageBucket: 'astreal-d3f70.firebasestorage.app',
    androidClientId: '470077449550-t115dcdl5leqafgfblel0smmsaj990g8.apps.googleusercontent.com',
    iosClientId: '470077449550-1phpb4dacdtludhrf6v9j6u085resde9.apps.googleusercontent.com',
    iosBundleId: 'com.one.astreal.macos',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyBCTw5KZQC10-xYmciTb6VLdYkCkrfH6m0',
    appId: '1:470077449550:web:bd0469a89cc9b70896aa1f',
    messagingSenderId: '470077449550',
    projectId: 'astreal-d3f70',
    authDomain: 'astreal-d3f70.firebaseapp.com',
    databaseURL: 'https://astreal-d3f70-default-rtdb.asia-southeast1.firebasedatabase.app',
    storageBucket: 'astreal-d3f70.firebasestorage.app',
    measurementId: 'G-F63MRK2VJ1',
  );

}