/// AstReal - 應用核心庫
/// 
/// 這個文件提供了應用的主要導出，方便其他模組導入所需的類和函數。
library astreal;

export 'core/constants/ascension_table.dart';
// 核心模組
export 'core/constants/astrology_constants.dart';
export 'core/constants/zodiac_symbols.dart';
export 'core/utils/date_formatter.dart';
export 'core/utils/id_generator.dart';
export 'core/utils/logger_utils.dart';
export 'data/models/astrology/aspect_info.dart';
export 'data/models/astrology/astro_event.dart';
export 'data/models/astrology/astrology_mode_config.dart';
export 'data/models/astrology/chart_calculation_params.dart';
export 'data/models/astrology/chart_category.dart';
export 'data/models/astrology/chart_data.dart';
export 'data/models/astrology/chart_settings.dart';
export 'data/models/astrology/chart_type.dart';
export 'data/models/astrology/firdaria_data.dart';
// 數據模型 - 占星
export 'data/models/astrology/planet_position.dart';
export 'data/models/astrology/planetary_conjunction.dart';
export 'data/models/astrology/profection_data.dart';
export 'data/models/astrology/zodiac_sign.dart';
export 'data/models/interpretation/ai_model.dart';
export 'data/models/interpretation/ai_usage_stats.dart';
export 'data/models/interpretation/analysis_category.dart';
export 'data/models/interpretation/celebrity_example.dart';
export 'data/models/interpretation/chart_interpretation_model.dart';
export 'data/models/interpretation/chart_theme_analysis_result.dart';
export 'data/models/interpretation/consultation_analysis_result.dart';
export 'data/models/interpretation/consultation_analysis_theme.dart';
export 'data/models/interpretation/daily_fortune.dart';
export 'data/models/interpretation/divination_record.dart';
export 'data/models/interpretation/financial_analysis.dart';
// 數據模型 - 解讀
export 'data/models/interpretation/interpretation_record.dart';
export 'data/models/interpretation/term_ruler_progression_result.dart';
export 'data/models/interpretation/term_ruler_timeline_result.dart';
export 'data/models/interpretation/yijing_hexagram.dart';
export 'data/models/payment/booking_model.dart';
// 數據模型 - 支付
export 'data/models/payment/payment_record.dart';
// 數據模型 - 用戶
export 'data/models/user/app_user.dart';
export 'data/models/user/auth_state.dart';
export 'data/models/user/birth_data.dart';
export 'data/models/user/feedback_model.dart';
export 'data/models/user/gender.dart';
export 'data/models/user/learning_progress.dart';
export 'data/models/user/recent_chart_record.dart';
export 'data/models/user/recent_person_record.dart';
export 'data/models/user/user_profile.dart';
export 'data/repositories/user_profile_credits_repository.dart';
export 'data/repositories/user_profile_repository.dart';
// 統一服務與資料庫存取層
export 'data/services/user_profile_unified_service.dart';
// 功能模組
// export 'features/astrology/astrology.dart';
// export 'features/chart/chart_display_module.dart';

// 表現層
export 'presentation/themes/app_theme.dart';
export 'presentation/themes/theme_provider.dart';
export 'presentation/viewmodels/auth_viewmodel.dart';
// ViewModels
export 'presentation/viewmodels/chart_viewmodel.dart';
export 'presentation/viewmodels/home_viewmodel.dart';
export 'presentation/viewmodels/settings_viewmodel.dart';
export 'presentation/widgets/chart/aspect_table_widget.dart';
// 共享組件
export 'presentation/widgets/chart/chart_view_widget.dart';
export 'presentation/widgets/chart/firdaria_widget.dart';
export 'presentation/widgets/chart/houses_widget.dart';
export 'presentation/widgets/chart/planet_list_widget.dart';
export 'presentation/widgets/common/empty_state.dart';
export 'presentation/widgets/common/loading_state.dart';
export 'presentation/widgets/common/page_title.dart';
export 'presentation/widgets/common/styled_card.dart';
// 共享工具
export 'shared/utils/copy_options.dart';
