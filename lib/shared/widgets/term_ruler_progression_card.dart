import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import '../../astreal.dart';
import '../../features/astrology/calculations/aspect_calculator.dart';
import '../../features/astrology/constants/aspect_definitions.dart';
import '../../presentation/pages/term_ruler_stock_analysis_page.dart';

/// 界主星配置法卡片組件
class TermRulerProgressionCard extends StatefulWidget {
  final TermRulerTimelineResult result;
  final ChartViewModel viewModel;
  final VoidCallback onClose;
  final Function(TermRulerTimelineItem)? onNavigateToAnalysis;

  const TermRulerProgressionCard({
    super.key,
    required this.result,
    required this.viewModel,
    required this.onClose,
    this.onNavigateToAnalysis,
  });

  @override
  State<TermRulerProgressionCard> createState() =>
      _TermRulerProgressionCardState();
}

class _TermRulerProgressionCardState extends State<TermRulerProgressionCard> {
  // 從設置中獲取星座界主星顯示狀態
  bool get _showSignRulers =>
      widget.viewModel.settingsViewModel?.currentChartTypeSettings
          ?.showZodiacRulers ??
      false;

  @override
  Widget build(BuildContext context) {
    final currentInfo = widget.result.currentInfo;
    final timeline =
        widget.result.timeline as List<TermRulerTimelineItem>? ?? [];
    final latitude = widget.result.latitude as double? ?? 0.0;
    final longitude = widget.result.longitude as double? ?? 0.0;
    final ascendantLongitude =
        widget.result.ascendantLongitude as double? ?? 0.0;

    return Container(
      width: 320,
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height * 0.8,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.15),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 標題欄
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  AppColors.indigoSurface,
                  AppColors.indigoSurface.withValues(alpha: 0.8),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                const SizedBox(width: 12),
                const Expanded(
                  child: Text(
                    '界主星配置法',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
                // 星座界主星開關
                Container(
                  margin: const EdgeInsets.only(right: 8),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.stars,
                        color: Colors.white.withValues(alpha: 0.8),
                        size: 16,
                      ),
                      const SizedBox(width: 4),
                      Switch(
                        value: _showSignRulers,
                        onChanged: (value) {
                          final settingsViewModel =
                              widget.viewModel.settingsViewModel;
                          if (settingsViewModel != null) {
                            settingsViewModel
                                .updateZodiacRulersVisibility(value);
                            setState(() {}); // 更新 UI 狀態
                          }
                        },
                        activeColor: Colors.white,
                        activeTrackColor: Colors.white.withValues(alpha: 0.3),
                        inactiveThumbColor: Colors.white.withValues(alpha: 0.7),
                        inactiveTrackColor: Colors.white.withValues(alpha: 0.2),
                        materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      ),
                    ],
                  ),
                ),
                IconButton(
                  onPressed: widget.onClose,
                  icon: const Icon(
                    Icons.close,
                    color: Colors.white,
                    size: 20,
                  ),
                  tooltip: '關閉',
                ),
              ],
            ),
          ),

          // 可滾動的內容部分
          Expanded(
            child: SingleChildScrollView(
              physics: const BouncingScrollPhysics(),
              clipBehavior: Clip.antiAlias,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 地理資訊卡片
                    _buildInfoCard(
                      title: '地理資訊',
                      icon: Icons.location_on,
                      color: Colors.teal,
                      children: [
                        _buildInfoRow(
                          icon: Icons.explore,
                          label: '緯度',
                          value:
                              '${latitude.toStringAsFixed(4)}° ${latitude >= 0 ? "北" : "南"}',
                        ),
                        _buildInfoRow(
                          icon: Icons.explore,
                          label: '經度',
                          value:
                              '${longitude.toStringAsFixed(4)}° ${longitude >= 0 ? "東" : "西"}',
                        ),
                        _buildInfoRow(
                          icon: Icons.trending_up,
                          label: '上升點經度',
                          value: '${ascendantLongitude.toStringAsFixed(4)}°',
                        ),
                      ],
                    ),

                    const SizedBox(height: 12),

                    // Ascensional Times Table 卡片
                    _buildAscensionalTimesTable(latitude),

                    const SizedBox(height: 12),

                    // 出生時間資訊卡片
                    _buildInfoCard(
                      title: '出生時間資訊',
                      icon: Icons.access_time,
                      color: Colors.indigo,
                      children: [
                        _buildSymbolInfoRow(
                          icon: Icons.star,
                          label: '上升星座',
                          value: currentInfo.ascendantSign,
                          symbol: ZodiacSymbols.getZodiacSymbol(
                              currentInfo.ascendantSign),
                          symbolColor: _getSignColor(currentInfo.ascendantSign),
                          isPlanet: false,
                        ),
                        _buildSymbolInfoRow(
                          icon: Icons.auto_awesome,
                          label: '上升界主星',
                          value: currentInfo.ascendantTermRulerName,
                          symbol: ZodiacSymbols.getPlanetSymbol(
                              currentInfo.ascendantTermRulerName),
                          symbolColor: _getPlanetColor(
                              currentInfo.ascendantTermRulerName),
                          isPlanet: true,
                        ),
                        // 星座界主星資訊（根據開關顯示）
                        if (_showSignRulers) ...[
                          _buildSymbolInfoRow(
                            icon: Icons.star,
                            label: '星座界主星',
                            value: _getSignRuler(currentInfo.ascendantSign),
                            symbol: ZodiacSymbols.getPlanetSymbol(
                                _getSignRuler(currentInfo.ascendantSign)),
                            symbolColor: _getPlanetColor(
                                _getSignRuler(currentInfo.ascendantSign)),
                            isPlanet: true,
                          ),
                        ],
                        _buildInfoRow(
                          icon: Icons.straighten,
                          label: '當前度數',
                          value:
                              '${(currentInfo.currentDegreeInSign as double? ?? 0.0).toStringAsFixed(2)}°',
                        ),
                        _buildInfoRow(
                          icon: Icons.schedule,
                          label: '每度時間',
                          value:
                              '${(currentInfo.timePerDegree as double? ?? 0.0).toStringAsFixed(4)} 天',
                        ),
                      ],
                    ),

                    const SizedBox(height: 16),

                    // 時間表標題
                    Container(
                      padding: const EdgeInsets.symmetric(
                          vertical: 8, horizontal: 12),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            Colors.purple.shade50,
                            Colors.purple.shade100
                          ],
                          begin: Alignment.centerLeft,
                          end: Alignment.centerRight,
                        ),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.purple.shade200),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.timeline,
                            color: Colors.purple.shade600,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            '界主星時間表',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.purple.shade700,
                            ),
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 8),

                    // 時間表項目
                    ...timeline.map(
                        (term) => _buildTermItem(context, currentInfo, term)),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 構建信息卡片
  Widget _buildInfoCard({
    required String title,
    required IconData icon,
    required Color color,
    required List<Widget> children,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
        boxShadow: [
          BoxShadow(
            color: color.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 卡片標題
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  color.withValues(alpha: 0.1),
                  color.withValues(alpha: 0.05)
                ],
                begin: Alignment.centerLeft,
                end: Alignment.centerRight,
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Icon(
                    icon,
                    color: color,
                    size: 16,
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ],
            ),
          ),
          // 卡片內容
          Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: children,
            ),
          ),
        ],
      ),
    );
  }

  /// 構建信息行
  Widget _buildInfoRow({
    required IconData icon,
    required String label,
    required String value,
    Color? valueColor,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Icon(
            icon,
            size: 14,
            color: Colors.grey.shade600,
          ),
          const SizedBox(width: 8),
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 13,
                color: Colors.grey.shade700,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              value,
              style: TextStyle(
                fontSize: 13,
                color: valueColor ?? Colors.black87,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 構建帶符號的信息行（用於星座和行星）
  Widget _buildSymbolInfoRow({
    required IconData icon,
    required String label,
    required String value,
    String? symbol,
    Color? symbolColor,
    bool isPlanet = false,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Icon(
            icon,
            size: 14,
            color: Colors.grey.shade600,
          ),
          const SizedBox(width: 8),
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 13,
                color: Colors.grey.shade700,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    value,
                    style: TextStyle(
                      fontSize: 14,
                      color: symbolColor ?? Colors.black87,
                      fontWeight: FontWeight.w600,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                if (symbol != null) ...[
                  Text(
                    symbol,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: symbolColor ?? Colors.black87,
                      fontFamily: 'astro_one_font',
                    ),
                  ),
                  const SizedBox(width: 6),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 構建時間表項目
  Widget _buildTermItem(BuildContext context,
      TermRulerProgressionResult currentInfo, TermRulerTimelineItem term) {
    final signColors = {
      '牡羊座': Colors.red,
      '金牛座': const Color(0xFFCC9933),
      '雙子座': const Color(0xFF127116),
      '巨蟹座': const Color(0xFF0A0AFF),
      '獅子座': Colors.red,
      '處女座': const Color(0xFFCC9933),
      '天秤座': const Color(0xFF127116),
      '天蠍座': const Color(0xFF0A0AFF),
      '射手座': Colors.red,
      '摩羯座': const Color(0xFFCC9933),
      '水瓶座': const Color(0xFF127116),
      '雙魚座': const Color(0xFF0A0AFF),
    };

    final signColor = signColors[term.sign] ?? Colors.grey;
    final planetColor = _getPlanetColor(term.termRulerName);

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              // 界主星符號
              Text(
                '${term.termRulerName}${ZodiacSymbols.getPlanetSymbol(term.termRulerName)}',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: planetColor, // 使用行星顏色
                  fontFamily: 'astro_one_font',
                ),
              ),
              const SizedBox(width: 4),
              // 星座符號
              Expanded(
                child: Text(
                  '${term.sign}${ZodiacSymbols.getZodiacSymbol(term.sign)}',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: signColor,
                    fontFamily: 'astro_one_font',
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              // 星座界主星符號（根據開關顯示）
              if (_showSignRulers) ...[
                const SizedBox(width: 8),
                Text(
                  '(${_getSignRuler(term.sign)}${ZodiacSymbols.getPlanetSymbol(_getSignRuler(term.sign))})',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: _getPlanetColor(_getSignRuler(term.sign)),
                    fontFamily: 'astro_one_font',
                  ),
                ),
              ],
            ],
          ),
          // if (term.isSignChange)
          Text(
            '每度時間:${(term.timePerDegree as double? ?? 0.0).toStringAsFixed(4)} 天',
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade600,
            ),
          ),
          Text(
            '開始: ${widget.viewModel.formatDateTime(term.startDateTime)} (${widget.viewModel.formatDegree(term.startDegree)})',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade600,
            ),
          ),
          // Text(
          //   '圈內累計: ${term.degreesInCircle.toStringAsFixed(2)}° / 360°',
          //   style: TextStyle(
          //     fontSize: 12,
          //     color: Colors.grey.shade500,
          //     fontStyle: FontStyle.italic,
          //   ),
          // ),
          Text(
            '結束: ${widget.viewModel.formatDateTime(term.endDateTime)} (${widget.viewModel.formatDegree(term.endDegree)})',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade600,
            ),
          ),
          Text(
            '持續: ${(term.durationDays as double? ?? 0.0).toStringAsFixed(2)} 天',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade600,
            ),
          ),

          // 相位顯示
          _buildAspectsDisplay(_calculateTermRulerAspects(term)),

          if (!kReleaseMode) ...[
            // 分析按鈕
            const SizedBox(height: 8),
            Align(
              alignment: Alignment.centerRight,
              child: ElevatedButton.icon(
                onPressed: () => _navigateToAnalysisPage(context, term),
                icon: const Icon(Icons.analytics, size: 16),
                label: const Text('股市分析'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.royalIndigo,
                  foregroundColor: Colors.white,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  minimumSize: const Size(0, 32),
                  textStyle: const TextStyle(fontSize: 12),
                ),
              ),
            ),
          ]
        ],
      ),
    );
  }

  /// 構建 Ascensional Times Table
  Widget _buildAscensionalTimesTable(double latitude) {
    // 確保緯度在有效範圍內（支援南緯到北緯）
    final clampedLatitude = latitude.clamp(-66.0, 66.0);

    // 獲取所有星座的上升時間資料
    final signs = [
      '牡羊座',
      '金牛座',
      '雙子座',
      '巨蟹座',
      '獅子座',
      '處女座',
      '天秤座',
      '天蠍座',
      '射手座',
      '摩羯座',
      '水瓶座',
      '雙魚座'
    ];

    // 根據緯度正負值顯示北緯或南緯
    String latitudeDisplay = clampedLatitude >= 0
        ? '北緯${clampedLatitude.toStringAsFixed(1)}°'
        : '南緯${(-clampedLatitude).toStringAsFixed(1)}°';

    return _buildInfoCard(
      title: '上升時間表 - $latitudeDisplay',
      icon: Icons.table_chart,
      color: Colors.deepPurple,
      children: [
        // 副標題
        Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: Text(
            '回歸黃道星座',
            style: TextStyle(
              fontSize: 13,
              fontWeight: FontWeight.w600,
              color: Colors.deepPurple.shade700,
              fontStyle: FontStyle.normal,
            ),
          ),
        ),

        // 表格標題行
        Container(
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
          decoration: BoxDecoration(
            color: Colors.deepPurple.shade50,
            borderRadius: BorderRadius.circular(6),
            border: Border.all(color: Colors.deepPurple.shade200),
          ),
          child: Row(
            children: [
              Expanded(
                flex: 2,
                child: Text(
                  '星座',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: Colors.deepPurple.shade800,
                  ),
                ),
              ),
              Expanded(
                flex: 2,
                child: Text(
                  '上升時間',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: Colors.deepPurple.shade800,
                  ),
                ),
              ),
              Expanded(
                flex: 2,
                child: Text(
                  '年/1°',
                  textAlign: TextAlign.right,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: Colors.deepPurple.shade800,
                  ),
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 4),

        // 表格資料行
        ...List.generate(signs.length, (index) {
          final sign = signs[index];
          final symbol = ZodiacSymbols.getZodiacSymbol(sign);

          try {
            final risingTime =
                AscensionTable.getRisingTime(clampedLatitude, sign);
            final timePerDegree =
                AscensionTable.getTimePerDegree(clampedLatitude, sign);

            return Container(
              margin: const EdgeInsets.only(bottom: 2),
              padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 4),
              decoration: BoxDecoration(
                color: index % 2 == 0 ? Colors.grey.shade50 : Colors.white,
                borderRadius: BorderRadius.circular(4),
              ),
              child: Row(
                children: [
                  Expanded(
                    flex: 2,
                    child: Row(
                      children: [
                        // 星座符號，使用 astro_one_font
                        Text(
                          symbol,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: _getSignColor(sign),
                            fontFamily: 'astro_one_font',
                          ),
                        ),
                        const SizedBox(width: 6),
                        // 星座名稱，使用 Flexible 避免溢出
                        Flexible(
                          child: Text(
                            sign,
                            style: const TextStyle(
                              fontSize: 10,
                              fontWeight: FontWeight.w500,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: Text(
                      risingTime.toStringAsFixed(2),
                      textAlign: TextAlign.center,
                      style: const TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.w600,
                        fontFamily: 'monospace',
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: Text(
                      timePerDegree.toStringAsFixed(4),
                      textAlign: TextAlign.right,
                      style: const TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.w600,
                        fontFamily: 'monospace',
                      ),
                    ),
                  ),
                ],
              ),
            );
          } catch (e) {
            // 如果獲取資料失敗，顯示錯誤訊息
            return Container(
              margin: const EdgeInsets.only(bottom: 2),
              padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 4),
              child: Row(
                children: [
                  Expanded(
                    flex: 2,
                    child: Row(
                      children: [
                        Text(
                          symbol,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: _getSignColor(sign),
                            fontFamily: 'astro_one_font',
                          ),
                        ),
                        const SizedBox(width: 6),
                        Flexible(
                          child: Text(
                            sign,
                            style: const TextStyle(fontSize: 10),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const Expanded(
                    flex: 4,
                    child: Text(
                      'Error loading data',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 9,
                        color: Colors.red,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ),
                ],
              ),
            );
          }
        }),
      ],
    );
  }

  /// 獲取星座顏色
  Color _getSignColor(String sign) {
    final signColors = {
      '牡羊座': Colors.red,
      '金牛座': const Color(0xFFCC9933),
      '雙子座': const Color(0xFF127116),
      '巨蟹座': const Color(0xFF0A0AFF),
      '獅子座': Colors.red,
      '處女座': const Color(0xFFCC9933),
      '天秤座': const Color(0xFF127116),
      '天蠍座': const Color(0xFF0A0AFF),
      '射手座': Colors.red,
      '摩羯座': const Color(0xFFCC9933),
      '水瓶座': const Color(0xFF127116),
      '雙魚座': const Color(0xFF0A0AFF),
    };
    return signColors[sign] ?? Colors.grey;
  }

  /// 獲取行星顏色（從 AstrologyConstants 中獲取）
  Color _getPlanetColor(String planetName) {
    try {
      final planet = AstrologyConstants.PLANETS.firstWhere(
        (p) => p['name'] == planetName,
      );
      return planet['color'] as Color;
    } catch (e) {
      return Colors.grey; // 預設顏色
    }
  }

  /// 獲取星座的界主星（傳統守護星）
  String _getSignRuler(String sign) {
    final signRulers = {
      '牡羊座': '火星',
      '金牛座': '金星',
      '雙子座': '水星',
      '巨蟹座': '月亮',
      '獅子座': '太陽',
      '處女座': '水星',
      '天秤座': '金星',
      '天蠍座': '火星',
      '射手座': '木星',
      '摩羯座': '土星',
      '水瓶座': '土星',
      '雙魚座': '木星',
    };
    return signRulers[sign] ?? '未知';
  }

  /// 計算界主星與其他行星的相位
  List<AspectInfo> _calculateTermRulerAspects(TermRulerTimelineItem term) {
    final aspects = <AspectInfo>[];

    // 獲取星盤中的所有行星
    final planets = widget.viewModel.chartData.planets;
    if (planets == null || planets.isEmpty) return aspects;

    // 創建一個虛擬的界主星行星位置對象
    final termRulerPlanet = PlanetPosition(
      id: 999,
      // 使用特殊ID
      name: term.termRulerName,
      symbol: ZodiacSymbols.getPlanetSymbol(term.termRulerName),
      longitude: term.longitude,
      latitude: 0.0,
      distance: 0.0,
      longitudeSpeed: 0.0,
      latitudeSpeed: 0.0,
      distanceSpeed: 0.0,
      sign: term.sign,
      house: 1,
      // 暫時設為1宮
      color: _getPlanetColor(term.termRulerName),
    );

    // 計算與每個行星的相位
    for (final planet in planets) {
      final aspect = AspectCalculator.calculateAspectBetweenPlanets(
        termRulerPlanet,
        planet,
        aspectOrbs: widget
            .viewModel.settingsViewModel?.currentChartTypeSettings?.aspectOrbs,
        includeMinorAspects: false, // 只顯示主要相位
      );

      if (aspect != null) {
        aspects.add(aspect);
      }
    }

    return aspects;
  }

  /// 構建相位顯示組件
  Widget _buildAspectsDisplay(List<AspectInfo> aspects) {
    if (aspects.isEmpty) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.only(top: 8),
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.blue.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(
          color: Colors.blue.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.compare_arrows,
                size: 14,
                color: Colors.blue[600],
              ),
              const SizedBox(width: 4),
              Text(
                '相位',
                style: TextStyle(
                  fontSize: 11,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue[700],
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Wrap(
            spacing: 6,
            runSpacing: 2,
            children:
                aspects.map((aspect) => _buildAspectChip(aspect)).toList(),
          ),
        ],
      ),
    );
  }

  /// 構建單個相位標籤
  Widget _buildAspectChip(AspectInfo aspect) {
    final aspectDef = AspectDefinitions.getAspectDefinition(aspect.aspect);
    final aspectColor = aspectDef?['color'] as Color? ?? Colors.grey;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: aspectColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          color: aspectColor.withValues(alpha: 0.3),
          width: 0.5,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            '${aspect.planet2.name}${ZodiacSymbols.getPlanetSymbol(aspect.planet2.name)}',
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.w600,
              color: aspect.planet2.color,
              fontFamily: 'astro_one_font',
            ),
          ),
          const SizedBox(width: 2),
          Text(
            aspect.symbol,
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.bold,
              color: aspectColor,
              fontFamily: 'astro_one_font',
            ),
          ),
          const SizedBox(width: 2),
          Text(
            '${aspect.orb.toStringAsFixed(1)}°',
            style: TextStyle(
              fontSize: 9,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  /// 導航到分析頁面
  void _navigateToAnalysisPage(
      BuildContext context, TermRulerTimelineItem term) {

    // 如果有自訂的導航回調，使用它
    if (widget.onNavigateToAnalysis != null) {
      widget.onNavigateToAnalysis!(term);
      return;
    }

    // 否則使用預設的導航邏輯
    Navigator.push(
      context,
      MaterialPageRoute(
        settings: const RouteSettings(name: 'term_ruler_stock_analysis'),
        builder: (context) => TermRulerStockAnalysisPage(
          chartViewModel: widget.viewModel,
          termRulerItem: term,
          aspects: _calculateTermRulerAspects(term),
        ),
      ),
    );
  }
}
