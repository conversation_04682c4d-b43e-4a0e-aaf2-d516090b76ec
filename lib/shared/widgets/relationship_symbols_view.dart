import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';

import '../../../data/models/astrology/chart_data.dart';
import '../../../data/models/astrology/chart_type.dart';
import '../../../data/models/astrology/planet_position.dart';
import '../../../presentation/themes/app_theme.dart';
import '../../presentation/pages/chart_page.dart';
import '../../presentation/viewmodels/chart_viewmodel.dart';
import '../../presentation/viewmodels/relationship_analysis_viewmodel.dart';


class RelationshipSymbolsView extends StatelessWidget {
  final ChartData chartData;

  const RelationshipSymbolsView({
    Key? key,
    required this.chartData,
  }) : super(key: key);

  /// 導航到本命盤頁面
  void _navigateToNatalChart(BuildContext context) {
    // 創建一個新的 ChartViewModel 實例
    final chartViewModel = ChartViewModel();

    // 設置主要人物的出生數據
    chartViewModel.setPrimaryPerson(chartData.primaryPerson);

    // 導航到星盤頁面
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ChangeNotifierProvider.value(
          value: chartViewModel,
          child: ChartPage(chartType: ChartType.natal),
        ),
      ),
    );
  }

  /// 格式化戀愛象徵資訊為可複製的文本
  String _formatRelationshipInfoText(BuildContext context) {
    // 獲取 ViewModel
    final viewModel = Provider.of<RelationshipAnalysisViewModel>(context, listen: false);

    // 尋找所有行星
    final Map<String, PlanetPosition> planets = {};
    final planetNames = [
      '太陽', '月亮', '水星', '金星', '火星',
      '木星', '土星', '天王星', '海王星', '冥王星'
    ];

    for (final name in planetNames) {
      try {
        planets[name] = chartData.planets!.firstWhere(
          (planet) => planet.name == name,
          orElse: () => throw Exception('找不到$name數據'),
        );
      } catch (e) {
        // 如果找不到行星，則忽略
      }
    }

    // 確保金星和火星存在
    if (!planets.containsKey('金星') || !planets.containsKey('火星')) {
      throw Exception('無法獲取金星或火星數據');
    }

    final venus = planets['金星']!;
    final mars = planets['火星']!;

    // 尋找第5宮和第7宮的宮頭
    final house5Cusp = chartData.houses!.cusps[5];
    final house7Cusp = chartData.houses!.cusps[7];

    // 獲取第5宮和第7宮的宮主
    final house5Ruler = viewModel.getHouseRuler(house5Cusp);
    final house7Ruler = viewModel.getHouseRuler(house7Cusp);

    // 尋找第5宮主和第7宮主的行星
    final house5RulerPlanet = chartData.planets!.firstWhere(
      (planet) => planet.name == house5Ruler,
      orElse: () => throw Exception('找不到第5宮主行星'),
    );

    final house7RulerPlanet = chartData.planets!.firstWhere(
      (planet) => planet.name == house7Ruler,
      orElse: () => throw Exception('找不到第7宮主行星'),
    );

    // 基本資訊
    final StringBuffer buffer = StringBuffer();
    buffer.writeln('【${chartData.primaryPerson.name}的戀愛象徵分析】');
    buffer.writeln();

    // 金星資訊
    buffer.writeln('【金星】');
    buffer.writeln('位置：${venus.sign} ${venus.house}宮 ${venus.getFormattedLongitude()}');
    buffer.writeln('屬性：愛情觀和吸引力類型');
    buffer.writeln('宮位影響：位於第${venus.house}宮，${viewModel.getHouseDescription(venus.house)}');

    // 添加金星是哪些宮位的宮主星
    final venusHousesRuled = _getHousesRuledByPlanet('金星', context);
    if (venusHousesRuled.isNotEmpty) {
      buffer.writeln('宮主星：第${venusHousesRuled.join('宮、第')}宮的宮主');
    }

    buffer.writeln('尊貴力量：${venus.getDignityText()}');
    buffer.writeln();

    // 火星資訊
    buffer.writeln('【火星】');
    buffer.writeln('位置：${mars.sign} ${mars.house}宮 ${mars.getFormattedLongitude()}');
    buffer.writeln('屬性：慾望和追求方式');
    buffer.writeln('宮位影響：位於第${mars.house}宮，${viewModel.getHouseDescription(mars.house)}');

    // 添加火星是哪些宮位的宮主星
    final marsHousesRuled = _getHousesRuledByPlanet('火星', context);
    if (marsHousesRuled.isNotEmpty) {
      buffer.writeln('宮主星：第${marsHousesRuled.join('宮、第')}宮的宮主');
    }

    buffer.writeln('尊貴力量：${mars.getDignityText()}');
    buffer.writeln();

    // 第7宮主資訊
    buffer.writeln('【第7宮主（$house7Ruler）】');
    buffer.writeln('位置：${house7RulerPlanet.sign} ${house7RulerPlanet.house}宮 ${house7RulerPlanet.getFormattedLongitude()}');
    buffer.writeln('屬性：理想的伴侶類型');
    buffer.writeln('宮位影響：位於第${house7RulerPlanet.house}宮，${viewModel.getHouseDescription(house7RulerPlanet.house)}');

    // 添加第7宮主是哪些宮位的宮主星
    final house7RulerHousesRuled = _getHousesRuledByPlanet(house7Ruler, context);
    if (house7RulerHousesRuled.isNotEmpty) {
      buffer.writeln('宮主星：第${house7RulerHousesRuled.join('宮、第')}宮的宮主');
    }

    buffer.writeln('尊貴力量：${house7RulerPlanet.getDignityText()}');
    buffer.writeln();

    // 第5宮主資訊
    buffer.writeln('【第5宮主（$house5Ruler）】');
    buffer.writeln('位置：${house5RulerPlanet.sign} ${house5RulerPlanet.house}宮 ${house5RulerPlanet.getFormattedLongitude()}');
    buffer.writeln('屬性：戀愛體驗和表達方式');
    buffer.writeln('宮位影響：位於第${house5RulerPlanet.house}宮，${viewModel.getHouseDescription(house5RulerPlanet.house)}');

    // 添加第5宮主是哪些宮位的宮主星
    final house5RulerHousesRuled = _getHousesRuledByPlanet(house5Ruler, context);
    if (house5RulerHousesRuled.isNotEmpty) {
      buffer.writeln('宮主星：第${house5RulerHousesRuled.join('宮、第')}宮的宮主');
    }

    buffer.writeln('尊貴力量：${house5RulerPlanet.getDignityText()}');
    buffer.writeln();

    // 其他行星資訊
    buffer.writeln('【其他行星】');

    // 太陽資訊
    if (planets.containsKey('太陽')) {
      final sun = planets['太陽']!;
      buffer.writeln('太陽：${sun.sign} ${sun.house}宮 ${sun.getFormattedLongitude()}');

      // 添加太陽是哪些宮位的宮主星
      final sunHousesRuled = _getHousesRuledByPlanet('太陽', context);
      if (sunHousesRuled.isNotEmpty) {
        buffer.writeln('  宮主星：第${sunHousesRuled.join('宮、第')}宮的宮主');
      }
    }

    // 月亮資訊
    if (planets.containsKey('月亮')) {
      final moon = planets['月亮']!;
      buffer.writeln('月亮：${moon.sign} ${moon.house}宮 ${moon.getFormattedLongitude()}');

      // 添加月亮是哪些宮位的宮主星
      final moonHousesRuled = _getHousesRuledByPlanet('月亮', context);
      if (moonHousesRuled.isNotEmpty) {
        buffer.writeln('  宮主星：第${moonHousesRuled.join('宮、第')}宮的宮主');
      }
    }

    // 水星資訊
    if (planets.containsKey('水星')) {
      final mercury = planets['水星']!;
      buffer.writeln('水星：${mercury.sign} ${mercury.house}宮 ${mercury.getFormattedLongitude()}');

      // 添加水星是哪些宮位的宮主星
      final mercuryHousesRuled = _getHousesRuledByPlanet('水星', context);
      if (mercuryHousesRuled.isNotEmpty) {
        buffer.writeln('  宮主星：第${mercuryHousesRuled.join('宮、第')}宮的宮主');
      }
    }

    // 木星資訊
    if (planets.containsKey('木星')) {
      final jupiter = planets['木星']!;
      buffer.writeln('木星：${jupiter.sign} ${jupiter.house}宮 ${jupiter.getFormattedLongitude()}');

      // 添加木星是哪些宮位的宮主星
      final jupiterHousesRuled = _getHousesRuledByPlanet('木星', context);
      if (jupiterHousesRuled.isNotEmpty) {
        buffer.writeln('  宮主星：第${jupiterHousesRuled.join('宮、第')}宮的宮主');
      }
    }

    // 土星資訊
    if (planets.containsKey('土星')) {
      final saturn = planets['土星']!;
      buffer.writeln('土星：${saturn.sign} ${saturn.house}宮 ${saturn.getFormattedLongitude()}');

      // 添加土星是哪些宮位的宮主星
      final saturnHousesRuled = _getHousesRuledByPlanet('土星', context);
      if (saturnHousesRuled.isNotEmpty) {
        buffer.writeln('  宮主星：第${saturnHousesRuled.join('宮、第')}宮的宮主');
      }
    }

    return buffer.toString();
  }

  /// 複製星盤資訊
  void _copyChartInfo(BuildContext context) {
    if (chartData.planets == null || chartData.houses == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('無法獲取完整的星盤數據')),
      );
      return;
    }

    try {
      // 格式化戀愛象徵資訊
      final text = _formatRelationshipInfoText(context);

      // 複製到剪貼板
      Clipboard.setData(ClipboardData(text: text));

      // 顯示提示
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('戀愛象徵資訊已複製到剪貼板'),
          duration: Duration(seconds: 2),
        ),
      );
    } catch (e) {
      // 顯示錯誤提示
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('複製資訊時出錯: $e')),
      );
    }
  }

  /// 格式化日期時間
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
        '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  /// 確定行星是哪些宮位的宮主星
  List<int> _getHousesRuledByPlanet(String planetName, BuildContext context) {
    final viewModel = Provider.of<RelationshipAnalysisViewModel>(context, listen: false);
    final List<int> housesRuled = [];

    // 檢查每個宮位
    for (int i = 1; i <= 12; i++) {
      final cuspLongitude = chartData.houses!.cusps[i];
      final houseRuler = viewModel.getHouseRuler(cuspLongitude);

      if (houseRuler == planetName) {
        housesRuled.add(i);
      }
    }

    return housesRuled;
  }

  /// 獲取行星是哪些宮位的宮主星的文本描述
  String _getHouseRulerText(String planetName, BuildContext context) {
    final housesRuled = _getHousesRuledByPlanet(planetName, context);
    if (housesRuled.isEmpty) {
      return '';
    }

    return '。同時是第${housesRuled.join('宮、第')}宮的宮主星';
  }

  @override
  Widget build(BuildContext context) {
    final viewModel = Provider.of<RelationshipAnalysisViewModel>(context);

    if (chartData.planets == null || chartData.houses == null) {
      return const Center(
        child: Text('無法獲取完整的星盤數據'),
      );
    }

    // 尋找金星和火星
    final venus = chartData.planets!.firstWhere(
      (planet) => planet.name == '金星',
      orElse: () => throw Exception('找不到金星數據'),
    );

    final mars = chartData.planets!.firstWhere(
      (planet) => planet.name == '火星',
      orElse: () => throw Exception('找不到火星數據'),
    );

    // 尋找第5宮和第7宮的宮頭
    final house5Cusp = chartData.houses!.cusps[5];
    final house7Cusp = chartData.houses!.cusps[7];

    // 獲取第5宮和第7宮的宮主
    final house5Ruler = viewModel.getHouseRuler(house5Cusp);
    final house7Ruler = viewModel.getHouseRuler(house7Cusp);

    // 尋找第5宮主和第7宮主的行星
    final house5RulerPlanet = chartData.planets!.firstWhere(
      (planet) => planet.name == house5Ruler,
      orElse: () => throw Exception('找不到第5宮主行星'),
    );

    final house7RulerPlanet = chartData.planets!.firstWhere(
      (planet) => planet.name == house7Ruler,
      orElse: () => throw Exception('找不到第7宮主行星'),
    );

    return DraggableScrollableSheet(
      initialChildSize: 0.7,
      minChildSize: 0.5,
      maxChildSize: 0.95,
      expand: false,
      builder: (context, scrollController) {
        return Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '${chartData.primaryPerson.name}的戀愛象徵分析',
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Navigator.pop(context),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  ElevatedButton.icon(
                    icon: const Icon(Icons.visibility),
                    label: const Text('查看本命盤'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.royalIndigo,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    ),
                    onPressed: () => _navigateToNatalChart(context),
                  ),
                  ElevatedButton.icon(
                    icon: const Icon(Icons.copy),
                    label: const Text('複製資訊'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.solarAmber,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    ),
                    onPressed: () => _copyChartInfo(context),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              const Divider(),
              Expanded(
                child: ListView(
                  controller: scrollController,
                  children: [
                    _buildAnalysisResultCard(
                      context,
                      '金星',
                      '愛情觀和吸引力',
                      Icons.favorite,
                      '金星在${venus.sign}，位於第${venus.house}宮',
                      '金星代表您的愛情觀和吸引力類型。金星在${venus.sign}表示您${viewModel.getVenusDescription(venus.sign)}。位於第${venus.house}宮，意味著${viewModel.getHouseDescription(venus.house)}${_getHouseRulerText('金星', context)}',
                    ),
                    _buildAnalysisResultCard(
                      context,
                      '火星',
                      '慾望和追求方式',
                      Icons.local_fire_department,
                      '火星在${mars.sign}，位於第${mars.house}宮',
                      '火星代表您的慾望和追求方式。火星在${mars.sign}表示您${viewModel.getMarsDescription(mars.sign)}。位於第${mars.house}宮，意味著${viewModel.getHouseDescription(mars.house)}${_getHouseRulerText('火星', context)}',
                    ),
                    _buildAnalysisResultCard(
                      context,
                      '第7宮主',
                      '伴侶類型',
                      Icons.people,
                      '第7宮主為$house7Ruler，位於${house7RulerPlanet.sign}的第${house7RulerPlanet.house}宮',
                      '第7宮主宰您理想的伴侶類型。$house7Ruler在${house7RulerPlanet.sign}表示您被${viewModel.getPlanetInSignDescription(house7Ruler, house7RulerPlanet.sign)}的人所吸引。位於第${house7RulerPlanet.house}宮，意味著${viewModel.getHouseDescription(house7RulerPlanet.house)}${_getHouseRulerText(house7Ruler, context)}',
                    ),
                    _buildAnalysisResultCard(
                      context,
                      '第5宮主',
                      '戀愛體驗',
                      Icons.favorite_border,
                      '第5宮主為$house5Ruler，位於${house5RulerPlanet.sign}的第${house5RulerPlanet.house}宮',
                      '第5宮主宰您的戀愛體驗和表達方式。$house5Ruler在${house5RulerPlanet.sign}表示您的戀愛體驗傾向於${viewModel.getPlanetInSignDescription(house5Ruler, house5RulerPlanet.sign)}。位於第${house5RulerPlanet.house}宮，意味著${viewModel.getHouseDescription(house5RulerPlanet.house)}${_getHouseRulerText(house5Ruler, context)}',
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildAnalysisResultCard(
    BuildContext context,
    String title,
    String subtitle,
    IconData icon,
    String summary,
    String content,
  ) {
    return Card(
      elevation: 3,
      margin: const EdgeInsets.only(bottom: 16),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CircleAvatar(
                  backgroundColor: AppColors.solarAmber,
                  radius: 24,
                  child: Icon(icon, color: Colors.white, size: 24),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        subtitle,
                        style: const TextStyle(fontSize: 14, color: Colors.black87),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                summary,
                style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
            ),
            const SizedBox(height: 12),
            Text(
              content,
              style: const TextStyle(fontSize: 16),
            ),
          ],
        ),
      ),
    );
  }
}
