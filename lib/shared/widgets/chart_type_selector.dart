import 'package:flutter/material.dart';

import '../../astreal.dart';

/// 星盤類型選擇器組件
class ChartTypeSelector extends StatelessWidget {
  final ChartType selectedChartType;
  final Function(ChartType) onChartTypeChanged;

  const ChartTypeSelector({
    super.key,
    required this.selectedChartType,
    required this.onChartTypeChanged,
  });

  @override
  Widget build(BuildContext context) {
    return StyledCard(
      padding: const EdgeInsets.all(16),
      // decoration: BoxDecoration(
      //   gradient: LinearGradient(
      //     colors: [
      //       AppColors.royalIndigo.withValues(alpha: 0.1),
      //       AppColors.solarAmber.withValues(alpha: 0.1),
      //     ],
      //     begin: Alignment.topLeft,
      //     end: Alignment.bottomRight,
      //   ),
      //   borderRadius: BorderRadius.circular(12),
      //   border: Border.all(
      //     color: AppColors.royalIndigo.withValues(alpha: 0.2),
      //   ),
      // ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(
                Icons.tune,
                color: AppColors.royalIndigo,
                size: 24,
              ),
              SizedBox(width: 8),
              Text(
                '選擇星盤類型',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppColors.royalIndigo,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            '不同類型的星盤可以有不同的顯示設定。選擇要配置的星盤類型：',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade700,
              height: 1.5,
            ),
          ),
          const SizedBox(height: 16),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.white,
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(8),
            ),
            child: DropdownButtonHideUnderline(
              child: DropdownButton<ChartType>(
                value: selectedChartType,
                isExpanded: true,
                items: ChartType.values.map((ChartType chartType) {
                  return DropdownMenuItem<ChartType>(
                    value: chartType,
                    child: Row(
                      children: [
                        // Icon(
                        //   _getChartTypeIcon(chartType),
                        //   color: _getChartTypeColor(chartType),
                        //   size: 20,
                        // ),
                        // const SizedBox(width: 12),
                        Text(_getChartTypeDisplayName(chartType)),
                      ],
                    ),
                  );
                }).toList(),
                onChanged: (ChartType? newValue) {
                  if (newValue != null) {
                    onChartTypeChanged(newValue);
                  }
                },
              ),
            ),
          ),
          const SizedBox(height: 12),
          _buildChartTypeInfo(selectedChartType),
        ],
      ),
    );
  }

  /// 構建星盤類型說明
  Widget _buildChartTypeInfo(ChartType chartType) {
    final info = _getChartTypeInfo(chartType);
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: info['color'].withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(
            Icons.info_outline,
            color: info['color'],
            size: 16,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              info['description'],
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade700,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 獲取星盤類型顯示名稱
  String _getChartTypeDisplayName(ChartType chartType) {
    return chartType.name;
  }

  /// 獲取星盤類型信息
  Map<String, dynamic> _getChartTypeInfo(ChartType chartType) {
    switch (chartType) {
      case ChartType.natal:
        return {
          'color': Colors.blue,
          'description': '本命盤是個人出生時的天體位置圖，顯示個人的基本性格和潛能。',
        };
      case ChartType.transit:
        return {
          'color': Colors.green,
          'description': '流年盤顯示當前天體位置對本命盤的影響，用於預測時運變化。',
        };
      case ChartType.synastry:
        return {
          'color': Colors.pink,
          'description': '合盤比較兩個人的星盤，分析彼此的相容性和關係動態。',
        };
      case ChartType.composite:
        return {
          'color': Colors.purple,
          'description': '組合盤是兩個人星盤的中點圖，代表關係本身的特質。',
        };
      case ChartType.eclipse:
        return {
          'color': Colors.indigo,
          'description': '日月蝕盤分析蝕相事件的影響，預測重大轉折和變化。',
        };
      case ChartType.equinoxSolstice:
        return {
          'color': Colors.orange,
          'description': '二分二至盤分析季節轉換時的能量，用於預測社會和自然趨勢。',
        };
      default:
        return {
          'color': Colors.grey,
          'description': '未知的星盤類型。',
        };
    }
  }
}
