import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../core/config/app_version.dart';
import '../../core/utils/logger_utils.dart';
import '../../presentation/themes/app_theme.dart';

/// 更新提示對話框
class UpdateDialog extends StatelessWidget {
  final AppVersion versionInfo;
  final bool isForceUpdate;
  final VoidCallback? onLater;
  final VoidCallback? onUpdate;

  const UpdateDialog({
    super.key,
    required this.versionInfo,
    required this.isForceUpdate,
    this.onLater,
    this.onUpdate,
  });

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: !isForceUpdate, // 強制更新時不允許返回
      child: AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: [
            Icon(
              isForceUpdate ? Icons.warning : Icons.system_update,
              color: isForceUpdate ? Colors.orange : AppColors.royalIndigo,
              size: 28,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                isForceUpdate ? '必須更新' : '發現新版本',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: isForceUpdate ? Colors.orange : AppColors.royalIndigo,
                ),
              ),
            ),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 版本信息
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppColors.indigoSurface.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: AppColors.royalIndigo,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '最新版本：${versionInfo.version}',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
              
              const SizedBox(height: 16),
              
              // 更新說明
              Text(
                versionInfo.updateMessage,
                style: const TextStyle(
                  fontSize: 14,
                  height: 1.5,
                ),
              ),
              
              // 新功能列表
              if (versionInfo.features.isNotEmpty) ...[
                const SizedBox(height: 16),
                const Text(
                  '新功能：',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 8),
                ...versionInfo.features.map((feature) => Padding(
                  padding: const EdgeInsets.only(bottom: 4),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text('• ', style: TextStyle(fontSize: 14)),
                      Expanded(
                        child: Text(
                          feature,
                          style: const TextStyle(fontSize: 14),
                        ),
                      ),
                    ],
                  ),
                )),
              ],
              
              // 強制更新警告
              if (isForceUpdate) ...[
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.orange.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: Colors.orange.withOpacity(0.3),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.warning,
                        color: Colors.orange,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      const Expanded(
                        child: Text(
                          '您的版本過舊，必須更新才能繼續使用',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.orange,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        ),
        actions: [
          // 稍後更新按鈕（僅在非強制更新時顯示）
          if (!isForceUpdate)
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                onLater?.call();
              },
              child: const Text(
                '稍後更新',
                style: TextStyle(
                  color: Colors.grey,
                  fontSize: 16,
                ),
              ),
            ),
          
          // 立即更新按鈕
          ElevatedButton(
            onPressed: () async {
              await _handleUpdate(context);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: isForceUpdate ? Colors.orange : AppColors.royalIndigo,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              isForceUpdate ? '立即更新' : '更新',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 處理更新操作
  Future<void> _handleUpdate(BuildContext context) async {
    try {
      logger.i('用戶點擊更新，準備打開更新 URL: ${versionInfo.updateUrl}');
      
      final uri = Uri.parse(versionInfo.updateUrl);
      
      if (await canLaunchUrl(uri)) {
        await launchUrl(
          uri,
          mode: LaunchMode.externalApplication,
        );
        
        // 調用回調
        onUpdate?.call();
        
        // 如果不是強制更新，關閉對話框
        if (!isForceUpdate && context.mounted) {
          Navigator.of(context).pop();
        }
      } else {
        logger.e('無法打開更新 URL: ${versionInfo.updateUrl}');
        
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('無法打開更新頁面，請手動前往應用商店更新'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      logger.e('打開更新 URL 失敗: $e');
      
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('更新失敗：$e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// 顯示更新對話框
  static Future<void> show(
    BuildContext context, {
    required AppVersion versionInfo,
    required bool isForceUpdate,
    VoidCallback? onLater,
    VoidCallback? onUpdate,
  }) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: !isForceUpdate, // 強制更新時不允許點擊外部關閉
      builder: (BuildContext context) {
        return UpdateDialog(
          versionInfo: versionInfo,
          isForceUpdate: isForceUpdate,
          onLater: onLater,
          onUpdate: onUpdate,
        );
      },
    );
  }
}
