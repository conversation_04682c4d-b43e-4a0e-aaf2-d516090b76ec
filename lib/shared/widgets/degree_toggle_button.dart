import 'package:flutter/material.dart';

import '../../presentation/viewmodels/settings_viewmodel.dart';

class DegreeToggleButton extends StatefulWidget {
  final SettingsViewModel settingsViewModel;
  final VoidCallback? onToggle;

  const DegreeToggleButton({
    Key? key,
    required this.settingsViewModel,
    this.onToggle,
  }) : super(key: key);

  @override
  State<DegreeToggleButton> createState() => _DegreeToggleButtonState();
}

class _DegreeToggleButtonState extends State<DegreeToggleButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _rotationAnimation;
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 0.5,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _toggleExpansion() {
    setState(() {
      _isExpanded = !_isExpanded;
      if (_isExpanded) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final chartSettings = widget.settingsViewModel.chartSettings;
    if (chartSettings == null) return const SizedBox.shrink();

    final screenWidth = MediaQuery.of(context).size.width;
    final isMobile = screenWidth <= 600;

    return Positioned(
      right: isMobile ? 8 : 16,
      bottom: isMobile ? 80 : 100,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 展開的選項
          AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
            height: _isExpanded ? null : 0,
            child: _isExpanded
                ? Container(
                    margin: const EdgeInsets.only(bottom: 8),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.9),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: Colors.grey.shade300,
                        width: 1,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.08),
                          blurRadius: 6,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        _buildToggleOption(
                          icon: Icons.home,
                          label: '宮位度數',
                          value: chartSettings.showHouseDegrees,
                          onChanged: (value) {
                            widget.settingsViewModel.updateHouseDegreesVisibility(value);
                            widget.onToggle?.call();
                          },
                          color: Colors.orange,
                        ),
                        _buildToggleOption(
                          icon: Icons.public,
                          label: '行星度數',
                          value: chartSettings.showPlanetDegrees,
                          onChanged: (value) {
                            widget.settingsViewModel.updatePlanetDegreesVisibility(value);
                            widget.onToggle?.call();
                          },
                          color: Colors.blue,
                        ),
                      ],
                    ),
                  )
                : const SizedBox.shrink(),
          ),
          // 主按鈕
          Container(
            decoration: BoxDecoration(
              color: Colors.blue.shade600,
              borderRadius: BorderRadius.circular(isMobile ? 24 : 28),
              border: Border.all(
                color: Colors.blue.shade300,
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 6,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(28),
                onTap: _toggleExpansion,
                child: Container(
                  width: isMobile ? 48 : 56,
                  height: isMobile ? 48 : 56,
                  child: AnimatedBuilder(
                    animation: _rotationAnimation,
                    builder: (context, child) {
                      return Transform.rotate(
                        angle: _rotationAnimation.value * 3.14159,
                        child: Icon(
                          Icons.straighten,
                          color: Colors.white,
                          size: isMobile ? 20 : 24,
                        ),
                      );
                    },
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildToggleOption({
    required IconData icon,
    required String label,
    required bool value,
    required Function(bool) onChanged,
    required Color color,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: () => onChanged(!value),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: value ? color.withValues(alpha: 0.2) : Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    icon,
                    size: 16,
                    color: value ? color : Colors.grey.shade600,
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: value ? color : Colors.grey.shade700,
                  ),
                ),
                const SizedBox(width: 8),
                Container(
                  width: 20,
                  height: 12,
                  decoration: BoxDecoration(
                    color: value ? color : Colors.grey.shade300,
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: AnimatedAlign(
                    duration: const Duration(milliseconds: 200),
                    alignment: value ? Alignment.centerRight : Alignment.centerLeft,
                    child: Container(
                      width: 8,
                      height: 8,
                      margin: const EdgeInsets.all(2),
                      decoration: const BoxDecoration(
                        color: Colors.white,
                        shape: BoxShape.circle,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
