import 'package:flutter/material.dart';

/// 響應式包裝器組件
/// 在大螢幕上限制最大寬度，避免 UI 元素分散過開
class ResponsiveWrapper extends StatelessWidget {
  final Widget child;
  final double maxWidth;
  final EdgeInsetsGeometry? padding;
  final bool centerContent;

  const ResponsiveWrapper({
    super.key,
    required this.child,
    this.maxWidth = 800.0, // 預設最大寬度 800px
    this.padding,
    this.centerContent = true,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    
    // 如果螢幕寬度小於等於最大寬度，直接返回子組件
    if (screenWidth <= maxWidth) {
      return child;
    }

    // 大螢幕情況下，限制寬度並居中
    return Center(
      child: Container(
        width: maxWidth,
        padding: padding,
        child: child,
      ),
    );
  }
}

/// 響應式頁面包裝器
/// 專門用於包裝整個頁面內容
class ResponsivePageWrapper extends StatelessWidget {
  final Widget child;
  final double maxWidth;
  final EdgeInsetsGeometry? padding;

  const ResponsivePageWrapper({
    super.key,
    required this.child,
    this.maxWidth = 1200.0, // 頁面級別的最大寬度更大一些
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    
    // 如果螢幕寬度小於等於最大寬度，直接返回子組件
    if (screenWidth <= maxWidth) {
      return child;
    }

    // 大螢幕情況下，限制寬度並居中
    return Center(
      child: Container(
        width: maxWidth,
        padding: padding ?? const EdgeInsets.symmetric(horizontal: 16.0),
        child: child,
      ),
    );
  }
}

/// 響應式卡片包裝器
/// 用於包裝卡片內容，在大螢幕上限制寬度
class ResponsiveCardWrapper extends StatelessWidget {
  final Widget child;
  final double maxWidth;
  final EdgeInsetsGeometry? margin;

  const ResponsiveCardWrapper({
    super.key,
    required this.child,
    this.maxWidth = 600.0, // 卡片級別的最大寬度
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    
    // 如果螢幕寬度小於等於最大寬度，直接返回子組件
    if (screenWidth <= maxWidth) {
      return Container(
        margin: margin,
        child: child,
      );
    }

    // 大螢幕情況下，限制寬度並居中
    return Center(
      child: Container(
        width: maxWidth,
        margin: margin,
        child: child,
      ),
    );
  }
}

/// 響應式表單包裝器
/// 專門用於表單內容，提供合適的寬度限制
class ResponsiveFormWrapper extends StatelessWidget {
  final Widget child;
  final double maxWidth;
  final EdgeInsetsGeometry? padding;

  const ResponsiveFormWrapper({
    super.key,
    required this.child,
    this.maxWidth = 500.0, // 表單的最大寬度較小，更適合閱讀
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    
    // 如果螢幕寬度小於等於最大寬度，直接返回子組件
    if (screenWidth <= maxWidth) {
      return Container(
        padding: padding,
        child: child,
      );
    }

    // 大螢幕情況下，限制寬度並居中
    return Center(
      child: Container(
        width: maxWidth,
        padding: padding,
        child: child,
      ),
    );
  }
}

/// 響應式工具類
class ResponsiveUtils {
  /// 判斷是否為大螢幕
  static bool isLargeScreen(BuildContext context) {
    return MediaQuery.of(context).size.width > 800;
  }

  /// 判斷是否為中等螢幕
  static bool isMediumScreen(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width > 600 && width <= 800;
  }

  /// 判斷是否為小螢幕
  static bool isSmallScreen(BuildContext context) {
    return MediaQuery.of(context).size.width <= 600;
  }

  /// 獲取響應式的邊距
  static EdgeInsetsGeometry getResponsivePadding(BuildContext context) {
    if (isLargeScreen(context)) {
      return const EdgeInsets.symmetric(horizontal: 32.0, vertical: 16.0);
    } else if (isMediumScreen(context)) {
      return const EdgeInsets.symmetric(horizontal: 24.0, vertical: 16.0);
    } else {
      return const EdgeInsets.symmetric(horizontal: 16.0, vertical: 16.0);
    }
  }

  /// 獲取響應式的卡片邊距
  static EdgeInsetsGeometry getResponsiveCardMargin(BuildContext context) {
    if (isLargeScreen(context)) {
      return const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0);
    } else {
      return const EdgeInsets.symmetric(horizontal: 8.0, vertical: 8.0);
    }
  }

  /// 獲取響應式的列數（用於網格布局）
  static int getResponsiveColumns(BuildContext context, {int maxColumns = 3}) {
    final width = MediaQuery.of(context).size.width;
    if (width > 1200) {
      return maxColumns;
    } else if (width > 800) {
      return (maxColumns - 1).clamp(1, maxColumns);
    } else if (width > 600) {
      return (maxColumns - 2).clamp(1, maxColumns);
    } else {
      return 1;
    }
  }
}
