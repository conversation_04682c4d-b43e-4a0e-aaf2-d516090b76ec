import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../../../presentation/themes/app_theme.dart';

/// 自定義日期時間選擇器底部彈出視窗
class DateTimePickerBottomSheet extends StatefulWidget {
  final DateTime initialDateTime;
  final Function(DateTime) onDateTimeChanged;
  final String title;
  final DateTime? minDate;
  final DateTime? maxDate;
  final DateTime? defaultDateTime; // 預設時間，用於還原功能

  const DateTimePickerBottomSheet({
    Key? key,
    required this.initialDateTime,
    required this.onDateTimeChanged,
    this.title = '選擇日期和時間',
    this.minDate,
    this.maxDate,
    this.defaultDateTime,
  }) : super(key: key);

  @override
  State<DateTimePickerBottomSheet> createState() => _DateTimePickerBottomSheetState();

  /// 顯示日期時間選擇器
  static Future<DateTime?> show({
    required BuildContext context,
    required DateTime initialDateTime,
    String title = '選擇日期和時間',
    DateTime? minDate,
    DateTime? maxDate,
    DateTime? defaultDateTime,
  }) async {
    DateTime? selectedDateTime;

    await showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      isScrollControlled: true,
      builder: (BuildContext context) {
        return DateTimePickerBottomSheet(
          initialDateTime: initialDateTime,
          title: title,
          minDate: minDate,
          maxDate: maxDate,
          defaultDateTime: defaultDateTime,
          onDateTimeChanged: (DateTime newDateTime) {
            selectedDateTime = newDateTime;
          },
        );
      },
    );

    return selectedDateTime;
  }
}

class _DateTimePickerBottomSheetState extends State<DateTimePickerBottomSheet> {
  late DateTime _selectedDateTime;
  late FixedExtentScrollController _yearController;
  late FixedExtentScrollController _monthController;
  late FixedExtentScrollController _dayController;
  late FixedExtentScrollController _hourController;
  late FixedExtentScrollController _minuteController;

  @override
  void initState() {
    super.initState();
    _selectedDateTime = widget.initialDateTime;
    
    // 計算年份範圍
    final minYear = widget.minDate?.year ?? 1900;
    final maxYear = widget.maxDate?.year ?? 2100;
    
    // 初始化滾動控制器
    _yearController = FixedExtentScrollController(
      initialItem: _selectedDateTime.year - minYear,
    );
    _monthController = FixedExtentScrollController(
      initialItem: _selectedDateTime.month - 1,
    );
    _dayController = FixedExtentScrollController(
      initialItem: _selectedDateTime.day - 1,
    );
    _hourController = FixedExtentScrollController(
      initialItem: _selectedDateTime.hour,
    );
    _minuteController = FixedExtentScrollController(
      initialItem: _selectedDateTime.minute,
    );
  }

  @override
  void dispose() {
    _yearController.dispose();
    _monthController.dispose();
    _dayController.dispose();
    _hourController.dispose();
    _minuteController.dispose();
    super.dispose();
  }

  // 獲取指定年月的天數
  int _getDaysInMonth(int year, int month) {
    return DateTime(year, month + 1, 0).day;
  }

  // 獲取年份範圍
  int get _minYear => widget.minDate?.year ?? 1900;
  int get _maxYear => widget.maxDate?.year ?? 2100;
  int get _yearCount => _maxYear - _minYear + 1;

  // 更新選中的日期時間
  void _updateDateTime({
    int? year,
    int? month,
    int? day,
    int? hour,
    int? minute,
  }) {
    final newYear = year ?? _selectedDateTime.year;
    final newMonth = month ?? _selectedDateTime.month;
    var newDay = day ?? _selectedDateTime.day;
    final newHour = hour ?? _selectedDateTime.hour;
    final newMinute = minute ?? _selectedDateTime.minute;

    // 確保日期有效（處理月份變化時的日期調整）
    final maxDays = _getDaysInMonth(newYear, newMonth);
    if (newDay > maxDays) {
      newDay = maxDays;
      // 如果日期被調整，更新日期滾動控制器
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (_dayController.hasClients) {
          _dayController.animateToItem(
            newDay - 1,
            duration: const Duration(milliseconds: 200),
            curve: Curves.easeInOut,
          );
        }
      });
    }

    setState(() {
      _selectedDateTime = DateTime(newYear, newMonth, newDay, newHour, newMinute);
    });
  }

  // 還原到預設時間
  void _resetToDefault() {
    if (widget.defaultDateTime != null) {
      setState(() {
        _selectedDateTime = widget.defaultDateTime!;
      });

      // 更新所有滾動控制器到預設時間
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (_yearController.hasClients) {
          _yearController.animateToItem(
            _selectedDateTime.year - _minYear,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
          );
        }
        if (_monthController.hasClients) {
          _monthController.animateToItem(
            _selectedDateTime.month - 1,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
          );
        }
        if (_dayController.hasClients) {
          _dayController.animateToItem(
            _selectedDateTime.day - 1,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
          );
        }
        if (_hourController.hasClients) {
          _hourController.animateToItem(
            _selectedDateTime.hour,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
          );
        }
        if (_minuteController.hasClients) {
          _minuteController.animateToItem(
            _selectedDateTime.minute,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
          );
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.6,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        children: [
          // 標題和按鈕
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('取消', style: TextStyle(color: Colors.grey)),
              ),
              Expanded(
                child: Text(
                  widget.title,
                  textAlign: TextAlign.center,
                  style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ),
              TextButton(
                onPressed: () {
                  widget.onDateTimeChanged(_selectedDateTime);
                  Navigator.pop(context);
                },
                child: const Text('確定', style: TextStyle(color: AppColors.royalIndigo)),
              ),
            ],
          ),

          // 還原預設時間按鈕
          if (widget.defaultDateTime != null)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  TextButton.icon(
                    onPressed: () {
                      _resetToDefault();
                    },
                    icon: const Icon(Icons.restore, size: 16),
                    label: const Text('還原預設時間'),
                    style: TextButton.styleFrom(
                      foregroundColor: AppColors.royalIndigo,
                      textStyle: const TextStyle(fontSize: 12),
                    ),
                  ),
                ],
              ),
            ),
          const Divider(),
          
          // 當前選中的日期時間顯示
          Container(
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            margin: const EdgeInsets.only(bottom: 16),
            decoration: BoxDecoration(
              color: AppColors.royalIndigo.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: AppColors.royalIndigo.withValues(alpha: 0.3)),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.access_time, color: AppColors.royalIndigo, size: 20),
                const SizedBox(width: 8),
                Text(
                  _formatDisplayDateTime(_selectedDateTime),
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: AppColors.royalIndigo,
                  ),
                ),
              ],
            ),
          ),

          // 標籤行
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            child: Row(
              children: [
                Expanded(child: Center(child: Text('年', style: TextStyle(fontSize: 12, color: Colors.grey[600])))),
                Expanded(child: Center(child: Text('月', style: TextStyle(fontSize: 12, color: Colors.grey[600])))),
                Expanded(child: Center(child: Text('日', style: TextStyle(fontSize: 12, color: Colors.grey[600])))),
                Expanded(child: Center(child: Text('時', style: TextStyle(fontSize: 12, color: Colors.grey[600])))),
                Expanded(child: Center(child: Text('分', style: TextStyle(fontSize: 12, color: Colors.grey[600])))),
              ],
            ),
          ),
          
          const SizedBox(height: 8),
          
          // 滾動選擇器
          Expanded(
            child: Row(
              children: [
                // 年份選擇器
                Expanded(
                  child: _buildScrollPicker(
                    controller: _yearController,
                    itemCount: _yearCount,
                    itemBuilder: (index) => (_minYear + index).toString(),
                    onSelectedItemChanged: (index) {
                      _updateDateTime(year: _minYear + index);
                    },
                  ),
                ),
                // 月份選擇器
                Expanded(
                  child: _buildScrollPicker(
                    controller: _monthController,
                    itemCount: 12,
                    itemBuilder: (index) => (index + 1).toString().padLeft(2, '0'),
                    onSelectedItemChanged: (index) {
                      _updateDateTime(month: index + 1);
                    },
                  ),
                ),
                // 日期選擇器
                Expanded(
                  child: _buildScrollPicker(
                    controller: _dayController,
                    itemCount: _getDaysInMonth(_selectedDateTime.year, _selectedDateTime.month),
                    itemBuilder: (index) => (index + 1).toString().padLeft(2, '0'),
                    onSelectedItemChanged: (index) {
                      _updateDateTime(day: index + 1);
                    },
                  ),
                ),
                // 小時選擇器
                Expanded(
                  child: _buildScrollPicker(
                    controller: _hourController,
                    itemCount: 24,
                    itemBuilder: (index) => index.toString().padLeft(2, '0'),
                    onSelectedItemChanged: (index) {
                      _updateDateTime(hour: index);
                    },
                  ),
                ),
                // 分鐘選擇器
                Expanded(
                  child: _buildScrollPicker(
                    controller: _minuteController,
                    itemCount: 60,
                    itemBuilder: (index) => index.toString().padLeft(2, '0'),
                    onSelectedItemChanged: (index) {
                      _updateDateTime(minute: index);
                    },
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 建立滾動選擇器
  Widget _buildScrollPicker({
    required FixedExtentScrollController controller,
    required int itemCount,
    required String Function(int) itemBuilder,
    required Function(int) onSelectedItemChanged,
  }) {
    return CupertinoPicker(
      scrollController: controller,
      itemExtent: 40,
      onSelectedItemChanged: onSelectedItemChanged,
      children: List.generate(
        itemCount,
        (index) => Center(
          child: Text(
            itemBuilder(index),
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }

  // 格式化顯示的日期時間
  String _formatDisplayDateTime(DateTime dateTime) {
    return '${dateTime.year}年${dateTime.month.toString().padLeft(2, '0')}月${dateTime.day.toString().padLeft(2, '0')}日 '
           '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
