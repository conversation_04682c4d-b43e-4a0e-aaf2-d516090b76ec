import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../astreal.dart';
import '../../data/services/api/birth_data_service.dart';
import '../../presentation/pages/chart_page.dart';
import '../../presentation/viewmodels/recent_persons_viewmodel.dart';


/// 出生資料卡片組件
class BirthDataCard extends StatelessWidget {
  final BirthData data;
  final int index;
  final bool isSelected;
  final bool isMultiSelectMode;
  final bool isSelectionMode;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final Function(String)? onToggleSelection;
  final Function(BirthData)? onEdit;
  final Function(String)? onDelete;

  const BirthDataCard({
    super.key,
    required this.data,
    required this.index,
    this.isSelected = false,
    this.isMultiSelectMode = false,
    this.isSelectionMode = false,
    this.onTap,
    this.onLongPress,
    this.onToggleSelection,
    this.onEdit,
    this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return StyledCard(
      elevation: 2.0,
      margin: const EdgeInsets.only(bottom: 16.0),
      borderRadius: BorderRadius.circular(16.0),
      color: isSelected ? AppColors.royalIndigo.withValues(alpha: 0.1) : Colors.white,
      isSelected: isSelected,
      onTap: onTap ?? () => _handleTap(context),
      onLongPress: onLongPress ?? () => _handleLongPress(context),
      child: Row(
        children: [
          // 左側選擇框或頭像
          _buildLeadingWidget(),
          const SizedBox(width: 16),
          
          // 中間內容區域
          Expanded(
            child: _buildContentArea(),
          ),
          
          // 右側操作按鈕
          if (!isMultiSelectMode)
            _buildTrailingWidget(context),
        ],
      ),
    );
  }

  /// 構建左側組件（選擇框或頭像）
  Widget _buildLeadingWidget() {
    if (isMultiSelectMode) {
      return Checkbox(
        value: isSelected,
        onChanged: (bool? value) {
          onToggleSelection?.call(data.id);
        },
      );
    }

    return Stack(
      children: [
        Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: data.category.color,
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: data.category.color.withValues(alpha: 0.2),
                blurRadius: 6,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Center(
            child: Text(
              data.name.isNotEmpty ? data.name.substring(0, 1) : '?',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
        // 編號顯示
        Positioned(
          top: -2,
          right: -2,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              color: AppColors.royalIndigo,
              borderRadius: BorderRadius.circular(10),
              border: Border.all(color: Colors.white, width: 1),
            ),
            child: Text(
              '${index + 1}',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 10,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// 構建內容區域
  Widget _buildContentArea() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Row(
        //   children: [
        //     Text(
        //       data.id,
        //       style: const TextStyle(color: AppColors.textMedium, fontSize: 12),
        //     ),
        //   ],
        // ),
        // const SizedBox(height: 4.0),
        // 姓名和類別標籤行
        Row(
          children: [
            Expanded(
              child: Text(
               data.name,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 18,
                  color: AppColors.textDark,
                ),
              ),
            ),
            // 類別標籤
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: data.category.color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: data.category.color.withValues(alpha: 0.3),
                  width: 0.5,
                ),
              ),
              child: Text(
                data.category.displayName,
                style: TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.w500,
                  color: data.category.color,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        
        // 出生日期
        Row(
          children: [
            const Icon(Icons.calendar_today, size: 16, color: AppColors.textMedium),
            const SizedBox(width: 4),
            Expanded(
              child: Text(
                _formatBirthDateTime(),
                style: const TextStyle(color: AppColors.textMedium, fontSize: 14),
              ),
            ),
          ],
        ),
        const SizedBox(height: 4.0),

        // 出生地點
        Row(
          children: [
            const Icon(Icons.location_on, size: 16, color: AppColors.textMedium),
            const SizedBox(width: 4),
            Expanded(
              child: Text(
                data.birthPlace,
                style: const TextStyle(color: AppColors.textMedium, fontSize: 14),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),

        // 性別資訊（如果有的話）
        if (data.gender != null) ...[
          const SizedBox(height: 4.0),
          Row(
            children: [
              Icon(
                data.gender == Gender.male ? Icons.male : Icons.female,
                size: 16,
                color: AppColors.textMedium,
              ),
              const SizedBox(width: 4),
              Text(
                data.gender!.displayName,
                style: const TextStyle(color: AppColors.textMedium, fontSize: 14),
              ),
            ],
          ),
        ],
        // 備註
        if (data.notes != null && data.notes!.isNotEmpty) ...[
          const SizedBox(height: 4.0),
          Row(
            children: [
              const Icon(Icons.note, size: 16, color: AppColors.textMedium),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  data.notes!,
                  style: const TextStyle(color: AppColors.textMedium, fontSize: 14),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  /// 構建右側操作按鈕
  Widget _buildTrailingWidget(BuildContext context) {
    return PopupMenuButton<String>(
      icon: const Icon(Icons.more_vert, color: AppColors.royalIndigo),
      onSelected: (value) {
        switch (value) {
          case 'edit':
            onEdit?.call(data);
            break;
          case 'delete':
            onDelete?.call(data.id);
            break;
        }
      },
      itemBuilder: (context) => [
        const PopupMenuItem(
          value: 'edit',
          child: Row(
            children: [
              Icon(Icons.edit, size: 18),
              SizedBox(width: 8),
              Text('編輯'),
            ],
          ),
        ),
        const PopupMenuItem(
          value: 'delete',
          child: Row(
            children: [
              Icon(Icons.delete, size: 18, color: Colors.red),
              SizedBox(width: 8),
              Text('刪除', style: TextStyle(color: Colors.red)),
            ],
          ),
        ),
      ],
    );
  }

  /// 處理點擊事件
  void _handleTap(BuildContext context) {
    // 記錄選中的人物
    try {
      final recentPersonsViewModel = Provider.of<RecentPersonsViewModel>(context, listen: false);
      recentPersonsViewModel.recordSelectedPerson(data);
    } catch (e) {
      // 如果記錄失敗，不影響選擇功能
    }

    // 更新最後訪問時間
    final birthDataService = BirthDataService();
    birthDataService.updateLastAccessedTime(data.id);

    if (isMultiSelectMode) {
      onToggleSelection?.call(data.id);
    } else if (isSelectionMode) {
      // 選擇模式：返回選中的出生資料
      Navigator.pop(context, data);
    } else {
      // 直接進入基本星盤
      final chartData = ChartData(
        chartType: ChartType.natal,
        primaryPerson: data,
      );

      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => ChangeNotifierProvider(
            create: (_) => ChartViewModel.withChartData(initialChartData: chartData),
            child: ChartPage(chartData: chartData),
          ),
        ),
      );
    }
  }

  /// 處理長按事件
  void _handleLongPress(BuildContext context) {
    if (!isMultiSelectMode) {
      // 這裡需要通過回調來通知父組件進入多選模式
      // 由於我們無法直接訪問 FilesViewModel，所以需要父組件處理
      onLongPress?.call();
    }
  }

  /// 格式化日期時間
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}/${dateTime.month.toString().padLeft(2, '0')}/${dateTime.day.toString().padLeft(2, '0')} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  /// 格式化出生時間，包含不確定標記
  String _formatBirthDateTime() {
    final dateTimeStr = _formatDateTime(data.dateTime);
    if (data.isTimeUncertain) {
      return '$dateTimeStr (時間不確定)';
    }
    return dateTimeStr;
  }
}
