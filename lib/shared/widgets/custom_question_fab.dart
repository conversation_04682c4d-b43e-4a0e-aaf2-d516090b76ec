import 'package:flutter/material.dart';

import '../../astreal.dart';
import '../../presentation/pages/ai_interpretation_result_page.dart';

/// 客製化問題分析浮動按鈕組件
class CustomQuestionFAB extends StatelessWidget {
  final ChartData chartData;
  final String? customTitle;

  const CustomQuestionFAB({
    super.key,
    required this.chartData,
    this.customTitle,
  });

  @override
  Widget build(BuildContext context) {
    return FloatingActionButton.extended(
      onPressed: () => _showCustomQuestionDialog(context),
      backgroundColor: AppColors.solarAmber,
      foregroundColor: Colors.white,
      icon: const Icon(Icons.psychology),
      label: const Text(
        '客製化問題',
        style: TextStyle(
          fontWeight: FontWeight.w600,
        ),
      ),
      elevation: 6,
      extendedPadding: const EdgeInsets.symmetric(horizontal: 20),
    );
  }

  /// 顯示客製化問題對話框
  void _showCustomQuestionDialog(BuildContext context) async {
    final result = await showDialog<String>(
      context: context,
      builder: (context) => _CustomQuestionDialog(chartData: chartData),
    );

    if (result != null && result.isNotEmpty && context.mounted) {
      _navigateToCustomQuestionAnalysis(context, result);
    }
  }

  /// 導航到客製化問題分析
  void _navigateToCustomQuestionAnalysis(
      BuildContext context, String question) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AIInterpretationResultPage(
          chartData: chartData,
          interpretationTitle: customTitle ?? '客製化問題分析',
          subtitle: question,
          autoExecuteFirstQuestion: true,
          // 自動執行第一個問題
          suggestedQuestions: _generateSuggestedQuestions(question),
        ),
      ),
    );
  }

  /// 生成根據星盤類型的建議問題
  List<String> _generateSuggestedQuestions(String originalQuestion) {
    final baseQuestions = [originalQuestion]; // 用戶的原始問題會自動被分析

    switch (chartData.chartType) {
      case ChartType.natal:
        baseQuestions.addAll([
          '這個問題反映了我性格中的哪些特質？',
          '根據我的本命盤配置，我應該如何應對？',
          '這個問題與我的人生課題有什麼關聯？',
          '我的天賦和潛能如何幫助解決這個問題？',
        ]);
        break;

      case ChartType.transit:
        baseQuestions.addAll([
          '當前的行運如何影響這個問題？',
          '什麼時候是處理這個問題的最佳時機？',
          '我需要特別注意哪些行運影響？',
          '這段時期我應該採取什麼行動策略？',
        ]);
        break;

      case ChartType.synastry:
        baseQuestions.addAll([
          '這個問題如何影響我們的關係？',
          '我們的星盤配置如何幫助解決這個問題？',
          '在這個問題上我們有什麼互補的特質？',
          '我們應該如何協調不同的觀點？',
        ]);
        break;

      case ChartType.composite:
        baseQuestions.addAll([
          '這個問題對我們關係的本質有什麼意義？',
          '我們的關係如何共同面對這個挑戰？',
          '這個問題揭示了關係中的什麼課題？',
          '我們可以如何將這個問題轉化為成長機會？',
        ]);
        break;

      case ChartType.horary:
        baseQuestions.addAll([
          '這個問題的答案是肯定還是否定的？',
          '什麼時候會有結果？',
          '我需要採取什麼具體行動？',
          '有什麼潛在的障礙或幫助？',
        ]);
        break;

      case ChartType.solarReturn:
        baseQuestions.addAll([
          '這個問題在我今年的主題中扮演什麼角色？',
          '今年我應該如何處理這類問題？',
          '這個問題與我的年度目標有什麼關聯？',
          '今年在這個領域我有什麼機會和挑戰？',
        ]);
        break;

      case ChartType.eclipse:
        baseQuestions.addAll([
          '這個問題是否預示著重大的轉變？',
          '我需要釋放什麼舊有的模式？',
          '這個轉折點帶來什麼新的可能性？',
          '我應該如何準備迎接這個變化？',
        ]);
        break;

      default:
        baseQuestions.addAll([
          '這個問題在占星學上有什麼特殊意義？',
          '根據我的星盤配置，我應該如何應對？',
          '有什麼需要特別注意的時機或風險？',
          '您還有其他相關的建議嗎？',
        ]);
    }

    return baseQuestions;
  }
}

/// 客製化問題對話框
class _CustomQuestionDialog extends StatefulWidget {
  final ChartData chartData;

  const _CustomQuestionDialog({required this.chartData});

  @override
  State<_CustomQuestionDialog> createState() => _CustomQuestionDialogState();
}

class _CustomQuestionDialogState extends State<_CustomQuestionDialog> {
  final TextEditingController _questionController = TextEditingController();
  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    // 延遲聚焦，確保對話框完全顯示後再聚焦
    Future.delayed(const Duration(milliseconds: 300), () {
      if (mounted) {
        _focusNode.requestFocus();
      }
    });
  }

  @override
  void dispose() {
    _questionController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    final keyboardHeight = mediaQuery.viewInsets.bottom;
    final screenHeight = mediaQuery.size.height;

    // 計算可用高度，考慮鍵盤空間
    final availableHeight = screenHeight - keyboardHeight - 100; // 預留100px的邊距
    final maxDialogHeight = availableHeight * 0.9;
    List<String> suggestions = _getSuggestedQuestionsByChartType(widget.chartData.chartType);

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        constraints: BoxConstraints(
          maxWidth: 500,
          maxHeight: maxDialogHeight,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 可滾動的內容區域
            Flexible(
              child: SingleChildScrollView(
                padding: const EdgeInsets.fromLTRB(24, 24, 24, 16),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 標題
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color:
                                AppColors.indigoSurface.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: const Icon(
                            Icons.psychology,
                            color: AppColors.indigoSurface,
                            size: 24,
                          ),
                        ),
                        const SizedBox(width: 12),
                        const Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                '客製化問題分析',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.indigoSurface,
                                ),
                              ),
                              SizedBox(height: 4),
                              Text(
                                '請輸入您想了解的問題',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),

                    // 問題輸入框
                    TextField(
                      controller: _questionController,
                      focusNode: _focusNode,
                      maxLines: 4,
                      decoration: InputDecoration(
                        hintText:
                            '例如：\n${suggestions.join('\n')}',
                        hintStyle: TextStyle(
                          color: Colors.grey[400],
                          fontSize: 13,
                        ),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide(color: Colors.grey[300]!),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: const BorderSide(
                              color: AppColors.indigoSurface, width: 2),
                        ),
                        contentPadding: const EdgeInsets.all(16),
                        filled: true,
                        fillColor: Colors.grey[50],
                      ),
                      style: const TextStyle(fontSize: 14),
                    ),
                    const SizedBox(height: 16),

                    // 建議問題
                    _buildSuggestedQuestions(),
                  ],
                ),
              ),
            ),

            // 固定在底部的按鈕區域
            Container(
              padding: const EdgeInsets.fromLTRB(24, 16, 24, 24),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(16),
                  bottomRight: Radius.circular(16),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withValues(alpha: 0.1),
                    offset: const Offset(0, -2),
                    blurRadius: 4,
                  ),
                ],
              ),
              child: Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => Navigator.of(context).pop(),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: Colors.grey,
                        side: BorderSide(color: Colors.grey[300]!),
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                      child: const Text('取消'),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    flex: 2,
                    child: ElevatedButton.icon(
                      onPressed: () {
                        final question = _questionController.text.trim();
                        if (question.isNotEmpty) {
                          Navigator.of(context).pop(question);
                        } else {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('請輸入您的問題'),
                              backgroundColor: Colors.orange,
                            ),
                          );
                        }
                      },
                      icon: const Icon(Icons.analytics),
                      label: const Text('開始分析'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.indigoSurface,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 構建建議問題區塊
  Widget _buildSuggestedQuestions() {
    final suggestions = _getSuggestedQuestionsByChartType(widget.chartData.chartType);

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.indigoSurface.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: AppColors.indigoSurface.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.lightbulb_outline,
                color: AppColors.indigoSurface,
                size: 16,
              ),
              const SizedBox(width: 6),
              Text(
                '適合${widget.chartData.chartType.displayName}的問題類型',
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: AppColors.indigoSurface,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 4,
            children: suggestions.map((suggestion) =>
              _buildSuggestionChip(suggestion)
            ).toList(),
          ),
        ],
      ),
    );
  }

  /// 根據星盤類型獲取建議問題
  List<String> _getSuggestedQuestionsByChartType(ChartType chartType) {
    switch (chartType) {
      case ChartType.natal:
        return ['性格分析', '天賦潛能', '人生課題', '事業方向', '感情模式', '健康體質'];

      case ChartType.transit:
        return ['當前運勢', '機會把握', '挑戰應對', '時機選擇', '發展方向', '注意事項'];

      case ChartType.synastry:
        return ['關係相容性', '溝通模式', '衝突化解', '共同成長', '關係發展', '互補特質'];

      case ChartType.composite:
        return ['關係本質', '共同目標', '關係挑戰', '發展潛力', '關係使命', '和諧之道'];

      case ChartType.davison:
        return ['關係命運', '時空意義', '共同業力', '關係演變', '宇宙連結', '靈魂契約'];

      case ChartType.marks:
        return ['主觀感受', '情感投射', '關係期待', '內在需求', '感情體驗', '心理動力'];

      case ChartType.secondaryProgression:
        return ['內在成長', '心理演變', '人格發展', '生命階段', '意識轉化', '靈魂進化'];

      case ChartType.tertiaryProgression:
        return ['短期變化', '情緒波動', '心境調整', '適應能力', '當下狀態', '心理節奏'];

      case ChartType.solarArcDirection:
        return ['重大轉折', '人生變化', '命運轉向', '關鍵時刻', '生命主題', '發展契機'];

      case ChartType.solarReturn:
        return ['年度運勢', '年度主題', '發展重點', '機會挑戰', '年度目標', '注意事項'];

      case ChartType.lunarReturn:
        return ['月度情緒', '情感週期', '心境變化', '直覺感受', '內在需求', '情緒調節'];

      case ChartType.horary:
        return ['具體問題', '是否可行', '時機判斷', '結果預測', '行動建議', '注意風險'];

      case ChartType.event:
        return ['事件意義', '影響分析', '發展趨勢', '應對策略', '機會把握', '風險評估'];

      case ChartType.mundane:
        return ['社會趨勢', '政治環境', '經濟走向', '集體意識', '時代變化', '社會影響'];

      case ChartType.eclipse:
        return ['命運轉折', '重大變化', '業力清算', '新的開始', '釋放舊有', '轉化機會'];

      case ChartType.equinoxSolstice:
        return ['季節能量', '自然節奏', '能量轉換', '生活調整', '順應天時', '季節養生'];

      case ChartType.firdaria:
        return ['時序分析', '人生階段', '主導行星', '時期特色', '發展重點', '階段任務'];

      case ChartType.profection:
        return ['年度主題', '小限宮位', '時主星', '年度重點', '發展方向', '注意領域'];

      default:
        return ['性格分析', '事業發展', '感情關係', '健康狀況', '靈性成長', '人生課題'];
    }
  }

  /// 構建建議標籤
  Widget _buildSuggestionChip(String label) {
    return GestureDetector(
      onTap: () {
        // 點擊建議標籤時，在輸入框中添加相關提示
        final currentText = _questionController.text;
        if (currentText.isEmpty) {
          _questionController.text = '關於$label，我想知道...';
        }
        _questionController.selection = TextSelection.fromPosition(
          TextPosition(offset: _questionController.text.length),
        );
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: AppColors.indigoSurface.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: AppColors.indigoSurface.withValues(alpha: 0.3),
          ),
        ),
        child: Text(
          label,
          style: const TextStyle(
            fontSize: 11,
            color: AppColors.indigoSurface,
          ),
        ),
      ),
    );
  }
}
