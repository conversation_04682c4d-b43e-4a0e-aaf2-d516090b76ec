import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import '../../core/utils/logger_utils.dart';
import '../../core/utils/web_navigation_helper.dart' if (dart.library.io) '../../core/utils/web_navigation_helper_stub.dart';

/// 網頁感知的 PopScope
/// 在網頁版中正確處理返回行為
class WebAwarePopScope extends StatefulWidget {
  final Widget child;
  final bool canPop;
  final PopInvokedCallback? onPopInvoked;
  final VoidCallback? onBackPressed;
  final String? routeName;

  const WebAwarePopScope({
    super.key,
    required this.child,
    this.canPop = true,
    this.onPopInvoked,
    this.onBackPressed,
    this.routeName,
  });

  @override
  State<WebAwarePopScope> createState() => _WebAwarePopScopeState();
}

class _WebAwarePopScopeState extends State<WebAwarePopScope> {
  @override
  void initState() {
    super.initState();
    
    // 在網頁版中設置自定義返回處理
    if (kIsWeb && widget.onBackPressed != null) {
      WebNavigationHelper.setBackPressedHandler(widget.onBackPressed);
    }
    
    // 如果提供了路由名稱，更新瀏覽器 URL
    if (kIsWeb && widget.routeName != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        WebNavigationHelper.pushRoute(widget.routeName!);
        WebNavigationHelper.setPageTitle('AstReal - ${widget.routeName}');
      });
    }
  }

  @override
  void dispose() {
    // 清除自定義返回處理
    if (kIsWeb) {
      WebNavigationHelper.clearBackPressedHandler();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: widget.canPop,
      onPopInvoked: (didPop) {
        logger.d('🔙 PopScope.onPopInvoked 被調用，didPop: $didPop');
        
        // 執行原始的 onPopInvoked 回調
        if (widget.onPopInvoked != null) {
          widget.onPopInvoked!(didPop);
        }
        
        // 在網頁版中處理特殊邏輯
        if (kIsWeb && !didPop) {
          _handleWebPopInvoked();
        }
      },
      child: widget.child,
    );
  }

  /// 處理網頁版的 PopInvoked
  void _handleWebPopInvoked() {
    logger.d('🌐 處理網頁版 PopInvoked');
    
    try {
      // 如果有自定義的返回處理，執行它
      if (widget.onBackPressed != null) {
        logger.d('🔙 執行自定義返回處理');
        widget.onBackPressed!();
        return;
      }
      
      // 檢查是否可以在 Flutter 路由中返回
      if (Navigator.canPop(context)) {
        logger.d('🔙 使用 Flutter Navigator 返回');
        Navigator.pop(context);
      } else {
        logger.d('🔙 無法使用 Flutter Navigator 返回，導航到主頁面');
        _navigateToHome();
      }
    } catch (e) {
      logger.e('處理網頁版 PopInvoked 時出錯: $e');
    }
  }

  /// 導航到主頁面
  void _navigateToHome() {
    try {
      // 清除路由堆疊並導航到主頁面
      Navigator.pushNamedAndRemoveUntil(
        context,
        '/main',
        (route) => false,
      );
      
      // 更新網頁版的路由狀態
      if (kIsWeb) {
        WebNavigationHelper.replaceRoute('/main');
        WebNavigationHelper.setPageTitle('AstReal - 首頁');
      }
      
      logger.d('🏠 已導航到主頁面');
    } catch (e) {
      logger.e('導航到主頁面時出錯: $e');
    }
  }
}

/// WebAwarePopScope 的便捷構造函數
extension WebAwarePopScopeExtension on Widget {
  /// 包裝在 WebAwarePopScope 中
  Widget wrapWithWebAwarePopScope({
    bool canPop = true,
    PopInvokedCallback? onPopInvoked,
    VoidCallback? onBackPressed,
    String? routeName,
  }) {
    return WebAwarePopScope(
      canPop: canPop,
      onPopInvoked: onPopInvoked,
      onBackPressed: onBackPressed,
      routeName: routeName,
      child: this,
    );
  }

  /// 包裝在 WebAwarePopScope 中，並設置自定義返回處理
  Widget wrapWithCustomBackHandler({
    required VoidCallback onBackPressed,
    bool canPop = false,
    String? routeName,
  }) {
    return WebAwarePopScope(
      canPop: canPop,
      onBackPressed: onBackPressed,
      routeName: routeName,
      child: this,
    );
  }

  /// 包裝在 WebAwarePopScope 中，並設置路由名稱
  Widget wrapWithRouteName(String routeName) {
    return WebAwarePopScope(
      routeName: routeName,
      child: this,
    );
  }
}

/// 用於頁面的 WebAwarePopScope 包裝器
class WebAwarePageWrapper extends StatelessWidget {
  final Widget child;
  final String? title;
  final String? routeName;
  final VoidCallback? onBackPressed;
  final bool canPop;

  const WebAwarePageWrapper({
    super.key,
    required this.child,
    this.title,
    this.routeName,
    this.onBackPressed,
    this.canPop = true,
  });

  @override
  Widget build(BuildContext context) {
    Widget wrappedChild = child;

    // 包裝在 WebAwarePopScope 中
    wrappedChild = WebAwarePopScope(
      canPop: canPop,
      onBackPressed: onBackPressed,
      routeName: routeName,
      child: wrappedChild,
    );

    return wrappedChild;
  }

  /// 設置頁面標題
  void _setPageTitle() {
    if (kIsWeb && title != null) {
      WebNavigationHelper.setPageTitle('AstReal - $title');
    }
  }
}

/// 用於對話框的 WebAwarePopScope 包裝器
class WebAwareDialogWrapper extends StatelessWidget {
  final Widget child;
  final bool canPop;
  final VoidCallback? onBackPressed;

  const WebAwareDialogWrapper({
    super.key,
    required this.child,
    this.canPop = true,
    this.onBackPressed,
  });

  @override
  Widget build(BuildContext context) {
    return WebAwarePopScope(
      canPop: canPop,
      onBackPressed: onBackPressed ?? () {
        // 對話框的預設返回行為：關閉對話框
        if (Navigator.canPop(context)) {
          Navigator.pop(context);
        }
      },
      child: child,
    );
  }
}
