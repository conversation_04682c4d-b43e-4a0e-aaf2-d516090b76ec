import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:logger/logger.dart';

import '../../data/models/astrology/chart_type.dart';
import '../../data/services/api/chart_image_service.dart';
import '../../presentation/viewmodels/chart_viewmodel.dart';
import '../../shared/widgets/painter/chart_painter.dart';
import '../../shared/widgets/painter/dual_chart_painter.dart';
import '../../shared/widgets/painter/firdaria_chart_painter.dart';

/// 星盤圖片 Widget
///
/// 用於生成可儲存的星盤圖片，包含完整的星盤信息和標題
class ChartImageWidget extends StatelessWidget {
  final ChartViewModel viewModel;
  final Size chartSize;
  final bool showTitle;
  final bool showDateTime;
  final bool showLocation;
  final GlobalKey repaintBoundaryKey;

  static final Logger _logger = Logger();

  ChartImageWidget({
    Key? key,
    required this.viewModel,
    this.chartSize = const Size(3000, 3000),
    this.showTitle = true,
    this.showDateTime = false,
    this.showLocation = false,
  }) : repaintBoundaryKey = GlobalKey(),
       super(key: key);

  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(
      key: repaintBoundaryKey,
      child: Container(
        width: chartSize.width,
        height: chartSize.height,
        color: Colors.white,
        child: Column(
          children: [
            // 標題區域
            if (showTitle) _buildTitleSection(),

            // 星盤圖區域
            Expanded(
              child: _buildChartSection(),
            ),

            // 底部信息區域
            if (showDateTime || showLocation) _buildInfoSection(),
          ],
        ),
      ),
    );
  }

  /// 構建標題區域
  Widget _buildTitleSection() {
    final primaryPerson = viewModel.chartData.primaryPerson;
    final chartTypeName = _getChartTypeName();

    return Container(
      padding: const EdgeInsets.all(8),
      child: Column(
        children: [
          Text(
            primaryPerson.name,
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            chartTypeName,
            style: const TextStyle(
              fontSize: 18,
              color: Colors.black87,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// 構建星盤圖區域
  Widget _buildChartSection() {
    return Container(
      padding: const EdgeInsets.all(0),
      child: AspectRatio(
        aspectRatio: 1.0,
        child: CustomPaint(
          painter: _getChartPainter(),
          size: Size.infinite,
        ),
      ),
    );
  }

  /// 構建底部信息區域
  Widget _buildInfoSection() {
    final primaryPerson = viewModel.chartData.primaryPerson;
    final birthDate = primaryPerson.dateTime;

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          if (showDateTime) ...[
            Text(
              '出生時間：${_formatDateTime(birthDate)}',
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black87,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
          ],
          if (showLocation) ...[
            Text(
              '出生地點：${primaryPerson.birthPlace}',
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black87,
              ),
              textAlign: TextAlign.center,
            ),
            Text(
              '經緯度：${primaryPerson.longitude.toStringAsFixed(2)}°, ${primaryPerson.latitude.toStringAsFixed(2)}°',
              style: const TextStyle(
                fontSize: 12,
                color: Colors.black54,
              ),
              textAlign: TextAlign.center,
            ),
          ],
          const SizedBox(height: 8),
          const Text(
            'Generated by AstReal',
            style: TextStyle(
              fontSize: 12,
              color: Colors.black45,
              fontStyle: FontStyle.italic,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// 獲取星盤繪製器
  CustomPainter _getChartPainter() {
    final chartType = viewModel.chartType;
    final planets = viewModel.chartData.planets ?? [];
    final aspects = viewModel.chartData.aspects ?? [];
    final houses = viewModel.chartData.houses;

    if (houses == null) {
      _logger.w('宮位數據為空，無法繪製星盤');
      return _EmptyChartPainter();
    }

    switch (chartType) {
      case ChartType.mundane:
      case ChartType.natal:
      case ChartType.transit:
      case ChartType.solarReturn:
      case ChartType.lunarReturn:
        return ChartPainter(
          viewModel.chartData,
          planets,
          aspects,
          housesData: houses,
          chartType: chartType,
        );

      case ChartType.composite:
      case ChartType.synastry:
        final natalPlanets = viewModel.natalPlanetsWithAspects;
        final transitPlanets = viewModel.transitPlanetsWithAspects;
        
        return DualChartPainter(
          viewModel.chartData,
          natalPlanets,
          transitPlanets,
          aspects,
          housesData: houses,
          chartType: chartType,
        );

      case ChartType.firdaria:
        final firdariaData = viewModel.firdariaData ?? [];
        final birthDate = viewModel.chartData.primaryPerson.dateTime;
        final isDaytime = viewModel.isDaytimeBirth;
        final currentDate = viewModel.specificDate;

        return FirdariaChartPainter(
          planets,
          aspects,
          housesData: houses,
          chartType: chartType,
          firdariaData: firdariaData,
          birthDate: birthDate,
          isDaytime: isDaytime,
          selectedPeriodIndex: viewModel.selectedFirdariaPeriodIndex,
          currentDate: currentDate,
        );

      default:
        _logger.w('未知的星盤類型: $chartType');
        return _EmptyChartPainter();
    }
  }

  /// 獲取星盤類型名稱
  String _getChartTypeName() {
    return viewModel.chartType.name;
  }

  /// 格式化日期時間
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}年${dateTime.month}月${dateTime.day}日 '
           '${dateTime.hour.toString().padLeft(2, '0')}:'
           '${dateTime.minute.toString().padLeft(2, '0')}';
  }

  /// 轉換為圖片
  ///
  /// [pixelRatio] 像素比例，預設為 2.0（高解析度）
  ///
  /// 返回 Uint8List 格式的 PNG 圖片數據
  Future<Uint8List?> toImage({double pixelRatio = 2.0}) async {
    try {
      _logger.i('開始將星盤轉換為圖片');

      return await ChartImageService.captureFromKey(
        repaintBoundaryKey,
        pixelRatio: pixelRatio,
      );
    } catch (e, stackTrace) {
      _logger.e('星盤轉換為圖片失敗: $e');
      _logger.e('堆疊追蹤: $stackTrace');
      return null;
    }
  }

  /// 儲存到相簿
  /// 
  /// [fileName] 檔案名稱，如果為空則自動生成
  /// [pixelRatio] 像素比例，預設為 2.0（高解析度）
  /// 
  /// 返回是否儲存成功
  Future<bool> saveToGallery({
    String? fileName,
    double pixelRatio = 2.0,
  }) async {
    try {
      _logger.i('開始儲存星盤到相簿');

      final imageBytes = await toImage(pixelRatio: pixelRatio);
      if (imageBytes == null) {
        _logger.e('無法生成星盤圖片');
        return false;
      }

      final finalFileName = fileName ?? ChartImageService.generateFileName(
        personName: viewModel.chartData.primaryPerson.name,
        chartType: _getChartTypeName(),
      );

      return await ChartImageService.saveToGallery(
        imageBytes,
        fileName: finalFileName,
      );
    } catch (e, stackTrace) {
      _logger.e('儲存星盤到相簿失敗: $e');
      _logger.e('堆疊追蹤: $stackTrace');
      return false;
    }
  }

  /// 分享圖片
  /// 
  /// [fileName] 檔案名稱，如果為空則自動生成
  /// [subject] 分享主題
  /// [text] 分享文字
  /// [pixelRatio] 像素比例，預設為 2.0（高解析度）
  /// 
  /// 返回是否分享成功
  Future<bool> shareImage({
    String? fileName,
    String? subject,
    String? text,
    double pixelRatio = 2.0,
  }) async {
    try {
      _logger.i('開始分享星盤圖片');

      final imageBytes = await toImage(pixelRatio: pixelRatio);
      if (imageBytes == null) {
        _logger.e('無法生成星盤圖片');
        return false;
      }

      final finalFileName = fileName ?? ChartImageService.generateFileName(
        personName: viewModel.chartData.primaryPerson.name,
        chartType: _getChartTypeName(),
      );

      final finalSubject = subject ?? '${viewModel.chartData.primaryPerson.name}的${_getChartTypeName()}';
      final finalText = text ?? '來自 AstReal 的星盤圖';

      return await ChartImageService.shareImage(
        imageBytes,
        fileName: finalFileName,
        subject: finalSubject,
        text: finalText,
      );
    } catch (e, stackTrace) {
      _logger.e('分享星盤圖片失敗: $e');
      _logger.e('堆疊追蹤: $stackTrace');
      return false;
    }
  }
}

/// 空的星盤繪製器（用於錯誤情況）
class _EmptyChartPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.grey.shade300
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;

    // 繪製一個簡單的圓圈
    canvas.drawCircle(
      Offset(size.width / 2, size.height / 2),
      size.width / 2 - 10,
      paint,
    );

    // 繪製錯誤文字
    final textPainter = TextPainter(
      text: const TextSpan(
        text: '星盤數據不完整',
        style: TextStyle(
          color: Colors.grey,
          fontSize: 16,
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    textPainter.layout();
    textPainter.paint(
      canvas,
      Offset(
        (size.width - textPainter.width) / 2,
        (size.height - textPainter.height) / 2,
      ),
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
