import 'package:astreal/data/models/astrology/aspect_info.dart';
import 'package:astreal/presentation/viewmodels/chart_viewmodel.dart';
import 'package:flutter/material.dart';

import '../../core/constants/astrology_constants.dart';
import '../../data/models/astrology/planet_position.dart';
import '../../shared/widgets/arabic_points_widget.dart';

/// 古典占星資料顯示組件
///
/// 顯示行星的尊貴力量（廟旺落陷）和互容接納關係
class ClassicalAstrologyWidget extends StatelessWidget {
  final ChartViewModel viewModel;

  const ClassicalAstrologyWidget({
    Key? key,
    required this.viewModel,
  }) : super(key: key);

  /// 根據互容接納類型返回對應的顏色
  Color _getReceptionColor(ReceptionType receptionType) {
    switch (receptionType) {
      case ReceptionType.mutualReception:
        return Colors.purple; // 互相互容用紫色
      case ReceptionType.reception:
        return Colors.teal; // 互容用藍綠色
      case ReceptionType.mutualAcceptance:
        return Colors.deepPurple; // 互相接納用深紫色
      case ReceptionType.acceptance:
        return Colors.indigo; // 接納用靛藍色
      case ReceptionType.none:
      return Colors.grey; // 無接納用灰色
    }
  }

  @override
  Widget build(BuildContext context) {
    // 獲取行星位置列表
    final planets = viewModel.chartData.planets;

    // 獲取互容接納關係列表
    final allReceptions = viewModel.chartData.aspects
            ?.where((aspect) => aspect.receptionType != ReceptionType.none)
            .toList() ??
        [];

    // 過濾掉重複的互容接納關係
    final receptions = _filterDuplicateReceptions(allReceptions);

    if (planets == null || planets.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.info_outline, size: 48, color: Colors.grey),
            SizedBox(height: 16),
            Text('沒有行星數據', style: TextStyle(fontSize: 18, color: Colors.grey)),
          ],
        ),
      );
    }

    return DefaultTabController(
      length: 5,
      child: Column(
        children: [
          TabBar(
            labelColor: Theme.of(context).primaryColor,
            unselectedLabelColor: Colors.grey,
            isScrollable: true,
            tabs: const [
              Tab(text: '先天尊貴'),
              Tab(text: '後天狀態'),
              Tab(text: '映點'),
              Tab(text: '互容接納'),
              Tab(text: '特殊點'),
            ],
          ),
          Expanded(
            child: TabBarView(
              children: [
                // 先天尊貴標籤頁
                _buildDignityTab(planets),

                // 後天狀態標籤頁
                _buildAccidentalStateTab(planets),

                // 映點標籤頁
                _buildAntisciaTab(planets),

                // 互容接納關係標籤頁
                _buildReceptionTab(receptions),

                // 特殊點（阿拉伯點）標籤頁
                ArabicPointsWidget(viewModel: viewModel),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 構建先天尊貴標籤頁
  Widget _buildDignityTab(List<dynamic> planets) {
    // 過濾出指定的行星：太陽、月亮、水星、金星、火星、木星、土星、北交點
    final targetPlanetIds = [0, 1, 2, 3, 4, 5, 6, 11]; // 對應的行星ID
    final filteredPlanets =
        planets.where((planet) => targetPlanetIds.contains(planet.id)).toList();

    // 按照指定順序排序
    filteredPlanets.sort((a, b) {
      final aIndex = targetPlanetIds.indexOf(a.id);
      final bIndex = targetPlanetIds.indexOf(b.id);
      return aIndex.compareTo(bIndex);
    });

    return SingleChildScrollView(
      padding: const EdgeInsets.all(10),
      child: Card(
        elevation: 3,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: Padding(
          padding: const EdgeInsets.all(10),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                '先天尊貴表',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              const Text(
                '顯示每個尊貴等級的原本行星與此命盤符合的行星（以粗體標示）',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                ),
              ),
              const SizedBox(height: 16),
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Table(
                  border: TableBorder.all(
                    color: Colors.grey.shade300,
                    width: 1,
                  ),
                  columnWidths: const {
                    0: FixedColumnWidth(50), // 行星
                    1: FixedColumnWidth(60), // 落入星座與度數
                    2: FixedColumnWidth(30), // 廟
                    3: FixedColumnWidth(30), // 旺
                    4: FixedColumnWidth(60), // 三分
                    5: FixedColumnWidth(30), // 界
                    6: FixedColumnWidth(30), // 十
                    7: FixedColumnWidth(30), // 弱
                    8: FixedColumnWidth(30), // 陷
                  },
                  children: [
                    // 表頭
                    TableRow(
                      decoration: BoxDecoration(
                        color: Colors.grey.shade100,
                      ),
                      children: const [
                        _TableCell(
                          text: '行星',
                          isHeader: true,
                        ),
                        _TableCell(
                          text: '星座',
                          isHeader: true,
                        ),
                        _TableCell(
                          text: '廟',
                          isHeader: true,
                        ),
                        _TableCell(
                          text: '旺',
                          isHeader: true,
                        ),
                        _TableCell(
                          text: '三分',
                          isHeader: true,
                        ),
                        _TableCell(
                          text: '界',
                          isHeader: true,
                        ),
                        _TableCell(
                          text: '十',
                          isHeader: true,
                        ),
                        _TableCell(
                          text: '弱',
                          isHeader: true,
                        ),
                        _TableCell(
                          text: '陷',
                          isHeader: true,
                        ),
                      ],
                    ),
                    // 行星數據行
                    ...filteredPlanets.map(
                        (planet) => _buildPlanetRow(planet, filteredPlanets)),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 構建行星數據行
  TableRow _buildPlanetRow(dynamic planet, List<dynamic> allPlanets) {
    return TableRow(
      children: [
        // 行星名稱和符號
        _TableCell(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              CircleAvatar(
                backgroundColor: planet.color,
                radius: 10,
                child: Text(
                  planet.symbol,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontFamily: "astro_one_font",
                  ),
                ),
              ),
            ],
          ),
        ),
        // 落入星座與度數
        _TableCell(
          child: Column(
            children: [
              Text(
                planet.sign,
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 2),
              Text(
                _formatDegree(planet.longitude),
                style: const TextStyle(
                  fontSize: 10,
                  color: Colors.grey,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
        // 廟
        _TableCell(
          child: _buildDignityCell(planet, PlanetDignity.domicile, allPlanets),
        ),
        // 旺
        _TableCell(
          child:
              _buildDignityCell(planet, PlanetDignity.exaltation, allPlanets),
        ),
        // 三分
        _TableCell(
          child:
              _buildDignityCell(planet, PlanetDignity.triplicity, allPlanets),
        ),
        // 界
        _TableCell(
          child: _buildDignityCell(planet, PlanetDignity.terms, allPlanets),
        ),
        // 十
        _TableCell(
          child: _buildDignityCell(planet, PlanetDignity.decan, allPlanets),
        ),
        // 弱
        _TableCell(
          child: _buildDignityCell(planet, PlanetDignity.fall, allPlanets),
        ),
        // 陷
        _TableCell(
          child: _buildDignityCell(planet, PlanetDignity.detriment, allPlanets),
        ),
      ],
    );
  }

  /// 構建尊貴等級單元格
  Widget _buildDignityCell(dynamic currentPlanet, PlanetDignity dignityType,
      List<dynamic> allPlanets) {
    // 獲取該星座的該尊貴等級應該有的行星
    final expectedPlanets = _getExpectedPlanetsForDignity(
        currentPlanet.sign, dignityType, currentPlanet.longitude);

    if (expectedPlanets.isEmpty) {
      return const SizedBox.shrink();
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly, // 平均間隔
      crossAxisAlignment: CrossAxisAlignment.center, // 垂直置中
      children: [
        // 顯示原本應該有的行星
        ...expectedPlanets.map((expectedPlanetId) {
          final planetInfo = _getPlanetInfo(expectedPlanetId);

          // 檢查命盤中是否有這個行星，並且這個行星是否實際具有這個尊貴等級
          dynamic actualPlanet;
          try {
            actualPlanet =
                allPlanets.firstWhere((p) => p.id == expectedPlanetId);
          } catch (e) {
            actualPlanet = null;
          }

          // 判斷是否匹配：檢查當前行星是否符合該尊貴等級
          // 1. 當前行星的ID是否等於期望的行星ID
          // 2. 當前行星是否實際符合這個尊貴等級（不只是主要尊貴等級）
          bool isMatching = false;
          if (currentPlanet.id == expectedPlanetId) {
            // 檢查當前行星是否實際符合這個特定的尊貴等級
            isMatching = _checkPlanetHasDignity(currentPlanet, dignityType);
          }

          return Padding(
            padding: const EdgeInsets.symmetric(vertical: 1),
            child: Text(
              planetInfo['symbol'],
              style: TextStyle(
                fontSize: 12,
                fontFamily: "astro_one_font",
                fontWeight: isMatching ? FontWeight.bold : FontWeight.normal,
                color: isMatching
                    ? planetInfo['color']
                    : Colors.grey.shade600,
              ),
            ),
          );
        }).toList(),
      ],
    );
  }

  /// 檢查行星是否符合特定的尊貴等級
  bool _checkPlanetHasDignity(dynamic planet, PlanetDignity dignityType) {
    switch (dignityType) {
      case PlanetDignity.domicile:
        return _checkDomicile(planet);
      case PlanetDignity.exaltation:
        return _checkExaltation(planet);
      case PlanetDignity.triplicity:
        return _checkTriplicity(planet);
      case PlanetDignity.terms:
        return _checkTerms(planet);
      case PlanetDignity.decan:
        return _checkDecan(planet);
      case PlanetDignity.fall:
        return _checkFall(planet);
      case PlanetDignity.detriment:
        return _checkDetriment(planet);
      case PlanetDignity.peregrine:
        return false; // 普通狀態不需要特別標示
    }
  }

  /// 檢查行星是否為廟主星
  bool _checkDomicile(dynamic planet) {
    final domicilePlanets = _getDomicilePlanets(planet.sign);
    return domicilePlanets.contains(planet.id);
  }

  /// 檢查行星是否為旺主星
  bool _checkExaltation(dynamic planet) {
    final exaltationPlanets = _getExaltationPlanets(planet.sign);
    return exaltationPlanets.contains(planet.id);
  }

  /// 檢查行星是否為三分主星（Dorothean System）
  bool _checkTriplicity(dynamic planet) {
    if (AstrologyConstants.ZODIAC_TRIPLICITIES.containsKey(planet.sign)) {
      final triplicityRulers = AstrologyConstants.ZODIAC_TRIPLICITIES[planet.sign]!;
      final dayRuler = triplicityRulers['day'];
      final nightRuler = triplicityRulers['night'];
      final participatingRuler = triplicityRulers['participating'];

      // 檢查是否為日間主星、夜間主星或通用主星
      return planet.id == dayRuler ||
             planet.id == nightRuler ||
             planet.id == participatingRuler;
    }
    return false;
  }

  /// 檢查行星是否為界主星
  bool _checkTerms(dynamic planet) {
    final termsPlanets = _getTermsPlanets(planet.sign, planet.longitude);
    return termsPlanets.contains(planet.id);
  }

  /// 檢查行星是否為十度主星
  bool _checkDecan(dynamic planet) {
    final decanPlanets = _getDecanPlanets(planet.sign, planet.longitude);
    return decanPlanets.contains(planet.id);
  }

  /// 檢查行星是否為弱主星
  bool _checkFall(dynamic planet) {
    final fallPlanets = _getFallPlanets(planet.sign);
    return fallPlanets.contains(planet.id);
  }

  /// 檢查行星是否為陷主星
  bool _checkDetriment(dynamic planet) {
    final detrimentPlanets = _getDetrimentPlanets(planet.sign);
    return detrimentPlanets.contains(planet.id);
  }

  /// 獲取指定星座和尊貴等級應該有的行星ID列表
  List<int> _getExpectedPlanetsForDignity(
      String sign, PlanetDignity dignityType, double longitude) {
    switch (dignityType) {
      case PlanetDignity.domicile:
        return _getDomicilePlanets(sign);
      case PlanetDignity.exaltation:
        return _getExaltationPlanets(sign);
      case PlanetDignity.triplicity:
        return _getTriplicityPlanets(sign);
      case PlanetDignity.terms:
        return _getTermsPlanets(sign, longitude);
      case PlanetDignity.decan:
        return _getDecanPlanets(sign, longitude);
      case PlanetDignity.fall:
        return _getFallPlanets(sign);
      case PlanetDignity.detriment:
        return _getDetrimentPlanets(sign);
      case PlanetDignity.peregrine:
        return [];
    }
  }

  /// 格式化度數顯示
  String _formatDegree(double longitude) {
    final degreeInSign = longitude % 30.0;
    final degrees = degreeInSign.floor();
    final minutes = ((degreeInSign - degrees) * 60).floor();
    return '$degrees°${minutes.toString().padLeft(2, '0')}\'';
  }

  /// 獲取行星信息
  Map<String, dynamic> _getPlanetInfo(int planetId) {
    try {
      return AstrologyConstants.PLANETS.firstWhere((p) => p['id'] == planetId);
    } catch (e) {
      return {'name': '未知', 'symbol': '?', 'color': Colors.grey};
    }
  }

  /// 獲取廟主星
  List<int> _getDomicilePlanets(String sign) {
    for (final entry in AstrologyConstants.PLANET_DIGNITIES.entries) {
      final dignities = entry.value;
      final domicileStr = dignities[AstrologyConstants.DOMICILE] ?? '';
      final domicileSigns = domicileStr.split(',');
      if (domicileSigns.contains(sign)) {
        return [entry.key];
      }
    }
    return [];
  }

  /// 獲取旺主星
  List<int> _getExaltationPlanets(String sign) {
    for (final entry in AstrologyConstants.PLANET_DIGNITIES.entries) {
      final dignities = entry.value;
      final exaltationStr = dignities[AstrologyConstants.EXALTATION] ?? '';
      if (exaltationStr == sign) {
        return [entry.key];
      }
    }
    return [];
  }

  /// 獲取三分主星（Dorothean System）
  List<int> _getTriplicityPlanets(String sign) {
    if (AstrologyConstants.ZODIAC_TRIPLICITIES.containsKey(sign)) {
      final triplicityRulers = AstrologyConstants.ZODIAC_TRIPLICITIES[sign]!;
      final dayRuler = triplicityRulers['day'];
      final nightRuler = triplicityRulers['night'];
      final participatingRuler = triplicityRulers['participating'];
      final result = <int>[];

      // 添加日間主星
      if (dayRuler != null) result.add(dayRuler);

      // 添加夜間主星（如果與日間主星不同）
      if (nightRuler != null && nightRuler != dayRuler) result.add(nightRuler);

      // 添加通用主星（如果與日間和夜間主星都不同）
      if (participatingRuler != null &&
          participatingRuler != dayRuler &&
          participatingRuler != nightRuler) {
        result.add(participatingRuler);
      }

      return result;
    }
    return [];
  }

  /// 獲取界主星
  List<int> _getTermsPlanets(String sign, double longitude) {
    if (AstrologyConstants.ZODIAC_TERMS.containsKey(sign)) {
      final terms = AstrologyConstants.ZODIAC_TERMS[sign]!;
      final degreeInSign = longitude % 30.0;

      // 找到對應度數的界主星
      for (final entry in terms.entries) {
        final startDegree = double.parse(entry.key);
        final planetId = entry.value;

        // 找到下一個度數作為結束度數
        final allDegrees = terms.keys.map((k) => double.parse(k)).toList()
          ..sort();
        final currentIndex = allDegrees.indexOf(startDegree);
        final endDegree = currentIndex < allDegrees.length - 1
            ? allDegrees[currentIndex + 1]
            : 30.0;

        if (degreeInSign >= startDegree && degreeInSign < endDegree) {
          return [planetId];
        }
      }
    }
    return [];
  }

  /// 獲取十度主星
  List<int> _getDecanPlanets(String sign, double longitude) {
    if (AstrologyConstants.ZODIAC_DECANS.containsKey(sign)) {
      final decans = AstrologyConstants.ZODIAC_DECANS[sign]!;
      final degreeInSign = longitude % 30.0;

      if (degreeInSign >= 0 && degreeInSign < 10) {
        final planetId = decans['0.0'];
        return planetId != null ? [planetId] : [];
      } else if (degreeInSign >= 10 && degreeInSign < 20) {
        final planetId = decans['10.0'];
        return planetId != null ? [planetId] : [];
      } else if (degreeInSign >= 20 && degreeInSign < 30) {
        final planetId = decans['20.0'];
        return planetId != null ? [planetId] : [];
      }
    }
    return [];
  }

  /// 獲取弱主星
  List<int> _getFallPlanets(String sign) {
    for (final entry in AstrologyConstants.PLANET_DIGNITIES.entries) {
      final dignities = entry.value;
      final fallStr = dignities[AstrologyConstants.FALL] ?? '';
      if (fallStr == sign) {
        return [entry.key];
      }
    }
    return [];
  }

  /// 獲取陷主星
  List<int> _getDetrimentPlanets(String sign) {
    for (final entry in AstrologyConstants.PLANET_DIGNITIES.entries) {
      final dignities = entry.value;
      final detrimentStr = dignities[AstrologyConstants.DETRIMENT] ?? '';
      final detrimentSigns = detrimentStr.split(',');
      if (detrimentSigns.contains(sign)) {
        return [entry.key];
      }
    }
    return [];
  }

  /// 構建互容接納關係標籤頁
  Widget _buildReceptionTab(List<AspectInfo> receptions) {
    if (receptions.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.info_outline, size: 48, color: Colors.grey),
            SizedBox(height: 16),
            Text('沒有互容接納關係',
                style: TextStyle(fontSize: 18, color: Colors.grey)),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: receptions.length,
      itemBuilder: (context, index) {
        final reception = receptions[index];
        final receptionColor = _getReceptionColor(reception.receptionType);

        return Card(
          color: Colors.white,
          margin: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
          elevation: 3,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
            side: BorderSide(
              color: receptionColor.withOpacity(0.3),
              width: 1.5,
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(10),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 互容接納符號圓形圖標
                CircleAvatar(
                  backgroundColor: receptionColor,
                  radius: 24,
                  child: const Icon(
                    Icons.swap_horiz,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${reception.planet1.name} ↔ ${reception.planet2.name}',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Icon(
                            Icons.swap_horiz,
                            size: 16,
                            color: receptionColor,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            reception.getReceptionText(),
                            style: TextStyle(
                              fontSize: 16,
                              color: receptionColor,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        reception.receptionDescription ?? '',
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 8),
                      _buildPlanetDetails(reception.planet1),
                      const SizedBox(height: 4),
                      _buildPlanetDetails(reception.planet2),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// 構建行星詳細信息
  Widget _buildPlanetDetails(dynamic planet) {
    return Row(
      children: [
        CircleAvatar(
          backgroundColor: planet.color.withOpacity(0.2),
          radius: 12,
          child: Text(
            planet.symbol,
            style: TextStyle(
              color: planet.color,
              fontSize: 14,
              fontFamily: "astro_one_font",
            ),
          ),
        ),
        const SizedBox(width: 8),
        Text(
          '${planet.name}: ${planet.sign}',
          style: const TextStyle(
            fontSize: 14,
            color: Colors.black54,
          ),
        ),
      ],
    );
  }

  /// 過濾掉重複的互容接納關係
  ///
  /// 當兩個行星之間存在互容接納關係時，可能會有兩條記錄：
  /// 1. 行星A與行星B的接納關係
  /// 2. 行星B與行星A的接納關係
  ///
  /// 這個方法會過濾掉重複的記錄，只保留一條
  List<AspectInfo> _filterDuplicateReceptions(List<AspectInfo> receptions) {
    final filteredReceptions = <AspectInfo>[];
    final processedPairs = <String>{};

    for (final reception in receptions) {
      // 創建行星對的唯一標識符（按ID排序以確保一致性）
      final planetIds = [reception.planet1.id, reception.planet2.id];
      planetIds.sort(); // 排序以確保 [A,B] 和 [B,A] 產生相同的標識符
      final pairKey = planetIds.join('-');

      // 如果這對行星還沒有處理過，則添加到結果中
      if (!processedPairs.contains(pairKey)) {
        filteredReceptions.add(reception);
        processedPairs.add(pairKey);
      }
    }

    return filteredReceptions;
  }

  /// 獲取行星尊貴力量的描述
  String _getDignityDescription(dynamic planet) {
    switch (planet.dignity) {
      case PlanetDignity.domicile:
        return '${planet.name}在${planet.sign}為廟，表示其力量最強，能充分發揮其特質。';
      case PlanetDignity.exaltation:
        return '${planet.name}在${planet.sign}為旺，表示其力量增強，能良好發揮其特質。';
      case PlanetDignity.triplicity:
        return '${planet.name}在${planet.sign}為三分主星，表示其力量良好。';
      case PlanetDignity.terms:
        return '${planet.name}在${planet.sign}為界主星，表示其力量適中。';
      case PlanetDignity.decan:
        return '${planet.name}在${planet.sign}為十度主星，表示其力量輕微。';
      case PlanetDignity.detriment:
        return '${planet.name}在${planet.sign}為陷，表示其力量受阻，難以發揮其特質。';
      case PlanetDignity.fall:
        return '${planet.name}在${planet.sign}為弱，表示其力量減弱，不易發揮其特質。';
      case PlanetDignity.peregrine:
      default:
        return '${planet.name}在${planet.sign}為外來的狀態，力量適中。';
    }
  }

  /// 構建後天狀態標籤頁
  Widget _buildAccidentalStateTab(List<dynamic> planets) {
    // 過濾出指定的行星：太陽、月亮、水星、金星、火星、木星、土星、北交點
    final targetPlanetIds = [0, 1, 2, 3, 4, 5, 6, 11]; // 對應的行星ID
    final filteredPlanets =
        planets.where((planet) => targetPlanetIds.contains(planet.id)).toList();

    // 按照指定順序排序
    filteredPlanets.sort((a, b) {
      final aIndex = targetPlanetIds.indexOf(a.id);
      final bIndex = targetPlanetIds.indexOf(b.id);
      return aIndex.compareTo(bIndex);
    });

    return SingleChildScrollView(
      padding: const EdgeInsets.all(10),
      child: Card(
        elevation: 3,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: Padding(
          padding: const EdgeInsets.all(10),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                '後天狀態表',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              const Text(
                '顯示每個行星的後天狀態，包括宮位、速度、得時失時等資訊',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                ),
              ),
              const SizedBox(height: 16),
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Table(
                  border: TableBorder.all(
                    color: Colors.grey.shade300,
                    width: 1,
                  ),
                  columnWidths: const {
                    0: FixedColumnWidth(45),  // 行星
                    1: FixedColumnWidth(55), // 星座度數
                    2: FixedColumnWidth(45),  // 宮位
                    3: FixedColumnWidth(50),  // 速度
                    4: FixedColumnWidth(45),  // 得時失時
                    5: FixedColumnWidth(45),  // 東出西入
                    6: FixedColumnWidth(45), // 太陽距離狀態
                    7: FixedColumnWidth(45),  // 是否喜樂
                  },
                  children: [
                    // 表頭
                    TableRow(
                      decoration: BoxDecoration(
                        color: Colors.grey.shade100,
                      ),
                      children: const [
                        _TableCell(
                          text: '行星',
                          isHeader: true,
                        ),
                        _TableCell(
                          text: '星座',
                          isHeader: true,
                        ),
                        _TableCell(
                          text: '宮位',
                          isHeader: true,
                        ),
                        _TableCell(
                          text: '速度',
                          isHeader: true,
                        ),
                        _TableCell(
                          text: '失時',
                          isHeader: true,
                        ),
                        _TableCell(
                          text: '東出西入',
                          isHeader: true,
                        ),
                        _TableCell(
                          text: '太陽距離',
                          isHeader: true,
                        ),
                        _TableCell(
                          text: '喜樂',
                          isHeader: true,
                        ),
                      ],
                    ),
                    // 行星數據行
                    ...filteredPlanets.map((planet) => _buildAccidentalStateRow(planet)),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 構建後天狀態數據行
  TableRow _buildAccidentalStateRow(dynamic planet) {
    return TableRow(
      children: [
        // 行星名稱和符號
        _TableCell(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              CircleAvatar(
                backgroundColor: planet.color,
                radius: 10,
                child: Text(
                  planet.symbol,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontFamily: "astro_one_font",
                  ),
                ),
              ),
              // const SizedBox(height: 4),
              // Text(
              //   planet.name,
              //   style: const TextStyle(
              //     fontSize: 11,
              //     fontWeight: FontWeight.w500,
              //   ),
              //   textAlign: TextAlign.center,
              //   maxLines: 2,
              //   overflow: TextOverflow.ellipsis,
              // ),
            ],
          ),
        ),
        // 星座度數
        _TableCell(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                planet.sign,
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 2),
              Text(
                _formatDegree(planet.longitude),
                style: const TextStyle(
                  fontSize: 10,
                  color: Colors.grey,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
        // 宮位
        _TableCell(
          text: _getHouseText(planet.house),
        ),
        // 速度
        _TableCell(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                _getSpeedStatus(planet),
                style: TextStyle(
                  fontSize: 11,
                  fontWeight: FontWeight.w500,
                  color: _getSpeedStatusColor(planet),
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 2),
              Text(
                _formatSpeed(planet.longitudeSpeed),
                style: const TextStyle(
                  fontSize: 9,
                  color: Colors.grey,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
        // 得時失時
        _TableCell(
          child: Text(
            _getSeasonalStatus(planet),
            style: TextStyle(
              fontSize: 11,
              fontWeight: FontWeight.w500,
              color: _getSeasonalStatusColor(planet),
            ),
            textAlign: TextAlign.center,
          ),
        ),
        // 東出西入
        _TableCell(
          child: Text(
            _getOrientalOccidentalStatus(planet),
            style: TextStyle(
              fontSize: 11,
              fontWeight: FontWeight.w500,
              color: _getOrientalOccidentalStatusColor(planet),
            ),
            textAlign: TextAlign.center,
          ),
        ),
        // 太陽距離狀態
        _TableCell(
          child: Text(
            _getSolarDistanceStatus(planet),
            style: TextStyle(
              fontSize: 11,
              fontWeight: FontWeight.w500,
              color: _getSolarDistanceStatusColor(planet),
            ),
            textAlign: TextAlign.center,
          ),
        ),
        // 是否喜樂
        _TableCell(
          child: Text(
            _getJoyStatus(planet),
            style: TextStyle(
              fontSize: 11,
              fontWeight: FontWeight.w500,
              color: _getJoyStatusColor(planet),
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ],
    );
  }

  /// 獲取宮位文字
  String _getHouseText(int? house) {
    if (house == null) return '-';
    return '$house';
  }

  /// 獲取速度狀態
  String _getSpeedStatus(dynamic planet) {
    if (planet.longitudeSpeed == null) return '-';

    if (planet.longitudeSpeed < 0) {
      return '逆行';
    } else if (planet.longitudeSpeed == 0) {
      return '停滯';
    } else {
      // 檢查是否為正常速度
      final normalSpeed = _getNormalSpeed(planet.id);
      if (normalSpeed != null) {
        final speedRatio = planet.longitudeSpeed / normalSpeed;
        if (speedRatio > 1.2) {
          return '快';
        } else if (speedRatio < 0.8) {
          return '慢';
        } else {
          return '平均';
        }
      }
      return '順行';
    }
  }

  /// 獲取速度狀態顏色
  Color _getSpeedStatusColor(dynamic planet) {
    final status = _getSpeedStatus(planet);
    switch (status) {
      case '逆行':
        return Colors.red;
      case '停滯':
        return Colors.orange;
      case '快':
        return Colors.blue;
      case '慢':
        return Colors.purple;
      case '平均':
        return Colors.green;
      default:
        return Colors.black87;
    }
  }

  /// 格式化速度顯示
  String _formatSpeed(double? longitudeSpeed) {
    if (longitudeSpeed == null) return '-';
    return '${longitudeSpeed.toStringAsFixed(2)}°/日';
  }

  /// 獲取行星正常速度
  double? _getNormalSpeed(int planetId) {
    // 行星平均每日運行速度（度/日）
    const normalSpeeds = {
      0: 0.9856, // 太陽
      1: 13.176, // 月亮
      2: 0.9856, // 1.383,  // 水星 平均視速度、簡化占星邏輯
      3: 0.9856, // 1.602,  // 金星 平均視速度、簡化占星邏輯
      4: 0.524,  // 火星
      5: 0.083,  // 木星
      6: 0.033,  // 土星
      11: -0.053, // 北交點（逆行）
    };
    return normalSpeeds[planetId];
  }

  /// 獲取得時失時狀態
  /// 根據完整的得失時判斷法則：
  /// 1. 日生盤，晝星落晝、落陽性星座、落合適日相位置，則完全得時
  /// 2. 夜生盤，夜星落夜位、落陰性星座、落合適日相位置，則完全得時
  /// 3. 日生盤，夜星落晝、落陽性星座、落不當日相位置，則完全失時
  /// 4. 夜生盤，晝星落夜位、落陰性星座、落不當日相位置，則完全失時
  /// 5. 水星中性，先太陽東出為晝星，後太陽西入為夜星，相應斷之
  String _getSeasonalStatus(dynamic planet) {
    // 判斷是否為日生盤（太陽在地平線上）
    final isDayChart = _isDayChart();

    // 根據行星ID判斷晝夜星分類
    bool isDayPlanet = _isDayPlanet(planet.id);

    // 判斷行星是否在晝位（與太陽同一半球）
    final isDiurnallyPlaced = _isDiurnallyPlaced(planet);

    // 判斷行星所在星座的陰陽性
    bool isPositiveSign = _isPositiveSign(planet.sign);

    if (isDayChart) {
      // 日生盤的判斷
      if (isDayPlanet && isDiurnallyPlaced && isPositiveSign) {
        return '得時'; // 晝星落晝、落陽性星座、落合適日相位置
      } else if (!isDayPlanet && isDiurnallyPlaced && isPositiveSign) {
        return '失時'; // 夜星落晝、落陽性星座、落不當日相位置
      } else {
        return '失時'; // 其他情況
      }
    } else {
      // 夜生盤的判斷
      if (!isDayPlanet && !isDiurnallyPlaced && !isPositiveSign) {
        return '得時'; // 夜星落夜位、落陰性星座、落合適日相位置
      } else if (isDayPlanet && !isDiurnallyPlaced && !isPositiveSign) {
        return '失時'; // 晝星落夜位、落陰性星座、落不當日相位置
      } else {
        return '失時'; // 其他情況
      }
    }
  }

  /// 判斷是否為日生盤（太陽在地平線上）
  bool _isDayChart() {
    final sun = _getSunPosition();
    if (sun == null) return true; // 默認為日生盤

    return _isAboveHorizon(sun);
  }

  /// 判斷行星是否在地平線上
  bool _isAboveHorizon(dynamic planet) {
    if (planet.house == null) return true; // 默認在地平線上

    // 宮位 1-6 為地平線上，7-12 為地平線下
    return planet.house >= 1 && planet.house <= 6;
  }

  /// 判斷行星是否在晝位（與太陽位於同一半球）
  bool _isDiurnallyPlaced(dynamic planet) {
    final sun = _getSunPosition();
    if (sun == null) return true; // 默認為晝位

    final sunAboveHorizon = _isAboveHorizon(sun);
    final planetAboveHorizon = _isAboveHorizon(planet);

    // 與太陽位於同一半球即為晝位
    return sunAboveHorizon == planetAboveHorizon;
  }

  /// 判斷是否為晝星
  /// 根據托勒密《四書》的分類：
  /// 晝星：太陽、木星、土星
  /// 夜星：月亮、金星、火星
  /// 水星：中性，先太陽東出為晝星，後太陽西入為夜星
  bool _isDayPlanet(int planetId) {
    switch (planetId) {
      case 0: // 太陽
      case 5: // 木星
      case 6: // 土星
        return true;
      case 1: // 月亮
      case 3: // 金星
      case 4: // 火星
        return false;
      case 2: // 水星
        return _isMercuryDayPlanet(); // 水星特殊判斷
      default:
        return true;
    }
  }

  /// 判斷水星是否為晝星
  /// 水星中性，先太陽東出為晝星，後太陽西入為夜星
  bool _isMercuryDayPlanet() {
    // 獲取水星的東出西入狀態
    final mercury = _getMercuryPosition();
    if (mercury == null) return true; // 默認為晝星

    final orientalStatus = _getOrientalOccidentalStatus(mercury);
    return orientalStatus == '東出'; // 東出為晝星，西入為夜星
  }

  /// 獲取水星位置
  dynamic _getMercuryPosition() {
    try {
      final planets = viewModel.natalPlanets;
      if (planets != null && planets.isNotEmpty) {
        for (final planet in planets) {
          if (planet.id == 2) { // 水星ID為2
            return planet;
          }
        }
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// 判斷星座是否為陽性星座
  /// 陽性星座：火象和風象星座（牧羊座、獅子座、射手座、雙子座、天秤座、水瓶座）
  /// 陰性星座：水象和土象星座（巨蟹座、天蠍座、雙魚座、金牛座、處女座、摩羯座）
  bool _isPositiveSign(String sign) {
    const positiveSignsZh = [
      '牡羊座', '獅子座', '射手座', // 火象星座
      '雙子座', '天秤座', '水瓶座', // 風象星座
    ];

    const positiveSignsEn = [
      'Aries', 'Leo', 'Sagittarius', // 火象星座
      'Gemini', 'Libra', 'Aquarius', // 風象星座
    ];

    return positiveSignsZh.contains(sign) || positiveSignsEn.contains(sign);
  }

  /// 獲取得時失時狀態顏色
  Color _getSeasonalStatusColor(dynamic planet) {
    final status = _getSeasonalStatus(planet);
    switch (status) {
      case '得時':
        return Colors.green;
      case '失時':
        return Colors.red;
      case '中性':
        return Colors.orange;
      default:
        return Colors.black87;
    }
  }

  /// 獲取東出西入狀態
  /// 東出：行星在太陽之前東升（黎明前在東邊天空可見）
  /// 西入：行星在太陽之後西沉（太陽西沉時在西邊天空可見）
  String _getOrientalOccidentalStatus(dynamic planet) {
    if (planet.id == 0) return '-'; // 太陽本身不判斷

    final sun = _getSunPosition();
    if (sun == null) return '-';

    double planetLon = planet.longitude % 360;
    double sunLon = sun.longitude % 360;

    double clockwiseDiff = (planetLon - sunLon + 360) % 360;
    double counterClockwiseDiff = (sunLon - planetLon + 360) % 360;

    return clockwiseDiff < counterClockwiseDiff ? '西入' : '東出';
  }
  /// 獲取太陽位置
  dynamic _getSunPosition() {
    try {
      // 從 viewModel 獲取當前的行星數據
      final planets = viewModel.chartData.planets;
      if (planets!.isNotEmpty) {
        // 查找太陽（ID = 0）
        for (final planet in planets) {
          if (planet.id == 0) {
            return planet;
          }
        }
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// 獲取東出西入狀態顏色
  Color _getOrientalOccidentalStatusColor(dynamic planet) {
    final status = _getOrientalOccidentalStatus(planet);
    switch (status) {
      case '東出':
        return Colors.blue;
      case '西入':
        return Colors.purple;
      default:
        return Colors.black87;
    }
  }

  /// 獲取太陽距離狀態
  String _getSolarDistanceStatus(dynamic planet) {
    if (planet.id == 0) return '-'; // 太陽本身

    // 獲取太陽位置
    final sun = _getSunPosition();
    if (sun == null) return '-';

    // 計算行星與太陽的角度距離
    double distance = _calculateAngularDistance(planet.longitude, sun.longitude);

    // 判斷太陽距離狀態
    if (distance <= 0.5) {
      return '核心內'; // Cazimi：與太陽距離在 0.5° 以內
    } else if (distance <= 8.5) {
      return '焦傷'; // Combustion：與太陽距離在 8.5° 以內
    } else if (distance <= 17.0) {
      return '在光束下'; // Under the Beams：與太陽距離在 17° 以內
    } else {
      return '正常'; // 正常距離
    }
  }

  /// 計算兩個經度之間的角度距離
  double _calculateAngularDistance(double longitude1, double longitude2) {
    double diff = (longitude1 - longitude2).abs();

    // 確保距離是最短的弧度距離
    if (diff > 180) {
      diff = 360 - diff;
    }

    return diff;
  }

  /// 獲取太陽距離狀態顏色
  Color _getSolarDistanceStatusColor(dynamic planet) {
    final status = _getSolarDistanceStatus(planet);
    switch (status) {
      case '核心內':
        return Colors.purple; // 在心是最強的狀態，用紫色表示
      case '焦傷':
        return Colors.red; // 燃燒是不利狀態，用紅色表示
      case '在光束下':
        return Colors.orange; // 在光束下是輕微不利，用橙色表示
      case '正常':
        return Colors.green; // 正常距離，用綠色表示
      default:
        return Colors.black87;
    }
  }

  /// 獲取喜樂狀態
  String _getJoyStatus(dynamic planet) {
    // 行星喜樂宮位
    const joyHouses = {
      0: 9,  // 太陽喜樂第9宮
      1: 3,  // 月亮喜樂第3宮
      2: 1,  // 水星喜樂第1宮
      3: 5,  // 金星喜樂第5宮
      4: 6,  // 火星喜樂第6宮
      5: 11, // 木星喜樂第11宮
      6: 12, // 土星喜樂第12宮
    };

    final joyHouse = joyHouses[planet.id];
    if (joyHouse == null) return '-';

    return planet.house == joyHouse ? '喜樂' : '非喜樂';
  }

  /// 獲取喜樂狀態顏色
  Color _getJoyStatusColor(dynamic planet) {
    final status = _getJoyStatus(planet);
    switch (status) {
      case '喜樂':
        return Colors.green;
      case '非喜樂':
        return Colors.grey;
      default:
        return Colors.black87;
    }
  }

  /// 構建映點標籤頁
  Widget _buildAntisciaTab(List<dynamic> planets) {
    // 過濾出指定的行星：太陽、月亮、水星、金星、火星、木星、土星、北交點
    final targetPlanetIds = [0, 1, 2, 3, 4, 5, 6, 11]; // 對應的行星ID
    final filteredPlanets =
        planets.where((planet) => targetPlanetIds.contains(planet.id)).toList();

    // 按照指定順序排序
    filteredPlanets.sort((a, b) {
      final aIndex = targetPlanetIds.indexOf(a.id);
      final bIndex = targetPlanetIds.indexOf(b.id);
      return aIndex.compareTo(bIndex);
    });

    return SingleChildScrollView(
      padding: const EdgeInsets.all(10),
      child: Card(
        elevation: 3,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: Padding(
          padding: const EdgeInsets.all(10),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                '映點與反映點',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              const Text(
                '顯示每個行星的映點（Antiscia）和反映點（Contra-Antiscia）位置',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                ),
              ),
              const SizedBox(height: 16),
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Table(
                  border: TableBorder.all(
                    color: Colors.grey.shade300,
                    width: 1,
                  ),
                  columnWidths: const {
                    0: FixedColumnWidth(40), // 行星
                    1: FixedColumnWidth(60), // 原位置
                    2: FixedColumnWidth(70), // 映點位置
                    3: FixedColumnWidth(70), // 反映點位置
                    4: FixedColumnWidth(70), // 映點相位
                    5: FixedColumnWidth(70), // 反映點相位
                  },
                  children: [
                    // 表頭
                    TableRow(
                      decoration: BoxDecoration(
                        color: Colors.grey.shade100,
                      ),
                      children: const [
                        _TableCell(
                          text: '行星',
                          isHeader: true,
                        ),
                        _TableCell(
                          text: '原位置',
                          isHeader: true,
                        ),
                        _TableCell(
                          text: '映點位置',
                          isHeader: true,
                        ),
                        _TableCell(
                          text: '反映點位置',
                          isHeader: true,
                        ),
                        _TableCell(
                          text: '映點匹配',
                          isHeader: true,
                        ),
                        _TableCell(
                          text: '反映點匹配',
                          isHeader: true,
                        ),
                      ],
                    ),
                    // 行星數據行
                    ...filteredPlanets.map((planet) => _buildAntisciaRow(planet, filteredPlanets)),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 構建映點反映點數據行
  TableRow _buildAntisciaRow(dynamic planet, List<dynamic> allPlanets) {
    final antisciaPosition = _calculateAntiscia(planet.longitude);
    final contraAntisciaPosition = _calculateContraAntiscia(planet.longitude);

    final antisciaMatches = _findAntisciaMatches(contraAntisciaPosition, allPlanets, planet);
    final contraAntisciaMatches = _findContraAntisciaMatches(antisciaPosition, allPlanets, planet);

    return TableRow(
      children: [
        // 行星名稱和符號
        _TableCell(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              CircleAvatar(
                backgroundColor: planet.color,
                radius: 12,
                child: Text(
                  planet.symbol,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontFamily: "astro_one_font",
                  ),
                ),
              ),
              const SizedBox(height: 4),
              Text(
                planet.name,
                style: const TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
        // 原位置
        _TableCell(
          child: Column(
            children: [
              Text(
                planet.sign,
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 2),
              Text(
                _formatDegree(planet.longitude),
                style: const TextStyle(
                  fontSize: 10,
                  color: Colors.grey,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
        // 映點位置
        _TableCell(
          child: Column(
            children: [
              Text(
                _getSignFromLongitude(contraAntisciaPosition),
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: Colors.blue,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 2),
              Text(
                _formatDegree(antisciaPosition),
                style: const TextStyle(
                  fontSize: 10,
                  color: Colors.blue,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
        // 反映點位置
        _TableCell(
          child: Column(
            children: [
              Text(
                _getSignFromLongitude(antisciaPosition),
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: Colors.purple,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 2),
              Text(
                _formatDegree(contraAntisciaPosition),
                style: const TextStyle(
                  fontSize: 10,
                  color: Colors.purple,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
        // 映點匹配
        _TableCell(
          child: Column(
            children: antisciaMatches.map((match) =>
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 1),
                child: match['type'] == 'none'
                  ? Text(
                      match['text'],
                      style: const TextStyle(
                        fontSize: 10,
                        color: Colors.grey,
                      ),
                      textAlign: TextAlign.center,
                    )
                  : Container(
                      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.blue.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(4),
                        border: Border.all(color: Colors.blue, width: 1),
                      ),
                      child: Column(
                        children: [
                          const Icon(
                            Icons.compare_arrows,
                            size: 12,
                            color: Colors.blue,
                          ),
                          const SizedBox(height: 2),
                          Text(
                            match['text'],
                            style: const TextStyle(
                              fontSize: 9,
                              color: Colors.blue,
                              fontWeight: FontWeight.bold,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const Text(
                            '映點',
                            style: TextStyle(
                              fontSize: 8,
                              color: Colors.blue,
                              fontWeight: FontWeight.w500,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
              ),
            ).toList(),
          ),
        ),
        // 反映點匹配
        _TableCell(
          child: Column(
            children: contraAntisciaMatches.map((match) =>
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 1),
                child: match['type'] == 'none'
                  ? Text(
                      match['text'],
                      style: const TextStyle(
                        fontSize: 10,
                        color: Colors.grey,
                      ),
                      textAlign: TextAlign.center,
                    )
                  : Container(
                      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.purple.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(4),
                        border: Border.all(color: Colors.purple, width: 1),
                      ),
                      child: Column(
                        children: [
                          const Icon(
                            Icons.swap_horiz,
                            size: 12,
                            color: Colors.purple,
                          ),
                          const SizedBox(height: 2),
                          Text(
                            match['text'],
                            style: const TextStyle(
                              fontSize: 9,
                              color: Colors.purple,
                              fontWeight: FontWeight.bold,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const Text(
                            '反映點',
                            style: TextStyle(
                              fontSize: 8,
                              color: Colors.purple,
                              fontWeight: FontWeight.w500,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
              ),
            ).toList(),
          ),
        ),
      ],
    );
  }

  /// 計算映點（Antiscia）位置
  /// 映點是相對於巨蟹座0度和摩羯座0度軸線的鏡像位置
  double _calculateAntiscia(double longitude) {
    // 映點計算公式：30° - (longitude % 30°) + 對應的映點星座起始度數
    // 巨蟹座0度 = 90度，摩羯座0度 = 270度

    // 將經度標準化到0-360度範圍
    longitude = longitude % 360;

    // 映點軸線：巨蟹座0度（90°）和摩羯座0度（270°）
    // 計算相對於至點軸線的映點
    double antiscia;

    if (longitude >= 0 && longitude < 90) {
      // 第一象限：映射到第二象限
      antiscia = 180 - longitude;
    } else if (longitude >= 90 && longitude < 180) {
      // 第二象限：映射到第一象限
      antiscia = 180 - longitude;
    } else if (longitude >= 180 && longitude < 270) {
      // 第三象限：映射到第四象限
      antiscia = 360 - longitude;
    } else {
      // 第四象限：映射到第三象限
      antiscia = 360 - longitude;
    }

    return antiscia % 360;
  }

  /// 計算反映點（Contra-Antiscia）位置
  /// 反映點是映點再加180度
  double _calculateContraAntiscia(double longitude) {
    final antiscia = _calculateAntiscia(longitude);
    return (antiscia + 180) % 360;
  }

  /// 根據經度獲取星座名稱
  String _getSignFromLongitude(double longitude) {
    final signs = [
      '牡羊座', '金牛座', '雙子座', '巨蟹座', '獅子座', '處女座',
      '天秤座', '天蠍座', '射手座', '摩羯座', '水瓶座', '雙魚座'
    ];

    final signIndex = (longitude / 30).floor() % 12;
    return signs[signIndex];
  }

  /// 尋找映點或反映點與其他行星的匹配
  /// 先檢查星座是否相符，再檢查4度容許度內的匹配
  List<Map<String, dynamic>> _findAntisciaMatches(double antisciaPosition, List<dynamic> allPlanets, dynamic currentPlanet) {
    final matches = <Map<String, dynamic>>[];
    const tolerance = 4.0; // 4度容許度
    final antisciaSign = _getSignFromLongitude(antisciaPosition);

    for (final planet in allPlanets) {
      if (planet.id == currentPlanet.id) continue; // 跳過自己

      print('當下行星${currentPlanet.name}在${currentPlanet.sign}，映點在$antisciaSign 比對 行星${planet.name}在${planet.sign}');

      // 首先檢查星座是否相符
      if (planet.sign == antisciaSign) {
        // 星座相符，再檢查度數是否在4度容許度內
        final distance = _calculateAngularDistance(antisciaPosition, planet.longitude);

        print('映點差距$distance');

        if (distance <= tolerance) {
          print('找到映點匹配');
          matches.add({
            'planet': planet,
            'distance': distance,
            'text': '${currentPlanet.name} ⟷ ${planet.name}\n±${distance.toStringAsFixed(1)}°',
            'type': 'antiscia', // 標記為映點關係
          });
        }
      }
    }

    // 如果沒有匹配，返回空的佔位符
    if (matches.isEmpty) {
      return [{'text': '-', 'planet': null, 'distance': 0.0, 'type': 'none'}];
    }

    // 按距離排序，最接近的在前
    matches.sort((a, b) => a['distance'].compareTo(b['distance']));

    return matches;
  }

  /// 尋找反映點與其他行星的匹配
  /// 先檢查星座是否相符，再檢查4度容許度內的匹配
  List<Map<String, dynamic>> _findContraAntisciaMatches(double contraAntisciaPosition, List<dynamic> allPlanets, dynamic currentPlanet) {
    final matches = <Map<String, dynamic>>[];
    final tolerance = 4.0; // 4度容許度
    final contraAntisciaSign = _getSignFromLongitude(contraAntisciaPosition);

    for (final planet in allPlanets) {
      if (planet.id == currentPlanet.id) continue; // 跳過自己

      // 首先檢查星座是否相符
      if (planet.sign == contraAntisciaSign) {
        // 星座相符，再檢查度數是否在4度容許度內
        final distance = _calculateAngularDistance(contraAntisciaPosition, planet.longitude);

        if (distance <= tolerance) {
          matches.add({
            'planet': planet,
            'distance': distance,
            'text': '${currentPlanet.name} ⟷ ${planet.name}\n±${distance.toStringAsFixed(1)}°',
            'type': 'contra-antiscia', // 標記為反映點關係
          });
        }
      }
    }

    // 如果沒有匹配，返回空的佔位符
    if (matches.isEmpty) {
      return [{'text': '-', 'planet': null, 'distance': 0.0, 'type': 'none'}];
    }

    // 按距離排序，最接近的在前
    matches.sort((a, b) => a['distance'].compareTo(b['distance']));

    return matches;
  }
}

/// 表格單元格組件
class _TableCell extends StatelessWidget {
  final String? text;
  final Widget? child;
  final bool isHeader;
  final Color? color;

  const _TableCell({
    this.text,
    this.child,
    this.isHeader = false,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    return TableCell(
      verticalAlignment: TableCellVerticalAlignment.middle,
      child: Container(
        padding: const EdgeInsets.all(8),
        alignment: Alignment.center,
        child: child ??
            Text(
              text ?? '',
              style: TextStyle(
                fontSize: isHeader ? 14 : 13,
                fontWeight: isHeader ? FontWeight.bold : FontWeight.normal,
                color: color ?? (isHeader ? Colors.black87 : Colors.black),
              ),
              textAlign: TextAlign.center,
            ),
      ),
    );
  }
}