import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';

import '../../../presentation/themes/app_theme.dart';
import '../../presentation/viewmodels/divination_analysis_viewmodel.dart';

class DivinationResultView extends StatelessWidget {
  final Map<String, dynamic> divinationResult;

  const DivinationResultView({
    Key? key,
    required this.divinationResult,
  }) : super(key: key);

  /// 複製卜卦結果
  void _copyDivinationResult(BuildContext context) {
    try {
      // 格式化卜卦結果
      final text = _formatDivinationResultText();

      // 複製到剪貼板
      Clipboard.setData(ClipboardData(text: text));

      // 顯示提示
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('卜卦結果已複製到剪貼板'),
          duration: Duration(seconds: 2),
        ),
      );
    } catch (e) {
      // 顯示錯誤提示
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('複製資訊時出錯: $e')),
      );
    }
  }

  /// 格式化卜卦結果為可複製的文本
  String _formatDivinationResultText() {
    final StringBuffer buffer = StringBuffer();
    final hexagram = divinationResult['hexagram'];
    final changedHexagram = divinationResult['changedHexagram'];
    final interpretation = divinationResult['interpretation'];
    final question = divinationResult['question'];

    buffer.writeln('【卜卦問題】');
    buffer.writeln(question);
    buffer.writeln();
    buffer.writeln('【卜卦結果】');
    buffer.writeln('本卦：${hexagram['name']}卦（${hexagram['meaning']}）');
    buffer.writeln('變卦：${changedHexagram['name']}卦（${changedHexagram['meaning']}）');
    buffer.writeln();
    buffer.writeln(interpretation);

    return buffer.toString();
  }

  @override
  Widget build(BuildContext context) {
    final viewModel = Provider.of<DivinationAnalysisViewModel>(context);
    final hexagram = divinationResult['hexagram'];
    final changedHexagram = divinationResult['changedHexagram'];
    final yao = divinationResult['yao'] as List<int>;
    final changedYao = divinationResult['changedYao'] as List<int>;
    final changingLines = divinationResult['changingLines'] as List<int>;
    final interpretation = divinationResult['interpretation'] as String;
    final question = divinationResult['question'] as String;

    // 獲取卦象的圖形表示
    final hexagramSymbol = viewModel.getHexagramSymbol(yao);
    final changedHexagramSymbol = viewModel.getHexagramSymbol(changedYao);

    return DraggableScrollableSheet(
      initialChildSize: 0.7,
      minChildSize: 0.5,
      maxChildSize: 0.95,
      expand: false,
      builder: (context, scrollController) {
        return Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '卜卦結果',
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Navigator.pop(context),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                '問題：$question',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  ElevatedButton.icon(
                    icon: const Icon(Icons.copy),
                    label: const Text('複製結果'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.solarAmber,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    ),
                    onPressed: () => _copyDivinationResult(context),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              const Divider(),
              Expanded(
                child: ListView(
                  controller: scrollController,
                  children: [
                    _buildHexagramSection(
                      context,
                      '本卦',
                      hexagram,
                      hexagramSymbol,
                      changingLines,
                    ),
                    const SizedBox(height: 16),
                    _buildHexagramSection(
                      context,
                      '變卦',
                      changedHexagram,
                      changedHexagramSymbol,
                      [],
                    ),
                    const SizedBox(height: 16),
                    _buildInterpretationSection(context, interpretation),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildHexagramSection(
    BuildContext context,
    String title,
    Map<String, dynamic> hexagram,
    List<String> symbol,
    List<int> changingLines,
  ) {
    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CircleAvatar(
                  backgroundColor: title == '本卦' ? AppColors.royalIndigo : AppColors.solarAmber,
                  radius: 24,
                  child: Text(
                    hexagram['name'],
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '$title：${hexagram['name']}卦',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${hexagram['meaning']} - ${hexagram['nature']}',
                        style: const TextStyle(fontSize: 14, color: Colors.black87),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                // 卦象圖形
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      for (int i = 0; i < 6; i++)
                        Container(
                          margin: const EdgeInsets.symmetric(vertical: 4),
                          child: Row(
                            children: [
                              Text(
                                '${i + 1}',
                                style: TextStyle(
                                  fontWeight: changingLines.contains(i) ? FontWeight.bold : FontWeight.normal,
                                  color: changingLines.contains(i) ? Colors.red : Colors.black,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Text(
                                symbol[i],
                                style: TextStyle(
                                  fontFamily: 'monospace',
                                  fontSize: 16,
                                  fontWeight: changingLines.contains(i) ? FontWeight.bold : FontWeight.normal,
                                  color: changingLines.contains(i) ? Colors.red : Colors.black,
                                ),
                              ),
                            ],
                          ),
                        ),
                    ],
                  ),
                ),
                const SizedBox(width: 16),
                // 卦象描述
                Expanded(
                  child: Text(
                    hexagram['description'],
                    style: const TextStyle(fontSize: 14),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInterpretationSection(BuildContext context, String interpretation) {
    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.lightbulb, color: AppColors.solarAmber, size: 24),
                const SizedBox(width: 12),
                const Text(
                  '卜卦解釋',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              interpretation,
              style: const TextStyle(fontSize: 16, height: 1.5),
            ),
          ],
        ),
      ),
    );
  }
}
