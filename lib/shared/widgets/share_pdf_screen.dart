import 'dart:io';
import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';

/// PDF 分享螢幕
/// 用於分享生成的 PDF 檔案
class SharePdfScreen extends StatefulWidget {
  final Uint8List pdfBytes;
  final String fileName;

  const SharePdfScreen({
    super.key,
    required this.pdfBytes,
    required this.fileName,
  });

  @override
  _SharePdfScreenState createState() => _SharePdfScreenState();
}

class _SharePdfScreenState extends State<SharePdfScreen> {
  bool _isSharing = false;
  String? _errorMessage;
  File? _pdfFile;

  @override
  void initState() {
    super.initState();
    _savePdfToFile();
  }

  Future<void> _savePdfToFile() async {
    setState(() {
      _isSharing = true;
      _errorMessage = null;
    });

    try {
      // 獲取臨時目錄
      final tempDir = await getTemporaryDirectory();
      final file = File('${tempDir.path}/${widget.fileName}');

      // 寫入 PDF 數據
      await file.writeAsBytes(widget.pdfBytes);

      setState(() {
        _pdfFile = file;
        _isSharing = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = '保存 PDF 失敗: $e';
        _isSharing = false;
      });
    }
  }

  Future<void> _sharePdf() async {
    if (_pdfFile == null) {
      setState(() {
        _errorMessage = 'PDF 文件不可用';
      });
      return;
    }

    setState(() {
      _isSharing = true;
      _errorMessage = null;
    });

    try {
      // 分享 PDF 文件
      await Share.shareXFiles(
        [XFile(_pdfFile!.path)],
        text: '我的星盤數據',
      );
    } catch (e) {
      setState(() {
        _errorMessage = '分享 PDF 失敗: $e';
      });
    } finally {
      setState(() {
        _isSharing = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('分享 PDF'),
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.picture_as_pdf,
                size: 80,
                color: Colors.red,
              ),
              const SizedBox(height: 24),
              Text(
                widget.fileName,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),
              if (_isSharing)
                const Column(
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text('正在處理...'),
                  ],
                )
              else if (_errorMessage != null)
                Column(
                  children: [
                    Icon(
                      Icons.error_outline,
                      size: 48,
                      color: Colors.red[700],
                    ),
                    const SizedBox(height: 16),
                    Text(
                      _errorMessage!,
                      style: TextStyle(
                        color: Colors.red[700],
                        fontSize: 16,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 24),
                    ElevatedButton(
                      onPressed: _savePdfToFile,
                      child: const Text('重試'),
                    ),
                  ],
                )
              else
                Column(
                  children: [
                    const Text(
                      'PDF 已準備好，您可以分享給其他人',
                      style: TextStyle(fontSize: 16),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 24),
                    ElevatedButton.icon(
                      onPressed: _sharePdf,
                      icon: const Icon(Icons.share),
                      label: const Text('分享 PDF'),
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 32,
                          vertical: 16,
                        ),
                      ),
                    ),
                  ],
                ),
            ],
          ),
        ),
      ),
    );
  }
}
