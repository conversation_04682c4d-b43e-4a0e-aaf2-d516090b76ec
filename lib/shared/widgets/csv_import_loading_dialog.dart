import 'package:flutter/material.dart';

/// CSV 匯入 Loading 對話框
class CsvImportLoadingDialog extends StatelessWidget {
  final String status;
  final int progress;
  final int total;
  final double progressPercent;
  final VoidCallback? onCancel;

  const CsvImportLoadingDialog({
    Key? key,
    required this.status,
    required this.progress,
    required this.total,
    required this.progressPercent,
    this.onCancel,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        width: 350,
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 標題
            Row(
              children: [
                Icon(
                  Icons.file_download,
                  color: Colors.blue.shade600,
                  size: 24,
                ),
                const SizedBox(width: 12),
                const Text(
                  'CSV 資料匯入',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // 進度指示器
            _buildProgressIndicator(),
            const SizedBox(height: 16),

            // 狀態文字
            Text(
              status,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black87,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),

            // 進度文字
            if (total > 0)
              Text(
                '$progress / $total',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                ),
              ),
            const SizedBox(height: 24),

            // 取消按鈕（可選）
            if (onCancel != null)
              SizedBox(
                width: double.infinity,
                child: OutlinedButton(
                  onPressed: onCancel,
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    side: BorderSide(color: Colors.grey.shade400),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text(
                    '取消',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.black87,
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressIndicator() {
    if (total > 0) {
      // 有明確進度的進度條
      return Column(
        children: [
          LinearProgressIndicator(
            value: progressPercent,
            backgroundColor: Colors.grey.shade200,
            valueColor: AlwaysStoppedAnimation<Color>(Colors.blue.shade600),
            minHeight: 6,
          ),
          const SizedBox(height: 8),
          Text(
            '${(progressPercent * 100).toInt()}%',
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: Colors.blue.shade600,
            ),
          ),
        ],
      );
    } else {
      // 不確定進度的旋轉指示器
      return Column(
        children: [
          SizedBox(
            width: 40,
            height: 40,
            child: CircularProgressIndicator(
              strokeWidth: 3,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.blue.shade600),
            ),
          ),
        ],
      );
    }
  }
}

/// 顯示 CSV 匯入 Loading 對話框
Future<void> showCsvImportLoadingDialog({
  required BuildContext context,
  required String status,
  required int progress,
  required int total,
  required double progressPercent,
  VoidCallback? onCancel,
}) {
  return showDialog(
    context: context,
    barrierDismissible: false, // 防止點擊外部關閉
    builder: (context) => CsvImportLoadingDialog(
      status: status,
      progress: progress,
      total: total,
      progressPercent: progressPercent,
      onCancel: onCancel,
    ),
  );
}

/// CSV 匯入進度監聽器 Widget
class CsvImportProgressListener extends StatefulWidget {
  final Widget child;
  final bool isImporting;
  final String importStatus;
  final int importProgress;
  final int importTotal;
  final double importProgressPercent;

  const CsvImportProgressListener({
    Key? key,
    required this.child,
    required this.isImporting,
    required this.importStatus,
    required this.importProgress,
    required this.importTotal,
    required this.importProgressPercent,
  }) : super(key: key);

  @override
  State<CsvImportProgressListener> createState() => _CsvImportProgressListenerState();
}

class _CsvImportProgressListenerState extends State<CsvImportProgressListener> {
  bool _dialogShown = false;

  @override
  void initState() {
    super.initState();
    // 檢查初始狀態
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkImportingState();
    });
  }

  @override
  void didUpdateWidget(CsvImportProgressListener oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 當匯入狀態改變時，檢查是否需要顯示或隱藏對話框
    _checkImportingState();
  }

  void _checkImportingState() {
    if (widget.isImporting && !_dialogShown) {
      _showLoadingDialog();
    } else if (!widget.isImporting && _dialogShown) {
      _hideLoadingDialog();
    }
  }

  void _showLoadingDialog() {
    if (!_dialogShown && mounted) {
      _dialogShown = true;
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => _DialogContent(
          listener: widget,
        ),
      );
    }
  }

  void _hideLoadingDialog() {
    if (_dialogShown && mounted) {
      _dialogShown = false;
      Navigator.of(context).pop();
    }
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }

  @override
  void dispose() {
    if (_dialogShown) {
      _hideLoadingDialog();
    }
    super.dispose();
  }
}

/// 對話框內容組件，用於實時更新進度
class _DialogContent extends StatefulWidget {
  final CsvImportProgressListener listener;

  const _DialogContent({
    Key? key,
    required this.listener,
  }) : super(key: key);

  @override
  State<_DialogContent> createState() => _DialogContentState();
}

class _DialogContentState extends State<_DialogContent> {
  @override
  Widget build(BuildContext context) {
    return CsvImportLoadingDialog(
      status: widget.listener.importStatus,
      progress: widget.listener.importProgress,
      total: widget.listener.importTotal,
      progressPercent: widget.listener.importProgressPercent,
    );
  }
}
