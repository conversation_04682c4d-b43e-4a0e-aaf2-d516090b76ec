import 'package:flutter/material.dart';

import '../../core/utils/logger_utils.dart';
import '../../presentation/themes/app_theme.dart';
import '../utils/email_auth_diagnostics.dart';
/// 認證錯誤對話框
class AuthErrorDialog extends StatefulWidget {
  final String title;
  final String message;
  final String? email;
  final VoidCallback? onRetry;
  final VoidCallback? onForgotPassword;
  final bool showDiagnostics;

  const AuthErrorDialog({
    super.key,
    required this.title,
    required this.message,
    this.email,
    this.onRetry,
    this.onForgotPassword,
    this.showDiagnostics = false,
  });

  @override
  State<AuthErrorDialog> createState() => _AuthErrorDialogState();
}

class _AuthErrorDialogState extends State<AuthErrorDialog> {
  bool _isRunningDiagnostics = false;
  bool _isResettingPassword = false;
  String? _diagnosticsResult;

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      title: Row(
        children: [
          Icon(
            Icons.error_outline,
            color: Colors.red.shade600,
            size: 28,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              widget.title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppColors.textDark,
              ),
            ),
          ),
        ],
      ),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 錯誤訊息
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.red.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red.shade200),
              ),
              child: Text(
                widget.message,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.red.shade700,
                  height: 1.4,
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // 建議操作
            _buildSuggestions(),
            
            // 診斷結果（如果有）
            if (_diagnosticsResult != null) ...[
              const SizedBox(height: 16),
              _buildDiagnosticsResult(),
            ],
          ],
        ),
      ),
      actions: [
        // 診斷按鈕（開發模式）
        if (widget.showDiagnostics && widget.email != null)
          TextButton.icon(
            onPressed: _isRunningDiagnostics ? null : _runDiagnostics,
            icon: _isRunningDiagnostics
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Icon(Icons.bug_report),
            label: Text(_isRunningDiagnostics ? '診斷中...' : '診斷'),
            style: TextButton.styleFrom(
              foregroundColor: AppColors.royalIndigo,
            ),
          ),
        
        // 忘記密碼按鈕
        if (widget.email != null)
          TextButton.icon(
            onPressed: _isResettingPassword ? null : _resetPassword,
            icon: _isResettingPassword
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Icon(Icons.lock_reset),
            label: Text(_isResettingPassword ? '發送中...' : '重設密碼'),
            style: TextButton.styleFrom(
              foregroundColor: Colors.orange.shade600,
            ),
          ),
        
        // 取消按鈕
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('取消'),
          style: TextButton.styleFrom(
            foregroundColor: Colors.grey.shade600,
          ),
        ),
        
        // 重試按鈕
        if (widget.onRetry != null)
          ElevatedButton.icon(
            onPressed: () {
              Navigator.of(context).pop();
              widget.onRetry!();
            },
            icon: const Icon(Icons.refresh),
            label: const Text('重試'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.royalIndigo,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
      ],
    );
  }

  /// 構建建議操作
  Widget _buildSuggestions() {
    final suggestions = _getSuggestions();
    
    if (suggestions.isEmpty) {
      return const SizedBox.shrink();
    }
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '💡 建議解決方案：',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: Colors.blue.shade700,
          ),
        ),
        const SizedBox(height: 8),
        ...suggestions.map((suggestion) => Padding(
          padding: const EdgeInsets.only(bottom: 4),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '• ',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.blue.shade600,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Expanded(
                child: Text(
                  suggestion,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey.shade700,
                    height: 1.3,
                  ),
                ),
              ),
            ],
          ),
        )),
      ],
    );
  }

  /// 構建診斷結果
  Widget _buildDiagnosticsResult() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.analytics,
                color: Colors.blue.shade600,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                '診斷結果',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue.shade700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            _diagnosticsResult!,
            style: TextStyle(
              fontSize: 12,
              color: Colors.blue.shade600,
              fontFamily: 'monospace',
            ),
          ),
        ],
      ),
    );
  }

  /// 獲取建議
  List<String> _getSuggestions() {
    final message = widget.message.toLowerCase();
    final suggestions = <String>[];
    
    if (message.contains('認證憑證無效') || message.contains('invalid-credential')) {
      suggestions.addAll([
        '檢查電子郵件和密碼是否正確',
        '確認帳戶是否已註冊',
        '嘗試重設密碼',
        '檢查網路連接是否正常',
      ]);
    } else if (message.contains('密碼錯誤') || message.contains('wrong-password')) {
      suggestions.addAll([
        '檢查密碼是否正確',
        '注意大小寫和特殊字符',
        '嘗試重設密碼',
      ]);
    } else if (message.contains('找不到') || message.contains('user-not-found')) {
      suggestions.addAll([
        '檢查電子郵件是否正確',
        '確認是否已註冊帳戶',
        '嘗試註冊新帳戶',
      ]);
    } else if (message.contains('網路') || message.contains('network')) {
      suggestions.addAll([
        '檢查網路連接',
        '嘗試切換網路環境',
        '稍後再試',
      ]);
    } else {
      suggestions.addAll([
        '檢查網路連接',
        '稍後再試',
        '聯繫技術支援',
      ]);
    }
    
    return suggestions;
  }

  /// 執行診斷
  Future<void> _runDiagnostics() async {
    if (widget.email == null) return;
    
    setState(() {
      _isRunningDiagnostics = true;
      _diagnosticsResult = null;
    });
    
    try {
      final diagnostics = EmailAuthDiagnostics();
      final result = await diagnostics.diagnoseEmailAuth(
        email: widget.email!,
        password: '***', // 不記錄實際密碼
      );
      
      // 簡化診斷結果顯示
      final buffer = StringBuffer();
      
      final validation = result['input_validation'] as Map<String, dynamic>? ?? {};
      buffer.writeln('輸入驗證: ${validation['email_format_valid'] == true ? '✅' : '❌'}');
      
      final connection = result['firebase_connection'] as Map<String, dynamic>? ?? {};
      buffer.writeln('Firebase 連接: ${connection['firebase_initialized'] == true ? '✅' : '❌'}');
      
      final userCheck = result['user_exists'] as Map<String, dynamic>? ?? {};
      buffer.writeln('用戶存在: ${userCheck['user_exists'] == true ? '✅' : '❌'}');
      
      final attempt = result['auth_attempt'] as Map<String, dynamic>? ?? {};
      buffer.writeln('認證結果: ${attempt['success'] == true ? '✅' : '❌'}');
      
      if (attempt['error_code'] != null) {
        buffer.writeln('錯誤代碼: ${attempt['error_code']}');
      }
      
      setState(() {
        _diagnosticsResult = buffer.toString();
      });
      
    } catch (e) {
      logger.e('診斷失敗: $e');
      setState(() {
        _diagnosticsResult = '診斷失敗: $e';
      });
    } finally {
      setState(() {
        _isRunningDiagnostics = false;
      });
    }
  }

  /// 重設密碼
  Future<void> _resetPassword() async {
    if (widget.email == null) return;
    
    setState(() {
      _isResettingPassword = true;
    });
    
    try {
      final diagnostics = EmailAuthDiagnostics();
      final success = await diagnostics.resetPassword(widget.email!);
      
      if (success) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('密碼重設郵件已發送到 ${widget.email}'),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 3),
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('密碼重設失敗，請稍後再試'),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 3),
            ),
          );
        }
      }
      
    } catch (e) {
      logger.e('重設密碼失敗: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('重設密碼失敗: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isResettingPassword = false;
        });
      }
    }
  }

  /// 顯示認證錯誤對話框
  static Future<void> show(
    BuildContext context, {
    required String title,
    required String message,
    String? email,
    VoidCallback? onRetry,
    VoidCallback? onForgotPassword,
    bool showDiagnostics = false,
  }) {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (context) => AuthErrorDialog(
        title: title,
        message: message,
        email: email,
        onRetry: onRetry,
        onForgotPassword: onForgotPassword,
        showDiagnostics: showDiagnostics,
      ),
    );
  }
}
