import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../data/models/user/birth_data.dart';
import '../../presentation/themes/app_theme.dart';
import '../../presentation/viewmodels/recent_persons_viewmodel.dart';


/// 近期選中人物區域的共用組件
class RecentPersonsSection extends StatelessWidget {
  /// 所有人物列表
  final List<BirthData> allPersons;
  
  /// 最大顯示數量
  final int maxCount;
  
  /// 是否顯示清除按鈕
  final bool showClearButton;
  
  /// 是否在搜索時隱藏
  final bool hideWhenSearching;
  
  /// 搜索文本（用於判斷是否在搜索）
  final String? searchText;
  
  /// 選中人物的回調
  final void Function(BirthData person) onPersonSelected;
  
  /// 長按人物的回調（可選）
  final void Function(BirthData person)? onPersonLongPressed;
  
  /// 清除所有記錄的回調（可選）
  final VoidCallback? onClearAll;
  
  /// 自定義標題（可選）
  final String? title;
  
  /// 自定義圖標（可選）
  final IconData? icon;
  
  /// 自定義顏色主題（可選）
  final Color? themeColor;
  
  /// 是否顯示選中狀態
  final bool showSelectedState;
  
  /// 獲取選中狀態的回調
  final bool Function(BirthData person)? isPersonSelected;

  const RecentPersonsSection({
    super.key,
    required this.allPersons,
    required this.onPersonSelected,
    this.maxCount = 5,
    this.showClearButton = true,
    this.hideWhenSearching = true,
    this.searchText,
    this.onPersonLongPressed,
    this.onClearAll,
    this.title,
    this.icon,
    this.themeColor,
    this.showSelectedState = false,
    this.isPersonSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<RecentPersonsViewModel>(
      builder: (context, recentPersonsViewModel, child) {
        // 獲取近期選中的人物
        final recentPersons = recentPersonsViewModel.getRecentPersons(
          allPersons,
          maxCount: maxCount,
        );

        // 如果沒有近期人物，不顯示此區域
        if (recentPersons.isEmpty) {
          return const SizedBox.shrink();
        }

        // 如果在搜索且設置了隱藏，不顯示此區域
        if (hideWhenSearching && 
            searchText != null && 
            searchText!.isNotEmpty) {
          return const SizedBox.shrink();
        }

        final effectiveThemeColor = themeColor ?? AppColors.royalIndigo;
        final effectiveTitle = title ?? '近期選中';
        final effectiveIcon = icon ?? Icons.history;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 標題行
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 4),
              child: Row(
                children: [
                  Icon(
                    effectiveIcon,
                    size: 16,
                    color: Colors.black,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    effectiveTitle,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: Colors.black,
                    ),
                  ),
                  const Spacer(),
                  // 清除按鈕
                  if (showClearButton)
                    TextButton(
                      onPressed: () => _showClearConfirmDialog(context, recentPersonsViewModel),
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        minimumSize: Size.zero,
                        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      ),
                      child: const Text(
                        '清除',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.black,
                        ),
                      ),
                    ),
                ],
              ),
            ),
            const SizedBox(height: 8),
            
            // 近期人物水平滾動列表
            SizedBox(
              height: 40,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                padding: const EdgeInsets.symmetric(horizontal: 4),
                itemCount: recentPersons.length,
                itemBuilder: (context, index) {
                  final person = recentPersons[index];
                  return _buildRecentPersonChip(
                    person, 
                    effectiveThemeColor,
                    context,
                  );
                },
              ),
            ),
          ],
        );
      },
    );
  }

  /// 構建近期人物芯片
  Widget _buildRecentPersonChip(
    BirthData person, 
    Color themeColor,
    BuildContext context,
  ) {
    // 檢查是否選中
    final isSelected = showSelectedState && 
                      isPersonSelected != null && 
                      isPersonSelected!(person);

    return Container(
      margin: const EdgeInsets.only(right: 8),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(20),
          onTap: () => _handlePersonTap(person, context),
          onLongPress: onPersonLongPressed != null 
              ? () => onPersonLongPressed!(person)
              : null,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: isSelected
                  ? themeColor.withValues(alpha: 0.2)
                  : themeColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: isSelected
                    ? themeColor
                    : themeColor.withValues(alpha: 0.3),
                width: isSelected ? 2 : 1,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 頭像
                CircleAvatar(
                  radius: 10,
                  backgroundColor: themeColor.withValues(alpha: 0.2),
                  child: Text(
                    person.name.isNotEmpty ? person.name[0] : '?',
                    style: TextStyle(
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                      color: themeColor,
                    ),
                  ),
                ),
                const SizedBox(width: 6),
                // 姓名
                Text(
                  person.name,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: isSelected ? themeColor : AppColors.textDark,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 處理人物點擊
  void _handlePersonTap(BirthData person, BuildContext context) {
    // 記錄選中的人物
    try {
      final recentPersonsViewModel = Provider.of<RecentPersonsViewModel>(context, listen: false);
      recentPersonsViewModel.recordSelectedPerson(person);
    } catch (e) {
      // 如果記錄失敗，不影響選擇功能
    }

    // 調用回調
    onPersonSelected(person);
  }

  /// 顯示清除確認對話框
  void _showClearConfirmDialog(BuildContext context, RecentPersonsViewModel viewModel) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('清除近期記錄'),
          content: const Text('確定要清除所有近期選中的人物記錄嗎？'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _clearAllRecords(viewModel);
              },
              child: const Text('確定'),
            ),
          ],
        );
      },
    );
  }

  /// 清除所有記錄
  void _clearAllRecords(RecentPersonsViewModel viewModel) {
    viewModel.clearAllRecords();
    
    // 如果有自定義的清除回調，也調用它
    if (onClearAll != null) {
      onClearAll!();
    }
  }
}
