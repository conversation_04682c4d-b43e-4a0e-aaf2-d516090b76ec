import 'package:astreal/data/models/astrology/planet_position.dart';
import 'package:astreal/presentation/viewmodels/chart_viewmodel.dart';
import 'package:astreal/shared/utils/astrology_calculator.dart' as astro;
import 'package:flutter/material.dart';

class CustomHouseCard extends StatelessWidget {
  final int houseNumber;
  final double houseAngle;
  final String sign;
  final double signDegree;
  final String houseDescription;
  final Color houseColor;
  final List<Map<String, dynamic>> rulerInfoList;
  final List<PlanetPosition> planetsInHouse;
  final ChartViewModel viewModel;

  const CustomHouseCard({
    super.key,
    required this.houseNumber,
    required this.houseAngle,
    required this.sign,
    required this.signDegree,
    required this.houseDescription,
    required this.houseColor,
    required this.rulerInfoList,
    required this.planetsInHouse,
    required this.viewModel,
  });

  /// 根據行星尊貴力量狀態返回對應的顏色
  Color _getDignityColor(PlanetDignity dignity) {
    switch (dignity) {
      case PlanetDignity.domicile:
        return Colors.purple; // 廟狀態用紫色
      case PlanetDignity.exaltation:
        return Colors.green; // 旺狀態用綠色
      case PlanetDignity.detriment:
        return Colors.red; // 陷狀態用紅色
      case PlanetDignity.fall:
        return Colors.orange; // 弱狀態用橙色
      case PlanetDignity.peregrine:
        return Colors.grey; // 普通狀態用灰色
      default:
        return Colors.grey;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      color: Colors.white,
      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 3),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
        side: BorderSide(color: houseColor.withOpacity(0.3), width: 1),
      ),
      child: Padding(
        padding: const EdgeInsets.all(8),
        child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 第一行：宮位基本資訊
              Row(
                children: [
                  // 宮位數字圓形圖標（縮小）
                  CircleAvatar(
                    backgroundColor: houseColor,
                    radius: 16,
                    child: Text(
                      '$houseNumber',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    '第$houseNumber宮',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      '$sign ${viewModel.formatDegree(signDegree)}',
                      style: TextStyle(
                        fontSize: 13,
                        color: Colors.grey[600],
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
              // 第二行：宮主星（如有）
              if (rulerInfoList.isNotEmpty) ...[
                const SizedBox(height: 6),
                Row(
                  children: [
                    Text(
                      '宮主星:',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(width: 6),
                    Expanded(
                      child: Wrap(
                        spacing: 4,
                        runSpacing: 2,
                        children: rulerInfoList.map((rulerInfo) {
                          return Container(
                            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: (rulerInfo['color'] as Color).withOpacity(0.1),
                              borderRadius: BorderRadius.circular(4),
                              border: Border.all(
                                color: (rulerInfo['color'] as Color).withOpacity(0.3),
                                width: 0.5,
                              ),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  rulerInfo['symbol'],
                                  style: TextStyle(
                                    fontFamily: "astro_one_font",
                                    fontSize: 12,
                                    color: rulerInfo['color'],
                                  ),
                                ),
                                const SizedBox(width: 2),
                                Text(
                                  '飛入第${rulerInfo['house']}宮',
                                  style: const TextStyle(
                                    fontSize: 11,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                if (rulerInfo['isModern'] == true)
                                  Container(
                                    margin: const EdgeInsets.only(left: 2),
                                    width: 4,
                                    height: 4,
                                    decoration: const BoxDecoration(
                                      color: Colors.blue,
                                      shape: BoxShape.circle,
                                    ),
                                  ),
                              ],
                            ),
                          );
                        }).toList(),
                      ),
                    ),
                  ],
                ),
              ],
              // 第三行：宮位含義（緊湊顯示）
              const SizedBox(height: 4),
              Text(
                houseDescription,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[700],
                  fontStyle: FontStyle.italic,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              // 行星資訊（如有）
              if (planetsInHouse.isNotEmpty) ...[
                const SizedBox(height: 6),
                Wrap(
                  spacing: 8,
                  runSpacing: 2,
                  children: planetsInHouse.map<Widget>((planet) {
                    final signDegree = planet.longitude % 30;
                    final planetColor = astro.AstrologyCalculator.getPlanetColor(planet.name);
                    return Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: planetColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(4),
                        border: Border.all(
                          color: planetColor.withOpacity(0.3),
                          width: 0.5,
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            planet.symbol,
                            style: TextStyle(
                              fontFamily: "astro_one_font",
                              fontSize: 14,
                              color: planetColor,
                            ),
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '${planet.sign} ${viewModel.formatDegree(signDegree)}',
                            style: const TextStyle(fontSize: 11),
                          ),
                          const SizedBox(width: 4),
                          Container(
                            width: 8,
                            height: 8,
                            decoration: BoxDecoration(
                              color: _getDignityColor(planet.dignity),
                              shape: BoxShape.circle,
                            ),
                          ),
                        ],
                      ),
                    );
                  }).toList(),
                ),
              ],
            ],
          ),
        ),
      );
  }
}
