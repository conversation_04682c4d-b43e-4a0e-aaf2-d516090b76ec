import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import '../../core/utils/logger_utils.dart';
import '../../core/utils/web_navigation_helper.dart' if (dart.library.io) '../../core/utils/web_navigation_helper_stub.dart';

/// 網頁感知的 AppBar
/// 在網頁版中正確處理返回按鈕行為
class WebAwareAppBar extends StatelessWidget implements PreferredSizeWidget {
  final Widget? title;
  final List<Widget>? actions;
  final Widget? leading;
  final bool automaticallyImplyLeading;
  final Color? backgroundColor;
  final double? elevation;
  final IconThemeData? iconTheme;
  final TextStyle? titleTextStyle;
  final bool centerTitle;
  final double? titleSpacing;
  final double toolbarHeight;
  final VoidCallback? onBackPressed;
  final PreferredSizeWidget? bottom;

  const WebAwareAppBar({
    super.key,
    this.title,
    this.actions,
    this.leading,
    this.automaticallyImplyLeading = true,
    this.backgroundColor,
    this.elevation,
    this.iconTheme,
    this.titleTextStyle,
    this.centerTitle = false,
    this.titleSpacing,
    this.toolbarHeight = kToolbarHeight,
    this.onBackPressed,
    this.bottom,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: title,
      actions: actions,
      leading: _buildLeading(context),
      automaticallyImplyLeading: automaticallyImplyLeading,
      backgroundColor: backgroundColor,
      elevation: elevation,
      iconTheme: iconTheme,
      titleTextStyle: titleTextStyle,
      centerTitle: centerTitle,
      titleSpacing: titleSpacing,
      toolbarHeight: toolbarHeight,
      bottom: bottom,
    );
  }

  /// 構建返回按鈕
  Widget? _buildLeading(BuildContext context) {
    // 如果明確提供了 leading widget，使用它
    if (leading != null) {
      return leading;
    }

    // 如果不自動顯示返回按鈕，返回 null
    if (!automaticallyImplyLeading) {
      return null;
    }

    // 檢查是否需要顯示返回按鈕
    final canPop = Navigator.canPop(context);
    final hasDrawer = Scaffold.maybeOf(context)?.hasDrawer ?? false;
    final hasEndDrawer = Scaffold.maybeOf(context)?.hasEndDrawer ?? false;

    if (!canPop && !hasDrawer && !hasEndDrawer) {
      return null;
    }

    // 如果有 Drawer，顯示菜單按鈕
    if (hasDrawer) {
      return IconButton(
        icon: const Icon(Icons.menu),
        onPressed: () {
          Scaffold.of(context).openDrawer();
        },
        tooltip: MaterialLocalizations.of(context).openAppDrawerTooltip,
      );
    }

    // 如果有 EndDrawer，顯示菜單按鈕
    if (hasEndDrawer) {
      return IconButton(
        icon: const Icon(Icons.menu),
        onPressed: () {
          Scaffold.of(context).openEndDrawer();
        },
        tooltip: MaterialLocalizations.of(context).openAppDrawerTooltip,
      );
    }

    // 顯示返回按鈕
    return IconButton(
      icon: const Icon(Icons.arrow_back),
      onPressed: () => _handleBackPressed(context),
      tooltip: MaterialLocalizations.of(context).backButtonTooltip,
    );
  }

  /// 處理返回按鈕點擊
  void _handleBackPressed(BuildContext context) {
    logger.d('🔙 AppBar 返回按鈕被點擊');

    try {
      // 如果有自定義的返回處理，執行它
      if (onBackPressed != null) {
        logger.d('🔙 執行自定義返回處理');
        onBackPressed!();
        return;
      }

      // 在網頁版中，需要特殊處理
      if (kIsWeb) {
        _handleWebBackPressed(context);
      } else {
        _handleMobileBackPressed(context);
      }
    } catch (e) {
      logger.e('處理返回按鈕點擊時出錯: $e');
      // 降級處理：直接使用 Navigator.pop
      if (Navigator.canPop(context)) {
        Navigator.pop(context);
      }
    }
  }

  /// 處理網頁版返回
  void _handleWebBackPressed(BuildContext context) {
    logger.d('🌐 處理網頁版返回');

    // 檢查是否可以在 Flutter 路由中返回
    if (Navigator.canPop(context)) {
      logger.d('🔙 使用 Flutter Navigator 返回');
      Navigator.pop(context);
      
      // 更新瀏覽器歷史記錄
      if (WebNavigationHelper.canGoBack) {
        // 這裡不調用 WebNavigationHelper.goBack()，
        // 因為 Navigator.pop() 已經處理了 Flutter 路由
        logger.d('🌐 Flutter 路由已處理，無需更新瀏覽器歷史');
      }
    } else {
      logger.d('🔙 無法使用 Flutter Navigator 返回，導航到主頁面');
      _navigateToHome(context);
    }
  }

  /// 處理移動版返回
  void _handleMobileBackPressed(BuildContext context) {
    logger.d('📱 處理移動版返回');
    
    if (Navigator.canPop(context)) {
      Navigator.pop(context);
    }
  }

  /// 導航到主頁面
  void _navigateToHome(BuildContext context) {
    try {
      // 清除路由堆疊並導航到主頁面
      Navigator.pushNamedAndRemoveUntil(
        context,
        '/main',
        (route) => false,
      );
      
      // 更新網頁版的路由狀態
      if (kIsWeb) {
        WebNavigationHelper.replaceRoute('/main');
        WebNavigationHelper.setPageTitle('AstReal - 首頁');
      }
      
      logger.d('🏠 已導航到主頁面');
    } catch (e) {
      logger.e('導航到主頁面時出錯: $e');
    }
  }

  @override
  Size get preferredSize => Size.fromHeight(toolbarHeight + (bottom?.preferredSize.height ?? 0.0));
}

/// WebAwareAppBar 的便捷構造函數
class WebAwareAppBarHelper {
  /// 創建一個標準的 WebAwareAppBar
  static PreferredSizeWidget create({
    Widget? title,
    List<Widget>? actions,
    Widget? leading,
    bool automaticallyImplyLeading = true,
    Color? backgroundColor,
    double? elevation,
    IconThemeData? iconTheme,
    TextStyle? titleTextStyle,
    bool centerTitle = false,
    double? titleSpacing,
    double toolbarHeight = kToolbarHeight,
    VoidCallback? onBackPressed,
    PreferredSizeWidget? bottom,
  }) {
    return WebAwareAppBar(
      title: title,
      actions: actions,
      leading: leading,
      automaticallyImplyLeading: automaticallyImplyLeading,
      backgroundColor: backgroundColor,
      elevation: elevation,
      iconTheme: iconTheme,
      titleTextStyle: titleTextStyle,
      centerTitle: centerTitle,
      titleSpacing: titleSpacing,
      toolbarHeight: toolbarHeight,
      onBackPressed: onBackPressed,
      bottom: bottom,
    );
  }

  /// 創建一個帶有自定義返回處理的 WebAwareAppBar
  static PreferredSizeWidget createWithCustomBack({
    required Widget title,
    required VoidCallback onBackPressed,
    List<Widget>? actions,
    Color? backgroundColor,
    double? elevation,
  }) {
    return WebAwareAppBar(
      title: title,
      actions: actions,
      backgroundColor: backgroundColor,
      elevation: elevation,
      onBackPressed: onBackPressed,
    );
  }

  /// 創建一個簡單的 WebAwareAppBar
  static PreferredSizeWidget simple({
    required String title,
    List<Widget>? actions,
    Color? backgroundColor,
    VoidCallback? onBackPressed,
  }) {
    return WebAwareAppBar(
      title: Text(title),
      actions: actions,
      backgroundColor: backgroundColor,
      onBackPressed: onBackPressed,
    );
  }
}
