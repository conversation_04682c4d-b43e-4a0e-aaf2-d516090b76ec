import 'package:flutter/material.dart';

import '../../../astreal.dart';
import '../../../data/services/api/profection_service.dart';
import '../../../presentation/pages/ai_interpretation_result_page.dart';
import '../common/date_time_picker_bottom_sheet.dart';

/// 可拖動的小限法卡片組件
class DraggableProfectionCard extends StatefulWidget {
  final ProfectionTimelineResult result;
  final ChartViewModel viewModel;
  final VoidCallback onClose;
  final Offset initialPosition;

  const DraggableProfectionCard({
    Key? key,
    required this.result,
    required this.viewModel,
    required this.onClose,
    required this.initialPosition,
  }) : super(key: key);

  @override
  State<DraggableProfectionCard> createState() => _DraggableProfectionCardState();
}

class _DraggableProfectionCardState extends State<DraggableProfectionCard> {
  late Offset _position;
  bool _isDragging = false;
  double _opacity = 1.0;
  late ScrollController _scrollController;

  // 時間設定相關狀態
  DateTime? _customDate;
  ProfectionTimelineResult? _customResult;
  bool _isCalculating = false;

  @override
  void initState() {
    super.initState();
    _position = widget.initialPosition;
    _scrollController = ScrollController();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;

    // 確保卡片不會超出螢幕範圍
    const cardWidth = 380.0;
    const cardHeight = 750.0;

    // 限制卡片位置在螢幕內
    double safeX = _position.dx;
    double safeY = _position.dy;

    // 確保卡片不會超出右邊界
    if (safeX + cardWidth > screenSize.width) {
      safeX = screenSize.width - cardWidth - 10;
    }

    // 確保卡片不會超出左邊界
    if (safeX < 10) {
      safeX = 10;
    }

    // 確保卡片不會超出下邊界
    if (safeY + cardHeight > screenSize.height) {
      safeY = screenSize.height - cardHeight - 10;
    }

    // 確保卡片不會超出上邊界
    if (safeY < 10) {
      safeY = 10;
    }

    return Positioned(
      left: safeX,
      top: safeY,
      child: GestureDetector(
        onPanStart: (details) {
          setState(() {
            _isDragging = true;
            _opacity = 0.7; // 拖動時變透明
          });
        },
        onPanUpdate: (details) {
          setState(() {
            _position = Offset(
              _position.dx + details.delta.dx,
              _position.dy + details.delta.dy,
            );
          });
        },
        onPanEnd: (details) {
          setState(() {
            _isDragging = false;
            _opacity = 1.0; // 停止拖動時恢復不透明
          });
        },
        onTap: () {
          // 點擊卡片時切換透明度
          setState(() {
            _opacity = _opacity == 1.0 ? 0.3 : 1.0;
          });
        },
        child: AnimatedOpacity(
          opacity: _opacity,
          duration: const Duration(milliseconds: 200),
          child: Container(
            width: cardWidth,
            height: cardHeight,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: _isDragging ? AppColors.indigoSurface : Colors.grey.shade300,
                width: _isDragging ? 2 : 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(_isDragging ? 0.3 : 0.15),
                  blurRadius: _isDragging ? 25 : 20,
                  offset: Offset(0, _isDragging ? 12 : 8),
                ),
              ],
            ),
            child: Material(
              color: Colors.transparent,
              child: Column(
                children: [
                  // 標題欄
                  _buildHeader(),

                  // 內容區域
                  Expanded(
                    child: _buildContent(),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.royalIndigo,
            AppColors.indigoSurface,
          ],
        ),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.timeline,
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  '小限法時間軸',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                    decoration: TextDecoration.none,
                  ),
                ),
                if (_customDate != null)
                  Text(
                    '設定時間：${_formatDateTime(_customDate!)}',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.white.withOpacity(0.8),
                      decoration: TextDecoration.none,
                    ),
                  ),
              ],
            ),
          ),
          // 關閉按鈕
          Container(
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(20),
            ),
            child: IconButton(
              onPressed: widget.onClose,
              icon: const Icon(Icons.close),
              iconSize: 20,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 時間設定按鈕區域
          _buildTimeSettingButtons(),

          const SizedBox(height: 16),

          // 當前小限法信息
          _buildCurrentProfectionInfo(),

          const SizedBox(height: 20),

          // 時間軸標題
          Row(
            children: [
              const Icon(
                Icons.schedule,
                color: AppColors.indigoSurface,
                size: 20,
              ),
              const SizedBox(width: 8),
              const Text(
                '年度時間軸',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                  decoration: TextDecoration.none,
                ),
              ),
              if (_isCalculating) ...[
                const SizedBox(width: 12),
                const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(AppColors.indigoSurface),
                  ),
                ),
              ],
            ],
          ),
          const SizedBox(height: 12),

          // 時間軸列表
          Expanded(
            child: _isCalculating
                ? const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CircularProgressIndicator(
                          valueColor: AlwaysStoppedAnimation<Color>(AppColors.indigoSurface),
                        ),
                        SizedBox(height: 16),
                        Text(
                          '正在計算小限法數據...',
                          style: TextStyle(
                            color: Colors.grey,
                            decoration: TextDecoration.none,
                          ),
                        ),
                      ],
                    ),
                  )
                : _buildTimelineList(),
          ),
        ],
      ),
    );
  }

  /// 構建時間設定按鈕區域
  Widget _buildTimeSettingButtons() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: AppColors.royalIndigo.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.royalIndigo.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.access_time,
            color: AppColors.royalIndigo,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  '分析時間設定',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: AppColors.royalIndigo,
                    decoration: TextDecoration.none,
                  ),
                ),
                Text(
                  _customDate != null
                      ? '自訂時間：${_formatDateTime(_customDate!)}'
                      : '使用當前時間',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                    decoration: TextDecoration.none,
                  ),
                ),
              ],
            ),
          ),
          // 時間設定按鈕
          Container(
            decoration: BoxDecoration(
              color: AppColors.royalIndigo,
              borderRadius: BorderRadius.circular(8),
            ),
            child: IconButton(
              onPressed: _isCalculating ? null : _showDateTimePicker,
              icon: const Icon(Icons.schedule),
              iconSize: 18,
              color: Colors.white,
              tooltip: '設定時間',
            ),
          ),
          const SizedBox(width: 8),
          // 重置按鈕（只在有自訂時間時顯示）
          if (_customDate != null)
            Container(
              decoration: BoxDecoration(
                color: Colors.orange.shade600,
                borderRadius: BorderRadius.circular(8),
              ),
              child: IconButton(
                onPressed: _isCalculating ? null : _resetToCurrentTime,
                icon: const Icon(Icons.refresh),
                iconSize: 18,
                color: Colors.white,
                tooltip: '重置為當前時間',
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildCurrentProfectionInfo() {
    // 使用自訂結果或原始結果
    final result = _customResult ?? widget.result;
    final current = result.currentProfection;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: AppColors.indigoSurface),
        boxShadow: [
          BoxShadow(
            color: AppColors.indigoSurface.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 標題行
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.indigoSurface,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.star,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                '當前 ${current.currentAge} 歲',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                  decoration: TextDecoration.none,
                ),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: AppColors.indigoSurface,
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Text(
                  '第${current.profectionHouse}宮',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    decoration: TextDecoration.none,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // 詳細信息網格
          Row(
            children: [
              Expanded(
                child: _buildInfoItem(
                  icon: Icons.brightness_1,
                  label: '星座',
                  value: current.profectionSign,
                  color: AppColors.indigoSurface,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildInfoItem(
                  icon: Icons.public,
                  label: '主星',
                  value: current.timeLordPlanetName,
                  color: Colors.blue.shade600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),

          _buildInfoItem(
            icon: Icons.category,
            label: '主題',
            value: current.houseTheme,
            color: Colors.green.shade600,
            isFullWidth: true,
          ),
          const SizedBox(height: 12),

          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.7),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              current.description,
              style: TextStyle(
                fontSize: 13,
                color: Colors.grey.shade700,
                height: 1.4,
                decoration: TextDecoration.none,
              ),
            ),
          ),
          const SizedBox(height: 16),

          // 解讀按鈕
          _buildInterpretationButton(),
        ],
      ),
    );
  }

  Widget _buildInfoItem({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
    bool isFullWidth = false,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.8),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: isFullWidth
          ? Row(
              children: [
                Icon(icon, color: color, size: 16),
                const SizedBox(width: 8),
                Text(
                  '$label：',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                    fontWeight: FontWeight.w500,
                    decoration: TextDecoration.none,
                  ),
                ),
                Expanded(
                  child: Text(
                    value,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                      decoration: TextDecoration.none,
                    ),
                  ),
                ),
              ],
            )
          : Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(icon, color: color, size: 16),
                    const SizedBox(width: 6),
                    Text(
                      label,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                        fontWeight: FontWeight.w500,
                        decoration: TextDecoration.none,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  value,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                    decoration: TextDecoration.none,
                  ),
                ),
              ],
            ),
    );
  }

  Widget _buildTimelineList() {
    // 使用自訂結果或原始結果
    final result = _customResult ?? widget.result;

    return Scrollbar(
      controller: _scrollController,
      thumbVisibility: true,
      child: ListView.builder(
        controller: _scrollController,
        itemCount: result.timeline.length,
        padding: const EdgeInsets.only(bottom: 8),
        itemBuilder: (context, index) {
          final profection = result.timeline[index];
          final isCurrent = profection.isCurrent;
          final isFirst = index == 0;
          final isLast = index == result.timeline.length - 1;

          return Container(
            margin: EdgeInsets.only(
              bottom: isLast ? 0 : 12,
              top: isFirst ? 0 : 0,
            ),
            child: _buildTimelineItem(profection, isCurrent, index),
          );
        },
      ),
    );
  }

  Widget _buildTimelineItem(ProfectionData profection, bool isCurrent, int index) {
    final colors = _getHouseColors(profection.profectionHouse);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: isCurrent
            ? LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Colors.purple.shade50,
                  Colors.purple.shade100,
                ],
              )
            : LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Colors.grey.shade50,
                  Colors.grey.shade100,
                ],
              ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isCurrent ? AppColors.indigoSurface : Colors.grey.shade300,
          width: isCurrent ? 2 : 1,
        ),
        boxShadow: [
          BoxShadow(
            color: (isCurrent ? AppColors.indigoSurface : Colors.grey).withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // 年齡圓圈
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: isCurrent
                    ? [Colors.purple.shade500, Colors.purple.shade700]
                    : [colors['primary']!, colors['secondary']!],
              ),
              borderRadius: BorderRadius.circular(25),
              boxShadow: [
                BoxShadow(
                  color: (isCurrent ? AppColors.indigoSurface : colors['primary']!).withOpacity(0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Center(
              child: Text(
                '${profection.currentAge}',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  decoration: TextDecoration.none,
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),

          // 詳細信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 宮位和星座
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: colors['primary']!.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: colors['primary']!.withOpacity(0.3)),
                      ),
                      child: Text(
                        '第${profection.profectionHouse}宮',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: colors['primary'],
                          decoration: TextDecoration.none,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        profection.profectionSign,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: isCurrent ? AppColors.indigoSurface : Colors.black87,
                          decoration: TextDecoration.none,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),

                // 主星
                Row(
                  children: [
                    Icon(
                      Icons.star,
                      size: 16,
                      color: Colors.amber.shade600,
                    ),
                    const SizedBox(width: 6),
                    Text(
                      '主星：${profection.timeLordPlanetName}',
                      style: const TextStyle(
                        fontSize: 13,
                        fontWeight: FontWeight.w500,
                        color: Colors.black87,
                        decoration: TextDecoration.none,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 6),

                // 主題
                Text(
                  profection.houseTheme,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                    height: 1.3,
                    decoration: TextDecoration.none,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),

          // 當前標記
          if (isCurrent)
            Container(
              padding: const EdgeInsets.all(6),
              decoration: BoxDecoration(
                color: AppColors.indigoSurface,
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Icon(
                Icons.schedule,
                color: Colors.white,
                size: 16,
              ),
            ),
        ],
      ),
    );
  }

  /// 構建解讀按鈕
  Widget _buildInterpretationButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: () => _navigateToInterpretation(),
        icon: const Icon(
          Icons.psychology,
          size: 20,
        ),
        label: const Text(
          '解讀當前小限法',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.solarAmber,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 2,
        ),
      ),
    );
  }

  /// 導航到 AI 解讀頁面
  void _navigateToInterpretation() {
    if (!mounted) return;

    // 使用自訂結果或原始結果
    final result = _customResult ?? widget.result;
    final current = result.currentProfection;

    // 創建一個基於原始星盤的 ChartData，但添加小限法信息
    final chartData = ChartData(
      chartType: widget.viewModel.chartData.chartType,
      primaryPerson: widget.viewModel.chartData.primaryPerson,
      secondaryPerson: widget.viewModel.chartData.secondaryPerson,
      specificDate: widget.viewModel.chartData.specificDate,
      planets: widget.viewModel.chartData.planets,
      houses: widget.viewModel.chartData.houses,
      aspects: widget.viewModel.chartData.aspects,
    );

    // 先執行導航，然後關閉卡片
    try {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) {
            return AIInterpretationResultPage(
              chartData: chartData,
              interpretationTitle: '小限法解讀',
              subtitle: '${current.currentAge}歲 - 第${current.profectionHouse}宮 ${current.profectionSign}',
              suggestedQuestions: [
                _buildProfectionInterpretationPrompt(current),
              ],
              autoExecuteFirstQuestion: true,
            );
          },
        ),
      );

      // 導航成功後關閉小限法卡片
      widget.onClose();

    } catch (e) {
      // 如果導航失敗，可以在這裡處理錯誤
      debugPrint('導航到解讀頁面失敗: $e');
    }
  }

  /// 構建小限法專用的解讀提示詞
  String _buildProfectionInterpretationPrompt(ProfectionData current) {
    return '''
請基於這個本命盤為用戶提供詳細的小限法（Profection）解讀分析：

當前小限法信息：
- 年齡：${current.currentAge}歲
- 小限宮位：第${current.profectionHouse}宮
- 小限星座：${current.profectionSign}
- 時間主星：${current.timeLordPlanetName}
- 宮位主題：${current.houseTheme}

請提供以下分析：

1. 當前年度主題分析
   - 第${current.profectionHouse}宮在這一年的重要意義
   - ${current.profectionSign}座能量如何影響這一年的發展
   - 這個宮位代表的生活領域會有什麼重點

2. 時間主星 ${current.timeLordPlanetName} 的影響
   - ${current.timeLordPlanetName}在本命盤中的位置和相位
   - 作為這一年的時間主星，它會帶來什麼樣的能量
   - 如何善用${current.timeLordPlanetName}的力量來度過這一年

3. 生活重點與機會
   - 這一年應該重點關注的生活領域
   - 可能出現的機會和挑戰
   - 如何把握第${current.profectionHouse}宮帶來的成長機會

4. 實用建議與指導
   - 針對${current.currentAge}歲這個年齡的具體建議
   - 如何與${current.profectionSign}座的能量協調
   - 這一年的行動策略和注意事項

5. 與本命盤的呼應
   - 小限法如何與本命盤的配置產生共鳴
   - 這一年的主題如何反映個人的長期發展方向
   - 從占星學角度看這一年在人生中的意義

請用溫暖、正面且實用的語調提供分析，幫助用戶更好地理解和運用小限法的智慧來規劃這一年的生活。
''';
  }

  Map<String, Color> _getHouseColors(int house) {
    final colorSets = [
      {'primary': Colors.red.shade600, 'secondary': Colors.red.shade800},
      {'primary': Colors.green.shade600, 'secondary': Colors.green.shade800},
      {'primary': Colors.blue.shade600, 'secondary': Colors.blue.shade800},
      {'primary': Colors.orange.shade600, 'secondary': Colors.orange.shade800},
      {'primary': Colors.teal.shade600, 'secondary': Colors.teal.shade800},
      {'primary': Colors.indigo.shade600, 'secondary': Colors.indigo.shade800},
      {'primary': Colors.pink.shade600, 'secondary': Colors.pink.shade800},
      {'primary': Colors.cyan.shade600, 'secondary': Colors.cyan.shade800},
      {'primary': Colors.amber.shade600, 'secondary': Colors.amber.shade800},
      {'primary': Colors.deepOrange.shade600, 'secondary': Colors.deepOrange.shade800},
      {'primary': Colors.lightBlue.shade600, 'secondary': Colors.lightBlue.shade800},
      {'primary': Colors.deepPurple.shade600, 'secondary': Colors.deepPurple.shade800},
    ];

    return colorSets[(house - 1) % colorSets.length];
  }

  /// 顯示日期時間選擇器
  Future<void> _showDateTimePicker() async {
    if (_isCalculating) return;

    final initialDate = _customDate ?? DateTime.now();

    // 創建一個新的 OverlayEntry 來顯示日期時間選擇器
    OverlayEntry? overlayEntry;
    DateTime? selectedDateTime;

    overlayEntry = OverlayEntry(
      builder: (BuildContext overlayContext) {
        return Material(
          color: Colors.black87, // 更深的背景，提高對比度
          child: GestureDetector(
            onTap: () {
              // 點擊背景關閉
              overlayEntry?.remove();
            },
            child: SizedBox(
              width: double.infinity,
              height: double.infinity,
              child: GestureDetector(
                onTap: () {}, // 防止點擊內容區域時關閉
                child: Align(
                  alignment: Alignment.bottomCenter,
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.white, // 確保選擇器背景是白色
                      borderRadius: const BorderRadius.vertical(
                        top: Radius.circular(16),
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.3),
                          blurRadius: 10,
                          offset: const Offset(0, -2),
                        ),
                      ],
                    ),
                    child: DateTimePickerBottomSheet(
                      initialDateTime: initialDate,
                      title: '選擇小限法分析時間',
                      minDate: DateTime(1800, 1, 1),
                      maxDate: DateTime(2100, 12, 31),
                      defaultDateTime: DateTime.now(),
                      onDateTimeChanged: (DateTime dateTime) {
                        selectedDateTime = dateTime;
                        overlayEntry?.remove();
                      },
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );

    // 插入到根 Overlay
    Overlay.of(context, rootOverlay: true).insert(overlayEntry);

    // 等待選擇完成
    await Future.delayed(const Duration(milliseconds: 100));

    // 等待 overlay 被移除或用戶選擇
    while (overlayEntry.mounted) {
      await Future.delayed(const Duration(milliseconds: 100));
    }

    if (selectedDateTime != null) {
      await _updateCustomTime(selectedDateTime!);
    }
  }

  /// 更新自訂時間並重新計算小限法
  Future<void> _updateCustomTime(DateTime customDate) async {
    if (_isCalculating) return;

    setState(() {
      _isCalculating = true;
      _customDate = customDate;
    });

    try {
      // 使用 ProfectionService 重新計算小限法數據
      final profectionService = ProfectionService();
      final newResult = await profectionService.calculateProfectionTimeline(
        widget.viewModel.primaryPerson,
        currentDate: customDate,
        yearsRange: 10, // 計算前後各10年
        housesData: widget.viewModel.chartData.houses,
      );

      if (mounted) {
        setState(() {
          _customResult = newResult;
          _isCalculating = false;
        });
      }
    } catch (e) {
      logger.e('重新計算小限法時出錯: $e');
      if (mounted) {
        setState(() {
          _isCalculating = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('計算小限法時出錯: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// 重置為當前時間
  Future<void> _resetToCurrentTime() async {
    if (_isCalculating) return;

    setState(() {
      _customDate = null;
      _customResult = null;
    });
  }

  /// 格式化日期時間顯示
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}/${dateTime.month.toString().padLeft(2, '0')}/${dateTime.day.toString().padLeft(2, '0')} '
           '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
