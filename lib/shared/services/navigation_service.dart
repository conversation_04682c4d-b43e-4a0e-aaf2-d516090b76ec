import 'package:flutter/material.dart';

/// 全域導航服務
/// 提供在沒有 BuildContext 的情況下進行導航的能力
class NavigationService {
  static final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();
  
  /// 獲取當前上下文
  static BuildContext? get currentContext => navigatorKey.currentContext;
  
  /// 導航到指定頁面
  static Future<T?> navigateTo<T extends Object?>(Widget page) async {
    final context = currentContext;
    if (context != null) {
      return Navigator.push<T>(
        context,
        MaterialPageRoute(builder: (context) => page),
      );
    }
    return null;
  }
  
  /// 替換當前頁面
  static Future<T?> navigateAndReplace<T extends Object?>(Widget page) async {
    final context = currentContext;
    if (context != null) {
      return Navigator.pushReplacement<T, void>(
        context,
        MaterialPageRoute(builder: (context) => page),
      );
    }
    return null;
  }
  
  /// 導航到指定路由
  static Future<T?> navigateToRoute<T extends Object?>(String routeName, {Object? arguments}) async {
    final context = currentContext;
    if (context != null) {
      return Navigator.pushNamed<T>(context, routeName, arguments: arguments);
    }
    return null;
  }
  
  /// 返回上一頁
  static void goBack<T extends Object?>([T? result]) {
    final context = currentContext;
    if (context != null && Navigator.canPop(context)) {
      Navigator.pop<T>(context, result);
    }
  }
  
  /// 返回到根頁面
  static void goBackToRoot() {
    final context = currentContext;
    if (context != null) {
      Navigator.popUntil(context, (route) => route.isFirst);
    }
  }
}