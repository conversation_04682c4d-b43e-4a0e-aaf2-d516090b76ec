import '../../data/models/user/birth_data.dart';
import '../utils/logger_utils.dart';

/// 匹配結果的可信度等級
enum MatchConfidence {
  perfect,    // 完美匹配（所有關鍵資訊都相符）
  high,       // 高可信度（姓名+日期+地點匹配）
  medium,     // 中等可信度（姓名+日期匹配且唯一）
  low,        // 低可信度（僅姓名匹配且唯一）
  none,       // 無匹配
}

/// 匹配結果
class MatchResult {
  final BirthData? person;
  final MatchConfidence confidence;
  final String reason;

  MatchResult(this.person, this.confidence, this.reason);

  bool get isValid => person != null && confidence != MatchConfidence.none;
}

/// 安全的人物匹配工具，避免 ID 重複導致的錯誤匹配
class PersonMatcher {

  /// 安全地匹配人物，避免 ID 重複問題
  static MatchResult findMatchingPerson(
    List<BirthData> candidates,
    Map<String, dynamic> targetData,
  ) {
    if (candidates.isEmpty) {
      return MatchResult(null, MatchConfidence.none, '候選列表為空');
    }

    try {
      final targetName = targetData['name'] as String?;
      final targetBirthDate = targetData['birthDate'] as String?;
      final targetBirthPlace = targetData['birthPlace'] as String?;
      final targetLatitude = targetData['latitude'] as double?;
      final targetLongitude = targetData['longitude'] as double?;
      final targetId = targetData['id'] as String?;

      if (targetName == null || targetBirthDate == null) {
        return MatchResult(null, MatchConfidence.none, '目標數據缺少必要資訊');
      }

      // 第一優先：完美匹配（所有關鍵資訊都相符）
      final perfectMatch = _findPerfectMatch(
        candidates, targetName, targetBirthDate, targetBirthPlace, 
        targetLatitude, targetLongitude
      );
      if (perfectMatch != null) {
        return MatchResult(perfectMatch, MatchConfidence.perfect, 
          '完美匹配：姓名、出生日期、地點、經緯度都相符');
      }

      // 第二優先：高可信度匹配（姓名+日期+地點）
      final highConfidenceMatch = _findHighConfidenceMatch(
        candidates, targetName, targetBirthDate, targetBirthPlace
      );
      if (highConfidenceMatch != null) {
        return MatchResult(highConfidenceMatch, MatchConfidence.high,
          '高可信度匹配：姓名、出生日期、出生地點都相符');
      }

      // 第三優先：中等可信度匹配（姓名+日期，但確保唯一性）
      final mediumConfidenceMatch = _findMediumConfidenceMatch(
        candidates, targetName, targetBirthDate
      );
      if (mediumConfidenceMatch != null) {
        return MatchResult(mediumConfidenceMatch, MatchConfidence.medium,
          '中等可信度匹配：姓名和出生日期相符且唯一');
      }

      // 第四優先：謹慎的 ID 匹配（只有在其他資訊也相符時才使用）
      if (targetId != null) {
        final idMatch = _findSafeIdMatch(
          candidates, targetId, targetName, targetBirthDate
        );
        if (idMatch != null) {
          return MatchResult(idMatch, MatchConfidence.medium,
            'ID 匹配且基本資訊相符');
        }
      }

      // 最後優先：低可信度匹配（僅姓名，但確保唯一性）
      final lowConfidenceMatch = _findLowConfidenceMatch(
        candidates, targetName
      );
      if (lowConfidenceMatch != null) {
        return MatchResult(lowConfidenceMatch, MatchConfidence.low,
          '低可信度匹配：僅姓名相符且唯一');
      }

      return MatchResult(null, MatchConfidence.none, '未找到匹配的人物');

    } catch (e) {
      logger.e('人物匹配時出錯: $e');
      return MatchResult(null, MatchConfidence.none, '匹配過程出錯: $e');
    }
  }

  /// 完美匹配：所有關鍵資訊都相符
  static BirthData? _findPerfectMatch(
    List<BirthData> candidates,
    String targetName,
    String targetBirthDate,
    String? targetBirthPlace,
    double? targetLatitude,
    double? targetLongitude,
  ) {
    if (targetBirthPlace == null || targetLatitude == null || targetLongitude == null) {
      return null;
    }

    final matches = candidates.where((person) =>
      person.name == targetName &&
      person.dateTime.toString() == targetBirthDate &&
      person.birthPlace == targetBirthPlace &&
      (person.latitude - targetLatitude).abs() < 0.001 &&
      (person.longitude - targetLongitude).abs() < 0.001
    ).toList();

    if (matches.length == 1) {
      logger.d('找到完美匹配: ${matches.first.name}');
      return matches.first;
    } else if (matches.length > 1) {
      logger.w('發現多個完美匹配，這不應該發生: ${matches.map((p) => p.name).join(', ')}');
      return matches.first; // 返回第一個，但記錄警告
    }

    return null;
  }

  /// 高可信度匹配：姓名+日期+地點
  static BirthData? _findHighConfidenceMatch(
    List<BirthData> candidates,
    String targetName,
    String targetBirthDate,
    String? targetBirthPlace,
  ) {
    if (targetBirthPlace == null) return null;

    final matches = candidates.where((person) =>
      person.name == targetName &&
      person.dateTime.toString() == targetBirthDate &&
      person.birthPlace == targetBirthPlace
    ).toList();

    if (matches.length == 1) {
      logger.d('找到高可信度匹配: ${matches.first.name}');
      return matches.first;
    } else if (matches.length > 1) {
      logger.w('發現多個高可信度匹配: ${matches.map((p) => p.name).join(', ')}');
      // 可以進一步通過經緯度篩選
      return matches.first;
    }

    return null;
  }

  /// 中等可信度匹配：姓名+日期，確保唯一性
  static BirthData? _findMediumConfidenceMatch(
    List<BirthData> candidates,
    String targetName,
    String targetBirthDate,
  ) {
    final matches = candidates.where((person) =>
      person.name == targetName &&
      person.dateTime.toString() == targetBirthDate
    ).toList();

    if (matches.length == 1) {
      logger.d('找到中等可信度匹配: ${matches.first.name}');
      return matches.first;
    } else if (matches.length > 1) {
      logger.w('發現多個姓名和日期相同的人物: ${matches.map((p) => p.name).join(', ')}');
      return null; // 不確定選哪個，返回 null
    }

    return null;
  }

  /// 安全的 ID 匹配：只有在其他資訊也相符時才使用
  static BirthData? _findSafeIdMatch(
    List<BirthData> candidates,
    String targetId,
    String targetName,
    String targetBirthDate,
  ) {
    final idMatches = candidates.where((person) => person.id == targetId).toList();

    if (idMatches.isEmpty) {
      return null;
    }

    if (idMatches.length == 1) {
      final candidate = idMatches.first;
      
      // 驗證基本資訊是否相符
      if (candidate.name == targetName || candidate.dateTime.toString() == targetBirthDate) {
        logger.d('找到安全的 ID 匹配: ${candidate.name}');
        return candidate;
      } else {
        logger.w('ID 匹配但基本資訊不符: ID=$targetId, 候選人=${candidate.name}, 目標=${targetName}');
        return null;
      }
    } else {
      logger.w('發現重複的 ID: $targetId，有 ${idMatches.length} 個匹配');
      
      // 如果有多個相同 ID，嘗試通過姓名和日期進一步篩選
      final nameAndDateMatches = idMatches.where((person) =>
        person.name == targetName && person.dateTime.toString() == targetBirthDate
      ).toList();

      if (nameAndDateMatches.length == 1) {
        logger.d('通過姓名和日期篩選出唯一的 ID 匹配: ${nameAndDateMatches.first.name}');
        return nameAndDateMatches.first;
      } else {
        logger.w('無法通過 ID 確定唯一匹配');
        return null;
      }
    }
  }

  /// 低可信度匹配：僅姓名，確保唯一性
  static BirthData? _findLowConfidenceMatch(
    List<BirthData> candidates,
    String targetName,
  ) {
    final matches = candidates.where((person) => person.name == targetName).toList();

    if (matches.length == 1) {
      logger.d('找到低可信度匹配（僅姓名）: ${matches.first.name}');
      return matches.first;
    } else if (matches.length > 1) {
      logger.w('發現多個同名人物: ${matches.map((p) => p.name).join(', ')}');
      return null; // 不確定選哪個
    }

    return null;
  }

  /// 檢查人物列表中是否有重複的 ID
  static Map<String, List<BirthData>> findDuplicateIds(List<BirthData> people) {
    final idGroups = <String, List<BirthData>>{};
    
    for (final person in people) {
      idGroups.putIfAbsent(person.id, () => []).add(person);
    }

    // 只返回有重複的 ID
    return Map.fromEntries(
      idGroups.entries.where((entry) => entry.value.length > 1)
    );
  }

  /// 生成人物匹配報告
  static String generateMatchReport(List<BirthData> people) {
    final duplicates = findDuplicateIds(people);
    final buffer = StringBuffer();

    buffer.writeln('=== 人物匹配報告 ===');
    buffer.writeln('總人數: ${people.length}');
    buffer.writeln('唯一 ID 數量: ${people.map((p) => p.id).toSet().length}');

    if (duplicates.isNotEmpty) {
      buffer.writeln('\n⚠️ 發現重複的 ID:');
      duplicates.forEach((id, persons) {
        buffer.writeln('ID: $id (${persons.length} 個人)');
        for (final person in persons) {
          buffer.writeln('  - ${person.name} (${person.dateTime})');
        }
      });
    } else {
      buffer.writeln('\n✅ 沒有發現重複的 ID');
    }

    return buffer.toString();
  }
}
