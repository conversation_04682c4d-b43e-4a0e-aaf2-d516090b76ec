import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:google_sign_in/google_sign_in.dart';

import '../../core/utils/logger_utils.dart';

/// Google Sign-In 專門診斷工具
class GoogleSignInDiagnostic {
  
  /// 執行完整的 Google Sign-In 診斷
  static Future<Map<String, dynamic>> runFullDiagnostic() async {
    final result = <String, dynamic>{
      'timestamp': DateTime.now().toIso8601String(),
      'platform': kIsWeb ? 'web' : Platform.operatingSystem,
    };
    
    logger.i('開始 Google Sign-In 完整診斷...');
    
    // 1. 環境檢查
    result['environment'] = await _checkEnvironment();
    
    // 2. 配置檢查
    result['configuration'] = await _checkConfiguration();
    
    // 3. 服務狀態檢查
    result['serviceStatus'] = await _checkServiceStatus();
    
    // 4. 認證流程測試
    result['authenticationTest'] = await _testAuthentication();
    
    // 5. 生成建議
    result['recommendations'] = _generateRecommendations(result);
    
    logger.i('Google Sign-In 診斷完成');
    return result;
  }
  
  /// 檢查環境
  static Future<Map<String, dynamic>> _checkEnvironment() async {
    final env = <String, dynamic>{};
    
    try {
      // 平台信息
      env['platform'] = kIsWeb ? 'web' : Platform.operatingSystem;
      env['isWeb'] = kIsWeb;
      
      if (!kIsWeb) {
        env['platformVersion'] = Platform.operatingSystemVersion;
      }
      
      // Google Play 服務檢查（Android）
      if (!kIsWeb && Platform.isAndroid) {
        try {
          // 嘗試檢查 Google Play 服務
          final googleSignIn = GoogleSignIn();
          await googleSignIn.isSignedIn();
          env['googlePlayServices'] = 'available';
        } catch (e) {
          env['googlePlayServices'] = 'error';
          env['googlePlayServicesError'] = e.toString();
        }
      }
      
      env['status'] = 'success';
    } catch (e) {
      env['status'] = 'error';
      env['error'] = e.toString();
    }
    
    return env;
  }
  
  /// 檢查配置
  static Future<Map<String, dynamic>> _checkConfiguration() async {
    final config = <String, dynamic>{};
    
    try {
      config['scopes'] = [
        'email',
        'https://www.googleapis.com/auth/userinfo.profile',
        'openid',
      ];
      
      // 檢查配置文件
      if (!kIsWeb && Platform.isAndroid) {
        config['configFile'] = 'google-services.json';
        config['configLocation'] = 'android/app/google-services.json';
      } else if (!kIsWeb && Platform.isIOS) {
        config['configFile'] = 'GoogleService-Info.plist';
        config['configLocation'] = 'ios/Runner/GoogleService-Info.plist';
      }
      
      config['status'] = 'success';
    } catch (e) {
      config['status'] = 'error';
      config['error'] = e.toString();
    }
    
    return config;
  }
  
  /// 檢查服務狀態
  static Future<Map<String, dynamic>> _checkServiceStatus() async {
    final status = <String, dynamic>{};
    
    try {
      final googleSignIn = GoogleSignIn(
        scopes: [
          'email',
          'https://www.googleapis.com/auth/userinfo.profile',
          'openid',
        ],
      );
      
      // 檢查是否已登入
      final isSignedIn = await googleSignIn.isSignedIn();
      status['isSignedIn'] = isSignedIn;
      
      if (isSignedIn) {
        final currentUser = googleSignIn.currentUser;
        status['currentUser'] = {
          'email': currentUser?.email,
          'displayName': currentUser?.displayName,
          'id': currentUser?.id,
          'photoUrl': currentUser?.photoUrl,
        };
        
        // 檢查當前用戶的認證信息
        if (currentUser != null) {
          try {
            final auth = await currentUser.authentication;
            status['currentAuthentication'] = {
              'hasIdToken': auth.idToken != null,
              'hasAccessToken': auth.accessToken != null,
              'idTokenLength': auth.idToken?.length ?? 0,
              'accessTokenLength': auth.accessToken?.length ?? 0,
            };
          } catch (e) {
            status['authenticationError'] = e.toString();
          }
        }
      }
      
      // 嘗試靜默登入
      try {
        final silentUser = await googleSignIn.signInSilently();
        status['silentSignIn'] = silentUser != null ? 'success' : 'no_user';
        if (silentUser != null) {
          status['silentUser'] = {
            'email': silentUser.email,
            'displayName': silentUser.displayName,
          };
        }
      } catch (e) {
        status['silentSignInError'] = e.toString();
      }
      
      status['status'] = 'success';
    } catch (e) {
      status['status'] = 'error';
      status['error'] = e.toString();
    }
    
    return status;
  }
  
  /// 測試認證流程
  static Future<Map<String, dynamic>> _testAuthentication() async {
    final test = <String, dynamic>{};
    
    try {
      logger.i('開始測試 Google 認證流程...');
      
      final googleSignIn = GoogleSignIn(
        scopes: [
          'email',
          'https://www.googleapis.com/auth/userinfo.profile',
          'openid',
        ],
      );
      
      // 先登出確保乾淨狀態
      try {
        await googleSignIn.signOut();
        test['signOut'] = 'success';
      } catch (e) {
        test['signOutError'] = e.toString();
      }
      
      // 嘗試登入（這會彈出登入對話框）
      logger.i('嘗試 Google 登入（會彈出對話框）...');
      final user = await googleSignIn.signIn();
      
      if (user == null) {
        test['result'] = 'canceled';
        test['message'] = '用戶取消登入';
      } else {
        test['result'] = 'success';
        test['user'] = {
          'email': user.email,
          'displayName': user.displayName,
          'id': user.id,
          'photoUrl': user.photoUrl,
        };
        
        // 獲取認證信息
        try {
          logger.i('獲取認證信息...');
          final auth = await user.authentication;
          
          test['authentication'] = {
            'hasIdToken': auth.idToken != null,
            'hasAccessToken': auth.accessToken != null,
            'idTokenLength': auth.idToken?.length ?? 0,
            'accessTokenLength': auth.accessToken?.length ?? 0,
          };
          
          if (auth.idToken != null) {
            test['idTokenPreview'] = '${auth.idToken!.substring(0, 50)}...';
          }

          if (auth.accessToken != null) {
            test['accessTokenPreview'] = '${auth.accessToken!.substring(0, 50)}...';
          }
          
          // 檢查 ID Token 是否為空的問題
          if (auth.idToken == null) {
            test['idTokenIssue'] = {
              'problem': 'ID Token 為空',
              'possibleCauses': [
                'openid scope 未正確配置',
                'Google 服務配置問題',
                '網路連接問題',
                'Google Play 服務版本過舊',
                'SHA-1 指紋配置錯誤',
              ],
            };
          }
          
        } catch (authError) {
          test['authenticationError'] = authError.toString();
          test['authenticationErrorType'] = authError.runtimeType.toString();
        }
        
        // 測試清除快取和重新獲取
        try {
          logger.i('測試清除快取和重新獲取...');
          await user.clearAuthCache();
          final retryAuth = await user.authentication;
          
          test['retryAuthentication'] = {
            'hasIdToken': retryAuth.idToken != null,
            'hasAccessToken': retryAuth.accessToken != null,
            'idTokenLength': retryAuth.idToken?.length ?? 0,
            'accessTokenLength': retryAuth.accessToken?.length ?? 0,
          };
        } catch (retryError) {
          test['retryError'] = retryError.toString();
        }
      }
      
    } on PlatformException catch (e) {
      test['result'] = 'platform_error';
      test['platformError'] = {
        'code': e.code,
        'message': e.message,
        'details': e.details,
      };
    } catch (e) {
      test['result'] = 'error';
      test['error'] = e.toString();
      test['errorType'] = e.runtimeType.toString();
    }
    
    return test;
  }
  
  /// 生成建議
  static List<String> _generateRecommendations(Map<String, dynamic> diagnostic) {
    final recommendations = <String>[];
    
    // 檢查環境問題
    final env = diagnostic['environment'] as Map<String, dynamic>?;
    if (env?['googlePlayServices'] == 'error') {
      recommendations.add('更新 Google Play 服務到最新版本');
    }
    
    // 檢查認證問題
    final authTest = diagnostic['authenticationTest'] as Map<String, dynamic>?;
    if (authTest?['result'] == 'success') {
      final auth = authTest?['authentication'] as Map<String, dynamic>?;
      if (auth?['hasIdToken'] == false) {
        recommendations.addAll([
          '檢查 Google 開發者控制台中的 OAuth 2.0 配置',
          '確認 SHA-1 指紋已正確添加到 Firebase 項目',
          '檢查 google-services.json 或 GoogleService-Info.plist 是否為最新版本',
          '確認 openid scope 已包含在配置中',
          '嘗試重新下載配置文件並替換現有文件',
        ]);
      }
    } else if (authTest?['result'] == 'platform_error') {
      final platformError = authTest?['platformError'] as Map<String, dynamic>?;
      final code = platformError?['code'] as String?;
      
      switch (code) {
        case 'sign_in_failed':
          recommendations.addAll([
            '檢查網路連接',
            '確認 Google 服務配置正確',
            '重新啟動應用程式',
          ]);
          break;
        case 'network_error':
          recommendations.addAll([
            '檢查網路連接',
            '嘗試切換到不同的網路',
            '檢查防火牆設定',
          ]);
          break;
        default:
          recommendations.add('查看 Google Sign-In 官方文檔以解決平台特定問題');
      }
    }
    
    // 通用建議
    if (recommendations.isEmpty) {
      recommendations.addAll([
        '確認已正確配置 Google 服務',
        '檢查網路連接',
        '重新啟動應用程式',
        '清除應用程式快取',
      ]);
    }
    
    return recommendations;
  }
  
  /// 打印診斷結果
  static void printDiagnostic(Map<String, dynamic> diagnostic) {
    logger.i('=== Google Sign-In 診斷結果 ===');
    logger.i('時間: ${diagnostic['timestamp']}');
    logger.i('平台: ${diagnostic['platform']}');
    
    // 環境
    final env = diagnostic['environment'] as Map<String, dynamic>?;
    if (env != null) {
      logger.i('--- 環境檢查 ---');
      logger.i('狀態: ${env['status']}');
      if (env['googlePlayServices'] != null) {
        logger.i('Google Play 服務: ${env['googlePlayServices']}');
      }
    }
    
    // 服務狀態
    final status = diagnostic['serviceStatus'] as Map<String, dynamic>?;
    if (status != null) {
      logger.i('--- 服務狀態 ---');
      logger.i('已登入: ${status['isSignedIn']}');
      if (status['silentSignIn'] != null) {
        logger.i('靜默登入: ${status['silentSignIn']}');
      }
    }
    
    // 認證測試
    final authTest = diagnostic['authenticationTest'] as Map<String, dynamic>?;
    if (authTest != null) {
      logger.i('--- 認證測試 ---');
      logger.i('結果: ${authTest['result']}');
      
      if (authTest['authentication'] != null) {
        final auth = authTest['authentication'] as Map<String, dynamic>;
        logger.i('ID Token: ${auth['hasIdToken'] ? "存在" : "缺失"}');
        logger.i('Access Token: ${auth['hasAccessToken'] ? "存在" : "缺失"}');
      }
      
      if (authTest['idTokenIssue'] != null) {
        final issue = authTest['idTokenIssue'] as Map<String, dynamic>;
        logger.w('ID Token 問題: ${issue['problem']}');
      }
    }
    
    // 建議
    final recommendations = diagnostic['recommendations'] as List<String>?;
    if (recommendations != null && recommendations.isNotEmpty) {
      logger.i('--- 建議 ---');
      for (int i = 0; i < recommendations.length; i++) {
        logger.i('${i + 1}. ${recommendations[i]}');
      }
    }
    
    logger.i('=== 診斷結束 ===');
  }
}
