import 'dart:convert';
import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;

import '../../astreal.dart';

/// 設備信息服務
/// 提供獲取設備平台信息和 IP 地址的功能
class DeviceInfoService {
  static DeviceInfoPlugin? _deviceInfo;
  static String? _cachedPlatform;
  static String? _cachedIpAddress;
  static DateTime? _lastIpCheck;

  /// 初始化設備信息服務
  static Future<void> initialize() async {
    try {
      _deviceInfo = DeviceInfoPlugin();
      logger.i('設備信息服務初始化成功');
    } catch (e) {
      logger.e('設備信息服務初始化失敗: $e');
    }
  }

  /// 獲取平台信息
  static Future<String> getPlatformInfo() async {
    // 如果已經有快取的平台信息，直接返回
    if (_cachedPlatform != null) {
      return _cachedPlatform!;
    }

    try {
      String platformInfo = '';

      if (kIsWeb) {
        // Web 平台
        final webInfo = await _deviceInfo!.webBrowserInfo;
        platformInfo = 'Web (${webInfo.browserName.name})';
      } else if (Platform.isAndroid) {
        // Android 平台
        final androidInfo = await _deviceInfo!.androidInfo;
        platformInfo = 'Android ${androidInfo.version.release} (${androidInfo.model})';
      } else if (Platform.isIOS) {
        // iOS 平台
        final iosInfo = await _deviceInfo!.iosInfo;
        platformInfo = 'iOS ${iosInfo.systemVersion} (${iosInfo.model})';
      } else if (Platform.isMacOS) {
        // macOS 平台
        final macInfo = await _deviceInfo!.macOsInfo;
        platformInfo = 'macOS ${macInfo.osRelease} (${macInfo.model})';
      } else if (Platform.isWindows) {
        // Windows 平台
        final windowsInfo = await _deviceInfo!.windowsInfo;
        platformInfo = 'Windows ${windowsInfo.displayVersion} (${windowsInfo.productName})';
      } else if (Platform.isLinux) {
        // Linux 平台
        final linuxInfo = await _deviceInfo!.linuxInfo;
        platformInfo = 'Linux (${linuxInfo.name})';
      } else {
        platformInfo = 'Unknown Platform';
      }

      _cachedPlatform = platformInfo;
      logger.d('獲取平台信息: $platformInfo');
      return platformInfo;
    } catch (e) {
      logger.e('獲取平台信息失敗: $e');
      
      // 備用方案：使用基本平台檢測
      String fallbackPlatform = '';
      if (kIsWeb) {
        fallbackPlatform = 'Web';
      } else {
        try {
          if (Platform.isAndroid) fallbackPlatform = 'Android';
          else if (Platform.isIOS) fallbackPlatform = 'iOS';
          else if (Platform.isMacOS) fallbackPlatform = 'macOS';
          else if (Platform.isWindows) fallbackPlatform = 'Windows';
          else if (Platform.isLinux) fallbackPlatform = 'Linux';
          else fallbackPlatform = 'Unknown';
        } catch (e) {
          fallbackPlatform = 'Unknown';
        }
      }
      
      _cachedPlatform = fallbackPlatform;
      return fallbackPlatform;
    }
  }

  /// 獲取用戶的公網 IP 地址
  static Future<String?> getPublicIpAddress() async {
    // 檢查快取是否有效（5分鐘內）
    if (_cachedIpAddress != null && 
        _lastIpCheck != null && 
        DateTime.now().difference(_lastIpCheck!).inMinutes < 5) {
      return _cachedIpAddress;
    }

    try {
      // 嘗試多個 IP 檢測服務，提高成功率
      final ipServices = [
        'https://api.ipify.org?format=json',
        'https://httpbin.org/ip',
        'https://api.myip.com',
        'https://ipapi.co/json/',
      ];

      for (final service in ipServices) {
        try {
          final response = await http.get(
            Uri.parse(service),
            headers: {'User-Agent': 'AstReal-App/1.0'},
          ).timeout(const Duration(seconds: 5));

          if (response.statusCode == 200) {
            final data = json.decode(response.body);
            String? ip;

            // 根據不同服務的回應格式解析 IP
            if (service.contains('ipify.org')) {
              ip = data['ip'] as String?;
            } else if (service.contains('httpbin.org')) {
              ip = data['origin'] as String?;
            } else if (service.contains('myip.com')) {
              ip = data['ip'] as String?;
            } else if (service.contains('ipapi.co')) {
              ip = data['ip'] as String?;
            }

            if (ip != null && _isValidIpAddress(ip)) {
              _cachedIpAddress = ip;
              _lastIpCheck = DateTime.now();
              logger.d('獲取公網 IP 地址成功: $ip (來源: $service)');
              return ip;
            }
          }
        } catch (e) {
          logger.w('IP 服務 $service 請求失敗: $e');
          continue; // 嘗試下一個服務
        }
      }

      logger.w('所有 IP 檢測服務都失敗了');
      return null;
    } catch (e) {
      logger.e('獲取公網 IP 地址失敗: $e');
      return null;
    }
  }

  /// 驗證 IP 地址格式是否有效
  static bool _isValidIpAddress(String ip) {
    // 簡單的 IPv4 格式驗證
    final ipv4Regex = RegExp(r'^(\d{1,3}\.){3}\d{1,3}$');
    if (ipv4Regex.hasMatch(ip)) {
      final parts = ip.split('.');
      return parts.every((part) {
        final num = int.tryParse(part);
        return num != null && num >= 0 && num <= 255;
      });
    }

    // 簡單的 IPv6 格式驗證
    final ipv6Regex = RegExp(r'^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$');
    return ipv6Regex.hasMatch(ip);
  }

  /// 獲取設備的詳細信息（用於調試）
  static Future<Map<String, dynamic>> getDeviceDetails() async {
    try {
      final details = <String, dynamic>{};

      if (kIsWeb) {
        final webInfo = await _deviceInfo!.webBrowserInfo;
        details.addAll({
          'platform': 'web',
          'browser_name': webInfo.browserName.name,
          'user_agent': webInfo.userAgent,
          'language': webInfo.language,
          'platform_name': webInfo.platform,
        });
      } else if (Platform.isAndroid) {
        final androidInfo = await _deviceInfo!.androidInfo;
        details.addAll({
          'platform': 'android',
          'model': androidInfo.model,
          'manufacturer': androidInfo.manufacturer,
          'brand': androidInfo.brand,
          'android_version': androidInfo.version.release,
          'sdk_int': androidInfo.version.sdkInt,
          'device_id': androidInfo.id,
        });
      } else if (Platform.isIOS) {
        final iosInfo = await _deviceInfo!.iosInfo;
        details.addAll({
          'platform': 'ios',
          'model': iosInfo.model,
          'name': iosInfo.name,
          'system_version': iosInfo.systemVersion,
          'identifier_for_vendor': iosInfo.identifierForVendor,
        });
      } else if (Platform.isMacOS) {
        final macInfo = await _deviceInfo!.macOsInfo;
        details.addAll({
          'platform': 'macos',
          'model': macInfo.model,
          'computer_name': macInfo.computerName,
          'os_release': macInfo.osRelease,
          'kernel_version': macInfo.kernelVersion,
        });
      } else if (Platform.isWindows) {
        final windowsInfo = await _deviceInfo!.windowsInfo;
        details.addAll({
          'platform': 'windows',
          'computer_name': windowsInfo.computerName,
          'product_name': windowsInfo.productName,
          'display_version': windowsInfo.displayVersion,
          'build_number': windowsInfo.buildNumber,
        });
      } else if (Platform.isLinux) {
        final linuxInfo = await _deviceInfo!.linuxInfo;
        details.addAll({
          'platform': 'linux',
          'name': linuxInfo.name,
          'version': linuxInfo.version,
          'id': linuxInfo.id,
          'machine_id': linuxInfo.machineId,
        });
      }

      return details;
    } catch (e) {
      logger.e('獲取設備詳細信息失敗: $e');
      return {'error': e.toString()};
    }
  }

  /// 清除快取
  static void clearCache() {
    _cachedPlatform = null;
    _cachedIpAddress = null;
    _lastIpCheck = null;
    logger.d('設備信息快取已清除');
  }

  /// 獲取用戶的完整設備和網路信息
  static Future<Map<String, String?>> getUserDeviceAndNetworkInfo() async {
    final platform = await getPlatformInfo();
    final ipAddress = await getPublicIpAddress();
    
    return {
      'platform': platform,
      'ip_address': ipAddress,
    };
  }
}
