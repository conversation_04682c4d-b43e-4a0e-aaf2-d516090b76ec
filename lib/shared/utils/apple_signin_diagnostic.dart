import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';

import '../../core/utils/logger_utils.dart';

/// Apple Sign-In 診斷工具
class AppleSignInDiagnostic {
  
  /// 執行完整的 Apple Sign-In 診斷
  static Future<Map<String, dynamic>> runFullDiagnostic() async {
    final result = <String, dynamic>{
      'timestamp': DateTime.now().toIso8601String(),
      'platform': kIsWeb ? 'web' : Platform.operatingSystem,
    };
    
    logger.i('開始 Apple Sign-In 診斷...');
    
    // 1. 檢查平台支援
    result['platformSupport'] = _checkPlatformSupport();
    
    // 2. 檢查 Apple Sign-In 可用性
    result['availability'] = await _checkAvailability();
    
    // 3. 檢查設備和系統要求
    result['systemRequirements'] = await _checkSystemRequirements();
    
    // 4. 測試 Apple Sign-In 流程
    result['signInTest'] = await _testAppleSignIn();
    
    // 5. 分析錯誤 1000
    result['error1000Analysis'] = _analyzeError1000();
    
    // 6. 生成建議
    result['recommendations'] = _generateRecommendations(result);
    
    logger.i('Apple Sign-In 診斷完成');
    return result;
  }
  
  /// 檢查平台支援
  static Map<String, dynamic> _checkPlatformSupport() {
    final support = <String, dynamic>{};
    
    support['isWeb'] = kIsWeb;
    support['isIOS'] = !kIsWeb && Platform.isIOS;
    support['isMacOS'] = !kIsWeb && Platform.isMacOS;
    support['isAndroid'] = !kIsWeb && Platform.isAndroid;
    support['isWindows'] = !kIsWeb && Platform.isWindows;
    support['isLinux'] = !kIsWeb && Platform.isLinux;
    
    // Apple Sign-In 支援的平台
    support['appleSignInSupported'] = support['isIOS'] || support['isMacOS'] || support['isWeb'];
    
    if (!support['appleSignInSupported']) {
      support['reason'] = 'Apple Sign-In 僅支援 iOS、macOS 和 Web 平台';
    }
    
    return support;
  }
  
  /// 檢查 Apple Sign-In 可用性
  static Future<Map<String, dynamic>> _checkAvailability() async {
    final availability = <String, dynamic>{};
    
    try {
      // 檢查 Apple Sign-In 是否可用
      final isAvailable = await SignInWithApple.isAvailable();
      availability['isAvailable'] = isAvailable;
      availability['status'] = 'success';
      
      if (!isAvailable) {
        availability['reason'] = '設備不支援 Apple Sign-In 或未正確配置';
      }
      
    } catch (e) {
      availability['isAvailable'] = false;
      availability['status'] = 'error';
      availability['error'] = e.toString();
      availability['errorType'] = e.runtimeType.toString();
    }
    
    return availability;
  }
  
  /// 檢查系統要求
  static Future<Map<String, dynamic>> _checkSystemRequirements() async {
    final requirements = <String, dynamic>{};
    
    if (!kIsWeb) {
      if (Platform.isIOS) {
        requirements['platform'] = 'iOS';
        requirements['minimumVersion'] = 'iOS 13.0+';
        requirements['note'] = 'Apple Sign-In 需要 iOS 13.0 或更高版本';
        
        // 無法直接獲取 iOS 版本，但可以檢查 API 可用性
        try {
          await SignInWithApple.isAvailable();
          requirements['versionCheck'] = 'passed';
        } catch (e) {
          requirements['versionCheck'] = 'failed';
          requirements['versionError'] = e.toString();
        }
        
      } else if (Platform.isMacOS) {
        requirements['platform'] = 'macOS';
        requirements['minimumVersion'] = 'macOS 10.15+';
        requirements['note'] = 'Apple Sign-In 需要 macOS 10.15 或更高版本';
        
      } else {
        requirements['platform'] = Platform.operatingSystem;
        requirements['supported'] = false;
        requirements['note'] = 'Apple Sign-In 不支援此平台';
      }
    } else {
      requirements['platform'] = 'Web';
      requirements['note'] = 'Web 平台支援 Apple Sign-In';
      requirements['requirements'] = [
        'HTTPS 連接',
        '正確的域名配置',
        'Apple Developer 帳戶配置',
      ];
    }
    
    return requirements;
  }
  
  /// 測試 Apple Sign-In 流程
  static Future<Map<String, dynamic>> _testAppleSignIn() async {
    final test = <String, dynamic>{};
    
    try {
      logger.i('開始測試 Apple Sign-In 流程...');
      
      // 檢查平台支援
      if (kIsWeb || (!Platform.isIOS && !Platform.isMacOS)) {
        test['result'] = 'unsupported';
        test['message'] = '當前平台不支援 Apple Sign-In 測試';
        return test;
      }
      
      // 檢查可用性
      final isAvailable = await SignInWithApple.isAvailable();
      if (!isAvailable) {
        test['result'] = 'unavailable';
        test['message'] = 'Apple Sign-In 在此設備上不可用';
        return test;
      }
      
      // 嘗試獲取憑證（這會彈出 Apple Sign-In 對話框）
      logger.i('嘗試獲取 Apple ID 憑證（會彈出對話框）...');
      
      final credential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
      );
      
      test['result'] = 'success';
      test['credential'] = {
        'userIdentifier': credential.userIdentifier,
        'hasEmail': credential.email != null,
        'hasGivenName': credential.givenName != null,
        'hasFamilyName': credential.familyName != null,
        'hasIdentityToken': credential.identityToken != null,
        'hasAuthorizationCode': credential.authorizationCode.isNotEmpty,
      };
      
      final email = credential.email;
      if (email != null) {
        test['email'] = email;
      }

      final givenName = credential.givenName;
      final familyName = credential.familyName;
      if (givenName != null || familyName != null) {
        test['fullName'] = '${givenName ?? ''} ${familyName ?? ''}'.trim();
      }
      
      logger.i('Apple Sign-In 測試成功');
      
    } on SignInWithAppleAuthorizationException catch (e) {
      test['result'] = 'authorization_error';
      test['errorCode'] = e.code.toString();
      test['errorMessage'] = e.message;
      test['errorDetails'] = e.toString();
      
      // 特別處理錯誤 1000
      if (e.code == AuthorizationErrorCode.unknown) {
        test['isError1000'] = true;
        test['error1000Details'] = {
          'description': '錯誤 1000 通常表示網路問題、配置問題或系統限制',
          'possibleCauses': [
            '網路連接問題',
            'Apple ID 服務暫時不可用',
            '設備時間設定錯誤',
            'Apple Developer 配置問題',
            '系統版本過舊',
            'Keychain 問題',
          ],
        };
      }
      
      logger.e('Apple Sign-In 授權錯誤: ${e.code} - ${e.message}');
      
    } catch (e) {
      test['result'] = 'error';
      test['error'] = e.toString();
      test['errorType'] = e.runtimeType.toString();
      
      logger.e('Apple Sign-In 測試失敗: $e');
    }
    
    return test;
  }
  
  /// 分析錯誤 1000
  static Map<String, dynamic> _analyzeError1000() {
    final analysis = <String, dynamic>{};
    
    analysis['errorCode'] = 1000;
    analysis['officialName'] = 'AuthorizationErrorCode.unknown';
    analysis['description'] = 'Apple Sign-In 未知錯誤，通常與系統或網路問題相關';
    
    analysis['commonCauses'] = [
      {
        'cause': '網路連接問題',
        'description': '無法連接到 Apple ID 服務器',
        'solutions': [
          '檢查網路連接',
          '嘗試切換到不同的網路',
          '檢查防火牆設定',
        ],
      },
      {
        'cause': '設備時間錯誤',
        'description': '設備時間與 Apple 服務器時間不同步',
        'solutions': [
          '檢查設備時間設定',
          '啟用自動時間同步',
          '手動校正時間',
        ],
      },
      {
        'cause': 'Apple ID 服務問題',
        'description': 'Apple ID 服務暫時不可用',
        'solutions': [
          '稍後再試',
          '檢查 Apple 系統狀態頁面',
          '重新啟動設備',
        ],
      },
      {
        'cause': 'Keychain 問題',
        'description': 'iOS Keychain 存儲問題',
        'solutions': [
          '重新啟動設備',
          '登出並重新登入 Apple ID',
          '重置 Keychain（謹慎操作）',
        ],
      },
      {
        'cause': '應用配置問題',
        'description': 'Apple Developer 配置或應用設定問題',
        'solutions': [
          '檢查 Apple Developer 控制台配置',
          '確認 Bundle ID 正確',
          '檢查 Capabilities 設定',
        ],
      },
    ];
    
    analysis['troubleshootingSteps'] = [
      '1. 檢查網路連接和設備時間',
      '2. 重新啟動設備',
      '3. 檢查 Apple ID 登入狀態',
      '4. 嘗試在設定中登出並重新登入 Apple ID',
      '5. 檢查 Apple 系統狀態',
      '6. 如果問題持續，聯繫 Apple 支援',
    ];
    
    return analysis;
  }
  
  /// 生成建議
  static List<String> _generateRecommendations(Map<String, dynamic> diagnostic) {
    final recommendations = <String>[];
    
    // 檢查平台支援
    final platformSupport = diagnostic['platformSupport'] as Map<String, dynamic>?;
    if (platformSupport?['appleSignInSupported'] != true) {
      recommendations.add('Apple Sign-In 僅支援 iOS、macOS 和 Web 平台');
      return recommendations;
    }
    
    // 檢查可用性
    final availability = diagnostic['availability'] as Map<String, dynamic>?;
    if (availability?['isAvailable'] != true) {
      recommendations.addAll([
        '檢查設備是否支援 Apple Sign-In',
        '確認系統版本符合要求（iOS 13.0+ 或 macOS 10.15+）',
        '檢查 Apple ID 登入狀態',
      ]);
    }
    
    // 檢查測試結果
    final signInTest = diagnostic['signInTest'] as Map<String, dynamic>?;
    if (signInTest?['result'] == 'authorization_error') {
      final errorCode = signInTest?['errorCode'] as String?;
      
      if (errorCode == 'AuthorizationErrorCode.unknown') {
        recommendations.addAll([
          '錯誤 1000 解決方案：',
          '• 檢查網路連接和設備時間設定',
          '• 重新啟動設備',
          '• 檢查 Apple ID 登入狀態',
          '• 嘗試登出並重新登入 Apple ID',
          '• 檢查 Apple 系統狀態頁面',
        ]);
      } else {
        recommendations.add('處理 Apple Sign-In 授權錯誤：${signInTest?['errorMessage']}');
      }
    } else if (signInTest?['result'] == 'success') {
      recommendations.add('Apple Sign-In 測試成功，功能正常');
    }
    
    // 通用建議
    if (recommendations.isEmpty) {
      recommendations.addAll([
        'Apple Sign-In 配置看起來正常',
        '如果仍有問題，請檢查網路連接和設備設定',
        '確認 Apple Developer 配置正確',
      ]);
    }
    
    return recommendations;
  }
  
  /// 打印診斷結果
  static void printDiagnostic(Map<String, dynamic> diagnostic) {
    logger.i('=== Apple Sign-In 診斷結果 ===');
    logger.i('時間: ${diagnostic['timestamp']}');
    logger.i('平台: ${diagnostic['platform']}');
    
    // 平台支援
    final platformSupport = diagnostic['platformSupport'] as Map<String, dynamic>?;
    if (platformSupport != null) {
      logger.i('--- 平台支援 ---');
      logger.i('Apple Sign-In 支援: ${platformSupport['appleSignInSupported']}');
      if (platformSupport['reason'] != null) {
        logger.w('原因: ${platformSupport['reason']}');
      }
    }
    
    // 可用性
    final availability = diagnostic['availability'] as Map<String, dynamic>?;
    if (availability != null) {
      logger.i('--- 可用性檢查 ---');
      logger.i('可用: ${availability['isAvailable']}');
      if (availability['error'] != null) {
        logger.w('錯誤: ${availability['error']}');
      }
    }
    
    // 測試結果
    final signInTest = diagnostic['signInTest'] as Map<String, dynamic>?;
    if (signInTest != null) {
      logger.i('--- Apple Sign-In 測試 ---');
      logger.i('結果: ${signInTest['result']}');
      
      if (signInTest['isError1000'] == true) {
        logger.w('檢測到錯誤 1000');
        final error1000Details = signInTest['error1000Details'] as Map<String, dynamic>?;
        if (error1000Details != null) {
          logger.w('描述: ${error1000Details['description']}');
        }
      }
      
      if (signInTest['errorMessage'] != null) {
        logger.w('錯誤訊息: ${signInTest['errorMessage']}');
      }
    }
    
    // 建議
    final recommendations = diagnostic['recommendations'] as List<String>?;
    if (recommendations != null && recommendations.isNotEmpty) {
      logger.i('--- 建議 ---');
      for (int i = 0; i < recommendations.length; i++) {
        logger.i('${i + 1}. ${recommendations[i]}');
      }
    }
    
    logger.i('=== 診斷結束 ===');
  }
}
