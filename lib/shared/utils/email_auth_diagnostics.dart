import 'package:firebase_auth/firebase_auth.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../core/utils/logger_utils.dart';

/// 電子郵件認證診斷和修復工具
class EmailAuthDiagnostics {
  static final EmailAuthDiagnostics _instance = EmailAuthDiagnostics._internal();
  factory EmailAuthDiagnostics() => _instance;
  EmailAuthDiagnostics._internal();

  /// 診斷電子郵件認證問題
  Future<Map<String, dynamic>> diagnoseEmailAuth({
    required String email,
    required String password,
  }) async {
    logger.i('🔍 開始診斷電子郵件認證問題...');
    
    final diagnosis = <String, dynamic>{
      'email': email,
      'timestamp': DateTime.now().toIso8601String(),
    };

    try {
      // 1. 檢查輸入驗證
      diagnosis['input_validation'] = _validateInput(email, password);
      
      // 2. 檢查 Firebase 連接
      diagnosis['firebase_connection'] = await _checkFirebaseConnection();
      
      // 3. 檢查用戶是否存在
      diagnosis['user_exists'] = await _checkUserExists(email);
      
      // 4. 檢查認證狀態
      diagnosis['auth_state'] = await _checkAuthState();
      
      // 5. 嘗試認證並記錄詳細錯誤
      diagnosis['auth_attempt'] = await _attemptAuthentication(email, password);
      
      // 6. 生成診斷報告
      final report = _generateEmailAuthReport(diagnosis);
      logger.i('📋 電子郵件認證診斷報告:\n$report');
      
      return diagnosis;
      
    } catch (e) {
      logger.e('診斷過程中發生錯誤: $e');
      diagnosis['diagnosis_error'] = e.toString();
      return diagnosis;
    }
  }

  /// 驗證輸入
  Map<String, dynamic> _validateInput(String email, String password) {
    final validation = <String, dynamic>{};
    
    // 電子郵件格式驗證
    final emailRegex = RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');
    validation['email_format_valid'] = emailRegex.hasMatch(email);
    validation['email_length'] = email.length;
    
    // 密碼驗證
    validation['password_length'] = password.length;
    validation['password_not_empty'] = password.isNotEmpty;
    validation['password_min_length'] = password.length >= 6;
    
    // 常見問題檢查
    validation['email_has_spaces'] = email.contains(' ');
    validation['password_has_spaces'] = password.startsWith(' ') || password.endsWith(' ');
    
    logger.d('輸入驗證完成: ${validation}');
    return validation;
  }

  /// 檢查 Firebase 連接
  Future<Map<String, dynamic>> _checkFirebaseConnection() async {
    final connection = <String, dynamic>{};
    
    try {
      final auth = FirebaseAuth.instance;
      connection['firebase_initialized'] = true;
      connection['auth_instance_available'] = auth != null;
      connection['current_user'] = auth.currentUser?.uid;
      
      // 檢查 Firebase 配置
      connection['app_name'] = auth.app.name;
      connection['project_id'] = auth.app.options.projectId;
      
      logger.d('Firebase 連接檢查完成');
      
    } catch (e) {
      logger.e('Firebase 連接檢查失敗: $e');
      connection['firebase_initialized'] = false;
      connection['error'] = e.toString();
    }
    
    return connection;
  }

  /// 檢查用戶是否存在
  Future<Map<String, dynamic>> _checkUserExists(String email) async {
    final userCheck = <String, dynamic>{};
    
    try {
      // 使用 fetchSignInMethodsForEmail 檢查用戶是否存在
      final signInMethods = await FirebaseAuth.instance.fetchSignInMethodsForEmail(email);
      
      userCheck['user_exists'] = signInMethods.isNotEmpty;
      userCheck['sign_in_methods'] = signInMethods;
      userCheck['has_password'] = signInMethods.contains('password');
      userCheck['has_google'] = signInMethods.contains('google.com');
      userCheck['has_apple'] = signInMethods.contains('apple.com');
      
      logger.d('用戶存在檢查完成: $email -> ${signInMethods}');
      
    } catch (e) {
      logger.e('用戶存在檢查失敗: $e');
      userCheck['check_failed'] = true;
      userCheck['error'] = e.toString();
    }
    
    return userCheck;
  }

  /// 檢查認證狀態
  Future<Map<String, dynamic>> _checkAuthState() async {
    final authState = <String, dynamic>{};
    
    try {
      final auth = FirebaseAuth.instance;
      final currentUser = auth.currentUser;
      
      authState['has_current_user'] = currentUser != null;
      
      if (currentUser != null) {
        authState['current_user_uid'] = currentUser.uid;
        authState['current_user_email'] = currentUser.email;
        authState['current_user_verified'] = currentUser.emailVerified;
        
        // 檢查 ID Token
        try {
          final idTokenResult = await currentUser.getIdTokenResult();
          authState['id_token_valid'] = true;
          authState['id_token_expiry'] = idTokenResult.expirationTime?.toIso8601String();
          
          // 檢查 token 是否即將過期（30分鐘內）
          final now = DateTime.now();
          final expiry = idTokenResult.expirationTime;
          if (expiry != null) {
            final timeToExpiry = expiry.difference(now);
            authState['token_expires_soon'] = timeToExpiry.inMinutes < 30;
            authState['time_to_expiry_minutes'] = timeToExpiry.inMinutes;
          }
          
        } catch (tokenError) {
          authState['id_token_valid'] = false;
          authState['id_token_error'] = tokenError.toString();
        }
      }
      
      logger.d('認證狀態檢查完成');
      
    } catch (e) {
      logger.e('認證狀態檢查失敗: $e');
      authState['error'] = e.toString();
    }
    
    return authState;
  }

  /// 嘗試認證並記錄詳細錯誤
  Future<Map<String, dynamic>> _attemptAuthentication(String email, String password) async {
    final attempt = <String, dynamic>{};
    
    try {
      logger.i('嘗試電子郵件認證: $email');
      
      final startTime = DateTime.now();
      
      final credential = await FirebaseAuth.instance.signInWithEmailAndPassword(
        email: email,
        password: password,
      );
      
      final endTime = DateTime.now();
      final duration = endTime.difference(startTime);
      
      attempt['success'] = true;
      attempt['duration_ms'] = duration.inMilliseconds;
      attempt['user_uid'] = credential.user?.uid;
      attempt['user_email'] = credential.user?.email;
      attempt['user_verified'] = credential.user?.emailVerified;
      
      logger.i('認證成功: ${credential.user?.uid}');
      
    } on FirebaseAuthException catch (e) {
      attempt['success'] = false;
      attempt['error_code'] = e.code;
      attempt['error_message'] = e.message;
      attempt['error_details'] = {
        'code': e.code,
        'message': e.message,
        'plugin': e.plugin,
        'stackTrace': e.stackTrace?.toString(),
      };
      
      // 分析具體錯誤原因
      attempt['error_analysis'] = _analyzeAuthError(e);
      
      logger.e('認證失敗: ${e.code} - ${e.message}');
      
    } catch (e) {
      attempt['success'] = false;
      attempt['unexpected_error'] = e.toString();
      
      logger.e('認證過程中發生意外錯誤: $e');
    }
    
    return attempt;
  }

  /// 分析認證錯誤
  Map<String, dynamic> _analyzeAuthError(FirebaseAuthException e) {
    final analysis = <String, dynamic>{};
    
    switch (e.code) {
      case 'invalid-credential':
        analysis['likely_cause'] = '認證憑證無效或已過期';
        analysis['possible_reasons'] = [
          '密碼錯誤',
          '用戶不存在',
          '帳戶已被停用',
          'Firebase 配置問題',
          '網路連接問題',
        ];
        analysis['suggested_actions'] = [
          '檢查密碼是否正確',
          '確認用戶帳戶是否存在',
          '檢查 Firebase 配置',
          '嘗試重設密碼',
        ];
        break;
        
      case 'user-not-found':
        analysis['likely_cause'] = '用戶不存在';
        analysis['suggested_actions'] = [
          '檢查電子郵件是否正確',
          '嘗試註冊新帳戶',
        ];
        break;
        
      case 'wrong-password':
        analysis['likely_cause'] = '密碼錯誤';
        analysis['suggested_actions'] = [
          '檢查密碼是否正確',
          '嘗試重設密碼',
        ];
        break;
        
      case 'user-disabled':
        analysis['likely_cause'] = '帳戶已被停用';
        analysis['suggested_actions'] = [
          '聯繫管理員',
        ];
        break;
        
      case 'too-many-requests':
        analysis['likely_cause'] = '請求過於頻繁';
        analysis['suggested_actions'] = [
          '等待一段時間後重試',
          '檢查是否有自動重試機制',
        ];
        break;
        
      default:
        analysis['likely_cause'] = '未知錯誤';
        analysis['suggested_actions'] = [
          '檢查網路連接',
          '檢查 Firebase 配置',
          '聯繫技術支援',
        ];
    }
    
    return analysis;
  }

  /// 生成電子郵件認證診斷報告
  String _generateEmailAuthReport(Map<String, dynamic> diagnosis) {
    final buffer = StringBuffer();
    
    buffer.writeln('📧 電子郵件認證診斷報告');
    buffer.writeln('=' * 50);
    buffer.writeln();
    
    // 基本信息
    buffer.writeln('📋 基本信息:');
    buffer.writeln('  電子郵件: ${diagnosis['email']}');
    buffer.writeln('  診斷時間: ${diagnosis['timestamp']}');
    buffer.writeln();
    
    // 輸入驗證
    final validation = diagnosis['input_validation'] as Map<String, dynamic>? ?? {};
    buffer.writeln('✅ 輸入驗證:');
    buffer.writeln('  電子郵件格式: ${validation['email_format_valid'] == true ? '✅' : '❌'}');
    buffer.writeln('  密碼長度: ${validation['password_length']} (最少6位)');
    buffer.writeln('  密碼有效: ${validation['password_min_length'] == true ? '✅' : '❌'}');
    buffer.writeln();
    
    // Firebase 連接
    final connection = diagnosis['firebase_connection'] as Map<String, dynamic>? ?? {};
    buffer.writeln('🔥 Firebase 連接:');
    buffer.writeln('  Firebase 已初始化: ${connection['firebase_initialized'] == true ? '✅' : '❌'}');
    buffer.writeln('  專案 ID: ${connection['project_id'] ?? 'Unknown'}');
    buffer.writeln();
    
    // 用戶存在檢查
    final userCheck = diagnosis['user_exists'] as Map<String, dynamic>? ?? {};
    buffer.writeln('👤 用戶檢查:');
    buffer.writeln('  用戶存在: ${userCheck['user_exists'] == true ? '✅' : '❌'}');
    if (userCheck['sign_in_methods'] != null) {
      final methods = userCheck['sign_in_methods'] as List;
      buffer.writeln('  登入方式: ${methods.join(', ')}');
    }
    buffer.writeln();
    
    // 認證嘗試
    final attempt = diagnosis['auth_attempt'] as Map<String, dynamic>? ?? {};
    buffer.writeln('🔐 認證嘗試:');
    buffer.writeln('  認證結果: ${attempt['success'] == true ? '✅ 成功' : '❌ 失敗'}');
    
    if (attempt['success'] != true) {
      buffer.writeln('  錯誤代碼: ${attempt['error_code'] ?? 'Unknown'}');
      buffer.writeln('  錯誤訊息: ${attempt['error_message'] ?? 'Unknown'}');
      
      final analysis = attempt['error_analysis'] as Map<String, dynamic>? ?? {};
      if (analysis['likely_cause'] != null) {
        buffer.writeln('  可能原因: ${analysis['likely_cause']}');
      }
      
      if (analysis['suggested_actions'] != null) {
        buffer.writeln('  建議操作:');
        final actions = analysis['suggested_actions'] as List;
        for (final action in actions) {
          buffer.writeln('    • $action');
        }
      }
    }
    
    return buffer.toString();
  }

  /// 嘗試自動修復電子郵件認證問題
  Future<bool> attemptAutoFix() async {
    logger.i('🔧 嘗試自動修復電子郵件認證問題...');
    
    try {
      // 1. 清除認證狀態
      await FirebaseAuth.instance.signOut();
      logger.i('✅ 已清除認證狀態');
      
      // 2. 清除本地快取
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('firebase_auth_token');
      await prefs.remove('firebase_user_data');
      logger.i('✅ 已清除本地快取');
      
      // 3. 重新載入 Firebase Auth
      await FirebaseAuth.instance.authStateChanges().first;
      logger.i('✅ 已重新載入 Firebase Auth');
      
      return true;
      
    } catch (e) {
      logger.e('❌ 自動修復失敗: $e');
      return false;
    }
  }

  /// 重設密碼
  Future<bool> resetPassword(String email) async {
    try {
      logger.i('🔄 開始重設密碼: $email');
      
      await FirebaseAuth.instance.sendPasswordResetEmail(email: email);
      
      logger.i('✅ 密碼重設郵件已發送');
      return true;
      
    } on FirebaseAuthException catch (e) {
      logger.e('❌ 密碼重設失敗: ${e.code} - ${e.message}');
      return false;
    } catch (e) {
      logger.e('❌ 密碼重設失敗: $e');
      return false;
    }
  }
}
