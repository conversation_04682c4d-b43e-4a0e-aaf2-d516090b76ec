import '../../core/config/app_version.dart';
import '../utils/logger_utils.dart';

/// 版本初始化輔助工具
/// 用於在 Firebase Firestore 中初始化版本信息
class VersionInitHelper {
  
  /// 初始化所有平台的版本信息
  static Future<void> initializeAllPlatforms() async {
    try {
      logger.i('開始初始化所有平台的版本信息...');
      
      // Android 版本信息
      final androidVersion = AppVersion(
        version: '1.0.0',
        buildNumber: 1,
        minRequiredVersion: '1.0.0',
        minRequiredBuildNumber: 1,
        forceUpdate: false,
        updateMessage: '歡迎使用 AstReal 占星應用！這是我們的首個正式版本，提供完整的占星功能。',
        updateUrl: 'https://play.google.com/store/apps/details?id=com.one.astreal',
        releaseDate: DateTime.now(),
        features: [
          '完整的本命盤分析功能',
          '多種星盤類型支持',
          '深入剖析',
          '專業的占星計算引擎',
          '直觀的用戶界面'
        ],
        isActive: true,
      );
      
      // iOS 版本信息
      final iosVersion = AppVersion(
        version: '1.0.0',
        buildNumber: 1,
        minRequiredVersion: '1.0.0',
        minRequiredBuildNumber: 1,
        forceUpdate: false,
        updateMessage: '歡迎使用 AstReal 占星應用！這是我們的首個正式版本，提供完整的占星功能。',
        updateUrl: 'https://apps.apple.com/app/astreal/id123456789',
        releaseDate: DateTime.now(),
        features: [
          '完整的本命盤分析功能',
          '多種星盤類型支持',
          '深入剖析',
          '專業的占星計算引擎',
          '直觀的用戶界面'
        ],
        isActive: true,
      );
      
      // macOS 版本信息
      final macosVersion = AppVersion(
        version: '1.0.0',
        buildNumber: 1,
        minRequiredVersion: '1.0.0',
        minRequiredBuildNumber: 1,
        forceUpdate: false,
        updateMessage: '歡迎使用 AstReal 占星應用！這是我們的首個正式版本，提供完整的占星功能。',
        updateUrl: 'https://astreal.app/download/macos',
        releaseDate: DateTime.now(),
        features: [
          '完整的本命盤分析功能',
          '多種星盤類型支持',
          '深入剖析',
          '專業的占星計算引擎',
          '桌面優化的用戶界面'
        ],
        isActive: true,
      );
      
      // Web 版本信息
      final webVersion = AppVersion(
        version: '1.0.0',
        buildNumber: 1,
        minRequiredVersion: '1.0.0',
        minRequiredBuildNumber: 1,
        forceUpdate: false,
        updateMessage: '歡迎使用 AstReal 占星應用！這是我們的首個正式版本，提供完整的占星功能。',
        updateUrl: 'https://astreal.app',
        releaseDate: DateTime.now(),
        features: [
          '完整的本命盤分析功能',
          '多種星盤類型支持',
          '深入剖析',
          '專業的占星計算引擎',
          '響應式網頁設計'
        ],
        isActive: true,
      );
      
      // 初始化版本檢查服務
      // await VersionCheckService.initialize();
      
      // 更新各平台的版本信息
      // 注意：這些操作需要管理員權限，通常在開發環境中執行
      logger.i('正在初始化版本信息...');
      
      // 這裡只是示例，實際使用時需要根據當前平台來設置
      // 在生產環境中，這些信息應該通過 Firebase Console 或管理後台來設置
      
      logger.i('版本信息初始化完成');
      
    } catch (e) {
      logger.e('初始化版本信息失敗: $e');
      rethrow;
    }
  }
  
  /// 創建強制更新版本（示例）
  static AppVersion createForceUpdateVersion() {
    return AppVersion(
      version: '1.1.0',
      buildNumber: 2,
      minRequiredVersion: '1.1.0',
      minRequiredBuildNumber: 2,
      forceUpdate: true,
      updateMessage: '重要安全更新！此版本修復了關鍵的安全問題，必須立即更新才能繼續使用應用。',
      updateUrl: 'https://play.google.com/store/apps/details?id=com.one.astreal',
      releaseDate: DateTime.now(),
      features: [
        '修復重要安全漏洞',
        '提升應用穩定性',
        '優化性能表現',
        '修復已知問題'
      ],
      isActive: true,
    );
  }
  
  /// 創建可選更新版本（示例）
  static AppVersion createOptionalUpdateVersion() {
    return AppVersion(
      version: '1.0.1',
      buildNumber: 2,
      minRequiredVersion: '1.0.0',
      minRequiredBuildNumber: 1,
      forceUpdate: false,
      updateMessage: '新版本可用！此版本包含了一些改進和新功能，建議您更新以獲得更好的使用體驗。',
      updateUrl: 'https://play.google.com/store/apps/details?id=com.one.astreal',
      releaseDate: DateTime.now(),
      features: [
        '新增星盤分享功能',
        '改進解讀準確性',
        '優化用戶界面',
        '修復小問題',
        '提升應用性能'
      ],
      isActive: true,
    );
  }
  
  /// 獲取平台特定的更新 URL
  static String getPlatformUpdateUrl(String platform) {
    switch (platform.toLowerCase()) {
      case 'android':
        return 'https://play.google.com/store/apps/details?id=com.one.astreal';
      case 'ios':
        return 'https://apps.apple.com/app/astreal/id123456789';
      case 'macos':
        return 'https://astreal.app/download/macos';
      case 'windows':
        return 'https://astreal.app/download/windows';
      case 'web':
        return 'https://astreal.app';
      default:
        return 'https://astreal.app/download';
    }
  }
  
  /// 驗證版本信息格式
  static bool validateVersionInfo(AppVersion version) {
    try {
      // 檢查版本號格式
      final versionParts = version.version.split('.');
      if (versionParts.length != 3) return false;
      
      for (final part in versionParts) {
        if (int.tryParse(part) == null) return false;
      }
      
      // 檢查必要字段
      if (version.buildNumber <= 0) return false;
      if (version.updateMessage.isEmpty) return false;
      if (version.updateUrl.isEmpty) return false;
      
      // 檢查最小版本要求
      final minVersionParts = version.minRequiredVersion.split('.');
      if (minVersionParts.length != 3) return false;
      
      if (version.minRequiredBuildNumber <= 0) return false;
      
      return true;
    } catch (e) {
      logger.e('驗證版本信息時發生錯誤: $e');
      return false;
    }
  }
  
  /// 比較版本號
  static int compareVersions(String version1, String version2) {
    final v1Parts = version1.split('.').map(int.parse).toList();
    final v2Parts = version2.split('.').map(int.parse).toList();
    
    // 確保版本號長度一致
    while (v1Parts.length < 3) v1Parts.add(0);
    while (v2Parts.length < 3) v2Parts.add(0);
    
    for (int i = 0; i < 3; i++) {
      if (v1Parts[i] < v2Parts[i]) return -1;
      if (v1Parts[i] > v2Parts[i]) return 1;
    }
    
    return 0;
  }
}
