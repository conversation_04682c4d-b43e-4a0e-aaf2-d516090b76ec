import 'dart:convert';
import 'dart:io';

import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';

import '../../core/utils/logger_utils.dart';
import '../../firebase_options.dart';

/// Firebase 初始化診斷工具
class FirebaseInitDiagnostic {
  
  /// 執行完整的 Firebase 初始化診斷
  static Future<Map<String, dynamic>> runFullDiagnostic() async {
    final result = <String, dynamic>{
      'timestamp': DateTime.now().toIso8601String(),
      'platform': kIsWeb ? 'web' : Platform.operatingSystem,
    };
    
    logger.i('開始 Firebase 初始化診斷...');
    
    // 1. 檢查 Firebase 初始化狀態
    result['initializationStatus'] = await _checkInitializationStatus();
    
    // 2. 檢查配置文件
    result['configFiles'] = await _checkConfigFiles();
    
    // 3. 檢查依賴項
    result['dependencies'] = _checkDependencies();
    
    // 4. 嘗試手動初始化
    result['manualInitialization'] = await _attemptManualInitialization();
    
    // 5. 生成建議
    result['recommendations'] = _generateRecommendations(result);
    
    logger.i('Firebase 初始化診斷完成');
    return result;
  }
  
  /// 檢查 Firebase 初始化狀態
  static Future<Map<String, dynamic>> _checkInitializationStatus() async {
    final status = <String, dynamic>{};
    
    try {
      // 檢查 Firebase 應用
      final apps = Firebase.apps;
      status['appsCount'] = apps.length;
      status['hasApps'] = apps.isNotEmpty;
      
      if (apps.isNotEmpty) {
        status['appNames'] = apps.map((app) => app.name).toList();
        
        // 檢查默認應用
        final hasDefaultApp = apps.any((app) => app.name == '[DEFAULT]');
        status['hasDefaultApp'] = hasDefaultApp;
        
        if (hasDefaultApp) {
          try {
            final defaultApp = Firebase.app();
            status['defaultApp'] = {
              'name': defaultApp.name,
              'projectId': defaultApp.options.projectId,
              'appId': defaultApp.options.appId,
              'apiKey': '${defaultApp.options.apiKey.substring(0, 10)}...',
            };
            status['status'] = 'initialized';
          } catch (e) {
            status['defaultAppError'] = e.toString();
            status['status'] = 'error';
          }
        } else {
          status['status'] = 'no_default_app';
        }
      } else {
        status['status'] = 'not_initialized';
      }
    } catch (e) {
      status['status'] = 'error';
      status['error'] = e.toString();
      status['errorType'] = e.runtimeType.toString();
    }
    
    return status;
  }
  
  /// 檢查配置文件
  static Future<Map<String, dynamic>> _checkConfigFiles() async {
    final config = <String, dynamic>{};
    
    if (!kIsWeb) {
      if (Platform.isAndroid) {
        config['android'] = await _checkAndroidConfig();
      } else if (Platform.isIOS) {
        config['ios'] = await _checkIOSConfig();
      }
    } else {
      config['web'] = _checkWebConfig();
    }
    
    return config;
  }
  
  /// 檢查 Android 配置
  static Future<Map<String, dynamic>> _checkAndroidConfig() async {
    final android = <String, dynamic>{};
    
    try {
      // 檢查 google-services.json
      final configFile = File('android/app/google-services.json');
      android['googleServicesJson'] = {
        'exists': await configFile.exists(),
        'path': 'android/app/google-services.json',
      };
      
      if (await configFile.exists()) {
        try {
          final content = await configFile.readAsString();
          final json = jsonDecode(content) as Map<String, dynamic>;
          
          android['googleServicesJson']['size'] = content.length;
          android['googleServicesJson']['projectInfo'] = {
            'projectId': json['project_info']?['project_id'],
            'projectNumber': json['project_info']?['project_number'],
            'storageBucket': json['project_info']?['storage_bucket'],
          };
          
          // 檢查客戶端配置
          final clients = json['client'] as List<dynamic>?;
          if (clients != null && clients.isNotEmpty) {
            final client = clients.first as Map<String, dynamic>;
            android['googleServicesJson']['clientInfo'] = {
              'mobilesdkAppId': client['client_info']?['mobilesdk_app_id'],
              'androidClientInfo': client['client_info']?['android_client_info'],
            };
          }
          
          android['googleServicesJson']['valid'] = true;
        } catch (e) {
          android['googleServicesJson']['parseError'] = e.toString();
          android['googleServicesJson']['valid'] = false;
        }
      }
      
      // 檢查 build.gradle 配置
      android['buildGradle'] = await _checkBuildGradleConfig();
      
    } catch (e) {
      android['error'] = e.toString();
    }
    
    return android;
  }
  
  /// 檢查 build.gradle 配置
  static Future<Map<String, dynamic>> _checkBuildGradleConfig() async {
    final gradle = <String, dynamic>{};
    
    try {
      // 檢查項目級 build.gradle
      final projectGradle = File('android/build.gradle.kts');
      if (await projectGradle.exists()) {
        final content = await projectGradle.readAsString();
        gradle['project'] = {
          'exists': true,
          'hasGoogleServicesPlugin': content.contains('google-services'),
          'hasGoogleServicesClasspath': content.contains('com.google.gms:google-services'),
        };
      } else {
        gradle['project'] = {'exists': false};
      }
      
      // 檢查應用級 build.gradle
      final appGradle = File('android/app/build.gradle.kts');
      if (await appGradle.exists()) {
        final content = await appGradle.readAsString();
        gradle['app'] = {
          'exists': true,
          'appliesGoogleServicesPlugin': content.contains('google-services'),
        };
      } else {
        gradle['app'] = {'exists': false};
      }
      
    } catch (e) {
      gradle['error'] = e.toString();
    }
    
    return gradle;
  }
  
  /// 檢查 iOS 配置
  static Future<Map<String, dynamic>> _checkIOSConfig() async {
    final ios = <String, dynamic>{};
    
    try {
      // 檢查 GoogleService-Info.plist
      final configFile = File('ios/Runner/GoogleService-Info.plist');
      ios['googleServiceInfoPlist'] = {
        'exists': await configFile.exists(),
        'path': 'ios/Runner/GoogleService-Info.plist',
      };
      
      if (await configFile.exists()) {
        try {
          final content = await configFile.readAsString();
          ios['googleServiceInfoPlist']['size'] = content.length;
          ios['googleServiceInfoPlist']['hasProjectId'] = content.contains('PROJECT_ID');
          ios['googleServiceInfoPlist']['hasClientId'] = content.contains('CLIENT_ID');
          ios['googleServiceInfoPlist']['hasBundleId'] = content.contains('BUNDLE_ID');
          ios['googleServiceInfoPlist']['valid'] = true;
        } catch (e) {
          ios['googleServiceInfoPlist']['readError'] = e.toString();
          ios['googleServiceInfoPlist']['valid'] = false;
        }
      }
      
    } catch (e) {
      ios['error'] = e.toString();
    }
    
    return ios;
  }
  
  /// 檢查 Web 配置
  static Map<String, dynamic> _checkWebConfig() {
    final web = <String, dynamic>{};
    
    web['note'] = 'Web 配置需要在 index.html 中設定或通過 Firebase.initializeApp() 傳入';
    web['recommendation'] = '檢查 web/index.html 中的 Firebase 配置腳本';
    
    return web;
  }
  
  /// 檢查依賴項
  static Map<String, dynamic> _checkDependencies() {
    final deps = <String, dynamic>{};
    
    try {
      // 檢查 Firebase Core 是否可用
      deps['firebaseCore'] = {
        'available': true,
        'version': 'unknown', // 無法直接獲取版本
      };
      
      // 檢查平台支援
      deps['platformSupport'] = {
        'isWeb': kIsWeb,
        'isAndroid': !kIsWeb && Platform.isAndroid,
        'isIOS': !kIsWeb && Platform.isIOS,
        'isWindows': !kIsWeb && Platform.isWindows,
        'isMacOS': !kIsWeb && Platform.isMacOS,
        'isLinux': !kIsWeb && Platform.isLinux,
      };
      
    } catch (e) {
      deps['error'] = e.toString();
    }
    
    return deps;
  }
  
  /// 嘗試手動初始化
  static Future<Map<String, dynamic>> _attemptManualInitialization() async {
    final attempt = <String, dynamic>{};
    
    try {
      logger.i('嘗試手動初始化 Firebase...');
      
      // 檢查是否已經初始化
      final existingApps = Firebase.apps;
      if (existingApps.isNotEmpty) {
        attempt['result'] = 'already_initialized';
        attempt['existingApps'] = existingApps.map((app) => app.name).toList();
        return attempt;
      }
      
      // 嘗試初始化
      final app = await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform,
      );

      attempt['result'] = 'success';
      attempt['appName'] = app.name;
      attempt['projectId'] = app.options.projectId;
      attempt['appId'] = app.options.appId;
      
      logger.i('手動初始化成功');
      
    } catch (e) {
      attempt['result'] = 'failed';
      attempt['error'] = e.toString();
      attempt['errorType'] = e.runtimeType.toString();
      
      logger.e('手動初始化失敗: $e');
      
      // 分析錯誤類型
      final errorString = e.toString().toLowerCase();
      if (errorString.contains('google-services.json')) {
        attempt['errorCategory'] = 'android_config';
      } else if (errorString.contains('googleservice-info.plist')) {
        attempt['errorCategory'] = 'ios_config';
      } else if (errorString.contains('network')) {
        attempt['errorCategory'] = 'network';
      } else {
        attempt['errorCategory'] = 'unknown';
      }
    }
    
    return attempt;
  }
  
  /// 生成建議
  static List<String> _generateRecommendations(Map<String, dynamic> diagnostic) {
    final recommendations = <String>[];
    
    // 檢查初始化狀態
    final initStatus = diagnostic['initializationStatus'] as Map<String, dynamic>?;
    if (initStatus?['status'] != 'initialized') {
      recommendations.addAll([
        '在 main.dart 中調用 Firebase.initializeApp()',
        '確保在使用 Firebase 服務之前完成初始化',
      ]);
    }
    
    // 檢查配置文件
    final configFiles = diagnostic['configFiles'] as Map<String, dynamic>?;
    if (configFiles != null) {
      if (configFiles['android'] != null) {
        final android = configFiles['android'] as Map<String, dynamic>;
        final googleServices = android['googleServicesJson'] as Map<String, dynamic>?;
        if (googleServices?['exists'] != true) {
          recommendations.add('下載 google-services.json 並放置在 android/app/ 目錄');
        } else if (googleServices?['valid'] != true) {
          recommendations.add('檢查 google-services.json 文件格式是否正確');
        }
        
        final gradle = android['buildGradle'] as Map<String, dynamic>?;
        if (gradle?['project']?['hasGoogleServicesClasspath'] != true) {
          recommendations.add('在 android/build.gradle.kts 中添加 Google Services 插件');
        }
        if (gradle?['app']?['appliesGoogleServicesPlugin'] != true) {
          recommendations.add('在 android/app/build.gradle.kts 中應用 Google Services 插件');
        }
      }
      
      if (configFiles['ios'] != null) {
        final ios = configFiles['ios'] as Map<String, dynamic>;
        final googleServiceInfo = ios['googleServiceInfoPlist'] as Map<String, dynamic>?;
        if (googleServiceInfo?['exists'] != true) {
          recommendations.add('下載 GoogleService-Info.plist 並放置在 ios/Runner/ 目錄');
        } else if (googleServiceInfo?['valid'] != true) {
          recommendations.add('檢查 GoogleService-Info.plist 文件格式是否正確');
        }
      }
    }
    
    // 檢查手動初始化結果
    final manualInit = diagnostic['manualInitialization'] as Map<String, dynamic>?;
    if (manualInit?['result'] == 'failed') {
      final errorCategory = manualInit?['errorCategory'] as String?;
      switch (errorCategory) {
        case 'android_config':
          recommendations.add('重新下載 google-services.json 文件');
          break;
        case 'ios_config':
          recommendations.add('重新下載 GoogleService-Info.plist 文件');
          break;
        case 'network':
          recommendations.addAll([
            '檢查網路連接',
            '檢查防火牆設定',
          ]);
          break;
        default:
          recommendations.add('檢查 Firebase 項目配置');
      }
    }
    
    // 通用建議
    if (recommendations.isEmpty) {
      recommendations.addAll([
        'Firebase 配置看起來正常',
        '如果仍有問題，請重新啟動應用程式',
        '檢查 Firebase 控制台中的項目設定',
      ]);
    }
    
    return recommendations;
  }
  
  /// 打印診斷結果
  static void printDiagnostic(Map<String, dynamic> diagnostic) {
    logger.i('=== Firebase 初始化診斷結果 ===');
    logger.i('時間: ${diagnostic['timestamp']}');
    logger.i('平台: ${diagnostic['platform']}');
    
    // 初始化狀態
    final initStatus = diagnostic['initializationStatus'] as Map<String, dynamic>?;
    if (initStatus != null) {
      logger.i('--- 初始化狀態 ---');
      logger.i('狀態: ${initStatus['status']}');
      logger.i('應用數量: ${initStatus['appsCount']}');
      logger.i('有默認應用: ${initStatus['hasDefaultApp']}');
      if (initStatus['error'] != null) {
        logger.w('錯誤: ${initStatus['error']}');
      }
    }
    
    // 手動初始化結果
    final manualInit = diagnostic['manualInitialization'] as Map<String, dynamic>?;
    if (manualInit != null) {
      logger.i('--- 手動初始化測試 ---');
      logger.i('結果: ${manualInit['result']}');
      if (manualInit['error'] != null) {
        logger.w('錯誤: ${manualInit['error']}');
      }
    }
    
    // 建議
    final recommendations = diagnostic['recommendations'] as List<String>?;
    if (recommendations != null && recommendations.isNotEmpty) {
      logger.i('--- 建議 ---');
      for (int i = 0; i < recommendations.length; i++) {
        logger.i('${i + 1}. ${recommendations[i]}');
      }
    }
    
    logger.i('=== 診斷結束 ===');
  }
}
