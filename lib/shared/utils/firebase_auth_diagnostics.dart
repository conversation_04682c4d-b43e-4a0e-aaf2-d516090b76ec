import 'dart:io';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:package_info_plus/package_info_plus.dart';

import '../../core/utils/logger_utils.dart';

/// Firebase 認證診斷工具
/// 
/// 用於診斷和解決 Firebase 認證相關問題
class FirebaseAuthDiagnostics {
  static final FirebaseAuthDiagnostics _instance = FirebaseAuthDiagnostics._internal();
  factory FirebaseAuthDiagnostics() => _instance;
  FirebaseAuthDiagnostics._internal();

  /// 執行完整的 Firebase 認證診斷
  Future<Map<String, dynamic>> runFullDiagnostics() async {
    logger.i('🔍 開始 Firebase 認證診斷...');
    
    final diagnostics = <String, dynamic>{};
    
    try {
      // 基本環境檢查
      diagnostics['environment'] = await _checkEnvironment();
      
      // Firebase 配置檢查
      diagnostics['firebase_config'] = await _checkFirebaseConfig();
      
      // Google 登入配置檢查
      diagnostics['google_signin_config'] = await _checkGoogleSignInConfig();
      
      // 網路連接檢查
      diagnostics['network'] = await _checkNetworkConnectivity();
      
      // 認證狀態檢查
      diagnostics['auth_state'] = await _checkAuthState();
      
      // 平台特定檢查
      diagnostics['platform_specific'] = await _checkPlatformSpecific();
      
      // 生成診斷報告
      final report = _generateDiagnosticReport(diagnostics);
      logger.i('📋 診斷報告:\n$report');
      
      return diagnostics;
      
    } catch (e) {
      logger.e('診斷過程中發生錯誤: $e');
      diagnostics['error'] = e.toString();
      return diagnostics;
    }
  }

  /// 檢查環境信息
  Future<Map<String, dynamic>> _checkEnvironment() async {
    final env = <String, dynamic>{};
    
    try {
      // 平台信息
      env['platform'] = kIsWeb ? 'web' : Platform.operatingSystem;
      env['is_debug'] = kDebugMode;
      env['is_release'] = kReleaseMode;
      env['is_profile'] = kProfileMode;
      
      // 應用信息
      if (!kIsWeb) {
        final packageInfo = await PackageInfo.fromPlatform();
        env['app_name'] = packageInfo.appName;
        env['package_name'] = packageInfo.packageName;
        env['version'] = packageInfo.version;
        env['build_number'] = packageInfo.buildNumber;
      }
      
      // Flutter 版本（如果可獲取）
      env['flutter_version'] = 'Unknown';
      
      logger.d('環境檢查完成: ${env['platform']}');
      
    } catch (e) {
      logger.e('環境檢查失敗: $e');
      env['error'] = e.toString();
    }
    
    return env;
  }

  /// 檢查 Firebase 配置
  Future<Map<String, dynamic>> _checkFirebaseConfig() async {
    final config = <String, dynamic>{};
    
    try {
      // Firebase 應用狀態
      final apps = Firebase.apps;
      config['firebase_apps_count'] = apps.length;
      config['has_default_app'] = apps.any((app) => app.name == '[DEFAULT]');
      
      if (config['has_default_app']) {
        final defaultApp = Firebase.app();
        config['project_id'] = defaultApp.options.projectId;
        config['app_id'] = defaultApp.options.appId;
        config['api_key'] = defaultApp.options.apiKey.substring(0, 10) + '...';
        config['auth_domain'] = defaultApp.options.authDomain;
      }
      
      // Firebase Auth 狀態
      final auth = FirebaseAuth.instance;
      config['auth_language_code'] = auth.languageCode;
      config['current_user_exists'] = auth.currentUser != null;
      
      if (auth.currentUser != null) {
        final user = auth.currentUser!;
        config['current_user_uid'] = user.uid;
        config['current_user_email'] = user.email;
        config['current_user_verified'] = user.emailVerified;
        config['current_user_anonymous'] = user.isAnonymous;
      }
      
      logger.d('Firebase 配置檢查完成');
      
    } catch (e) {
      logger.e('Firebase 配置檢查失敗: $e');
      config['error'] = e.toString();
    }
    
    return config;
  }

  /// 檢查 Google 登入配置
  Future<Map<String, dynamic>> _checkGoogleSignInConfig() async {
    final config = <String, dynamic>{};
    
    try {
      final googleSignIn = GoogleSignIn();
      
      config['client_id'] = googleSignIn.clientId ?? 'auto';
      config['scopes'] = googleSignIn.scopes;
      
      // 檢查當前登入狀態
      final currentUser = googleSignIn.currentUser;
      config['is_signed_in'] = currentUser != null;
      
      if (currentUser != null) {
        config['google_user_id'] = currentUser.id;
        config['google_user_email'] = currentUser.email;
        config['google_user_name'] = currentUser.displayName;
      }
      
      // 檢查是否可以靜默登入
      try {
        final silentUser = await googleSignIn.signInSilently();
        config['can_sign_in_silently'] = silentUser != null;
      } catch (e) {
        config['can_sign_in_silently'] = false;
        config['silent_sign_in_error'] = e.toString();
      }
      
      logger.d('Google 登入配置檢查完成');
      
    } catch (e) {
      logger.e('Google 登入配置檢查失敗: $e');
      config['error'] = e.toString();
    }
    
    return config;
  }

  /// 檢查網路連接
  Future<Map<String, dynamic>> _checkNetworkConnectivity() async {
    final network = <String, dynamic>{};
    
    try {
      // 檢查基本網路連接
      final result = await InternetAddress.lookup('google.com');
      network['internet_available'] = result.isNotEmpty && result[0].rawAddress.isNotEmpty;
      
      // 檢查 Firebase 服務連接
      try {
        final firebaseResult = await InternetAddress.lookup('firebase.googleapis.com');
        network['firebase_reachable'] = firebaseResult.isNotEmpty;
      } catch (e) {
        network['firebase_reachable'] = false;
        network['firebase_error'] = e.toString();
      }
      
      // 檢查 Google APIs 連接
      try {
        final googleResult = await InternetAddress.lookup('accounts.google.com');
        network['google_apis_reachable'] = googleResult.isNotEmpty;
      } catch (e) {
        network['google_apis_reachable'] = false;
        network['google_apis_error'] = e.toString();
      }
      
      logger.d('網路連接檢查完成');
      
    } catch (e) {
      logger.e('網路連接檢查失敗: $e');
      network['internet_available'] = false;
      network['error'] = e.toString();
    }
    
    return network;
  }

  /// 檢查認證狀態
  Future<Map<String, dynamic>> _checkAuthState() async {
    final authState = <String, dynamic>{};
    
    try {
      final auth = FirebaseAuth.instance;
      
      // 基本認證狀態
      authState['has_current_user'] = auth.currentUser != null;
      authState['auth_state_changes_listening'] = true; // 假設正在監聽
      
      if (auth.currentUser != null) {
        final user = auth.currentUser!;
        
        // 用戶詳細信息
        authState['user_uid'] = user.uid;
        authState['user_email'] = user.email;
        authState['user_display_name'] = user.displayName;
        authState['user_photo_url'] = user.photoURL;
        authState['user_email_verified'] = user.emailVerified;
        authState['user_is_anonymous'] = user.isAnonymous;
        authState['user_creation_time'] = user.metadata.creationTime?.toIso8601String();
        authState['user_last_sign_in_time'] = user.metadata.lastSignInTime?.toIso8601String();
        
        // 提供者信息
        authState['provider_data'] = user.providerData.map((provider) => {
          'provider_id': provider.providerId,
          'uid': provider.uid,
          'email': provider.email,
          'display_name': provider.displayName,
          'photo_url': provider.photoURL,
        }).toList();
        
        // 檢查 ID Token
        try {
          final idTokenResult = await user.getIdTokenResult();
          authState['id_token_valid'] = true;
          authState['id_token_expiration'] = idTokenResult.expirationTime?.toIso8601String();
          authState['id_token_issued_at'] = idTokenResult.issuedAtTime?.toIso8601String();
          authState['id_token_auth_time'] = idTokenResult.authTime?.toIso8601String();
        } catch (e) {
          authState['id_token_valid'] = false;
          authState['id_token_error'] = e.toString();
        }
      }
      
      logger.d('認證狀態檢查完成');
      
    } catch (e) {
      logger.e('認證狀態檢查失敗: $e');
      authState['error'] = e.toString();
    }
    
    return authState;
  }

  /// 檢查平台特定配置
  Future<Map<String, dynamic>> _checkPlatformSpecific() async {
    final platformConfig = <String, dynamic>{};
    
    try {
      if (kIsWeb) {
        // Web 平台檢查
        platformConfig['platform'] = 'web';
        platformConfig['user_agent'] = 'Unknown'; // 需要 dart:html 來獲取

      } else {
        // 移動平台檢查
        try {
          if (Platform.isAndroid) {
            // Android 平台檢查
            platformConfig['platform'] = 'android';
            platformConfig['google_play_services_available'] = 'Unknown'; // 需要額外檢查

          } else if (Platform.isIOS) {
            // iOS 平台檢查
            platformConfig['platform'] = 'ios';

          } else {
            platformConfig['platform'] = Platform.operatingSystem;
          }
        } catch (e) {
          // 如果 Platform 檢查失敗，設為未知
          platformConfig['platform'] = 'unknown';
          platformConfig['platform_error'] = e.toString();
        }
      }
      
      logger.d('平台特定檢查完成: ${platformConfig['platform']}');
      
    } catch (e) {
      logger.e('平台特定檢查失敗: $e');
      platformConfig['error'] = e.toString();
    }
    
    return platformConfig;
  }

  /// 生成診斷報告
  String _generateDiagnosticReport(Map<String, dynamic> diagnostics) {
    final buffer = StringBuffer();
    
    buffer.writeln('🔍 Firebase 認證診斷報告');
    buffer.writeln('=' * 50);
    buffer.writeln();
    
    // 環境信息
    final env = diagnostics['environment'] as Map<String, dynamic>? ?? {};
    buffer.writeln('📱 環境信息:');
    buffer.writeln('  平台: ${env['platform'] ?? 'Unknown'}');
    buffer.writeln('  模式: ${env['is_debug'] == true ? 'Debug' : env['is_release'] == true ? 'Release' : 'Unknown'}');
    if (env['package_name'] != null) {
      buffer.writeln('  應用: ${env['app_name']} (${env['package_name']})');
      buffer.writeln('  版本: ${env['version']} (${env['build_number']})');
    }
    buffer.writeln();
    
    // Firebase 配置
    final firebase = diagnostics['firebase_config'] as Map<String, dynamic>? ?? {};
    buffer.writeln('🔥 Firebase 配置:');
    buffer.writeln('  專案 ID: ${firebase['project_id'] ?? 'Unknown'}');
    buffer.writeln('  應用 ID: ${firebase['app_id'] ?? 'Unknown'}');
    buffer.writeln('  認證域: ${firebase['auth_domain'] ?? 'Unknown'}');
    buffer.writeln('  當前用戶: ${firebase['current_user_exists'] == true ? '已登入' : '未登入'}');
    buffer.writeln();
    
    // 網路狀態
    final network = diagnostics['network'] as Map<String, dynamic>? ?? {};
    buffer.writeln('🌐 網路連接:');
    buffer.writeln('  網際網路: ${network['internet_available'] == true ? '✅' : '❌'}');
    buffer.writeln('  Firebase: ${network['firebase_reachable'] == true ? '✅' : '❌'}');
    buffer.writeln('  Google APIs: ${network['google_apis_reachable'] == true ? '✅' : '❌'}');
    buffer.writeln();
    
    // Google 登入
    final google = diagnostics['google_signin_config'] as Map<String, dynamic>? ?? {};
    buffer.writeln('🔐 Google 登入:');
    buffer.writeln('  客戶端 ID: ${google['client_id'] ?? 'Unknown'}');
    buffer.writeln('  登入狀態: ${google['is_signed_in'] == true ? '已登入' : '未登入'}');
    buffer.writeln('  靜默登入: ${google['can_sign_in_silently'] == true ? '可用' : '不可用'}');
    buffer.writeln();
    
    // 建議
    buffer.writeln('💡 建議:');
    if (network['internet_available'] != true) {
      buffer.writeln('  • 檢查網路連接');
    }
    if (firebase['current_user_exists'] != true) {
      buffer.writeln('  • 嘗試重新登入');
    }
    if (google['can_sign_in_silently'] != true) {
      buffer.writeln('  • 清除 Google 登入快取');
    }
    
    return buffer.toString();
  }

  /// 嘗試修復常見的認證問題
  Future<bool> attemptAutoFix() async {
    logger.i('🔧 嘗試自動修復認證問題...');
    
    try {
      // 清除 Google 登入快取
      final googleSignIn = GoogleSignIn();
      await googleSignIn.signOut();
      logger.i('✅ Google 登入快取已清除');
      
      // 重新載入當前用戶
      final auth = FirebaseAuth.instance;
      if (auth.currentUser != null) {
        await auth.currentUser!.reload();
        logger.i('✅ 用戶信息已重新載入');
      }
      
      return true;
      
    } catch (e) {
      logger.e('❌ 自動修復失敗: $e');
      return false;
    }
  }
}
