import 'dart:io';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';

import '../../core/utils/logger_utils.dart';

/// Firebase 配置檢查工具
class FirebaseConfigChecker {
  
  /// 檢查 Firebase 配置狀態
  static Future<Map<String, dynamic>> checkFirebaseConfig() async {
    final result = <String, dynamic>{
      'timestamp': DateTime.now().toIso8601String(),
      'platform': kIsWeb ? 'web' : Platform.operatingSystem,
    };
    
    logger.i('開始檢查 Firebase 配置...');
    
    // 1. 檢查 Firebase 初始化狀態
    result['initialization'] = await _checkFirebaseInitialization();
    
    // 2. 檢查配置文件
    result['configFiles'] = await _checkConfigFiles();
    
    // 3. 檢查 Firebase Auth
    result['auth'] = await _checkFirebaseAuth();
    
    // 4. 生成建議
    result['recommendations'] = _generateRecommendations(result);
    
    logger.i('Firebase 配置檢查完成');
    return result;
  }
  
  /// 檢查 Firebase 初始化狀態
  static Future<Map<String, dynamic>> _checkFirebaseInitialization() async {
    final init = <String, dynamic>{};
    
    try {
      // 檢查 Firebase 是否已初始化
      final apps = Firebase.apps;
      init['appsCount'] = apps.length;
      init['hasDefaultApp'] = apps.any((app) => app.name == '[DEFAULT]');
      
      if (init['hasDefaultApp'] == true) {
        final defaultApp = Firebase.app();
        init['defaultApp'] = {
          'name': defaultApp.name,
          'projectId': defaultApp.options.projectId,
          'appId': defaultApp.options.appId,
          'apiKey': '${defaultApp.options.apiKey.substring(0, 10)}...',
        };
        init['status'] = 'initialized';
      } else {
        init['status'] = 'not_initialized';
        init['error'] = 'No default Firebase app found';
      }
    } catch (e) {
      init['status'] = 'error';
      init['error'] = e.toString();
    }
    
    return init;
  }
  
  /// 檢查配置文件
  static Future<Map<String, dynamic>> _checkConfigFiles() async {
    final config = <String, dynamic>{};
    
    if (!kIsWeb) {
      if (Platform.isAndroid) {
        // 檢查 Android 配置
        config['android'] = await _checkAndroidConfig();
      } else if (Platform.isIOS) {
        // 檢查 iOS 配置
        config['ios'] = await _checkIOSConfig();
      }
    } else {
      // Web 配置檢查
      config['web'] = _checkWebConfig();
    }
    
    return config;
  }
  
  /// 檢查 Android 配置
  static Future<Map<String, dynamic>> _checkAndroidConfig() async {
    final android = <String, dynamic>{};
    
    try {
      // 檢查 google-services.json 文件
      final configFile = File('android/app/google-services.json');
      android['googleServicesJson'] = {
        'exists': await configFile.exists(),
        'path': 'android/app/google-services.json',
      };
      
      if (await configFile.exists()) {
        try {
          final content = await configFile.readAsString();
          android['googleServicesJson']['size'] = content.length;
          android['googleServicesJson']['hasProjectId'] = content.contains('"project_id"');
          android['googleServicesJson']['hasClientId'] = content.contains('"client_id"');
        } catch (e) {
          android['googleServicesJson']['readError'] = e.toString();
        }
      }
      
      // 檢查 build.gradle 配置
      android['buildGradle'] = await _checkBuildGradleConfig();
      
    } catch (e) {
      android['error'] = e.toString();
    }
    
    return android;
  }
  
  /// 檢查 build.gradle 配置
  static Future<Map<String, dynamic>> _checkBuildGradleConfig() async {
    final gradle = <String, dynamic>{};
    
    try {
      // 檢查項目級 build.gradle
      final projectGradle = File('android/build.gradle.kts');
      if (await projectGradle.exists()) {
        final content = await projectGradle.readAsString();
        gradle['project'] = {
          'exists': true,
          'hasGoogleServicesPlugin': content.contains('google-services'),
        };
      } else {
        gradle['project'] = {'exists': false};
      }
      
      // 檢查應用級 build.gradle
      final appGradle = File('android/app/build.gradle.kts');
      if (await appGradle.exists()) {
        final content = await appGradle.readAsString();
        gradle['app'] = {
          'exists': true,
          'appliesGoogleServicesPlugin': content.contains('google-services'),
        };
      } else {
        gradle['app'] = {'exists': false};
      }
      
    } catch (e) {
      gradle['error'] = e.toString();
    }
    
    return gradle;
  }
  
  /// 檢查 iOS 配置
  static Future<Map<String, dynamic>> _checkIOSConfig() async {
    final ios = <String, dynamic>{};
    
    try {
      // 檢查 GoogleService-Info.plist 文件
      final configFile = File('ios/Runner/GoogleService-Info.plist');
      ios['googleServiceInfoPlist'] = {
        'exists': await configFile.exists(),
        'path': 'ios/Runner/GoogleService-Info.plist',
      };
      
      if (await configFile.exists()) {
        try {
          final content = await configFile.readAsString();
          ios['googleServiceInfoPlist']['size'] = content.length;
          ios['googleServiceInfoPlist']['hasProjectId'] = content.contains('PROJECT_ID');
          ios['googleServiceInfoPlist']['hasClientId'] = content.contains('CLIENT_ID');
        } catch (e) {
          ios['googleServiceInfoPlist']['readError'] = e.toString();
        }
      }
      
    } catch (e) {
      ios['error'] = e.toString();
    }
    
    return ios;
  }
  
  /// 檢查 Web 配置
  static Map<String, dynamic> _checkWebConfig() {
    final web = <String, dynamic>{};
    
    // Web 配置通常在 index.html 或通過 Firebase.initializeApp() 傳入
    web['note'] = 'Web 配置需要在 index.html 中設定或通過代碼傳入';
    
    return web;
  }
  
  /// 檢查 Firebase Auth
  static Future<Map<String, dynamic>> _checkFirebaseAuth() async {
    final auth = <String, dynamic>{};
    
    try {
      // 嘗試訪問 Firebase Auth
      final firebaseAuth = FirebaseAuth.instance;
      auth['accessible'] = true;
      auth['currentUser'] = firebaseAuth.currentUser != null;
      
      if (firebaseAuth.currentUser != null) {
        final user = firebaseAuth.currentUser!;
        auth['userInfo'] = {
          'uid': user.uid,
          'email': user.email,
          'emailVerified': user.emailVerified,
          'isAnonymous': user.isAnonymous,
        };
      }
      
      // 檢查 Auth 設定（移除不存在的 settings 屬性）
      auth['authState'] = 'accessible';
      
    } catch (e) {
      auth['accessible'] = false;
      auth['error'] = e.toString();
    }
    
    return auth;
  }
  
  /// 生成建議
  static List<String> _generateRecommendations(Map<String, dynamic> diagnostic) {
    final recommendations = <String>[];
    
    // 檢查初始化問題
    final init = diagnostic['initialization'] as Map<String, dynamic>?;
    if (init?['status'] != 'initialized') {
      recommendations.addAll([
        '在 main.dart 中調用 Firebase.initializeApp()',
        '確保在使用 Firebase 服務之前完成初始化',
      ]);
    }
    
    // 檢查配置文件問題
    final config = diagnostic['configFiles'] as Map<String, dynamic>?;
    if (config != null) {
      if (config['android'] != null) {
        final android = config['android'] as Map<String, dynamic>;
        final googleServices = android['googleServicesJson'] as Map<String, dynamic>?;
        if (googleServices?['exists'] != true) {
          recommendations.add('下載 google-services.json 並放置在 android/app/ 目錄');
        }
        
        final gradle = android['buildGradle'] as Map<String, dynamic>?;
        if (gradle?['project']?['hasGoogleServicesPlugin'] != true) {
          recommendations.add('在 android/build.gradle.kts 中添加 Google Services 插件');
        }
        if (gradle?['app']?['appliesGoogleServicesPlugin'] != true) {
          recommendations.add('在 android/app/build.gradle.kts 中應用 Google Services 插件');
        }
      }
      
      if (config['ios'] != null) {
        final ios = config['ios'] as Map<String, dynamic>;
        final googleServiceInfo = ios['googleServiceInfoPlist'] as Map<String, dynamic>?;
        if (googleServiceInfo?['exists'] != true) {
          recommendations.add('下載 GoogleService-Info.plist 並放置在 ios/Runner/ 目錄');
        }
      }
    }
    
    // 檢查 Auth 問題
    final auth = diagnostic['auth'] as Map<String, dynamic>?;
    if (auth?['accessible'] != true) {
      recommendations.addAll([
        '檢查 Firebase 項目配置',
        '確認應用已在 Firebase 控制台中註冊',
        '檢查網路連接',
      ]);
    }
    
    // 通用建議
    if (recommendations.isEmpty) {
      recommendations.addAll([
        'Firebase 配置看起來正常',
        '如果仍有問題，請檢查 Firebase 控制台設定',
        '確認應用的 Bundle ID/Package Name 與 Firebase 項目匹配',
      ]);
    }
    
    return recommendations;
  }
  
  /// 打印診斷結果
  static void printDiagnostic(Map<String, dynamic> diagnostic) {
    logger.i('=== Firebase 配置診斷結果 ===');
    logger.i('時間: ${diagnostic['timestamp']}');
    logger.i('平台: ${diagnostic['platform']}');
    
    // 初始化狀態
    final init = diagnostic['initialization'] as Map<String, dynamic>?;
    if (init != null) {
      logger.i('--- Firebase 初始化 ---');
      logger.i('狀態: ${init['status']}');
      logger.i('應用數量: ${init['appsCount']}');
      logger.i('有默認應用: ${init['hasDefaultApp']}');
      if (init['error'] != null) {
        logger.w('錯誤: ${init['error']}');
      }
    }
    
    // Auth 狀態
    final auth = diagnostic['auth'] as Map<String, dynamic>?;
    if (auth != null) {
      logger.i('--- Firebase Auth ---');
      logger.i('可訪問: ${auth['accessible']}');
      logger.i('有當前用戶: ${auth['currentUser']}');
      if (auth['error'] != null) {
        logger.w('錯誤: ${auth['error']}');
      }
    }
    
    // 建議
    final recommendations = diagnostic['recommendations'] as List<String>?;
    if (recommendations != null && recommendations.isNotEmpty) {
      logger.i('--- 建議 ---');
      for (int i = 0; i < recommendations.length; i++) {
        logger.i('${i + 1}. ${recommendations[i]}');
      }
    }
    
    logger.i('=== 診斷結束 ===');
  }
}
