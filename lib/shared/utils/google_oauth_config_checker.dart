import 'dart:convert';
import 'dart:io';

import 'package:flutter/foundation.dart';

import '../../core/utils/logger_utils.dart';

/// Google OAuth 配置檢查工具
class GoogleOAuthConfigChecker {
  
  /// 檢查 Google OAuth 配置
  static Future<Map<String, dynamic>> checkGoogleOAuthConfig() async {
    final result = <String, dynamic>{
      'timestamp': DateTime.now().toIso8601String(),
      'platform': kIsWeb ? 'web' : Platform.operatingSystem,
    };
    
    logger.i('開始檢查 Google OAuth 配置...');
    
    // 1. 檢查 Firebase 配置文件
    result['firebaseConfig'] = await _checkFirebaseConfig();
    
    // 2. 檢查 Google Sign-In 配置
    result['googleSignInConfig'] = _checkGoogleSignInConfig();
    
    // 3. 分析客戶端 ID 匹配
    result['clientIdAnalysis'] = _analyzeClientIds(result);
    
    // 4. 生成建議
    result['recommendations'] = _generateRecommendations(result);
    
    logger.i('Google OAuth 配置檢查完成');
    return result;
  }
  
  /// 檢查 Firebase 配置文件
  static Future<Map<String, dynamic>> _checkFirebaseConfig() async {
    final config = <String, dynamic>{};
    
    if (!kIsWeb) {
      if (Platform.isAndroid) {
        config['android'] = await _checkAndroidFirebaseConfig();
      } else if (Platform.isIOS) {
        config['ios'] = await _checkIOSFirebaseConfig();
      }
    }
    
    return config;
  }
  
  /// 檢查 Android Firebase 配置
  static Future<Map<String, dynamic>> _checkAndroidFirebaseConfig() async {
    final android = <String, dynamic>{};
    
    try {
      final configFile = File('android/app/google-services.json');
      if (await configFile.exists()) {
        final content = await configFile.readAsString();
        final json = jsonDecode(content) as Map<String, dynamic>;
        
        android['exists'] = true;
        android['projectId'] = json['project_info']?['project_id'];
        android['projectNumber'] = json['project_info']?['project_number'];
        
        // 提取客戶端 ID
        final clients = json['client'] as List<dynamic>?;
        if (clients != null && clients.isNotEmpty) {
          final client = clients.first as Map<String, dynamic>;
          final oauthClients = client['oauth_client'] as List<dynamic>?;
          
          if (oauthClients != null) {
            android['oauthClients'] = [];
            for (final oauthClient in oauthClients) {
              final clientMap = oauthClient as Map<String, dynamic>;
              android['oauthClients'].add({
                'clientId': clientMap['client_id'],
                'clientType': clientMap['client_type'],
                'description': _getClientTypeDescription(clientMap['client_type']),
              });
            }
          }
          
          // 提取 API Key
          final apiKeys = client['api_key'] as List<dynamic>?;
          if (apiKeys != null && apiKeys.isNotEmpty) {
            final apiKey = apiKeys.first as Map<String, dynamic>;
            android['apiKey'] = apiKey['current_key'];
          }
        }
      } else {
        android['exists'] = false;
        android['error'] = 'google-services.json 文件不存在';
      }
    } catch (e) {
      android['error'] = e.toString();
    }
    
    return android;
  }
  
  /// 檢查 iOS Firebase 配置
  static Future<Map<String, dynamic>> _checkIOSFirebaseConfig() async {
    final ios = <String, dynamic>{};
    
    try {
      final configFile = File('ios/Runner/GoogleService-Info.plist');
      if (await configFile.exists()) {
        ios['exists'] = true;
        // iOS plist 文件解析較複雜，這裡只檢查存在性
        ios['note'] = 'GoogleService-Info.plist 存在，需要手動檢查客戶端 ID';
      } else {
        ios['exists'] = false;
        ios['error'] = 'GoogleService-Info.plist 文件不存在';
      }
    } catch (e) {
      ios['error'] = e.toString();
    }
    
    return ios;
  }
  
  /// 檢查 Google Sign-In 配置
  static Map<String, dynamic> _checkGoogleSignInConfig() {
    final config = <String, dynamic>{};
    
    // 這裡我們檢查代碼中的配置
    config['serverClientId'] = '470077449550-4kcl1ng61mk48gtqns86f8irlqsiab48.apps.googleusercontent.com';
    config['scopes'] = [
      'email',
      'https://www.googleapis.com/auth/userinfo.profile',
      'openid',
    ];
    config['note'] = '這是代碼中配置的 Google Sign-In 設定';
    
    return config;
  }
  
  /// 分析客戶端 ID 匹配
  static Map<String, dynamic> _analyzeClientIds(Map<String, dynamic> diagnostic) {
    final analysis = <String, dynamic>{};
    
    try {
      final firebaseConfig = diagnostic['firebaseConfig'] as Map<String, dynamic>?;
      final googleSignInConfig = diagnostic['googleSignInConfig'] as Map<String, dynamic>?;
      
      if (firebaseConfig != null && googleSignInConfig != null) {
        final platform = diagnostic['platform'] as String;
        
        if (platform == 'android') {
          final android = firebaseConfig['android'] as Map<String, dynamic>?;
          if (android != null && android['oauthClients'] != null) {
            final oauthClients = android['oauthClients'] as List<dynamic>;
            final serverClientId = googleSignInConfig['serverClientId'] as String?;
            
            analysis['firebaseOAuthClients'] = oauthClients;
            analysis['googleSignInServerClientId'] = serverClientId;
            
            // 檢查匹配
            final webClient = oauthClients.firstWhere(
              (client) => client['clientType'] == 3,
              orElse: () => null,
            );
            
            if (webClient != null) {
              analysis['expectedWebClientId'] = webClient['clientId'];
              analysis['configuredServerClientId'] = serverClientId;
              analysis['isMatching'] = webClient['clientId'] == serverClientId;
              
              if (analysis['isMatching'] == true) {
                analysis['status'] = 'correct';
                analysis['message'] = '客戶端 ID 配置正確';
              } else {
                analysis['status'] = 'mismatch';
                analysis['message'] = 'Google Sign-In 的 serverClientId 與 Firebase 配置不匹配';
              }
            } else {
              analysis['status'] = 'missing_web_client';
              analysis['message'] = 'Firebase 配置中缺少 Web 客戶端 ID';
            }
          }
        }
      }
    } catch (e) {
      analysis['error'] = e.toString();
    }
    
    return analysis;
  }
  
  /// 生成建議
  static List<String> _generateRecommendations(Map<String, dynamic> diagnostic) {
    final recommendations = <String>[];
    
    final analysis = diagnostic['clientIdAnalysis'] as Map<String, dynamic>?;
    
    if (analysis != null) {
      switch (analysis['status']) {
        case 'correct':
          recommendations.add('Google OAuth 配置正確');
          break;
          
        case 'mismatch':
          recommendations.addAll([
            '更新 Google Sign-In 配置中的 serverClientId',
            '使用 Firebase 配置中的 Web 客戶端 ID：${analysis['expectedWebClientId']}',
            '確保 Google Sign-In 和 Firebase 使用相同的客戶端 ID',
          ]);
          break;
          
        case 'missing_web_client':
          recommendations.addAll([
            '在 Firebase 控制台中添加 Web 客戶端 ID',
            '重新下載 google-services.json 文件',
            '確保 Firebase 項目中已啟用 Google 登入提供者',
          ]);
          break;
          
        default:
          recommendations.addAll([
            '檢查 Firebase 配置文件是否存在',
            '確認 Google Sign-In 配置正確',
            '檢查網路連接',
          ]);
      }
    }
    
    // 通用建議
    recommendations.addAll([
      '確保 Firebase 項目中已啟用 Google 認證提供者',
      '檢查應用的 SHA-1 指紋是否已添加到 Firebase',
      '確認應用的 Package Name 與 Firebase 配置匹配',
    ]);
    
    return recommendations;
  }
  
  /// 獲取客戶端類型描述
  static String _getClientTypeDescription(dynamic clientType) {
    switch (clientType) {
      case 1:
        return 'Android 客戶端';
      case 2:
        return 'iOS 客戶端';
      case 3:
        return 'Web 客戶端';
      default:
        return '未知類型 ($clientType)';
    }
  }
  
  /// 打印診斷結果
  static void printDiagnostic(Map<String, dynamic> diagnostic) {
    logger.i('=== Google OAuth 配置診斷結果 ===');
    logger.i('時間: ${diagnostic['timestamp']}');
    logger.i('平台: ${diagnostic['platform']}');
    
    // Firebase 配置
    final firebaseConfig = diagnostic['firebaseConfig'] as Map<String, dynamic>?;
    if (firebaseConfig != null) {
      logger.i('--- Firebase 配置 ---');
      
      if (firebaseConfig['android'] != null) {
        final android = firebaseConfig['android'] as Map<String, dynamic>;
        logger.i('Android 配置存在: ${android['exists']}');
        if (android['projectId'] != null) {
          logger.i('項目 ID: ${android['projectId']}');
        }
        if (android['oauthClients'] != null) {
          final clients = android['oauthClients'] as List<dynamic>;
          logger.i('OAuth 客戶端數量: ${clients.length}');
          for (final client in clients) {
            logger.i('- ${client['description']}: ${client['clientId']}');
          }
        }
      }
    }
    
    // 客戶端 ID 分析
    final analysis = diagnostic['clientIdAnalysis'] as Map<String, dynamic>?;
    if (analysis != null) {
      logger.i('--- 客戶端 ID 分析 ---');
      logger.i('狀態: ${analysis['status']}');
      logger.i('訊息: ${analysis['message']}');
      
      if (analysis['expectedWebClientId'] != null) {
        logger.i('期望的 Web 客戶端 ID: ${analysis['expectedWebClientId']}');
      }
      if (analysis['configuredServerClientId'] != null) {
        logger.i('配置的 Server 客戶端 ID: ${analysis['configuredServerClientId']}');
      }
    }
    
    // 建議
    final recommendations = diagnostic['recommendations'] as List<String>?;
    if (recommendations != null && recommendations.isNotEmpty) {
      logger.i('--- 建議 ---');
      for (int i = 0; i < recommendations.length; i++) {
        logger.i('${i + 1}. ${recommendations[i]}');
      }
    }
    
    logger.i('=== 診斷結束 ===');
  }
}
