/// 星盤資訊格式化工具
///
/// 提供統一的格式化功能，包括日期時間、角度、文本格式等
class ChartInfoFormatter {
  /// 格式化日期時間為指定格式
  static String formatDateTime(DateTime dateTime) {
    return '${dateTime.year}/${dateTime.month}/${dateTime.day} '
        '${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  /// 格式化角度為度分格式
  static String formatDegree(double degree) {
    final int deg = degree.floor();
    final double minDouble = (degree - deg) * 60;
    final int min = minDouble.floor();

    // 確保分是兩位數字
    final String minStr = min.toString().padLeft(2, '0');

    return '$deg°$minStr\'';
  }

  /// 生成區段標題
  static String sectionTitle(String title) {
    return '\n【$title】';
  }

  /// 生成子區段標題
  static String subSectionTitle(String title) {
    return '$title:';
  }

  /// 格式化行星列表為文本
  static String formatPlanetList(List<dynamic> planets) {
    if (planets.isEmpty) return '無';
    return planets.map((planet) => '${planet.name}').join(', ');
  }

  /// 格式化宮位文本
  static String formatHouseText(int house) {
    return '第$house宮';
  }

  /// 格式化宮位列表文本
  static String formatHousesList(List<int> houses) {
    return houses.map((h) => formatHouseText(h)).join('、');
  }

  /// 格式化相位方向文本
  static String formatAspectDirection(String? direction) {
    return direction != null ? ' $direction' : '';
  }

  /// 格式化容許度文本
  static String formatOrb(double orb) {
    return orb.toStringAsFixed(2);
  }

  /// 格式化逆行狀態
  static String formatRetrograde(bool isRetrograde) {
    return isRetrograde ? ' (逆行)' : '';
  }

  /// 格式化尊貴力量
  static String formatDignity(String dignityText, bool hasDignity) {
    return hasDignity ? ' ($dignityText)' : '';
  }

  /// 格式化宮主星信息
  static String formatHouseRulerInfo(List<int> housesRuled) {
    if (housesRuled.isEmpty) return '';
    return ' ${formatHousesList(housesRuled)}主星';
  }

  /// 格式化日夜區分信息
  static String formatSectStatus(String sectStatusText, bool shouldShow) {
    return shouldShow ? ' $sectStatusText' : '';
  }

  /// 格式化統計數據
  static String formatStatValue(String label, int value) {
    return '  $label：$value';
  }

  /// 格式化元素行星詳細信息
  static String formatElementPlanets(String element, List<dynamic> planets) {
    return '$element元素：${formatPlanetList(planets)}';
  }

  /// 格式化特殊點信息
  static String formatArabicPoint(String name, String sign, double signDegree, String houseText) {
    return '$name：$sign ${formatDegree(signDegree)}, $houseText';
  }

  /// 格式化相位信息
  static String formatAspectInfo(String planet1, String aspectName, String planet2, double orb, String? direction) {
    final directionText = formatAspectDirection(direction);
    return '$planet1 $aspectName $planet2 (容許度：${formatOrb(orb)}°$directionText)';
  }

  static String formatAspectInfoDual(String primaryPerson, String secondaryPerson, String planet1, String aspectName, String planet2, double orb, String? direction) {
    final directionText = formatAspectDirection(direction);
    return '$secondaryPerson的$planet1 $aspectName $primaryPerson的$planet2 (容許度：${formatOrb(orb)}°$directionText)';
  }

  /// 格式化互容接納信息
  static String formatReceptionInfo(String planet1, String receptionType, String planet2) {
    return '$planet1 $receptionType $planet2';
  }

  /// 格式化宮位詳細信息
  static String formatHouseDetail(int houseNumber, String sign, double signDegree, String ruler, String rulerHouse) {
    return '第$houseNumber宮：$sign ${formatDegree(signDegree)} 宮主星：$ruler 飛入 $rulerHouse';
  }

  /// 格式化行星基本位置信息
  static String formatPlanetPosition(String planetName, String sign, double signDegree, String houseText) {
    return '$planetName：$sign ${formatDegree(signDegree)}, $houseText';
  }

  static String formatPlanetPositionDual(String secondaryPerson, String planetName, String sign, double signDegree, String houseText) {
    return '$secondaryPerson$planetName：$sign ${formatDegree(signDegree)}, $houseText';
  }
}
