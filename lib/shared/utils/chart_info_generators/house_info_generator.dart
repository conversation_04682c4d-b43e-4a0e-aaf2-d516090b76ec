import '../../../astreal.dart' hide logger;
import '../logger_utils.dart';
import 'chart_info_calculator.dart';
import 'chart_info_formatter.dart';

/// 宮位信息生成器
///
/// 負責生成宮位位置、宮主星等信息
class HouseInfoGenerator {
  /// 生成宮位位置信息
  static Future<String> generateHousePositions(
      ChartData chartData, CopyOptions options) async {
    if (!options.includeHousePositions || chartData.houses == null) return '';

    // 如果出生時間不確定，則不顯示宮位信息
    if (chartData.primaryPerson.isTimeUncertain) {
      return '';
    }
    if (chartData.secondaryPerson != null &&
        chartData.secondaryPerson!.isTimeUncertain) {
      return '';
    }

    final StringBuffer text = StringBuffer();
    final houseSystem = await _getCurrentHouseSystem();

    text.writeln(ChartInfoFormatter.sectionTitle('宮位'));
    text.writeln('宮位制：${houseSystem.displayName} ${houseSystem.englishName}');

    for (int i = 1; i <= 12; i++) {
      final houseAngle = chartData.houses!.cusps[i];
      final sign = ChartInfoCalculator.getZodiacSign(houseAngle);
      final houseRuler = ChartInfoCalculator.getHouseRuler(houseAngle);
      final signDegree = houseAngle % 30;

      // 找到宮主星在哪個宮位
      String rulerHouse = '未知';
      if (chartData.planets != null) {
        try {
          final planet =
              chartData.planets!.firstWhere((p) => p.name == houseRuler);
          rulerHouse = planet.getHouseText();
        } catch (e) {
          // 如果找不到對應的行星，保持預設值
        }
      }

      text.writeln(ChartInfoFormatter.formatHouseDetail(
        i,
        sign,
        signDegree,
        houseRuler,
        rulerHouse,
      ));
    }

    return text.toString();
  }

  /// 獲取當前宮位制設定
  static Future<HouseSystem> _getCurrentHouseSystem() async {
    try {
      final settings = await ChartSettings.loadFromPrefs();
      return settings.houseSystem;
    } catch (e) {
      logger.e('获取宫位制设置失败：$e');
      return HouseSystem.placidus; // 默认使用 Placidus 系统
    }
  }
}
