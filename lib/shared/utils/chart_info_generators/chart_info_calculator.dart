import '../../../core/constants/astrology_constants.dart';
import '../../../data/models/astrology/chart_data.dart';
import '../../../data/models/astrology/planet_position.dart';

/// 星盤資訊計算工具
///
/// 提供各種占星計算功能，如日夜區分、宮主星、元素統計等
class ChartInfoCalculator {
  /// 判斷是否為白天出生
  /// 
  /// 白天出生：太陽在地平線上方（第7宮到第12宮之間）
  /// 夜晚出生：太陽在地平線下方（第1宮到第6宮之間）
  static bool? calculateDaytimeStatus(List<PlanetPosition>? planets) {
    if (planets == null) return null;

    final sun = planets.firstWhere(
      (planet) => planet.id == AstrologyConstants.SUN,
      orElse: () => PlanetPosition(
        id: -1,
        name: '',
        symbol: '',
        longitude: 0,
        latitude: 0,
        distance: 0,
        longitudeSpeed: 0,
        latitudeSpeed: 0,
        distanceSpeed: 0,
        sign: '',
        house: 0,
        dignity: PlanetDignity.peregrine,
        solarCondition: SolarCondition.free,
        isDaytime: false,
        houseType: HouseType.angular,
        isPlanetDiurnal: false,
        isSignMasculine: false,
        sectStatus: SectStatus.outOfSect,
      ),
    );

    if (sun.id == -1) return null;
    return sun.house > 6;
  }

  /// 計算宮主星對應關係
  /// 
  /// 返回 Map<行星ID, 所主宰的宮位列表>
  static Map<int, List<int>> calculatePlanetToHousesRuled(ChartData chartData) {
    final Map<int, List<int>> planetToHousesRuled = {};
    
    if (chartData.houses == null) return planetToHousesRuled;

    // 象限制宮主星
    for (int i = 1; i <= 12; i++) {
      final cuspLongitude = chartData.houses!.cusps[i];
      final signIndex = (cuspLongitude / 30).floor() % 12;
      final planetId = _getPlanetIdBySignIndex(signIndex);

      if (planetId != -1) {
        if (!planetToHousesRuled.containsKey(planetId)) {
          planetToHousesRuled[planetId] = [];
        }
        planetToHousesRuled[planetId]!.add(i);
      }
    }

    return planetToHousesRuled;
  }

  /// 根據宮頭經度獲取宮主星名稱
  static String getHouseRuler(double cuspLongitude) {
    // 獲取宮頭所在星座
    final signIndex = (cuspLongitude / 30).floor() % 12;

    // 根據星座確定宮主
    switch (signIndex) {
      case 0: return '火星'; // 牡羊座
      case 1: return '金星'; // 金牛座
      case 2: return '水星'; // 雙子座
      case 3: return '月亮'; // 巨蟹座
      case 4: return '太陽'; // 獅子座
      case 5: return '水星'; // 處女座
      case 6: return '金星'; // 天秤座
      case 7: return '火星'; // 天蠍座
      case 8: return '木星'; // 射手座
      case 9: return '土星'; // 摩羯座
      case 10: return '土星'; // 水瓶座
      case 11: return '木星'; // 雙魚座
      default: return '未知';
    }
  }

  /// 根據經度獲取星座
  static String getZodiacSign(double longitude) {
    final int signIndex = (longitude / 30).floor() % 12;
    return AstrologyConstants.ZODIAC_SIGNS[signIndex];
  }

  /// 計算元素分佈（火土風水）
  static Map<String, int> calculateElementStats(List<PlanetPosition> planets) {
    final Map<String, int> stats = {
      '火': 0,
      '土': 0,
      '風': 0,
      '水': 0,
    };

    for (final planet in planets) {
      // 只計算主要行星（太陽到冥王星）
      if (planet.id >= 0 && planet.id <= 9) {
        // 使用 AstrologyConstants 中的常量來確定元素
        final element = AstrologyConstants.SIGN_ELEMENTS[planet.sign];
        if (element != null) {
          stats[element] = (stats[element] ?? 0) + 1;
        }
      }
    }

    return stats;
  }

  /// 計算品質分佈（啟動、固定、變動）
  static Map<String, int> calculateModalityStats(List<PlanetPosition> planets) {
    final Map<String, int> stats = {
      '啟動': 0,
      '固定': 0,
      '變動': 0,
    };

    for (final planet in planets) {
      // 只計算主要行星（太陽到冥王星）
      if (planet.id >= 0 && planet.id <= 9) {
        // 使用 AstrologyConstants 中的常量來確定品質
        final modality = AstrologyConstants.SIGN_MODALITIES[planet.sign];
        if (modality != null) {
          // 將英文品質轉換為中文
          String modalityZh = '';
          switch (modality) {
            case 'cardinal': modalityZh = '啟動'; break;
            case 'fixed': modalityZh = '固定'; break;
            case 'mutable': modalityZh = '變動'; break;
          }
          if (modalityZh.isNotEmpty) {
            stats[modalityZh] = (stats[modalityZh] ?? 0) + 1;
          }
        }
      }
    }

    return stats;
  }

  /// 計算陰陽分佈
  static Map<String, int> calculatePolarityStats(List<PlanetPosition> planets) {
    final Map<String, int> stats = {
      '陽性': 0,
      '陰性': 0,
    };

    for (final planet in planets) {
      // 只計算主要行星（太陽到冥王星）
      if (planet.id >= 0 && planet.id <= 9) {
        // 使用 AstrologyConstants 中的常量來確定陰陽性
        final isMasculine = AstrologyConstants.SIGN_POLARITY[planet.sign] ?? false;
        final polarityKey = isMasculine ? '陽性' : '陰性';
        stats[polarityKey] = (stats[polarityKey] ?? 0) + 1;
      }
    }

    return stats;
  }

  /// 按元素分組行星
  static Map<String, List<PlanetPosition>> getPlanetsByElement(List<PlanetPosition> planets) {
    final Map<String, List<PlanetPosition>> elementPlanets = {
      '火': [],
      '土': [],
      '風': [],
      '水': [],
    };

    // 只考慮主要行星（太陽到冥王星）
    for (final planet in planets) {
      if (planet.id >= 0 && planet.id <= 9) {
        // 使用 AstrologyConstants 中的常量來確定元素
        final element = AstrologyConstants.SIGN_ELEMENTS[planet.sign];
        if (element != null) {
          elementPlanets[element]!.add(planet);
        }
      }
    }

    return elementPlanets;
  }

  /// 根據星座索引獲取行星 ID
  static int _getPlanetIdBySignIndex(int signIndex) {
    // 根據星座確定行星 ID
    switch (signIndex) {
      case 0: return AstrologyConstants.MARS; // 牡羊座 - 火星
      case 1: return AstrologyConstants.VENUS; // 金牛座 - 金星
      case 2: return AstrologyConstants.MERCURY; // 雙子座 - 水星
      case 3: return AstrologyConstants.MOON; // 巨蟹座 - 月亮
      case 4: return AstrologyConstants.SUN; // 獅子座 - 太陽
      case 5: return AstrologyConstants.MERCURY; // 處女座 - 水星
      case 6: return AstrologyConstants.VENUS; // 天秤座 - 金星
      case 7: return AstrologyConstants.MARS; // 天蠍座 - 火星
      case 8: return AstrologyConstants.JUPITER; // 射手座 - 木星
      case 9: return AstrologyConstants.SATURN; // 摩羯座 - 土星
      case 10: return AstrologyConstants.SATURN; // 水瓶座 - 土星
      case 11: return AstrologyConstants.JUPITER; // 雙魚座 - 木星
      default: return -1;
    }
  }
}
