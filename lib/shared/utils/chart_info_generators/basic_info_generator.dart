import '../../../astreal.dart';
import 'chart_info_formatter.dart';

/// 基本資料生成器
///
/// 負責生成星盤的基本資料信息，包括姓名、出生日期、地點等
class BasicInfoGenerator {
  /// 生成基本資料信息
  static String generateBasicInfo(ChartData chartData, CopyOptions options) {
    if (!options.includeBasicInfo) return '';

    final StringBuffer text = StringBuffer();
    final chartType = chartData.chartType;
    final primaryPerson = chartData.primaryPerson;
    final secondaryPerson = chartData.secondaryPerson;

    text.writeln(ChartInfoFormatter.sectionTitle('基本資料'));
    
    // 主要人物資料
    _addPrimaryPersonInfo(text, primaryPerson);
    
    // 特定日期信息（用於推運盤等）
    _addSpecificDateInfo(text, chartData, chartType);
    
    // 天象日期信息（用於 transit 星盤）
    _addTransitDateInfo(text, chartData, chartType);
    
    // 第二人物資料（用於合盤）
    _addSecondaryPersonInfo(text, chartData, chartType, secondaryPerson);

    return text.toString();
  }

  /// 添加主要人物資料
  static void _addPrimaryPersonInfo(StringBuffer text, dynamic primaryPerson) {
    text.writeln('姓名：${primaryPerson.name}');
    text.writeln('出生日期：${ChartInfoFormatter.formatDateTime(primaryPerson.dateTime)}');
    text.writeln('出生地點：${primaryPerson.birthPlace}');
  }

  /// 添加特定日期信息
  static void _addSpecificDateInfo(StringBuffer text, ChartData chartData, ChartType chartType) {
    // 如果星盤類型需要特定日期，則顯示日期資訊
    if (chartType != ChartType.transit &&
        chartType.requiresSpecificDate &&
        chartData.specificDate != null) {
      text.writeln('推運日期：${ChartInfoFormatter.formatDateTime(chartData.specificDate!)}');
    }
  }

  /// 添加天象日期信息
  static void _addTransitDateInfo(StringBuffer text, ChartData chartData, ChartType chartType) {
    // 如果是 transit 星盤，顯示 transit 日期
    if (chartType == ChartType.transit && chartData.specificDate != null) {
      text.writeln('天象日期：${ChartInfoFormatter.formatDateTime(chartData.specificDate!)}');
    }
  }

  /// 添加第二人物資料
  static void _addSecondaryPersonInfo(
    StringBuffer text,
    ChartData chartData,
    ChartType chartType,
    dynamic secondaryPerson,
  ) {
    // 如果是合盤，顯示第二個人的資訊
    if (chartType.requiresTwoPersons && secondaryPerson != null) {
      text.writeln(ChartInfoFormatter.subSectionTitle('次要人物資料'));
      text.writeln('姓名：${secondaryPerson.name}');
      text.writeln('出生日期：${ChartInfoFormatter.formatDateTime(secondaryPerson.dateTime)}');
      text.writeln('出生地點：${secondaryPerson.birthPlace}');
    }
  }
}
