import '../../../astreal.dart';
import 'chart_info_formatter.dart';

/// 相位信息生成器
///
/// 負責生成相位關係和互容接納信息
class AspectInfoGenerator {
  /// 生成相位信息
  static String generateAspects(ChartData chartData, CopyOptions options) {
    if (!options.includeAspects ||
        chartData.aspects == null ||
        chartData.aspects!.isEmpty) {
      return '';
    }

    final StringBuffer text = StringBuffer();
    final normalAspects = chartData.aspects;

    if (normalAspects!.isNotEmpty) {
      text.writeln(ChartInfoFormatter.sectionTitle('相位'));
      final seenPairs = <String>{};
      final aspects = normalAspects.where((aspect) {
        final planetA = aspect.planet1.name;
        final planetB = aspect.planet2.name;
        final sortedPair = [planetA, planetB]..sort();
        final key =
            '${sortedPair.join("-")}-${aspect.aspect}-${aspect.angle}-${aspect.receptionType}';
        return seenPairs.add(key); // 只加入第一次出現的組合
      }).toList();

      switch (chartData.chartType) {
        case ChartType.synastry:
          text.writeln(
              '${chartData.primaryPerson.name}的行星 vs ${chartData.secondaryPerson!.name}的行星');
          break;
        case ChartType.transit:
        case ChartType.solarArcDirection:
          text.writeln(
              '${chartData.primaryPerson.name}的行星 vs ${chartData.chartType.displayName}的行星');
          break;
        default:
          break;
      }

      for (final aspect in aspects) {
        // 添加入相或出相的信息
        String directionText =
            aspect.direction != null ? aspect.getDirectionText() : '';

        switch (chartData.chartType) {
          case ChartType.synastry:
            text.writeln(ChartInfoFormatter.formatAspectInfoDual(
              chartData.secondaryPerson!.name,
              chartData.primaryPerson.name,
              aspect.planet1.name,
              aspect.shortZh,
              aspect.planet2.name,
              aspect.orb,
              directionText.isNotEmpty ? directionText : null,
            ));
            break;
          case ChartType.transit:
          case ChartType.solarArcDirection:
            text.writeln(ChartInfoFormatter.formatAspectInfoDual(
              chartData.chartType.displayName,
              chartData.primaryPerson.name,
              aspect.planet1.name,
              aspect.shortZh,
              aspect.planet2.name,
              aspect.orb,
              directionText.isNotEmpty ? directionText : null,
            ));
            break;
          default:
            text.writeln(ChartInfoFormatter.formatAspectInfo(
              aspect.planet1.name,
              aspect.shortZh,
              aspect.planet2.name,
              aspect.orb,
              directionText.isNotEmpty ? directionText : null,
            ));
            break;
        }
      }
    }

    // 添加互容接納關係
    if (options.includeReceptions) {
      final receptionsText = _generateReceptions(chartData.aspects!);
      if (receptionsText.isNotEmpty) {
        text.write(receptionsText);
      }
    }

    return text.toString();
  }

  /// 生成互容接納信息
  static String _generateReceptions(List<AspectInfo> aspects) {
    final StringBuffer text = StringBuffer();
    final seenPairs = <String>{};

    final receptions = aspects
        .where((aspect) => aspect.receptionType != ReceptionType.none)
        .where((aspect) {
      final planetA = aspect.planet1.name;
      final planetB = aspect.planet2.name;
      final sortedPair = [planetA, planetB]..sort();
      final key =
          '${sortedPair.join("-")}-${aspect.aspect}-${aspect.angle}-${aspect.receptionType}';
      return seenPairs.add(key); // 只加入第一次出現的組合
    }).toList();

    if (receptions.isNotEmpty) {
      text.writeln(ChartInfoFormatter.sectionTitle('互容接納'));
      for (final reception in receptions) {
        final receptionTypeText = getReceptionText(reception.receptionType);

        if (receptionTypeText.isNotEmpty) {
          text.writeln(ChartInfoFormatter.formatReceptionInfo(
            reception.planet1.name,
            receptionTypeText,
            reception.planet2.name,
          ));
        }
      }
    }

    return text.toString();
  }

  /// 獲取互容接納的文字描述
  static String getReceptionText(ReceptionType receptionType) {
    switch (receptionType) {
      case ReceptionType.reception:
        return '互容';
      case ReceptionType.mutualReception:
        return '互相互容';
      case ReceptionType.acceptance:
        return '接納';
      case ReceptionType.mutualAcceptance:
        return '互容接納';
      case ReceptionType.none:
        return '';
    }
  }
}
