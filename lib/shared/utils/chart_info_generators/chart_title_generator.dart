import '../../../astreal.dart';
import '../../../data/services/api/firdaria_service.dart';
import 'chart_info_calculator.dart';
import 'chart_info_formatter.dart';

/// 星盤標題與類型信息生成器
///
/// 負責生成星盤類型、宮位制、日夜區分等標題信息
class ChartTitleGenerator {
  /// 生成星盤標題信息
  static Future<String> generateTitleInfo(
      ChartData chartData, CopyOptions options) async {
    final StringBuffer text = StringBuffer();
    final chartType = chartData.chartType;
    final primaryPerson = chartData.primaryPerson;
    final secondaryPerson = chartData.secondaryPerson;

    // 添加星盤類型標題
    text.writeln(ChartInfoFormatter.sectionTitle('星盤類型'));
    if (chartData.primaryPerson.category == ChartCategory.event) {
      text.writeln('${chartType.name} - 事件');
      text.writeln('${chartData.primaryPerson.name} 請針對此事件分析');
    } else {
      text.writeln('${chartType.name} - ${chartType.nameEn}');
      text.writeln(chartType.description);
    }

    // 根據不同星盤類型添加特定信息
    await _addChartTypeSpecificInfo(
        text, chartType, primaryPerson, secondaryPerson, chartData);

    // 添加宮位制信息
    HouseSystem houseSystem = await _getCurrentHouseSystem();

    text.writeln(ChartInfoFormatter.sectionTitle('宮位系統'));
    text.writeln("${houseSystem.displayName} ${houseSystem.englishName}");
    // 添加日夜區分信息
    _addDaytimeInfo(text, chartData);

    return text.toString();
  }

  /// 根據星盤類型添加特定信息
  static Future<void> _addChartTypeSpecificInfo(
    StringBuffer text,
    ChartType chartType,
    dynamic primaryPerson,
    dynamic secondaryPerson,
    ChartData chartData,
  ) async {
    switch (chartType) {
      case ChartType.synastry:
      case ChartType.synastrySecondary:
      case ChartType.synastryTertiary:
      case ChartType.davisonSecondary:
      case ChartType.davisonTertiary:
      case ChartType.davison:
      case ChartType.compositeTertiary:
      case ChartType.compositeSecondary:
      case ChartType.composite:
      case ChartType.marksSecondary:
      case ChartType.marksTertiary:
      case ChartType.marks:
        text.writeln('${primaryPerson.name} VS ${secondaryPerson!.name}');
        break;
      case ChartType.equinoxSolstice:
        text.writeln(chartData.primaryPerson.name);
        break;
      case ChartType.horary:
        text.writeln(ChartInfoFormatter.sectionTitle('卜卦問題'));
        text.writeln(chartData.primaryPerson.notes);
        break;
      case ChartType.firdaria:
        await _addFirdariaInfo(text, chartData);
        break;
      default:
        break;
    }
  }

  /// 添加 Firdaria 特定信息
  static Future<void> _addFirdariaInfo(StringBuffer text, ChartData chartData) async {
    chartData.firdariaData ??= await FirdariaService().calculateFirdaria(
        chartData.primaryPerson,
        currentDate: chartData.specificDate,
      );

    final currentPeriod = chartData.firdariaData!.firstWhere(
      (period) => period.isCurrent,
      orElse: () => chartData.firdariaData!.first,
    );
    final currentSubPeriod = currentPeriod.subPeriods.firstWhere(
      (subPeriod) => subPeriod.isCurrent,
      orElse: () => currentPeriod.subPeriods.first,
    );

    text.writeln(
        "主星：${currentPeriod.majorPlanetName}\n次星：${currentSubPeriod.subPlanetName}");
    text.writeln("需要分析主星次星有關的相位關係與宮位 有相位的行星是在什麼宮位有什麼其他的相位關係");
  }

  /// 添加日夜區分信息
  static void _addDaytimeInfo(StringBuffer text, ChartData chartData) {
    final isDaytime =
        ChartInfoCalculator.calculateDaytimeStatus(chartData.planets);

    if (isDaytime != null) {
      text.writeln(ChartInfoFormatter.sectionTitle('日夜區分'));
      text.writeln(isDaytime ? '日生盤' : '夜生盤');
    }
  }

  /// 獲取當前宮位制設定
  static Future<HouseSystem> _getCurrentHouseSystem() async {
    try {
      ChartSettings settings = await ChartSettings.loadFromPrefs();
      return settings.houseSystem;
    } catch (e) {
      logger.e('获取宫位制设置失败：$e');
      return HouseSystem.placidus; // 默认使用 Placidus 系统
    }
  }
}
