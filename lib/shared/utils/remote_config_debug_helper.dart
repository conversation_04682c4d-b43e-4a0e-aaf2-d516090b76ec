import 'dart:convert';
import 'dart:io';

import 'package:flutter/foundation.dart';

import '../../core/utils/logger_utils.dart';
import '../../data/services/api/remote_config_service.dart';

/// Remote Config 調試助手
class RemoteConfigDebugHelper {
  
  /// 詳細調試 OpenAI Key 獲取過程
  static Map<String, dynamic> debugOpenAIKeyRetrieval() {
    final debug = <String, dynamic>{
      'timestamp': DateTime.now().toIso8601String(),
    };
    
    logger.i('=== 開始調試 OpenAI Key 獲取過程 ===');
    
    // 1. 檢查平台
    final platform = _getCurrentPlatform();
    debug['platform'] = platform;
    logger.i('當前平台: $platform');
    
    // 2. 檢查 Remote Config 狀態
    final status = RemoteConfigService.getStatus();
    debug['remoteConfigStatus'] = status;
    logger.i('Remote Config 初始化狀態: ${status['isInitialized']}');
    logger.i('最後獲取時間: ${status['lastFetchTime']}');
    logger.i('最後獲取狀態: ${status['lastFetchStatus']}');
    
    // 3. 獲取原始配置
    final rawConfig = RemoteConfigService.getAIApiKeys();
    debug['rawConfig'] = rawConfig;
    logger.i('原始配置: ${jsonEncode(rawConfig)}');
    
    // 4. 檢查 OpenAI 配置結構
    final openAIConfig = rawConfig['OpenAIKey'];
    debug['openAIConfig'] = openAIConfig;
    logger.i('OpenAI 配置: ${jsonEncode(openAIConfig)}');
    
    if (openAIConfig is Map<String, dynamic>) {
      // 5. 檢查平台特定的 Key
      final platformKey = openAIConfig[platform];
      debug['platformKey'] = {
        'exists': platformKey != null,
        'type': platformKey.runtimeType.toString(),
        'isEmpty': platformKey?.toString().isEmpty ?? true,
        'length': platformKey?.toString().length ?? 0,
        'value': platformKey != null
            ? '${platformKey.toString().substring(0, platformKey.toString().length > 20 ? 20 : platformKey.toString().length)}...'
            : 'null',
      };
      
      logger.i('平台 $platform 的 Key 存在: ${platformKey != null}');
      logger.i('Key 類型: ${platformKey.runtimeType}');
      logger.i('Key 是否為空: ${platformKey?.toString().isEmpty ?? true}');
      logger.i('Key 長度: ${platformKey?.toString().length ?? 0}');
      if (platformKey != null) {
        final keyStr = platformKey.toString();
        final prefixLength = keyStr.length > 20 ? 20 : keyStr.length;
        logger.i('Key 前綴: ${keyStr.substring(0, prefixLength)}...');
      } else {
        logger.i('Key 前綴: null');
      }
      
      // 6. 檢查所有平台的 Keys
      debug['allPlatformKeys'] = <String, dynamic>{};
      for (final entry in openAIConfig.entries) {
        final key = entry.key;
        final value = entry.value;
        debug['allPlatformKeys'][key] = {
          'exists': value != null,
          'isEmpty': value?.toString().isEmpty ?? true,
          'length': value?.toString().length ?? 0,
        };
        logger.i('平台 $key: 存在=${value != null}, 空=${value?.toString().isEmpty ?? true}, 長度=${value?.toString().length ?? 0}');
      }
    } else {
      debug['openAIConfigError'] = 'OpenAI 配置不是 Map 類型: ${openAIConfig.runtimeType}';
      logger.e('OpenAI 配置不是 Map 類型: ${openAIConfig.runtimeType}');
    }
    
    // 7. 測試實際的 API Key 獲取
    final actualKey = RemoteConfigService.getOpenAIKey();
    debug['actualKey'] = {
      'isEmpty': actualKey.isEmpty,
      'length': actualKey.length,
      'prefix': actualKey.length > 20 ? actualKey.substring(0, 20) + '...' : actualKey,
    };
    logger.i('實際獲取的 Key 是否為空: ${actualKey.isEmpty}');
    logger.i('實際獲取的 Key 長度: ${actualKey.length}');
    
    // 8. 檢查原始 Remote Config 值
    final rawValue = RemoteConfigService.getConfigValue('ai_api_keys');
    debug['rawRemoteConfigValue'] = {
      'isEmpty': rawValue.isEmpty,
      'length': rawValue.length,
      'isValidJson': _isValidJson(rawValue),
    };
    logger.i('原始 Remote Config 值是否為空: ${rawValue.isEmpty}');
    logger.i('原始 Remote Config 值長度: ${rawValue.length}');
    logger.i('原始 Remote Config 值是否為有效 JSON: ${_isValidJson(rawValue)}');
    
    if (rawValue.isNotEmpty && rawValue.length < 1000) {
      logger.i('原始 Remote Config 值: $rawValue');
      debug['rawRemoteConfigValue']['content'] = rawValue;
    }
    
    logger.i('=== OpenAI Key 調試完成 ===');
    
    return debug;
  }
  
  /// 獲取當前平台標識（與 RemoteConfigService 中的方法相同）
  static String _getCurrentPlatform() {
    if (kIsWeb) {
      return 'web';
    } else if (Platform.isIOS) {
      return 'ios';
    } else if (Platform.isAndroid) {
      return 'android';
    } else if (Platform.isMacOS) {
      return 'macos';
    } else if (Platform.isWindows) {
      return 'windows';
    } else if (Platform.isLinux) {
      return 'linux';
    } else {
      return 'unknown';
    }
  }
  
  /// 檢查字符串是否為有效的 JSON
  static bool _isValidJson(String jsonString) {
    try {
      jsonDecode(jsonString);
      return true;
    } catch (e) {
      return false;
    }
  }
  
  /// 比較 Firebase 配置與本地默認配置
  static Map<String, dynamic> compareConfigurations() {
    final comparison = <String, dynamic>{};
    
    try {
      // 獲取 Remote Config 配置
      final remoteConfig = RemoteConfigService.getAIApiKeys();
      comparison['remoteConfig'] = remoteConfig;
      
      // 獲取原始 Remote Config 字符串
      final rawRemoteValue = RemoteConfigService.getConfigValue('ai_api_keys');
      comparison['rawRemoteValue'] = rawRemoteValue;
      
      // 解析您提供的配置
      const yourConfig = '''
{
  "OpenAIKey": {
    "ios": "",
    "android": "",
    "web": "",
    "macos": "",
    "windows": ""
  },
  "GroqAIKey": {
    "ios": "",
    "android": "",
    "web": "",
    "macos": "",
    "windows": ""
  },
  "GoogleGeminiKey": {
    "ios": "",
    "android": "",
    "web": "",
    "macos": "",
    "windows": ""
  }
}''';
      
      final expectedConfig = jsonDecode(yourConfig) as Map<String, dynamic>;
      comparison['expectedConfig'] = expectedConfig;
      
      // 比較配置
      comparison['configMatch'] = _deepEquals(remoteConfig, expectedConfig);
      
      // 比較 OpenAI iOS Key
      final remoteIOSKey = remoteConfig['OpenAIKey']?['ios'] as String?;
      final expectedIOSKey = expectedConfig['OpenAIKey']?['ios'] as String?;
      
      comparison['iosKeyComparison'] = {
        'remote': remoteIOSKey,
        'expected': expectedIOSKey,
        'match': remoteIOSKey == expectedIOSKey,
        'remoteLength': remoteIOSKey?.length ?? 0,
        'expectedLength': expectedIOSKey?.length ?? 0,
      };
      
      logger.i('Remote iOS Key: ${remoteIOSKey?.substring(0, 20)}...');
      logger.i('Expected iOS Key: ${expectedIOSKey?.substring(0, 20)}...');
      logger.i('Keys match: ${remoteIOSKey == expectedIOSKey}');
      
    } catch (e) {
      comparison['error'] = e.toString();
      logger.e('配置比較失敗: $e');
    }
    
    return comparison;
  }
  
  /// 深度比較兩個對象
  static bool _deepEquals(dynamic a, dynamic b) {
    if (a.runtimeType != b.runtimeType) return false;
    
    if (a is Map && b is Map) {
      if (a.length != b.length) return false;
      for (final key in a.keys) {
        if (!b.containsKey(key) || !_deepEquals(a[key], b[key])) {
          return false;
        }
      }
      return true;
    } else if (a is List && b is List) {
      if (a.length != b.length) return false;
      for (int i = 0; i < a.length; i++) {
        if (!_deepEquals(a[i], b[i])) return false;
      }
      return true;
    } else {
      return a == b;
    }
  }
  
  /// 強制刷新並重新檢查
  static Future<Map<String, dynamic>> forceRefreshAndCheck() async {
    final result = <String, dynamic>{};
    
    try {
      logger.i('強制刷新 Remote Config...');
      
      // 刷新配置
      final refreshResult = await RemoteConfigService.refresh();
      result['refreshSuccess'] = refreshResult;
      
      // 等待一下讓配置生效
      await Future.delayed(const Duration(seconds: 2));
      
      // 重新檢查
      final debugResult = debugOpenAIKeyRetrieval();
      result['debugAfterRefresh'] = debugResult;
      
      // 比較配置
      final comparison = compareConfigurations();
      result['configComparison'] = comparison;
      
    } catch (e) {
      result['error'] = e.toString();
      logger.e('強制刷新和檢查失敗: $e');
    }
    
    return result;
  }
  
  /// 打印完整的調試報告
  static void printFullDebugReport() {
    logger.i('=== Remote Config 完整調試報告 ===');
    
    // 1. 基本狀態
    final status = RemoteConfigService.getStatus();
    logger.i('Remote Config 狀態: ${jsonEncode(status)}');
    
    // 2. OpenAI Key 調試
    final openAIDebug = debugOpenAIKeyRetrieval();
    logger.i('OpenAI Key 調試結果: ${jsonEncode(openAIDebug)}');
    
    // 3. 配置比較
    final comparison = compareConfigurations();
    logger.i('配置比較結果: ${jsonEncode(comparison)}');
    
    logger.i('=== 調試報告結束 ===');
  }
}
