import 'dart:convert';

import 'package:shared_preferences/shared_preferences.dart';

import '../../data/models/user/birth_data.dart';
import '../utils/id_generator.dart';
import '../utils/logger_utils.dart';

/// 數據遷移工具，用於修復 ID 重複等問題
class DataMigration {
  static const String _migrationVersionKey = 'data_migration_version';
  static const int _currentMigrationVersion = 1;

  /// 執行所有必要的數據遷移
  static Future<void> runMigrations() async {
    final prefs = await SharedPreferences.getInstance();
    final currentVersion = prefs.getInt(_migrationVersionKey) ?? 0;

    logger.i('當前數據遷移版本: $currentVersion, 目標版本: $_currentMigrationVersion');

    if (currentVersion < 1) {
      await _migrationV1_FixDuplicateIds();
      await prefs.setInt(_migrationVersionKey, 1);
      logger.i('數據遷移 V1 完成：修復重複 ID');
    }

    // 未來的遷移可以在這裡添加
    // if (currentVersion < 2) {
    //   await _migrationV2_SomeOtherFix();
    //   await prefs.setInt(_migrationVersionKey, 2);
    // }

    logger.i('所有數據遷移完成');
  }

  /// 遷移 V1：修復重複的 ID 問題
  static Future<void> _migrationV1_FixDuplicateIds() async {
    logger.i('開始執行遷移 V1：修復重複 ID');

    try {
      final prefs = await SharedPreferences.getInstance();
      
      // 修復出生資料列表中的重複 ID
      await _fixBirthDataDuplicateIds(prefs);
      
      // 修復選中人物的 ID 引用
      await _fixSelectedPersonReference(prefs);
      
      logger.i('遷移 V1 成功完成');
    } catch (e) {
      logger.e('遷移 V1 執行失敗: $e');
      rethrow;
    }
  }

  /// 修復出生資料列表中的重複 ID
  static Future<void> _fixBirthDataDuplicateIds(SharedPreferences prefs) async {
    final String? birthDataListJson = prefs.getString('birthDataList');
    if (birthDataListJson == null) {
      logger.d('沒有找到出生資料列表，跳過 ID 修復');
      return;
    }

    try {
      final List<dynamic> decodedData = jsonDecode(birthDataListJson);
      final List<BirthData> birthDataList = decodedData
          .map((item) => BirthData.fromJson(item))
          .toList();

      logger.d('載入了 ${birthDataList.length} 筆出生資料');

      // 檢查重複的 ID
      final allIds = birthDataList.map((data) => data.id).toList();
      final duplicateIds = IdGenerator.findDuplicateIds(allIds);

      if (duplicateIds.isEmpty) {
        logger.d('沒有發現重複的 ID');
        return;
      }

      logger.w('發現 ${duplicateIds.length} 個重複的 ID: $duplicateIds');

      // 修復重複的 ID
      final fixedBirthDataList = <BirthData>[];
      final usedIds = <String>{};
      final idMapping = <String, String>{}; // 舊ID -> 新ID 的映射

      for (final birthData in birthDataList) {
        String newId = birthData.id;

        // 如果 ID 已經被使用或無效，生成新的 ID
        if (usedIds.contains(birthData.id) || !IdGenerator.isValidId(birthData.id)) {
          // 嘗試基於人物資訊生成確定性的 ID
          newId = IdGenerator.generatePersonId(
            name: birthData.name,
            birthDate: birthData.dateTime,
            birthPlace: birthData.birthPlace,
            latitude: birthData.latitude,
            longitude: birthData.longitude,
          );

          // 如果確定性 ID 也重複，則生成 UUID
          if (usedIds.contains(newId)) {
            newId = IdGenerator.generateUuid();
          }

          idMapping[birthData.id] = newId;
          logger.d('修復 ID: ${birthData.name} 從 ${birthData.id} 改為 $newId');
        }

        usedIds.add(newId);

        // 創建新的 BirthData 對象
        final fixedBirthData = birthData.copyWith(id: newId);
        fixedBirthDataList.add(fixedBirthData);
      }

      // 保存修復後的數據
      final List<Map<String, dynamic>> encodedData = 
          fixedBirthDataList.map((data) => data.toJson()).toList();
      await prefs.setString('birthDataList', jsonEncode(encodedData));

      logger.i('成功修復 ${idMapping.length} 個重複或無效的 ID');

      // 保存 ID 映射，以便其他地方使用
      if (idMapping.isNotEmpty) {
        await prefs.setString('id_mapping_v1', jsonEncode(idMapping));
      }

    } catch (e) {
      logger.e('修復出生資料 ID 時出錯: $e');
      rethrow;
    }
  }

  /// 修復選中人物的 ID 引用
  static Future<void> _fixSelectedPersonReference(SharedPreferences prefs) async {
    try {
      // 獲取 ID 映射
      final String? idMappingJson = prefs.getString('id_mapping_v1');
      if (idMappingJson == null) {
        logger.d('沒有 ID 映射，跳過選中人物引用修復');
        return;
      }

      final Map<String, dynamic> idMapping = jsonDecode(idMappingJson);
      if (idMapping.isEmpty) {
        logger.d('ID 映射為空，跳過選中人物引用修復');
        return;
      }

      // 檢查舊的 selectedPersonId
      final String? oldSelectedPersonId = prefs.getString('selectedPersonId');
      if (oldSelectedPersonId != null && idMapping.containsKey(oldSelectedPersonId)) {
        final String newSelectedPersonId = idMapping[oldSelectedPersonId];
        
        // 移除舊的 selectedPersonId
        await prefs.remove('selectedPersonId');
        
        // 這裡不設置新的 selectedPersonData，讓新的邏輯自動處理
        logger.d('移除了過期的 selectedPersonId: $oldSelectedPersonId');
      }

      // 清理臨時的 ID 映射
      await prefs.remove('id_mapping_v1');

    } catch (e) {
      logger.e('修復選中人物引用時出錯: $e');
      // 這個錯誤不是致命的，可以繼續
    }
  }

  /// 驗證數據完整性
  static Future<bool> validateDataIntegrity() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final String? birthDataListJson = prefs.getString('birthDataList');
      
      if (birthDataListJson == null) {
        return true; // 沒有數據也算正常
      }

      final List<dynamic> decodedData = jsonDecode(birthDataListJson);
      final List<BirthData> birthDataList = decodedData
          .map((item) => BirthData.fromJson(item))
          .toList();

      // 檢查 ID 唯一性
      final allIds = birthDataList.map((data) => data.id).toList();
      final duplicateIds = IdGenerator.findDuplicateIds(allIds);

      if (duplicateIds.isNotEmpty) {
        logger.w('數據完整性檢查失敗：發現重複 ID: $duplicateIds');
        return false;
      }

      // 檢查 ID 有效性
      final invalidIds = allIds.where((id) => !IdGenerator.isValidId(id)).toList();
      if (invalidIds.isNotEmpty) {
        logger.w('數據完整性檢查失敗：發現無效 ID: $invalidIds');
        return false;
      }

      logger.d('數據完整性檢查通過');
      return true;

    } catch (e) {
      logger.e('數據完整性檢查時出錯: $e');
      return false;
    }
  }

  /// 強制重新生成所有 ID（緊急修復用）
  static Future<void> forceRegenerateAllIds() async {
    logger.w('開始強制重新生成所有 ID');

    try {
      final prefs = await SharedPreferences.getInstance();
      final String? birthDataListJson = prefs.getString('birthDataList');
      
      if (birthDataListJson == null) {
        logger.d('沒有找到出生資料列表');
        return;
      }

      final List<dynamic> decodedData = jsonDecode(birthDataListJson);
      final List<BirthData> birthDataList = decodedData
          .map((item) => BirthData.fromJson(item))
          .toList();

      // 為每個人物重新生成 ID
      final fixedBirthDataList = birthDataList.map((birthData) {
        final newId = IdGenerator.generatePersonId(
          name: birthData.name,
          birthDate: birthData.dateTime,
          birthPlace: birthData.birthPlace,
          latitude: birthData.latitude,
          longitude: birthData.longitude,
        );

        logger.d('重新生成 ID: ${birthData.name} 從 ${birthData.id} 改為 $newId');
        return birthData.copyWith(id: newId);
      }).toList();

      // 保存修復後的數據
      final List<Map<String, dynamic>> encodedData = 
          fixedBirthDataList.map((data) => data.toJson()).toList();
      await prefs.setString('birthDataList', jsonEncode(encodedData));

      // 清除選中人物的引用，讓系統重新選擇
      await prefs.remove('selectedPersonId');
      await prefs.remove('selectedPersonData');

      logger.i('強制重新生成所有 ID 完成');

    } catch (e) {
      logger.e('強制重新生成 ID 時出錯: $e');
      rethrow;
    }
  }
}
