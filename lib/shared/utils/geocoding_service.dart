import 'dart:convert';

import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:geocoding/geocoding.dart';
import 'package:geolocator/geolocator.dart';
import 'package:http/http.dart' as http;

import '../../core/utils/logger_utils.dart';

/// 地理編碼相關異常
class LocationNotFoundException implements Exception {
  final String message;
  LocationNotFoundException(this.message);

  @override
  String toString() => message;
}

class GeocodingException implements Exception {
  final String message;
  GeocodingException(this.message);

  @override
  String toString() => message;
}

/// 地理編碼服務
/// 提供將地址轉換為經緯度的功能
class GeocodingService {
  /// 使用 Flutter 的 geocoding 套件進行地理編碼
  /// 將地址轉換為經緯度
  /// 如果找不到地址，會拋出 [LocationNotFoundException] 異常
  static Future<Map<String, double>> getCoordinatesFromAddress(
      String address) async {
    // 清理輸入地址
    final cleanedAddress = _cleanAddress(address);

    if (cleanedAddress.isEmpty) {
      throw LocationNotFoundException('輸入的地址包含無效字符，請輸入有效的地點名稱（如：台北市、美國紐約）');
    }

    try {
      // 使用 geocoding 套件進行地理編碼
      List<Location> locations = await locationFromAddress(cleanedAddress);

      if (locations.isNotEmpty) {
        Location location = locations.first;
        logger.d(
            '地址 "$cleanedAddress" 的經緯度: ${location.latitude}, ${location.longitude}');
        return {
          'latitude': location.latitude,
          'longitude': location.longitude,
        };
      } else {
        logger.w('找不到地址 "$cleanedAddress" 的經緯度');
        // 如果 geocoding 套件無法解析地址，嘗試使用 OpenStreetMap Nominatim API
        return await _getCoordinatesFromNominatim(cleanedAddress);
      }
    } catch (e) {
      logger.e('使用 geocoding 套件進行地理編碼時出錯: $e');
      // 如果 geocoding 套件出錯，嘗試使用 OpenStreetMap Nominatim API
      return await _getCoordinatesFromNominatim(cleanedAddress);
    }
  }

  /// 使用 OpenStreetMap Nominatim API 進行地理編碼
  /// 作為備用方案
  static Future<Map<String, double>> _getCoordinatesFromNominatim(
      String address) async {
    try {
      // 對地址進行 URL 編碼
      final encodedAddress = Uri.encodeComponent(address);

      // 構建 Nominatim API 請求 URL
      final url =
          'https://nominatim.openstreetmap.org/search?q=$encodedAddress&format=json&limit=1';

      // 發送 HTTP 請求
      final response = await http.get(
        Uri.parse(url),
        headers: {
          'User-Agent': 'AstroMatch/1.0', // Nominatim 要求提供 User-Agent
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);

        if (data.isNotEmpty) {
          final double latitude = double.parse(data[0]['lat']);
          final double longitude = double.parse(data[0]['lon']);

          logger.d(
              '使用 Nominatim API 獲取地址 "$address" 的經緯度: $latitude, $longitude');

          return {
            'latitude': latitude,
            'longitude': longitude,
          };
        } else {
          logger.w('Nominatim API 找不到地址 "$address" 的經緯度');
          throw LocationNotFoundException('找不到地址 "$address" 的經緯度，請確認地址是否正確');
        }
      } else {
        logger.e('Nominatim API 請求失敗: ${response.statusCode}');
        throw GeocodingException('地理編碼服務暫時無法使用，請稍後再試');
      }
    } catch (e) {
      if (e is LocationNotFoundException || e is GeocodingException) {
        rethrow;
      }
      logger.e('使用 Nominatim API 進行地理編碼時出錯: $e');
      throw GeocodingException('地理編碼服務發生錯誤: ${e.toString()}');
    }
  }

  /// 清理地址輸入，移除無效字符
  static String _cleanAddress(String address) {
    if (address.isEmpty) return '';

    String original = address;

    // 移除注音符號和無效字符
    String cleaned = address
        .replaceAll(RegExp(r'[ㄅ-ㄩˊˇˋ˙]'), '') // 移除注音符號
        .replaceAll(RegExp(r'[^\u4e00-\u9fff\w\s,.-]'), '') // 只保留中文、英文、數字、空格和常用標點
        .trim();

    // 如果清理後為空或太短，嘗試智能修復
    if (cleaned.isEmpty || cleaned.length < 2) {
      // 常見的輸入法問題修復
      if (original.contains('美') || original.toLowerCase().contains('us') || original.toLowerCase().contains('america')) {
        cleaned = '美國';
      } else if (original.contains('日') || original.toLowerCase().contains('japan')) {
        cleaned = '日本';
      } else if (original.contains('韓') || original.toLowerCase().contains('korea')) {
        cleaned = '韓國';
      } else if (original.contains('台') || original.contains('臺') || original.toLowerCase().contains('taiwan')) {
        cleaned = '台灣';
      } else if (original.contains('中') || original.toLowerCase().contains('china')) {
        cleaned = '中國';
      } else if (original.toLowerCase().contains('uk') || original.toLowerCase().contains('britain')) {
        cleaned = '英國';
      } else if (original.toLowerCase().contains('france')) {
        cleaned = '法國';
      } else if (original.toLowerCase().contains('germany')) {
        cleaned = '德國';
      }
    }

    // 記錄清理過程（只在有變化時記錄）
    if (original != cleaned) {
      logger.d('地址清理: "$original" -> "$cleaned"');
    }

    return cleaned;
  }

  /// 獲取預設的台北經緯度
  static Map<String, double> getDefaultCoordinates() {
    return {
      'latitude': 25.03, // 台北緯度
      'longitude': 121.57, // 台北經度
    };
  }

  /// 獲取當前位置的經緯度
  static Future<Map<String, double>?> getCurrentLocation() async {
    try {
      // 檢查位置服務是否啟用
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        logger.w('位置服務未啟用');
        return null;
      }

      // 檢查位置權限
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          logger.w('位置權限被拒絕');
          return null;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        logger.w('位置權限被永久拒絕');
        return null;
      }

      // 獲取當前位置
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      logger.d('獲取當前位置: ${position.latitude}, ${position.longitude}');

      return {
        'latitude': position.latitude,
        'longitude': position.longitude,
      };
    } catch (e) {
      logger.e('獲取當前位置時出錯: $e');
      return null;
    }
  }

  /// 根據經緯度獲取地址
  static Future<String?> getAddressFromCoordinates(double latitude, double longitude) async {
    try {
      // 如果是 Web 環境，使用 Nominatim API 獲取地址
      if (kIsWeb) {
        return await _getAddressFromNominatimAPI(latitude, longitude);
      }

      List<Placemark> placemarks = await placemarkFromCoordinates(latitude, longitude);

      if (placemarks.isNotEmpty) {
        Placemark place = placemarks.first;

        // 構建地址字符串
        String address = '';

        // 台灣地址格式
        if (place.country == 'Taiwan') {
          if (place.administrativeArea != null && place.administrativeArea!.isNotEmpty) {
            address += place.administrativeArea!; // 縣/市
          }
          if (place.locality != null && place.locality!.isNotEmpty) {
            address += place.locality!; // 鄉鎮市區
          }
        } else {
          // 其他國家的地址格式
          if (place.locality != null && place.locality!.isNotEmpty) {
            address += place.locality!;
          }
          if (place.administrativeArea != null && place.administrativeArea!.isNotEmpty) {
            if (address.isNotEmpty) address += ', ';
            address += place.administrativeArea!;
          }
          if (place.country != null && place.country!.isNotEmpty) {
            if (address.isNotEmpty) address += ', ';
            address += place.country!;
          }
        }

        logger.d('經緯度 $latitude, $longitude 的地址: $address');
        return address;
      } else {
        logger.w('找不到經緯度 $latitude, $longitude 的地址');
        // 如果本地獲取失敗，嘗試使用 Nominatim API
        return await _getAddressFromNominatimAPI(latitude, longitude);
      }
    } catch (e) {
      logger.e('根據經緯度獲取地址時出錯: $e');
      // 如果出錯，嘗試使用 Nominatim API
      return await _getAddressFromNominatimAPI(latitude, longitude);
    }
  }

  /// 使用 Nominatim API 獲取地址
  static Future<String?> _getAddressFromNominatimAPI(double latitude, double longitude) async {
    try {
      // 構建 Nominatim API 請求 URL
      final url = 'https://nominatim.openstreetmap.org/reverse?format=json&lat=$latitude&lon=$longitude&zoom=18&addressdetails=1';

      // 發送 HTTP 請求
      final response = await http.get(
        Uri.parse(url),
        headers: {
          'User-Agent': 'AstroMatch/1.0', // Nominatim 要求提供 User-Agent
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);

        if (data != null && data['display_name'] != null) {
          String address = data['display_name'];

          // 如果有詳細地址信息，嘗試提取縣市和區域
          if (data['address'] != null) {
            String simpleAddress = '';

            // 先嘗試取台灣的縣市和區域
            if (data['address']['city'] != null) {
              simpleAddress = data['address']['city'];
            } else if (data['address']['county'] != null) {
              simpleAddress = data['address']['county'];
            } else if (data['address']['state'] != null) {
              simpleAddress = data['address']['state'];
            }

            // 如果有區域信息，添加到地址中
            if (data['address']['suburb'] != null && simpleAddress.isNotEmpty) {
              simpleAddress += data['address']['suburb'];
            } else if (data['address']['district'] != null && simpleAddress.isNotEmpty) {
              simpleAddress += data['address']['district'];
            }

            // 如果成功提取了簡化地址，則使用簡化地址
            if (simpleAddress.isNotEmpty) {
              address = simpleAddress;
            }
          }

          logger.d('使用 Nominatim API 獲取地址: $address');
          return address;
        }
      }

      logger.w('Nominatim API 獲取地址失敗: ${response.statusCode}');
      return null;
    } catch (e) {
      logger.e('使用 Nominatim API 獲取地址時出錯: $e');
      return null;
    }
  }
}
