import 'package:flutter/material.dart';

/// 星盤文字樣式管理工具類
/// 根據星盤大小動態調整字體大小
class ChartTextStyles {
  /// 基準星盤大小（用於計算縮放比例）
  static const double BASE_CHART_SIZE = 400.0;
  
  /// 大星盤的閾值（超過此大小需要放大字體）
  static const double LARGE_CHART_THRESHOLD = 500.0;
  
  /// 計算字體縮放比例
  static double _getFontScale(double chartSize) {
    if (chartSize <= BASE_CHART_SIZE) {
      // 小於基準大小時，使用標準比例
      return chartSize / BASE_CHART_SIZE;
    } else if (chartSize > LARGE_CHART_THRESHOLD) {
      // 大於閾值時，額外放大字體
      final baseScale = chartSize / BASE_CHART_SIZE;
      final extraScale = (chartSize - LARGE_CHART_THRESHOLD) / BASE_CHART_SIZE * 0.3;
      return baseScale + extraScale;
    } else {
      // 在基準大小和閾值之間時，使用線性縮放
      return chartSize / BASE_CHART_SIZE;
    }
  }
  
  /// 宮位數字樣式
  static TextStyle getHouseNumberStyle(double chartSize) {
    final scale = _getFontScale(chartSize);
    return TextStyle(
      color: Colors.black,
      fontSize: (12 * scale).clamp(8.0, 18.0),
      fontWeight: FontWeight.bold,
    );
  }
  
  /// 星座符號樣式
  static TextStyle getZodiacSymbolStyle(double chartSize, Color color) {
    final scale = _getFontScale(chartSize);
    return TextStyle(
      color: color,
      fontSize: (16 * scale).clamp(12.0, 24.0),
      fontWeight: FontWeight.bold,
      fontFeatures: const [FontFeature.disable('liga')],
      fontFamily: 'astro_one_font',
    );
  }
  
  /// 行星符號樣式
  static TextStyle getPlanetSymbolStyle(double chartSize, Color color) {
    final scale = _getFontScale(chartSize);
    return TextStyle(
      color: color,
      fontSize: (18 * scale).clamp(12.0, 24.0),
      fontWeight: FontWeight.normal,
      fontFamily: 'astro_one_font',
    );
  }
  
  /// 相位符號樣式
  static TextStyle getAspectSymbolStyle(double chartSize, Color color) {
    final scale = _getFontScale(chartSize);
    return TextStyle(
      color: color,
      fontSize: (14 * scale).clamp(10.0, 20.0),
      fontFamily: 'astro_one_font',
    );
  }
  
  /// 上升點標記樣式
  static TextStyle getAscendantStyle(double chartSize) {
    final scale = _getFontScale(chartSize);
    return TextStyle(
      color: Colors.red,
      fontSize: (12 * scale).clamp(8.0, 18.0),
      fontWeight: FontWeight.bold,
    );
  }
  
  /// 界主星符號樣式
  static TextStyle getTermRulerStyle(double chartSize, Color color) {
    final scale = _getFontScale(chartSize);
    return TextStyle(
      color: color,
      fontSize: (10 * scale).clamp(7.0, 15.0),
      fontWeight: FontWeight.bold,
      fontFamily: 'astro_one_font',
    );
  }
  
  /// 宮位度數樣式
  static TextStyle getHouseDegreeStyle(double chartSize) {
    final scale = _getFontScale(chartSize);
    return TextStyle(
      color: Colors.black87,
      fontSize: (9 * scale).clamp(6.0, 14.0),
      fontWeight: FontWeight.w600,
      shadows: [
        Shadow(
          color: Colors.white.withValues(alpha: 0.9),
          blurRadius: 1,
          offset: const Offset(0, 0),
        ),
      ],
    );
  }
  
  /// 宮位分數樣式
  static TextStyle getHouseMinuteStyle(double chartSize) {
    final scale = _getFontScale(chartSize);
    return TextStyle(
      color: Colors.black87,
      fontSize: (8 * scale).clamp(6.0, 12.0),
      fontWeight: FontWeight.w500,
      shadows: [
        Shadow(
          color: Colors.white.withValues(alpha: 0.9),
          blurRadius: 1,
          offset: const Offset(0, 0),
        ),
      ],
    );
  }
  
  /// 行星度數樣式
  static TextStyle getPlanetDegreeStyle(double chartSize) {
    final scale = _getFontScale(chartSize);
    return TextStyle(
      color: Colors.black87,
      fontSize: (10 * scale).clamp(7.0, 15.0),
      fontWeight: FontWeight.w600,
      shadows: [
        Shadow(
          color: Colors.white.withValues(alpha: 0.9),
          blurRadius: 1,
          offset: const Offset(0, 0),
        ),
      ],
    );
  }
  
  /// 行星星座樣式
  static TextStyle getPlanetZodiacStyle(double chartSize, Color color) {
    final scale = _getFontScale(chartSize);
    return TextStyle(
      color: color,
      fontSize: (12 * scale).clamp(8.0, 18.0),
      fontWeight: FontWeight.bold,
      fontFamily: 'astro_one_font',
      shadows: [
        Shadow(
          color: Colors.white.withValues(alpha: 0.9),
          blurRadius: 1,
          offset: const Offset(0, 0),
        ),
      ],
    );
  }
  
  /// 行星分數樣式
  static TextStyle getPlanetMinuteStyle(double chartSize) {
    final scale = _getFontScale(chartSize);
    return TextStyle(
      color: Colors.black87,
      fontSize: (10 * scale).clamp(6.0, 12.0),
      fontWeight: FontWeight.w500,
      shadows: [
        Shadow(
          color: Colors.white.withValues(alpha: 0.9),
          blurRadius: 1,
          offset: const Offset(0, 0),
        ),
      ],
    );
  }
  
  /// 逆行符號樣式
  static TextStyle getRetrogradeStyle(double chartSize) {
    final scale = _getFontScale(chartSize);
    return TextStyle(
      color: Colors.red.shade700,
      fontSize: (14 * scale).clamp(7.0, 15.0),
      fontWeight: FontWeight.bold,
      shadows: [
        Shadow(
          color: Colors.white.withValues(alpha: 0.9),
          blurRadius: 1,
          offset: const Offset(0, 0),
        ),
      ],
    );
  }
  
  /// 獲取字體縮放比例（用於調試）
  static double getFontScale(double chartSize) {
    return _getFontScale(chartSize);
  }
  
  /// 獲取字體大小信息（用於調試）
  static Map<String, double> getFontSizeInfo(double chartSize) {
    final scale = _getFontScale(chartSize);
    return {
      'scale': scale,
      'houseNumber': (12 * scale).clamp(8.0, 18.0),
      'zodiacSymbol': (16 * scale).clamp(12.0, 24.0),
      'planetSymbol': (16 * scale).clamp(12.0, 24.0),
      'aspectSymbol': (14 * scale).clamp(10.0, 20.0),
      'ascendant': (12 * scale).clamp(8.0, 18.0),
      'termRuler': (10 * scale).clamp(7.0, 15.0),
      'houseDegree': (9 * scale).clamp(6.0, 14.0),
      'houseMinute': (8 * scale).clamp(6.0, 12.0),
      'planetDegree': (10 * scale).clamp(7.0, 15.0),
      'planetZodiac': (12 * scale).clamp(8.0, 18.0),
      'planetMinute': (8 * scale).clamp(6.0, 12.0),
      'retrograde': (10 * scale).clamp(7.0, 15.0),
    };
  }
}
