import 'dart:convert';
import 'dart:io' if (dart.library.html) 'dart:io' as io;

import 'package:csv/csv.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart' as flutter_foundation;
import 'package:intl/intl.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';

import '../../astreal.dart';
import 'geocoding_service.dart';
// 條件導入 web_utils.dart，只在 Web 平台上使用
// ignore: uri_does_not_exist
import 'web_utils.dart' if (dart.library.io) 'web_utils_stub.dart' as web_utils;

final dateFormatter = DateFormat('yyyy-MM-dd HH:mm');

/// CSV 文件處理工具類
/// 提供將出生資料匯出為 CSV 文件和從 CSV 文件匯入出生資料的功能
class CsvHelper {
  // CSV 文件的標題行
  static const List<String> _csvHeaders = [
    'id',
    'name',
    'birthDate',
    'birthPlace',
    'notes',
    'latitude',
    'longitude',
    'category',
    'gender',
    'isTimeUncertain',
    'lastAccessedAt',
    'createdAt'
  ];

  // 支持的替代標題行
  static const Map<String, String> _alternativeHeaders = {
    '姓名': 'name',
    '生日': 'birthDate',
    '出生地緯度': 'latitude',
    '出生地經度': 'longitude',
    '出生地': 'birthPlace',
    '居住地緯度': 'residenceLatitude',
    '居住地經度': 'residenceLongitude',
    '標籤': 'tags',
    '是否隱藏': 'isHidden'
  };

  /// 根據平台自動選擇匯出方式
  static Future<String> exportBirthData(List<BirthData> birthDataList) async {
    if (flutter_foundation.kIsWeb) {
      await _exportToCsvWeb(birthDataList);
      return "";
    } else {
      final filePath = await exportBirthDataToCsv(birthDataList);
      return filePath;
    }
  }

  /// 生成 CSV 內容字串（不儲存檔案）
  ///
  /// [birthDataList] - 要轉換的出生資料列表
  /// 返回 CSV 格式的字串
  static String generateCsvContent(List<BirthData> birthDataList) {
    try {
      // 將出生資料轉換為 CSV 格式
      List<List<dynamic>> csvData = [_csvHeaders];

      for (var data in birthDataList) {
        csvData.add([
          data.id,
          data.name,
          dateFormatter.format(data.dateTime),
          data.birthPlace,
          data.notes ?? '',
          data.latitude,
          data.longitude,
          data.category.displayName,
          data.gender?.displayName ?? '',
          data.isTimeUncertain ? '是' : '否',
          data.lastAccessedAt != null ? dateFormatter.format(data.lastAccessedAt!) : '',
          dateFormatter.format(data.createdAt)
        ]);
      }

      // 將 CSV 數據轉換為字符串
      return const ListToCsvConverter().convert(csvData);

    } catch (e) {
      logger.e('生成 CSV 內容時出錯: $e');
      return '';
    }
  }

  /// 將出生資料列表匯出為 CSV 文件
  ///
  /// [birthDataList] - 要匯出的出生資料列表
  /// 返回生成的 CSV 文件路徑
  static Future<String> exportBirthDataToCsv(
      List<BirthData> birthDataList) async {
    try {
      // 將出生資料轉換為 CSV 格式
      List<List<dynamic>> csvData = [_csvHeaders];

      for (var data in birthDataList) {
        csvData.add([
          data.id,
          data.name,
          dateFormatter.format(data.dateTime),
          data.birthPlace,
          data.notes ?? '',
          data.latitude,
          data.longitude,
          data.category.displayName,
          data.gender?.displayName ?? '',
          data.isTimeUncertain ? '是' : '否',
          data.lastAccessedAt != null ? dateFormatter.format(data.lastAccessedAt!) : '',
          dateFormatter.format(data.createdAt)
        ]);
      }

      // 將 CSV 數據轉換為字符串
      String csv = const ListToCsvConverter().convert(csvData);

      PackageInfo packageInfo = await PackageInfo.fromPlatform();
      String appName = packageInfo.appName;

      // 獲取臨時目錄
      final tempDir = await getTemporaryDirectory();
      final timestamp = DateFormat('yyyyMMddHHmm').format(DateTime.now());
      final fileName = '${appName}_birth_data_$timestamp.csv';
      final filePath = '${tempDir.path}/$fileName';

      // 寫入文件
      final file = io.File(filePath);
      await file.writeAsString(csv);

      logger.i('成功匯出 ${birthDataList.length} 條出生資料到 CSV 文件: $filePath');
      return filePath;
    } catch (e) {
      logger.e('匯出出生資料到 CSV 文件時出錯: $e');
      rethrow;
    }
  }

  /// Web 平台：觸發瀏覽器下載
  static Future<void> _exportToCsvWeb(List<BirthData> birthDataList) async {
    try {
      List<List<dynamic>> csvData = [_csvHeaders];

      for (var data in birthDataList) {
        csvData.add([
          data.id,
          data.name,
          dateFormatter.format(data.dateTime),
          data.birthPlace,
          data.notes ?? '',
          data.latitude,
          data.longitude,
          data.category.displayName,
          data.gender?.displayName ?? '',
          data.isTimeUncertain ? '是' : '否',
          data.lastAccessedAt != null ? dateFormatter.format(data.lastAccessedAt!) : '',
          dateFormatter.format(data.createdAt)
        ]);
      }

      String csv = const ListToCsvConverter().convert(csvData);

      // 使用條件導入來處理 Web 平台
      if (flutter_foundation.kIsWeb) {
        // 使用條件導入的 web_utils
        await web_utils.triggerWebDownload(csv);
      } else {
        // 非 Web 平台不應該執行到這裡
        logger.w('非 Web 平台不應該調用 _exportToCsvWeb 方法');
      }

      logger.i('已在 Web 上觸發 CSV 下載');
    } catch (e) {
      logger.e('Web 匯出 CSV 時出錯: $e');
    }
  }

  /// 從 CSV 文件匯入出生資料（帶進度回調）
  ///
  /// [onProgress] - 進度回調函數 (status, current, total)
  /// 返回匯入的出生資料列表
  static Future<List<BirthData>> importBirthDataFromCsvWithProgress({
    Function(String status, int current, int total)? onProgress,
  }) async {
    return await _importBirthDataFromCsvInternal(onProgress: onProgress);
  }

  /// 從 CSV 文件匯入出生資料
  ///
  /// 返回匯入的出生資料列表
  static Future<List<BirthData>> importBirthDataFromCsv() async {
    return await _importBirthDataFromCsvInternal();
  }

  /// 從 CSV 字串直接匯入出生資料
  ///
  /// [csvContent] - CSV 內容字串
  /// 返回匯入的出生資料列表
  static Future<List<BirthData>> importBirthData(String csvContent) async {
    try {
      logger.i('開始解析 CSV 內容，長度: ${csvContent.length}');

      // 解析 CSV 內容
      List<List<dynamic>> csvData = const CsvToListConverter().convert(csvContent);

      if (csvData.isEmpty) {
        logger.w('CSV 內容為空');
        return [];
      }

      logger.i('CSV 解析完成，共 ${csvData.length} 行（包含標題行）');

      // 檢查標題行
      if (csvData.length < 2) {
        logger.w('CSV 只有標題行，沒有資料');
        return [];
      }

      final headers = csvData[0].map((e) => e.toString()).toList();
      logger.d('CSV 標題: $headers');

      // 第一行是標題，從第二行開始是資料
      List<BirthData> birthDataList = [];
      int skippedCount = 0;

      for (int i = 1; i < csvData.length; i++) {
        try {
          List<dynamic> row = csvData[i];
          logger.d('處理第 ${i + 1} 行: $row');

          // 確保行有足夠的欄位
          if (row.length < 3) {
            logger.w('第 ${i + 1} 行資料不完整（欄位數: ${row.length}），跳過');
            skippedCount++;
            continue;
          }

          // 解析出生資料
          // 根據實際的 CSV 格式來調整欄位對應
          // 標準格式：id, name, dateTime, birthPlace, notes, latitude, longitude, category, lastAccessedAt, createdAt

          String name = row.length > 1 ? (row[1]?.toString() ?? '') : '';
          if (name.isEmpty) {
            logger.w('第 ${i + 1} 行姓名為空，跳過');
            skippedCount++;
            continue;
          }

          DateTime? birthDateTime = row.length > 2 ? _parseDate(row[2]?.toString()) : null;
          if (birthDateTime == null) {
            logger.w('第 ${i + 1} 行日期時間無效: ${row.length > 2 ? row[2] : "無日期欄位"}，跳過');
            skippedCount++;
            continue;
          }

          String birthPlace = row.length > 3 ? (row[3]?.toString() ?? '') : '';
          if (birthPlace.isEmpty) {
            logger.w('第 ${i + 1} 行出生地為空，跳過');
            skippedCount++;
            continue;
          }

          double latitude = row.length > 5 ? (_parseDouble(row[5]?.toString()) ?? 0.0) : 0.0;
          double longitude = row.length > 6 ? (_parseDouble(row[6]?.toString()) ?? 0.0) : 0.0;

          // 如果座標為 0,0 或無效，記錄警告但不跳過
          if (latitude == 0.0 && longitude == 0.0) {
            logger.w('第 ${i + 1} 行座標為 0,0，可能需要重新定位');
          }

          BirthData birthData = BirthData(
            id: '', // 強制重新生成 ID，避免重複覆蓋
            name: name,
            dateTime: birthDateTime,
            birthPlace: birthPlace,
            notes: row.length > 4 && row[4]?.toString().isNotEmpty == true ? row[4]?.toString() : null,
            latitude: latitude,
            longitude: longitude,
            // 正確處理 category 欄位
            category: row.length > 7 ? ChartCategory.fromString(row[7]?.toString() ?? '個人') : ChartCategory.personal,
            // 處理 gender 欄位
            gender: row.length > 8 && row[8]?.toString().isNotEmpty == true
                ? Gender.fromString(row[8]!.toString())
                : null,
            // 處理出生時間不確定欄位
            isTimeUncertain: row.length > 9 ? _parseBoolean(row[9]?.toString()) : false,
            // 處理其他可能的欄位
            lastAccessedAt: row.length > 10 && row[10]?.toString().isNotEmpty == true
                ? _parseDateTime(row[10]!.toString())
                : null,
            createdAt: row.length > 11 && row[11]?.toString().isNotEmpty == true
                ? _parseDateTime(row[11]!.toString()) ?? DateTime.now()
                : DateTime.now(),
          );

          birthDataList.add(birthData);
          logger.d('成功解析第 ${i + 1} 行: ${birthData.name}');
        } catch (e) {
          logger.w('解析第 ${i + 1} 行時出錯: $e, 行內容: ${i < csvData.length ? csvData[i] : "無法取得"}');
          skippedCount++;
          continue;
        }
      }

      logger.i('CSV 解析完成 - 成功: ${birthDataList.length} 筆, 跳過: $skippedCount 筆, 總計: ${csvData.length - 1} 筆');
      return birthDataList;

    } catch (e) {
      logger.e('從 CSV 字串匯入出生資料時出錯: $e');
      logger.e('CSV 內容: $csvContent');
      return [];
    }
  }

  /// 內部匯入方法
  static Future<List<BirthData>> _importBirthDataFromCsvInternal({
    Function(String status, int current, int total)? onProgress,
  }) async {
    try {
      onProgress?.call('正在選擇 CSV 文件...', 0, 0);

      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['csv'],
        withData: true,
      );

      if (result == null || result.files.isEmpty) {
        logger.w('未選擇 CSV 文件');
        onProgress?.call('未選擇文件', 0, 0);
        return [];
      }

      onProgress?.call('正在讀取文件內容...', 0, 0);

      String? csvString;
      if (result.files.single.bytes != null) {
        csvString = utf8.decode(result.files.single.bytes!);
      } else if (result.files.single.path != null && !flutter_foundation.kIsWeb) {
        final file = io.File(result.files.single.path!);
        csvString = await file.readAsString();
      } else {
        throw Exception('無法讀取選取的 CSV 文件');
      }

      onProgress?.call('正在解析 CSV 格式...', 0, 0);

      logger.d('原始 CSV 內容:\n$csvString');
      List<List<dynamic>> csvData = const CsvToListConverter(
        fieldDelimiter: ',',
        eol: '\n',
        shouldParseNumbers: false, // 避免把 0.0、false 亂轉型
      ).convert(csvString);

      if (csvData.isEmpty) {
        logger.w('CSV 文件為空');
        onProgress?.call('CSV 文件為空', 0, 0);
        return [];
      }

      onProgress?.call('正在驗證 CSV 格式...', 0, csvData.length - 1);

      final originalHeaders = csvData[0].map((e) => e.toString()).toList();
      Map<int, String> headerMap = {};
      for (int i = 0; i < originalHeaders.length; i++) {
        String header = originalHeaders[i];
        if (_alternativeHeaders.containsKey(header)) {
          headerMap[i] = _alternativeHeaders[header]!;
        } else {
          headerMap[i] = header;
        }
      }

      final mappedHeaders = headerMap.values.toList();
      bool hasNameColumn = mappedHeaders.contains('name');
      bool hasBirthPlaceColumn = mappedHeaders.contains('birthPlace');

      if (!hasNameColumn || !hasBirthPlaceColumn) {
        throw Exception('CSV 文件缺少必要的列：${!hasNameColumn ? "姓名" : ""} ${!hasBirthPlaceColumn ? "出生地" : ""}');
      }

      List<BirthData> importedData = [];
      final totalRows = csvData.length - 1; // 扣除標題行

      onProgress?.call('開始處理資料...', 0, totalRows);

      for (int i = 1; i < csvData.length; i++) {
        List<dynamic> row = csvData[i];

        // 更新進度
        onProgress?.call('正在處理第 $i 筆資料...', i, totalRows);

        if (row.length < originalHeaders.length) {
          logger.w('行 $i 欄位數不足，將嘗試修正...');
          String rowStr = row.join(',');
          List<String> splitRow = _fixRow(rowStr);
          row = splitRow.map((e) => e.trim()).toList();
        }

        if (row.isEmpty || row.every((cell) => cell == null || cell.toString().trim().isEmpty)) {
          logger.d('跳過空行: $i');
          continue;
        }

        Map<String, dynamic> rowMap = {};
        for (int j = 0; j < row.length && j < originalHeaders.length; j++) {
          String mappedHeader = headerMap[j] ?? originalHeaders[j];
          rowMap[mappedHeader] = row[j];
        }

        if (rowMap['name'] == null || rowMap['birthPlace'] == null) {
          logger.w('跳過缺少必要欄位的行 $i: $rowMap');
          continue;
        }

        double? latitude = _tryParseDouble(rowMap['latitude']);
        double? longitude = _tryParseDouble(rowMap['longitude']);

        if (_isInvalidCoord(latitude) || _isInvalidCoord(longitude)) {
          try {
            onProgress?.call('正在解析地址: ${rowMap['birthPlace']}...', i, totalRows);
            final coordinates = await GeocodingService.getCoordinatesFromAddress(rowMap['birthPlace'].toString());
            latitude = coordinates['latitude'];
            longitude = coordinates['longitude'];
            logger.d('地址轉換成功: ${rowMap['birthPlace']} -> $latitude, $longitude');
          } catch (e) {
            logger.e('地址轉換失敗: ${rowMap['birthPlace']}, 錯誤: $e');
            continue;
          }
        }

        DateTime? birthDate;
        String? birthDateStr = rowMap['birthDate']?.toString().trim();
        if (birthDateStr != null && birthDateStr.isNotEmpty) {
          birthDate = _parseDateTime(birthDateStr);
          if (birthDate == null) {
            logger.w('解析日期失敗 (跳過): $birthDateStr');
            continue;
          }
        }

        if (birthDate == null || latitude == null || longitude == null) {
          logger.w('資料不完整，跳過第 $i 行: birthDate=$birthDate, lat=$latitude, lon=$longitude');
          continue;
        }

        try {
          // 處理 ID 唯一性
          String id = rowMap['id']?.toString() ?? '';
          if (id.isEmpty) {
            id = _generateUniqueId();
          } else {
            // 檢查 ID 是否已存在，如果存在則生成新的
            final existingIds = importedData.map((data) => data.id).toSet();
            if (existingIds.contains(id)) {
              id = _generateUniqueId();
              logger.w('ID 重複，已生成新的唯一 ID: $id');
            }
          }
          // 解析最後訪問時間
          DateTime? lastAccessedAt;
          String? lastAccessedStr = rowMap['lastAccessedAt']?.toString().trim();
          if (lastAccessedStr != null && lastAccessedStr.isNotEmpty) {
            lastAccessedAt = _parseDateTime(lastAccessedStr);
            if (lastAccessedAt == null) {
              logger.w('解析最後訪問時間失敗: $lastAccessedStr');
            }
          }

          // 解析創建時間
          DateTime createdAt = DateTime.now();
          String? createdAtStr = rowMap['createdAt']?.toString().trim();
          if (createdAtStr != null && createdAtStr.isNotEmpty) {
            final parsedCreatedAt = _parseDateTime(createdAtStr);
            if (parsedCreatedAt != null) {
              createdAt = parsedCreatedAt;
            } else {
              logger.w('解析創建時間失敗，使用當前時間: $createdAtStr');
            }
          }

          // 解析性別
          Gender? gender;
          String? genderStr = rowMap['gender']?.toString().trim();
          if (genderStr != null && genderStr.isNotEmpty) {
            try {
              gender = Gender.fromString(genderStr);
            } catch (e) {
              logger.w('解析性別失敗: $genderStr, 錯誤: $e');
            }
          }

          // 解析出生時間不確定
          bool isTimeUncertain = false;
          String? timeUncertainStr = rowMap['isTimeUncertain']?.toString().trim();
          if (timeUncertainStr != null && timeUncertainStr.isNotEmpty) {
            isTimeUncertain = _parseBoolean(timeUncertainStr);
          }

          final birthData = BirthData(
            id: id,
            name: rowMap['name'].toString(),
            dateTime: birthDate,
            birthPlace: rowMap['birthPlace'].toString(),
            notes: rowMap['notes']?.toString(),
            latitude: latitude,
            longitude: longitude,
            category: ChartCategory.fromString(rowMap['category']?.toString() ?? '個人'),
            gender: gender,
            isTimeUncertain: isTimeUncertain,
            lastAccessedAt: lastAccessedAt,
            createdAt: createdAt,
          );

          importedData.add(birthData);
          logger.d('成功匯入: ${birthData.name}, ${birthData.dateTime}, ${birthData.birthPlace}, ID: ${birthData.id}');
        } catch (e) {
          logger.e('建立 BirthData 時出錯 (第 $i 行): $row, 錯誤: $e');
        }
      }

      onProgress?.call('匯入完成', importedData.length, importedData.length);
      logger.i('共匯入 ${importedData.length} 筆出生資料');
      return importedData;
    } catch (e) {
      logger.e('匯入過程發生錯誤: $e');
      onProgress?.call('匯入失敗: $e', 0, 0);
      rethrow;
    }
  }

  static double? _tryParseDouble(dynamic value) {
    if (value == null) return null;
    return double.tryParse(value.toString());
  }

  static bool _isInvalidCoord(double? coord) {
    return coord == null || coord == -1.0;
  }

  /// 生成唯一 ID
  static String _generateUniqueId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = (timestamp % 10000).toString().padLeft(4, '0');
    return 'bd_${timestamp}_$random';
  }

  /// 解析日期時間，支援多種格式
  static DateTime? _parseDateTime(String dateTimeStr) {
    if (dateTimeStr.isEmpty) return null;

    // 支援的日期格式列表
    final List<DateFormat> formats = [
      // ISO 8601 格式
      DateFormat('yyyy-MM-dd HH:mm:ss'),
      DateFormat('yyyy-MM-dd HH:mm'),
      DateFormat('yyyy-MM-dd H:mm'), // 單位數小時
      DateFormat('yyyy-MM-dd'),

      // 斜線分隔格式
      DateFormat('yyyy/MM/dd HH:mm:ss'),
      DateFormat('yyyy/MM/dd HH:mm'),
      DateFormat('yyyy/MM/dd H:mm'), // 單位數小時
      DateFormat('yyyy/MM/dd'),

      // 其他常見格式
      DateFormat('dd/MM/yyyy HH:mm'),
      DateFormat('dd/MM/yyyy H:mm'),
      DateFormat('dd/MM/yyyy'),
      DateFormat('MM/dd/yyyy HH:mm'),
      DateFormat('MM/dd/yyyy H:mm'),
      DateFormat('MM/dd/yyyy'),

      // 中文格式
      DateFormat('yyyy年MM月dd日 HH:mm'),
      DateFormat('yyyy年MM月dd日 H:mm'),
      DateFormat('yyyy年MM月dd日'),
    ];

    // 嘗試使用 DateTime.parse (支援 ISO 8601)
    try {
      return DateTime.parse(dateTimeStr);
    } catch (_) {
      // 如果 DateTime.parse 失敗，嘗試其他格式
    }

    // 嘗試各種格式
    for (final format in formats) {
      try {
        final parsed = format.parseStrict(dateTimeStr);
        logger.d('使用格式 ${format.pattern} 解析成功: $dateTimeStr -> $parsed');
        return parsed;
      } catch (_) {
        // 繼續嘗試下一個格式
      }
    }

    // 如果所有格式都失敗，嘗試手動解析
    try {
      return _manualParseDateTime(dateTimeStr);
    } catch (e) {
      logger.e('所有日期格式解析都失敗: $dateTimeStr, 錯誤: $e');
      return null;
    }
  }

  /// 手動解析日期時間（處理特殊情況）
  static DateTime? _manualParseDateTime(String dateTimeStr) {
    // 移除多餘的空格
    dateTimeStr = dateTimeStr.trim();

    // 處理 "2007-03-05 6:50" 這種格式（單位數小時沒有前導零）
    final RegExp dateTimeRegex = RegExp(r'^(\d{4})-(\d{1,2})-(\d{1,2})\s+(\d{1,2}):(\d{1,2})(?::(\d{1,2}))?$');
    final match = dateTimeRegex.firstMatch(dateTimeStr);

    if (match != null) {
      final year = int.parse(match.group(1)!);
      final month = int.parse(match.group(2)!);
      final day = int.parse(match.group(3)!);
      final hour = int.parse(match.group(4)!);
      final minute = int.parse(match.group(5)!);
      final second = match.group(6) != null ? int.parse(match.group(6)!) : 0;

      return DateTime(year, month, day, hour, minute, second);
    }

    // 處理只有日期的情況 "2007-03-05"
    final RegExp dateRegex = RegExp(r'^(\d{4})-(\d{1,2})-(\d{1,2})$');
    final dateMatch = dateRegex.firstMatch(dateTimeStr);

    if (dateMatch != null) {
      final year = int.parse(dateMatch.group(1)!);
      final month = int.parse(dateMatch.group(2)!);
      final day = int.parse(dateMatch.group(3)!);

      return DateTime(year, month, day);
    }

    return null;
  }

  // 修正錯位的資料，主要是將帶逗號的欄位合併
  static List<String> _fixRow(String rowStr) {
    List<String> columns = [];
    bool insideQuotes = false;
    StringBuffer currentColumn = StringBuffer();

    for (int i = 0; i < rowStr.length; i++) {
      String char = rowStr[i];

      if (char == ',' && !insideQuotes) {
        columns.add(currentColumn.toString().trim());
        currentColumn.clear();
      } else if (char == '"') {
        insideQuotes = !insideQuotes; // 進入或退出引號內部
      } else {
        currentColumn.write(char);
      }
    }
    // 最後一個欄位
    columns.add(currentColumn.toString().trim());

    return columns;
  }

  /// 分享 CSV 文件
  ///
  /// [filePath] - CSV 文件路徑
  static Future<void> shareCsvFile(String filePath) async {
    try {
      await Share.shareXFiles([XFile(filePath)], text: '出生資料 CSV 文件');
      logger.i('已分享 CSV 文件: $filePath');
    } catch (e) {
      logger.e('分享 CSV 文件時出錯: $e');
      rethrow;
    }
  }

  /// 解析單行 CSV 數據
  ///
  /// 專門處理格式為：姓名,生日,出生地緯度,出生地經度,出生地,居住地緯度,居住地經度,標籤,是否隱藏
  /// 的 CSV 數據
  static Future<List<BirthData>> parseCSV(List<List<dynamic>> csvData) async {
    final List<BirthData> birthDataList = [];

    try {
      if (csvData.isEmpty) {
        logger.w('CSV 文件為空');
        return [];
      }

      final originalHeaders = csvData[0].map((e) => e.toString()).toList();
      logger.d('原始 CSV 標題: $originalHeaders');

      // 標題映射：原始標題 -> 標準化標題
      final headers = List.generate(
        originalHeaders.length,
            (i) => _alternativeHeaders[originalHeaders[i]] ?? originalHeaders[i],
      );

      for (int rowIndex = 1; rowIndex < csvData.length; rowIndex++) {
        final row = csvData[rowIndex];
        if (row.length != headers.length) {
          logger.w('第 $rowIndex 行的欄位數與標題不一致，略過。');
          continue;
        }

        final Map<String, String> standardDataMap = {};
        for (int i = 0; i < headers.length; i++) {
          standardDataMap[headers[i]] = row[i]?.toString() ?? '';
        }

        // 特殊處理：補足常見欄位
        if (!standardDataMap.containsKey('birthPlace') &&
            standardDataMap.containsKey('出生地')) {
          standardDataMap['birthPlace'] = standardDataMap['出生地']!;
        }

        if (!standardDataMap.containsKey('name') &&
            standardDataMap.containsKey('姓名')) {
          standardDataMap['name'] = standardDataMap['姓名']!;
        }

        if (!standardDataMap.containsKey('birthDate') &&
            standardDataMap.containsKey('生日')) {
          standardDataMap['birthDate'] = standardDataMap['生日']!;
        }

        // 如果沒有名字或出生地，這筆就真的不成立了
        if (!standardDataMap.containsKey('name') ||
            !standardDataMap.containsKey('birthPlace')) {
          logger.w('第 $rowIndex 筆缺少必要欄位 name 或 birthPlace，略過。');
          continue;
        }

        // 經緯度處理
        double? latitude;
        double? longitude;

        latitude = double.tryParse(standardDataMap['latitude'] ?? '');
        longitude = double.tryParse(standardDataMap['longitude'] ?? '');

        if ((latitude == null || longitude == null) ||
            latitude == -1.0 || longitude == -1.0) {
          if (standardDataMap.containsKey('出生地緯度') &&
              standardDataMap.containsKey('出生地經度')) {
            latitude = double.tryParse(standardDataMap['出生地緯度'] ?? '');
            longitude = double.tryParse(standardDataMap['出生地經度'] ?? '');
          }
        }

        if ((latitude == null || longitude == null) ||
            latitude == -1.0 || longitude == -1.0) {
          try {
            final coordinates =
            await GeocodingService.getCoordinatesFromAddress(
                standardDataMap['birthPlace']!);
            if (coordinates.isNotEmpty &&
                coordinates['latitude'] != null &&
                coordinates['longitude'] != null) {
              latitude = coordinates['latitude'];
              longitude = coordinates['longitude'];
              logger.d(
                  '從地址取得經緯度成功: ${standardDataMap['birthPlace']} -> $latitude, $longitude');
            } else {
              logger.w('無法從地址取得經緯度，使用預設: ${standardDataMap['birthPlace']}');
              latitude = 0.0;
              longitude = 0.0;
            }
          } catch (e) {
            logger.e('第 $rowIndex 筆地點解析錯誤: $e');
            latitude = 0.0;
            longitude = 0.0;
          }
        }

        // 解析生日
        DateTime birthDate;
        String? birthDateStr = standardDataMap['birthDate'];

        if (birthDateStr == null || birthDateStr.trim().isEmpty) {
          logger.w('第 $rowIndex 筆缺生日，使用預設日期');
          birthDate = DateTime(1900, 1, 1);
          standardDataMap['notes'] = '生日待補';
        } else {
          final parsedDate = _parseDateTime(birthDateStr);
          if (parsedDate != null) {
            birthDate = parsedDate;
          } else {
            logger.e('第 $rowIndex 筆生日解析錯誤: $birthDateStr');
            birthDate = DateTime(1900, 1, 1);
            standardDataMap['notes'] = '生日格式錯誤';
          }
        }

        // 處理 ID 唯一性
        String id = standardDataMap['id'] ?? '';
        if (id.isEmpty) {
          id = _generateUniqueId();
        }
        // 解析最後訪問時間
        DateTime? lastAccessedAt;
        String? lastAccessedStr = standardDataMap['lastAccessedAt'];
        if (lastAccessedStr != null && lastAccessedStr.isNotEmpty) {
          lastAccessedAt = _parseDateTime(lastAccessedStr);
          if (lastAccessedAt == null) {
            logger.w('解析最後訪問時間失敗: $lastAccessedStr');
          }
        }

        // 解析創建時間
        DateTime createdAt = DateTime.now();
        String? createdAtStr = standardDataMap['createdAt'];
        if (createdAtStr != null && createdAtStr.isNotEmpty) {
          final parsedCreatedAt = _parseDateTime(createdAtStr);
          if (parsedCreatedAt != null) {
            createdAt = parsedCreatedAt;
          } else {
            logger.w('解析創建時間失敗，使用當前時間: $createdAtStr');
          }
        }

        final birthData = BirthData(
          id: id,
          name: standardDataMap['name']!,
          dateTime: birthDate,
          birthPlace: standardDataMap['birthPlace']!,
          latitude: latitude!,
          longitude: longitude!,
          notes: [
            standardDataMap['notes']
          ].whereType<String>().where((s) => s.trim().isNotEmpty).join(' / '),
          category: ChartCategory.fromString(standardDataMap['category'] ?? '個人'),
          lastAccessedAt: lastAccessedAt,
          createdAt: createdAt,
        );

        logger.d(
            '匯入成功: ${birthData.name}, ${birthData.dateTime}, ${birthData.birthPlace}');
        birthDataList.add(birthData);
      }

      return birthDataList;
    } catch (e) {
      logger.e('整體 CSV 解析失敗: $e');
      return [];
    }
  }

  /// 解析日期字串為 DateTime
  static DateTime? _parseDate(String? dateStr) {
    if (dateStr == null || dateStr.isEmpty) return null;

    try {
      // 使用現有的 _parseDateTime 方法
      return _parseDateTime(dateStr);
    } catch (e) {
      logger.w('解析日期失敗: $dateStr, 錯誤: $e');
      return null;
    }
  }

  /// 解析字串為 double
  static double? _parseDouble(String? str) {
    if (str == null || str.isEmpty) return null;

    try {
      return double.parse(str);
    } catch (e) {
      logger.w('解析數字失敗: $str, 錯誤: $e');
      return null;
    }
  }

  /// 解析字串為 boolean
  static bool _parseBoolean(String? str) {
    if (str == null || str.isEmpty) return false;

    final lowerStr = str.toLowerCase().trim();
    return lowerStr == 'true' ||
           lowerStr == '是' ||
           lowerStr == 'yes' ||
           lowerStr == '1' ||
           lowerStr == 'y';
  }
}
