import '../../core/utils/logger_utils.dart';
import '../../data/services/api/remote_config_service.dart';

/// Remote Config 診斷工具
class RemoteConfigDiagnostic {
  
  /// 執行完整的 Remote Config 診斷
  static Future<Map<String, dynamic>> runFullDiagnostic() async {
    final result = <String, dynamic>{
      'timestamp': DateTime.now().toIso8601String(),
    };
    
    logger.i('開始 Remote Config 診斷...');
    
    // 1. 檢查服務狀態
    result['serviceStatus'] = _checkServiceStatus();
    
    // 2. 檢查 AI API Keys 配置
    result['apiKeysConfig'] = _checkApiKeysConfig();
    
    // 3. 檢查各平台的 API Keys
    result['platformKeys'] = _checkPlatformKeys();
    
    // 4. 測試配置刷新
    result['refreshTest'] = await _testConfigRefresh();
    
    // 5. 生成建議
    result['recommendations'] = _generateRecommendations(result);
    
    logger.i('Remote Config 診斷完成');
    return result;
  }
  
  /// 檢查服務狀態
  static Map<String, dynamic> _checkServiceStatus() {
    final status = <String, dynamic>{};
    
    try {
      final serviceStatus = RemoteConfigService.getStatus();
      status.addAll(serviceStatus);
      
      if (serviceStatus['isInitialized'] == true) {
        status['status'] = 'initialized';
        status['message'] = 'Remote Config 服務已正確初始化';
      } else {
        status['status'] = 'not_initialized';
        status['message'] = 'Remote Config 服務未初始化';
      }
      
    } catch (e) {
      status['status'] = 'error';
      status['error'] = e.toString();
      status['message'] = 'Remote Config 服務狀態檢查失敗';
    }
    
    return status;
  }
  
  /// 檢查 AI API Keys 配置
  static Map<String, dynamic> _checkApiKeysConfig() {
    final config = <String, dynamic>{};
    
    try {
      final apiKeys = RemoteConfigService.getAIApiKeys();
      config['rawConfig'] = apiKeys;
      config['hasConfig'] = apiKeys.isNotEmpty;
      
      // 檢查配置結構
      final expectedKeys = ['OpenAIKey', 'GroqAIKey', 'GoogleGeminiKey'];
      final expectedPlatforms = ['ios', 'android', 'web', 'macos', 'windows'];
      
      config['structureCheck'] = <String, dynamic>{};
      
      for (final key in expectedKeys) {
        final keyConfig = apiKeys[key] as Map<String, dynamic>?;
        config['structureCheck'][key] = {
          'exists': keyConfig != null,
          'platforms': <String, dynamic>{},
        };
        
        if (keyConfig != null) {
          for (final platform in expectedPlatforms) {
            final platformKey = keyConfig[platform] as String?;
            config['structureCheck'][key]['platforms'][platform] = {
              'exists': platformKey != null,
              'hasValue': platformKey?.isNotEmpty == true,
              'length': platformKey?.length ?? 0,
            };
          }
        }
      }
      
      config['status'] = 'success';
      
    } catch (e) {
      config['status'] = 'error';
      config['error'] = e.toString();
    }
    
    return config;
  }
  
  /// 檢查各平台的 API Keys
  static Map<String, dynamic> _checkPlatformKeys() {
    final keys = <String, dynamic>{};
    
    try {
      // 獲取當前平台的 API Keys
      final allKeys = RemoteConfigService.getAllApiKeys();
      keys['currentPlatform'] = allKeys;
      
      // 檢查每個 API Key 的有效性
      keys['validation'] = <String, dynamic>{};
      
      for (final entry in allKeys.entries) {
        final keyName = entry.key;
        final keyValue = entry.value;
        
        keys['validation'][keyName] = {
          'hasValue': keyValue.isNotEmpty,
          'length': keyValue.length,
          'format': _validateKeyFormat(keyName, keyValue),
        };
      }
      
      keys['status'] = 'success';
      
    } catch (e) {
      keys['status'] = 'error';
      keys['error'] = e.toString();
    }
    
    return keys;
  }
  
  /// 驗證 API Key 格式
  static Map<String, dynamic> _validateKeyFormat(String keyType, String keyValue) {
    final validation = <String, dynamic>{
      'isValid': false,
      'reason': '',
    };
    
    if (keyValue.isEmpty) {
      validation['reason'] = 'Key 為空';
      return validation;
    }
    
    switch (keyType) {
      case 'openai':
        if (keyValue.startsWith('sk-proj-') || keyValue.startsWith('sk-')) {
          validation['isValid'] = true;
          validation['reason'] = 'OpenAI Key 格式正確';
        } else {
          validation['reason'] = 'OpenAI Key 應該以 sk- 或 sk-proj- 開頭';
        }
        break;
        
      case 'groq':
        if (keyValue.startsWith('gsk_')) {
          validation['isValid'] = true;
          validation['reason'] = 'Groq AI Key 格式正確';
        } else {
          validation['reason'] = 'Groq AI Key 應該以 gsk_ 開頭';
        }
        break;
        
      case 'gemini':
        if (keyValue.length >= 30) {
          validation['isValid'] = true;
          validation['reason'] = 'Google Gemini Key 格式可能正確';
        } else {
          validation['reason'] = 'Google Gemini Key 長度可能不足';
        }
        break;
        
      default:
        validation['reason'] = '未知的 Key 類型';
    }
    
    return validation;
  }
  
  /// 測試配置刷新
  static Future<Map<String, dynamic>> _testConfigRefresh() async {
    final test = <String, dynamic>{};
    
    try {
      logger.i('測試 Remote Config 刷新...');
      
      final refreshResult = await RemoteConfigService.refresh();
      
      test['refreshSuccess'] = refreshResult;
      test['status'] = refreshResult ? 'success' : 'no_update';
      test['message'] = refreshResult 
          ? '配置刷新成功，有新的配置更新'
          : '配置刷新完成，沒有新的更新';
      
    } catch (e) {
      test['refreshSuccess'] = false;
      test['status'] = 'error';
      test['error'] = e.toString();
      test['message'] = '配置刷新測試失敗';
    }
    
    return test;
  }
  
  /// 生成建議
  static List<String> _generateRecommendations(Map<String, dynamic> diagnostic) {
    final recommendations = <String>[];
    
    // 檢查服務狀態
    final serviceStatus = diagnostic['serviceStatus'] as Map<String, dynamic>?;
    if (serviceStatus?['status'] != 'initialized') {
      recommendations.addAll([
        '確保 Firebase 已正確初始化',
        '檢查 Firebase Remote Config 是否已在 Firebase 控制台中啟用',
        '確認應用有網路連接',
      ]);
    }
    
    // 檢查 API Keys 配置
    final apiKeysConfig = diagnostic['apiKeysConfig'] as Map<String, dynamic>?;
    if (apiKeysConfig?['hasConfig'] != true) {
      recommendations.addAll([
        '在 Firebase 控制台中設定 ai_api_keys 參數',
        '確保 JSON 格式正確',
        '發布配置更改',
      ]);
    }
    
    // 檢查平台 Keys
    final platformKeys = diagnostic['platformKeys'] as Map<String, dynamic>?;
    if (platformKeys?['status'] == 'success') {
      final validation = platformKeys?['validation'] as Map<String, dynamic>?;
      if (validation != null) {
        for (final entry in validation.entries) {
          final keyName = entry.key;
          final keyValidation = entry.value as Map<String, dynamic>;
          
          if (keyValidation['hasValue'] != true) {
            recommendations.add('設定 $keyName API Key');
          } else {
            final format = keyValidation['format'] as Map<String, dynamic>?;
            if (format?['isValid'] != true) {
              recommendations.add('檢查 $keyName API Key 格式：${format?['reason']}');
            }
          }
        }
      }
    }
    
    // 通用建議
    if (recommendations.isEmpty) {
      recommendations.addAll([
        'Remote Config 配置看起來正常',
        '定期檢查 API Keys 的有效性',
        '監控 API 使用量和配額',
      ]);
    } else {
      recommendations.addAll([
        '在 Firebase 控制台中更新配置後，記得發布更改',
        '可以使用手動刷新功能立即獲取最新配置',
      ]);
    }
    
    return recommendations;
  }
  
  /// 打印診斷結果
  static void printDiagnostic(Map<String, dynamic> diagnostic) {
    logger.i('=== Remote Config 診斷結果 ===');
    logger.i('時間: ${diagnostic['timestamp']}');
    
    // 服務狀態
    final serviceStatus = diagnostic['serviceStatus'] as Map<String, dynamic>?;
    if (serviceStatus != null) {
      logger.i('--- 服務狀態 ---');
      logger.i('狀態: ${serviceStatus['status']}');
      logger.i('平台: ${serviceStatus['platform']}');
      logger.i('已初始化: ${serviceStatus['isInitialized']}');
      if (serviceStatus['lastFetchTime'] != null) {
        logger.i('最後獲取時間: ${serviceStatus['lastFetchTime']}');
      }
    }
    
    // API Keys 狀態
    final platformKeys = diagnostic['platformKeys'] as Map<String, dynamic>?;
    if (platformKeys != null && platformKeys['status'] == 'success') {
      logger.i('--- API Keys 狀態 ---');
      final currentPlatform = platformKeys['currentPlatform'] as Map<String, dynamic>?;
      if (currentPlatform != null) {
        for (final entry in currentPlatform.entries) {
          final keyName = entry.key;
          final keyValue = entry.value as String;
          final hasValue = keyValue.isNotEmpty;
          logger.i('$keyName: ${hasValue ? '已設定' : '未設定'}');
        }
      }
    }
    
    // 建議
    final recommendations = diagnostic['recommendations'] as List<String>?;
    if (recommendations != null && recommendations.isNotEmpty) {
      logger.i('--- 建議 ---');
      for (int i = 0; i < recommendations.length; i++) {
        logger.i('${i + 1}. ${recommendations[i]}');
      }
    }
    
    logger.i('=== 診斷結束 ===');
  }
  
  /// 獲取配置摘要（用於 UI 顯示）
  static Map<String, dynamic> getConfigSummary() {
    try {
      final status = RemoteConfigService.getStatus();
      final allKeys = RemoteConfigService.getAllApiKeys();
      
      return {
        'isInitialized': status['isInitialized'],
        'platform': status['platform'],
        'apiKeys': {
          'openai': allKeys['openai']?.isNotEmpty == true,
          'groq': allKeys['groq']?.isNotEmpty == true,
          'gemini': allKeys['gemini']?.isNotEmpty == true,
        },
        'lastFetchTime': status['lastFetchTime'],
      };
    } catch (e) {
      return {
        'isInitialized': false,
        'error': e.toString(),
      };
    }
  }
}
