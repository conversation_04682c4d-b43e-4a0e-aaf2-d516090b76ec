import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../core/utils/logger_utils.dart';
import 'enhanced_timezone_service.dart';

/// 時區查詢監控服務
/// 
/// 用於監控時區查詢的成功率和失敗情況，幫助識別問題地點
class TimezoneMonitoringService {
  static final TimezoneMonitoringService _instance = TimezoneMonitoringService._internal();
  factory TimezoneMonitoringService() => _instance;
  TimezoneMonitoringService._internal();

  final EnhancedTimezoneService _timezoneService = EnhancedTimezoneService();
  
  // 查詢統計
  int _totalQueries = 0;
  int _successfulQueries = 0;
  int _failedQueries = 0;
  
  // 失敗記錄
  final List<TimezoneQueryFailure> _failures = [];
  
  // 成功記錄（用於分析模式）
  final List<TimezoneQuerySuccess> _successes = [];
  
  /// 執行時區查詢並記錄結果
  Future<String?> queryTimezoneWithMonitoring({
    required double latitude,
    required double longitude,
    String? locationName,
    String? context,
  }) async {
    _totalQueries++;
    
    final startTime = DateTime.now();
    
    try {
      logger.d('開始時區查詢監控: lat=$latitude, lng=$longitude, location=$locationName');
      
      final result = await _timezoneService.getTimeZoneFromLatLng(latitude, longitude);
      final duration = DateTime.now().difference(startTime);
      
      if (result != null) {
        _successfulQueries++;
        
        final success = TimezoneQuerySuccess(
          latitude: latitude,
          longitude: longitude,
          locationName: locationName,
          context: context,
          timezone: result,
          queryTime: startTime,
          duration: duration,
        );
        
        _successes.add(success);
        
        logger.i('時區查詢成功: $locationName ($latitude, $longitude) -> $result');
        
        // 在開發模式下記錄詳細信息
        if (kDebugMode) {
          logger.d('查詢詳情: 耗時${duration.inMilliseconds}ms, 上下文: $context');
        }
        
      } else {
        _failedQueries++;
        
        final failure = TimezoneQueryFailure(
          latitude: latitude,
          longitude: longitude,
          locationName: locationName,
          context: context,
          queryTime: startTime,
          duration: duration,
          errorMessage: '未找到對應時區',
        );
        
        _failures.add(failure);
        
        logger.w('時區查詢失敗: $locationName ($latitude, $longitude) - 未找到對應時區');
        
        // 嘗試備用查詢
        if (locationName != null) {
          final fallbackResult = _timezoneService.getFallbackTimezone(locationName);
          if (fallbackResult != null) {
            logger.i('使用備用時區: $locationName -> $fallbackResult');
            return fallbackResult;
          }
        }
      }
      
      return result;
      
    } catch (e, stackTrace) {
      _failedQueries++;
      final duration = DateTime.now().difference(startTime);
      
      final failure = TimezoneQueryFailure(
        latitude: latitude,
        longitude: longitude,
        locationName: locationName,
        context: context,
        queryTime: startTime,
        duration: duration,
        errorMessage: e.toString(),
        stackTrace: stackTrace.toString(),
      );
      
      _failures.add(failure);
      
      logger.e('時區查詢異常: $locationName ($latitude, $longitude) - $e');
      
      return null;
    }
  }
  
  /// 獲取查詢統計信息
  TimezoneQueryStats getStats() {
    return TimezoneQueryStats(
      totalQueries: _totalQueries,
      successfulQueries: _successfulQueries,
      failedQueries: _failedQueries,
      successRate: _totalQueries > 0 ? _successfulQueries / _totalQueries : 0.0,
      failures: List.unmodifiable(_failures),
      successes: List.unmodifiable(_successes),
    );
  }
  
  /// 獲取最近的失敗記錄
  List<TimezoneQueryFailure> getRecentFailures({int limit = 10}) {
    final sortedFailures = List<TimezoneQueryFailure>.from(_failures)
      ..sort((a, b) => b.queryTime.compareTo(a.queryTime));
    
    return sortedFailures.take(limit).toList();
  }
  
  /// 獲取失敗熱點（最常失敗的地區）
  Map<String, int> getFailureHotspots() {
    final hotspots = <String, int>{};
    
    for (final failure in _failures) {
      final key = failure.locationName ?? '${failure.latitude.toStringAsFixed(2)},${failure.longitude.toStringAsFixed(2)}';
      hotspots[key] = (hotspots[key] ?? 0) + 1;
    }
    
    // 按失敗次數排序
    final sortedEntries = hotspots.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    return Map.fromEntries(sortedEntries);
  }
  
  /// 生成監控報告
  String generateReport() {
    final stats = getStats();
    final hotspots = getFailureHotspots();
    final recentFailures = getRecentFailures();
    
    final buffer = StringBuffer();
    
    buffer.writeln('🌍 時區查詢監控報告');
    buffer.writeln('=' * 50);
    buffer.writeln();
    
    // 統計概覽
    buffer.writeln('📊 查詢統計:');
    buffer.writeln('  總查詢次數: ${stats.totalQueries}');
    buffer.writeln('  成功次數: ${stats.successfulQueries}');
    buffer.writeln('  失敗次數: ${stats.failedQueries}');
    buffer.writeln('  成功率: ${(stats.successRate * 100).toStringAsFixed(1)}%');
    buffer.writeln();
    
    // 失敗熱點
    if (hotspots.isNotEmpty) {
      buffer.writeln('🔥 失敗熱點 (前10名):');
      var count = 0;
      for (final entry in hotspots.entries) {
        if (count >= 10) break;
        buffer.writeln('  ${count + 1}. ${entry.key}: ${entry.value} 次失敗');
        count++;
      }
      buffer.writeln();
    }
    
    // 最近失敗記錄
    if (recentFailures.isNotEmpty) {
      buffer.writeln('⚠️ 最近失敗記錄:');
      for (final failure in recentFailures.take(5)) {
        buffer.writeln('  • ${failure.locationName ?? '未知地點'} (${failure.latitude}, ${failure.longitude})');
        buffer.writeln('    時間: ${failure.queryTime.toString().substring(0, 19)}');
        buffer.writeln('    錯誤: ${failure.errorMessage}');
        buffer.writeln();
      }
    }
    
    // 建議
    buffer.writeln('💡 改善建議:');
    if (stats.successRate < 0.9) {
      buffer.writeln('  • 成功率較低，建議更新時區資料庫');
    }
    if (hotspots.isNotEmpty) {
      buffer.writeln('  • 針對失敗熱點地區進行特別處理');
    }
    if (stats.failedQueries > 0) {
      buffer.writeln('  • 考慮擴展備用時區對應表');
    }
    
    return buffer.toString();
  }
  
  /// 保存監控數據到本地
  Future<void> saveMonitoringData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final stats = getStats();
      
      final data = {
        'totalQueries': stats.totalQueries,
        'successfulQueries': stats.successfulQueries,
        'failedQueries': stats.failedQueries,
        'failures': _failures.map((f) => f.toJson()).toList(),
        'lastUpdated': DateTime.now().toIso8601String(),
      };
      
      await prefs.setString('timezone_monitoring_data', jsonEncode(data));
      logger.d('監控數據已保存');
      
    } catch (e) {
      logger.e('保存監控數據失敗: $e');
    }
  }
  
  /// 從本地載入監控數據
  Future<void> loadMonitoringData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final dataString = prefs.getString('timezone_monitoring_data');
      
      if (dataString != null) {
        final data = jsonDecode(dataString) as Map<String, dynamic>;
        
        _totalQueries = data['totalQueries'] ?? 0;
        _successfulQueries = data['successfulQueries'] ?? 0;
        _failedQueries = data['failedQueries'] ?? 0;
        
        _failures.clear();
        final failuresData = data['failures'] as List<dynamic>? ?? [];
        for (final failureData in failuresData) {
          _failures.add(TimezoneQueryFailure.fromJson(failureData));
        }
        
        logger.d('監控數據已載入: 總查詢${_totalQueries}次, 成功率${(_successfulQueries/_totalQueries*100).toStringAsFixed(1)}%');
      }
      
    } catch (e) {
      logger.e('載入監控數據失敗: $e');
    }
  }
  
  /// 清除監控數據
  Future<void> clearMonitoringData() async {
    _totalQueries = 0;
    _successfulQueries = 0;
    _failedQueries = 0;
    _failures.clear();
    _successes.clear();
    
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('timezone_monitoring_data');
      logger.i('監控數據已清除');
    } catch (e) {
      logger.e('清除監控數據失敗: $e');
    }
  }
}

/// 時區查詢統計信息
class TimezoneQueryStats {
  final int totalQueries;
  final int successfulQueries;
  final int failedQueries;
  final double successRate;
  final List<TimezoneQueryFailure> failures;
  final List<TimezoneQuerySuccess> successes;

  const TimezoneQueryStats({
    required this.totalQueries,
    required this.successfulQueries,
    required this.failedQueries,
    required this.successRate,
    required this.failures,
    required this.successes,
  });
}

/// 時區查詢失敗記錄
class TimezoneQueryFailure {
  final double latitude;
  final double longitude;
  final String? locationName;
  final String? context;
  final DateTime queryTime;
  final Duration duration;
  final String errorMessage;
  final String? stackTrace;

  const TimezoneQueryFailure({
    required this.latitude,
    required this.longitude,
    this.locationName,
    this.context,
    required this.queryTime,
    required this.duration,
    required this.errorMessage,
    this.stackTrace,
  });

  Map<String, dynamic> toJson() {
    return {
      'latitude': latitude,
      'longitude': longitude,
      'locationName': locationName,
      'context': context,
      'queryTime': queryTime.toIso8601String(),
      'duration': duration.inMilliseconds,
      'errorMessage': errorMessage,
      'stackTrace': stackTrace,
    };
  }

  factory TimezoneQueryFailure.fromJson(Map<String, dynamic> json) {
    return TimezoneQueryFailure(
      latitude: json['latitude'],
      longitude: json['longitude'],
      locationName: json['locationName'],
      context: json['context'],
      queryTime: DateTime.parse(json['queryTime']),
      duration: Duration(milliseconds: json['duration']),
      errorMessage: json['errorMessage'],
      stackTrace: json['stackTrace'],
    );
  }
}

/// 時區查詢成功記錄
class TimezoneQuerySuccess {
  final double latitude;
  final double longitude;
  final String? locationName;
  final String? context;
  final String timezone;
  final DateTime queryTime;
  final Duration duration;

  const TimezoneQuerySuccess({
    required this.latitude,
    required this.longitude,
    this.locationName,
    this.context,
    required this.timezone,
    required this.queryTime,
    required this.duration,
  });
}
