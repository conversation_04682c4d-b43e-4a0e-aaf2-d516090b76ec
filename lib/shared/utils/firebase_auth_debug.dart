import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';

import '../../core/utils/logger_utils.dart';

/// Firebase 認證調試工具
class FirebaseAuthDebug {
  
  /// 檢查 Google Sign-In 環境
  static Future<Map<String, dynamic>> checkGoogleSignInEnvironment() async {
    final result = <String, dynamic>{};
    
    try {
      logger.i('檢查 Google Sign-In 環境...');
      
      // 平台信息
      result['platform'] = kIsWeb ? 'web' : Platform.operatingSystem;
      result['isWeb'] = kIsWeb;
      
      // Google Sign-In 實例
      final googleSignIn = GoogleSignIn(
        scopes: [
          'email',
          'https://www.googleapis.com/auth/userinfo.profile',
          'openid', // 👈 重點是這個！OpenID scope 會回傳 idToken
        ],
      );
      
      // 檢查是否已登入
      try {
        final isSignedIn = await googleSignIn.isSignedIn();
        result['isSignedIn'] = isSignedIn;
        logger.i('Google Sign-In 狀態: $isSignedIn');
        
        if (isSignedIn) {
          final currentUser = googleSignIn.currentUser;
          result['currentUser'] = {
            'email': currentUser?.email,
            'displayName': currentUser?.displayName,
            'id': currentUser?.id,
          };
        }
      } catch (e) {
        result['signInCheckError'] = e.toString();
        logger.e('檢查 Google Sign-In 狀態失敗: $e');
      }
      
      // 嘗試靜默登入
      try {
        logger.i('嘗試 Google 靜默登入...');
        final user = await googleSignIn.signInSilently();
        result['silentSignInResult'] = user != null ? 'success' : 'no_user';
        if (user != null) {
          result['silentSignInUser'] = {
            'email': user.email,
            'displayName': user.displayName,
          };
        }
      } catch (e) {
        result['silentSignInError'] = e.toString();
        logger.e('Google 靜默登入失敗: $e');
      }
      
    } catch (e) {
      result['generalError'] = e.toString();
      logger.e('Google Sign-In 環境檢查失敗: $e');
    }
    
    return result;
  }
  
  /// 檢查 Apple Sign-In 環境
  static Future<Map<String, dynamic>> checkAppleSignInEnvironment() async {
    final result = <String, dynamic>{};
    
    try {
      logger.i('檢查 Apple Sign-In 環境...');
      
      // 平台信息
      result['platform'] = kIsWeb ? 'web' : Platform.operatingSystem;
      result['isWeb'] = kIsWeb;
      result['isIOS'] = !kIsWeb && Platform.isIOS;
      result['isMacOS'] = !kIsWeb && Platform.isMacOS;
      
      // 檢查平台支援
      if (kIsWeb || (!Platform.isIOS && !Platform.isMacOS)) {
        result['supported'] = false;
        result['reason'] = 'Apple Sign-In 僅支援 iOS 和 macOS';
        return result;
      }
      
      result['supported'] = true;
      
      // 檢查可用性
      try {
        final isAvailable = await SignInWithApple.isAvailable();
        result['isAvailable'] = isAvailable;
        logger.i('Apple Sign-In 可用性: $isAvailable');
        
        if (!isAvailable) {
          result['reason'] = 'Apple Sign-In 在此設備上不可用';
        }
      } catch (e) {
        result['availabilityCheckError'] = e.toString();
        logger.e('檢查 Apple Sign-In 可用性失敗: $e');
      }
      
    } catch (e) {
      result['generalError'] = e.toString();
      logger.e('Apple Sign-In 環境檢查失敗: $e');
    }
    
    return result;
  }
  
  /// 安全的 Google 登入測試
  static Future<Map<String, dynamic>> safeGoogleSignInTest() async {
    final result = <String, dynamic>{};
    
    try {
      logger.i('開始安全的 Google 登入測試...');
      
      final googleSignIn = GoogleSignIn(
        scopes: [
          'email',
          'https://www.googleapis.com/auth/userinfo.profile',
          'openid', // 👈 重點是這個！OpenID scope 會回傳 idToken
        ],
      );
      
      // 先登出確保乾淨狀態
      try {
        await googleSignIn.signOut();
        result['signOutSuccess'] = true;
      } catch (e) {
        result['signOutError'] = e.toString();
      }
      
      // 嘗試登入
      try {
        logger.i('嘗試 Google 登入...');
        final user = await googleSignIn.signIn();
        
        if (user == null) {
          result['result'] = 'canceled';
          result['message'] = '用戶取消登入';
        } else {
          result['result'] = 'success';
          result['user'] = {
            'email': user.email,
            'displayName': user.displayName,
            'id': user.id,
            'photoUrl': user.photoUrl,
          };
          
          // 獲取認證信息
          try {
            final auth = await user.authentication;
            result['authentication'] = {
              'hasIdToken': auth.idToken != null,
              'hasAccessToken': auth.accessToken != null,
              'idTokenLength': auth.idToken?.length ?? 0,
              'accessTokenLength': auth.accessToken?.length ?? 0,
            };
          } catch (e) {
            result['authenticationError'] = e.toString();
          }
        }
      } catch (e) {
        result['result'] = 'error';
        result['error'] = e.toString();
        result['errorType'] = e.runtimeType.toString();
        logger.e('Google 登入測試失敗: $e');
      }
      
    } catch (e) {
      result['generalError'] = e.toString();
      logger.e('Google 登入測試失敗: $e');
    }
    
    return result;
  }
  
  /// 安全的 Apple 登入測試
  static Future<Map<String, dynamic>> safeAppleSignInTest() async {
    final result = <String, dynamic>{};
    
    try {
      logger.i('開始安全的 Apple 登入測試...');
      
      // 檢查平台支援
      if (kIsWeb || (!Platform.isIOS && !Platform.isMacOS)) {
        result['result'] = 'unsupported';
        result['message'] = 'Apple Sign-In 僅支援 iOS 和 macOS';
        return result;
      }
      
      // 檢查可用性
      final isAvailable = await SignInWithApple.isAvailable();
      if (!isAvailable) {
        result['result'] = 'unavailable';
        result['message'] = 'Apple Sign-In 在此設備上不可用';
        return result;
      }
      
      // 嘗試登入
      try {
        logger.i('嘗試 Apple 登入...');
        final credential = await SignInWithApple.getAppleIDCredential(
          scopes: [
            AppleIDAuthorizationScopes.email,
            AppleIDAuthorizationScopes.fullName,
          ],
        );
        
        result['result'] = 'success';
        result['credential'] = {
          'userIdentifier': credential.userIdentifier,
          'email': credential.email,
          'givenName': credential.givenName,
          'familyName': credential.familyName,
          'hasIdentityToken': credential.identityToken != null,
          'identityTokenLength': credential.identityToken?.length ?? 0,
          'authorizationCodeLength': credential.authorizationCode.length,
        };
        
      } catch (e) {
        result['result'] = 'error';
        result['error'] = e.toString();
        result['errorType'] = e.runtimeType.toString();
        
        if (e is SignInWithAppleAuthorizationException) {
          result['appleErrorCode'] = e.code.toString();
          result['appleErrorMessage'] = e.message;
        }
        
        logger.e('Apple 登入測試失敗: $e');
      }
      
    } catch (e) {
      result['generalError'] = e.toString();
      logger.e('Apple 登入測試失敗: $e');
    }
    
    return result;
  }
  
  /// 生成完整的診斷報告
  static Future<Map<String, dynamic>> generateDiagnosticReport() async {
    logger.i('生成 Firebase 認證診斷報告...');
    
    final report = <String, dynamic>{
      'timestamp': DateTime.now().toIso8601String(),
      'platform': kIsWeb ? 'web' : Platform.operatingSystem,
    };
    
    // Google Sign-In 診斷
    try {
      report['googleSignIn'] = await checkGoogleSignInEnvironment();
    } catch (e) {
      report['googleSignInError'] = e.toString();
    }
    
    // Apple Sign-In 診斷
    try {
      report['appleSignIn'] = await checkAppleSignInEnvironment();
    } catch (e) {
      report['appleSignInError'] = e.toString();
    }
    
    logger.i('診斷報告生成完成');
    return report;
  }
  
  /// 打印診斷報告
  static void printDiagnosticReport(Map<String, dynamic> report) {
    logger.i('=== Firebase 認證診斷報告 ===');
    logger.i('時間: ${report['timestamp']}');
    logger.i('平台: ${report['platform']}');
    
    if (report.containsKey('googleSignIn')) {
      final google = report['googleSignIn'] as Map<String, dynamic>;
      logger.i('--- Google Sign-In ---');
      logger.i('已登入: ${google['isSignedIn'] ?? 'unknown'}');
      if (google.containsKey('currentUser')) {
        logger.i('當前用戶: ${google['currentUser']}');
      }
      if (google.containsKey('silentSignInResult')) {
        logger.i('靜默登入: ${google['silentSignInResult']}');
      }
      if (google.containsKey('signInCheckError')) {
        logger.w('檢查錯誤: ${google['signInCheckError']}');
      }
    }
    
    if (report.containsKey('appleSignIn')) {
      final apple = report['appleSignIn'] as Map<String, dynamic>;
      logger.i('--- Apple Sign-In ---');
      logger.i('支援: ${apple['supported'] ?? 'unknown'}');
      logger.i('可用: ${apple['isAvailable'] ?? 'unknown'}');
      if (apple.containsKey('reason')) {
        logger.i('原因: ${apple['reason']}');
      }
    }
    
    logger.i('=== 診斷報告結束 ===');
  }
}
