import '../../data/models/astrology/aspect_info.dart';
import '../../data/models/astrology/planet_position.dart';

/// 相位分配工具，用於將相位信息分配給對應的行星
class AspectDistributor {
  /// 將相位列表分配給行星列表
  /// 
  /// [planets] 行星列表
  /// [aspects] 相位列表
  /// [aspectType] 相位類型：'general'（一般相位）、'natal'（本命相位）、'transit'（行運相位）
  /// 
  /// 返回包含相位信息的新行星列表
  static List<PlanetPosition> distributeAspects(
    List<PlanetPosition> planets,
    List<AspectInfo> aspects, {
    String aspectType = 'general',
  }) {
    // 創建行星ID到行星的映射，方便查找
    final planetMap = <int, PlanetPosition>{};
    for (final planet in planets) {
      planetMap[planet.id] = planet;
    }

    // 為每個行星創建相位列表
    final planetAspects = <int, List<AspectInfo>>{};
    for (final planet in planets) {
      planetAspects[planet.id] = [];
    }

    // 分配相位到對應的行星
    for (final aspect in aspects) {
      final planet1Id = aspect.planet1.id;
      final planet2Id = aspect.planet2.id;

      // 將相位添加到兩個行星的相位列表中
      if (planetAspects.containsKey(planet1Id)) {
        planetAspects[planet1Id]!.add(aspect);
      }
      if (planetAspects.containsKey(planet2Id)) {
        planetAspects[planet2Id]!.add(aspect);
      }
    }

    // 創建包含相位信息的新行星列表
    final updatedPlanets = <PlanetPosition>[];
    for (final planet in planets) {
      final planetAspectList = planetAspects[planet.id] ?? [];
      
      PlanetPosition updatedPlanet;
      switch (aspectType) {
        case 'natal':
          updatedPlanet = planet.copyWith(natalAspects: planetAspectList);
          break;
        case 'transit':
          updatedPlanet = planet.copyWith(transitAspects: planetAspectList);
          break;
        case 'general':
        default:
          updatedPlanet = planet.copyWith(aspects: planetAspectList);
          break;
      }
      
      updatedPlanets.add(updatedPlanet);
    }

    return updatedPlanets;
  }

  /// 合併多種類型的相位到行星列表
  /// 
  /// [planets] 基礎行星列表
  /// [generalAspects] 一般相位列表
  /// [natalAspects] 本命相位列表
  /// [transitAspects] 行運相位列表
  /// 
  /// 返回包含所有相位信息的行星列表
  static List<PlanetPosition> distributeMultipleAspects(
    List<PlanetPosition> planets, {
    List<AspectInfo>? generalAspects,
    List<AspectInfo>? natalAspects,
    List<AspectInfo>? transitAspects,
  }) {
    List<PlanetPosition> result = planets;

    // 分配一般相位
    if (generalAspects != null && generalAspects.isNotEmpty) {
      result = distributeAspects(result, generalAspects, aspectType: 'general');
    }

    // 分配本命相位
    if (natalAspects != null && natalAspects.isNotEmpty) {
      result = distributeAspects(result, natalAspects, aspectType: 'natal');
    }

    // 分配行運相位
    if (transitAspects != null && transitAspects.isNotEmpty) {
      result = distributeAspects(result, transitAspects, aspectType: 'transit');
    }

    return result;
  }

  /// 為行運盤分配相位
  /// 
  /// [natalPlanets] 本命盤行星列表
  /// [transitPlanets] 行運盤行星列表
  /// [natalAspects] 本命盤內部相位
  /// [transitAspects] 行運盤內部相位
  /// [natalTransitAspects] 本命盤與行運盤之間的相位
  /// 
  /// 返回包含相位信息的 [本命盤行星列表, 行運盤行星列表]
  static List<List<PlanetPosition>> distributeTransitAspects(
    List<PlanetPosition> natalPlanets,
    List<PlanetPosition> transitPlanets,
    List<AspectInfo> natalAspects,
    List<AspectInfo> transitAspects,
    List<AspectInfo> natalTransitAspects,
  ) {
    // 為本命盤行星分配相位
    List<PlanetPosition> updatedNatalPlanets = distributeMultipleAspects(
      natalPlanets,
      generalAspects: natalAspects,
      transitAspects: natalTransitAspects,
    );

    // 為行運盤行星分配相位
    List<PlanetPosition> updatedTransitPlanets = distributeMultipleAspects(
      transitPlanets,
      generalAspects: transitAspects,
      natalAspects: natalTransitAspects,
    );

    return [updatedNatalPlanets, updatedTransitPlanets];
  }

  /// 清除行星的特定類型相位
  /// 
  /// [planets] 行星列表
  /// [aspectType] 要清除的相位類型
  /// 
  /// 返回清除了指定相位類型的行星列表
  static List<PlanetPosition> clearAspects(
    List<PlanetPosition> planets,
    String aspectType,
  ) {
    return planets.map((planet) {
      switch (aspectType) {
        case 'natal':
          return planet.copyWith(natalAspects: []);
        case 'transit':
          return planet.copyWith(transitAspects: []);
        case 'general':
          return planet.copyWith(aspects: []);
        case 'all':
          return planet.copyWith(
            aspects: [],
            natalAspects: [],
            transitAspects: [],
          );
        default:
          return planet;
      }
    }).toList();
  }

  /// 獲取行星列表中所有相位的統計信息
  /// 
  /// [planets] 行星列表
  /// 
  /// 返回相位統計信息
  static Map<String, dynamic> getAspectStatistics(List<PlanetPosition> planets) {
    int totalAspects = 0;
    int totalNatalAspects = 0;
    int totalTransitAspects = 0;
    final aspectTypeCount = <String, int>{};
    final planetAspectCount = <String, int>{};

    for (final planet in planets) {
      // 統計各類型相位數量
      totalAspects += planet.aspects.length;

      // 統計行星的相位數量
      final planetTotalAspects = planet.getAllAspects().length;
      planetAspectCount[planet.name] = planetTotalAspects;

      // 統計相位類型
      for (final aspect in planet.getAllAspects()) {
        aspectTypeCount[aspect.aspect] = (aspectTypeCount[aspect.aspect] ?? 0) + 1;
      }
    }

    return {
      'totalAspects': totalAspects,
      'totalNatalAspects': totalNatalAspects,
      'totalTransitAspects': totalTransitAspects,
      'aspectTypeCount': aspectTypeCount,
      'planetAspectCount': planetAspectCount,
    };
  }

  /// 驗證相位分配的正確性
  /// 
  /// [planets] 行星列表
  /// [originalAspects] 原始相位列表
  /// 
  /// 返回驗證結果
  static Map<String, dynamic> validateAspectDistribution(
    List<PlanetPosition> planets,
    List<AspectInfo> originalAspects,
  ) {
    final distributedAspects = <AspectInfo>[];
    
    // 收集所有分配的相位
    for (final planet in planets) {
      distributedAspects.addAll(planet.getAllAspects());
    }

    // 去重（因為每個相位會被分配給兩個行星）
    final uniqueDistributedAspects = <AspectInfo>{};
    for (final aspect in distributedAspects) {
      uniqueDistributedAspects.add(aspect);
    }

    final isValid = uniqueDistributedAspects.length == originalAspects.length;
    
    return {
      'isValid': isValid,
      'originalCount': originalAspects.length,
      'distributedCount': uniqueDistributedAspects.length,
      'missingAspects': originalAspects.length - uniqueDistributedAspects.length,
    };
  }
}
