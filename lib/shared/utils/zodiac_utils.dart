class ZodiacUtils {
  static String getHouseDescription(int houseNumber) {
    switch (houseNumber) {
      case 1:
        return '代表個人形象、外表、個性和生命力';
      case 2:
        return '代表財富、價值觀和自我價值';
      case 3:
        return '代表溝通、學習和短途旅行';
      case 4:
        return '代表家庭、根源和情感基礎';
      case 5:
        return '代表創造力、娛樂和浪漫關係';
      case 6:
        return '代表工作、健康和日常生活';
      case 7:
        return '代表婚姻、合作關係和公開的敵人';
      case 8:
        return '代表轉變、共同財產和神秘事物';
      case 9:
        return '代表高等教育、哲學和遠途旅行';
      case 10:
        return '代表事業、社會地位和權威';
      case 11:
        return '代表友誼、群體活動和理想';
      case 12:
        return '代表潛意識、靈性和隱藏的事物';
      default:
        return '未知宮位';
    }
  }
}
