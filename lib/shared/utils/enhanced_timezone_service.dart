import 'dart:convert';
import 'dart:math' as math;

import 'package:flutter/services.dart' show rootBundle;
import 'package:turf/turf.dart';

import '../../core/utils/logger_utils.dart';

/// 增強版時區服務
/// 
/// 提供更準確的時區查詢功能，包含：
/// 1. 完整的時區資料庫支援
/// 2. 備用查詢機制
/// 3. 座標精度優化
/// 4. 錯誤處理和日誌記錄
class EnhancedTimezoneService {
  static final EnhancedTimezoneService _instance = EnhancedTimezoneService._internal();
  factory EnhancedTimezoneService() => _instance;
  EnhancedTimezoneService._internal();

  List<Map<String, dynamic>>? _cachedTimeZoneFeatures;
  Map<String, String>? _fallbackTimezones;

  /// 讀取並解析時區 GeoJSON
  Future<List<Map<String, dynamic>>> loadTimeZoneGeoJSON() async {
    if (_cachedTimeZoneFeatures != null) {
      return _cachedTimeZoneFeatures!;
    }

    try {
      // logger.i('載入時區 GeoJSON 資料...');
      String geojsonData = await rootBundle.loadString('assets/combined.json');
      Map<String, dynamic> geojson = jsonDecode(geojsonData);
      _cachedTimeZoneFeatures = List<Map<String, dynamic>>.from(geojson['features']);
      
      // logger.i('時區資料載入成功，共 ${_cachedTimeZoneFeatures!.length} 個時區');
      return _cachedTimeZoneFeatures!;
    } catch (e) {
      logger.e('載入時區 GeoJSON 資料失敗: $e');
      rethrow;
    }
  }

  /// 初始化備用時區對應表
  void _initializeFallbackTimezones() {
    if (_fallbackTimezones != null) return;

    _fallbackTimezones = {
      // 美國主要城市
      'honolulu': 'Pacific/Honolulu',
      'anchorage': 'America/Anchorage',
      'los_angeles': 'America/Los_Angeles',
      'denver': 'America/Denver',
      'chicago': 'America/Chicago',
      'new_york': 'America/New_York',
      
      // 歐洲主要城市
      'london': 'Europe/London',
      'paris': 'Europe/Paris',
      'berlin': 'Europe/Berlin',
      'rome': 'Europe/Rome',
      'madrid': 'Europe/Madrid',
      'zurich': 'Europe/Zurich',
      'vienna': 'Europe/Vienna',
      'amsterdam': 'Europe/Amsterdam',
      'brussels': 'Europe/Brussels',
      'stockholm': 'Europe/Stockholm',
      'oslo': 'Europe/Oslo',
      'copenhagen': 'Europe/Copenhagen',
      'helsinki': 'Europe/Helsinki',
      'warsaw': 'Europe/Warsaw',
      'prague': 'Europe/Prague',
      'budapest': 'Europe/Budapest',
      'athens': 'Europe/Athens',
      'moscow': 'Europe/Moscow',
      
      // 亞洲主要城市
      'tokyo': 'Asia/Tokyo',
      'seoul': 'Asia/Seoul',
      'beijing': 'Asia/Shanghai',
      'shanghai': 'Asia/Shanghai',
      'hong_kong': 'Asia/Hong_Kong',
      'taipei': 'Asia/Taipei',
      'singapore': 'Asia/Singapore',
      'bangkok': 'Asia/Bangkok',
      'jakarta': 'Asia/Jakarta',
      'manila': 'Asia/Manila',
      'kuala_lumpur': 'Asia/Kuala_Lumpur',
      'mumbai': 'Asia/Kolkata',
      'delhi': 'Asia/Kolkata',
      'karachi': 'Asia/Karachi',
      'dubai': 'Asia/Dubai',
      'riyadh': 'Asia/Riyadh',
      'tehran': 'Asia/Tehran',
      'istanbul': 'Europe/Istanbul',
      
      // 大洋洲
      'sydney': 'Australia/Sydney',
      'melbourne': 'Australia/Melbourne',
      'brisbane': 'Australia/Brisbane',
      'perth': 'Australia/Perth',
      'auckland': 'Pacific/Auckland',
      
      // 非洲
      'cairo': 'Africa/Cairo',
      'johannesburg': 'Africa/Johannesburg',
      'lagos': 'Africa/Lagos',
      'nairobi': 'Africa/Nairobi',
      'casablanca': 'Africa/Casablanca',
      
      // 南美洲
      'sao_paulo': 'America/Sao_Paulo',
      'buenos_aires': 'America/Argentina/Buenos_Aires',
      'lima': 'America/Lima',
      'bogota': 'America/Bogota',
      'caracas': 'America/Caracas',
      'santiago': 'America/Santiago',
    };
  }

  /// 透過 LatLng 查詢對應的時區
  Future<String?> getTimeZoneFromLatLng(double lat, double lng) async {
    try {
      logger.d('查詢時區: lat=$lat, lng=$lng');
      
      // 驗證座標範圍
      if (lat < -90 || lat > 90 || lng < -180 || lng > 180) {
        logger.w('座標超出有效範圍: lat=$lat, lng=$lng');
        return null;
      }

      List<Map<String, dynamic>> timeZoneFeatures = await loadTimeZoneGeoJSON();
      Position searchPosition = Position(lng, lat);

      // 第一次嘗試：精確匹配
      String? result = await _findTimezoneInFeatures(timeZoneFeatures, searchPosition);
      if (result != null) {
        // logger.d('找到時區: $result');
        return result;
      }

      // 第二次嘗試：擴大搜尋範圍（處理邊界問題）
      logger.d('精確匹配失敗，嘗試擴大搜尋範圍...');
      result = await _findTimezoneWithBuffer(timeZoneFeatures, lat, lng);
      if (result != null) {
        logger.d('擴大搜尋找到時區: $result');
        return result;
      }

      // 第三次嘗試：最近鄰居搜尋
      logger.d('擴大搜尋失敗，嘗試最近鄰居搜尋...');
      result = await _findNearestTimezone(timeZoneFeatures, lat, lng);
      if (result != null) {
        logger.d('最近鄰居搜尋找到時區: $result');
        return result;
      }

      logger.w('無法找到對應的時區: lat=$lat, lng=$lng');
      return null;
    } catch (e) {
      logger.e('查詢時區時發生錯誤: $e');
      return null;
    }
  }

  /// 在時區特徵中查找匹配的時區
  Future<String?> _findTimezoneInFeatures(
    List<Map<String, dynamic>> features, 
    Position position
  ) async {
    for (var feature in features) {
      try {
        var geometry = feature['geometry'];
        var properties = feature['properties'];

        if (geometry['type'] == 'Polygon') {
          List<List<Position>> polygonCoordinates = (geometry['coordinates'] as List)
              .map<List<Position>>((ring) => (ring as List)
              .map<Position>((coordinate) => Position(coordinate[0], coordinate[1]))
              .toList())
              .toList();

          Polygon polygon = Polygon(coordinates: polygonCoordinates);

          if (booleanPointInPolygon(position, polygon)) {
            return properties['tzid'];
          }
        } else if (geometry['type'] == 'MultiPolygon') {
          List<List<List<Position>>> multiPolygonCoordinates = (geometry['coordinates'] as List)
              .map<List<List<Position>>>((polygon) => (polygon as List)
              .map<List<Position>>((ring) => (ring as List)
              .map<Position>((coordinate) => Position(coordinate[0], coordinate[1]))
              .toList())
              .toList())
              .toList();

          MultiPolygon multiPolygon = MultiPolygon(coordinates: multiPolygonCoordinates);

          if (booleanPointInPolygon(position, multiPolygon)) {
            return properties['tzid'];
          }
        }
      } catch (e) {
        logger.w('處理時區特徵時發生錯誤: $e');
        continue;
      }
    }
    return null;
  }

  /// 使用緩衝區擴大搜尋範圍
  Future<String?> _findTimezoneWithBuffer(
    List<Map<String, dynamic>> features, 
    double lat, 
    double lng
  ) async {
    // 創建小範圍的搜尋緩衝區（約 1 公里）
    const double bufferDegrees = 0.01;
    
    List<Position> searchPositions = [
      Position(lng, lat),
      Position(lng + bufferDegrees, lat),
      Position(lng - bufferDegrees, lat),
      Position(lng, lat + bufferDegrees),
      Position(lng, lat - bufferDegrees),
      Position(lng + bufferDegrees, lat + bufferDegrees),
      Position(lng - bufferDegrees, lat - bufferDegrees),
      Position(lng + bufferDegrees, lat - bufferDegrees),
      Position(lng - bufferDegrees, lat + bufferDegrees),
    ];

    for (Position position in searchPositions) {
      String? result = await _findTimezoneInFeatures(features, position);
      if (result != null) {
        return result;
      }
    }
    return null;
  }

  /// 查找最近的時區
  Future<String?> _findNearestTimezone(
    List<Map<String, dynamic>> features, 
    double lat, 
    double lng
  ) async {
    double minDistance = double.infinity;
    String? nearestTimezone;

    for (var feature in features) {
      try {
        var geometry = feature['geometry'];
        var properties = feature['properties'];

        // 計算到時區邊界的最短距離
        double distance = _calculateDistanceToGeometry(geometry, lat, lng);
        
        if (distance < minDistance) {
          minDistance = distance;
          nearestTimezone = properties['tzid'];
        }
      } catch (e) {
        continue;
      }
    }

    // 只返回距離在合理範圍內的時區（約 100 公里）
    if (minDistance < 1.0) {  // 約 100 公里
      return nearestTimezone;
    }
    
    return null;
  }

  /// 計算點到幾何圖形的距離
  double _calculateDistanceToGeometry(Map<String, dynamic> geometry, double lat, double lng) {
    try {
      if (geometry['type'] == 'Polygon') {
        var coordinates = geometry['coordinates'][0] as List;  // 外環
        return _calculateDistanceToRing(coordinates, lat, lng);
      } else if (geometry['type'] == 'MultiPolygon') {
        double minDistance = double.infinity;
        var polygons = geometry['coordinates'] as List;
        
        for (var polygon in polygons) {
          var ring = polygon[0] as List;  // 外環
          double distance = _calculateDistanceToRing(ring, lat, lng);
          minDistance = math.min(minDistance, distance);
        }
        
        return minDistance;
      }
    } catch (e) {
      // 忽略錯誤，返回最大距離
    }
    
    return double.infinity;
  }

  /// 計算點到環的最短距離
  double _calculateDistanceToRing(List coordinates, double lat, double lng) {
    double minDistance = double.infinity;
    
    for (var coordinate in coordinates) {
      if (coordinate is List && coordinate.length >= 2) {
        double coordLng = coordinate[0].toDouble();
        double coordLat = coordinate[1].toDouble();
        
        double distance = _calculateHaversineDistance(lat, lng, coordLat, coordLng);
        minDistance = math.min(minDistance, distance);
      }
    }
    
    return minDistance;
  }

  /// 使用 Haversine 公式計算兩點間距離（度數）
  double _calculateHaversineDistance(double lat1, double lng1, double lat2, double lng2) {
    const double earthRadius = 6371.0; // 地球半徑（公里）
    
    double dLat = _degreesToRadians(lat2 - lat1);
    double dLng = _degreesToRadians(lng2 - lng1);
    
    double a = math.sin(dLat / 2) * math.sin(dLat / 2) +
        math.cos(_degreesToRadians(lat1)) * math.cos(_degreesToRadians(lat2)) *
        math.sin(dLng / 2) * math.sin(dLng / 2);
    
    double c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a));
    
    return earthRadius * c / 111.0; // 轉換為度數（約 111 公里 = 1 度）
  }

  /// 角度轉弧度
  double _degreesToRadians(double degrees) {
    return degrees * math.pi / 180.0;
  }

  /// 根據城市名稱獲取備用時區
  String? getFallbackTimezone(String cityName) {
    _fallbackTimezones ??= {};
    _initializeFallbackTimezones();
    
    String normalizedName = cityName.toLowerCase()
        .replaceAll(' ', '_')
        .replaceAll('-', '_')
        .replaceAll('.', '');
    
    return _fallbackTimezones![normalizedName];
  }

  /// 獲取所有可用的時區列表
  Future<List<String>> getAllTimezones() async {
    try {
      List<Map<String, dynamic>> features = await loadTimeZoneGeoJSON();
      Set<String> timezones = features
          .map((f) => f['properties']['tzid'] as String)
          .toSet();
      
      List<String> sortedTimezones = timezones.toList()..sort();
      logger.i('獲取到 ${sortedTimezones.length} 個時區');
      return sortedTimezones;
    } catch (e) {
      logger.e('獲取時區列表失敗: $e');
      return [];
    }
  }

  /// 清除快取
  void clearCache() {
    _cachedTimeZoneFeatures = null;
    logger.i('時區資料快取已清除');
  }
}
