import '../features/astrology/constants/zodiac_definitions.dart';

/// 星座使用範例
/// 
/// 展示如何使用新的類型安全的 ZodiacSign data class 和 ZodiacDefinitions 方法
class ZodiacUsageExample {
  
  /// 示例：使用類型安全的方法獲取星座信息
  static void demonstrateTypeSafeMethods() {
    print('=== 類型安全的星座方法示例 ===\n');
    
    // 1. 根據 ID 獲取星座
    final aries = ZodiacDefinitions.getSignByIdTyped(1);
    if (aries != null) {
      print('根據 ID 獲取星座：');
      print('  名稱: ${aries.name}');
      print('  符號: ${aries.symbol}');
      print('  元素: ${aries.element}');
      print('  性質: ${aries.quality}');
      print('  極性: ${aries.polarity}');
      print('  主星: ${aries.ruler}');
      print('  是否為火象星座: ${aries.isFireSign}');
      print('  是否為基本星座: ${aries.isCardinalSign}');
      print('  是否為陽性星座: ${aries.isPositive}');
      print('');
    }
    
    // 2. 根據度數獲取星座
    final signAt45Degrees = ZodiacDefinitions.getSignByDegreeTyped(45.0);
    print('45度對應的星座：');
    print('  名稱: ${signAt45Degrees.name}');
    print('  符號: ${signAt45Degrees.symbol}');
    print('  度數範圍: ${signAt45Degrees.startDegree}° - ${signAt45Degrees.endDegree}°');
    print('');
    
    // 3. 根據元素獲取星座
    final fireSigns = ZodiacDefinitions.getSignsByElementTyped('火');
    print('火象星座：');
    for (final sign in fireSigns) {
      print('  ${sign.name} (${sign.symbol})');
    }
    print('');
    
    // 4. 根據性質獲取星座
    final cardinalSigns = ZodiacDefinitions.getSignsByQualityTyped('基本');
    print('基本星座：');
    for (final sign in cardinalSigns) {
      print('  ${sign.name} (${sign.symbol})');
    }
    print('');
    
    // 5. 獲取對宮星座
    final oppositeOfAries = ZodiacDefinitions.getOppositeSignTyped(1);
    print('牡羊座的對宮星座：');
    print('  ${oppositeOfAries.name} (${oppositeOfAries.symbol})');
    print('');
    
    // 6. 獲取三分相星座
    final trineToAries = ZodiacDefinitions.getTrineSignsTyped(1);
    print('與牡羊座成三分相的星座：');
    for (final sign in trineToAries) {
      print('  ${sign.name} (${sign.symbol})');
    }
    print('');
  }
  
  /// 示例：比較舊方法與新方法
  static void compareOldAndNewMethods() {
    print('=== 舊方法 vs 新方法比較 ===\n');
    
    // 舊方法（Map）
    print('舊方法（Map<String, dynamic>）：');
    final oldSignData = ZodiacDefinitions.getSignById(1);
    if (oldSignData != null) {
      final name = oldSignData['name'] as String;
      final symbol = oldSignData['symbol'] as String;
      final element = oldSignData['element'] as String;
      print('  名稱: $name');
      print('  符號: $symbol');
      print('  元素: $element');
      print('  需要類型轉換: ${oldSignData['name'].runtimeType}');
    }
    print('');
    
    // 新方法（ZodiacSign）
    print('新方法（ZodiacSign data class）：');
    final newSign = ZodiacDefinitions.getSignByIdTyped(1);
    if (newSign != null) {
      print('  名稱: ${newSign.name}');
      print('  符號: ${newSign.symbol}');
      print('  元素: ${newSign.element}');
      print('  類型安全: ${newSign.name.runtimeType}');
      print('  便利方法: isFireSign = ${newSign.isFireSign}');
      print('  便利方法: isCardinalSign = ${newSign.isCardinalSign}');
      print('  便利方法: isPositive = ${newSign.isPositive}');
    }
    print('');
  }
  
  /// 示例：使用 ZodiacSign 的便利方法
  static void demonstrateConvenienceMethods() {
    print('=== ZodiacSign 便利方法示例 ===\n');
    
    final allSigns = ZodiacDefinitions.getAllSignsTyped();
    
    // 分類星座
    final fireSigns = allSigns.where((sign) => sign.isFireSign).toList();
    final earthSigns = allSigns.where((sign) => sign.isEarthSign).toList();
    final airSigns = allSigns.where((sign) => sign.isAirSign).toList();
    final waterSigns = allSigns.where((sign) => sign.isWaterSign).toList();
    
    print('按元素分類：');
    print('  火象星座: ${fireSigns.map((s) => s.name).join(', ')}');
    print('  土象星座: ${earthSigns.map((s) => s.name).join(', ')}');
    print('  風象星座: ${airSigns.map((s) => s.name).join(', ')}');
    print('  水象星座: ${waterSigns.map((s) => s.name).join(', ')}');
    print('');
    
    // 按性質分類
    final cardinalSigns = allSigns.where((sign) => sign.isCardinalSign).toList();
    final fixedSigns = allSigns.where((sign) => sign.isFixedSign).toList();
    final mutableSigns = allSigns.where((sign) => sign.isMutableSign).toList();
    
    print('按性質分類：');
    print('  基本星座: ${cardinalSigns.map((s) => s.name).join(', ')}');
    print('  固定星座: ${fixedSigns.map((s) => s.name).join(', ')}');
    print('  變動星座: ${mutableSigns.map((s) => s.name).join(', ')}');
    print('');
    
    // 按極性分類
    final positiveSigns = allSigns.where((sign) => sign.isPositive).toList();
    final negativeSigns = allSigns.where((sign) => sign.isNegative).toList();
    
    print('按極性分類：');
    print('  陽性星座: ${positiveSigns.map((s) => s.name).join(', ')}');
    print('  陰性星座: ${negativeSigns.map((s) => s.name).join(', ')}');
    print('');
  }
  
  /// 示例：星座關係分析
  static void demonstrateSignRelationships() {
    print('=== 星座關係分析示例 ===\n');
    
    final leo = ZodiacDefinitions.getSignByIdTyped(5)!; // 獅子座
    
    print('以獅子座為例：');
    print('  基本信息: ${leo.name} (${leo.symbol})');
    print('  元素: ${leo.element}');
    print('  性質: ${leo.quality}');
    print('  極性: ${leo.polarity}');
    print('');
    
    // 獲取相關星座
    final oppositeSigns = ZodiacDefinitions.getOppositeSignTyped(leo.id);
    final trineSigns = ZodiacDefinitions.getTrineSignsTyped(leo.id);
    final squareSigns = ZodiacDefinitions.getSquareSignsTyped(leo.id);
    final sextileSigns = ZodiacDefinitions.getSextileSignsTyped(leo.id);
    
    print('星座關係：');
    print('  對宮星座: ${oppositeSigns.name} (${oppositeSigns.symbol})');
    print('  三分相星座: ${trineSigns.map((s) => '${s.name}(${s.symbol})').join(', ')}');
    print('  四分相星座: ${squareSigns.map((s) => '${s.name}(${s.symbol})').join(', ')}');
    print('  六分相星座: ${sextileSigns.map((s) => '${s.name}(${s.symbol})').join(', ')}');
    print('');
    
    // 同元素和同性質的星座
    final sameElementSigns = leo.sameElementSigns;
    final sameQualitySigns = leo.sameQualitySigns;
    
    print('同類星座：');
    print('  同元素星座: ${sameElementSigns.map((s) => s.name).join(', ')}');
    print('  同性質星座: ${sameQualitySigns.map((s) => s.name).join(', ')}');
    print('');
  }
  
  /// 運行所有示例
  static void runAllExamples() {
    demonstrateTypeSafeMethods();
    compareOldAndNewMethods();
    demonstrateConvenienceMethods();
    demonstrateSignRelationships();
  }
}
