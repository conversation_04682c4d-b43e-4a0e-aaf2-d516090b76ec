# API 遷移指南：從 JSON 配置到 API 調用

## 概述
本指南說明如何將現有的 JSON 配置系統遷移到 API 調用，實現動態配置管理。

## 當前架構
```
本地 JSON 檔案 → InterpretationConfigService → UI 組件
```

## 目標架構
```
API 端點 → InterpretationConfigService → 本地快取 → UI 組件
```

## 遷移步驟

### 1. API 端點設計

#### 獲取解讀選項配置
```http
GET /api/v1/interpretation-config/{chartType}
```

**回應格式：**
```json
{
  "success": true,
  "data": {
    "version": "1.0.0",
    "lastUpdated": "2025-06-20T10:00:00Z",
    "chartType": "natal",
    "options": [
      {
        "id": "natal_personality",
        "title": "本命格的分析",
        "subtitle": "深入分析您的核心性格、天賦才能、人生格局和命運特質",
        "icon": "person",
        "color": "royalIndigo",
        "interpretationType": "personality",
        "questions": [
          "我的本命格局如何？是什麼樣的命格？",
          "我的核心性格特質和天賦才能是什麼？"
        ],
        "enabled": true,
        "order": 1
      }
    ]
  },
  "meta": {
    "cacheExpiry": 3600,
    "version": "1.0.0"
  }
}
```

#### 獲取所有配置版本
```http
GET /api/v1/interpretation-config/versions
```

### 2. 服務層修改

#### 修改 InterpretationConfigService
```dart
class InterpretationConfigService {
  static const String _baseUrl = 'https://api.astreal.app';
  static const Duration _cacheExpiry = Duration(hours: 1);
  
  // 快取配置和過期時間
  final Map<ChartType, InterpretationConfig> _configCache = {};
  final Map<ChartType, DateTime> _cacheTimestamps = {};

  /// 載入配置（優先使用 API，失敗時使用本地配置）
  Future<InterpretationConfig> loadConfig(ChartType chartType) async {
    // 檢查快取是否有效
    if (_isCacheValid(chartType)) {
      return _configCache[chartType]!;
    }

    try {
      // 嘗試從 API 載入
      final config = await _loadFromAPI(chartType);
      _updateCache(chartType, config);
      return config;
    } catch (e) {
      debugPrint('API 載入失敗，使用本地配置: $e');
      
      // API 失敗時使用本地配置
      return await _loadFromLocal(chartType);
    }
  }

  /// 從 API 載入配置
  Future<InterpretationConfig> _loadFromAPI(ChartType chartType) async {
    final response = await http.get(
      Uri.parse('$_baseUrl/api/v1/interpretation-config/${chartType.name}'),
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'Astreal-App/1.0.0',
      },
    );

    if (response.statusCode == 200) {
      final jsonData = json.decode(response.body);
      if (jsonData['success'] == true) {
        return InterpretationConfig.fromJson(jsonData['data']);
      } else {
        throw Exception('API 回應錯誤: ${jsonData['message']}');
      }
    } else {
      throw Exception('HTTP 錯誤: ${response.statusCode}');
    }
  }

  /// 從本地檔案載入配置（後備方案）
  Future<InterpretationConfig> _loadFromLocal(ChartType chartType) async {
    final configPath = _getLocalConfigPath(chartType);
    final jsonString = await rootBundle.loadString(configPath);
    final jsonData = json.decode(jsonString);
    return InterpretationConfig.fromJson(jsonData);
  }

  /// 檢查快取是否有效
  bool _isCacheValid(ChartType chartType) {
    if (!_configCache.containsKey(chartType)) return false;
    
    final timestamp = _cacheTimestamps[chartType];
    if (timestamp == null) return false;
    
    return DateTime.now().difference(timestamp) < _cacheExpiry;
  }

  /// 更新快取
  void _updateCache(ChartType chartType, InterpretationConfig config) {
    _configCache[chartType] = config;
    _cacheTimestamps[chartType] = DateTime.now();
  }

  /// 強制重新載入配置
  Future<InterpretationConfig> forceReload(ChartType chartType) async {
    _configCache.remove(chartType);
    _cacheTimestamps.remove(chartType);
    return loadConfig(chartType);
  }
}
```

### 3. 錯誤處理和重試機制

```dart
class APIConfigLoader {
  static const int _maxRetries = 3;
  static const Duration _retryDelay = Duration(seconds: 2);

  static Future<InterpretationConfig> loadWithRetry(
    ChartType chartType,
  ) async {
    for (int attempt = 1; attempt <= _maxRetries; attempt++) {
      try {
        return await _loadFromAPI(chartType);
      } catch (e) {
        if (attempt == _maxRetries) {
          throw Exception('API 載入失敗，已重試 $_maxRetries 次: $e');
        }
        
        debugPrint('API 載入失敗，第 $attempt 次重試: $e');
        await Future.delayed(_retryDelay * attempt);
      }
    }
    
    throw Exception('不應該到達這裡');
  }
}
```

### 4. 配置版本管理

```dart
class ConfigVersionManager {
  static const String _versionKey = 'config_version';
  
  /// 檢查配置版本是否需要更新
  static Future<bool> needsUpdate(ChartType chartType) async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/api/v1/interpretation-config/versions'),
      );
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final serverVersion = data['versions'][chartType.name];
        final localVersion = await _getLocalVersion(chartType);
        
        return serverVersion != localVersion;
      }
    } catch (e) {
      debugPrint('版本檢查失敗: $e');
    }
    
    return false;
  }

  /// 更新本地版本記錄
  static Future<void> updateLocalVersion(
    ChartType chartType, 
    String version,
  ) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('${_versionKey}_${chartType.name}', version);
  }
}
```

### 5. UI 層適配

```dart
class _AIInterpretationSelectionPageState extends State<AIInterpretationSelectionPage> {
  late Future<InterpretationConfig> _configFuture;
  
  @override
  void initState() {
    super.initState();
    _loadConfig();
  }

  void _loadConfig() {
    setState(() {
      _configFuture = InterpretationConfigService.instance
          .loadConfig(widget.chartData.chartType);
    });
  }

  /// 手動重新載入配置
  void _reloadConfig() {
    setState(() {
      _configFuture = InterpretationConfigService.instance
          .forceReload(widget.chartData.chartType);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('星盤解讀'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _reloadConfig,
            tooltip: '重新載入配置',
          ),
        ],
      ),
      body: FutureBuilder<InterpretationConfig>(
        future: _configFuture,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }

          if (snapshot.hasError) {
            return _buildErrorWidget(snapshot.error.toString());
          }

          return _buildConfigContent(snapshot.data!);
        },
      ),
    );
  }

  Widget _buildErrorWidget(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 64, color: Colors.red),
          const SizedBox(height: 16),
          Text('載入配置失敗', style: Theme.of(context).textTheme.headlineSmall),
          const SizedBox(height: 8),
          Text(error, textAlign: TextAlign.center),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _reloadConfig,
            child: const Text('重試'),
          ),
        ],
      ),
    );
  }
}
```

### 6. 測試策略

#### 單元測試
```dart
group('InterpretationConfigService API Tests', () {
  test('should load config from API successfully', () async {
    // Mock HTTP response
    when(mockClient.get(any)).thenAnswer((_) async => 
      http.Response(jsonEncode(mockApiResponse), 200));

    final config = await service.loadConfig(ChartType.natal);
    
    expect(config.chartType, equals('natal'));
    expect(config.options.length, greaterThan(0));
  });

  test('should fallback to local config when API fails', () async {
    // Mock API failure
    when(mockClient.get(any)).thenThrow(Exception('Network error'));

    final config = await service.loadConfig(ChartType.natal);
    
    // Should still return valid config from local fallback
    expect(config, isNotNull);
  });
});
```

### 7. 部署注意事項

1. **API 端點準備**：確保 API 端點已部署並可用
2. **向後兼容**：保留本地 JSON 檔案作為後備
3. **快取策略**：合理設定快取過期時間
4. **錯誤監控**：添加 API 調用失敗的監控和告警
5. **版本管理**：實施配置版本控制機制

### 8. 效能優化

1. **預載入**：在應用啟動時預載入常用配置
2. **增量更新**：只更新變更的配置項目
3. **壓縮傳輸**：使用 gzip 壓縮 API 回應
4. **CDN 快取**：將配置檔案部署到 CDN

這個遷移方案確保了平滑的過渡，同時保持了系統的穩定性和可靠性。
