# 法律條款頁面部署文檔

## 概述
為AstReal 應用程式創建了隱私權政策和服務條款的 HTML 頁面，用於部署到 Firebase Hosting。

## 創建的文件

### 1. 隱私權政策
- **文件位置**: `public/privacy-policy.html`
- **訪問網址**: `https://your-domain.com/privacy-policy.html`
- **內容包含**:
  - 資料蒐集說明
  - 使用目的
  - 資料保護措施
  - 第三方服務說明
  - 使用者權利
  - 未成年人保護條款

### 2. 服務條款
- **文件位置**: `public/terms-of-service.html`
- **訪問網址**: `https://your-domain.com/terms-of-service.html`
- **內容包含**:
  - 服務內容說明
  - 使用者義務
  - 智慧財產權
  - 免責聲明
  - 付費服務條款
  - 法律管轄權

## 設計特色

### 視覺設計
- **響應式設計**: 支援桌面和行動裝置
- **品牌一致性**: 使用與 App 一致的色彩主題
- **現代化 UI**: 漸層背景、圓角設計、陰影效果
- **易讀性**: 清晰的字體層級和適當的行距

### 用戶體驗
- **清晰結構**: 分段明確，易於閱讀
- **重點標示**: 重要條款使用特殊樣式突出
- **導航便利**: 提供返回應用程式的連結
- **行動友善**: 針對小螢幕優化的排版

## 部署到 Firebase Hosting

### 1. 自動部署
當您執行 `firebase deploy` 時，這些文件會自動部署到 Firebase Hosting：

```bash
firebase deploy --only hosting
```

### 2. 訪問網址
部署後，可以通過以下網址訪問：
- 隱私權政策: `https://your-project-id.web.app/privacy-policy.html`
- 服務條款: `https://your-project-id.web.app/terms-of-service.html`

### 3. 自定義域名
如果您有自定義域名，網址將是：
- 隱私權政策: `https://your-domain.com/privacy-policy.html`
- 服務條款: `https://your-domain.com/terms-of-service.html`

## 在應用程式中整合

### 1. 設定頁面連結
在應用程式的設定頁面中添加連結：

```dart
// 在設定頁面中添加
ListTile(
  title: Text('隱私權政策'),
  trailing: Icon(Icons.arrow_forward_ios),
  onTap: () => _launchURL('https://your-domain.com/privacy-policy.html'),
),
ListTile(
  title: Text('服務條款'),
  trailing: Icon(Icons.arrow_forward_ios),
  onTap: () => _launchURL('https://your-domain.com/terms-of-service.html'),
),
```

### 2. 註冊流程整合
在用戶註冊時顯示同意條款：

```dart
CheckboxListTile(
  title: RichText(
    text: TextSpan(
      text: '我已閱讀並同意 ',
      style: TextStyle(color: Colors.black),
      children: [
        TextSpan(
          text: '隱私權政策',
          style: TextStyle(color: Colors.blue),
          recognizer: TapGestureRecognizer()
            ..onTap = () => _launchURL('https://your-domain.com/privacy-policy.html'),
        ),
        TextSpan(text: ' 和 '),
        TextSpan(
          text: '服務條款',
          style: TextStyle(color: Colors.blue),
          recognizer: TapGestureRecognizer()
            ..onTap = () => _launchURL('https://your-domain.com/terms-of-service.html'),
        ),
      ],
    ),
  ),
  value: _agreedToTerms,
  onChanged: (value) => setState(() => _agreedToTerms = value ?? false),
),
```

### 3. URL 啟動功能
添加 URL 啟動功能：

```dart
import 'package:url_launcher/url_launcher.dart';

Future<void> _launchURL(String url) async {
  if (await canLaunch(url)) {
    await launch(url);
  } else {
    throw 'Could not launch $url';
  }
}
```

## 法律合規性

### 1. GDPR 合規
- 明確說明資料蒐集目的
- 提供資料刪除權利
- 說明第三方服務使用

### 2. 台灣個資法合規
- 符合個人資料保護法要求
- 明確告知資料使用目的
- 提供聯絡方式

### 3. App Store 合規
- 符合 Apple App Store 審核指南
- 符合 Google Play 政策要求
- 提供清楚的隱私權說明

## 維護和更新

### 1. 定期檢視
- 每季檢視條款內容
- 配合法律變更更新
- 根據服務變更調整

### 2. 版本控制
- 在文件中標註生效日期
- 保留歷史版本記錄
- 重大變更時通知用戶

### 3. 多語言支援
未來可以考慮添加英文版本：
- `public/privacy-policy-en.html`
- `public/terms-of-service-en.html`

## 技術規格

### 文件特性
- **編碼**: UTF-8
- **語言**: 繁體中文 (zh-TW)
- **響應式**: 支援各種螢幕尺寸
- **SEO 友善**: 包含適當的 meta 標籤

### 瀏覽器支援
- Chrome 60+
- Firefox 60+
- Safari 12+
- Edge 79+
- 行動瀏覽器

## 注意事項

1. **法律諮詢**: 建議在正式發布前諮詢法律專業人士
2. **定期更新**: 隨著服務功能變更，需要相應更新條款
3. **用戶通知**: 重大條款變更時應主動通知用戶
4. **備份保存**: 保留所有版本的條款文件作為記錄

這些法律條款頁面為您的應用程式提供了完整的法律保護和用戶透明度，符合現代應用程式的合規要求。
