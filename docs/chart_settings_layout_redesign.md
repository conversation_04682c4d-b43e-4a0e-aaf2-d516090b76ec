# 星盤顯示設置頁面佈局重新設計

## 📋 設計概述

成功重新設計了星盤顯示設置頁面的佈局，採用了更直觀和高效的雙欄式設計，並使用 Tab 分頁來組織不同類型的設置選項。新設計大大提升了用戶體驗和設置效率。

## 🎯 新佈局特色

### 1. Tab 分頁設計
- **星盤設置 Tab**：專注於特定星盤類型的核心設置
- **顯示選項 Tab**：通用的顯示和外觀設置

### 2. 雙欄式佈局（星盤設置 Tab）
- **左側欄**：星盤類型選擇器，可滾動瀏覽所有星盤類型
- **右側欄**：選中星盤類型的具體設置內容

### 3. 智能內容組織
- **核心設置**：宮位系統、行星顯示、相位設置
- **通用設置**：星座界主星、顯示模式

## 🎨 視覺設計

### 1. Tab 導航
```dart
TabBar(
  controller: _tabController,
  indicatorColor: Colors.white,
  labelColor: Colors.white,
  unselectedLabelColor: Colors.white70,
  tabs: const [
    Tab(
      icon: Icon(Icons.tune),
      text: '星盤設置',
    ),
    Tab(
      icon: Icon(Icons.palette),
      text: '顯示選項',
    ),
  ],
),
```

### 2. 左側星盤類型列表
- **固定寬度**：200px，確保內容不會過於擁擠
- **滾動支援**：ListView.builder 支援大量星盤類型
- **選中狀態**：清晰的視覺反饋和邊框高亮
- **描述信息**：每個星盤類型都有簡潔的描述

### 3. 右側設置區域
- **響應式設計**：自動填滿剩餘空間
- **當前類型標題**：顯示選中星盤類型的詳細信息
- **滾動內容**：支援長內容的滾動瀏覽

## 🔧 技術實作

### 1. TabController 管理
```dart
class _ChartDisplaySettingsPageState extends State<ChartDisplaySettingsPage> 
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }
}
```

### 2. 星盤類型列表
```dart
Widget _buildChartTypeList(SettingsViewModel settingsViewModel) {
  final chartTypes = ChartType.values;
  
  return ListView.builder(
    itemCount: chartTypes.length,
    itemBuilder: (context, index) {
      final chartType = chartTypes[index];
      final isSelected = settingsViewModel.currentChartType == chartType;
      
      return Container(
        decoration: BoxDecoration(
          color: isSelected 
              ? AppColors.indigoLight.withValues(alpha: 0.1)
              : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
          border: isSelected 
              ? Border.all(color: AppColors.indigoLight.withValues(alpha: 0.3))
              : null,
        ),
        child: ListTile(
          title: Text(chartType.displayName),
          subtitle: Text(_getChartTypeDescription(chartType)),
          onTap: () => settingsViewModel.setCurrentChartType(chartType),
        ),
      );
    },
  );
}
```

### 3. 動態設置內容
```dart
Widget _buildChartTypeSettings(SettingsViewModel settingsViewModel) {
  final currentChartType = settingsViewModel.currentChartType;
  
  if (currentChartType == null) {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.category_outlined, size: 64),
          Text('請選擇星盤類型'),
        ],
      ),
    );
  }

  return SingleChildScrollView(
    child: Column(
      children: [
        // 當前星盤類型標題
        _buildCurrentChartTypeHeader(currentChartType),
        // 宮位系統設置
        _buildHouseSystemSection(settingsViewModel),
        // 行星顯示設置
        _buildPlanetVisibilitySection(settingsViewModel),
        // 相位設置
        _buildAspectSettingsSection(settingsViewModel),
      ],
    ),
  );
}
```

## 📊 星盤類型描述系統

### 1. 完整的描述映射
```dart
String _getChartTypeDescription(ChartType chartType) {
  switch (chartType) {
    case ChartType.natal:
      return '個人出生星盤，分析性格與命運';
    case ChartType.transit:
      return '流年星盤，預測未來趨勢';
    case ChartType.synastry:
      return '合盤分析，探索關係相容性';
    case ChartType.composite:
      return '組合盤，分析關係本質';
    case ChartType.davison:
      return '戴維森盤，關係的中點分析';
    // ... 更多星盤類型
  }
}
```

### 2. 圖標系統
```dart
IconData _getChartTypeIcon(ChartType chartType) {
  switch (chartType) {
    case ChartType.natal:
      return Icons.person;
    case ChartType.transit:
      return Icons.timeline;
    case ChartType.synastry:
      return Icons.favorite;
    // ... 更多圖標映射
  }
}
```

## 🎯 用戶體驗優化

### 1. 直觀的導航
- **Tab 切換**：清晰的功能分類
- **視覺提示**：選中狀態的明確反饋
- **快速訪問**：一鍵切換星盤類型

### 2. 高效的設置流程
1. **選擇星盤類型**：在左側列表中點擊
2. **查看類型信息**：右側顯示詳細描述和圖標
3. **調整設置**：直接在右側進行各項設置
4. **切換到顯示選項**：調整通用的顯示設置

### 3. 響應式設計
- **固定左欄**：確保星盤類型列表始終可見
- **彈性右欄**：根據螢幕大小自動調整
- **滾動支援**：長內容的流暢滾動體驗

## 📱 內容組織

### 星盤設置 Tab
- **星盤類型選擇**：左側滾動列表
- **宮位系統設置**：針對選中的星盤類型
- **行星顯示設置**：包含新增的日月中點和宿命點
- **相位設置**：專用的容許度設置

### 顯示選項 Tab
- **星座界主星設置**：控制星座界主星的顯示
- **顯示模式設置**：經典模式 vs 度數模式
- **重置選項**：恢復預設設置

## 🚀 技術優勢

### 1. 模組化設計
- **獨立的 Tab 內容**：易於維護和擴展
- **可重用的組件**：設置卡片和選項組件
- **清晰的職責分離**：不同類型設置分開管理

### 2. 性能優化
- **懶加載**：只渲染當前 Tab 的內容
- **高效列表**：ListView.builder 的虛擬化
- **狀態管理**：Provider 的響應式更新

### 3. 可擴展性
- **新星盤類型**：易於添加新的星盤類型
- **新設置選項**：靈活的設置組件架構
- **新 Tab 頁面**：可以輕鬆添加更多分類

## 🔮 未來擴展

### 可能的改進
1. **搜索功能**：在星盤類型列表中添加搜索
2. **收藏功能**：標記常用的星盤類型
3. **預設組合**：為不同用戶群體提供預設配置
4. **匯入匯出**：設置的備份和分享功能

### 技術優化
1. **動畫效果**：Tab 切換和選擇的動畫
2. **鍵盤導航**：支援鍵盤快捷鍵
3. **無障礙改進**：更好的螢幕閱讀器支援
4. **主題適配**：支援深色模式

## 📋 使用指南

### 1. 基本操作
1. **選擇 Tab**：點擊「星盤設置」或「顯示選項」
2. **選擇星盤類型**：在左側列表中點擊想要設置的類型
3. **調整設置**：在右側區域進行具體設置
4. **查看效果**：設置即時生效並自動保存

### 2. 高效技巧
- **快速切換**：使用 Tab 在不同設置類別間切換
- **批量設置**：在星盤設置 Tab 中完成所有核心設置
- **通用調整**：在顯示選項 Tab 中調整外觀設置

## 📝 總結

新的星盤顯示設置頁面佈局成功地解決了原有設計的複雜性問題，通過清晰的功能分類和直觀的雙欄式佈局，大大提升了設置效率和用戶體驗。

### 主要成就
- ✅ 實現了清晰的 Tab 分頁設計
- ✅ 採用了高效的雙欄式佈局
- ✅ 提供了完整的星盤類型描述系統
- ✅ 優化了設置流程和用戶體驗
- ✅ 保持了所有原有功能的完整性
- ✅ 為未來擴展奠定了良好基礎

這個重新設計不僅提升了當前的用戶體驗，也為未來添加更多星盤類型和設置選項提供了可擴展的架構基礎。
