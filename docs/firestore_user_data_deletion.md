# Firestore 用戶資料刪除功能實現

## 🎯 功能概述

在 `_deleteUserViaSDK` 方法中添加了完整的 Firestore 用戶資料刪除功能，確保當用戶刪除帳戶時，不僅刪除 Firebase Auth 帳戶，同時也清除 Cloud Firestore 中所有相關的用戶資料。

## 🔧 實現詳情

### 1. 更新導入

**檔案**：`lib/services/firebase_auth_service.dart`

添加了 Cloud Firestore 導入：
```dart
import 'package:cloud_firestore/cloud_firestore.dart';
```

### 2. 更新 `_deleteUserViaSDK` 方法

```dart
static Future<void> _deleteUserViaSDK() async {
  try {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) {
      throw Exception('沒有已登入的用戶');
    }

    final userId = user.uid;
    logger.i('使用 Firebase SDK 刪除用戶帳戶: $userId');

    // 先刪除 Firestore 中的用戶資料
    await _deleteUserDataFromFirestore(userId);

    // 然後刪除 Firebase Auth 用戶帳戶
    await user.delete();

    // 清除本地會話
    await _clearUserSession();

    logger.i('Firebase SDK 用戶帳戶及相關資料刪除成功');
  } on FirebaseAuthException catch (e) {
    logger.e('Firebase SDK 刪除用戶帳戶失敗: ${e.code} - ${e.message}');
    throw Exception(_getFirebaseErrorMessage(e.code));
  } catch (e) {
    logger.e('Firebase SDK 刪除用戶帳戶失敗: $e');
    rethrow;
  }
}
```

### 3. 新增 `_deleteUserDataFromFirestore` 方法

```dart
/// 刪除 Firestore 中的用戶資料
static Future<void> _deleteUserDataFromFirestore(String userId) async {
  try {
    logger.i('開始刪除 Firestore 中的用戶資料: $userId');
    final firestore = FirebaseFirestore.instance;
    final batch = firestore.batch();

    // 定義需要刪除的集合和文檔
    final collectionsToDelete = [
      'user_profiles',           // 用戶檔案
      'user_preferences',        // 用戶偏好設定
      'user_free_trial',         // 免費試用記錄
      'user_subscriptions',      // 用戶訂閱狀態
    ];

    // 刪除主要集合中的用戶文檔
    for (final collection in collectionsToDelete) {
      final docRef = firestore.collection(collection).doc(userId);
      batch.delete(docRef);
      logger.d('標記刪除: $collection/$userId');
    }

    // 刪除用戶專屬的子集合
    await _deleteUserSubcollections(firestore, userId);

    // 執行批次刪除
    await batch.commit();
    logger.i('成功刪除 Firestore 中的用戶主要資料: $userId');

  } catch (e) {
    logger.e('刪除 Firestore 用戶資料失敗: $e');
    // 不拋出異常，因為即使 Firestore 刪除失敗，我們仍然要刪除 Auth 帳戶
  }
}
```

### 4. 新增 `_deleteUserSubcollections` 方法

```dart
/// 刪除用戶的子集合資料
static Future<void> _deleteUserSubcollections(FirebaseFirestore firestore, String userId) async {
  try {
    // 刪除用戶支付記錄子集合
    await _deleteSubcollection(firestore, 'user_payments', userId, 'payments');
    
    // 可以根據需要添加更多子集合的刪除邏輯
    
    logger.i('成功刪除用戶子集合資料: $userId');
  } catch (e) {
    logger.e('刪除用戶子集合資料失敗: $e');
  }
}
```

### 5. 新增 `_deleteSubcollection` 方法

```dart
/// 刪除指定的子集合
static Future<void> _deleteSubcollection(
  FirebaseFirestore firestore, 
  String parentCollection, 
  String documentId, 
  String subcollection
) async {
  try {
    final subcollectionRef = firestore
        .collection(parentCollection)
        .doc(documentId)
        .collection(subcollection);

    // 獲取子集合中的所有文檔
    final querySnapshot = await subcollectionRef.get();
    
    if (querySnapshot.docs.isNotEmpty) {
      final batch = firestore.batch();
      
      for (final doc in querySnapshot.docs) {
        batch.delete(doc.reference);
      }
      
      await batch.commit();
      logger.d('刪除子集合: $parentCollection/$documentId/$subcollection (${querySnapshot.docs.length} 個文檔)');
    }
  } catch (e) {
    logger.e('刪除子集合失敗 $parentCollection/$documentId/$subcollection: $e');
  }
}
```

## 📋 刪除的資料類型

### 主要集合文檔
- `user_profiles/{userId}` - 用戶檔案資料
- `user_preferences/{userId}` - 用戶偏好設定
- `user_free_trial/{userId}` - 免費試用記錄
- `user_subscriptions/{userId}` - 用戶訂閱狀態

### 子集合資料
- `user_payments/{userId}/payments/*` - 用戶支付記錄

## 🔒 安全性考量

### 1. 錯誤處理
- Firestore 刪除失敗不會阻止 Auth 帳戶刪除
- 詳細的錯誤日誌記錄
- 批次操作確保原子性

### 2. 執行順序
1. 先刪除 Firestore 資料
2. 再刪除 Firebase Auth 帳戶
3. 最後清除本地會話

### 3. 批次操作
- 使用 Firestore 批次操作確保主要文檔的原子性刪除
- 子集合單獨處理以避免批次大小限制

## 🚀 使用方式

用戶刪除帳戶時，系統會自動：

1. **檢查用戶登入狀態**
2. **刪除 Firestore 資料**
   - 主要集合中的用戶文檔
   - 用戶專屬的子集合資料
3. **刪除 Firebase Auth 帳戶**
4. **清除本地會話**

## 📊 日誌記錄

系統會記錄以下操作：
- 開始刪除用戶資料
- 標記刪除的每個集合
- 子集合刪除詳情
- 成功/失敗狀態

## 🔄 擴展性

### 添加新的資料類型
要刪除新的用戶資料類型，只需：

1. **主要集合**：添加到 `collectionsToDelete` 列表
2. **子集合**：在 `_deleteUserSubcollections` 中添加新的刪除邏輯

### 範例：添加新集合
```dart
final collectionsToDelete = [
  'user_profiles',
  'user_preferences', 
  'user_free_trial',
  'user_subscriptions',
  'user_birth_data',        // 新增：用戶出生資料
  'user_chart_history',     // 新增：星盤歷史記錄
];
```

## ⚠️ 注意事項

1. **不可逆操作**：用戶資料刪除後無法恢復
2. **網路依賴**：需要網路連接才能完成 Firestore 操作
3. **權限要求**：確保用戶有刪除自己資料的權限
4. **備份考量**：考慮在刪除前進行資料備份（如果需要）

## 🧪 測試建議

### 單元測試
- 測試各種錯誤情況
- 驗證批次操作的正確性
- 確認子集合刪除邏輯

### 整合測試
- 完整的用戶刪除流程
- 驗證所有相關資料都被清除
- 測試網路異常情況的處理

### 手動測試
1. 創建測試用戶並添加各種資料
2. 執行帳戶刪除
3. 驗證 Firestore 中沒有殘留資料
4. 確認 Auth 帳戶已被刪除
