# 解讀配置命名規範文件

## 概述

為了更好地識別和管理解讀配置，我們統一了 Remote Config 鍵值和 Assets 檔案的命名規範，使用 `interpretation_config_` 作為統一前綴。

## 命名規範

### 統一前綴
- **Remote Config 鍵值**：`interpretation_config_[類型]`
- **Assets 檔案路徑**：`assets/config/interpretation_config_[類型].json`

### 優勢
1. **易於識別**：一眼就能看出是解讀配置相關
2. **統一管理**：Remote Config 和 Assets 使用相同的命名邏輯
3. **避免衝突**：前綴確保不會與其他配置混淆
4. **便於搜尋**：可以快速找到所有解讀配置

## 完整映射表

| 星盤類型 | Remote Config 鍵值 | Assets 檔案路徑 |
|---------|-------------------|----------------|
| 本命盤 | `interpretation_config_natal` | `assets/config/interpretation_config_natal.json` |
| 推運盤 | `interpretation_config_transit` | `assets/config/interpretation_config_transit.json` |
| 次限推運 | `interpretation_config_secondary_progression` | `assets/config/interpretation_config_secondary_progression.json` |
| 三限推運 | `interpretation_config_tertiary_progression` | `assets/config/interpretation_config_tertiary_progression.json` |
| 太陽弧推運 | `interpretation_config_solar_arc` | `assets/config/interpretation_config_solar_arc.json` |
| 太陽返照 | `interpretation_config_solar_return` | `assets/config/interpretation_config_solar_return.json` |
| 月亮返照 | `interpretation_config_lunar_return` | `assets/config/interpretation_config_lunar_return.json` |
| 合盤類 | `interpretation_config_relationship` | `assets/config/interpretation_config_relationship.json` |
| 比較盤推運 | `interpretation_config_synastry_progression` | `assets/config/interpretation_config_synastry_progression.json` |
| 組合盤推運 | `interpretation_config_composite_progression` | `assets/config/interpretation_config_composite_progression.json` |
| 時空中點盤推運 | `interpretation_config_davison_progression` | `assets/config/interpretation_config_davison_progression.json` |
| 馬克思盤推運 | `interpretation_config_marks_progression` | `assets/config/interpretation_config_marks_progression.json` |
| 卜卦占星 | `interpretation_config_horary` | `assets/config/interpretation_config_horary.json` |
| 事件占星 | `interpretation_config_event` | `assets/config/interpretation_config_event.json` |
| 世俗占星 | `interpretation_config_mundane` | `assets/config/interpretation_config_mundane.json` |
| 法達星期 | `interpretation_config_firdaria` | `assets/config/interpretation_config_firdaria.json` |
| 小限法 | `interpretation_config_profection` | `assets/config/interpretation_config_profection.json` |
| 季節節氣 | `interpretation_config_equinox_solstice` | `assets/config/interpretation_config_equinox_solstice.json` |
| 日月蝕 | `interpretation_config_eclipse` | `assets/config/interpretation_config_eclipse.json` |
| 木土合相 | `interpretation_config_jupiter_saturn_conjunction` | `assets/config/interpretation_config_jupiter_saturn_conjunction.json` |
| 火土合相 | `interpretation_config_mars_saturn_conjunction` | `assets/config/interpretation_config_mars_saturn_conjunction.json` |

## 修改內容

### 1. Remote Config 鍵值更新
**修改前**：
```
natal_interpretation_options
transit_interpretation_options
relationship_interpretation_options
```

**修改後**：
```
interpretation_config_natal
interpretation_config_transit
interpretation_config_relationship
```

### 2. Assets 檔案路徑更新
**修改前**：
```
assets/config/natal_interpretation_options.json
assets/config/transit_interpretation_options.json
assets/config/relationship_interpretation_options.json
```

**修改後**：
```
assets/config/interpretation_config_natal.json
assets/config/interpretation_config_transit.json
assets/config/interpretation_config_relationship.json
```

### 3. 程式碼修改
- 更新 `_getRemoteConfigKey()` 方法中的所有鍵值
- 更新 `_getConfigPath()` 方法中的所有檔案路徑
- 更新測試檔案中的測試案例
- 更新文件中的映射表

## 向後相容性

此次修改是破壞性變更，需要：

1. **更新 Remote Config**：在 Firebase Console 中使用新的鍵值名稱
2. **重新命名 Assets 檔案**：將現有的配置檔案重新命名
3. **更新相關程式碼**：確保所有引用都使用新的命名規範

## 實施建議

1. **分階段實施**：
   - 第一階段：更新程式碼支援新命名
   - 第二階段：更新 Remote Config 配置
   - 第三階段：重新命名 Assets 檔案

2. **測試驗證**：
   - 確保所有星盤類型都能正確載入配置
   - 驗證 Remote Config 和 Assets 的降級機制
   - 測試快取和重新載入功能

3. **文件更新**：
   - 更新 API 文件
   - 更新配置管理指南
   - 更新開發者文件

## 注意事項

1. **一致性**：確保 Remote Config 和 Assets 使用相同的命名邏輯
2. **可讀性**：命名應該清晰表達配置的用途
3. **可維護性**：統一的前綴便於批量操作和管理
4. **擴展性**：新增星盤類型時遵循相同的命名規範
