# 重複扣費問題修復

## 📋 問題描述

用戶在使用 AI 解讀功能時遇到 "扣除分析次數失敗，但解析已完成" 的錯誤訊息。經過分析發現，這是由於系統在兩個地方重複扣除分析次數導致的。

## 🔍 問題分析

### 重複扣費的兩個位置

1. **AIInterpretationResultPage** 中的 `_checkAndConsumePermission()` 方法
2. **AIApiService** 中的 `getChartInterpretation()` 方法

### 問題流程

```
用戶點擊 AI 解讀
↓
AIInterpretationResultPage._checkAndConsumePermission()
├─ 檢查權限
├─ 扣除一次分析次數 ✅ (第一次扣除)
└─ 調用 ChartInterpretationService.getCustomInterpretation()
    ↓
    AIApiService.getChartInterpretation()
    ├─ 執行 AI 解析
    ├─ 解析成功
    └─ 再次扣除分析次數 ❌ (第二次扣除 - 重複)
```

### 錯誤訊息產生原因

當第二次嘗試扣除分析次數時：
- 如果用戶只有 1 次剩餘次數，第一次扣除後剩餘 0 次
- 第二次扣除時發現沒有剩餘次數，返回 `false`
- 系統記錄 "⚠️ 扣除分析次數失敗，但解析已完成"

## 🛠️ 修復方案

### 修復前的 AIApiService 代碼

```dart
// 記錄使用統計
await _recordUsageStats(modelConfig.provider, fullPrompt, response);

// 解析成功，從 Firebase 扣除分析次數
logger.i("解析成功，開始扣除分析次數...");
try {
  final success = await PaymentService.useSinglePurchaseAttempt();
  if (success) {
    logger.i("✅ 成功扣除一次分析次數");
  } else {
    logger.w("⚠️ 扣除分析次數失敗，但解析已完成");
  }
} catch (e) {
  logger.e("❌ 扣除分析次數時出錯: $e");
  // 不拋出異常，因為解析已經成功
}

return response;
```

### 修復後的 AIApiService 代碼

```dart
// 記錄使用統計
await _recordUsageStats(modelConfig.provider, fullPrompt, response);

// 注意：分析次數的扣除已經在 AIInterpretationResultPage 的 _checkAndConsumePermission() 中處理
// 這裡不再重複扣除，避免雙重扣費問題
logger.i("✅ AI 解析成功完成");

return response;
```

### 移除不需要的 Import

```dart
// 修復前
import 'payment_service.dart';

// 修復後
// 移除了不再使用的 import
```

## 🎯 修復邏輯

### 正確的扣費流程

```
用戶點擊 AI 解讀
↓
AIInterpretationResultPage._checkAndConsumePermission()
├─ 檢查權限
├─ 扣除一次分析次數 ✅ (唯一扣除點)
└─ 調用 ChartInterpretationService.getCustomInterpretation()
    ↓
    AIApiService.getChartInterpretation()
    ├─ 執行 AI 解析
    ├─ 解析成功
    └─ 記錄使用統計 ✅ (不再扣除次數)
```

### 扣費責任分離

- **AIInterpretationResultPage**：負責權限檢查和次數扣除
- **AIApiService**：負責 AI 解析和使用統計記錄

## 🔒 AIInterpretationResultPage 中的正確扣費邏輯

### 免費用戶的扣費優先級

```dart
// 如果是免費用戶，優先使用單次購買，然後使用免費試用
final isPremium = await PaymentService.isPremiumUser();
if (!isPremium) {
  // 1. 先嘗試使用單次購買
  final remainingSinglePurchases = await PaymentService.getRemainingSinglePurchases();
  if (remainingSinglePurchases > 0) {
    final success = await PaymentService.useSinglePurchaseAttempt();
    if (success) {
      // 顯示單次購買使用提示
      return true;
    }
  } else {
    // 2. 沒有單次購買，嘗試使用免費試用
    final success = await PaymentService.useFreeTrialAttempt();
    if (!success) {
      _showPaymentRequired();
      return false;
    }
    // 顯示免費試用使用提示
    return true;
  }
}

// 付費用戶，直接允許
return true;
```

### 扣費成功後的用戶提示

```dart
// 單次購買使用提示
ScaffoldMessenger.of(context).showSnackBar(
  SnackBar(
    content: Text('使用單次購買解讀，剩餘 $remaining 次'),
    backgroundColor: AppColors.solarAmber,
  ),
);

// 免費試用使用提示
ScaffoldMessenger.of(context).showSnackBar(
  SnackBar(
    content: Text('已使用免費試用，剩餘 $remainingTrials 次'),
    backgroundColor: AppColors.indigoLight,
  ),
);
```

## 🚀 修復效果

修復後的效果：
- ✅ **消除重複扣費**：每次 AI 解讀只扣除一次分析次數
- ✅ **錯誤訊息消失**：不再出現 "扣除分析次數失敗，但解析已完成" 的錯誤
- ✅ **用戶體驗改善**：用戶不會因為系統錯誤而損失額外的分析次數
- ✅ **代碼簡化**：移除了 AIApiService 中不必要的扣費邏輯
- ✅ **責任分離**：權限檢查和次數扣除集中在一個地方管理

## 📱 用戶體驗改善

### 修復前的用戶體驗問題
- 用戶可能會看到錯誤訊息，造成困惑
- 實際上可能被重複扣費（如果第二次扣除成功）
- 系統日誌中會有錯誤記錄

### 修復後的用戶體驗
- 清晰的扣費提示訊息
- 準確的剩餘次數顯示
- 沒有錯誤訊息干擾

這次修復確保了 AI 解讀功能的扣費邏輯正確且用戶友好，消除了重複扣費的問題。
