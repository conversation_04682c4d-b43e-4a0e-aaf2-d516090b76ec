# Firebase 用戶數據服務實現說明

## 概述

完成了 `UserDataInitializationService` 中所有 Firebase 服務調用的實現，支援通過 REST API 和 SDK 兩種方式來設置和獲取用戶檔案、設定和偏好數據。

## 實現的服務方法

### 1. 用戶檔案管理

#### 獲取用戶檔案
```dart
/// 獲取用戶檔案
static Future<Map<String, dynamic>?> _getUserProfile(String userId) async {
  if (Platform.isWindows) {
    return await _getUserProfileViaRestApi(userId);
  } else {
    return await _getUserProfileViaSDK(userId);
  }
}
```

#### REST API 實現
```dart
/// 通過 REST API 獲取用戶檔案
static Future<Map<String, dynamic>?> _getUserProfileViaRestApi(String userId) async {
  final config = FirebaseConfigWindows.instance;
  final url = '${config.databaseUrl}/user_profiles/$userId.json';
  
  final response = await http.get(Uri.parse(url));
  
  if (response.statusCode == 200) {
    final data = json.decode(response.body);
    if (data != null && data is Map<String, dynamic>) {
      return data;
    }
  }
  
  return null; // 檔案不存在
}
```

#### 設置用戶檔案
```dart
/// 通過 REST API 設置用戶檔案
static Future<bool> _setUserProfileViaRestApi(String userId, Map<String, dynamic> data) async {
  final config = FirebaseConfigWindows.instance;
  final url = '${config.databaseUrl}/user_profiles/$userId.json';
  
  final response = await http.put(
    Uri.parse(url),
    headers: {'Content-Type': 'application/json'},
    body: json.encode(data),
  );
  
  return response.statusCode == 200;
}
```

### 2. 用戶設定管理

#### 獲取用戶設定
```dart
/// 通過 REST API 獲取用戶設定
static Future<Map<String, dynamic>?> _getUserSettingsViaRestApi(String userId) async {
  final config = FirebaseConfigWindows.instance;
  final url = '${config.databaseUrl}/user_settings/$userId.json';
  
  final response = await http.get(Uri.parse(url));
  
  if (response.statusCode == 200) {
    final data = json.decode(response.body);
    if (data != null && data is Map<String, dynamic>) {
      return data;
    }
  }
  
  return null;
}
```

#### 設置用戶設定
```dart
/// 通過 REST API 設置用戶設定
static Future<bool> _setUserSettingsViaRestApi(String userId, Map<String, dynamic> data) async {
  final config = FirebaseConfigWindows.instance;
  final url = '${config.databaseUrl}/user_settings/$userId.json';
  
  final response = await http.put(
    Uri.parse(url),
    headers: {'Content-Type': 'application/json'},
    body: json.encode(data),
  );
  
  return response.statusCode == 200;
}
```

### 3. 用戶偏好設定管理

#### 獲取用戶偏好設定
```dart
/// 通過 REST API 獲取用戶偏好設定
static Future<Map<String, dynamic>?> _getUserPreferencesViaRestApi(String userId) async {
  final config = FirebaseConfigWindows.instance;
  final url = '${config.databaseUrl}/user_preferences/$userId.json';
  
  final response = await http.get(Uri.parse(url));
  
  if (response.statusCode == 200) {
    final data = json.decode(response.body);
    if (data != null && data is Map<String, dynamic>) {
      return data;
    }
  }
  
  return null;
}
```

#### 設置用戶偏好設定
```dart
/// 通過 REST API 設置用戶偏好設定
static Future<bool> _setUserPreferencesViaRestApi(String userId, Map<String, dynamic> data) async {
  final config = FirebaseConfigWindows.instance;
  final url = '${config.databaseUrl}/user_preferences/$userId.json';
  
  final response = await http.put(
    Uri.parse(url),
    headers: {'Content-Type': 'application/json'},
    body: json.encode(data),
  );
  
  return response.statusCode == 200;
}
```

## Firebase 數據結構

### 1. 用戶檔案 (`user_profiles`)
```
user_profiles/
├── {userId}/
    ├── user_id: string
    ├── email: string|null
    ├── display_name: string
    ├── is_anonymous: boolean
    ├── created_at: string (ISO 8601)
    ├── updated_at: string (ISO 8601)
    ├── profile_completed: boolean
    ├── last_login_at: string (ISO 8601)
    └── login_count: number
```

### 2. 用戶設定 (`user_settings`)
```
user_settings/
├── {userId}/
    ├── user_id: string
    ├── language: string
    ├── theme: string
    ├── user_mode: string
    ├── notifications_enabled: boolean
    ├── email_notifications: boolean
    ├── push_notifications: boolean
    ├── auto_save: boolean
    ├── created_at: string (ISO 8601)
    └── updated_at: string (ISO 8601)
```

### 3. 用戶偏好設定 (`user_preferences`)
```
user_preferences/
├── {userId}/
    ├── user_id: string
    ├── chart_display_preferences: object
    │   ├── default_chart_size: number
    │   ├── show_aspects: boolean
    │   ├── show_houses: boolean
    │   ├── show_signs: boolean
    │   └── color_scheme: string
    ├── interpretation_preferences: object
    │   ├── preferred_ai_model: string
    │   ├── interpretation_length: string
    │   ├── include_aspects: boolean
    │   └── include_houses: boolean
    ├── ui_preferences: object
    │   ├── sidebar_collapsed: boolean
    │   ├── show_tooltips: boolean
    │   └── animation_enabled: boolean
    ├── created_at: string (ISO 8601)
    └── updated_at: string (ISO 8601)
```

## 平台支援

### 1. Windows 平台
- 使用 REST API 方式
- 通過 `FirebaseConfigWindows` 獲取配置
- 使用 HTTP PUT/GET 請求

### 2. 其他平台（Android/iOS/macOS）
- 預留 SDK 方式實現
- 目前返回預設值或 false
- 未來可以添加 Firestore SDK 支援

## 錯誤處理

### 1. 網路錯誤
```dart
try {
  final response = await http.get(Uri.parse(url));
  // 處理響應
} catch (e) {
  logger.e('網路請求失敗: $e');
  return null; // 或 false
}
```

### 2. 數據格式錯誤
```dart
final data = json.decode(response.body);
if (data != null && data is Map<String, dynamic>) {
  return data;
}
return null; // 數據格式不正確
```

### 3. HTTP 狀態碼錯誤
```dart
if (response.statusCode == 200) {
  // 成功處理
  return true;
} else {
  logger.e('HTTP 錯誤: ${response.statusCode} - ${response.body}');
  return false;
}
```

## 使用流程

### 1. 初始化新用戶數據
```
檢查現有數據 → 如果不存在 → 創建預設數據 → 保存到 Firebase
    ↓
_getUserProfile(userId)
    ↓
如果返回 null
    ↓
_setUserProfile(userId, defaultData)
    ↓
保存成功
```

### 2. 數據同步
- 獲取：先檢查本地快取，再從 Firebase 獲取
- 設置：直接保存到 Firebase，更新本地快取
- 衝突：以 Firebase 數據為準

### 3. 離線處理
- 獲取失敗時使用本地快取
- 設置失敗時暫存到本地，下次連線時重試
- 提供離線模式指示

## 性能優化

### 1. 請求優化
- 使用 HTTP 連接池
- 適當的超時設置
- 請求重試機制

### 2. 數據快取
- 本地快取常用數據
- 智能快取更新策略
- 快取過期管理

### 3. 批量操作
- 合併多個小請求
- 使用 Firebase 批量 API
- 減少網路往返次數

## 安全考慮

### 1. 數據驗證
- 輸入數據格式驗證
- 用戶 ID 格式檢查
- 防止 SQL 注入（雖然是 NoSQL）

### 2. 權限控制
- 用戶只能訪問自己的數據
- 適當的 Firebase 安全規則
- API 密鑰保護

### 3. 數據加密
- 敏感數據加密存儲
- HTTPS 傳輸加密
- 本地數據加密

## 監控和日誌

### 1. 操作日誌
- 詳細的成功/失敗日誌
- 性能指標記錄
- 錯誤統計分析

### 2. 用戶行為追蹤
- 數據訪問模式
- 功能使用統計
- 錯誤發生頻率

### 3. 系統健康監控
- API 響應時間
- 成功率統計
- 資源使用情況

## 未來擴展

### 1. SDK 實現
- 添加 Firestore SDK 支援
- 實現離線同步
- 提供實時更新

### 2. 更多數據類型
- 用戶活動記錄
- 應用使用統計
- 社交關係數據

### 3. 高級功能
- 數據備份和恢復
- 跨設備同步
- 數據分析和洞察

這個完整的 Firebase 服務實現為用戶數據管理提供了可靠的雲端存儲和同步能力，支援多平台和多種數據類型，同時保證了數據安全性和系統性能。
