# 性別欄位新增功能實作文件

## 概述
本文件說明在 `BirthDataFormPage` 中新增性別輸入欄位的實作過程，該欄位為非必填項目，支援男性、女性、中性三種選項，以及未選擇狀態。

## 實作目標
- 在出生資料表單中新增性別選擇欄位
- 設計為非必填項目，用戶可以選擇不填寫
- 提供男性、女性、中性三種性別選項
- 支援未選擇狀態，給用戶更多彈性
- 整合到現有的 BirthData 模型和存儲系統

## 技術實現

### 1. 性別枚舉設計

#### Gender 枚舉定義
```dart
enum Gender {
  male('男性', Icons.male, Colors.blue),
  female('女性', Icons.female, Colors.pink),
  neutral('中性', Icons.transgender, Colors.purple);

  const Gender(this.displayName, this.icon, this.color);

  final String displayName;
  final IconData icon;
  final Color color;
}
```

#### 設計特點
- **多元化支援**：包含男性、女性、中性三種選項
- **視覺識別**：每種性別都有對應的圖標和顏色
- **國際化友好**：使用 displayName 支援多語言
- **存儲兼容**：提供字符串轉換方法

#### 轉換方法
```dart
// 從字符串獲取枚舉
static Gender fromString(String value) {
  switch (value) {
    case '男性': return Gender.male;
    case '女性': return Gender.female;
    case '中性': return Gender.neutral;
    default: return Gender.neutral; // 默認為中性
  }
}

// 轉換為存儲字符串
String toStorageString() {
  return displayName;
}
```

### 2. BirthData 模型更新

#### 新增性別欄位
```dart
class BirthData {
  final String id;
  final String name;
  final DateTime birthDate;
  final String birthPlace;
  final String? notes;
  final double latitude;
  final double longitude;
  final DateTime createdAt;
  final ChartCategory category;
  final Gender? gender; // 新增：性別欄位（非必填）
  // ... 其他欄位
}
```

#### 構造函數更新
```dart
BirthData({
  required this.id,
  required this.name,
  required this.birthDate,
  required this.birthPlace,
  this.notes,
  required this.latitude,
  required this.longitude,
  DateTime? createdAt,
  this.category = ChartCategory.personal,
  this.gender, // 性別（非必填）
  // ... 其他參數
})
```

#### JSON 序列化支援
```dart
// 從 JSON 創建
factory BirthData.fromJson(Map<String, dynamic> json) {
  return BirthData(
    // ... 其他欄位
    gender: json['gender'] != null ? Gender.fromString(json['gender'] as String) : null,
    // ... 其他欄位
  );
}

// 轉換為 JSON
Map<String, dynamic> toJson() {
  return {
    // ... 其他欄位
    'gender': gender?.toStorageString(),
    // ... 其他欄位
  };
}
```

#### copyWith 方法更新
```dart
BirthData copyWith({
  // ... 其他參數
  Gender? gender,
  // ... 其他參數
}) {
  return BirthData(
    // ... 其他欄位
    gender: gender ?? this.gender,
    // ... 其他欄位
  );
}
```

### 3. UI 界面設計

#### 性別選擇區域
```dart
Widget _buildGenderSelection() {
  return Row(
    children: [
      // 未選擇選項
      Expanded(
        child: GestureDetector(
          onTap: () => setState(() => _selectedGender = null),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
            decoration: BoxDecoration(
              color: _selectedGender == null
                  ? Colors.grey.withValues(alpha: 0.15)
                  : Colors.grey[100],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: _selectedGender == null
                    ? Colors.grey[600]!
                    : Colors.grey[300]!,
                width: _selectedGender == null ? 2 : 1,
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.help_outline, size: 18),
                const SizedBox(width: 6),
                Text('未選擇'),
              ],
            ),
          ),
        ),
      ),
      
      // 性別選項
      ...Gender.allGenders.map((gender) {
        final isSelected = _selectedGender == gender;
        return Expanded(
          child: GestureDetector(
            onTap: () => setState(() => _selectedGender = gender),
            child: Container(
              margin: const EdgeInsets.only(left: 8),
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
              decoration: BoxDecoration(
                color: isSelected
                    ? gender.color.withValues(alpha: 0.15)
                    : Colors.grey[100],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: isSelected ? gender.color : Colors.grey[300]!,
                  width: isSelected ? 2 : 1,
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(gender.icon, size: 18, color: isSelected ? gender.color : Colors.grey[600]),
                  const SizedBox(width: 6),
                  Text(gender.displayName),
                ],
              ),
            ),
          ),
        );
      }).toList(),
    ],
  );
}
```

#### 界面整合
在基本資料區塊中添加性別選擇：
```dart
Widget _buildBasicInfoSection() {
  return Container(
    child: Column(
      children: [
        // 姓名輸入
        _buildNameInput(),
        
        // 類別選擇
        _buildCategorySelection(),
        
        // 性別選擇（新增）
        const Text('性別（選填）'),
        const SizedBox(height: 8),
        _buildGenderSelection(),
      ],
    ),
  );
}
```

## 設計特點

### 1. 非必填設計
- **可選性**：用戶可以選擇不填寫性別信息
- **未選擇狀態**：提供明確的「未選擇」選項
- **靈活性**：不強制用戶提供性別信息

### 2. 多元化支援
- **三種選項**：男性、女性、中性
- **包容性設計**：支援不同性別認同
- **視覺區分**：每種性別都有獨特的圖標和顏色

### 3. 用戶體驗
- **直觀操作**：點擊選擇，簡單易用
- **視覺反饋**：選中狀態有明顯的視覺標示
- **一致性**：與類別選擇保持相似的設計風格

### 4. 技術兼容
- **向後兼容**：現有數據不受影響
- **存儲優化**：只在有值時才存儲
- **類型安全**：使用枚舉確保數據一致性

## 視覺設計

### 1. 顏色方案
- **男性**：藍色 (`Colors.blue`) - 傳統的男性代表色
- **女性**：粉色 (`Colors.pink`) - 傳統的女性代表色
- **中性**：紫色 (`Colors.purple`) - 中性和包容的顏色
- **未選擇**：灰色 (`Colors.grey`) - 中性的未選擇狀態

### 2. 圖標設計
- **男性**：`Icons.male` - 標準男性符號
- **女性**：`Icons.female` - 標準女性符號
- **中性**：`Icons.transgender` - 跨性別/中性符號
- **未選擇**：`Icons.help_outline` - 問號表示未確定

### 3. 佈局設計
- **水平排列**：四個選項水平排列，平均分配空間
- **圓角設計**：與其他表單元素保持一致的圓角風格
- **邊框變化**：選中狀態有更粗的邊框和對應顏色
- **背景色變化**：選中狀態有淡色背景

## 數據處理

### 1. 狀態管理
```dart
class _BirthDataFormPageState extends State<BirthDataFormPage> {
  Gender? _selectedGender; // 性別狀態（可為 null）
  
  @override
  void initState() {
    super.initState();
    if (widget.initialData != null) {
      _selectedGender = widget.initialData!.gender; // 初始化性別
    }
  }
}
```

### 2. 數據保存
```dart
Future<void> _saveBirthData() async {
  final birthData = BirthData(
    // ... 其他欄位
    gender: _selectedGender, // 保存性別（可為 null）
    // ... 其他欄位
  );
  
  Navigator.of(context).pop(birthData);
}
```

### 3. 數據驗證
- **無需驗證**：性別為非必填項目，不需要驗證
- **類型安全**：使用枚舉確保只能選擇有效值
- **null 安全**：正確處理未選擇狀態

## 使用場景

### 1. 占星分析
- **性別特質**：某些占星解釋可能會考慮性別因素
- **行星影響**：不同性別對某些行星的反應可能不同
- **統計分析**：可以按性別進行統計分析

### 2. 個人化體驗
- **稱謂使用**：根據性別使用適當的稱謂
- **建議內容**：提供性別相關的建議和解釋
- **UI 個人化**：可能的界面個人化選項

### 3. 數據分析
- **人口統計**：了解用戶群體的性別分佈
- **趨勢分析**：分析不同性別的使用模式
- **研究用途**：支援占星學研究和統計

## 隱私和安全

### 1. 數據保護
- **本地存儲**：性別信息只存儲在本地
- **用戶控制**：用戶完全控制是否提供性別信息
- **可修改性**：用戶可以隨時修改或清除性別信息

### 2. 隱私設計
- **非必填**：不強制用戶提供性別信息
- **包容性**：提供中性選項，尊重多元性別認同
- **透明性**：清楚標示為選填項目

## 未來擴展

### 1. 更多選項
- **自定義性別**：允許用戶輸入自定義性別
- **代詞選擇**：選擇偏好的代詞（他/她/它們）
- **性別流動**：支援性別流動的概念

### 2. 功能整合
- **占星解釋**：在星盤解釋中考慮性別因素
- **統計功能**：提供按性別分組的統計圖表
- **個人化建議**：根據性別提供個人化內容

### 3. 國際化
- **多語言支援**：支援不同語言的性別選項
- **文化適應**：根據不同文化調整性別選項
- **本地化**：適應不同地區的性別認知

## 測試驗證

### 1. 功能測試
- ✅ 性別選擇功能正常工作
- ✅ 未選擇狀態正確處理
- ✅ 數據保存和載入正常
- ✅ 表單驗證不受影響

### 2. UI 測試
- ✅ 視覺設計符合預期
- ✅ 選中狀態正確顯示
- ✅ 響應式佈局適配
- ✅ 與其他元素協調一致

### 3. 兼容性測試
- ✅ 向後兼容現有數據
- ✅ JSON 序列化正常
- ✅ 應用編譯成功
- ✅ 不影響其他功能

## 總結

性別欄位的新增為出生資料表單帶來了以下改進：

1. **包容性提升**：支援多元性別認同，體現應用的包容性
2. **用戶友好**：非必填設計給用戶更多選擇自由
3. **功能完整**：為未來的性別相關功能奠定基礎
4. **設計一致**：與現有 UI 風格保持一致
5. **技術穩健**：使用枚舉確保類型安全和數據一致性

這個功能的實現不僅滿足了基本的性別信息收集需求，更重要的是體現了對用戶多樣性的尊重和包容，為構建更加友好和包容的用戶體驗奠定了基礎。
