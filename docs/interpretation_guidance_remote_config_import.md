# 解讀指引 Remote Config 導入功能說明

## 概述

新增了將 Remote Config 指引內容導入到自定義編輯器的功能，讓用戶可以在 Remote Config 指引的基礎上進行個性化修改，而不需要從零開始編寫自定義指引。

## 功能特色

### 1. 自動導入機制
當用戶首次開啟「使用自定義指引」開關時：
- 如果編輯器為空，自動載入 Remote Config 指引作為起始內容
- 如果編輯器已有內容，不會自動覆蓋，保護用戶的工作

### 2. 手動導入功能
提供「帶入 Remote Config 指引」按鈕：
- 用戶可以隨時手動載入 Remote Config 指引
- 如果編輯器已有內容，會先詢問用戶確認是否覆蓋
- 載入後用戶可以在此基礎上進行修改

### 3. 用戶體驗優化
- 清晰的操作提示和確認對話框
- 成功/失敗的即時反饋
- 保護用戶已編輯的內容

## 技術實現

### 1. 自動導入邏輯

```dart
SwitchListTile(
  value: _useCustomGuidance,
  onChanged: (value) async {
    if (value && _customGuidanceController.text.trim().isEmpty) {
      // 當開啟自定義指引且編輯器為空時，帶入 Remote Config 內容
      await _loadRemoteConfigToEditor();
    }
    setState(() {
      _useCustomGuidance = value;
    });
  },
),
```

### 2. 手動導入方法

```dart
/// 將 Remote Config 指引載入到編輯器
Future<void> _loadRemoteConfigToEditor() async {
  try {
    // 檢查是否有未保存的自定義內容
    final currentText = _customGuidanceController.text.trim();
    if (currentText.isNotEmpty) {
      final confirmed = await _showConfirmDialog(
        '覆蓋確認',
        '這將覆蓋您當前的自定義指引內容。確定要繼續嗎？',
      );
      if (!confirmed) return;
    }

    // 載入 Remote Config 指引
    final remoteGuidance = await InterpretationGuidanceService.getRemoteGuidance();
    
    if (mounted) {
      setState(() {
        _customGuidanceController.text = remoteGuidance;
      });
      _showSuccessSnackBar('已載入 Remote Config 指引到編輯器');
    }
  } catch (e) {
    _showErrorSnackBar('載入 Remote Config 指引失敗: $e');
  }
}
```

### 3. UI 組件設計

```dart
// 帶入 Remote Config 按鈕
if (_useCustomGuidance) ...[
  const SizedBox(height: 8),
  TextButton.icon(
    onPressed: _loadRemoteConfigToEditor,
    icon: const Icon(Icons.cloud_download, size: 16),
    label: const Text('帶入 Remote Config 指引'),
    style: TextButton.styleFrom(
      foregroundColor: AppColors.royalIndigo,
    ),
  ),
],
```

## 使用場景

### 1. 新用戶首次使用
1. 用戶開啟「使用自定義指引」
2. 系統自動載入 Remote Config 指引到編輯器
3. 用戶可以在此基礎上進行修改
4. 保存自定義指引

### 2. 現有用戶更新指引
1. 管理員更新了 Remote Config 指引
2. 用戶想要基於新的 Remote Config 內容更新自定義指引
3. 點擊「帶入 Remote Config 指引」按鈕
4. 確認覆蓋現有內容
5. 在新內容基礎上進行修改

### 3. 重新開始編輯
1. 用戶想要重新開始編輯指引
2. 點擊「帶入 Remote Config 指引」按鈕
3. 獲得最新的 Remote Config 內容作為起點
4. 進行個性化修改

## 用戶體驗設計

### 1. 操作流程
```
開啟自定義指引開關
    ↓
編輯器為空？
    ↓ 是
自動載入 Remote Config 指引
    ↓
用戶編輯和修改
    ↓
保存自定義指引
```

### 2. 手動導入流程
```
點擊「帶入 Remote Config 指引」
    ↓
編輯器有內容？
    ↓ 是
顯示確認對話框
    ↓ 確認
載入 Remote Config 指引
    ↓
顯示成功提示
```

### 3. 安全機制
- **內容保護**：有內容時會先確認
- **錯誤處理**：網路錯誤時顯示友好提示
- **狀態反饋**：載入成功/失敗的即時通知

## 界面元素

### 1. 按鈕設計
- **圖標**：`Icons.cloud_download` 表示從雲端下載
- **文字**：「帶入 Remote Config 指引」清楚說明功能
- **顏色**：使用 `AppColors.royalIndigo` 保持一致性
- **位置**：在開關下方，邏輯上相關聯

### 2. 提示文字
更新了自定義指引卡片的提示文字：
```
提示：請至少輸入3行內容，指引將直接用於 AI 解讀。
您可以點擊上方的「帶入 Remote Config 指引」按鈕作為起始內容。
```

### 3. 確認對話框
- **標題**：「覆蓋確認」
- **內容**：「這將覆蓋您當前的自定義指引內容。確定要繼續嗎？」
- **按鈕**：「取消」和「確定」

## 優勢分析

### 1. 降低使用門檻
- 用戶不需要從零開始編寫指引
- 基於專業的 Remote Config 內容進行修改
- 減少了學習和使用成本

### 2. 保持內容品質
- Remote Config 指引通常是經過專業調校的
- 用戶的修改基於高品質的基礎內容
- 避免了完全自由編寫可能產生的品質問題

### 3. 靈活性與一致性平衡
- 保持了自定義的靈活性
- 同時確保了基礎內容的一致性
- 用戶可以根據需要進行個性化調整

### 4. 版本同步便利
- 當 Remote Config 更新時，用戶可以輕鬆獲取新內容
- 支援漸進式更新和個性化保留
- 避免了版本不一致的問題

## 錯誤處理

### 1. 網路錯誤
```dart
try {
  final remoteGuidance = await InterpretationGuidanceService.getRemoteGuidance();
  // 處理成功情況
} catch (e) {
  _showErrorSnackBar('載入 Remote Config 指引失敗: $e');
}
```

### 2. 內容為空
如果 Remote Config 返回空內容，會使用預設指引作為後備。

### 3. 用戶取消操作
當用戶在確認對話框中選擇取消時，操作會被安全中止。

## 測試建議

### 1. 功能測試
- 測試首次開啟自定義指引的自動導入
- 測試手動導入按鈕的功能
- 測試覆蓋確認對話框的行為
- 測試錯誤情況的處理

### 2. 用戶體驗測試
- 測試操作流程的直觀性
- 測試提示文字的清晰度
- 測試確認對話框的理解度

### 3. 邊界情況測試
- Remote Config 內容為空
- 網路連接失敗
- 用戶快速重複操作
- 編輯器內容過長

## 未來擴展

### 1. 版本比較
- 顯示當前自定義指引與 Remote Config 的差異
- 提供合併功能
- 支援選擇性更新

### 2. 模板系統
- 提供多種指引模板
- 支援模板的組合和自定義
- 建立指引模板庫

### 3. 協作功能
- 支援指引的分享和導入
- 團隊內的指引同步
- 指引版本的協作編輯

這個功能大大提升了自定義指引的易用性，讓用戶可以在專業內容的基礎上進行個性化調整，既保證了內容品質，又提供了充分的靈活性。
