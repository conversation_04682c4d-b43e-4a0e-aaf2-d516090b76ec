# DualChartPainter 和 FirdariaChartPainter 動態字體縮放實作文件

## 概述
本文件說明在 `DualChartPainter` 和 `FirdariaChartPainter` 中實現動態字體縮放和開發模式調試信息的過程，確保所有星盤繪製器都具有一致的字體縮放行為和調試功能。

## 實作目標
- 將動態字體縮放系統擴展到所有 CustomPainter
- 統一使用 ChartTextStyles 管理字體樣式
- 在開發模式下顯示星盤尺寸調試信息
- 保持各種星盤類型的一致性體驗
- 提供詳細的調試信息幫助開發

## 技術實現

### 1. DualChartPainter 修改

#### 導入動態字體管理
```dart
import '../../utils/chart_text_styles.dart';
```

#### 宮位數字樣式更新
```dart
// 修改前
void _drawHouseNumbers(Canvas canvas, Offset center, double radius) {
  const houseTextStyle = TextStyle(
    color: Colors.black,
    fontSize: 12,
    fontWeight: FontWeight.bold,
  );
  // ...
}

// 修改後
void _drawHouseNumbers(Canvas canvas, Offset center, double radius) {
  final chartSize = radius * 2; // 計算星盤大小
  final houseTextStyle = ChartTextStyles.getHouseNumberStyle(chartSize);
  // ...
}
```

#### 星座符號樣式更新
```dart
// 修改前
final zodiacTextStyle = TextStyle(
  color: ZodiacSymbols.getZodiacColor(signs[i]),
  fontSize: 16,
  fontWeight: FontWeight.bold,
  fontFeatures: [FontFeature.disable('liga')],
  fontFamily: 'astro_one_font'
);

// 修改後
final chartSize = radius * 2; // 計算星盤大小
final zodiacTextStyle = ChartTextStyles.getZodiacSymbolStyle(
    chartSize, ZodiacSymbols.getZodiacColor(signs[i]));
```

#### 相位符號樣式更新
```dart
// 修改前
final aspectTextStyle = TextStyle(
  color: ZodiacSymbols.getAspectColor(aspect.symbol),
  fontSize: 14,
  fontFamily: 'astro_one_font',
);

// 修改後
final chartSize = radius * 2; // 計算星盤大小
final aspectTextStyle = ChartTextStyles.getAspectSymbolStyle(
    chartSize, ZodiacSymbols.getAspectColor(aspect.symbol));
```

### 2. FirdariaChartPainter 修改

#### 導入動態字體管理
```dart
import '../../utils/chart_text_styles.dart';
```

#### 相同的樣式更新模式
- 宮位數字：使用 `ChartTextStyles.getHouseNumberStyle(chartSize)`
- 星座符號：使用 `ChartTextStyles.getZodiacSymbolStyle(chartSize, color)`
- 相位符號：使用 `ChartTextStyles.getAspectSymbolStyle(chartSize, color)`

### 3. 開發模式調試信息

#### DualChartPainter 調試信息
```dart
@override
void paint(Canvas canvas, Size size) {
  // ... 原有繪製邏輯
  
  // 開發模式：顯示星盤尺寸調試信息
  if (kDebugMode) {
    _drawDebugInfo(canvas, center, radius, size);
  }
}

void _drawDebugInfo(Canvas canvas, Offset center, double radius, Size size) {
  final chartSize = radius * 2;
  final fontScale = ChartTextStyles.getFontScale(chartSize);
  
  final debugInfo = [
    'DualChart Debug Info',
    'Screen: ${size.width.toInt()}×${size.height.toInt()}',
    'Chart: ${chartSize.toInt()}×${chartSize.toInt()}',
    'Font Scale: ${fontScale.toStringAsFixed(2)}',
    'Width Usage: ${((chartSize / size.width) * 100).toStringAsFixed(1)}%',
    'Height Usage: ${((chartSize / size.height) * 100).toStringAsFixed(1)}%',
    'Natal Planets: ${natalPlanets.length}',
    'Transit Planets: ${transitPlanets.length}',
  ];
  
  // 繪製調試信息到左上角
}
```

#### FirdariaChartPainter 調試信息
```dart
void _drawDebugInfo(Canvas canvas, Offset center, double radius, Size size) {
  final chartSize = radius * 2;
  final fontScale = ChartTextStyles.getFontScale(chartSize);
  
  final debugInfo = [
    'FirdariaChart Debug Info',
    'Screen: ${size.width.toInt()}×${size.height.toInt()}',
    'Chart: ${chartSize.toInt()}×${chartSize.toInt()}',
    'Font Scale: ${fontScale.toStringAsFixed(2)}',
    'Width Usage: ${((chartSize / size.width) * 100).toStringAsFixed(1)}%',
    'Height Usage: ${((chartSize / size.height) * 100).toStringAsFixed(1)}%',
  ];
  
  // 繪製調試信息到左上角
}
```

## 調試信息設計

### 1. 顯示內容
每個 painter 都顯示以下調試信息：

**通用信息**：
- Painter 類型標識
- 螢幕尺寸（寬×高）
- 星盤尺寸（直徑×直徑）
- 字體縮放比例
- 寬度利用率百分比
- 高度利用率百分比

**特定信息**：
- DualChart：本命盤行星數量、行運盤行星數量
- FirdariaChart：法達盤特有信息

### 2. 視覺設計
```dart
final debugTextStyle = TextStyle(
  color: Colors.orange[700],        // 橙色文字
  fontSize: 10,                     // 小字體
  fontWeight: FontWeight.w600,      // 半粗體
  backgroundColor: Colors.white.withValues(alpha: 0.8), // 半透明白色背景
);
```

### 3. 位置佈局
- **位置**：左上角，距離邊緣 10px
- **間距**：每行間距 15px
- **背景**：半透明白色背景確保可讀性
- **顏色**：橙色文字突出調試性質

## 字體縮放效果

### 1. 不同星盤類型的縮放表現

#### 單一星盤（ChartPainter）
```
400px 星盤：
- 宮位數字: 12px
- 星座符號: 16px
- 相位符號: 14px

600px 星盤：
- 宮位數字: 18px (達到最大限制)
- 星座符號: 24px (達到最大限制)
- 相位符號: 20px (達到最大限制)
```

#### 雙重星盤（DualChartPainter）
```
400px 星盤：
- 本命盤和行運盤使用相同的字體大小
- 保持視覺一致性

600px 星盤：
- 兩個星盤都獲得相同的字體放大
- 確保可讀性提升
```

#### 法達盤（FirdariaChartPainter）
```
400px 星盤：
- 基本星盤元素使用標準字體
- 法達盤特有元素也遵循縮放規則

600px 星盤：
- 所有元素統一放大
- 複雜的法達盤信息更易閱讀
```

### 2. 一致性保證
所有 painter 都使用相同的：
- 縮放算法：`ChartTextStyles._getFontScale()`
- 基準大小：400px
- 放大閾值：500px
- 最大最小限制：`clamp()` 方法

## 開發體驗改善

### 1. 即時調試信息
開發者可以在開發模式下即時看到：
- 當前星盤的實際尺寸
- 字體縮放比例
- 螢幕利用率
- 特定 painter 的額外信息

### 2. 性能監控
通過調試信息可以：
- 監控不同設備上的星盤大小
- 驗證字體縮放是否正確
- 檢查螢幕空間利用效率
- 對比不同星盤類型的表現

### 3. 開發效率
- **無需額外工具**：直接在應用中查看調試信息
- **實時更新**：螢幕旋轉或尺寸變化時自動更新
- **視覺化數據**：直觀的數據展示

## 技術優勢

### 1. 統一管理
- **一致性**：所有 painter 使用相同的字體管理系統
- **可維護性**：修改字體規則只需要在一個地方
- **擴展性**：新增 painter 可以輕鬆整合

### 2. 調試友好
- **開發模式限定**：生產環境不會顯示調試信息
- **詳細信息**：提供足夠的信息進行問題診斷
- **非侵入性**：不影響正常的星盤顯示

### 3. 性能優化
- **條件編譯**：使用 `kDebugMode` 確保生產環境無開銷
- **輕量級**：調試信息繪製對性能影響最小
- **動態生成**：不緩存調試信息，確保實時性

## 測試驗證

### 1. 功能測試
- ✅ DualChartPainter 字體縮放正常
- ✅ FirdariaChartPainter 字體縮放正常
- ✅ 開發模式調試信息正確顯示
- ✅ 生產模式不顯示調試信息

### 2. 一致性測試
- ✅ 所有 painter 使用相同的縮放算法
- ✅ 相同星盤大小下字體大小一致
- ✅ 調試信息格式統一

### 3. 視覺測試
- ✅ 大螢幕上字體適當放大
- ✅ 小螢幕上字體不會太小
- ✅ 調試信息不干擾星盤顯示

### 4. 性能測試
- ✅ 字體縮放不影響繪製性能
- ✅ 調試信息繪製開銷最小
- ✅ 生產環境性能無影響

## 未來擴展

### 1. 更多調試信息
- **繪製時間**：顯示星盤繪製耗時
- **記憶體使用**：顯示 painter 記憶體佔用
- **行星位置**：顯示行星的精確位置信息

### 2. 交互式調試
- **點擊切換**：點擊調試信息切換顯示內容
- **拖拽移動**：允許拖拽調試信息到不同位置
- **複製功能**：長按複製調試數據

### 3. 高級分析
- **性能分析**：分析不同 painter 的性能差異
- **設備適配**：收集不同設備的表現數據
- **自動優化**：根據設備特性自動調整參數

## 總結

DualChartPainter 和 FirdariaChartPainter 的動態字體縮放和調試信息實現帶來了以下改進：

1. **統一體驗**：所有星盤類型都具有一致的字體縮放行為
2. **開發效率**：豐富的調試信息幫助開發者快速定位問題
3. **可讀性提升**：大螢幕設備上的字體自動放大
4. **維護簡化**：統一的字體管理系統降低維護成本
5. **調試便利**：即時的尺寸和縮放信息

這個實現確保了整個星盤系統的一致性，無論是單一星盤、雙重星盤還是法達盤，用戶都能獲得最佳的可讀性體驗，同時為開發者提供了強大的調試工具。
