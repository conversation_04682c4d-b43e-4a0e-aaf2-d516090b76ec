# 解讀指引 Remote Config 功能說明

## 概述

實現了解讀指引的 Remote Config 管理功能，允許從遠端配置獲取解讀指引，並在設定頁面提供自定義選項。這個功能提供了靈活的解讀指引管理，支援預設值、Remote Config 和自定義指引三種來源。

## 功能特色

### 1. 多層次指引來源
- **自定義指引**：用戶可以設定自己的解讀指引
- **Remote Config**：從 Firebase Remote Config 獲取遠端配置
- **預設指引**：內建的預設解讀指引作為後備

### 2. 優先級順序
```
自定義指引（如果啟用）
    ↓
Remote Config 指引
    ↓
預設指引
```

### 3. 設定頁面功能
- 查看當前指引來源和狀態
- 切換使用自定義指引
- 編輯自定義指引內容
- 預覽 Remote Config 和預設指引
- 從 Remote Config 同步
- 重置為預設設定

## 技術實現

### 1. InterpretationGuidanceService

#### 核心方法
```dart
/// 獲取解讀指引（主要方法）
static Future<String> getGuidance() async {
  // 1. 檢查是否使用自定義指引
  // 2. 嘗試從 Remote Config 獲取
  // 3. 返回預設指引
}

/// 保存自定義指引
static Future<bool> saveCustomGuidance(String guidance)

/// 設置是否使用自定義指引
static Future<bool> setUseCustomGuidance(bool useCustom)

/// 從 Remote Config 獲取指引
static Future<String> getRemoteGuidance()
```

#### 配置鍵值
- `interpretation_guidance`：Remote Config 中的指引鍵
- `custom_interpretation_guidance`：本地自定義指引鍵
- `use_custom_interpretation_guidance`：是否使用自定義指引標記

### 2. AI API Service 整合

#### 修改前
```dart
// 解讀指引
buffer.writeln('請根據上述星盤資訊進行專業占星解讀，並遵守以下指引：');
buffer.writeln('- 回答請使用繁體中文');
// ... 硬編碼的指引內容
```

#### 修改後
```dart
// 解讀指引 - 從配置服務獲取
final guidance = await InterpretationGuidanceService.getGuidance();
buffer.writeln(guidance);
```

### 3. 設定頁面 UI

#### 頁面結構
```
AppBar（同步、重置按鈕）
├── 當前狀態卡片
├── 指引來源選擇
├── 自定義指引編輯（條件顯示）
├── Remote Config 指引預覽
├── 預設指引預覽
└── 保存按鈕（FloatingActionButton）
```

#### 主要功能
- **狀態顯示**：當前指引來源和使用狀態
- **來源切換**：SwitchListTile 切換自定義/遠端指引
- **指引編輯**：多行文字輸入框，支援驗證
- **預覽功能**：顯示不同來源指引的預覽
- **同步功能**：從 Remote Config 重新獲取
- **重置功能**：清除自定義設定

## Remote Config 設定

### 1. Firebase Console 設定

在 Firebase Console 的 Remote Config 中添加：

```json
{
  "interpretation_guidance": {
    "value": "請根據上述星盤資訊進行專業占星解讀，並遵守以下指引：\n- 回答請使用繁體中文\n- 使用技法：古典占星\n- 分析需具體且具實用性，避免空泛推論\n- 結合行星、星座與宮位的整體影響進行解釋\n- 採用清晰、親切、易於理解的語言，避免使用艱澀術語\n- 若有提供行星相位，請同時分析其影響\n- 禁用以下內容：\n開場白（如「你好，讓我們來看看你的星盤」）\n讚美詞（如「你是一個很有魅力的人」）\n主觀性描述（如「你的內心充滿夢想」）\n表情符號（如🌞、🌙 等）"
  }
}
```

### 2. 條件配置

可以根據不同條件設定不同的指引：
- **用戶群組**：專業用戶 vs 一般用戶
- **應用版本**：不同版本使用不同指引
- **地區**：不同地區的本地化指引
- **A/B 測試**：測試不同指引的效果

### 3. 更新策略

```dart
// 設定 Remote Config 的獲取間隔
final remoteConfig = FirebaseRemoteConfig.instance;
await remoteConfig.setConfigSettings(RemoteConfigSettings(
  fetchTimeout: const Duration(minutes: 1),
  minimumFetchInterval: const Duration(hours: 1),
));
```

## 使用流程

### 1. 管理員操作流程
1. 在 Firebase Console 更新 Remote Config 中的 `interpretation_guidance`
2. 發布配置更新
3. 用戶端會在下次啟動或手動同步時獲取新指引

### 2. 用戶操作流程
1. 進入設定頁面 → 解讀指引設定
2. 查看當前指引來源和內容
3. 選擇使用自定義指引或遠端指引
4. 如果使用自定義，編輯指引內容
5. 保存設定

### 3. 開發者操作流程
1. 修改預設指引：更新 `InterpretationGuidanceService.defaultGuidance`
2. 測試 Remote Config：在 Firebase Console 設定測試值
3. 驗證指引格式：使用 `validateGuidance()` 方法

## 錯誤處理

### 1. Remote Config 失敗
- 網路連接問題：自動降級到預設指引
- 配置格式錯誤：記錄錯誤並使用預設指引
- 獲取超時：使用快取或預設指引

### 2. 自定義指引驗證
- 空內容檢查：至少需要 3 行內容
- 格式驗證：確保指引結構合理
- 長度限制：避免過長的指引影響性能

### 3. 儲存失敗
- SharedPreferences 錯誤：顯示錯誤訊息
- 權限問題：提供重試選項
- 空間不足：清理舊資料

## 監控和分析

### 1. 使用統計
- 指引來源分佈（自定義 vs Remote Config vs 預設）
- 自定義指引的使用率
- Remote Config 同步成功率

### 2. 效果分析
- 不同指引對 AI 回應品質的影響
- 用戶滿意度與指引類型的關聯
- A/B 測試結果分析

### 3. 錯誤監控
- Remote Config 獲取失敗率
- 指引驗證失敗次數
- 用戶設定保存失敗率

## 最佳實踐

### 1. 指引內容設計
- **清晰明確**：避免模糊的表達
- **結構化**：使用條列式或分段式
- **可測試**：設計可驗證效果的指引
- **版本控制**：記錄指引變更歷史

### 2. Remote Config 管理
- **漸進式發布**：先小範圍測試再全面發布
- **回滾準備**：保留上一版本的指引
- **監控指標**：設定關鍵指標監控
- **文檔記錄**：記錄每次更新的原因和效果

### 3. 用戶體驗
- **預覽功能**：讓用戶了解不同指引的差異
- **簡單操作**：提供一鍵重置和同步功能
- **即時反饋**：保存和同步操作的即時狀態顯示
- **幫助說明**：提供指引設定的使用說明

## 未來擴展

### 1. 進階功能
- **指引模板**：提供多種預設模板供選擇
- **指引分享**：用戶間分享自定義指引
- **版本歷史**：查看和恢復歷史版本
- **批量管理**：管理員批量更新用戶指引

### 2. 智能化
- **AI 優化建議**：根據使用效果建議指引優化
- **自動調整**：根據用戶反饋自動調整指引
- **個性化推薦**：根據用戶偏好推薦合適的指引
- **效果預測**：預測指引變更對結果的影響

這個功能為解讀指引提供了靈活的管理方式，支援遠端配置和本地自定義，大大提升了系統的可維護性和用戶體驗。
