# Web 版本部署快取解決方案

## 問題描述

Flutter Web 應用部署到 Firebase Hosting 後，用戶瀏覽器可能會快取舊版本，導致無法立即看到新功能。這個問題主要由以下原因造成：

1. **瀏覽器快取**：瀏覽器快取了 HTML、CSS、JS 檔案
2. **Service Worker 快取**：Flutter 的 Service Worker 快取了應用資源
3. **CDN 快取**：Firebase Hosting 的 CDN 可能快取了舊版本
4. **版本檢查延遲**：版本檢查機制可能無法及時觸發更新通知

## 解決方案

### 1. 增強版本檢查系統

**檔案：`web/simple_version_check.js`**
- 更頻繁的版本檢查（3分鐘一次）
- 更強的快取破壞策略
- 智能更新通知（區分重要更新和一般更新）
- 強制快取清除功能

**主要改進：**
```javascript
// 使用更強的快取破壞策略
const cacheBuster = Date.now() + '_' + Math.random().toString(36).substr(2, 9);
fetch(`/version.json?_=${cacheBuster}`, {
  cache: 'no-cache',
  headers: {
    'Cache-Control': 'no-cache, no-store, must-revalidate',
    'Pragma': 'no-cache',
    'Expires': '0'
  }
})
```

### 2. 增強版 Service Worker

**檔案：`web/sw_enhanced.js`**
- 版本化快取名稱
- 自動清除舊版本快取
- 網路優先策略用於關鍵檔案
- 更好的錯誤處理

**主要特性：**
```javascript
const CACHE_NAME = 'astreal-cache-{{BUILD_VERSION}}';

// 不快取的關鍵檔案
const NETWORK_ONLY_URLS = [
  '/version.json',
  '/simple_version_check.js',
  '/sw_enhanced.js'
];
```

### 3. 優化的 Firebase Hosting 配置

**檔案：`firebase.json`**
- 針對不同檔案類型的精確快取策略
- 版本檢查檔案完全不快取
- 靜態資源適度快取（1小時）

### 4. 智能部署腳本

#### 一般部署：`scripts/deploy_web.sh`
```bash
# 一般部署
./scripts/deploy_web.sh

# 重要更新部署（會觸發更新通知）
./scripts/deploy_web.sh --notify-update

# 指定版本號部署
./scripts/deploy_web.sh --version=1.2.0

# 強制清除快取部署


```

#### 強制更新部署：`scripts/deploy_web_force_update.sh`
```bash
# 強制更新部署（解決嚴重快取問題）
./scripts/deploy_web_force_update.sh
```

## 使用方法

### 日常部署
對於一般的功能更新或錯誤修復：
```bash
./scripts/deploy_web.sh
```

### 重要更新
對於重要功能或安全更新：
```bash
./scripts/deploy_web.sh --notify-update
```

### 解決快取問題
當用戶反映看不到新版本時：
```bash
./scripts/deploy_web_force_update.sh
```

## 版本檢查機制

### 版本號格式
- **時間戳版本**：`20241225_143022`（日常部署）
- **重要更新版本**：`20241225_143022_update`（重要更新）
- **強制更新版本**：`20241225_143022_force_update`（解決快取問題）

### 更新通知邏輯
1. **一般更新**：藍色通知，可選擇稍後更新
2. **重要更新**：紅色通知，建議立即更新
3. **強制更新**：紅色通知，只有立即更新選項

### version.json 結構
```json
{
  "version": "20241225_143022_update",
  "buildTime": "2024-12-25T14:30:22Z",
  "assetVersion": "20241225143022",
  "description": "AstReal 應用",
  "forceCacheClear": true,
  "updateType": "important",
  "forceUpdate": false,
  "updateMessage": "重要更新已發布..."
}
```

## 用戶端快取清除

### 自動清除
版本檢查器會自動：
1. 清除所有 Cache API 快取
2. 清除 localStorage 和 sessionStorage（保留重要數據）
3. 使用時間戳強制重新載入頁面

### 手動清除
用戶可以手動：
1. **硬重新整理**：`Ctrl+Shift+R` (Windows/Linux) 或 `Cmd+Shift+R` (Mac)
2. **無痕模式**：開啟無痕/私人瀏覽模式
3. **清除瀏覽器資料**：清除網站資料和快取

## 監控和除錯

### 瀏覽器控制台日誌
版本檢查器會輸出詳細日誌：
```
🚀 Version checker initialized. Current version: 20241225_143022
🔍 Checking version (attempt 1)...
📊 Server version: 20241225_143022_update Current: 20241225_143022
🔔 Showing update notification for version: 20241225_143022_update
```

### 檢查版本狀態
在瀏覽器控制台執行：
```javascript
// 檢查當前版本
console.log('Current version:', document.querySelector('meta[name="version"]').content);

// 手動檢查更新
fetch('/version.json?_=' + Date.now()).then(r => r.json()).then(console.log);

// 清除所有快取
caches.keys().then(names => Promise.all(names.map(name => caches.delete(name))));
```

## 最佳實踐

### 部署前檢查
1. 確保所有更改已提交到 Git
2. 測試本地建置是否正常
3. 選擇適當的部署類型

### 部署後驗證
1. 檢查 version.json 是否更新
2. 使用無痕模式測試新功能
3. 監控用戶反饋

### 緊急情況處理
如果用戶大量反映看不到新版本：
1. 立即執行強制更新部署
2. 通知用戶清除瀏覽器快取
3. 考慮使用不同的版本號格式

## 故障排除

### 常見問題

**Q: 部署後用戶仍看到舊版本**
A: 執行 `./scripts/deploy_web_force_update.sh` 強制更新

**Q: 版本檢查器沒有顯示更新通知**
A: 檢查瀏覽器控制台是否有錯誤，確認 version.json 可以正常訪問

**Q: Service Worker 沒有更新**
A: 檢查 sw_enhanced.js 是否正確部署，版本號是否更新

**Q: 更新通知顯示但點擊後沒有更新**
A: 檢查快取清除邏輯，可能需要手動清除瀏覽器快取

### 除錯步驟
1. 檢查 Firebase Hosting 部署狀態
2. 驗證 version.json 內容
3. 檢查瀏覽器控制台日誌
4. 測試 Service Worker 狀態
5. 驗證快取策略設定

## 總結

這個解決方案提供了完整的 Web 版本更新機制，能夠有效解決瀏覽器快取問題，確保用戶能夠及時獲得最新版本的應用。通過智能的版本檢查、強制快取清除和用戶友好的更新通知，大大改善了 Web 應用的更新體驗。
