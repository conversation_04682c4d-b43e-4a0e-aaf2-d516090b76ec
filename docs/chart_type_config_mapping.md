# 根據星盤類型選擇配置檔案

## 📋 功能概述

成功實作了根據星盤類型自動選擇對應配置檔案的系統，為每種星盤類型提供專門的解讀選項配置，確保解讀內容的專業性和針對性。

## 🎯 實作內容

### 1. InterpretationConfigService 更新
**檔案位置**：`lib/services/interpretation_config_service.dart`

**核心改進**：
- 將原本的 switch 語句重構為專門的 `_getConfigPath()` 方法
- 支援所有 ChartType 枚舉中定義的星盤類型
- 提供完整的配置檔案映射邏輯

**新增方法**：
```dart
/// 根據星盤類型獲取配置檔案路徑
String _getConfigPath(ChartType chartType) {
  switch (chartType) {
    // 個人星盤
    case ChartType.natal:
      return 'assets/config/natal_interpretation_options.json';

    // 預測類星盤
    case ChartType.transit:
      return 'assets/config/transit_interpretation_options.json';
    case ChartType.secondaryProgression:
      return 'assets/config/secondary_progression_interpretation_options.json';
    case ChartType.tertiaryProgression:
      return 'assets/config/tertiary_progression_interpretation_options.json';
    case ChartType.solarArcDirection:
      return 'assets/config/solar_arc_interpretation_options.json';

    // 返照盤類
    case ChartType.solarReturn:
      return 'assets/config/solar_return_interpretation_options.json';
    case ChartType.lunarReturn:
      return 'assets/config/lunar_return_interpretation_options.json';

    // 合盤類
    case ChartType.synastry:
    case ChartType.composite:
    case ChartType.davison:
    case ChartType.marks:
      return 'assets/config/relationship_interpretation_options.json';

    // 事件占星
    case ChartType.horary:
      return 'assets/config/horary_interpretation_options.json';
    case ChartType.event:
      return 'assets/config/event_interpretation_options.json';

    // 特殊星盤
    case ChartType.mundane:
      return 'assets/config/mundane_interpretation_options.json';
    case ChartType.firdaria:
      return 'assets/config/firdaria_interpretation_options.json';
    case ChartType.profection:
      return 'assets/config/profection_interpretation_options.json';

    // 季節節氣星盤
    case ChartType.equinoxSolstice:
      return 'assets/config/equinox_solstice_interpretation_options.json';

    // 日月蝕星盤
    case ChartType.eclipse:
      return 'assets/config/eclipse_interpretation_options.json';
    case ChartType.conjunctionJupiterSaturn:
      return 'assets/config/jupiter_saturn_conjunction_interpretation_options.json';
    case ChartType.conjunctionMarsSaturn:
      return 'assets/config/mars_saturn_conjunction_interpretation_options.json';

    // 預設
    default:
      return 'assets/config/general_interpretation_options.json';
  }
}
```

### 2. 新增配置檔案

#### 預測類星盤配置
- **次限推運盤**：`secondary_progression_interpretation_options.json`
  - 推運總覽、推運太陽、推運月亮、推運相位等8個選項
  - 重點分析內在成長和心理發展

- **三限推運盤**：`tertiary_progression_interpretation_options.json`
  - 月度主題、情感週期、心智活動、日常節奏等8個選項
  - 重點分析短期心理變化和適應

- **太陽弧推運盤**：`solar_arc_interpretation_options.json`
  - 太陽弧總覽、重大人生事件、太陽弧行星等9個選項
  - 重點分析人生重大轉折和轉化

#### 古典占星配置
- **小限法**：`profection_interpretation_options.json`
  - 年度小限、時主星分析、宮位主題、小限週期等8個選項
  - 重點分析年度主題和古典預測技術

#### 事件占星配置
- **卜卦占星**：`horary_interpretation_options.json`
  - 問題分析、盤面有效性、象徵星分析、最終判斷等8個選項
  - 重點分析具體問題的占星學答案

- **事件占星**：`event_interpretation_options.json`
  - 事件意義、行星配置、軸角宮位、個人關聯等8個選項
  - 重點分析重要事件的占星學意義

#### 世俗占星配置
- **世俗占星**：`mundane_interpretation_options.json`
  - 國家星盤、政治趨勢、經濟預測、集體命運等8個選項
  - 重點分析國家和集體層面的占星學

#### 特殊會合配置
- **木土會合盤**：`jupiter_saturn_conjunction_interpretation_options.json`
  - 大會合意義、元素轉換、社會結構、個人適應等8個選項
  - 重點分析20年週期的時代變革

- **火土會合盤**：`mars_saturn_conjunction_interpretation_options.json`
  - 行動與限制、壓力與耐力、紀律行動、精通成就等8個選項
  - 重點分析意志與限制的整合

### 3. 配置檔案結構標準化

**統一格式**：
```json
{
  "version": "1.0.0",
  "lastUpdated": "2025-06-23",
  "description": "星盤類型解讀選項配置檔案",
  "chartType": "chartTypeId",
  "options": [
    {
      "id": "unique_option_id",
      "title": "選項標題",
      "subtitle": "選項副標題",
      "icon": "material_icon_name",
      "color": "color_key",
      "keyPoint": "AI 解讀重點指導",
      "questions": ["建議問題1", "建議問題2"],
      "enabled": true,
      "order": 1
    }
  ],
  "colorMapping": {
    "color_key": "#HEX_COLOR"
  },
  "iconMapping": {
    "icon_name": "Icons.icon_name"
  }
}
```

**設計原則**：
- 每個星盤類型8個核心解讀選項
- 專業的 keyPoint 指導 AI 分析重點
- 豐富的建議問題幫助用戶深入探索
- 統一的顏色和圖標映射系統

## 🎨 星盤類型分類

### 個人星盤
- **本命盤**：個人性格、天賦、命運分析

### 預測類星盤
- **行運盤**：當下行星影響
- **次限推運盤**：內在心理成長（一天=一年）
- **三限推運盤**：短期心理變化（一天=一月）
- **太陽弧推運盤**：重大人生轉折

### 返照盤類
- **太陽返照盤**：年度運勢分析
- **月亮返照盤**：月度運勢分析

### 合盤類
- **比較盤**：兩人關係分析
- **組合盤**：關係本質分析
- **時空中點盤**：關係命運分析
- **馬克思盤**：關係動力分析

### 事件占星
- **卜卦占星**：具體問題解答
- **事件占星**：重要事件分析

### 特殊星盤
- **世俗占星**：國家集體分析
- **法達盤**：古典時序技術
- **小限法**：古典年度預測
- **日月蝕盤**：命運轉折點
- **木土會合盤**：時代變革
- **火土會合盤**：意志試煉

## 🔧 技術實作

### 1. 自動配置選擇
```dart
// 使用方式
final config = await InterpretationConfigService.instance.loadConfig(ChartType.natal);
final options = config.enabledOptions;
```

### 2. 快取機制
- 配置檔案載入後自動快取
- 避免重複讀取提升性能
- 支援快取清除和更新

### 3. 錯誤處理
- 配置檔案載入失敗時使用預設配置
- 完整的錯誤日誌記錄
- 優雅的降級處理

### 4. 擴展性設計
- 新增星盤類型只需添加配置檔案
- 統一的配置格式便於維護
- 支援版本控制和更新

## 📊 配置檔案映射表

| 星盤類型 | 配置檔案 | 主要特色 |
|----------|----------|----------|
| natal | natal_interpretation_options.json | 12個人生領域分析 |
| transit | transit_interpretation_options.json | 當下行星影響 |
| secondaryProgression | secondary_progression_interpretation_options.json | 內在成長分析 |
| tertiaryProgression | tertiary_progression_interpretation_options.json | 短期變化分析 |
| solarArcDirection | solar_arc_interpretation_options.json | 重大轉折分析 |
| solarReturn | solar_return_interpretation_options.json | 年度運勢分析 |
| lunarReturn | lunar_return_interpretation_options.json | 月度運勢分析 |
| synastry/composite/davison/marks | relationship_interpretation_options.json | 關係分析 |
| horary | horary_interpretation_options.json | 卜卦問題解答 |
| event | event_interpretation_options.json | 事件意義分析 |
| mundane | mundane_interpretation_options.json | 世俗占星分析 |
| firdaria | firdaria_interpretation_options.json | 法達時序分析 |
| profection | profection_interpretation_options.json | 小限法分析 |
| eclipse | eclipse_interpretation_options.json | 日月蝕分析 |
| equinoxSolstice | equinox_solstice_interpretation_options.json | 節氣分析 |
| conjunctionJupiterSaturn | jupiter_saturn_conjunction_interpretation_options.json | 木土會合分析 |
| conjunctionMarsSaturn | mars_saturn_conjunction_interpretation_options.json | 火土會合分析 |
| default | general_interpretation_options.json | 通用分析 |

## 🚀 使用效果

### 1. 專業性提升
- 每種星盤類型都有專門的解讀選項
- AI 分析更加精準和專業
- 用戶獲得更有針對性的洞察

### 2. 用戶體驗優化
- 根據星盤類型自動調整解讀選項
- 無需手動選擇，系統智能匹配
- 一致的操作體驗

### 3. 系統可維護性
- 配置與代碼分離，易於更新
- 統一的配置格式便於管理
- 支援動態配置更新

### 4. 擴展性保證
- 新增星盤類型只需添加配置檔案
- 不需要修改核心代碼
- 支援未來功能擴展

## 📈 商業價值

### 1. 專業度提升
- 每種星盤類型都有專業的解讀框架
- 提升應用的占星學專業性
- 增強用戶信任和滿意度

### 2. 內容豐富度
- 18種不同的星盤類型配置
- 144個專業解讀選項（18×8）
- 豐富的解讀內容和角度

### 3. 用戶留存
- 專業的解讀內容增加用戶黏性
- 不同星盤類型提供持續探索價值
- 滿足不同層次用戶需求

### 4. 技術優勢
- 靈活的配置系統
- 易於維護和擴展
- 為未來發展奠定基礎

## 📝 總結

### 主要成就
- ✅ 完整實作了根據星盤類型選擇配置檔案的系統
- ✅ 為18種星盤類型創建了專門的配置檔案
- ✅ 建立了統一的配置格式和管理機制
- ✅ 提供了144個專業的解讀選項

### 技術優勢
- **自動化**：根據星盤類型自動選擇配置
- **專業化**：每種星盤類型都有專門的解讀框架
- **標準化**：統一的配置格式和結構
- **可擴展**：易於添加新的星盤類型和配置

### 用戶價值
- **專業性**：獲得針對性的專業解讀
- **豐富性**：多樣化的解讀選項和角度
- **便利性**：自動匹配，無需手動選擇
- **一致性**：統一的操作體驗

這個實作為應用的專業性和用戶體驗帶來了顯著提升，建立了完整的星盤類型配置管理系統，為未來的功能擴展奠定了堅實的基礎。
