# 完整用戶數據初始化功能說明

## 概述

實現了完整的用戶數據初始化系統，在用戶註冊會員或匿名登入時，自動在 Firebase 中創建所有必要的用戶數據結構，包括免費試用記錄、用戶檔案、設定、偏好和支付數據。

## 功能架構

### 1. UserDataInitializationService
新創建的專門服務，負責協調所有用戶數據的初始化：

```dart
class UserDataInitializationService {
  /// 初始化新用戶的完整數據
  static Future<void> initializeNewUserData(String userId, {
    String? email,
    String? displayName,
    bool isAnonymous = false,
  }) async {
    // 並行初始化各種數據
    final futures = <Future<void>>[
      _initializeFreeTrialRecord(userId),
      _initializeUserProfile(userId, ...),
      _initializeUserSettings(userId),
      _initializeUserPreferences(userId),
      _initializePaymentData(userId),
    ];
    
    await Future.wait(futures);
  }
}
```

### 2. 初始化的數據類型

#### A. 免費試用記錄 (`user_free_trial`)
```json
{
  "used_count": 0,
  "created_at": "timestamp",
  "updated_at": "timestamp"
}
```

#### B. 用戶檔案 (`user_profiles`)
```json
{
  "user_id": "string",
  "email": "string|null",
  "display_name": "string",
  "is_anonymous": "boolean",
  "created_at": "timestamp",
  "updated_at": "timestamp",
  "profile_completed": false,
  "last_login_at": "timestamp",
  "login_count": 1
}
```

#### C. 用戶設定 (`user_settings`)
```json
{
  "user_id": "string",
  "language": "zh-TW",
  "theme": "modern",
  "user_mode": "starlight",
  "notifications_enabled": true,
  "email_notifications": true,
  "push_notifications": true,
  "auto_save": true,
  "created_at": "timestamp",
  "updated_at": "timestamp"
}
```

#### D. 用戶偏好 (`user_preferences`)
```json
{
  "user_id": "string",
  "chart_display_preferences": {
    "default_chart_size": 400,
    "show_aspects": true,
    "show_houses": true,
    "show_signs": true,
    "color_scheme": "modern"
  },
  "interpretation_preferences": {
    "preferred_ai_model": "openai",
    "interpretation_length": "medium",
    "include_aspects": true,
    "include_houses": true
  },
  "ui_preferences": {
    "sidebar_collapsed": false,
    "show_tooltips": true,
    "animation_enabled": true
  },
  "created_at": "timestamp",
  "updated_at": "timestamp"
}
```

#### E. 支付數據 (`subscriptions`)
```json
{
  "user_id": "string",
  "status": "free",
  "plan_id": "free_plan",
  "trial_used": false,
  "created_at": "timestamp",
  "updated_at": "timestamp"
}
```

## 觸發時機

### 1. 用戶註冊時
- **REST API 註冊**：`_registerWithEmailAndPasswordViaRestApi`
- **SDK 註冊**：`_registerWithEmailAndPasswordViaSDK`
- **參數**：email, displayName, isAnonymous: false

### 2. 匿名登入時
- **REST API 匿名登入**：`_signInAnonymouslyViaRestApi`
- **SDK 匿名登入**：`_signInAnonymouslyViaSDK`
- **參數**：email: null, displayName: null, isAnonymous: true

### 3. 社交登入時（未來擴展）
- Google 登入
- Apple 登入
- Facebook 登入等

## 技術實現

### 1. 並行初始化
```dart
// 使用 Future.wait 並行執行，提高效率
final futures = <Future<void>>[
  _initializeFreeTrialRecord(userId),
  _initializeUserProfile(userId, ...),
  _initializeUserSettings(userId),
  _initializeUserPreferences(userId),
  _initializePaymentData(userId),
];

await Future.wait(futures);
```

### 2. 防重複機制
```dart
// 每個初始化方法都會檢查現有數據
final existingProfile = await _getUserProfile(userId);
if (existingProfile != null) {
  logger.d('用戶檔案已存在，跳過初始化');
  return;
}
```

### 3. 錯誤隔離
```dart
try {
  await _initializeFreeTrialRecord(userId);
} catch (e) {
  logger.e('初始化免費試用記錄失敗: $e');
  // 不拋出異常，繼續其他初始化
}
```

## 集成點

### 1. FirebaseAuthService 修改
```dart
/// 初始化新用戶的數據
static Future<void> _initializeNewUserData(String userId, {
  String? email,
  String? displayName,
  bool isAnonymous = false,
}) async {
  await UserDataInitializationService.initializeNewUserData(
    userId,
    email: email,
    displayName: displayName,
    isAnonymous: isAnonymous,
  );
}
```

### 2. 調用點更新
所有註冊和匿名登入方法都已更新：
```dart
// 註冊時
await _initializeNewUserData(
  user.uid,
  email: email,
  displayName: displayName,
  isAnonymous: false,
);

// 匿名登入時
await _initializeNewUserData(
  user.uid,
  email: null,
  displayName: null,
  isAnonymous: true,
);
```

## 數據流程

### 1. 註冊流程
```
用戶提交註冊信息
    ↓
Firebase 創建用戶帳號
    ↓
保存用戶會話
    ↓
並行初始化用戶數據
    ├── 免費試用記錄
    ├── 用戶檔案
    ├── 用戶設定
    ├── 用戶偏好
    └── 支付數據
    ↓
註冊完成
```

### 2. 匿名登入流程
```
用戶選擇匿名登入
    ↓
Firebase 創建匿名用戶
    ↓
保存用戶會話
    ↓
並行初始化用戶數據（匿名用戶專用）
    ├── 免費試用記錄
    ├── 匿名用戶檔案
    ├── 預設設定
    ├── 預設偏好
    └── 免費計劃數據
    ↓
匿名登入完成
```

## 性能優化

### 1. 並行執行
- 所有初始化操作並行執行
- 減少總體初始化時間
- 提高用戶體驗

### 2. 防重複檢查
- 避免覆蓋現有數據
- 減少不必要的網路請求
- 保護用戶已有設定

### 3. 錯誤隔離
- 單個初始化失敗不影響其他
- 不影響註冊/登入流程
- 後續可重試失敗的初始化

## 監控和維護

### 1. 日誌記錄
- 每個初始化步驟都有詳細日誌
- 成功/失敗狀態追蹤
- 性能指標記錄

### 2. 錯誤處理
- 優雅的錯誤處理
- 不影響主要流程
- 詳細的錯誤信息

### 3. 數據一致性
- 定期檢查數據完整性
- 修復缺失的用戶數據
- 數據遷移支援

## 未來擴展

### 1. 更多數據類型
- 用戶統計數據
- 學習進度記錄
- 社交關係數據

### 2. 智能初始化
- 根據用戶來源調整初始化
- 個性化的預設設定
- A/B 測試支援

### 3. 批量操作
- 批量用戶數據初始化
- 數據遷移工具
- 性能優化

這個完整的用戶數據初始化系統確保每個新用戶都有完整的數據結構，為應用的各種功能提供了可靠的數據基礎，同時保持了高性能和良好的用戶體驗。
