# 單次購買權限檢查修復

## 🐛 問題描述

**發現的問題**：單次購買卻可以解讀多次

**問題原因**：
1. 權限檢查只在頁面初始化時執行（`initState` 中的 `_checkPaymentAndProceed`）
2. 用戶在同一頁面中進行多次解讀操作時（點擊「重試」、問自訂問題等），沒有再次檢查權限
3. `_getInterpretation` 和 `_askCustomQuestion` 方法直接執行解讀，未進行權限驗證

**影響範圍**：
- 用戶購買一次單次解讀後，可以在同一頁面無限次使用解讀功能
- 違反了單次購買的商業邏輯
- 可能造成收入損失

## 🔧 修復方案

### 1. 創建統一的權限檢查方法
**新增方法**：`_checkAndConsumePermission()`

**功能**：
- 每次解讀前都檢查用戶權限
- 自動消費相應的使用次數（單次購買或免費試用）
- 提供用戶友善的提示訊息
- 處理無權限情況

```dart
/// 檢查並消費權限（每次解讀前調用）
Future<bool> _checkAndConsumePermission() async {
  try {
    // 檢查用戶是否有解讀權限
    final hasPermission = await PaymentService.hasInterpretationPermission();
    
    if (!hasPermission) {
      // 沒有權限，顯示支付頁面
      _showPaymentRequired();
      return false;
    }

    // 如果是免費用戶，優先使用單次購買，然後使用免費試用
    final isPremium = await PaymentService.isPremiumUser();
    if (!isPremium) {
      // 先嘗試使用單次購買
      final remainingSinglePurchases = await PaymentService.getRemainingSinglePurchases();
      if (remainingSinglePurchases > 0) {
        final success = await PaymentService.useSinglePurchaseAttempt();
        if (success) {
          final remaining = await PaymentService.getRemainingSinglePurchases();
          // 顯示單次購買使用提示
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('使用單次購買解讀，剩餘 $remaining 次'),
              backgroundColor: AppColors.solarAmber,
            ),
          );
          return true;
        }
      } else {
        // 沒有單次購買，嘗試使用免費試用
        final success = await PaymentService.useFreeTrialAttempt();
        if (!success) {
          _showPaymentRequired();
          return false;
        }
        
        // 顯示免費試用使用提示
        final remainingTrials = await PaymentService.getRemainingFreeTrials();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('免費試用剩餘 $remainingTrials 次'),
            backgroundColor: AppColors.indigoLight,
          ),
        );
        return true;
      }
    }

    // 付費用戶，直接允許
    return true;
  } catch (e) {
    // 錯誤處理
    return false;
  }
}
```

### 2. 修改解讀方法
**修改的方法**：
- `_getInterpretation()` - 獲取 AI 解讀
- `_askCustomQuestion()` - 詢問自訂問題

**修改內容**：在每個方法開始時添加權限檢查

```dart
/// 獲取 AI 解讀
Future<void> _getInterpretation() async {
  // 檢查權限
  if (!await _checkAndConsumePermission()) {
    return;
  }

  setState(() {
    _isLoading = true;
    _errorMessage = null;
  });
  
  // 原有的解讀邏輯...
}

/// 詢問自訂問題
Future<void> _askCustomQuestion(String question) async {
  // 檢查權限
  if (!await _checkAndConsumePermission()) {
    return;
  }

  setState(() {
    _isLoading = true;
    _errorMessage = null;
  });
  
  // 原有的解讀邏輯...
}
```

### 3. 簡化初始化檢查
**修改方法**：`_checkPaymentAndProceed()`

**變更**：
- 只檢查權限，不消費次數
- 將次數消費邏輯移到實際解讀時執行

```dart
/// 檢查支付狀態並決定是否繼續解讀
Future<void> _checkPaymentAndProceed() async {
  try {
    // 檢查用戶是否有解讀權限（不消費次數，只檢查）
    final hasPermission = await PaymentService.hasInterpretationPermission();
    
    if (!hasPermission) {
      // 沒有權限，顯示支付頁面
      _showPaymentRequired();
      return;
    }

    // 有權限，繼續執行解讀（解讀時會自動消費次數）
    if (widget.autoExecuteFirstQuestion &&
        widget.suggestedQuestions.isNotEmpty) {
      // 自動執行第一個問題
      WidgetsBinding.instance.addPostFrameCallback((_) {
        Future.delayed(const Duration(milliseconds: 500), () {
          if (mounted) {
            _askCustomQuestion(widget.suggestedQuestions.first);
          }
        });
      });
    } else {
      _getInterpretation();
    }
  } catch (e) {
    // 錯誤處理
  }
}
```

## 🎯 修復效果

### 1. 權限檢查時機
**修復前**：
- ✅ 頁面初始化時檢查權限
- ❌ 點擊「重試」時不檢查權限
- ❌ 問自訂問題時不檢查權限
- ❌ 點擊建議問題時不檢查權限

**修復後**：
- ✅ 頁面初始化時檢查權限
- ✅ 點擊「重試」時檢查權限
- ✅ 問自訂問題時檢查權限
- ✅ 點擊建議問題時檢查權限

### 2. 使用次數消費
**修復前**：
- 進入頁面時消費 1 次
- 同頁面內多次解讀不再消費

**修復後**：
- 每次解讀都消費 1 次
- 嚴格按照購買次數限制使用

### 3. 用戶體驗
**修復前**：
- 用戶可能誤以為購買了無限次解讀
- 商業邏輯不一致

**修復後**：
- 每次解讀都有明確的次數提示
- 用戶清楚知道剩餘次數
- 商業邏輯嚴格執行

## 📊 測試場景

### 場景 1：單次購買用戶
1. **購買 1 次單次解讀**
2. **進入解讀頁面** → 自動執行解讀，消費 1 次，剩餘 0 次
3. **點擊重試** → 檢查權限，發現無剩餘次數，顯示支付頁面
4. **問自訂問題** → 檢查權限，發現無剩餘次數，顯示支付頁面

### 場景 2：免費試用用戶
1. **免費試用剩餘 2 次**
2. **進入解讀頁面** → 自動執行解讀，消費 1 次，剩餘 1 次
3. **點擊重試** → 檢查權限，消費 1 次，剩餘 0 次
4. **問自訂問題** → 檢查權限，發現無剩餘次數，顯示支付頁面

### 場景 3：付費用戶
1. **付費會員**
2. **進入解讀頁面** → 直接執行解讀，無次數限制
3. **點擊重試** → 直接執行解讀，無次數限制
4. **問自訂問題** → 直接執行解讀，無次數限制

## 🔍 代碼變更摘要

### 新增方法
```dart
/// 檢查並消費權限（每次解讀前調用）
Future<bool> _checkAndConsumePermission() async
```

### 修改方法
```dart
/// 獲取 AI 解讀
Future<void> _getInterpretation() async {
  // 新增：檢查權限
  if (!await _checkAndConsumePermission()) {
    return;
  }
  // 原有邏輯...
}

/// 詢問自訂問題
Future<void> _askCustomQuestion(String question) async {
  // 新增：檢查權限
  if (!await _checkAndConsumePermission()) {
    return;
  }
  // 原有邏輯...
}

/// 檢查支付狀態並決定是否繼續解讀
Future<void> _checkPaymentAndProceed() async {
  // 簡化：只檢查權限，不消費次數
  // 原有的次數消費邏輯移到 _checkAndConsumePermission
}
```

## 🎨 用戶提示優化

### 1. 差異化提示訊息
- **單次購買**：「使用單次購買解讀，剩餘 X 次」（金色背景）
- **免費試用**：「免費試用剩餘 X 次」（藍色背景）
- **無權限**：顯示支付對話框

### 2. 提示時機
- 每次消費次數後立即顯示
- 提示持續 2 秒
- 顏色區分不同類型的使用

### 3. 支付引導
- 無權限時自動顯示支付對話框
- 清楚說明需要訂閱的原因
- 提供「稍後再說」和「立即訂閱」選項

## 📈 商業價值

### 1. 收入保護
- **防止濫用**：確保單次購買只能使用一次
- **促進轉換**：用完免費試用後引導付費
- **收入最大化**：每次解讀都有對應的付費

### 2. 用戶體驗
- **透明度**：用戶清楚知道剩餘次數
- **公平性**：所有用戶都遵循相同規則
- **信任度**：商業邏輯一致，建立用戶信任

### 3. 產品完整性
- **邏輯一致**：支付和使用邏輯完全對應
- **功能完整**：覆蓋所有解讀入口的權限檢查
- **維護性**：統一的權限檢查邏輯，易於維護

## 📝 總結

### 問題解決
- ✅ 修復了單次購買可以多次使用的漏洞
- ✅ 確保每次解讀都進行權限檢查
- ✅ 統一了權限檢查和次數消費邏輯
- ✅ 提供了清晰的用戶提示

### 技術改進
- ✅ 創建了統一的權限檢查方法
- ✅ 在所有解讀入口添加權限驗證
- ✅ 簡化了初始化邏輯
- ✅ 改善了錯誤處理

### 商業價值
- ✅ 保護了收入不被濫用
- ✅ 提升了用戶體驗的一致性
- ✅ 增強了產品的商業邏輯完整性
- ✅ 為未來的功能擴展奠定了基礎

這個修復確保了單次購買功能按照設計意圖正確運作，每次解讀都會消費相應的使用次數，維護了應用的商業邏輯完整性。
