# 出生地經緯度與時區顯示功能實作文件

## 概述
本文件說明在 `BirthDataFormPage` 中新增出生地下方顯示經緯度和時區信息的功能實作過程，讓用戶能夠即時確認地理位置的準確性和時區信息。

## 功能目標
- 在出生地輸入框下方顯示經緯度信息
- 顯示對應的時區信息
- 當用戶輸入地址時自動獲取並顯示位置信息
- 當使用定位功能時同步更新位置信息
- 提供清晰的視覺設計和用戶體驗

## 技術實現

### 1. 狀態變數新增

#### 地理位置狀態
```dart
class _BirthDataFormPageState extends State<BirthDataFormPage> {
  // 原有狀態變數...
  
  // 地理位置信息
  double? _latitude;    // 緯度
  double? _longitude;   // 經度
  String? _timezone;    // 時區
}
```

#### 設計考量
- **可空類型**：使用 `double?` 和 `String?` 允許未設置狀態
- **分離存儲**：將經緯度和時區分開存儲，便於獨立處理
- **狀態同步**：與現有的出生地輸入框狀態保持同步

### 2. 時區計算方法

#### 簡化時區計算
```dart
void _updateTimezone() {
  if (_latitude != null && _longitude != null) {
    // 簡單的時區計算（基於經度）
    // 這是一個簡化的計算，實際應用中可能需要更精確的時區數據庫
    final timezoneOffset = (_longitude! / 15).round();
    final sign = timezoneOffset >= 0 ? '+' : '';
    _timezone = 'UTC$sign$timezoneOffset';
  } else {
    _timezone = null;
  }
}
```

#### 計算邏輯
- **經度基礎**：每 15 度經度對應 1 小時時差
- **四捨五入**：使用 `round()` 獲取最接近的整數時區
- **格式化**：生成標準的 UTC±N 格式
- **邊界處理**：正確處理正負時區的符號顯示

### 3. 位置信息更新機制

#### 初始化時設置
```dart
@override
void initState() {
  super.initState();
  
  if (widget.initialData != null) {
    // 設置現有數據的位置信息
    _latitude = widget.initialData!.latitude;
    _longitude = widget.initialData!.longitude;
    _updateTimezone();
  }
}
```

#### 地址輸入時更新
```dart
onChanged: (value) async {
  // 當用戶輸入地址時，嘗試獲取經緯度
  if (value.isNotEmpty && value.length > 3) {
    final coordinates = await GeocodingService.getCoordinatesFromAddress(value);
    if (coordinates != null) {
      setState(() {
        _latitude = coordinates['latitude'];
        _longitude = coordinates['longitude'];
        _updateTimezone();
      });
    } else {
      setState(() {
        _latitude = null;
        _longitude = null;
        _timezone = null;
      });
    }
  }
},
```

#### 定位功能時更新
```dart
// 在 _getCurrentLocation 方法中
setState(() {
  _placeController.text = address;
  _latitude = coordinates['latitude'];    // 新增
  _longitude = coordinates['longitude'];  // 新增
  _updateTimezone();                     // 新增
  _isLoading = false;
});
```

### 4. UI 組件設計

#### 位置信息顯示組件
```dart
Widget _buildLocationInfo() {
  return Container(
    padding: const EdgeInsets.all(12),
    decoration: BoxDecoration(
      color: Colors.blue.withValues(alpha: 0.05),
      borderRadius: BorderRadius.circular(8),
      border: Border.all(
        color: Colors.blue.withValues(alpha: 0.2),
        width: 1,
      ),
    ),
    child: Column(
      children: [
        // 標題行
        Row(
          children: [
            Icon(Icons.location_on, color: Colors.blue[600], size: 16),
            const SizedBox(width: 8),
            Text(
              '地理位置信息',
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: Colors.blue[700],
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        
        // 信息行
        Row(
          children: [
            // 經緯度信息
            Expanded(
              child: Row(
                children: [
                  Icon(Icons.my_location, color: Colors.grey[600], size: 14),
                  const SizedBox(width: 4),
                  Text('經緯度:', style: labelStyle),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      '${_latitude!.toStringAsFixed(4)}, ${_longitude!.toStringAsFixed(4)}',
                      style: valueStyle,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(width: 16),
            
            // 時區信息
            Row(
              children: [
                Icon(Icons.schedule, color: Colors.grey[600], size: 14),
                const SizedBox(width: 4),
                Text('時區:', style: labelStyle),
                const SizedBox(width: 4),
                Text(_timezone ?? 'N/A', style: valueStyle),
              ],
            ),
          ],
        ),
      ],
    ),
  );
}
```

#### 條件顯示邏輯
```dart
// 在主要的 Column 中
if (_latitude != null && _longitude != null) ...[
  const SizedBox(height: 8),
  _buildLocationInfo(),
],
```

## 設計特點

### 1. 視覺設計
- **淡藍色主題**：使用藍色系表示地理位置信息
- **卡片式設計**：獨立的容器突出位置信息
- **圖標指引**：使用位置和時間圖標提供視覺提示
- **緊湊佈局**：在有限空間內顯示完整信息

### 2. 信息層次
```
┌─────────────────────────────────────┐
│ 📍 地理位置信息                      │
│ 📍 經緯度: 25.0330, 121.5654        │
│ ⏰ 時區: UTC+8                      │
└─────────────────────────────────────┘
```

### 3. 響應式設計
- **自適應寬度**：經緯度部分使用 `Expanded` 自適應
- **文字溢出處理**：長經緯度數字使用 `TextOverflow.ellipsis`
- **間距優化**：合理的內邊距和間距設計

### 4. 數據精度
- **經緯度精度**：顯示到小數點後 4 位（約 10 米精度）
- **時區簡化**：使用 UTC±N 格式，便於理解
- **等寬字體**：經緯度和時區使用 `monospace` 字體

## 用戶體驗

### 1. 即時反饋
- **輸入響應**：用戶輸入地址時自動獲取位置信息
- **定位同步**：使用定位功能時同步顯示位置信息
- **狀態清晰**：無效地址時清除位置信息顯示

### 2. 信息確認
- **準確性驗證**：用戶可以確認地址對應的經緯度是否正確
- **時區參考**：提供時區信息幫助用戶確認時間設置
- **視覺提示**：清晰的標籤和數值區分

### 3. 錯誤處理
- **無效地址**：無法獲取經緯度時不顯示位置信息
- **網絡問題**：地理編碼失敗時優雅降級
- **數據缺失**：初始狀態或無數據時不顯示組件

## 技術優化

### 1. 性能考量
```dart
// 防抖處理（可以進一步優化）
onChanged: (value) async {
  if (value.isNotEmpty && value.length > 3) {
    // 只在輸入長度大於 3 時才進行地理編碼
    final coordinates = await GeocodingService.getCoordinatesFromAddress(value);
    // 處理結果...
  }
},
```

### 2. 狀態管理
- **統一更新**：所有位置相關的狀態統一更新
- **條件渲染**：只在有數據時才渲染位置信息組件
- **狀態同步**：確保 UI 狀態與數據狀態一致

### 3. 錯誤處理
- **空值檢查**：所有位置相關操作都進行空值檢查
- **異常捕獲**：地理編碼失敗時優雅處理
- **用戶提示**：適當的錯誤信息和狀態提示

## 數據流程

### 1. 地址輸入流程
```
用戶輸入地址 → 地理編碼 → 獲取經緯度 → 計算時區 → 更新 UI
```

### 2. 定位功能流程
```
獲取當前位置 → 反向地理編碼 → 更新地址 → 設置經緯度 → 計算時區 → 更新 UI
```

### 3. 數據保存流程
```
表單驗證 → 地理編碼 → 創建 BirthData → 保存數據
```

## 未來擴展

### 1. 精確時區支援
- **時區數據庫**：使用更精確的時區數據庫（如 IANA）
- **夏令時處理**：考慮夏令時的影響
- **歷史時區**：支援歷史時區變化

### 2. 更多地理信息
- **海拔高度**：顯示海拔信息
- **行政區劃**：顯示國家、省市等信息
- **地理特徵**：顯示附近的地理特徵

### 3. 交互增強
- **點擊複製**：點擊經緯度複製到剪貼板
- **地圖預覽**：點擊位置信息顯示地圖
- **精度調整**：允許用戶調整顯示精度

### 4. 驗證功能
- **位置驗證**：驗證經緯度是否在合理範圍內
- **一致性檢查**：檢查地址與經緯度是否一致
- **警告提示**：位置信息異常時提供警告

## 測試驗證

### 1. 功能測試
- ✅ 地址輸入時正確獲取和顯示位置信息
- ✅ 定位功能時正確更新位置信息
- ✅ 無效地址時正確隱藏位置信息
- ✅ 時區計算準確性驗證

### 2. UI 測試
- ✅ 位置信息組件正確顯示
- ✅ 響應式佈局適配不同螢幕
- ✅ 文字溢出處理正常
- ✅ 視覺設計符合整體風格

### 3. 性能測試
- ✅ 地理編碼響應速度合理
- ✅ UI 更新流暢無卡頓
- ✅ 記憶體使用正常
- ✅ 網絡請求優化

### 4. 兼容性測試
- ✅ 不同地址格式支援
- ✅ 不同地理位置測試
- ✅ 網絡異常情況處理
- ✅ 數據缺失情況處理

## 總結

出生地經緯度與時區顯示功能的實現帶來了以下改進：

1. **信息透明度**：用戶可以清楚看到地址對應的精確位置
2. **數據準確性**：幫助用戶驗證地理位置的正確性
3. **時區參考**：提供時區信息輔助時間設置
4. **用戶體驗**：即時的位置信息反饋提升操作體驗
5. **專業性**：展示應用對地理數據的專業處理能力

這個功能特別適合需要精確地理位置的占星應用，讓用戶能夠確認出生地的準確性，從而確保星盤計算的精度。通過清晰的視覺設計和即時的信息反饋，用戶可以更有信心地輸入和確認出生地信息。
