# 多星盤類型設定功能實現

## 📋 功能概述

成功實現了設定頁面中星盤顯示設置與相位設置可以依照不同星盤種類而有不同設定的功能，為每種星盤類型提供專業和個性化的顯示配置。

## ✨ 核心功能

### 🎯 **多星盤類型設定系統**

#### **支援的星盤類型**
- 🔵 **本命盤** (`ChartType.natal`) - 個人基本性格和潛能分析
- 🟢 **流年盤** (`ChartType.transit`) - 當前天體位置影響分析
- 🩷 **合盤** (`ChartType.synastry`) - 兩人關係相容性分析
- 🟣 **組合盤** (`ChartType.composite`) - 關係本身特質分析
- 🔵 **日月蝕盤** (`ChartType.eclipse`) - 蝕相事件影響分析
- 🟠 **二分二至盤** (`ChartType.equinoxSolstice`) - 季節轉換能量分析

#### **獨立設定項目**
每種星盤類型都可以獨立設定：
- 🏠 **宮位系統** - Placidus、Koch、Equal House 等
- 🌟 **行星顯示** - 各行星的顯示/隱藏設定
- 📐 **相位容許度** - 各相位的精確度要求
- 🎨 **顏色配置** - 行星和相位的顏色設定
- 📊 **度數顯示** - 宮位度數、行星度數顯示選項
- ⭐ **星座界主星** - 界主星顯示設定

## 🔧 **技術架構**

### **新增的核心類別**

#### **1. ChartTypeSettings 類別**
```dart
class ChartTypeSettings {
  String houseSystem;                    // 宮位系統
  Map<String, bool> planetVisibility;   // 行星顯示設定
  Map<String, double> aspectOrbs;       // 相位容許度設定
  Map<String, Color> aspectColors;      // 相位顏色設定
  Map<String, Color> planetColors;      // 行星顏色設定
  bool showZodiacRulers;                // 星座界主星顯示
  bool showHouseDegrees;                // 宮位度數顯示
  bool showPlanetDegrees;               // 行星度數顯示
}
```

#### **2. MultiChartSettings 管理類別**
```dart
class MultiChartSettings {
  Map<ChartType, ChartTypeSettings> chartTypeSettings;  // 各星盤類型設定
  ChartType currentChartType;                           // 當前選中的星盤類型
  
  // 獲取當前星盤類型的設定
  ChartTypeSettings get currentSettings;
  
  // 獲取指定星盤類型的設定
  ChartTypeSettings getSettingsForChartType(ChartType chartType);
  
  // 更新指定星盤類型的設定
  void updateSettingsForChartType(ChartType chartType, ChartTypeSettings settings);
}
```

### **SettingsViewModel 增強**

#### **新增屬性和方法**
```dart
class SettingsViewModel extends ChangeNotifier {
  MultiChartSettings? _multiChartSettings;
  ChartType _currentChartType = ChartType.natal;
  
  // 設定當前星盤類型
  void setCurrentChartType(ChartType chartType);
  
  // 獲取指定星盤類型的設定
  ChartTypeSettings? getSettingsForChartType(ChartType chartType);
  
  // 保存多星盤設定
  Future<void> saveMultiChartSettings();
}
```

#### **智能設定更新**
所有設定更新方法都同時更新：
- 🔄 **向後兼容** - 更新原有的 `ChartSettings`
- 🆕 **多類型支援** - 更新當前選中星盤類型的設定
- 💾 **自動保存** - 即時保存到 SharedPreferences

## 🎨 **用戶界面增強**

### **星盤類型選擇器組件**

#### **ChartTypeSelector 特色**
- 🎯 **視覺化選擇** - 每種星盤類型都有專門的圖標和顏色
- 📝 **詳細說明** - 提供每種星盤類型的功能說明
- 🎨 **美觀設計** - 漸變背景和統一的設計風格
- 📱 **響應式** - 適應不同屏幕尺寸

#### **星盤類型信息**
| 星盤類型 | 圖標 | 顏色 | 說明 |
|---------|------|------|------|
| 本命盤 | 👤 | 藍色 | 個人出生時的天體位置圖，顯示基本性格和潛能 |
| 流年盤 | 📈 | 綠色 | 當前天體位置對本命盤的影響，預測時運變化 |
| 合盤 | 💕 | 粉色 | 比較兩個人的星盤，分析相容性和關係動態 |
| 組合盤 | 🔀 | 紫色 | 兩個人星盤的中點圖，代表關係本身的特質 |
| 日月蝕盤 | 🌙 | 靛藍 | 分析蝕相事件的影響，預測重大轉折和變化 |
| 二分二至盤 | ☀️ | 橙色 | 分析季節轉換時的能量，預測社會和自然趨勢 |

### **設定頁面整合**

#### **星盤顯示設置頁面**
- ✅ 添加星盤類型選擇器
- ✅ 根據選中類型顯示對應設定
- ✅ 更新頁面說明文字
- ✅ 保持原有功能完整性

#### **相位設置頁面**
- ✅ 添加星盤類型選擇器
- ✅ 根據星盤類型提供專門的預設選項
- ✅ 智能建議設置
- ✅ 更新說明文字

## 🎯 **專業化預設設定**

### **本命盤設定**
```dart
// 標準設定 - 適合一般分析
aspectOrbs: {
  '合相': 8.0, '對分相': 8.0, '三分相': 6.0, 
  '四分相': 6.0, '六分相': 4.0
}

// 嚴格設定 - 適合精確分析
aspectOrbs: {
  '合相': 6.0, '對分相': 6.0, '三分相': 4.0, 
  '四分相': 4.0, '六分相': 3.0
}
```

### **流年盤設定**
```dart
// 標準設定 - 適合流年分析
aspectOrbs: {
  '合相': 6.0, '對分相': 6.0, '三分相': 4.0, 
  '四分相': 4.0, '六分相': 3.0
}

// 精確設定 - 適合短期預測
aspectOrbs: {
  '合相': 4.0, '對分相': 4.0, '三分相': 3.0, 
  '四分相': 3.0, '六分相': 2.0
}
```

### **日月蝕盤設定**
```dart
// 標準設定 - 適合蝕相分析
aspectOrbs: {
  '合相': 10.0, '對分相': 10.0, '三分相': 8.0, 
  '四分相': 8.0, '六分相': 5.0
}

// 寬鬆設定 - 適合長期影響分析
aspectOrbs: {
  '合相': 12.0, '對分相': 12.0, '三分相': 10.0, 
  '四分相': 10.0, '六分相': 6.0
}
```

## 💾 **數據持久化**

### **存儲結構**
```json
{
  "multi_chart_settings": {
    "currentChartType": "ChartType.natal",
    "chartTypeSettings": {
      "ChartType.natal": {
        "houseSystem": "Placidus",
        "planetVisibility": {...},
        "aspectOrbs": {...},
        "aspectColors": {...},
        "planetColors": {...},
        "showZodiacRulers": false,
        "showHouseDegrees": true,
        "showPlanetDegrees": true
      },
      "ChartType.transit": {...},
      // 其他星盤類型設定
    }
  }
}
```

### **向後兼容性**
- ✅ 保留原有的 `ChartSettings` 類別
- ✅ 同時更新新舊設定系統
- ✅ 自動遷移現有用戶設定
- ✅ 無縫升級體驗

## 🌟 **用戶體驗優化**

### **智能預設**
- 🎯 **專業建議** - 每種星盤類型都有專門的預設選項
- 📊 **使用場景** - 根據分析目的提供不同的設定建議
- ⚡ **快速應用** - 一鍵應用專業預設設定
- 💡 **學習引導** - 通過預設設定學習專業配置

### **視覺反饋**
- 🎨 **即時預覽** - 設定更改即時反映在界面上
- 📱 **狀態指示** - 清晰的當前星盤類型指示
- 🔄 **切換動畫** - 流暢的星盤類型切換體驗
- 💬 **操作反饋** - 設定應用成功的提示信息

### **專業性保證**
- 📚 **理論基礎** - 基於傳統占星學理論的設定建議
- 🔬 **實用性** - 針對不同分析需求的優化設定
- 📈 **靈活性** - 支援用戶自定義和微調
- 🎓 **教育性** - 幫助用戶理解不同星盤類型的特點

## 🚀 **未來擴展**

### **功能增強**
- 📊 **設定模板** - 支援用戶創建和分享設定模板
- 🔄 **批量操作** - 支援批量應用設定到多個星盤類型
- 📱 **設定同步** - 跨設備的設定同步功能
- 🎯 **智能推薦** - 基於使用習慣的設定推薦

### **專業擴展**
- 🌟 **更多星盤類型** - 支援更多專業星盤類型
- 📐 **高級相位** - 支援更多相位類型和設定
- 🎨 **主題系統** - 支援不同的視覺主題
- 📊 **統計分析** - 設定使用情況的統計和分析

這個多星盤類型設定功能為用戶提供了專業、靈活且易用的星盤配置系統，大大提升了應用的專業性和用戶體驗！
