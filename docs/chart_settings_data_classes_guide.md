# 星盤設定資料類別使用指南

## 概述

為了改善程式碼維護性，我們將原本使用 `Map<String, dynamic>` 的星盤設定改為使用強型別的 data class。這些 data class 提供了更好的型別安全性、程式碼可讀性和維護性。

## 新增的 Data Classes

### 1. PlanetVisibilitySettings
取代 `Map<String, bool> planetVisibility`

**功能**：控制各個行星和特殊點在星盤中的顯示狀態

**屬性**：
- 主要行星：sun, moon, mercury, venus, mars, jupiter, saturn, uranus, neptune, pluto
- 四軸點：ascendant, midheaven, descendant, imumCoeli
- 交點：northNode, southNode
- 小行星：lilith, chiron, pholus, ceres, pallas, juno, vesta
- 特殊點：fortunePoint, spiritPoint, exaltationPoint, sunMoonMidpoint, vertexPoint

**預設配置**：
- `PlanetVisibilitySettings.analysisDefault`：分析模式預設值
- `PlanetVisibilitySettings.homeDefault`：首頁模式預設值

### 2. AspectOrbSettings
取代 `Map<String, double> aspectOrbs`

**功能**：設定各種相位的容許度

**屬性**：
- 主要相位：conjunction, sextile, square, trine, opposition
- 次要相位：semisextile, semisquare, quintile, decile, novile, septile, undecile

**預設配置**：
- `AspectOrbSettings.analysisDefault`：分析模式預設值
- `AspectOrbSettings.homeDefault`：首頁模式預設值
- `AspectOrbSettings.classicalDefault`：古典占星模式預設值
- `AspectOrbSettings.modernDefault`：現代占星模式預設值
- `AspectOrbSettings.specialDefault`：特殊模式預設值

### 3. AspectColorSettings
取代 `Map<String, Color> aspectColors`

**功能**：設定各種相位的顯示顏色

**特色**：
- 提供 `fromAspectDefinitions()` 方法從 AspectDefinitions 自動獲取顏色
- 支援所有主要和次要相位的顏色設定

### 4. PlanetColorSettings
取代 `Map<String, Color> planetColors`

**功能**：設定各個行星和特殊點的顯示顏色

**特色**：
- 提供 `fromAstrologyConstants()` 方法從 AstrologyConstants 自動獲取顏色
- 涵蓋所有行星、四軸點、交點和小行星的顏色設定

## 使用方式

### 基本使用

```dart
// 建立行星顯示設定
const planetSettings = PlanetVisibilitySettings(
  sun: true,
  moon: true,
  mercury: false,
  // ... 其他設定
);

// 建立相位容許度設定
const orbSettings = AspectOrbSettings(
  conjunction: 8.0,
  square: 6.0,
  // ... 其他設定
);

// 建立相位顏色設定
final colorSettings = AspectColorSettings.fromAspectDefinitions();

// 建立行星顏色設定
final planetColors = PlanetColorSettings.fromAstrologyConstants();
```

### 向後相容性

所有 data class 都提供 `toMap()` 和 `fromMap()` 方法來與現有的 Map 結構互轉：

```dart
// 從 Map 建立
final settings = PlanetVisibilitySettings.fromMap(existingMap);

// 轉換為 Map（用於現有程式碼）
final map = settings.toMap();
```

### 修改設定

使用 `copyWith` 方法來修改特定設定：

```dart
// 修改行星顯示狀態
final newSettings = planetSettings.copyWithPlanet('水星', false);

// 修改相位容許度
final newOrbSettings = orbSettings.copyWithAspect('合相', 10.0);

// 修改相位顏色
final newColorSettings = colorSettings.copyWithAspect('合相', Colors.blue);

// 修改行星顏色
final newPlanetColors = planetColors.copyWithPlanet('太陽', Colors.orange);
```

## 遷移指南

### 第一階段：建立適配器方法

在現有的 ChartSettings 和 ChartTypeSettings 類別中新增適配器方法：

```dart
class ChartTypeSettings {
  // 現有的 Map 屬性
  Map<String, bool> planetVisibility;
  Map<String, double> aspectOrbs;
  Map<String, Color> aspectColors;
  Map<String, Color> planetColors;

  // 新增的適配器方法
  PlanetVisibilitySettings get planetVisibilitySettings => 
      PlanetVisibilitySettings.fromMap(planetVisibility);
  
  set planetVisibilitySettings(PlanetVisibilitySettings settings) =>
      planetVisibility = settings.toMap();

  AspectOrbSettings get aspectOrbSettings => 
      AspectOrbSettings.fromMap(aspectOrbs);
  
  set aspectOrbSettings(AspectOrbSettings settings) =>
      aspectOrbs = settings.toMap();

  // ... 其他適配器方法
}
```

### 第二階段：逐步替換使用

在新的程式碼中使用 data class，舊的程式碼繼續使用 Map：

```dart
// 新程式碼
void updatePlanetVisibility(String planet, bool isVisible) {
  final settings = chartSettings.planetVisibilitySettings;
  final newSettings = settings.copyWithPlanet(planet, isVisible);
  chartSettings.planetVisibilitySettings = newSettings;
}

// 舊程式碼仍然可以正常運作
chartSettings.planetVisibility['太陽'] = true;
```

### 第三階段：完全遷移

當所有相關程式碼都更新後，可以將 ChartSettings 完全改為使用 data class。

## 優勢

1. **型別安全**：編譯時期就能發現型別錯誤
2. **程式碼可讀性**：明確的屬性名稱比字串鍵值更清楚
3. **IDE 支援**：自動完成、重構、查找引用等功能
4. **不可變性**：使用 const 建構子確保設定的不可變性
5. **預設值管理**：集中管理各種模式的預設設定
6. **向後相容**：提供 Map 轉換方法確保平滑遷移

## 注意事項

1. **記憶體使用**：data class 可能比 Map 使用更多記憶體，但換來更好的型別安全性
2. **序列化**：需要確保 JSON 序列化/反序列化正常運作
3. **測試**：需要更新相關的單元測試
4. **文件**：需要更新 API 文件和使用說明

## 未來擴展

這些 data class 為未來的功能擴展提供了良好的基礎：

1. **驗證邏輯**：可以在 data class 中加入設定值的驗證
2. **主題系統**：可以建立不同的顏色主題配置
3. **使用者自訂**：可以讓使用者自訂和儲存個人化設定
4. **設定匯入匯出**：可以輕鬆實現設定的匯入匯出功能
