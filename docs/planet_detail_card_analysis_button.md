# 行星詳情卡片分析按鈕功能

## 📋 功能概述

成功在 `PlanetDetailCard` 中新增了「分析行星詳情」按鈕，功能方式與小限法卡片中的「解讀當前小限法」按鈕相同，都會導航到 AI 解讀頁面並進行支付檢查，為用戶提供專業的行星占星學分析。

## 🎯 實作內容

### 1. 新增 Import 依賴
**檔案位置**：`lib/widgets/planet_detail_card.dart`

**新增的 Import**：
```dart
import 'package:astreal/models/chart_data.dart';
import 'package:astreal/ui/pages/ai_interpretation_result_page.dart';
```

### 2. 分析按鈕 UI 設計
**位置**：卡片內容區域的底部

**設計特色**：
- **動態顏色**：按鈕背景色使用行星的專屬顏色 `planet.color`
- **圖標設計**：使用 `Icons.psychology` 表示 AI 分析功能
- **按鈕文字**：「分析行星詳情」
- **視覺效果**：圓角設計、陰影效果、白色文字

```dart
Widget _buildAnalysisButton(BuildContext context) {
  return Container(
    width: double.infinity,
    margin: const EdgeInsets.symmetric(horizontal: 4),
    child: ElevatedButton.icon(
      onPressed: () => _navigateToInterpretation(context),
      icon: Icon(
        Icons.psychology,
        size: 18,
        color: Colors.white,
      ),
      label: const Text(
        '分析行星詳情',
        style: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
      style: ElevatedButton.styleFrom(
        backgroundColor: planet.color,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        elevation: 3,
        shadowColor: planet.color.withOpacity(0.3),
      ),
    ),
  );
}
```

### 3. 導航功能實作
**方法名稱**：`_navigateToInterpretation(BuildContext context)`

**執行流程**：
1. **創建 ChartData**：基於當前星盤數據創建 ChartData 對象
2. **關閉卡片**：調用 `onClose()` 關閉行星詳情卡片
3. **導航到解讀頁面**：使用 `Navigator.push` 導航到 `AIInterpretationResultPage`
4. **自動執行分析**：設置 `autoExecuteFirstQuestion: true` 自動開始分析

```dart
void _navigateToInterpretation(BuildContext context) {
  // 創建一個基於原始星盤的 ChartData
  final chartData = ChartData(
    chartType: viewModel.chartData.chartType,
    primaryPerson: viewModel.chartData.primaryPerson,
    secondaryPerson: viewModel.chartData.secondaryPerson,
    specificDate: viewModel.chartData.specificDate,
    planets: viewModel.chartData.planets,
    houses: viewModel.chartData.houses,
    aspects: viewModel.chartData.aspects,
  );

  // 先關閉行星詳情卡片
  onClose();

  // 導航到 AI 解讀結果頁面
  try {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) {
          return AIInterpretationResultPage(
            chartData: chartData,
            interpretationTitle: '行星詳情解讀',
            subtitle: '${planet.name} - ${planet.sign} ${viewModel.formatDegree(planet.longitude % 30)}',
            suggestedQuestions: [
              _buildPlanetInterpretationPrompt(),
            ],
            autoExecuteFirstQuestion: true,
          );
        },
      ),
    );
  } catch (e) {
    debugPrint('導航到解讀頁面失敗: $e');
  }
}
```

### 4. 專業提示詞生成
**方法名稱**：`_buildPlanetInterpretationPrompt()`

**提示詞結構**：
- **行星基本信息**：名稱、位置、宮位、尊貴力量等
- **宮主星信息**：象限制和整宮制下的宮主星責任
- **主要相位**：與其他行星的重要相位關係
- **互容接納關係**：與其他行星的互容接納狀態
- **分析要求**：8個具體的分析角度

**提示詞內容示例**：
```
請詳細分析以下行星的占星學意義：

【行星基本信息】
行星：火星
位置：牡羊座 15°23'
宮位：第1宮 (始宮)
尊貴力量：廟
運行狀態：順行
日夜盤：日間
區分狀態：在其區分位置

【主要相位】
拱 木星 (容許度: 2.3°)
刑 土星 (容許度: 1.8°)

【分析要求】
請從以下角度進行詳細分析：
1. 行星在此星座的基本特質和表現方式
2. 行星在此宮位的生活領域影響
3. 尊貴力量對行星能量的影響
4. 運行狀態（順行/逆行）的意義
5. 主要相位對行星能量的調節作用
6. 作為宮主星的責任和影響範圍
7. 在當前星盤配置下的整體評估
8. 實際生活中的具體建議和注意事項
```

## 🚀 用戶體驗流程

### 1. 完整的使用流程
1. **點擊行星** → 顯示行星詳情卡片
2. **查看詳細信息** → 瀏覽行星的各種占星學資料
3. **點擊分析按鈕** → 觸發「分析行星詳情」功能
4. **支付檢查** → 自動檢查用戶的支付狀態和權限
5. **AI 分析** → 自動執行專業的行星解讀分析
6. **查看結果** → 獲得詳細的占星學分析報告

### 2. 支付流程整合
- **免費用戶**：使用免費試用次數（3次）
- **試用用完**：引導到支付頁面選擇訂閱方案
- **付費用戶**：直接執行分析，無需額外步驟

### 3. 視覺體驗
- **動態顏色**：每個行星的按鈕都使用該行星的專屬顏色
- **即時反饋**：點擊後立即關閉卡片並導航
- **一致性**：與小限法解讀按鈕保持相同的交互模式

## 🎨 設計特色

### 1. 個性化顏色系統
每個行星都有專屬的顏色，按鈕會動態使用該行星的顏色：
- **太陽**：金黃色
- **月亮**：銀白色
- **水星**：橙色
- **金星**：綠色
- **火星**：紅色
- **木星**：藍色
- **土星**：深藍色
- **天王星**：青色
- **海王星**：紫色
- **冥王星**：深紫色

### 2. 統一的交互模式
與小限法卡片保持一致的交互體驗：
- **按鈕位置**：卡片底部
- **按鈕樣式**：圓角、陰影、圖標+文字
- **導航方式**：先關閉卡片，再導航到解讀頁面
- **自動執行**：自動開始 AI 分析

### 3. 專業的分析內容
提供8個維度的專業分析：
1. **星座特質**：行星在特定星座的表現
2. **宮位影響**：對特定生活領域的作用
3. **尊貴力量**：行星能量的強弱狀態
4. **運行狀態**：順行/逆行的意義
5. **相位調節**：其他行星的影響
6. **宮主責任**：作為宮主星的作用
7. **整體評估**：在當前星盤中的地位
8. **實用建議**：具體的生活指導

## 🔧 技術實作細節

### 1. 數據傳遞
```dart
final chartData = ChartData(
  chartType: viewModel.chartData.chartType,
  primaryPerson: viewModel.chartData.primaryPerson,
  secondaryPerson: viewModel.chartData.secondaryPerson,
  specificDate: viewModel.chartData.specificDate,
  planets: viewModel.chartData.planets,
  houses: viewModel.chartData.houses,
  aspects: viewModel.chartData.aspects,
);
```

### 2. 頁面參數設置
```dart
AIInterpretationResultPage(
  chartData: chartData,
  interpretationTitle: '行星詳情解讀',
  subtitle: '${planet.name} - ${planet.sign} ${viewModel.formatDegree(planet.longitude % 30)}',
  suggestedQuestions: [_buildPlanetInterpretationPrompt()],
  autoExecuteFirstQuestion: true,
)
```

### 3. 錯誤處理
```dart
try {
  Navigator.push(context, MaterialPageRoute(...));
} catch (e) {
  debugPrint('導航到解讀頁面失敗: $e');
}
```

## 🔮 未來擴展可能

### 1. 更多分析選項
- **行星相位專門分析**：針對特定相位的深度解讀
- **行星週期分析**：行星回歸、進展等時間技法
- **行星合相分析**：多行星合相的複合影響

### 2. 個性化設置
- **分析深度選擇**：基礎/進階/專業三個級別
- **分析角度偏好**：心理/事業/感情等特定領域
- **語言風格選擇**：傳統/現代/心理學導向

### 3. 互動功能
- **追問功能**：針對分析結果進行深入提問
- **比較分析**：多個行星的對比分析
- **時間分析**：行星在不同時期的變化

## 📝 總結

### 主要成就
- ✅ 成功整合行星詳情分析功能
- ✅ 實現與小限法解讀一致的用戶體驗
- ✅ 建立完整的支付流程檢查
- ✅ 設計個性化的行星顏色系統
- ✅ 創建專業的8維度分析框架
- ✅ 提供詳細的占星學提示詞生成

### 技術優勢
- **無縫整合**：與現有 AI 解讀系統完美整合
- **動態設計**：每個行星都有專屬的視覺體驗
- **專業內容**：基於傳統占星學的深度分析
- **用戶友善**：簡單點擊即可獲得專業解讀

### 用戶價值
- **即時分析**：點擊行星立即獲得專業解讀
- **個性化體驗**：每個行星都有獨特的分析角度
- **專業指導**：8個維度的全面分析
- **實用建議**：具體的生活應用指導

這個功能為用戶提供了更深入、更專業的行星分析服務，大大提升了應用的占星學專業性和實用價值。
