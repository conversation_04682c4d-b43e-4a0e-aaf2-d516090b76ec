# 解讀選項配置系統文件

## 概述
本文件說明新的解讀選項配置系統，該系統將原本硬編碼在 `_buildNatalOptions` 方法中的選項改為使用 JSON 檔案配置，方便未來改用 API 處理。

## 系統架構

### 1. 配置檔案結構
```
assets/config/
├── natal_interpretation_options.json          # 本命盤解讀選項
├── relationship_interpretation_options.json   # 關係盤解讀選項（待建立）
├── transit_interpretation_options.json        # 推運盤解讀選項（待建立）
├── solar_return_interpretation_options.json   # 太陽回歸盤解讀選項（待建立）
├── lunar_return_interpretation_options.json   # 月亮回歸盤解讀選項（待建立）
├── firdaria_interpretation_options.json       # 法達盤解讀選項（待建立）
├── eclipse_interpretation_options.json        # 日月蝕盤解讀選項（待建立）
├── equinox_solstice_interpretation_options.json # 二分二至盤解讀選項（待建立）
└── general_interpretation_options.json        # 通用解讀選項（待建立）
```

### 2. JSON 配置檔案格式
```json
{
  "version": "1.0.0",
  "lastUpdated": "2025-06-20",
  "description": "本命盤解讀選項配置檔案",
  "chartType": "natal",
  "options": [
    {
      "id": "unique_option_id",
      "title": "選項標題",
      "subtitle": "選項描述",
      "icon": "icon_name",
      "color": "color_name",
      "interpretationType": "interpretation_type",
      "questions": ["問題1", "問題2"],
      "enabled": true,
      "order": 1
    }
  ],
  "colorMapping": {
    "royalIndigo": "#4C51BF",
    "green": "#10B981"
  },
  "iconMapping": {
    "person": "Icons.person",
    "monetization_on": "Icons.monetization_on"
  }
}
```

### 3. 核心服務類

#### InterpretationConfigService
- **單例模式**：確保全應用只有一個配置服務實例
- **快取機制**：避免重複載入相同配置檔案
- **錯誤處理**：載入失敗時提供預設配置
- **類型安全**：支援所有星盤類型的配置載入

#### InterpretationConfig
- **配置模型**：表示整個配置檔案的結構
- **版本控制**：支援配置檔案版本管理
- **選項過濾**：自動過濾已停用的選項

#### InterpretationOption
- **選項模型**：表示單個解讀選項
- **類型轉換**：自動將字符串轉換為對應的 Flutter 類型
- **排序支援**：支援選項順序自定義

## 使用方式

### 1. 在頁面中使用
```dart
/// 本命盤解讀選項
List<Widget> _buildNatalOptions(BuildContext context) {
  return [
    FutureBuilder<InterpretationConfig>(
      future: InterpretationConfigService.instance.loadConfig(ChartType.natal),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          return Center(child: Text('載入配置失敗: ${snapshot.error}'));
        }

        final config = snapshot.data;
        return Column(
          children: config.enabledOptions.map((option) {
            return _buildOptionCard(
              context,
              title: option.title,
              subtitle: option.subtitle,
              icon: option.icon,
              color: option.color,
              interpretationType: option.interpretationType,
              questions: option.questions,
            );
          }).toList(),
        );
      },
    ),
  ];
}
```

### 2. 配置檔案管理
```dart
// 載入特定星盤類型的配置
final config = await InterpretationConfigService.instance.loadConfig(ChartType.natal);

// 清除快取（在配置更新後）
InterpretationConfigService.instance.clearCache();
```

## 優勢與特點

### 1. 靈活性
- **動態配置**：無需重新編譯即可修改解讀選項
- **A/B 測試**：可以輕鬆測試不同的選項配置
- **本地化支援**：可以為不同語言提供不同的配置檔案

### 2. 可維護性
- **集中管理**：所有解讀選項配置集中在 JSON 檔案中
- **版本控制**：支援配置檔案的版本管理和追蹤
- **結構化數據**：清晰的數據結構便於理解和修改

### 3. 擴展性
- **API 準備**：可以輕鬆改為從 API 載入配置
- **快取機制**：提高性能，減少重複載入
- **錯誤恢復**：載入失敗時自動使用預設配置

### 4. 開發效率
- **熱重載支援**：開發時可以快速測試配置變更
- **類型安全**：編譯時檢查類型錯誤
- **自動化**：減少手動編寫重複的選項代碼

## 未來擴展計劃

### 1. API 整合
```dart
// 未來可以輕鬆改為 API 載入
class InterpretationConfigService {
  Future<InterpretationConfig> loadConfig(ChartType chartType) async {
    // 優先從 API 載入
    try {
      final response = await http.get('/api/interpretation-config/$chartType');
      return InterpretationConfig.fromJson(response.data);
    } catch (e) {
      // 失敗時使用本地配置
      return loadLocalConfig(chartType);
    }
  }
}
```

### 2. 動態更新
- 支援配置檔案的熱更新
- 版本檢查和自動更新機制
- 用戶自定義選項支援

### 3. 多語言支援
- 支援多語言配置檔案
- 根據用戶語言設定載入對應配置
- 翻譯管理和同步機制

### 4. 個人化配置
- 用戶可以自定義解讀選項順序
- 隱藏不感興趣的選項
- 添加個人化問題模板

## 測試建議

### 1. 單元測試
- 測試配置檔案載入功能
- 測試錯誤處理機制
- 測試快取功能

### 2. 整合測試
- 測試完整的選項顯示流程
- 測試不同星盤類型的配置載入
- 測試配置檔案格式驗證

### 3. 用戶測試
- 測試載入性能
- 測試用戶體驗
- 測試錯誤情況下的用戶反饋

## 注意事項

1. **配置檔案格式**：必須嚴格遵循 JSON 格式規範
2. **圖示映射**：新增圖示時需要在 `_parseIcon` 方法中添加對應映射
3. **顏色映射**：新增顏色時需要在 `_parseColor` 方法中添加對應映射
4. **版本兼容**：修改配置檔案結構時需要考慮向後兼容性
5. **性能考量**：大型配置檔案可能影響載入性能，需要適當優化

這個配置系統為應用提供了更好的靈活性和可維護性，為未來的 API 整合和功能擴展奠定了良好的基礎。
