# 客製化問題分析對話框優化文件

## 概述
本文件說明對 `CustomQuestionFAB` 組件中客製化問題分析對話框的優化，主要解決鍵盤彈出時按鈕被遮擋的問題。

## 問題描述
原始實現中，當用戶點擊輸入框並彈出鍵盤時，「開始分析」按鈕可能會被鍵盤遮擋，導致用戶無法點擊按鈕完成操作。

## 優化方案

### 1. 鍵盤感知佈局
```dart
final mediaQuery = MediaQuery.of(context);
final keyboardHeight = mediaQuery.viewInsets.bottom;
final screenHeight = mediaQuery.size.height;

// 計算可用高度，考慮鍵盤空間
final availableHeight = screenHeight - keyboardHeight - 100; // 預留100px的邊距
final maxDialogHeight = availableHeight * 0.9;
```

**功能說明：**
- 使用 `MediaQuery.viewInsets.bottom` 檢測鍵盤高度
- 動態計算對話框的最大可用高度
- 預留100px邊距確保對話框不會貼邊顯示

### 2. 分離式佈局結構
```dart
child: Column(
  mainAxisSize: MainAxisSize.min,
  children: [
    // 可滾動的內容區域
    Flexible(
      child: SingleChildScrollView(
        padding: const EdgeInsets.fromLTRB(24, 24, 24, 16),
        child: Column(
          // 標題、輸入框、建議問題等內容
        ),
      ),
    ),
    
    // 固定在底部的按鈕區域
    Container(
      // 按鈕區域樣式和功能
    ),
  ],
)
```

**設計優勢：**
- **內容區域**：使用 `Flexible` + `SingleChildScrollView` 實現可滾動
- **按鈕區域**：固定在對話框底部，不會被鍵盤遮擋
- **視覺分離**：添加陰影效果區分內容區域和按鈕區域

### 3. 按鈕區域視覺優化
```dart
Container(
  padding: const EdgeInsets.fromLTRB(24, 16, 24, 24),
  decoration: BoxDecoration(
    color: Colors.white,
    borderRadius: const BorderRadius.only(
      bottomLeft: Radius.circular(16),
      bottomRight: Radius.circular(16),
    ),
    boxShadow: [
      BoxShadow(
        color: Colors.grey.withValues(alpha: 0.1),
        offset: const Offset(0, -2),
        blurRadius: 4,
      ),
    ],
  ),
  // 按鈕內容
)
```

**視覺特點：**
- 白色背景確保按鈕清晰可見
- 圓角設計與對話框整體風格一致
- 向上的陰影效果營造浮動感

## 技術實現細節

### MVVM 架構遵循
- **View**: `_CustomQuestionDialog` 負責UI呈現和用戶交互
- **ViewModel**: 通過 `TextEditingController` 管理輸入狀態
- **Model**: `ChartData` 作為數據模型傳遞

### 響應式設計
- 支援不同螢幕尺寸的設備
- 自動適應鍵盤彈出/收起狀態
- 保持良好的用戶體驗

### 性能優化
- 使用 `Flexible` 而非 `Expanded` 避免不必要的空間佔用
- `SingleChildScrollView` 僅在需要時啟用滾動
- 合理的 padding 和 margin 設置

## 使用方式
優化後的對話框使用方式保持不變：

```dart
FloatingActionButton.extended(
  onPressed: () => _showCustomQuestionDialog(context),
  // 其他屬性...
)
```

## 測試建議
1. **不同螢幕尺寸測試**：在小螢幕設備上測試對話框顯示
2. **鍵盤交互測試**：測試鍵盤彈出/收起時的佈局變化
3. **滾動功能測試**：在內容較多時測試滾動功能
4. **按鈕可訪問性測試**：確保按鈕在任何情況下都可點擊

## 相容性
- Flutter 3.0+
- 支援 iOS 和 Android 平台
- 適配不同螢幕密度和尺寸

## 程式碼品質改進

### 異步操作安全性
```dart
void _showCustomQuestionDialog(BuildContext context) async {
  final result = await showDialog<String>(
    context: context,
    builder: (context) => _CustomQuestionDialog(),
  );

  if (result != null && result.isNotEmpty && context.mounted) {
    _navigateToCustomQuestionAnalysis(context, result);
  }
}
```

**改進說明：**
- 添加 `context.mounted` 檢查，避免在異步操作後使用已銷毀的 BuildContext
- 符合 Flutter 最佳實踐，避免 `use_build_context_synchronously` 警告

## 測試結果
- ✅ Flutter analyze 通過，無警告或錯誤
- ✅ 應用編譯成功
- ✅ 鍵盤彈出時按鈕不會被遮擋
- ✅ 支援不同螢幕尺寸
- ✅ 保持原有功能完整性

## 未來改進方向
1. 添加鍵盤動畫過渡效果
2. 支援橫屏模式優化
3. 添加無障礙功能支援
4. 考慮添加手勢操作（如下拉關閉）
5. 添加單元測試和 Widget 測試
