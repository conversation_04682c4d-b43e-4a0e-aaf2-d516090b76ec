# Firebase Remote Config AI API Keys 使用指南

## 🎯 概述

使用 Firebase Remote Config 來管理 AI API Keys，支援不同平台使用不同的 API Keys，實現靈活的配置管理。

## ✅ 已實作功能

### 1. Remote Config 服務

#### 核心服務
**檔案**：`lib/services/remote_config_service.dart`

**功能特色**：
- **多平台支援**：iOS、Android、Web、macOS、Windows
- **自動平台檢測**：根據當前平台自動選擇對應的 API Key
- **錯誤處理**：完善的錯誤處理和降級機制
- **配置刷新**：支援手動刷新配置
- **狀態監控**：提供詳細的服務狀態信息

#### API Keys 支援
- **OpenAI API Key**：支援 `sk-` 和 `sk-proj-` 格式
- **Groq AI API Key**：支援 `gsk_` 格式
- **Google Gemini API Key**：支援標準格式

### 2. 配置格式

#### Firebase Remote Config 參數
**參數名稱**：`ai_api_keys`
**格式**：JSON 字符串

```json
{
  "OpenAIKey": {
    "ios": "sk-proj-your-ios-key",
    "android": "sk-proj-your-android-key",
    "web": "sk-proj-your-web-key",
    "macos": "sk-proj-your-macos-key",
    "windows": "sk-proj-your-windows-key"
  },
  "GroqAIKey": {
    "ios": "gsk_your-ios-key",
    "android": "gsk_your-android-key",
    "web": "gsk_your-web-key",
    "macos": "gsk_your-macos-key",
    "windows": "gsk_your-windows-key"
  },
  "GoogleGeminiKey": {
    "ios": "your-ios-gemini-key",
    "android": "your-android-gemini-key",
    "web": "your-web-gemini-key",
    "macos": "your-macos-gemini-key",
    "windows": "your-windows-gemini-key"
  }
}
```

### 3. 診斷工具

#### Remote Config 診斷器
**檔案**：`lib/utils/remote_config_diagnostic.dart`

**檢查項目**：
- Remote Config 服務初始化狀態
- AI API Keys 配置結構
- 各平台 API Keys 有效性
- API Key 格式驗證
- 配置刷新測試

#### 調試界面
**檔案**：`lib/ui/pages/debug/firebase_auth_debug_page.dart`

**新增功能**：
- **Remote Config 診斷**按鈕
- 詳細的配置狀態顯示
- API Keys 可用性檢查
- 一鍵複製診斷報告

### 4. 使用示例

#### AI 服務示例
**檔案**：`lib/services/ai_service_example.dart`

**功能展示**：
- 如何獲取和使用 OpenAI API Key
- 如何獲取和使用 Groq AI API Key
- 如何獲取和使用 Google Gemini API Key
- 智能選擇可用的 AI 服務
- 測試所有 AI 服務的可用性

## 🔧 設定步驟

### 1. Firebase 控制台設定

#### 步驟 1：啟用 Remote Config
1. 登入 [Firebase 控制台](https://console.firebase.google.com/)
2. 選擇您的項目
3. 進入「Remote Config」
4. 點擊「開始使用」

#### 步驟 2：添加參數
1. 點擊「添加參數」
2. 參數鍵：`ai_api_keys`
3. 默認值：完整的 JSON 配置（如上所示）
4. 點擊「發布更改」

#### 步驟 3：設定條件（可選）
```
條件名稱：iOS 用戶
條件：app.platform == 'ios'
值：iOS 專用的配置

條件名稱：Android 用戶
條件：app.platform == 'android'
值：Android 專用的配置
```

### 2. 應用程式設定

#### 步驟 1：添加依賴項
```yaml
# pubspec.yaml
dependencies:
  firebase_remote_config: ^5.1.4
```

#### 步驟 2：初始化服務
```dart
// main.dart
import 'services/remote_config_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // 初始化 Firebase
  await Firebase.initializeApp();
  
  // 初始化 Remote Config
  await RemoteConfigService.initialize();
  
  runApp(MyApp());
}
```

#### 步驟 3：使用 API Keys
```dart
// 獲取 OpenAI API Key
final openAIKey = RemoteConfigService.getOpenAIKey();

// 獲取 Groq AI API Key
final groqKey = RemoteConfigService.getGroqAIKey();

// 獲取 Google Gemini API Key
final geminiKey = RemoteConfigService.getGoogleGeminiKey();

// 獲取所有 API Keys
final allKeys = RemoteConfigService.getAllApiKeys();
```

## 🔍 使用方法

### 1. 基本使用

#### 獲取單個 API Key
```dart
import 'package:your_app/services/remote_config_service.dart';

class MyAIService {
  Future<String> generateText(String prompt) async {
    // 獲取 OpenAI API Key
    final apiKey = RemoteConfigService.getOpenAIKey();
    
    if (apiKey.isEmpty) {
      throw Exception('OpenAI API Key 未設定');
    }
    
    // 使用 API Key 調用 OpenAI API
    // ... API 調用邏輯
  }
}
```

#### 智能選擇 AI 服務
```dart
class SmartAIService {
  Future<String> generateText(String prompt) async {
    final allKeys = RemoteConfigService.getAllApiKeys();
    
    // 按優先級嘗試不同的 AI 服務
    if (allKeys['openai']?.isNotEmpty == true) {
      return await _callOpenAI(prompt);
    } else if (allKeys['groq']?.isNotEmpty == true) {
      return await _callGroqAI(prompt);
    } else if (allKeys['gemini']?.isNotEmpty == true) {
      return await _callGemini(prompt);
    } else {
      throw Exception('沒有可用的 AI 服務');
    }
  }
}
```

### 2. 配置刷新

#### 手動刷新配置
```dart
// 刷新 Remote Config
final updated = await RemoteConfigService.refresh();
if (updated) {
  print('配置已更新');
  // 重新獲取 API Keys
  final newKey = RemoteConfigService.getOpenAIKey();
}
```

#### 定期刷新
```dart
class ConfigManager {
  Timer? _refreshTimer;
  
  void startPeriodicRefresh() {
    _refreshTimer = Timer.periodic(Duration(hours: 1), (timer) async {
      try {
        await RemoteConfigService.refresh();
      } catch (e) {
        print('配置刷新失敗: $e');
      }
    });
  }
  
  void stopPeriodicRefresh() {
    _refreshTimer?.cancel();
  }
}
```

### 3. 錯誤處理

#### 完整的錯誤處理
```dart
class RobustAIService {
  Future<String> generateText(String prompt) async {
    try {
      // 檢查 Remote Config 狀態
      final status = RemoteConfigService.getStatus();
      if (status['isInitialized'] != true) {
        throw Exception('Remote Config 未初始化');
      }
      
      // 獲取 API Key
      final apiKey = RemoteConfigService.getOpenAIKey();
      if (apiKey.isEmpty) {
        throw Exception('OpenAI API Key 未設定');
      }
      
      // 調用 API
      return await _callAPI(apiKey, prompt);
      
    } catch (e) {
      // 記錄錯誤
      logger.e('AI 服務調用失敗: $e');
      
      // 嘗試刷新配置
      try {
        await RemoteConfigService.refresh();
        final newKey = RemoteConfigService.getOpenAIKey();
        if (newKey.isNotEmpty) {
          return await _callAPI(newKey, prompt);
        }
      } catch (refreshError) {
        logger.e('配置刷新失敗: $refreshError');
      }
      
      rethrow;
    }
  }
}
```

## 📊 診斷和監控

### 1. 使用診斷工具

#### 運行診斷
```dart
// 運行完整診斷
final diagnostic = await RemoteConfigDiagnostic.runFullDiagnostic();
RemoteConfigDiagnostic.printDiagnostic(diagnostic);

// 獲取配置摘要
final summary = RemoteConfigDiagnostic.getConfigSummary();
print('Remote Config 狀態: ${summary['isInitialized']}');
print('API Keys 可用性: ${summary['apiKeys']}');
```

#### 在調試頁面中使用
1. 打開應用的調試頁面
2. 點擊「Remote Config 診斷」按鈕
3. 查看詳細的診斷結果
4. 根據建議進行配置調整

### 2. 監控指標

#### 關鍵指標
```dart
class RemoteConfigMonitor {
  static Map<String, dynamic> getMetrics() {
    final status = RemoteConfigService.getStatus();
    final allKeys = RemoteConfigService.getAllApiKeys();
    
    return {
      'isInitialized': status['isInitialized'],
      'lastFetchTime': status['lastFetchTime'],
      'apiKeysCount': allKeys.values.where((key) => key.isNotEmpty).length,
      'availableServices': allKeys.entries
          .where((entry) => entry.value.isNotEmpty)
          .map((entry) => entry.key)
          .toList(),
    };
  }
}
```

## 🚀 最佳實踐

### 1. 安全性

#### API Key 保護
- 使用 Remote Config 避免在代碼中硬編碼 API Keys
- 不同平台使用不同的 API Keys
- 定期輪換 API Keys
- 監控 API 使用量

#### 訪問控制
```dart
class SecureAIService {
  static bool _isAuthorized() {
    // 檢查用戶權限
    final user = AuthService.getCurrentUser();
    return user != null && !user.isAnonymous;
  }
  
  static Future<String> generateText(String prompt) async {
    if (!_isAuthorized()) {
      throw Exception('未授權的 API 訪問');
    }
    
    // 繼續 API 調用
    final apiKey = RemoteConfigService.getOpenAIKey();
    // ...
  }
}
```

### 2. 性能優化

#### 快取機制
```dart
class CachedRemoteConfigService {
  static String? _cachedOpenAIKey;
  static DateTime? _lastCacheTime;
  static const Duration _cacheTimeout = Duration(minutes: 30);
  
  static String getOpenAIKey() {
    final now = DateTime.now();
    
    if (_cachedOpenAIKey != null && 
        _lastCacheTime != null && 
        now.difference(_lastCacheTime!) < _cacheTimeout) {
      return _cachedOpenAIKey!;
    }
    
    _cachedOpenAIKey = RemoteConfigService.getOpenAIKey();
    _lastCacheTime = now;
    
    return _cachedOpenAIKey!;
  }
}
```

### 3. 錯誤恢復

#### 降級策略
```dart
class FallbackAIService {
  static const Map<String, String> _fallbackKeys = {
    'openai': 'FALLBACK_OPENAI_KEY',
    'groq': 'FALLBACK_GROQ_KEY',
  };
  
  static String getAPIKey(String service) {
    // 首先嘗試從 Remote Config 獲取
    String key;
    switch (service) {
      case 'openai':
        key = RemoteConfigService.getOpenAIKey();
        break;
      case 'groq':
        key = RemoteConfigService.getGroqAIKey();
        break;
      default:
        key = '';
    }
    
    // 如果 Remote Config 失敗，使用降級 Key
    if (key.isEmpty) {
      key = _fallbackKeys[service] ?? '';
      logger.w('使用降級 API Key: $service');
    }
    
    return key;
  }
}
```

## 📝 總結

### 主要優勢
- ✅ **安全性**：API Keys 不在代碼中硬編碼
- ✅ **靈活性**：不同平台可使用不同的 API Keys
- ✅ **可維護性**：無需重新發布應用即可更新 API Keys
- ✅ **監控性**：完整的診斷和監控工具
- ✅ **可靠性**：完善的錯誤處理和降級機制

### 技術特色
- **多平台支援**：iOS、Android、Web、macOS、Windows
- **自動平台檢測**：根據運行平台自動選擇對應配置
- **智能降級**：Remote Config 失敗時使用默認配置
- **實時更新**：支援配置的實時刷新
- **完整診斷**：專業的診斷工具和狀態監控

### 商業價值
- **成本控制**：不同平台可使用不同的 API 配額
- **安全合規**：符合 API Key 管理最佳實踐
- **運營效率**：無需重新發布應用即可調整配置
- **用戶體驗**：智能選擇最佳可用的 AI 服務

現在 Astreal 應用擁有了企業級的 AI API Keys 管理系統，可以安全、靈活地管理多個 AI 服務的 API Keys，並根據不同平台提供最適合的配置！
