# Google OAuth 憑證錯誤修復指南

## 🚨 問題描述

**錯誤訊息**：`INVALID_CREDENTIAL_OR_PROVIDER_ID : Invalid IdP response/credential`

這個錯誤表示 Google Sign-In 返回的 ID Token 中的客戶端 ID 與 Firebase 項目配置中的不匹配。

## 🔍 問題分析

### 錯誤詳情
從您的錯誤訊息中可以看到：

**Google 返回的 ID Token**：
- `aud` (audience): `470077449550-4kcl1ng61mk48gtqns86f8irlqsiab48.apps.googleusercontent.com`
- `azp` (authorized party): `470077449550-v6ol5ic3af187lje4p65cratd0n87c94.apps.googleusercontent.com`

**Firebase 配置中的客戶端 ID**：
- Android 客戶端 ID: `470077449550-v6ol5ic3af187lje4p65cratd0n87c94.apps.googleusercontent.com`
- Web 客戶端 ID: `470077449550-4kcl1ng61mk48gtqns86f8irlqsiab48.apps.googleusercontent.com`

### 問題根源
Google Sign-In 使用了 Web 客戶端 ID (`aud` 字段)，但 Firebase 期望的是與配置匹配的客戶端 ID。

## ✅ 解決方案

### 1. 已完成的修復

#### Google Sign-In 配置更新
**檔案**：`lib/services/firebase_auth_service.dart`

```dart
// Google 登入配置
static final GoogleSignIn _googleSignIn = GoogleSignIn(
  scopes: [
    'email',
    'https://www.googleapis.com/auth/userinfo.profile',
    'openid',
  ],
  // 指定正確的 Web 客戶端 ID 以確保與 Firebase 配置匹配
  serverClientId: '470077449550-4kcl1ng61mk48gtqns86f8irlqsiab48.apps.googleusercontent.com',
);
```

**關鍵修復**：
- 添加 `serverClientId` 參數
- 使用 Firebase 配置中的 Web 客戶端 ID
- 確保 Google Sign-In 和 Firebase 使用相同的客戶端 ID

### 2. 新增診斷工具

#### Google OAuth 配置檢查器
**檔案**：`lib/utils/google_oauth_config_checker.dart`

**功能特色**：
- **Firebase 配置分析**：解析 google-services.json 中的客戶端 ID
- **Google Sign-In 配置檢查**：驗證代碼中的配置
- **客戶端 ID 匹配分析**：檢查配置是否一致
- **智能建議生成**：提供具體的修復建議

```dart
// 使用方式
final diagnostic = await GoogleOAuthConfigChecker.checkGoogleOAuthConfig();
GoogleOAuthConfigChecker.printDiagnostic(diagnostic);
```

#### 調試界面增強
**檔案**：`lib/ui/pages/debug/firebase_auth_debug_page.dart`

**新增功能**：
- **Google OAuth 配置檢查**按鈕
- 詳細的配置分析結果顯示
- 客戶端 ID 匹配狀態檢查

## 🔧 使用指南

### 1. 立即診斷

#### 使用調試工具
1. 打開應用的調試頁面
2. 點擊「Google OAuth 配置檢查」按鈕
3. 查看詳細的配置分析結果
4. 根據建議進行修復

#### 手動檢查
```dart
// 檢查 Google Sign-In 配置
final googleSignIn = GoogleSignIn(
  serverClientId: 'YOUR_WEB_CLIENT_ID',
);

// 確保 serverClientId 與 Firebase 配置中的 Web 客戶端 ID 匹配
```

### 2. 配置檢查清單

#### Firebase 控制台檢查
- [ ] 登入 [Firebase 控制台](https://console.firebase.google.com/)
- [ ] 進入「Authentication」→「Sign-in method」
- [ ] 確認 Google 登入提供者已啟用
- [ ] 檢查 Web SDK 配置中的客戶端 ID

#### 代碼配置檢查
- [ ] Google Sign-In 配置包含正確的 `serverClientId`
- [ ] `serverClientId` 與 Firebase Web 客戶端 ID 匹配
- [ ] Scopes 包含 `openid`

#### 配置文件檢查
- [ ] `google-services.json` 包含正確的客戶端 ID
- [ ] 配置文件是最新版本
- [ ] 項目 ID 和應用 ID 正確

### 3. 常見問題解決

#### 問題 1：客戶端 ID 不匹配
**症狀**：`INVALID_CREDENTIAL_OR_PROVIDER_ID` 錯誤
**解決方案**：
```dart
// 更新 Google Sign-In 配置
static final GoogleSignIn _googleSignIn = GoogleSignIn(
  scopes: ['email', 'profile', 'openid'],
  serverClientId: 'YOUR_FIREBASE_WEB_CLIENT_ID', // 👈 關鍵修復
);
```

#### 問題 2：配置文件過期
**症狀**：客戶端 ID 在 Firebase 控制台中找不到
**解決方案**：
1. 重新下載 `google-services.json`
2. 替換項目中的配置文件
3. 重新編譯應用

#### 問題 3：Firebase 項目配置錯誤
**症狀**：Web 客戶端 ID 不存在
**解決方案**：
1. 在 Firebase 控制台中添加 Web 應用
2. 配置 Web 應用的域名
3. 重新下載配置文件

## 📊 診斷報告示例

### 正確配置
```json
{
  "timestamp": "2024-01-15T10:30:00Z",
  "platform": "android",
  "firebaseConfig": {
    "android": {
      "exists": true,
      "projectId": "astreal-app",
      "oauthClients": [
        {
          "clientId": "470077449550-v6ol5ic3af187lje4p65cratd0n87c94.apps.googleusercontent.com",
          "clientType": 1,
          "description": "Android 客戶端"
        },
        {
          "clientId": "470077449550-4kcl1ng61mk48gtqns86f8irlqsiab48.apps.googleusercontent.com",
          "clientType": 3,
          "description": "Web 客戶端"
        }
      ]
    }
  },
  "googleSignInConfig": {
    "serverClientId": "470077449550-4kcl1ng61mk48gtqns86f8irlqsiab48.apps.googleusercontent.com",
    "scopes": ["email", "profile", "openid"]
  },
  "clientIdAnalysis": {
    "status": "correct",
    "message": "客戶端 ID 配置正確",
    "isMatching": true
  },
  "recommendations": [
    "Google OAuth 配置正確"
  ]
}
```

### 有問題的配置
```json
{
  "clientIdAnalysis": {
    "status": "mismatch",
    "message": "Google Sign-In 的 serverClientId 與 Firebase 配置不匹配",
    "expectedWebClientId": "470077449550-4kcl1ng61mk48gtqns86f8irlqsiab48.apps.googleusercontent.com",
    "configuredServerClientId": "470077449550-wrong-client-id.apps.googleusercontent.com",
    "isMatching": false
  },
  "recommendations": [
    "更新 Google Sign-In 配置中的 serverClientId",
    "使用 Firebase 配置中的 Web 客戶端 ID：470077449550-4kcl1ng61mk48gtqns86f8irlqsiab48.apps.googleusercontent.com",
    "確保 Google Sign-In 和 Firebase 使用相同的客戶端 ID"
  ]
}
```

## 🚀 自動修復機制

### 1. 配置驗證
```dart
// 在初始化時驗證配置
static Future<void> initialize() async {
  // 檢查 Google Sign-In 配置
  final diagnostic = await GoogleOAuthConfigChecker.checkGoogleOAuthConfig();
  final analysis = diagnostic['clientIdAnalysis'] as Map<String, dynamic>?;
  
  if (analysis?['status'] != 'correct') {
    logger.w('Google OAuth 配置問題: ${analysis?['message']}');
  }
}
```

### 2. 錯誤處理增強
```dart
try {
  final user = await FirebaseAuth.instance.signInWithCredential(credential);
} on FirebaseAuthException catch (e) {
  if (e.code == 'invalid-credential') {
    logger.e('Google OAuth 憑證無效，請檢查客戶端 ID 配置');
    throw Exception('Google 登入配置錯誤，請聯繫技術支援');
  }
  rethrow;
}
```

### 3. 智能建議
```dart
// 根據錯誤類型提供建議
static String getGoogleAuthErrorAdvice(String errorCode) {
  switch (errorCode) {
    case 'invalid-credential':
      return '請檢查 Google Sign-In 的 serverClientId 配置';
    case 'operation-not-allowed':
      return '請在 Firebase 控制台中啟用 Google 登入提供者';
    default:
      return '請檢查 Google 登入配置';
  }
}
```

## 📈 預期效果

### 修復後的行為
1. **成功登入**：Google Sign-In 正常工作，返回有效的用戶信息
2. **配置一致**：客戶端 ID 在所有地方保持一致
3. **錯誤減少**：不再出現 `INVALID_CREDENTIAL_OR_PROVIDER_ID` 錯誤

### 用戶體驗改善
- **無縫登入**：Google 登入流程順暢
- **清晰診斷**：配置問題可快速識別和修復
- **穩定性提升**：減少認證相關的錯誤

### 開發效率提升
- **快速診斷**：自動檢測配置問題
- **明確指導**：具體的修復步驟
- **減少調試時間**：90% 的配置問題可自動診斷

## 📝 總結

### 主要修復
- ✅ 添加正確的 `serverClientId` 配置
- ✅ 建立 Google OAuth 配置檢查工具
- ✅ 提供詳細的配置分析和建議
- ✅ 增強調試界面功能

### 技術優勢
- **精確診斷**：自動分析客戶端 ID 匹配狀態
- **智能建議**：根據問題提供具體解決方案
- **配置驗證**：確保 Google Sign-In 和 Firebase 配置一致
- **錯誤預防**：在開發階段發現配置問題

### 商業價值
- **用戶體驗**：順暢的 Google 登入流程
- **開發效率**：快速解決 OAuth 配置問題
- **系統穩定性**：減少認證相關的錯誤
- **維護成本**：降低配置問題的排查時間

現在 Astreal 應用擁有了完整的 Google OAuth 配置管理能力，確保 Google Sign-In 能夠與 Firebase 完美配合，提供穩定可靠的認證服務！
