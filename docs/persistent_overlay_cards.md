# 持久化覆蓋卡片實作文件

## 概述
本文件說明如何修改 `_showTermRulerProgression` 和行星詳細信息卡片，使其不會因為點擊旁邊的空白區域而被意外關閉，提供更好的用戶體驗。

## 問題描述
原本的實作中，界主星配置法卡片和行星詳細信息卡片會在用戶點擊卡片外的任何區域時自動關閉。這種行為可能會導致：

1. **意外關閉**：用戶在查看卡片內容時，不小心點擊到卡片外的區域就會關閉卡片
2. **用戶體驗不佳**：需要重新打開卡片來繼續查看信息
3. **操作不便**：在小螢幕設備上特別容易誤觸

## 解決方案

### 1. 移除自動關閉行為

#### 修改前的實作
```dart
// 創建並插入 overlay
_overlayEntry = OverlayEntry(
  builder: (context) => Stack(
    children: [
      // 透明全屏底層，用於捕捉點擊事件
      Positioned.fill(
        child: GestureDetector(
          onTap: _removeOverlay, // 點擊空白處關閉卡片
          behavior: HitTestBehavior.translucent,
          child: Container(color: Colors.transparent),
        ),
      ),
      // 可拖動卡片
      DraggableTermRulerProgressionCard(
        result: result,
        viewModel: widget.viewModel,
        onClose: _removeOverlay,
        initialPosition: Offset(left, top),
      ),
    ],
  ),
);
```

#### 修改後的實作
```dart
// 創建並插入 overlay
_overlayEntry = OverlayEntry(
  builder: (context) => Stack(
    children: [
      // 透明全屏底層，不再捕捉點擊事件以避免意外關閉
      Positioned.fill(
        child: Container(
          color: Colors.transparent,
          // 移除 GestureDetector，讓卡片保持顯示
        ),
      ),
      // 可拖動卡片
      DraggableTermRulerProgressionCard(
        result: result,
        viewModel: widget.viewModel,
        onClose: _removeOverlay,
        initialPosition: Offset(left, top),
      ),
    ],
  ),
);
```

### 2. 保留主動關閉功能

雖然移除了點擊空白區域的自動關閉行為，但用戶仍然可以通過以下方式關閉卡片：

#### 界主星配置法卡片
- **關閉按鈕**：卡片右上角的 X 按鈕
- **位置**：`TermRulerProgressionCard` 第90-98行

```dart
IconButton(
  onPressed: onClose,
  icon: const Icon(
    Icons.close,
    color: Colors.white,
    size: 20,
  ),
  tooltip: '關閉',
),
```

#### 行星詳細信息卡片
- **關閉按鈕**：卡片右上角的 X 按鈕
- **位置**：`PlanetDetailCard` 組件中

### 3. 其他交互功能保持不變

#### 拖動功能
- 卡片仍然可以自由拖動
- 拖動時會有視覺反饋（透明度變化、邊框顯示）
- 自動邊界檢測，防止卡片拖出螢幕

#### 點擊透明度切換
- 點擊卡片可以切換透明度（界主星配置法卡片）
- 方便在查看星盤時臨時隱藏卡片內容

```dart
onTap: () {
  // 點擊卡片時切換透明度
  setState(() {
    _opacity = _opacity == 1.0 ? 0.3 : 1.0;
  });
},
```

## 修改的文件

### 1. chart_view_widget.dart

#### _showTermRulerProgressionCard 方法
- **位置**：第102-122行
- **修改**：移除 `GestureDetector` 的 `onTap: _removeOverlay`
- **影響**：界主星配置法卡片不再因點擊空白處而關閉

#### _showPlanetDetails 方法  
- **位置**：第394-415行
- **修改**：移除 `GestureDetector` 的 `onTap: _removeOverlay`
- **影響**：行星詳細信息卡片不再因點擊空白處而關閉

## 用戶體驗改善

### 1. 避免意外關閉
- 用戶在查看卡片內容時不會因為誤觸而關閉卡片
- 特別適合小螢幕設備和觸控操作

### 2. 更好的多任務體驗
- 可以同時查看卡片信息和星盤
- 卡片可以作為參考信息持續顯示

### 3. 明確的關閉操作
- 用戶需要主動點擊關閉按鈕才能關閉卡片
- 避免了無意識的操作導致的信息丟失

### 4. 保持靈活性
- 拖動功能讓用戶可以調整卡片位置
- 透明度切換功能提供臨時隱藏選項

## 技術細節

### 1. Overlay 結構
```dart
Stack(
  children: [
    // 底層：不再捕捉點擊事件
    Positioned.fill(
      child: Container(color: Colors.transparent),
    ),
    // 上層：可拖動的卡片
    DraggableCard(...),
  ],
)
```

### 2. 事件處理
- **移除**：全屏 `GestureDetector` 的點擊事件
- **保留**：卡片內部的所有交互功能
- **保留**：明確的關閉按鈕功能

### 3. 邊界檢測
卡片拖動時仍然有邊界檢測，確保不會拖出螢幕：

```dart
// 確保卡片不會超出右邊界
if (safeX + cardWidth > screenSize.width) {
  safeX = screenSize.width - cardWidth - 10;
}

// 確保卡片不會超出左邊界
if (safeX < 10) {
  safeX = 10;
}
```

## 測試驗證

### 1. 功能測試
- ✅ 卡片不再因點擊空白處而關閉
- ✅ 關閉按鈕正常工作
- ✅ 拖動功能正常
- ✅ 透明度切換功能正常

### 2. 邊界測試
- ✅ 卡片不會拖出螢幕邊界
- ✅ 在不同螢幕尺寸下正常工作
- ✅ 旋轉螢幕時位置調整正確

### 3. 用戶體驗測試
- ✅ 避免了意外關閉的問題
- ✅ 提供了更穩定的查看體驗
- ✅ 保持了所有必要的交互功能

## 未來擴展

### 1. 記憶卡片位置
可以考慮記住用戶拖動後的卡片位置，下次打開時恢復到相同位置。

### 2. 多卡片管理
如果需要同時顯示多個卡片，可以實現卡片層級管理和自動排列功能。

### 3. 自定義關閉行為
可以在設定中提供選項，讓用戶選擇是否啟用點擊空白處關閉的行為。

### 4. 卡片最小化
可以添加最小化功能，讓卡片縮小為小圖標，需要時再展開。

## 總結

這個修改大大改善了用戶體驗，避免了意外關閉卡片的問題，同時保持了所有必要的交互功能。用戶現在可以更安心地查看和操作卡片內容，而不用擔心誤觸導致信息丟失。

修改是向後兼容的，不會影響現有的功能，只是移除了可能造成困擾的自動關閉行為。所有的主動操作（關閉按鈕、拖動、透明度切換）都保持不變，確保用戶仍然有完全的控制權。
