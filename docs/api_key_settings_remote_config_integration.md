# ApiKeySettingsPage Remote Config 整合

## 🎯 概述

已成功將 `ApiKeySettingsPage` 整合 Firebase Remote Config，讓它能夠從雲端配置獲取 AI API Keys，並提供本地存儲作為降級方案。

## ✅ 已完成的修改

### 1. 核心功能整合

#### 導入 Remote Config 服務
**檔案**：`lib/ui/pages/settings/api_key_settings_page.dart`

```dart
import '../../../services/remote_config_service.dart';
import '../../../utils/remote_config_diagnostic.dart';
```

#### 新增狀態變數
```dart
// Remote Config 相關狀態
Map<String, dynamic>? _remoteConfigStatus;
bool _showRemoteConfigKeys = false;
```

### 2. API Keys 載入邏輯優化

#### 智能載入策略
```dart
Future<void> _loadApiKeys() async {
  try {
    // 獲取 Remote Config 狀態
    _remoteConfigStatus = RemoteConfigDiagnostic.getConfigSummary();
    
    // 優先從 Remote Config 獲取 API Keys
    String openaiKey = '';
    String groqKey = '';
    String geminiKey = '';
    
    if (_remoteConfigStatus?['isInitialized'] == true) {
      // 從 Remote Config 獲取
      openaiKey = RemoteConfigService.getOpenAIKey();
      groqKey = RemoteConfigService.getGroqAIKey();
      geminiKey = RemoteConfigService.getGoogleGeminiKey();
    } else {
      // 降級到本地存儲
      openaiKey = await AIApiService.getOpenAIApiKey() ?? '';
      groqKey = await AIApiService.getGroqApiKey() ?? '';
      geminiKey = await AIApiService.getGeminiApiKey() ?? '';
    }
    
    // 更新 UI
    setState(() {
      _openaiController.text = openaiKey;
      _groqController.text = groqKey;
      _geminiController.text = geminiKey;
      _isLoading = false;
    });
  } catch (e) {
    // 錯誤處理
  }
}
```

### 3. Remote Config 刷新功能

#### 手動刷新方法
```dart
Future<void> _refreshRemoteConfig() async {
  try {
    final updated = await RemoteConfigService.refresh();
    await _loadApiKeys();
    
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(updated ? 'Remote Config 已更新' : 'Remote Config 無新更新'),
          backgroundColor: updated ? AppColors.successGreen : AppColors.royalIndigo,
        ),
      );
    }
  } catch (e) {
    // 錯誤處理
  }
}
```

### 4. UI 增強功能

#### AppBar 狀態指示器
```dart
appBar: AppBar(
  title: const Text('API Key 設置'),
  backgroundColor: AppColors.royalIndigo,
  foregroundColor: Colors.white,
  actions: [
    // Remote Config 狀態指示器
    if (_remoteConfigStatus != null) ...[
      IconButton(
        icon: Icon(
          _remoteConfigStatus!['isInitialized'] == true 
              ? Icons.cloud_done 
              : Icons.cloud_off,
          color: _remoteConfigStatus!['isInitialized'] == true 
              ? AppColors.successGreen 
              : Colors.orange,
        ),
        onPressed: () {
          setState(() {
            _showRemoteConfigKeys = !_showRemoteConfigKeys;
          });
        },
        tooltip: _remoteConfigStatus!['isInitialized'] == true 
            ? 'Remote Config 已連接' 
            : 'Remote Config 未連接',
      ),
      // 刷新按鈕
      IconButton(
        icon: const Icon(Icons.refresh),
        onPressed: _refreshRemoteConfig,
        tooltip: '刷新 Remote Config',
      ),
    ],
  ],
),
```

#### 狀態信息顯示
```dart
// Remote Config 狀態信息
if (_remoteConfigStatus != null) ...[
  Row(
    children: [
      Icon(
        _remoteConfigStatus!['isInitialized'] == true 
            ? Icons.cloud_done 
            : Icons.cloud_off,
        size: 16,
        color: _remoteConfigStatus!['isInitialized'] == true 
            ? AppColors.successGreen 
            : Colors.orange,
      ),
      const SizedBox(width: 8),
      Expanded(
        child: Text(
          _remoteConfigStatus!['isInitialized'] == true 
              ? 'Remote Config 已連接，優先使用雲端配置'
              : 'Remote Config 未連接，使用本地存儲',
          style: TextStyle(
            fontSize: 12,
            color: _remoteConfigStatus!['isInitialized'] == true 
                ? AppColors.successGreen 
                : Colors.orange,
          ),
        ),
      ),
    ],
  ),
],
```

#### API Key 來源指示器
每個 API Key 輸入框都會顯示該 Key 是來自 Remote Config 還是本地存儲：

```dart
Row(
  children: [
    Text('OpenAI API Key', style: ...),
    const SizedBox(width: 8),
    if (_remoteConfigStatus?['isInitialized'] == true && 
        RemoteConfigService.getOpenAIKey().isNotEmpty) ...[
      Container(
        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
        decoration: BoxDecoration(
          color: AppColors.successGreen.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(4),
          border: Border.all(color: AppColors.successGreen, width: 0.5),
        ),
        child: Text(
          'Remote',
          style: TextStyle(
            fontSize: 10,
            color: AppColors.successGreen,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    ] else ...[
      Container(
        // Local 指示器樣式
        child: Text('Local', style: ...),
      ),
    ],
  ],
),
```

## 🔧 功能特色

### 1. 智能降級機制
- **Remote Config 可用**：優先使用雲端配置的 API Keys
- **Remote Config 不可用**：自動降級到本地存儲的 API Keys
- **無縫切換**：用戶無感知的降級和恢復

### 2. 視覺化狀態指示
- **雲端圖標**：顯示 Remote Config 連接狀態
- **來源標籤**：每個 API Key 顯示來源（Remote/Local）
- **顏色編碼**：綠色表示雲端，灰色表示本地

### 3. 實時刷新功能
- **手動刷新**：點擊刷新按鈕立即獲取最新配置
- **狀態反饋**：顯示刷新結果和更新狀態
- **自動重載**：刷新後自動重新載入 API Keys

### 4. 支援的 API Keys
- **OpenAI API Key**：從 Remote Config 獲取
- **Groq AI API Key**：從 Remote Config 獲取
- **Google Gemini API Key**：從 Remote Config 獲取
- **Anthropic API Key**：暫時從本地存儲獲取（Remote Config 中未配置）

## 📊 用戶體驗

### 1. 狀態可見性
用戶可以清楚地看到：
- Remote Config 是否已連接
- 每個 API Key 的來源
- 配置的最後更新時間

### 2. 操作便利性
- **一鍵刷新**：輕鬆獲取最新的雲端配置
- **狀態切換**：點擊雲端圖標查看詳細狀態
- **即時反饋**：操作結果立即顯示

### 3. 錯誤處理
- **網路問題**：自動降級到本地存儲
- **配置錯誤**：顯示清晰的錯誤訊息
- **恢復機制**：問題解決後自動恢復雲端配置

## 🎯 技術優勢

### 1. 混合架構
- **雲端優先**：優先使用 Remote Config 的最新配置
- **本地備份**：本地存儲作為可靠的降級方案
- **無縫整合**：兩種方式無縫切換

### 2. 實時同步
- **配置更新**：無需重新發布應用即可更新 API Keys
- **平台特定**：不同平台可使用不同的 API Keys
- **即時生效**：配置更新後立即生效

### 3. 安全性
- **雲端管理**：API Keys 在 Firebase 控制台中安全管理
- **本地加密**：本地存儲的 API Keys 仍然加密保存
- **訪問控制**：通過 Firebase 規則控制配置訪問

## 📝 使用指南

### 1. 查看狀態
- 查看 AppBar 右上角的雲端圖標
- 綠色 ✅ = Remote Config 已連接
- 橙色 ⚠️ = Remote Config 未連接

### 2. 刷新配置
- 點擊 AppBar 右上角的刷新按鈕
- 等待刷新完成的提示訊息
- 查看 API Keys 是否有更新

### 3. 檢查來源
- 查看每個 API Key 標題旁的標籤
- "Remote" = 來自 Remote Config
- "Local" = 來自本地存儲

### 4. 故障排除
- 如果 Remote Config 未連接，檢查網路連接
- 如果 API Keys 為空，檢查 Firebase 控制台配置
- 使用調試頁面的 Remote Config 診斷功能

## 🚀 未來擴展

### 1. 更多 AI 服務
- 可以輕鬆添加更多 AI 服務的 API Keys
- 統一的配置管理和顯示邏輯

### 2. 配置版本控制
- 追蹤配置變更歷史
- 支援配置回滾功能

### 3. 使用量監控
- 整合 API 使用量監控
- 顯示各個 API Key 的使用狀態

現在 `ApiKeySettingsPage` 已經完全整合了 Remote Config，提供了智能的 API Key 管理功能，讓用戶可以享受雲端配置的便利性，同時保持本地存儲的可靠性！
