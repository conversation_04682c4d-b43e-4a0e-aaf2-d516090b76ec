# 時區資料更新指南

## 🎯 問題分析

您遇到的問題是 `combined.json` 時區資料不完整，導致檀香山、瑞士等地區查不到時區。

### 當前狀況
- **檔案大小**：1.46 MB
- **包含時區**：確實包含 `Pacific/Honolulu` 和 `Europe/Zurich`
- **問題原因**：可能是座標精度、邊界檢測或資料版本問題

## ✅ 解決方案

### 1. 立即修復 - 使用增強版時區服務

已創建 `EnhancedTimezoneService` 提供：
- **多層次查詢**：精確匹配 → 緩衝區搜尋 → 最近鄰居
- **錯誤處理**：完整的異常處理和日誌記錄
- **備用機制**：主要城市的備用時區對應表
- **效能優化**：資料快取和座標驗證

### 2. 長期解決 - 更新到最新時區資料

#### 下載最新完整版時區資料

```bash
# 下載 2025b 版本的完整時區資料（約 200MB）
wget https://github.com/evansiroky/timezone-boundary-builder/releases/download/2025b/timezones.geojson.zip

# 解壓縮
unzip timezones.geojson.zip

# 重命名為 combined.json
mv combined.json assets/combined.json
```

#### 可選的精簡版本

如果檔案太大，可以選擇精簡版：

```bash
# 下載 1970 年後相同時區的版本（較小）
wget https://github.com/evansiroky/timezone-boundary-builder/releases/download/2025b/timezones-1970.geojson.zip

# 或下載當前相同時區的版本（最小）
wget https://github.com/evansiroky/timezone-boundary-builder/releases/download/2025b/timezones-now.geojson.zip
```

## 🔧 實作步驟

### 步驟 1：整合增強版服務

```dart
// 在需要使用的地方
import '../utils/enhanced_timezone_service.dart';

final timezoneService = EnhancedTimezoneService();

// 查詢時區
String? timezone = await timezoneService.getTimeZoneFromLatLng(21.3099, -157.8581);
print('檀香山時區: $timezone'); // 應該輸出: Pacific/Honolulu
```

### 步驟 2：測試關鍵地點

```dart
// 測試檀香山
final honoluluTz = await timezoneService.getTimeZoneFromLatLng(21.3099, -157.8581);
assert(honoluluTz == 'Pacific/Honolulu');

// 測試瑞士
final switzerlandTz = await timezoneService.getTimeZoneFromLatLng(47.3769, 8.5417);
assert(switzerlandTz == 'Europe/Zurich');

// 測試台北
final taipeiTz = await timezoneService.getTimeZoneFromLatLng(25.0330, 121.5654);
assert(taipeiTz == 'Asia/Taipei');
```

### 步驟 3：更新現有程式碼

將現有的 `TimezoneService` 替換為 `EnhancedTimezoneService`：

```dart
// 舊的
// final timezone = await TimezoneService().getTimeZoneFromLatLng(lat, lng);

// 新的
final timezone = await EnhancedTimezoneService().getTimeZoneFromLatLng(lat, lng);
```

## 📊 資料版本比較

| 版本 | 檔案大小 | 時區數量 | 特點 |
|------|----------|----------|------|
| 當前 combined.json | 1.46 MB | ~400 | 基本版本，可能有遺漏 |
| timezones.geojson | ~200 MB | ~600 | 完整版本，包含所有時區 |
| timezones-1970.geojson | ~150 MB | ~400 | 1970年後相同的時區 |
| timezones-now.geojson | ~100 MB | ~200 | 當前相同的時區 |

## 🚀 效能優化建議

### 1. 資料壓縮
```bash
# 使用 gzip 壓縮減少檔案大小
gzip -9 assets/combined.json
# 在程式中使用 GZipCodec 解壓縮
```

### 2. 分區載入
```dart
// 只載入需要的地區資料
Future<List<Map<String, dynamic>>> loadRegionalTimezones(String region) async {
  // 實作分區載入邏輯
}
```

### 3. 索引建立
```dart
// 建立空間索引加速查詢
class SpatialIndex {
  Map<String, List<Map<String, dynamic>>> _regionIndex = {};
  
  void buildIndex(List<Map<String, dynamic>> features) {
    // 實作空間索引
  }
}
```

## 🧪 測試驗證

### 單元測試
```dart
test('檀香山時區查詢', () async {
  final service = EnhancedTimezoneService();
  final timezone = await service.getTimeZoneFromLatLng(21.3099, -157.8581);
  expect(timezone, equals('Pacific/Honolulu'));
});

test('瑞士時區查詢', () async {
  final service = EnhancedTimezoneService();
  final timezone = await service.getTimeZoneFromLatLng(47.3769, 8.5417);
  expect(timezone, equals('Europe/Zurich'));
});
```

### 整合測試
```dart
test('名人出生地時區查詢', () async {
  final service = EnhancedTimezoneService();
  
  // 測試名人範例中的所有出生地
  final celebrities = CelebrityExamplesService().getAllExamples();
  
  for (final celebrity in celebrities) {
    final timezone = await service.getTimeZoneFromLatLng(
      celebrity.birthData.latitude,
      celebrity.birthData.longitude,
    );
    
    expect(timezone, isNotNull, 
      reason: '${celebrity.name} 的出生地時區查詢失敗');
  }
});
```

## 📝 維護建議

### 1. 定期更新
- 每年檢查 timezone-boundary-builder 的新版本
- 關注時區規則變更（如夏令時間調整）

### 2. 監控日誌
```dart
// 監控時區查詢失敗的情況
logger.w('時區查詢失敗: lat=$lat, lng=$lng, location=$locationName');
```

### 3. 備用方案
```dart
// 當主要查詢失敗時使用備用方案
String? timezone = await primaryService.getTimeZone(lat, lng);
timezone ??= await fallbackService.getTimeZone(lat, lng);
timezone ??= getDefaultTimezone(lat, lng);
```

## 🔍 故障排除

### 常見問題

1. **記憶體不足**
   - 使用分區載入
   - 實作資料串流處理

2. **查詢速度慢**
   - 建立空間索引
   - 使用快取機制

3. **邊界精度問題**
   - 使用緩衝區搜尋
   - 實作最近鄰居查詢

### 調試工具
```dart
// 啟用詳細日誌
Logger.level = Level.debug;

// 測試特定座標
await timezoneService.getTimeZoneFromLatLng(lat, lng);
// 檢查日誌輸出
```

## 📚 參考資料

- [timezone-boundary-builder GitHub](https://github.com/evansiroky/timezone-boundary-builder)
- [IANA 時區資料庫](https://www.iana.org/time-zones)
- [OpenStreetMap 時區標記](https://wiki.openstreetmap.org/wiki/Key:timezone)
- [時區查詢函式庫列表](https://github.com/evansiroky/timezone-boundary-builder#lookup-libraries)
