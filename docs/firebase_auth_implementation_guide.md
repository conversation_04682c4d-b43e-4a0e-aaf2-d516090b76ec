# Firebase 認證實作指南

## 🎯 使用方式

### 1. 基本認證操作

#### 初始化服務
```dart
// 在 main.dart 中初始化
await FirebaseAuthService.initialize();
```

#### 電子郵件登入
```dart
final user = await FirebaseAuthService.signInWithEmailAndPassword(
  email: '<EMAIL>',
  password: 'password123',
);
```

#### Google 登入
```dart
final user = await FirebaseAuthService.signInWithGoogle();
if (user == null) {
  // 用戶取消登入
} else {
  // 登入成功
}
```

#### Apple 登入
```dart
final user = await FirebaseAuthService.signInWithApple();
if (user == null) {
  // 用戶取消或不支援
} else {
  // 登入成功
}
```

#### 匿名登入
```dart
final user = await FirebaseAuthService.signInAnonymously();
```

### 2. 在設定頁面使用

#### 快速登入
```dart
// 在設定頁面的快速登入區塊
void _handleQuickLogin(String type, AuthViewModel authViewModel) async {
  try {
    bool? success;
    switch (type) {
      case 'google':
        success = await authViewModel.signInWithGoogle();
        break;
      case 'anonymous':
        success = await authViewModel.signInAnonymously();
        break;
    }
    
    // 顯示結果
    if (success == true) {
      _showSuccessMessage('登入成功');
    } else if (success == false) {
      _showErrorMessage('登入失敗');
    }
    // success == null 表示用戶取消
  } catch (e) {
    _showErrorMessage('登入失敗：$e');
  }
}
```

### 3. 錯誤處理

#### 捕獲和處理異常
```dart
try {
  final user = await FirebaseAuthService.signInWithGoogle();
  // 處理成功情況
} catch (e) {
  // 錯誤已經被轉換為友善訊息
  showDialog(
    context: context,
    builder: (context) => AlertDialog(
      title: Text('登入失敗'),
      content: Text(e.toString()),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: Text('確定'),
        ),
      ],
    ),
  );
}
```

## 🔧 調試工具使用

### 1. 診斷報告

#### 生成診斷報告
```dart
import 'package:your_app/utils/firebase_auth_debug.dart';

// 生成完整診斷報告
final report = await FirebaseAuthDebug.generateDiagnosticReport();

// 打印到控制台
FirebaseAuthDebug.printDiagnosticReport(report);

// 複製到剪貼板
final jsonString = JsonEncoder.withIndent('  ').convert(report);
await Clipboard.setData(ClipboardData(text: jsonString));
```

### 2. 安全測試

#### Google 登入測試
```dart
final result = await FirebaseAuthDebug.safeGoogleSignInTest();
print('Google 登入測試結果: ${result['result']}');

if (result['result'] == 'success') {
  print('用戶: ${result['user']}');
  print('認證: ${result['authentication']}');
} else if (result['result'] == 'error') {
  print('錯誤: ${result['error']}');
}
```

#### Apple 登入測試
```dart
final result = await FirebaseAuthDebug.safeAppleSignInTest();
print('Apple 登入測試結果: ${result['result']}');

if (result['result'] == 'success') {
  print('憑證: ${result['credential']}');
} else if (result['result'] == 'unsupported') {
  print('平台不支援 Apple 登入');
}
```

### 3. 調試頁面

#### 導航到調試頁面
```dart
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => const FirebaseAuthDebugPage(),
  ),
);
```

## 🚨 常見問題解決

### 1. Google 登入問題

#### 問題：編譯器崩潰
**解決方案**：
- 確保使用最新版本的 `google_sign_in` 套件
- 檢查 Android/iOS 配置是否正確
- 使用調試工具檢查環境

#### 問題：認證信息不完整
**解決方案**：
```dart
// 檢查認證信息
final auth = await googleUser.authentication;
if (auth.idToken == null || auth.accessToken == null) {
  throw Exception('Google 認證信息不完整');
}
```

### 2. Apple 登入問題

#### 問題：錯誤 1000
**解決方案**：
- 檢查網路連接
- 確認 Apple Developer 配置
- 檢查設備是否支援 Apple 登入

#### 問題：平台不支援
**解決方案**：
```dart
// 平台檢查
if (!kIsWeb && (Platform.isIOS || Platform.isMacOS)) {
  // 顯示 Apple 登入選項
} else {
  // 隱藏 Apple 登入選項
}
```

### 3. Token 問題

#### 問題：Token 過期
**解決方案**：
- 系統會自動嘗試刷新 token
- 如果刷新失敗，會自動登出
- 網路問題不會強制登出

#### 問題：Token 驗證失敗
**解決方案**：
```dart
// 手動驗證 token
final user = FirebaseAuthService.getCurrentUser();
if (user != null) {
  // 用戶已登入，token 有效
} else {
  // 需要重新登入
}
```

## 📱 平台特定配置

### 1. Android 配置

#### google-services.json
```
android/app/google-services.json
```

#### build.gradle
```gradle
dependencies {
    implementation 'com.google.android.gms:play-services-auth:20.7.0'
}
```

### 2. iOS 配置

#### GoogleService-Info.plist
```
ios/Runner/GoogleService-Info.plist
```

#### Info.plist
```xml
<key>CFBundleURLTypes</key>
<array>
    <dict>
        <key>CFBundleURLName</key>
        <string>REVERSED_CLIENT_ID</string>
        <key>CFBundleURLSchemes</key>
        <array>
            <string>YOUR_REVERSED_CLIENT_ID</string>
        </array>
    </dict>
</array>
```

### 3. Web 配置

#### index.html
```html
<script src="https://apis.google.com/js/platform.js" async defer></script>
<meta name="google-signin-client_id" content="YOUR_CLIENT_ID">
```

## 🔒 安全最佳實踐

### 1. Token 管理

#### 安全存儲
```dart
// 使用 SharedPreferences 安全存儲
final prefs = await SharedPreferences.getInstance();
await prefs.setString('firebase_token', idToken);
```

#### 自動刷新
```dart
// 系統會自動處理 token 刷新
// 無需手動管理
```

### 2. 錯誤處理

#### 不暴露敏感信息
```dart
// 好的做法
throw Exception('登入失敗，請稍後再試');

// 避免的做法
throw Exception('API Key invalid: $apiKey');
```

#### 用戶友善訊息
```dart
// 將技術錯誤轉換為用戶友善訊息
String _getFirebaseErrorMessage(String errorCode) {
  switch (errorCode) {
    case 'EMAIL_EXISTS':
      return '此電子郵件已被註冊';
    case 'INVALID_PASSWORD':
      return '密碼錯誤';
    default:
      return '認證失敗，請稍後再試';
  }
}
```

### 3. 狀態管理

#### 防止重複操作
```dart
bool _isLoading = false;

Future<void> _handleLogin() async {
  if (_isLoading) return;
  
  setState(() {
    _isLoading = true;
  });
  
  try {
    // 執行登入
  } finally {
    setState(() {
      _isLoading = false;
    });
  }
}
```

## 📊 監控和分析

### 1. 日誌記錄

#### 重要事件記錄
```dart
logger.i('用戶開始登入: $email');
logger.i('登入成功: ${user.uid}');
logger.w('登入失敗: $error');
```

#### 性能監控
```dart
final stopwatch = Stopwatch()..start();
final user = await FirebaseAuthService.signInWithGoogle();
stopwatch.stop();
logger.i('Google 登入耗時: ${stopwatch.elapsedMilliseconds}ms');
```

### 2. 錯誤追蹤

#### 錯誤分類
```dart
// 按錯誤類型分類
if (e is PlatformException) {
  logger.e('平台錯誤: ${e.code} - ${e.message}');
} else if (e is SignInWithAppleAuthorizationException) {
  logger.e('Apple 認證錯誤: ${e.code} - ${e.message}');
} else {
  logger.e('未知錯誤: $e');
}
```

## 📝 總結

### 使用要點
1. **初始化**：應用啟動時調用 `initialize()`
2. **錯誤處理**：使用 try-catch 捕獲異常
3. **用戶體驗**：處理用戶取消操作（返回 null）
4. **調試**：使用調試工具診斷問題
5. **平台適配**：根據平台顯示適當的登入選項

### 最佳實踐
1. **防禦性編程**：完善的異常處理
2. **用戶友善**：友善的錯誤訊息
3. **性能優化**：非阻塞的初始化
4. **安全性**：安全的 token 管理
5. **可維護性**：詳細的日誌記錄

這個 Firebase 認證系統為 Astreal 應用提供了穩定、安全、用戶友善的認證體驗，同時為開發團隊提供了強大的調試和監控工具。
