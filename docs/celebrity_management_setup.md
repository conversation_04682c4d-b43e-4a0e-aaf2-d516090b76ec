# 名人資料管理系統設置指南

## 🚀 快速開始

### 1. 確認管理員權限
```dart
// 在 AdminService 中添加您的 UID
static const List<String> _adminUids = [
  'your_admin_uid_here',
  // 添加其他管理員 UID
];
```

### 2. 訪問管理系統
1. 使用管理員帳號登入應用
2. 進入「設定」頁面
3. 在「管理者選項」中點擊「管理後台」
4. 點擊「名人資料管理」

### 3. 初始化資料
有三種方式初始化名人資料：

#### 方式一：從 RemoteConfig 導入
```
管理頁面 → 右上角選單 → 從 RemoteConfig 導入
```

#### 方式二：手動新增
```
管理頁面 → 右上角 + 按鈕 → 填寫表單 → 儲存
```

#### 方式三：程式化初始化
```dart
// 在開發環境中執行一次
await _initializeDefaultCelebrities();
```

## 📋 操作流程

### 新增名人資料
1. **基本資訊**
   - 姓名（必填）
   - 特點描述（必填）

2. **出生資料**
   - 點擊「設定」按鈕
   - 填寫完整的出生資訊
   - 包含日期、時間、地點、經緯度

3. **分類設定**
   - 選擇適當的類別
   - 設定是否為熱門

4. **推薦主題**
   - 輸入解讀主題，用逗號分隔
   - 例如：領導力分析, 政治天賦, 公眾魅力

5. **圖片設定**（選填）
   - 輸入圖片 URL
   - 系統會顯示預覽

### 編輯現有資料
1. 在列表中找到要編輯的名人
2. 點擊右側的選單按鈕（⋮）
3. 選擇「編輯」
4. 修改相關資訊
5. 點擊「儲存」

### 刪除資料
1. 在列表中找到要刪除的名人
2. 點擊右側的選單按鈕（⋮）
3. 選擇「刪除」
4. 確認刪除操作

## 🔄 同步操作

### 同步到 RemoteConfig
```
目的：將 Firestore 中的資料同步到 RemoteConfig
步驟：管理頁面 → 右上角選單 → 同步到 RemoteConfig
結果：資料準備完成，等待技術人員最終同步
```

### 從 RemoteConfig 導入
```
目的：將 RemoteConfig 中的資料導入到管理系統
步驟：管理頁面 → 右上角選單 → 從 RemoteConfig 導入
結果：新資料自動導入，重複資料跳過
```

## 🔍 搜尋和篩選

### 文字搜尋
- 在搜尋欄輸入關鍵字
- 支援姓名和描述搜尋
- 即時顯示結果

### 類別篩選
- 點擊類別晶片進行篩選
- 支援單一類別篩選
- 點擊「全部」清除篩選

### 統計資訊
- **總計**：所有名人資料數量
- **顯示**：當前篩選結果數量
- **熱門**：標記為熱門的數量

## 📊 資料格式

### 出生資料格式
```json
{
  "id": "celebrity_barack_obama",
  "name": "巴拉克・歐巴馬",
  "dateTime": "1961-08-04T19:24:00.000Z",
  "birthPlace": "檀香山，夏威夷",
  "latitude": 21.3099,
  "longitude": -157.8581,
  "notes": "第44任美國總統，有出生證書公開"
}
```

### 推薦主題格式
```
領導力分析, 政治天賦, 公眾魅力, 演說能力
```

### 類別選項
- 政治人物 (politician)
- 娛樂明星 (entertainment)
- 學者專家 (scholar)
- 商業領袖 (business)
- 藝術家 (artist)
- 運動員 (athlete)
- 作家 (writer)
- 科學家 (scientist)
- 其他 (other)

## ⚠️ 注意事項

### 資料驗證
- 姓名和描述為必填欄位
- 出生資料必須完整設定
- 圖片 URL 必須是有效的網址格式

### 權限限制
- 只有管理員可以訪問此功能
- 所有操作都會記錄日誌
- 刪除操作無法復原

### 同步限制
- 客戶端無法直接更新 RemoteConfig
- 需要技術人員協助完成最終同步
- 建議定期備份重要資料

## 🛠️ 故障排除

### 常見問題

#### 1. 無法訪問管理功能
**原因**：沒有管理員權限
**解決**：確認 UID 已添加到 AdminService._adminUids 列表

#### 2. 出生資料設定失敗
**原因**：資料格式不正確
**解決**：確保所有必填欄位都已填寫

#### 3. 同步失敗
**原因**：網路連接問題或權限不足
**解決**：檢查網路連接，確認管理員權限

#### 4. 圖片無法顯示
**原因**：URL 無效或圖片不存在
**解決**：檢查 URL 格式，確認圖片可訪問

### 錯誤代碼
- `permission-denied`：權限不足
- `invalid-argument`：參數格式錯誤
- `not-found`：資料不存在
- `network-error`：網路連接問題

## 📞 技術支援

### 開發者聯繫
如需技術支援或功能建議，請聯繫開發團隊。

### 日誌查看
管理員可以在「設定」→「日誌管理」中查看詳細的操作日誌。

### 備份建議
建議定期匯出名人資料作為備份，避免資料遺失。

---

**按照這個指南，您可以輕鬆設置和使用名人資料管理系統！** 🎉
