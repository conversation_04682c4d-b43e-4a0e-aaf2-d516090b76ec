# Starmaster 專業占星師首頁設計文件

## 概述

`StarmasterHomePage` 是專為專業占星師設計的首頁，提供更專業、更高效的工作流程和工具。相較於 `StarlightHomePage` 的初心者友好設計，Starmaster 版本更注重專業功能和工作效率。

## 設計理念

### 1. 專業導向
- 以占星師的工作流程為核心設計
- 提供快速訪問常用專業工具
- 強調客戶管理和星盤分析功能

### 2. 效率優先
- 減少不必要的裝飾元素
- 直接的功能入口
- 快速的星盤創建流程

### 3. 視覺專業感
- 使用深色漸變背景（皇家靛藍 + 宇宙紫）
- 簡潔的卡片設計
- 專業的色彩搭配

## 主要功能區域

### 1. 專業標題區域 (`_buildProfessionalHeader`)
```dart
// 特色
- "Professional Starmaster ⭐" 品牌標識
- 深色漸變背景（royalIndigo → cosmicPurple）
- 專業標語："專業占星師工作台，精準解讀星象奧秘"
- 設置按鈕快速入口
```

### 2. 客戶管理區域 (`_buildClientManagementSection`)
```dart
// 功能
- 當前選中客戶顯示
- 客戶選擇功能
- 新增客戶快速按鈕
- 客戶基本信息展示（姓名、出生地）
```

### 3. 專業工具區域 (`_buildProfessionalToolsSection`)
```dart
// 四大核心工具
1. 本命盤分析 (Natal Chart) - 藍色
2. 推運分析 (Transit Analysis) - 綠色  
3. 合盤分析 (Synastry) - 粉色
4. 返照盤 (Solar Return) - 橙色

// 特點
- 2x2 網格布局
- 固定高度 100px
- 英文副標題增加專業感
- 直接創建對應星盤類型
```

### 4. 星盤類型區域 (`_buildChartTypesSection`)
```dart
// 專業星盤類型標籤
- 預測類 (Transit)
- 合盤類 (Synastry) 
- 返照盤 (Solar Return)
- 事件占星 (Horary)
- 世俗占星 (Mundane)

// 特點
- 水平滾動設計
- 彩色標籤設計
- 查看全部功能
- 直接進入星盤選擇頁面
```

### 5. 高級功能區域 (`_buildAdvancedFeaturesSection`)
```dart
// 深入剖析功能
- 漸變背景卡片
- PRO 標籤突出專業性
- 功能說明和價值主張
- 為未來 AI 功能預留入口
```

## 與 Starlight 版本的差異

### 設計差異
| 特性 | Starlight (初心者) | Starmaster (專業) |
|------|-------------------|-------------------|
| 主色調 | 太陽琥珀色 (溫暖) | 皇家靛藍 (專業) |
| 背景漸變 | 單色漸變 | 雙色漸變 |
| 標語 | "開始您的占星之旅" | "專業占星師工作台" |
| 圖示風格 | 友好圓潤 | 簡潔專業 |

### 功能差異
| 功能 | Starlight | Starmaster |
|------|-----------|------------|
| 學習進度 | ✅ 顯示 | ❌ 隱藏 |
| 今日運勢 | ✅ 顯示 | ❌ 隱藏 |
| 客戶管理 | 簡單選擇 | 專業管理 |
| 星盤工具 | 基礎分析 | 專業工具 |
| AI 功能 | 無 | 高級功能 |

### 用戶體驗差異
- **Starlight**: 引導式學習，循序漸進
- **Starmaster**: 直接高效，專業導向

## 技術實作細節

### 1. 色彩系統
```dart
// 主要色彩
- AppColors.royalIndigo (主色)
- AppColors.cosmicPurple (輔助色)
- AppColors.textDark (文字)

// 功能色彩
- 本命盤: Color(0xFF6366F1) 藍色
- 推運: Color(0xFF10B981) 綠色
- 合盤: Color(0xFFEC4899) 粉色
- 返照: Color(0xFFF59E0B) 橙色
```

### 2. 布局系統
```dart
// 統一的卡片設計
decoration: BoxDecoration(
  color: Colors.white,
  borderRadius: BorderRadius.circular(12),
  boxShadow: [
    BoxShadow(
      color: Colors.black.withValues(alpha: 0.05),
      blurRadius: 4,
      offset: const Offset(0, 2),
    ),
  ],
),
```

### 3. 導航邏輯
```dart
// 直接創建星盤
_createChart(viewModel, chartType)

// 客戶管理
_showBirthDataSelection(viewModel)

// 星盤類型選擇
Navigator.push → ChartSelectionPage
```

## 響應式設計

### 1. 網格布局
- 專業工具：2x2 固定網格
- 客戶管理：彈性行布局
- 星盤類型：水平滾動

### 2. 間距系統
- 區域間距：24px
- 卡片間距：8px
- 內容間距：12px

### 3. 字體層次
- 標題：18px, bold
- 副標題：14px, bold  
- 說明文字：12px, regular
- 標籤文字：12px, medium

## 擴展性設計

### 1. 模組化結構
每個功能區域都是獨立的 Widget，便於：
- 功能調整
- 布局重組
- 新功能添加

### 2. 配置化設計
- 工具列表可配置
- 星盤類型可擴展
- 色彩主題可切換

### 3. 未來功能預留
- AI 功能區域
- 更多專業工具
- 客戶管理擴展

## 使用場景

### 1. 日常工作流程
```
1. 選擇/新增客戶
2. 選擇分析工具
3. 創建星盤
4. 進行解讀
```

### 2. 快速分析
```
1. 點擊專業工具
2. 直接創建對應星盤
3. 開始分析
```

### 3. 探索功能
```
1. 瀏覽星盤類型
2. 查看全部選項
3. 選擇合適工具
```

## 性能優化

### 1. 懶加載
- 客戶列表按需載入
- 星盤類型動態生成

### 2. 狀態管理
- 使用 Provider 管理狀態
- 客戶選擇狀態持久化

### 3. 導航優化
- 預載入常用頁面
- 快速頁面切換

## 維護指南

### 1. 新增專業工具
在 `_buildProfessionalToolsSection` 的 tools 列表中添加新項目。

### 2. 調整星盤類型
修改 `_buildChartTypesSection` 中的標籤列表。

### 3. 更新色彩主題
在 AppColors 中定義新顏色，然後更新對應的使用位置。

### 4. 添加新功能區域
創建新的 `_build...Section` 方法，並在主 ListView 中添加。
