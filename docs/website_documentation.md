# AstReal 官方網站文件

## 概述
本文件記錄了AstReal 官方網站的結構、功能和部署方式。

## 網站結構

### 主要頁面
1. **landing.html** - 官方網站首頁
2. **download.html** - 下載頁面
3. **home.html** - 重定向頁面
4. **privacy-policy.html** - 隱私權政策
5. **terms-of-service.html** - 服務條款

### 頁面功能

#### 1. 官方網站首頁 (landing.html)
**功能特色**：
- 響應式設計，支援各種螢幕尺寸
- 現代化的視覺設計和動畫效果
- 完整的應用程式介紹
- 功能特色展示
- 各平台下載連結
- 平滑滾動和視差效果

**主要區塊**：
- **Header 區域**：應用程式標題、副標題和主要 CTA 按鈕
- **Features 區域**：六大核心功能介紹
- **Download 區域**：四個平台的下載選項
- **Footer 區域**：詳細的網站導航和法律資訊

#### 2. 下載頁面 (download.html)
**功能特色**：
- 詳細的平台資訊和系統需求
- 各平台專屬功能介紹
- 下載狀態指示（可用/即將推出）
- QR Code 支援（Web 版本）

**平台支援**：
- **iOS**：App Store 下載（即將推出）
- **Android**：Google Play 下載（即將推出）
- **macOS**：直接下載（即將推出）
- **Web**：即時使用（已可用）

#### 3. 重定向頁面 (home.html)
**功能**：
- 自動重定向到官方網站首頁
- 載入動畫和備用連結
- 適用於域名根目錄訪問

## 技術特點

### 1. 響應式設計
- 使用 CSS Grid 和 Flexbox 佈局
- 支援桌面、平板和手機設備
- 斷點設計：768px 以下為手機版

### 2. 視覺效果
- CSS 動畫和過渡效果
- 懸停效果和互動反饋
- 漸層背景和陰影效果
- 平滑滾動和視差動畫

### 3. 用戶體驗
- 快速載入和優化性能
- 直觀的導航和操作
- 清晰的資訊架構
- 無障礙設計考量

### 4. SEO 優化
- 完整的 meta 標籤
- Open Graph 和 Twitter Card 支援
- 語義化 HTML 結構
- 適當的標題層級

## 下載連結配置

### 當前狀態
```
iOS App Store: https://apps.apple.com/app/astreal/id123456789 (即將推出)
Google Play: https://play.google.com/store/apps/details?id=com.one.astreal (即將推出)
macOS 下載: https://astreal.app/download/macos (即將推出)
Web 版本: https://astreal-d3f70.web.app/ (已可用)
```

### 更新下載連結
當應用程式在各平台上架後，需要更新以下文件中的連結：
1. `web/landing.html` - 首頁下載區域
2. `web/download.html` - 詳細下載頁面
3. 移除 `coming-soon` 類別，改為 `available` 類別

## 部署方式

### Firebase Hosting 部署
```bash
# 部署到 Firebase Hosting
firebase deploy --only hosting

# 部署特定文件
firebase deploy --only hosting:astreal-d3f70
```

### 自定義域名設置
1. 在 Firebase Console 中設置自定義域名
2. 配置 DNS 記錄指向 Firebase
3. 啟用 SSL 證書

### 文件結構
```
web/
├── landing.html          # 官方網站首頁
├── download.html         # 下載頁面
├── home.html            # 重定向頁面
├── privacy-policy.html  # 隱私權政策
├── terms-of-service.html # 服務條款
├── index.html           # Flutter Web 應用入口
├── manifest.json        # Web App Manifest
└── favicon.png          # 網站圖標
```

## 內容管理

### 更新應用資訊
當應用程式有新版本時，需要更新：
1. 版本號碼
2. 功能特色描述
3. 系統需求
4. 下載連結狀態

### 多語言支援
目前支援繁體中文，未來可擴展：
- 簡體中文
- 英文
- 日文

## 分析和監控

### Google Analytics
建議添加 Google Analytics 追蹤碼：
```html
<!-- Google Analytics -->
<script async src="https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'GA_MEASUREMENT_ID');
</script>
```

### 關鍵指標
- 頁面瀏覽量
- 下載按鈕點擊率
- 用戶停留時間
- 跳出率

## 維護和更新

### 定期檢查
1. 下載連結有效性
2. 應用程式資訊準確性
3. 網站性能和載入速度
4. 跨瀏覽器兼容性

### 內容更新流程
1. 修改相應的 HTML 文件
2. 測試本地預覽
3. 部署到 Firebase Hosting
4. 驗證線上版本

## 安全考量

### HTTPS 強制
- 所有頁面強制使用 HTTPS
- 設置安全標頭
- 定期更新依賴項

### 隱私保護
- 遵循 GDPR 和相關法規
- 明確的隱私權政策
- 用戶數據保護措施

## 未來改進

### 功能擴展
1. 用戶註冊和登入系統
2. 線上試用功能
3. 客戶支援聊天機器人
4. 多語言切換
5. 暗色主題支援

### 技術優化
1. PWA 支援
2. 離線功能
3. 推送通知
4. 更好的 SEO 優化
5. 性能監控

## 總結
AstReal 官方網站提供了完整的應用程式介紹和下載功能，採用現代化的設計和技術，為用戶提供優質的瀏覽體驗。隨著應用程式的發展，網站將持續更新和優化，以滿足用戶需求和市場變化。
