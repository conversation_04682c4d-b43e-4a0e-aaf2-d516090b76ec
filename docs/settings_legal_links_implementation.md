# 設定頁面法律條款連結實現文檔

## 概述
在設定頁面中添加了隱私權政策和服務條款的連結，用戶可以直接點擊查看相關法律文件。

## 實現功能

### 1. 新增法律條款區塊
在設定頁面中添加了專門的法律條款區塊，包含：
- **隱私權政策**：連結到 `https://astreal-d3f70.web.app/privacy-policy.html`
- **服務條款**：連結到 `https://astreal-d3f70.web.app/terms-of-service.html`

### 2. 主要修改

#### 2.1 添加依賴項
- 使用現有的 `url_launcher: ^6.2.4` 套件來開啟外部連結

#### 2.2 UI 組件設計
- **法律條款區塊**：使用 `StyledCard` 保持與應用設計一致
- **項目設計**：每個法律條款項目包含圖標、標題、說明和外部連結圖標
- **互動效果**：使用 `InkWell` 提供點擊回饋效果

#### 2.3 程式碼結構

##### 新增方法
```dart
/// 構建法律條款區塊
Widget _buildLegalSection()

/// 構建法律條款項目
Widget _buildLegalItem({
  required String title,
  required String subtitle,
  required IconData icon,
  required VoidCallback onTap,
})

/// 啟動URL
Future<void> _launchURL(String url)
```

##### URL 啟動功能
- 使用 `canLaunchUrl()` 檢查是否可以開啟連結
- 使用 `LaunchMode.externalApplication` 在外部瀏覽器中開啟
- 包含完整的錯誤處理和用戶提示

### 3. 技術實現細節

#### 3.1 法律條款區塊設計
```dart
Widget _buildLegalSection() {
  return StyledCard(
    child: Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 區塊標題
          const Row(
            children: [
              Icon(Icons.gavel, color: AppColors.textDark, size: 20),
              SizedBox(width: 8),
              Text('法律條款', style: TextStyle(...)),
            ],
          ),
          const SizedBox(height: 16),
          
          // 隱私權政策
          _buildLegalItem(...),
          
          // 服務條款
          _buildLegalItem(...),
        ],
      ),
    ),
  );
}
```

#### 3.2 法律條款項目設計
```dart
Widget _buildLegalItem({...}) {
  return InkWell(
    onTap: onTap,
    borderRadius: BorderRadius.circular(8),
    child: Padding(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
      child: Row(
        children: [
          // 圖標容器
          Container(
            width: 36,
            height: 36,
            decoration: BoxDecoration(
              color: AppColors.royalIndigo.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: AppColors.royalIndigo, size: 18),
          ),
          const SizedBox(width: 12),
          
          // 文字內容
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(title, style: TextStyle(...)),
                Text(subtitle, style: TextStyle(...)),
              ],
            ),
          ),
          
          // 外部連結圖標
          Icon(Icons.open_in_new, color: AppColors.royalIndigo, size: 16),
        ],
      ),
    ),
  );
}
```

#### 3.3 URL 啟動功能
```dart
Future<void> _launchURL(String url) async {
  try {
    final Uri uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    } else {
      // 顯示錯誤訊息
      ScaffoldMessenger.of(context).showSnackBar(...);
    }
  } catch (e) {
    // 異常處理
    ScaffoldMessenger.of(context).showSnackBar(...);
  }
}
```

### 4. 用戶體驗設計

#### 4.1 視覺設計
- **一致性**：使用與應用其他設定項目相同的設計語言
- **清晰度**：明確的圖標和文字說明，讓用戶了解每個連結的用途
- **回饋**：點擊時提供視覺回饋，外部連結圖標表明會開啟新頁面

#### 4.2 互動設計
- **易用性**：大的點擊區域，方便用戶操作
- **安全性**：在外部瀏覽器中開啟，保護應用安全
- **錯誤處理**：當連結無法開啟時提供友善的錯誤訊息

### 5. 部署配置

#### 5.1 連結配置
- **隱私權政策**：`https://astreal-d3f70.web.app/privacy-policy.html`
- **服務條款**：`https://astreal-d3f70.web.app/terms-of-service.html`

#### 5.2 Firebase Hosting
- 法律條款頁面已部署到 Firebase Hosting
- 使用 HTTPS 確保安全性
- 響應式設計支援各種設備

### 6. 法律合規性

#### 6.1 透明度
- 用戶可以輕鬆訪問隱私權政策和服務條款
- 符合 GDPR 和其他隱私法規要求
- 提供清楚的法律條款說明

#### 6.2 可訪問性
- 法律條款頁面使用清晰的排版和適當的字體大小
- 支援螢幕閱讀器和其他輔助技術
- 在各種設備上都能正常顯示

### 7. 測試驗證

#### 7.1 功能測試
- ✅ 法律條款區塊正確顯示
- ✅ 隱私權政策連結正常工作
- ✅ 服務條款連結正常工作
- ✅ 錯誤處理機制正常

#### 7.2 用戶體驗測試
- ✅ 點擊回饋效果正常
- ✅ 外部瀏覽器正確開啟
- ✅ 錯誤訊息友善易懂
- ✅ 視覺設計與應用一致

### 8. 維護和更新

#### 8.1 連結維護
- 定期檢查連結是否正常工作
- 法律條款更新時同步更新連結
- 監控用戶回饋和問題報告

#### 8.2 內容更新
- 法律條款內容變更時及時更新 HTML 文件
- 重新部署到 Firebase Hosting
- 通知用戶重要的條款變更

## 使用方式

1. **訪問設定頁面**：在應用中進入設定頁面
2. **找到法律條款區塊**：在頁面中找到「法律條款」區塊
3. **點擊相應連結**：
   - 點擊「隱私權政策」查看隱私權相關條款
   - 點擊「服務條款」查看使用條款和規定
4. **外部瀏覽器開啟**：連結會在外部瀏覽器中開啟，方便閱讀

## 後續優化建議

1. **多語言支援**：為法律條款頁面添加英文版本
2. **離線支援**：考慮在應用內提供法律條款的離線版本
3. **版本追蹤**：添加法律條款版本號和更新日期顯示
4. **用戶確認**：在重要更新時要求用戶重新確認條款

這個實現為用戶提供了便捷的法律條款訪問方式，確保了應用的法律合規性和用戶權益保護。
