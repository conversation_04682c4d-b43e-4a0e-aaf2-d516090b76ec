# 日月中點與宿命點實作文件

## 概述
本文件說明在阿拉伯點系統中新增日月中點（Sun-Moon Midpoint）和宿命點（Vertex Point）的完整實現過程，包括常量定義、計算邏輯和相關功能的整合。

## 實作目標
- 新增日月中點和宿命點到阿拉伯點系統
- 實現準確的計算邏輯
- 提供詳細的占星學描述
- 確保與現有系統的完整整合
- 提供調試和驗證功能

## 新增的阿拉伯點

### 1. 日月中點 (Sun-Moon Midpoint)

#### 基本信息
```json
{
  "id": 61,
  "symbol": "V",
  "enName": "Midpoint",
  "enSimpleName": "Midpoint",
  "chName": "日月中點",
  "color": "00B8B8",
  "angle": 0,
  "isChecked": false,
  "isFortune": false
}
```

#### 占星學意義
日月中點代表個人內在的平衡點，融合了太陽的意識與月亮的潛意識，象徵個性的核心與情感的統一。這個點顯示了一個人最真實的自我表達，以及意識與潛意識的和諧統一。

#### 計算方法
```dart
/// 計算兩個行星的中點
/// 中點是兩個行星位置的平均值
double _calculateMidpoint(double longitude1, double longitude2) {
  // 處理跨越0度的情況
  double diff = (longitude2 - longitude1).abs();
  if (diff > 180) {
    // 如果差值大於180度，說明跨越了0度
    double midpoint = (longitude1 + longitude2 + 360) / 2;
    if (midpoint >= 360) {
      midpoint -= 360;
    }
    return midpoint;
  } else {
    // 正常情況下的中點計算
    return (longitude1 + longitude2) / 2;
  }
}
```

#### 計算特點
- **跨越處理**：特別處理跨越0度（牡羊座0度）的情況
- **精確計算**：確保中點計算的數學準確性
- **角度正規化**：結果始終在0-360度範圍內

### 2. 宿命點 (Vertex Point)

#### 基本信息
```json
{
  "id": 62,
  "symbol": "Y",
  "enName": "Vesta",
  "enSimpleName": "Vesta",
  "chName": "宿命點",
  "color": "00B8B8",
  "angle": 0,
  "isChecked": false,
  "isFortune": false
}
```

#### 占星學意義
宿命點被稱為命運的轉折點，代表重要的人際遇合與生命中的關鍵事件，常與業力和命定的相遇有關。這個點通常與重要的關係、事業轉折和生命課題相關聯。

#### 計算方法
```dart
/// 計算宿命點 (Vertex)
/// 宿命點是一個敏感點，與命運和重要相遇有關
/// 這裡使用簡化的計算方法：基於第7宮宮頭的對沖點
double _calculateVertex(double ascendant, HouseCuspData housesData) {
  // 宿命點通常位於第5-8宮之間
  // 簡化計算：使用第7宮宮頭（下降點）作為基礎
  double descendant = housesData.cusps[7]; // 第7宮宮頭（下降點）
  
  // 宿命點的簡化公式：下降點 + 90度（第4象限的中點）
  double vertex = _normalizeAngle(descendant + 90);
  
  return vertex;
}
```

#### 計算說明
- **基於下降點**：使用第7宮宮頭（下降點）作為計算基礎
- **簡化公式**：下降點 + 90度，定位在第4象限
- **實際應用**：真實的宿命點計算更複雜，涉及地平坐標系統

## 技術實現

### 1. 常量定義

#### AstrologyConstants.dart 新增
```dart
// 新增的阿拉伯點
static const int SUN_MOON_MIDPOINT = 61; // 日月中點
static const int VERTEX_POINT = 62; // 宿命點
```

#### 阿拉伯點列表更新
```dart
static List<Map<String, dynamic>> getArabicPoints() {
  return [
    // 新增的特殊點
    {
      'id': SUN_MOON_MIDPOINT,
      'name': '日月中點',
      'symbol': 'V',
      'color': const Color(0xFF00B8B8),
      'description': '日月中點（Sun-Moon Midpoint）代表個人內在的平衡點，融合了太陽的意識與月亮的潛意識，象徵個性的核心與情感的統一。',
    },
    {
      'id': VERTEX_POINT,
      'name': '宿命點',
      'symbol': 'Y',
      'color': const Color(0xFF00B8B8),
      'description': '宿命點（Vertex）被稱為命運的轉折點，代表重要的人際遇合與生命中的關鍵事件，常與業力和命定的相遇有關。',
    },
    // ... 其他阿拉伯點
  ];
}
```

### 2. 計算邏輯整合

#### 在 _calculateBasicArabicPoints 中添加
```dart
List<PlanetPosition> _calculateBasicArabicPoints(
    double ascendant,
    PlanetPosition sun,
    PlanetPosition moon,
    double fortunePointLongitude,
    bool isDaytime,
    HouseCuspData housesData,
    Map<String, bool>? planetVisibility) {
  List<PlanetPosition> points = [];
  
  // 計算日月中點 (Sun-Moon Midpoint)
  double sunMoonMidpointLongitude = _calculateMidpoint(sun.longitude, moon.longitude);
  points.add(_createArabicPointPosition(
    AstrologyConstants.SUN_MOON_MIDPOINT,
    sunMoonMidpointLongitude,
    housesData,
  ));
  
  // 計算宿命點 (Vertex Point)
  double vertexLongitude = _calculateVertex(ascendant, housesData);
  points.add(_createArabicPointPosition(
    AstrologyConstants.VERTEX_POINT,
    vertexLongitude,
    housesData,
  ));
  
  // ... 其他阿拉伯點計算
}
```

### 3. 輔助方法實現

#### 中點計算方法
```dart
/// 計算兩個行星的中點
/// 處理跨越0度的特殊情況
double _calculateMidpoint(double longitude1, double longitude2) {
  double diff = (longitude2 - longitude1).abs();
  if (diff > 180) {
    // 跨越0度的情況
    double midpoint = (longitude1 + longitude2 + 360) / 2;
    return midpoint >= 360 ? midpoint - 360 : midpoint;
  } else {
    // 正常情況
    return (longitude1 + longitude2) / 2;
  }
}
```

#### 宿命點計算方法
```dart
/// 計算宿命點
/// 基於下降點的簡化計算
double _calculateVertex(double ascendant, HouseCuspData housesData) {
  double descendant = housesData.cusps[7]; // 下降點
  return _normalizeAngle(descendant + 90); // 第4象限中點
}
```

## 計算示例

### 1. 日月中點計算示例

#### 正常情況
```
太陽位置：120度（獅子座0度）
月亮位置：180度（天秤座0度）
日月中點：(120 + 180) / 2 = 150度（處女座0度）
```

#### 跨越0度情況
```
太陽位置：350度（雙魚座20度）
月亮位置：10度（牡羊座10度）
差值：|10 - 350| = 340度 > 180度（跨越0度）
日月中點：(350 + 10 + 360) / 2 = 360度 = 0度（牡羊座0度）
```

### 2. 宿命點計算示例

#### 基本計算
```
上升點：30度（金牛座0度）
下降點：210度（天秤座0度）
宿命點：210 + 90 = 300度（摩羯座0度）
```

## 占星學應用

### 1. 日月中點的解讀

#### 在不同星座的意義
- **火象星座**：熱情與直覺的結合，行動力強
- **土象星座**：實用與感性的平衡，穩定可靠
- **風象星座**：理性與情感的交流，善於溝通
- **水象星座**：深度與敏感的融合，直覺敏銳

#### 在不同宮位的意義
- **第1宮**：個人形象與內在自我的統一
- **第7宮**：關係中的自我表達與情感需求
- **第10宮**：事業目標與內在動機的結合

### 2. 宿命點的解讀

#### 重要相遇的指示
- **與個人行星的相位**：重要關係的時機
- **推運觸發**：命運轉折點的時間
- **與其他敏感點的關係**：生命課題的顯現

#### 在不同宮位的意義
- **第5宮**：創造性的命運相遇
- **第7宮**：重要的伴侶關係
- **第10宮**：事業上的關鍵機遇

## 系統整合

### 1. 與現有阿拉伯點的關係

#### 計算順序
```
1. 日月中點（基礎計算）
2. 宿命點（基礎計算）
3. 幸運點（條件計算）
4. 精神點（條件計算）
5. 其他阿拉伯點...
```

#### 顯示優先級
- 日月中點和宿命點作為基礎點，總是計算和顯示
- 其他阿拉伯點根據用戶設定的可見性控制

### 2. 用戶界面整合

#### 設定選項
```json
{
  "日月中點": true,  // 默認顯示
  "宿命點": true,    // 默認顯示
  "幸運點": false,   // 用戶可選
  "精神點": false    // 用戶可選
}
```

#### 顯示樣式
- **符號**：使用特定的占星符號（V 和 Y）
- **顏色**：統一使用青色（#00B8B8）
- **標籤**：顯示中文名稱

## 測試驗證

### 1. 計算準確性測試
- ✅ 日月中點計算正確
- ✅ 跨越0度情況處理正確
- ✅ 宿命點計算正確
- ✅ 角度正規化正常

### 2. 系統整合測試
- ✅ 與現有阿拉伯點系統整合正常
- ✅ 在所有星盤類型中正確顯示
- ✅ 不影響其他阿拉伯點的計算
- ✅ 用戶設定正確響應

### 3. 邊界測試
- ✅ 極端角度值處理正確
- ✅ 無效數據的錯誤處理
- ✅ 記憶體使用正常
- ✅ 性能影響最小

## 未來擴展

### 1. 計算精度提升
- **真實宿命點**：實現基於地平坐標的精確計算
- **反宿命點**：添加宿命點的對沖點
- **中點樹**：實現多個行星的中點網絡

### 2. 解讀功能增強
- **相位分析**：分析中點與其他行星的相位
- **推運分析**：追蹤推運對中點的影響
- **合盤分析**：比較兩人的中點關係

### 3. 用戶體驗改進
- **自定義符號**：允許用戶自定義顯示符號
- **顏色主題**：提供多種顏色主題選擇
- **詳細信息**：點擊顯示詳細的占星學解釋

## 總結

通過新增日月中點和宿命點，阿拉伯點系統得到了重要的擴展：

1. **理論完整性**：添加了重要的占星學敏感點
2. **計算準確性**：實現了數學上正確的計算邏輯
3. **系統整合**：與現有系統無縫整合
4. **用戶價值**：為用戶提供了更豐富的占星學分析工具

這兩個新增的點為占星師和愛好者提供了更深入的個人分析工具，特別是在理解個人內在平衡和命運轉折點方面具有重要價值。通過精確的計算和清晰的顯示，用戶可以更好地理解這些敏感點在個人星盤中的意義和影響。
