# Firebase SDK 實現說明

## 概述

完成了 `UserDataInitializationService` 中所有 Firebase SDK 方法的 Firestore 實現。這些實現目前被註釋掉，因為項目中的 `cloud_firestore` 依賴被暫時移除。當需要啟用 SDK 功能時，只需取消註釋相關代碼並添加依賴即可。

## 實現的 SDK 方法

### 1. 用戶檔案管理

#### 獲取用戶檔案
```dart
/// 通過 SDK 獲取用戶檔案
static Future<Map<String, dynamic>?> _getUserProfileViaSDK(String userId) async {
  try {
    final firestore = FirebaseFirestore.instance;
    final docRef = firestore.collection('user_profiles').doc(userId);
    final docSnapshot = await docRef.get();
    
    if (docSnapshot.exists) {
      final data = docSnapshot.data() as Map<String, dynamic>?;
      if (data != null) {
        logger.d('通過 SDK 獲取用戶檔案成功: $userId');
        return data;
      }
    }
    
    logger.d('用戶檔案不存在: $userId');
    return null;
  } catch (e) {
    logger.e('通過 SDK 獲取用戶檔案失敗: $e');
    return null;
  }
}
```

#### 設置用戶檔案
```dart
/// 通過 SDK 設置用戶檔案
static Future<bool> _setUserProfileViaSDK(String userId, Map<String, dynamic> data) async {
  try {
    final firestore = FirebaseFirestore.instance;
    final docRef = firestore.collection('user_profiles').doc(userId);
    
    // 添加伺服器時間戳
    data['updated_at'] = FieldValue.serverTimestamp();
    if (!data.containsKey('created_at')) {
      data['created_at'] = FieldValue.serverTimestamp();
    }
    
    await docRef.set(data, SetOptions(merge: true));
    logger.i('通過 SDK 設置用戶檔案成功: $userId');
    return true;
  } catch (e) {
    logger.e('通過 SDK 設置用戶檔案失敗: $e');
    return false;
  }
}
```

### 2. 用戶設定管理

#### 獲取用戶設定
```dart
/// 通過 SDK 獲取用戶設定
static Future<Map<String, dynamic>?> _getUserSettingsViaSDK(String userId) async {
  try {
    final firestore = FirebaseFirestore.instance;
    final docRef = firestore.collection('user_settings').doc(userId);
    final docSnapshot = await docRef.get();
    
    if (docSnapshot.exists) {
      final data = docSnapshot.data() as Map<String, dynamic>?;
      if (data != null) {
        logger.d('通過 SDK 獲取用戶設定成功: $userId');
        return data;
      }
    }
    
    logger.d('用戶設定不存在: $userId');
    return null;
  } catch (e) {
    logger.e('通過 SDK 獲取用戶設定失敗: $e');
    return null;
  }
}
```

#### 設置用戶設定
```dart
/// 通過 SDK 設置用戶設定
static Future<bool> _setUserSettingsViaSDK(String userId, Map<String, dynamic> data) async {
  try {
    final firestore = FirebaseFirestore.instance;
    final docRef = firestore.collection('user_settings').doc(userId);
    
    // 添加伺服器時間戳
    data['updated_at'] = FieldValue.serverTimestamp();
    if (!data.containsKey('created_at')) {
      data['created_at'] = FieldValue.serverTimestamp();
    }
    
    await docRef.set(data, SetOptions(merge: true));
    logger.i('通過 SDK 設置用戶設定成功: $userId');
    return true;
  } catch (e) {
    logger.e('通過 SDK 設置用戶設定失敗: $e');
    return false;
  }
}
```

### 3. 用戶偏好設定管理

#### 獲取用戶偏好設定
```dart
/// 通過 SDK 獲取用戶偏好設定
static Future<Map<String, dynamic>?> _getUserPreferencesViaSDK(String userId) async {
  try {
    final firestore = FirebaseFirestore.instance;
    final docRef = firestore.collection('user_preferences').doc(userId);
    final docSnapshot = await docRef.get();
    
    if (docSnapshot.exists) {
      final data = docSnapshot.data() as Map<String, dynamic>?;
      if (data != null) {
        logger.d('通過 SDK 獲取用戶偏好設定成功: $userId');
        return data;
      }
    }
    
    logger.d('用戶偏好設定不存在: $userId');
    return null;
  } catch (e) {
    logger.e('通過 SDK 獲取用戶偏好設定失敗: $e');
    return null;
  }
}
```

#### 設置用戶偏好設定
```dart
/// 通過 SDK 設置用戶偏好設定
static Future<bool> _setUserPreferencesViaSDK(String userId, Map<String, dynamic> data) async {
  try {
    final firestore = FirebaseFirestore.instance;
    final docRef = firestore.collection('user_preferences').doc(userId);
    
    // 添加伺服器時間戳
    data['updated_at'] = FieldValue.serverTimestamp();
    if (!data.containsKey('created_at')) {
      data['created_at'] = FieldValue.serverTimestamp();
    }
    
    await docRef.set(data, SetOptions(merge: true));
    logger.i('通過 SDK 設置用戶偏好設定成功: $userId');
    return true;
  } catch (e) {
    logger.e('通過 SDK 設置用戶偏好設定失敗: $e');
    return false;
  }
}
```

## 啟用 SDK 功能的步驟

### 1. 添加依賴
在 `pubspec.yaml` 中取消註釋：
```yaml
dependencies:
  cloud_firestore: ^4.13.6
```

### 2. 取消註釋導入
在 `user_data_initialization_service.dart` 中：
```dart
import 'package:cloud_firestore/cloud_firestore.dart'; // 取消註釋這行
```

### 3. 取消註釋實現代碼
將所有 SDK 方法中被註釋的 Firestore 代碼取消註釋，並移除暫時實現的部分。

### 4. 配置 Firebase
確保 Firebase 項目已正確配置 Firestore 數據庫。

## SDK 實現的優勢

### 1. 實時同步
- 支援實時數據更新
- 自動處理離線/在線狀態
- 本地快取機制

### 2. 伺服器時間戳
```dart
data['updated_at'] = FieldValue.serverTimestamp();
data['created_at'] = FieldValue.serverTimestamp();
```
- 使用伺服器時間戳確保一致性
- 避免客戶端時間不準確的問題

### 3. 原子性操作
```dart
await docRef.set(data, SetOptions(merge: true));
```
- 使用 `merge: true` 保留現有欄位
- 原子性更新，避免數據競爭

### 4. 類型安全
```dart
final data = docSnapshot.data() as Map<String, dynamic>?;
```
- 強類型檢查
- 空安全處理

## Firestore 安全規則

### 建議的安全規則
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // 用戶檔案 - 只有用戶本人可以讀寫
    match /user_profiles/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // 用戶設定 - 只有用戶本人可以讀寫
    match /user_settings/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // 用戶偏好設定 - 只有用戶本人可以讀寫
    match /user_preferences/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
  }
}
```

## 性能優化

### 1. 批量操作
```dart
// 批量寫入多個文檔
final batch = firestore.batch();
batch.set(profileRef, profileData);
batch.set(settingsRef, settingsData);
batch.set(preferencesRef, preferencesData);
await batch.commit();
```

### 2. 索引優化
- 為常用查詢字段創建索引
- 避免不必要的複合索引
- 監控查詢性能

### 3. 快取策略
```dart
// 使用快取優先策略
final docSnapshot = await docRef.get(const GetOptions(source: Source.cache));
if (!docSnapshot.exists) {
  // 如果快取中沒有，從伺服器獲取
  final serverSnapshot = await docRef.get(const GetOptions(source: Source.server));
}
```

## 錯誤處理

### 1. 網路錯誤
```dart
try {
  await docRef.set(data);
} on FirebaseException catch (e) {
  if (e.code == 'unavailable') {
    // 網路不可用，使用本地快取
    logger.w('網路不可用，操作將在恢復連接後重試');
  }
}
```

### 2. 權限錯誤
```dart
} on FirebaseException catch (e) {
  if (e.code == 'permission-denied') {
    logger.e('權限被拒絕，請檢查安全規則');
    return false;
  }
}
```

### 3. 配額限制
```dart
} on FirebaseException catch (e) {
  if (e.code == 'resource-exhausted') {
    logger.e('已達到配額限制，請稍後重試');
    return false;
  }
}
```

## 監控和分析

### 1. 操作統計
- 讀寫操作次數
- 錯誤率統計
- 響應時間分析

### 2. 成本控制
- 監控讀寫操作成本
- 優化查詢效率
- 使用快取減少請求

### 3. 用戶行為分析
- 數據訪問模式
- 功能使用頻率
- 錯誤發生趨勢

## 遷移策略

### 1. 從 REST API 遷移到 SDK
- 逐步遷移各個功能模組
- 保持數據格式一致性
- 提供回滾機制

### 2. 數據同步
- 確保 REST API 和 SDK 數據一致
- 處理遷移期間的數據衝突
- 驗證遷移結果

### 3. 測試策略
- A/B 測試比較性能
- 功能測試確保一致性
- 壓力測試驗證穩定性

這個完整的 SDK 實現為用戶數據管理提供了更強大的功能，包括實時同步、離線支援和更好的性能。當項目準備好使用 Firestore SDK 時，只需按照上述步驟啟用即可。
