# Starlight 初心者模式星盤選擇頁面整合說明

## 概述

`StarlightChartSelectionPage` 是專為初心者模式設計的星盤選擇頁面，與傳統的 `ChartSelectionPage` 不同，它不顯示複雜的星盤類型選擇，而是直接專注於參數選擇，讓用戶更容易理解需要提供什麼資料。

## 主要特色

### 1. 簡化的用戶界面
- **隱藏星盤類型選擇**：不顯示複雜的星盤類型網格
- **專注參數選擇**：直接顯示需要的參數（人物、時間等）
- **清晰的說明**：每個分析都有通俗易懂的描述

### 2. 用戶友好的設計
- **漸層背景**：使用溫暖的太陽琥珀色漸層
- **說明卡片**：頂部顯示分析功能的詳細說明
- **直觀的參數卡片**：每個參數都有清楚的標籤和說明

### 3. 智能化的參數管理
- **條件顯示**：根據星盤類型自動顯示需要的參數
- **排除邏輯**：選擇第二個人時自動排除已選的主要人物
- **驗證機制**：確保所有必要參數都已選擇

## 設計原則

### 1. 通俗化語言
```dart
String _getAnalysisDescription() {
  switch (widget.chartType) {
    case ChartType.natal:
      return '透過您的出生時間和地點，分析您的性格特質、天賦能力和人生方向。';
    case ChartType.synastry:
      return '比較兩個人的出生資料，分析你們的相處模式和關係相容性。';
    // ...
  }
}
```

### 2. 視覺層次
- **主標題**：分析功能名稱
- **說明文字**：功能描述和用途
- **參數區塊**：清楚分組的選擇區域
- **行動按鈕**：明確的下一步指示

### 3. 互動設計
- **快速選擇**：近期使用的人物快速選擇
- **詳細選擇**：完整的人物選擇器
- **即時反饋**：選擇狀態的即時更新

## 技術實現

### 1. 構造函數設計
```dart
const StarlightChartSelectionPage({
  super.key,
  this.primaryPerson,        // 可預設主要人物
  this.secondaryPerson,      // 可預設第二個人
  required this.chartType,   // 星盤類型（從分析頁面傳入）
  required this.analysisTitle, // 分析標題
  this.fromAnalysisPage = false,
});
```

### 2. 參數驗證邏輯
```dart
bool _canProceed() {
  if (_primaryPerson == null) return false;
  
  if (widget.chartType.requiresTwoPersons && _secondaryPerson == null) {
    return false;
  }
  
  return true;
}
```

### 3. 智能導航
```dart
Future<void> _proceedToAnalysis() async {
  final chartData = ChartData(
    chartType: widget.chartType,
    primaryPerson: _primaryPerson!,
    secondaryPerson: _secondaryPerson,
    specificDate: widget.chartType.requiresSpecificDate ? _selectedDate : null,
  );

  final calculatedChartData = await ChartViewModel.calculateChartData(chartData);
  
  Navigator.push(
    context,
    MaterialPageRoute(
      builder: (context) => AIInterpretationSelectionPage(
        chartData: calculatedChartData,
      ),
    ),
  );
}
```

## 與 StarlightAnalysisPage 的整合

### 1. 導航邏輯更新
所有分析處理方法都已更新為使用 `StarlightChartSelectionPage`：

```dart
// 雙人分析
Future<void> _handleTwoPeopleChart(ChartType chartType, String title) async {
  Navigator.push(
    context,
    MaterialPageRoute(
      builder: (context) => StarlightChartSelectionPage(
        chartType: chartType,
        analysisTitle: title,
        fromAnalysisPage: true,
      ),
    ),
  );
}

// 時間選擇分析
Future<void> _handleTimeSelectionChart(ChartType chartType, String title) async {
  final selectedPerson = await _selectBirthData(title);
  if (selectedPerson != null) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => StarlightChartSelectionPage(
          primaryPerson: selectedPerson,
          chartType: chartType,
          analysisTitle: title,
          fromAnalysisPage: true,
        ),
      ),
    );
  }
}
```

### 2. 參數傳遞
- `chartType`：從分析頁面傳入的星盤類型
- `analysisTitle`：通俗化的分析標題
- `primaryPerson`：預選的主要人物（如果有）
- `fromAnalysisPage`：標記來源頁面

## 用戶體驗流程

### 1. 從分析頁面進入
1. 用戶在 `StarlightAnalysisPage` 點擊分析卡片
2. 系統判斷需要的參數類型
3. 導航到 `StarlightChartSelectionPage`
4. 顯示對應的參數選擇界面

### 2. 參數選擇過程
1. **查看說明**：了解分析功能和用途
2. **快速選擇**：從近期使用的人物中選擇
3. **詳細選擇**：使用完整的人物選擇器
4. **時間設定**：選擇分析時間（如果需要）
5. **確認進行**：點擊開始分析按鈕

### 3. 驗證和導航
1. 系統驗證所有必要參數
2. 計算星盤數據
3. 導航到 AI 解讀選擇頁面

## 優勢對比

### 傳統 ChartSelectionPage
- ✅ 功能完整，支援所有星盤類型
- ✅ 專業用戶友好
- ❌ 界面複雜，初學者難以理解
- ❌ 需要占星知識背景

### Starlight ChartSelectionPage
- ✅ 界面簡潔，易於理解
- ✅ 通俗化語言，無需專業知識
- ✅ 專注參數選擇，減少困惑
- ✅ 智能化驗證和引導
- ❌ 功能相對簡化

## 維護建議

### 1. 保持同步
- 當添加新的星盤類型時，更新分析描述
- 確保參數驗證邏輯與星盤類型需求一致
- 保持與主版本的功能對等

### 2. 用戶反饋
- 收集初心者用戶的使用反饋
- 優化說明文字的清晰度
- 改進參數選擇的用戶體驗

### 3. 測試覆蓋
- 測試所有星盤類型的參數選擇
- 驗證導航流程的正確性
- 確保錯誤處理的完整性

## 未來擴展

### 1. 引導功能
- 添加新手引導動畫
- 提供參數選擇的幫助提示
- 實現漸進式功能揭示

### 2. 個性化
- 記住用戶的常用選擇
- 提供個性化的分析推薦
- 支援自定義分析流程

### 3. 教育功能
- 添加占星知識的簡單介紹
- 提供從初心者到專業的學習路徑
- 實現互動式的功能說明
