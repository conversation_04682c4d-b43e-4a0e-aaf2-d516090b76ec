# Google Sign-In ID Token 為空問題修復指南

## 🚨 問題描述

**錯誤訊息**：`Google 登入失敗: Exception: Google ID Token 為空`

這個問題通常發生在 Google Sign-In 配置不正確或網路問題導致無法獲取有效的 ID Token。

## 🔍 問題原因

### 1. 配置問題
- **SHA-1 指紋未正確配置**：最常見的原因
- **google-services.json 過期**：配置文件版本過舊
- **OAuth 2.0 客戶端配置錯誤**：Google 開發者控制台配置問題
- **openid scope 缺失**：認證範圍配置不完整

### 2. 環境問題
- **Google Play 服務版本過舊**：Android 設備問題
- **網路連接問題**：無法連接到 Google 服務
- **防火牆阻擋**：企業網路環境問題

### 3. 代碼問題
- **認證流程錯誤**：調用順序或方式不正確
- **快取問題**：舊的認證信息干擾

## ✅ 解決方案

### 1. 立即診斷

#### 使用診斷工具
```dart
// 在調試頁面中運行
final diagnostic = await GoogleSignInDiagnostic.runFullDiagnostic();
GoogleSignInDiagnostic.printDiagnostic(diagnostic);
```

#### 手動檢查
1. 打開 Firebase 認證調試頁面
2. 點擊「Google 完整診斷」按鈕
3. 查看詳細的診斷結果
4. 根據建議進行修復

### 2. 配置修復

#### 檢查 SHA-1 指紋
```bash
# 獲取調試 SHA-1 指紋
keytool -list -v -keystore ~/.android/debug.keystore -alias androiddebugkey -storepass android -keypass android

# 獲取發布 SHA-1 指紋
keytool -list -v -keystore /path/to/your/keystore.jks -alias your-alias
```

#### 更新 Firebase 配置
1. 登入 [Firebase 控制台](https://console.firebase.google.com/)
2. 選擇您的項目
3. 進入「項目設定」→「您的應用程式」
4. 添加或更新 SHA-1 指紋
5. 下載最新的 `google-services.json`
6. 替換 `android/app/google-services.json`

#### 檢查 Google 開發者控制台
1. 登入 [Google 開發者控制台](https://console.developers.google.com/)
2. 選擇您的項目
3. 進入「憑證」頁面
4. 檢查 OAuth 2.0 客戶端 ID 配置
5. 確認 SHA-1 指紋已正確添加

### 3. 代碼修復

#### 確認 Scopes 配置
```dart
static final GoogleSignIn _googleSignIn = GoogleSignIn(
  scopes: [
    'email',
    'https://www.googleapis.com/auth/userinfo.profile',
    'openid', // 👈 這個很重要！
  ],
);
```

#### 改善錯誤處理
```dart
static Future<AppUser?> signInWithGoogle() async {
  try {
    // 清理現有狀態
    if (await _googleSignIn.isSignedIn()) {
      await _googleSignIn.signOut();
    }

    final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();
    if (googleUser == null) return null;

    final GoogleSignInAuthentication googleAuth = await googleUser.authentication;
    
    // 檢查 ID Token
    if (googleAuth.idToken == null) {
      // 嘗試清除快取並重試
      await googleUser.clearAuthCache();
      final retryAuth = await googleUser.authentication;
      
      if (retryAuth.idToken == null) {
        throw Exception('無法獲取 Google ID Token，請檢查配置');
      }
      
      // 使用重試的認證信息
      return await _processGoogleAuth(retryAuth);
    }
    
    return await _processGoogleAuth(googleAuth);
  } catch (e) {
    logger.e('Google 登入失敗: $e');
    rethrow;
  }
}
```

### 4. 環境修復

#### Android 環境
```bash
# 更新 Google Play 服務
# 在設備上打開 Google Play 商店
# 搜尋「Google Play 服務」並更新

# 清除應用快取
adb shell pm clear com.your.package.name
```

#### 網路環境
```bash
# 測試網路連接
ping google.com
nslookup accounts.google.com

# 檢查防火牆設定
# 確保以下域名可以訪問：
# - accounts.google.com
# - oauth2.googleapis.com
# - www.googleapis.com
```

## 🔧 逐步修復流程

### 步驟 1：運行診斷
1. 打開應用的調試頁面
2. 點擊「Google 完整診斷」
3. 查看診斷結果
4. 記錄發現的問題

### 步驟 2：修復配置
1. 檢查並更新 SHA-1 指紋
2. 下載最新的 google-services.json
3. 替換項目中的配置文件
4. 重新編譯應用

### 步驟 3：測試修復
1. 清除應用數據和快取
2. 重新安裝應用
3. 嘗試 Google 登入
4. 檢查是否獲得 ID Token

### 步驟 4：驗證結果
1. 再次運行診斷工具
2. 確認 ID Token 正常獲取
3. 測試完整的登入流程

## 📋 檢查清單

### 配置檢查
- [ ] SHA-1 指紋已添加到 Firebase
- [ ] google-services.json 是最新版本
- [ ] OAuth 2.0 客戶端配置正確
- [ ] openid scope 已包含

### 環境檢查
- [ ] Google Play 服務已更新
- [ ] 網路連接正常
- [ ] 防火牆允許 Google 服務
- [ ] 設備時間正確

### 代碼檢查
- [ ] GoogleSignIn 配置正確
- [ ] 錯誤處理完善
- [ ] 認證流程正確
- [ ] 快取清理機制

## 🚀 預防措施

### 1. 定期檢查
- 每月檢查 Google 服務配置
- 監控認證成功率
- 更新依賴項版本

### 2. 監控告警
```dart
// 添加認證監控
static Future<void> _monitorAuthentication() async {
  try {
    final user = await signInWithGoogle();
    if (user != null) {
      logger.i('Google 認證成功');
      // 發送成功指標
    }
  } catch (e) {
    logger.e('Google 認證失敗: $e');
    // 發送失敗告警
  }
}
```

### 3. 用戶指導
- 提供清晰的錯誤訊息
- 指導用戶更新 Google Play 服務
- 提供替代登入方式

## 📞 技術支援

### 常見問題
1. **Q**: 為什麼 ID Token 仍然為空？
   **A**: 檢查 SHA-1 指紋是否正確，並確保 google-services.json 是最新版本。

2. **Q**: 如何確認配置是否正確？
   **A**: 使用診斷工具檢查所有配置項目。

3. **Q**: 網路環境有什麼要求？
   **A**: 需要能夠訪問 Google 的認證服務器，檢查防火牆設定。

### 聯繫支援
- 查看 [Google Sign-In 官方文檔](https://developers.google.com/identity/sign-in/android)
- 檢查 [Firebase 認證文檔](https://firebase.google.com/docs/auth)
- 使用診斷工具生成詳細報告

## 📊 成功指標

修復成功後，您應該看到：
- ✅ 診斷工具顯示 ID Token 存在
- ✅ Google 登入流程順利完成
- ✅ 用戶資料正確獲取
- ✅ 認證狀態正常保持

通過遵循這個指南，您應該能夠解決 Google Sign-In ID Token 為空的問題，並建立穩定可靠的認證系統。
