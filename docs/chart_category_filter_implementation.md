# 星盤類型過濾功能實作文件

## 概述
本文件說明在出生資料列表中新增星盤類型過濾功能的實作過程，允許用戶選擇只顯示特定類型的星盤資料，提供更精確的資料管理和查看體驗。

## 實作目標
- 在排序選擇器中新增過濾功能標籤頁
- 支援多選星盤類型進行過濾
- 整合到現有的排序和搜尋系統
- 提供直觀的用戶界面和操作體驗
- 保持與現有功能的兼容性

## 主要功能特點

### 1. 星盤類型分類
根據 `ChartCategory` 枚舉，將星盤類型分為三大類：

#### 個人相關類別
- **個人**：個人本命盤和相關分析
- **家人**：家庭成員的星盤資料
- **朋友**：朋友的星盤資料
- **伴侶**：伴侶和戀人的星盤資料

#### 專業相關類別
- **工作**：工作相關的星盤資料
- **客戶**：客戶的星盤資料

#### 特殊類別
- **事件**：特殊事件的星盤
- **天象**：天象事件的星盤
- **其他**：其他類型的星盤

### 2. 用戶界面設計

#### Tab 式界面
```dart
DefaultTabController(
  length: 2,
  child: Column(
    children: [
      TabBar(
        tabs: [
          Tab(text: '排序'),
          Tab(text: '過濾'),
        ],
      ),
      TabBarView(
        children: [
          _buildSortOptionsTab(),
          _buildFilterOptionsTab(),
        ],
      ),
    ],
  ),
)
```

#### 過濾選項界面
- **分組顯示**：按類別分組顯示星盤類型
- **多選支援**：支援同時選擇多個星盤類型
- **視覺反饋**：選中的選項有明顯的視覺標示
- **快捷操作**：提供全選和清除按鈕

## 技術實現

### 1. 數據結構更新

#### SortFilterResult 類別
```dart
class SortFilterResult {
  final SortOption? sortOption;
  final Set<ChartCategory>? categoryFilter;

  const SortFilterResult({
    this.sortOption,
    this.categoryFilter,
  });
}
```

#### FilesViewModel 擴展
```dart
class FilesViewModel extends ChangeNotifier {
  // 過濾相關
  Set<ChartCategory> _categoryFilter = Set.from(ChartCategory.values);
  
  Set<ChartCategory> get categoryFilter => _categoryFilter;
  
  void setCategoryFilter(Set<ChartCategory> categories) {
    _categoryFilter = Set.from(categories);
    _filterBirthData();
  }
}
```

### 2. 過濾邏輯實現

#### 雙重過濾機制
```dart
void _filterBirthData() {
  final query = searchController.text.toLowerCase();

  // 先根據類別過濾
  List<BirthData> categoryFiltered = _birthDataList.where((data) {
    return _categoryFilter.contains(data.category);
  }).toList();

  // 再根據搜索文本過濾
  if (query.isEmpty) {
    _filteredList = categoryFiltered;
  } else {
    _filteredList = categoryFiltered.where((data) {
      return data.name.toLowerCase().contains(query) ||
          data.birthPlace.toLowerCase().contains(query) ||
          (data.notes != null && data.notes!.toLowerCase().contains(query));
    }).toList();
  }

  _sortBirthData();
  notifyListeners();
}
```

### 3. UI 組件實現

#### 類別選項構建
```dart
Widget _buildCategoryOption(ChartCategory category) {
  final isSelected = _selectedCategories.contains(category);
  
  return Container(
    margin: const EdgeInsets.only(bottom: 8),
    child: InkWell(
      onTap: () {
        setState(() {
          if (isSelected) {
            _selectedCategories.remove(category);
          } else {
            _selectedCategories.add(category);
          }
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: isSelected ? category.color.withOpacity(0.1) : Colors.transparent,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? category.color : Colors.grey.shade300,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          children: [
            Icon(category.icon, color: isSelected ? category.color : AppColors.textMedium),
            const SizedBox(width: 12),
            Expanded(child: Text(category.displayName)),
            if (isSelected) Icon(Icons.check_circle, color: category.color),
          ],
        ),
      ),
    ),
  );
}
```

## 用戶操作流程

### 1. 訪問過濾功能
1. 在出生資料列表頁面點擊排序按鈕
2. 在彈出的選擇器中切換到「過濾」標籤頁
3. 查看按類別分組的星盤類型選項

### 2. 選擇過濾條件
1. **單選**：點擊任一星盤類型進行選擇/取消選擇
2. **全選**：點擊「全選」按鈕選擇所有類型
3. **清除**：點擊「清除」按鈕取消所有選擇
4. **套用**：點擊「套用過濾」按鈕應用設定

### 3. 查看過濾結果
1. 列表自動更新，只顯示選中類型的星盤資料
2. 過濾條件與搜尋功能同時生效
3. 排序功能正常作用於過濾後的結果

## 整合特點

### 1. 與現有功能的整合

#### 搜尋功能整合
- 過濾後的結果仍然支援文字搜尋
- 搜尋範圍限制在已過濾的資料中
- 搜尋和過濾條件同時生效

#### 排序功能整合
- 所有排序選項對過濾後的資料生效
- 排序和過濾設定獨立保存
- 用戶可以同時調整排序和過濾條件

#### 資料夾功能整合
- 過濾功能在所有資料夾中都可使用
- 過濾條件對當前資料夾的內容生效
- 切換資料夾時保持過濾設定

### 2. 狀態管理

#### 過濾狀態持久化
- 過濾設定在應用會話期間保持
- 重新打開過濾器時顯示當前設定
- 支援重置為預設狀態（顯示所有類型）

#### 響應式更新
- 過濾條件變更時立即更新列表
- 使用 ChangeNotifier 模式通知 UI 更新
- 確保數據一致性

## 性能優化

### 1. 高效過濾算法
- 使用 Set 數據結構進行快速查找
- 避免重複的數據處理
- 最小化 UI 重建次數

### 2. 內存管理
- 保持原始數據不變
- 只在需要時創建過濾後的副本
- 及時釋放不需要的臨時數據

## 用戶體驗設計

### 1. 直觀的視覺設計
- **顏色編碼**：每個星盤類型使用獨特的顏色
- **圖標識別**：清晰的圖標幫助快速識別
- **狀態反饋**：選中狀態有明顯的視覺標示

### 2. 便捷的操作方式
- **快捷按鈕**：全選和清除按鈕提供快速操作
- **分組顯示**：按邏輯分組減少認知負擔
- **即時預覽**：選擇變更時即時顯示影響

### 3. 一致的交互模式
- **統一的選擇器**：與排序功能使用相同的 UI 模式
- **標準手勢**：遵循 Material Design 的交互規範
- **清晰的導航**：Tab 切換提供清晰的功能分區

## 測試驗證

### 1. 功能測試
- ✅ 過濾功能正確篩選資料
- ✅ 多選和單選操作正常
- ✅ 全選和清除功能正常
- ✅ 與搜尋功能正確整合

### 2. 整合測試
- ✅ 與排序功能協同工作
- ✅ 與資料夾功能協同工作
- ✅ 狀態管理正確
- ✅ UI 響應及時

### 3. 用戶體驗測試
- ✅ 操作流程直觀
- ✅ 視覺反饋清晰
- ✅ 性能表現良好
- ✅ 錯誤處理適當

## 未來擴展

### 1. 高級過濾選項
- 支援日期範圍過濾
- 支援標籤過濾
- 支援自定義過濾條件

### 2. 過濾預設
- 保存常用的過濾組合
- 快速切換預設過濾條件
- 分享過濾設定

### 3. 統計功能
- 顯示各類型的資料統計
- 提供過濾結果的摘要信息
- 支援數據分析和報告

## 總結

星盤類型過濾功能的實作大大提升了出生資料管理的靈活性和效率：

1. **精確篩選**：用戶可以快速找到特定類型的星盤資料
2. **直觀操作**：分組顯示和多選支援提供良好的用戶體驗
3. **完美整合**：與現有的搜尋、排序、資料夾功能無縫整合
4. **高效性能**：優化的過濾算法確保流暢的操作體驗

這個功能特別適合管理大量不同類型星盤資料的用戶，幫助他們更有效地組織和查找所需的資料，提升整體的工作效率和用戶滿意度。
