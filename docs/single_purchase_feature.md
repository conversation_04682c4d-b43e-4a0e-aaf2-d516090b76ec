# 單次購買功能實作

## 📋 功能概述

成功在 `PaymentPage` 中新增了單次購買選項，讓用戶可以購買單次解讀服務（NT$ 99），而不需要訂閱月度/季度/年度方案。這為用戶提供了更靈活的付費選擇，特別適合偶爾使用解讀功能的用戶。

## 🎯 實作內容

### 1. PaymentService 擴展
**檔案位置**：`lib/services/payment_service.dart`

**新增常數**：
```dart
static const String _singlePurchaseKey = 'astreal_single_purchase';
```

**新增方法**：
- `getRemainingSinglePurchases()` - 獲取剩餘單次購買次數
- `addSinglePurchase(int count)` - 添加單次購買次數
- `useSinglePurchaseAttempt()` - 使用一次單次購買

**權限檢查邏輯更新**：
```dart
/// 檢查用戶是否有權限進行 AI 解讀
static Future<bool> hasInterpretationPermission() async {
  // 1. 檢查是否為付費用戶
  if (await isPremiumUser()) return true;
  
  // 2. 檢查單次購買次數
  if (await getRemainingSinglePurchases() > 0) return true;
  
  // 3. 檢查免費試用次數
  return await getRemainingFreeTrials() > 0;
}
```

### 2. 支付方案擴展
**新增單次購買方案**：
```dart
{
  'id': 'single',
  'name': '單次解讀',
  'description': '購買一次 AI 解讀服務',
  'price': 99.0,
  'currency': 'TWD',
  'duration': 0, // 單次購買
  'type': 'single', // 標記為單次購買
  'features': [
    '一次完整 AI 星盤解讀',
    '專業占星分析',
    '個人化建議',
    '立即可用',
  ],
}
```

### 3. 模擬支付流程更新
**單次購買處理邏輯**：
```dart
// 如果是單次購買，添加單次購買次數
if (planType == 'single') {
  final success = await addSinglePurchase(1);
  if (success) {
    logger.i('單次購買成功，添加 1 次解讀次數');
    // 創建單次購買記錄
    final payment = PaymentRecord(
      id: 'single_${DateTime.now().millisecondsSinceEpoch}',
      planType: planType,
      amount: amount,
      currency: 'TWD',
      paymentDate: DateTime.now(),
      expiryDate: DateTime.now().add(const Duration(days: 365)), // 1年有效期
      isValid: true,
      paymentMethod: 'simulation',
      transactionId: 'sim_${DateTime.now().millisecondsSinceEpoch}',
    );
    await addPaymentRecord(payment);
    return payment;
  }
}
```

### 4. PaymentPage UI 更新
**檔案位置**：`lib/ui/pages/payment_page.dart`

**狀態顯示更新**：
```dart
if (remainingSinglePurchases > 0) ...[
  Row(
    children: [
      Icon(Icons.payment, color: AppColors.solarAmber, size: 16),
      const SizedBox(width: 4),
      Text('單次購買剩餘：$remainingSinglePurchases 次'),
    ],
  ),
  const SizedBox(height: 4),
],
```

**按鈕文字動態化**：
```dart
Text(
  selectedPlan['type'] == 'single' 
      ? '立即購買 - NT\$ ${selectedPlan['price'].toInt()}'
      : '立即訂閱 - NT\$ ${selectedPlan['price'].toInt()}',
  // ...
)
```

**價格顯示優化**：
```dart
Text(
  plan['type'] == 'single' 
      ? '/ 單次'
      : '/ ${plan['duration']} 個月',
  style: const TextStyle(color: Colors.grey),
)
```

**成功訊息差異化**：
```dart
final successMessage = plan['type'] == 'single'
    ? '購買成功！您獲得了 1 次解讀機會。'
    : '訂閱成功！您現在可以無限次使用解讀功能。';
```

### 5. AI 解讀頁面整合
**檔案位置**：`lib/ui/pages/ai_interpretation_result_page.dart`

**使用優先級邏輯**：
```dart
// 如果是免費用戶，優先使用單次購買，然後使用免費試用
final isPremium = await PaymentService.isPremiumUser();
if (!isPremium) {
  // 先嘗試使用單次購買
  final remainingSinglePurchases = await PaymentService.getRemainingSinglePurchases();
  if (remainingSinglePurchases > 0) {
    final success = await PaymentService.useSinglePurchaseAttempt();
    if (success) {
      final remaining = await PaymentService.getRemainingSinglePurchases();
      // 顯示單次購買使用提示
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('使用單次購買解讀，剩餘 $remaining 次'),
          backgroundColor: AppColors.solarAmber,
        ),
      );
    }
  } else {
    // 沒有單次購買，嘗試使用免費試用
    final success = await PaymentService.useFreeTrialAttempt();
    if (!success) {
      _showPaymentRequired();
      return;
    }
    // 顯示免費試用使用提示
  }
}
```

## 🚀 用戶體驗流程

### 1. 單次購買流程
1. **進入支付頁面** → 看到四個選項（單次、月度、季度、年度）
2. **選擇單次解讀** → NT$ 99 / 單次
3. **點擊立即購買** → 模擬支付處理
4. **購買成功** → 「購買成功！您獲得了 1 次解讀機會。」
5. **返回解讀頁面** → 自動執行解讀

### 2. 使用單次購買解讀
1. **進入解讀頁面** → 自動檢查權限
2. **優先使用單次購買** → 扣除 1 次單次購買次數
3. **顯示剩餘次數** → 「使用單次購買解讀，剩餘 X 次」
4. **執行解讀** → 正常進行 AI 解讀

### 3. 狀態顯示優化
**支付頁面狀態卡片**：
- 付費會員：「您目前是付費會員，可以無限次使用解讀功能。」
- 免費用戶：
  - 💰 單次購買剩餘：X 次
  - ☕ 免費試用剩餘：X 次
  - 說明：「可購買單次解讀或升級為付費會員享受無限次解讀服務。」

## 📊 方案對比

| 方案類型 | 價格 | 期限 | 適用對象 | 特色 |
|----------|------|------|----------|------|
| **單次解讀** | NT$ 99 | 單次 | 偶爾使用者 | 立即可用、無承諾 |
| **月度方案** | NT$ 299 | 1個月 | 定期使用者 | 無限次解讀 |
| **季度方案** | NT$ 799 | 3個月 | 節省 10% | 季度專屬報告 |
| **年度方案** | NT$ 2899 | 12個月 | 節省 20% | 一對一諮詢機會 |

## 🎨 UI/UX 設計特色

### 1. 視覺層次
- **單次購買**：突出顯示「/ 單次」標籤
- **動態按鈕**：「立即購買」vs「立即訂閱」
- **狀態圖標**：💰 單次購買、☕ 免費試用、⭐ 付費會員

### 2. 用戶提示
- **購買成功**：「購買成功！您獲得了 1 次解讀機會。」
- **使用提示**：「使用單次購買解讀，剩餘 X 次」（金色背景）
- **免費試用**：「免費試用剩餘 X 次」（藍色背景）

### 3. 狀態管理
- **本地存儲**：使用 SharedPreferences 存儲單次購買次數
- **即時更新**：購買後立即更新狀態顯示
- **優先級邏輯**：付費會員 > 單次購買 > 免費試用

## 🔧 技術實作細節

### 1. 數據存儲
```dart
// 存儲單次購買次數
static const String _singlePurchaseKey = 'astreal_single_purchase';

// 獲取剩餘次數
final prefs = await SharedPreferences.getInstance();
return prefs.getInt(_singlePurchaseKey) ?? 0;

// 添加次數
await prefs.setInt(_singlePurchaseKey, currentCount + count);

// 使用次數
await prefs.setInt(_singlePurchaseKey, remainingPurchases - 1);
```

### 2. 權限檢查邏輯
```dart
/// 檢查順序：付費會員 → 單次購買 → 免費試用
static Future<bool> hasInterpretationPermission() async {
  if (await isPremiumUser()) return true;
  if (await getRemainingSinglePurchases() > 0) return true;
  return await getRemainingFreeTrials() > 0;
}
```

### 3. 使用優先級
```dart
/// 使用順序：付費會員（無限制） → 單次購買 → 免費試用
if (!isPremium) {
  if (remainingSinglePurchases > 0) {
    // 使用單次購買
    await PaymentService.useSinglePurchaseAttempt();
  } else {
    // 使用免費試用
    await PaymentService.useFreeTrialAttempt();
  }
}
```

## 🔮 未來擴展

### 1. 批量購買
- **5次解讀包**：NT$ 450（省 10%）
- **10次解讀包**：NT$ 850（省 15%）
- **20次解讀包**：NT$ 1600（省 20%）

### 2. 特殊解讀類型
- **深度解讀**：NT$ 199（更詳細的分析）
- **快速解讀**：NT$ 49（簡化版分析）
- **專題解讀**：NT$ 149（特定主題分析）

### 3. 促銷活動
- **首次購買優惠**：第一次單次購買 50% 折扣
- **生日優惠**：生日月免費單次解讀
- **推薦獎勵**：推薦朋友獲得免費解讀次數

### 4. 使用統計
- **購買歷史**：顯示所有單次購買記錄
- **使用分析**：統計用戶的解讀習慣
- **個性化推薦**：根據使用頻率推薦合適方案

## 📈 商業價值

### 1. 降低用戶門檻
- **低價嘗試**：NT$ 99 的低門檻讓用戶容易嘗試
- **無承諾壓力**：不需要訂閱承諾，減少用戶顧慮
- **靈活選擇**：滿足不同使用頻率的用戶需求

### 2. 增加轉換機會
- **體驗優化**：通過單次購買體驗服務品質
- **升級引導**：使用頻繁的用戶自然會考慮訂閱
- **收入多元化**：除了訂閱收入，增加單次購買收入

### 3. 用戶留存
- **彈性付費**：用戶可以根據需要靈活購買
- **降低流失**：免費試用用完後仍有低價選項
- **品牌忠誠度**：良好的單次體驗建立信任

## 📝 總結

### 主要成就
- ✅ 成功實作單次購買功能（NT$ 99）
- ✅ 完整的權限檢查和使用邏輯
- ✅ 優化的用戶體驗和狀態顯示
- ✅ 靈活的支付選項組合
- ✅ 本地存儲和狀態管理
- ✅ 差異化的 UI 提示和反饋

### 技術優勢
- **無縫整合**：與現有支付系統完美整合
- **優先級邏輯**：智能的使用順序管理
- **狀態同步**：即時的狀態更新和顯示
- **用戶友善**：清晰的提示和反饋機制

### 商業價值
- **降低門檻**：NT$ 99 的低價嘗試選項
- **增加轉換**：從單次購買到訂閱的自然升級路徑
- **收入多元化**：單次購買 + 訂閱的雙重收入模式
- **用戶留存**：靈活的付費選項提高用戶滿意度

這個單次購買功能為應用提供了更完整的商業模式，滿足了不同用戶群體的需求，同時為業務增長提供了新的機會。
