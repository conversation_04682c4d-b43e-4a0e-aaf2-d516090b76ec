# Flutter Web 部署快取解決方案使用指南

## 問題概述

Flutter Web 應用部署到 Firebase Hosting 後，用戶瀏覽器可能會快取舊版本，導致無法立即看到新功能。本指南提供完整的解決方案。

## 解決方案架構

### 1. 版本管理系統
- **版本文件**: `web/version.json` - 記錄當前版本資訊
- **版本檢查**: `web/simple_version_check.js` - 自動檢查並提示更新
- **快取控制**: `firebase.json` - 設定適當的快取標頭

### 2. 部署腳本
- **標準部署**: `scripts/deploy_web.sh` - 完整的部署流程
- **強制更新**: `scripts/deploy_web_force_update.sh` - 強制清除快取的部署
- **快速部署**: `scripts/quick_deploy.sh` - 簡化的快速部署
- **部署驗證**: `scripts/verify_deployment.sh` - 驗證部署是否成功

## 使用方法

### 一般部署
```bash
# 標準部署（推薦）
./scripts/deploy_web.sh

# 重要更新部署（會提示用戶更新）
./scripts/deploy_web.sh --notify-update

# 指定版本號部署
./scripts/deploy_web.sh --version=1.2.0

# 強制清除快取部署
./scripts/deploy_web.sh --force-cache-clear
```

### 快速部署
```bash
# 適用於小幅修改的快速部署
./scripts/quick_deploy.sh
```

### 強制更新部署
```bash
# 適用於重要更新，確保用戶立即看到新版本
./scripts/deploy_web_force_update.sh
```

### 驗證部署
```bash
# 驗證部署是否成功
./scripts/verify_deployment.sh

# 驗證特定版本
./scripts/verify_deployment.sh "20250127_143000"
```

## 版本檢查機制

### 自動檢查
- 頁面載入後 30 秒開始首次檢查
- 每 2 分鐘自動檢查一次
- 頁面重新獲得焦點時檢查
- 頁面可見性變化時檢查

### 更新通知
- **一般更新**: 藍色通知，可選擇稍後更新
- **重要更新**: 紅色通知，建議立即更新
- **強制更新**: 自動清除快取並重新載入

### 智能通知邏輯
- 避免相同版本重複通知
- 語義化版本號比較
- 時間戳版本智能判斷
- 記錄已通知版本避免重複

## 快取控制策略

### Firebase Hosting 設定
```json
{
  "source": "/version.json",
  "headers": [
    { "key": "Cache-Control", "value": "no-cache, no-store, must-revalidate" },
    { "key": "Pragma", "value": "no-cache" },
    { "key": "Expires", "value": "0" }
  ]
}
```

### 關鍵文件無快取
- `version.json` - 版本資訊文件
- `simple_version_check.js` - 版本檢查腳本
- `main.dart.js` - 主要應用程式碼
- `flutter_service_worker.js` - Service Worker
- `index.html` - 主頁面

### 靜態資源快取
- 圖片、字型等靜態資源設定 24 小時快取
- 使用版本參數避免快取問題

## 故障排除

### 用戶看不到更新
1. **檢查版本文件**:
   ```bash
   curl "https://astreal.web.app/version.json?_=$(date +%s)"
   ```

2. **強制重新載入**:
   - Windows/Linux: `Ctrl + Shift + R`
   - macOS: `Cmd + Shift + R`

3. **使用無痕模式**:
   - 開啟瀏覽器無痕模式訪問網站

4. **清除瀏覽器快取**:
   - 手動清除瀏覽器快取和 Cookie

### 部署驗證失敗
1. **檢查 Firebase 專案**:
   ```bash
   firebase projects:list
   firebase use astreal-d3f70
   ```

2. **檢查部署狀態**:
   ```bash
   firebase hosting:sites:list
   ```

3. **重新部署**:
   ```bash
   ./scripts/deploy_web_force_update.sh
   ```

### 版本檢查不工作
1. **檢查控制台錯誤**: 開啟瀏覽器開發者工具查看錯誤
2. **檢查網路請求**: 確認 version.json 請求是否成功
3. **檢查快取設定**: 確認 Firebase Hosting 快取標頭設定

## 最佳實踐

### 部署前檢查
- [ ] 確認所有更改已提交到版本控制
- [ ] 執行本地測試確保功能正常
- [ ] 選擇適當的部署類型（一般/重要/強制）

### 部署後驗證
- [ ] 執行部署驗證腳本
- [ ] 檢查版本文件是否正確更新
- [ ] 測試版本檢查機制是否工作
- [ ] 在不同瀏覽器中測試

### 用戶溝通
- 重要更新時提前通知用戶
- 提供清除快取的操作指南
- 設定適當的更新訊息

## 監控和維護

### 定期檢查
- 監控部署成功率
- 檢查用戶更新採用率
- 分析快取命中率

### 日誌分析
- 檢查版本檢查請求日誌
- 分析用戶更新行為
- 監控錯誤率和失敗原因

### 持續改進
- 根據用戶反饋調整檢查頻率
- 優化更新通知體驗
- 改進快取策略
