# Firestore SDK 已啟用說明

## 概述

已成功啟用 `UserDataInitializationService` 中的所有 Firestore SDK 功能。現在非 Windows 平台將使用完整的 Firestore SDK 來管理用戶數據，而 Windows 平台繼續使用 REST API。

## 已啟用的功能

### ✅ 1. 用戶檔案管理

#### `_getUserProfileViaSDK`
```dart
static Future<Map<String, dynamic>?> _getUserProfileViaSDK(String userId) async {
  try {
    final firestore = FirebaseFirestore.instance;
    final docRef = firestore.collection('user_profiles').doc(userId);
    final docSnapshot = await docRef.get();

    if (docSnapshot.exists) {
      final data = docSnapshot.data();
      if (data != null) {
        logger.d('通過 SDK 獲取用戶檔案成功: $userId');
        return data;
      }
    }

    logger.d('用戶檔案不存在: $userId');
    return null;
  } on FirebaseException catch (e) {
    logger.e('Firestore 錯誤 - 獲取用戶檔案失敗: ${e.code} - ${e.message}');
    return null;
  } catch (e) {
    logger.e('通過 SDK 獲取用戶檔案失敗: $e');
    return null;
  }
}
```

#### `_setUserProfileViaSDK`
```dart
static Future<bool> _setUserProfileViaSDK(String userId, Map<String, dynamic> data) async {
  try {
    final firestore = FirebaseFirestore.instance;
    final docRef = firestore.collection('user_profiles').doc(userId);

    // 添加伺服器時間戳
    data['updated_at'] = FieldValue.serverTimestamp();
    if (!data.containsKey('created_at')) {
      data['created_at'] = FieldValue.serverTimestamp();
    }

    // 使用 merge 選項保留現有欄位
    await docRef.set(data, SetOptions(merge: true));
    logger.i('通過 SDK 設置用戶檔案成功: $userId');
    return true;
  } on FirebaseException catch (e) {
    logger.e('Firestore 錯誤 - 設置用戶檔案失敗: ${e.code} - ${e.message}');
    return false;
  } catch (e) {
    logger.e('通過 SDK 設置用戶檔案失敗: $e');
    return false;
  }
}
```

### ✅ 2. 用戶設定管理

#### `_getUserSettingsViaSDK` 和 `_setUserSettingsViaSDK`
- 完整的 Firestore 實現
- 自動時間戳管理
- 合併模式更新
- 詳細的錯誤處理

### ✅ 3. 用戶偏好管理

#### `_getUserPreferencesViaSDK` 和 `_setUserPreferencesViaSDK`
- 完整的 Firestore 實現
- 支援複雜的嵌套數據結構
- 原子性操作
- 完整的錯誤處理

## 技術特色

### 🔥 Firestore 原生功能

1. **實時同步**：
   - 數據變更即時更新
   - 自動處理網路狀態變化
   - 支援離線模式

2. **伺服器時間戳**：
   ```dart
   data['updated_at'] = FieldValue.serverTimestamp();
   data['created_at'] = FieldValue.serverTimestamp();
   ```
   - 確保時間一致性
   - 避免客戶端時間偏差

3. **合併更新**：
   ```dart
   await docRef.set(data, SetOptions(merge: true));
   ```
   - 保留現有欄位
   - 避免意外覆蓋
   - 支援部分更新

4. **類型安全**：
   ```dart
   final data = docSnapshot.data();
   ```
   - 強類型檢查
   - 空安全處理
   - 編譯時錯誤檢測

### 🛡️ 錯誤處理

1. **分層錯誤處理**：
   ```dart
   } on FirebaseException catch (e) {
     logger.e('Firestore 錯誤: ${e.code} - ${e.message}');
   } catch (e) {
     logger.e('一般錯誤: $e');
   }
   ```

2. **詳細錯誤信息**：
   - Firebase 特定錯誤碼
   - 詳細錯誤描述
   - 便於調試和監控

3. **優雅降級**：
   - 錯誤時返回適當的預設值
   - 不影響應用主要流程
   - 記錄詳細日誌

## 平台適配

### Windows 平台
```dart
if (Platform.isWindows) {
  return await _setUserProfileViaRestApi(userId, data);
}
```
- 使用 REST API
- 通過 HTTP 請求操作
- 適用於沒有 SDK 支援的環境

### 其他平台（Android/iOS/macOS）
```dart
else {
  return await _setUserProfileViaSDK(userId, data);
}
```
- 使用 Firestore SDK
- 原生 Firebase 功能
- 更好的性能和功能

## 數據結構

### Firestore 集合結構
```
user_profiles/
├── {userId}/
    ├── user_id: string
    ├── email: string|null
    ├── display_name: string
    ├── is_anonymous: boolean
    ├── created_at: Timestamp (server)
    ├── updated_at: Timestamp (server)
    ├── profile_completed: boolean
    ├── last_login_at: Timestamp
    └── login_count: number

user_settings/
├── {userId}/
    ├── user_id: string
    ├── language: string
    ├── theme: string
    ├── user_mode: string
    ├── notifications_enabled: boolean
    ├── created_at: Timestamp (server)
    └── updated_at: Timestamp (server)

user_preferences/
├── {userId}/
    ├── user_id: string
    ├── chart_display_preferences: object
    ├── interpretation_preferences: object
    ├── ui_preferences: object
    ├── created_at: Timestamp (server)
    └── updated_at: Timestamp (server)
```

## 使用流程

### 1. 用戶註冊/登入時
```
initializeNewUserData()
    ↓
Platform.isWindows?
    ↓ No (Android/iOS/macOS)
使用 Firestore SDK
    ↓
並行創建文檔
    ├── user_profiles/{userId}
    ├── user_settings/{userId}
    └── user_preferences/{userId}
    ↓
所有文檔創建完成
```

### 2. 數據讀取時
```
_getUserProfile(userId)
    ↓
Platform.isWindows?
    ↓ No
_getUserProfileViaSDK(userId)
    ↓
firestore.collection('user_profiles').doc(userId).get()
    ↓
返回用戶檔案數據
```

### 3. 數據更新時
```
_setUserProfile(userId, data)
    ↓
Platform.isWindows?
    ↓ No
_setUserProfileViaSDK(userId, data)
    ↓
添加時間戳
    ↓
firestore.collection('user_profiles').doc(userId).set(data, merge: true)
    ↓
更新成功
```

## 性能優勢

### 相比 REST API

| 特性 | REST API | Firestore SDK |
|------|----------|---------------|
| 實時同步 | ❌ | ✅ |
| 離線支援 | ❌ | ✅ |
| 本地快取 | ❌ | ✅ |
| 批量操作 | ❌ | ✅ |
| 原子性事務 | ❌ | ✅ |
| 類型安全 | ⚠️ | ✅ |
| 錯誤處理 | 基本 | 詳細 |
| 網路優化 | 手動 | 自動 |

## 監控和調試

### 1. 日誌記錄
- 成功操作：`logger.i()`
- 調試信息：`logger.d()`
- 錯誤信息：`logger.e()`
- 警告信息：`logger.w()`

### 2. 錯誤追蹤
- Firebase 錯誤碼
- 詳細錯誤描述
- 操作上下文信息

### 3. 性能監控
- 操作耗時
- 成功/失敗率
- 網路狀態影響

## 安全考慮

### 1. Firestore 安全規則
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /user_profiles/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    match /user_settings/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    match /user_preferences/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
  }
}
```

### 2. 數據驗證
- 用戶 ID 驗證
- 數據格式檢查
- 權限確認

## 未來擴展

### 1. 實時功能
- 實時數據監聽
- 多設備同步
- 協作功能

### 2. 高級查詢
- 複合查詢
- 分頁載入
- 排序和過濾

### 3. 批量操作
- 批量寫入
- 事務操作
- 原子性更新

現在您的應用已經擁有了完整的 Firestore SDK 支援，可以享受 Firebase 的所有原生功能和性能優勢！
