# 星盤顯示設置頁面功能說明

## 概述

新增了一個專門的星盤顯示設置頁面，讓用戶可以更方便地調整星盤的顯示選項。用戶可以通過點擊星盤頁面右上角的設置按鈕進入此頁面。

## 功能特色

### 1. 入口位置
- **位置**：星盤頁面右上角的調色盤圖示按鈕
- **導航**：點擊後直接進入專門的設置頁面，而非彈出對話框
- **返回**：使用標準的返回按鈕回到星盤頁面

### 2. 頁面設計
- **現代化界面**：使用卡片式設計，每個設置分類都有獨立的卡片
- **圖示標識**：每個設置分類都有對應的彩色圖示
- **清晰分組**：設置項目按功能分組，便於查找和使用

## 設置分類

### 1. 顯示模式
- **經典模式**：傳統星盤顯示，簡潔清晰
- **度數模式**：顯示宮位度數與行星度數
- **圖示**：👁️ 可見性圖示
- **顏色**：藍色主題

### 2. 界主星顯示
- **功能**：控制是否在星盤外圈顯示星座界主星符號
- **開關**：簡單的開關控制
- **圖示**：⭐ 星星圖示
- **顏色**：琥珀色主題

### 3. 度數顯示
- **宮位度數**：控制是否在宮位線上顯示精確度數
- **行星度數**：控制是否在行星符號旁顯示精確度數
- **圖示**：📏 直尺圖示
- **顏色**：綠色主題

### 4. 相位顯示
- **主要相位**：顯示合相、對分、三分、四分相位線
- **次要相位**：顯示六分、八分等次要相位線
- **圖示**：📈 時間軸圖示
- **顏色**：紫色主題

### 5. 顏色主題
- **經典主題**：傳統的黑白配色方案
- **現代主題**：彩色的現代配色方案
- **圖示**：🎨 調色盤圖示
- **顏色**：橙色主題

## 技術實作

### 1. 頁面結構
```dart
class ChartDisplaySettingsPage extends StatefulWidget {
  final ChartViewModel? chartViewModel;
  
  const ChartDisplaySettingsPage({
    Key? key,
    this.chartViewModel,
  }) : super(key: key);
}
```

### 2. 設置卡片組件
```dart
Widget _buildSettingCard({
  required String title,
  required IconData icon,
  required Color iconColor,
  required Widget child,
}) {
  return Container(
    decoration: BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.circular(12),
      boxShadow: [
        BoxShadow(
          color: Colors.black.withOpacity(0.05),
          blurRadius: 8,
          offset: const Offset(0, 2),
        ),
      ],
    ),
    // ... 卡片內容
  );
}
```

### 3. 開關選項組件
```dart
Widget _buildSwitchOption({
  required String title,
  required String subtitle,
  required bool value,
  required ValueChanged<bool> onChanged,
}) {
  return SwitchListTile(
    title: Text(title),
    subtitle: Text(subtitle),
    value: value,
    onChanged: onChanged,
    activeColor: Colors.blue.shade600,
  );
}
```

### 4. 單選選項組件
```dart
Widget _buildRadioOption({
  required String title,
  required String subtitle,
  required String value,
  required String groupValue,
  required ValueChanged<String?> onChanged,
}) {
  return RadioListTile<String>(
    title: Text(title),
    subtitle: Text(subtitle),
    value: value,
    groupValue: groupValue,
    onChanged: onChanged,
    activeColor: Colors.blue.shade600,
  );
}
```

## 數據模型擴展

### 1. ChartSettings 擴展
新增了以下屬性：
- `bool showMajorAspects`：主要相位顯示
- `bool showMinorAspects`：次要相位顯示
- `String colorTheme`：顏色主題

### 2. ChartTypeSettings 擴展
同樣新增了相同的屬性，支援多星盤類型設置。

### 3. SettingsViewModel 擴展
新增了以下方法：
- `updateMajorAspectsVisibility(bool isVisible)`
- `updateMinorAspectsVisibility(bool isVisible)`
- `updateColorTheme(String theme)`
- `resetChartSettings()`

## 用戶體驗

### 1. 導航流程
1. 用戶在星盤頁面點擊右上角設置按鈕
2. 進入專門的設置頁面
3. 調整各種顯示選項
4. 設置即時生效
5. 點擊返回按鈕回到星盤頁面

### 2. 即時預覽
- 所有設置變更都會即時保存
- 返回星盤頁面後立即看到變更效果
- 無需額外的保存步驟

### 3. 重置功能
- 提供重置為預設值的功能
- 確認對話框防止誤操作
- 重置後顯示成功提示

## 設計原則

### 1. 一致性
- 使用統一的設計語言
- 圖示和顏色有明確的語義
- 交互方式保持一致

### 2. 可用性
- 清晰的分類和標籤
- 直觀的開關和選項
- 適當的視覺反饋

### 3. 可擴展性
- 模組化的組件設計
- 易於添加新的設置項目
- 支援多種設置類型

## 未來擴展

### 可能的新增功能
1. **字體設置**：調整星盤中文字的大小和字體
2. **線條設置**：調整相位線的粗細和樣式
3. **背景設置**：選擇不同的背景顏色或圖案
4. **匯出設置**：將設置匯出為配置文件
5. **預設模板**：提供多種預設的設置模板

### 技術改進
1. **設置預覽**：在設置頁面中提供小型星盤預覽
2. **設置搜索**：當設置項目增多時提供搜索功能
3. **設置分享**：允許用戶分享自己的設置配置
4. **雲端同步**：將設置同步到雲端

## 總結

新的星盤顯示設置頁面提供了更好的用戶體驗，讓用戶可以更方便地自定義星盤的顯示效果。通過模組化的設計，未來可以輕鬆添加更多的設置選項，滿足不同用戶的需求。
