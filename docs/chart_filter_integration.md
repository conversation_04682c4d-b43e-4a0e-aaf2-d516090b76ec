# 星盤篩選器整合文檔

## 概述

本文檔說明如何在FilesPage中整合ChartFilterPage（星盤篩選器）功能，讓用戶可以從出生資料頁面直接訪問占星應用中的多重篩選器。

## 功能特點

### 1. 入口位置
- 在FilesPage的AppBar中新增了星盤篩選器按鈕
- 按鈕圖標：`Icons.filter_list`
- 按鈕提示：「星盤篩選器」
- 位置：在多選模式按鈕之前，匯入/匯出功能已整合到「更多選項」選單中

### 1.1 UI優化
為了避免AppBar按鈕過多導致的溢出問題，進行了以下優化：
- 保留星盤篩選器和多選模式按鈕作為直接訪問按鈕
- 將匯入和匯出功能整合到PopupMenuButton（更多選項）中
- 減少AppBar中的按鈕數量，提升在小螢幕設備上的用戶體驗

### 2. 功能實現

#### 2.1 `_openChartFilter()` 方法
```dart
void _openChartFilter() async {
  try {
    // 顯示加載對話框
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return const AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('正在計算星盤資料...'),
            ],
          ),
        );
      },
    );

    // 將出生資料轉換為ChartData列表
    final charts = <ChartData>[];

    // 限制處理的數量，避免計算過多導致性能問題
    final maxCharts = 50;
    final birthDataToProcess = _viewModel.birthDataList.length > maxCharts
        ? _viewModel.birthDataList.sublist(0, maxCharts)
        : _viewModel.birthDataList;

    for (final birthData in birthDataToProcess) {
      try {
        // 創建基本ChartData
        final chartData = ChartData(
          chartType: ChartType.natal,
          primaryPerson: birthData,
        );

        // 計算星盤資料（行星位置、宮位、相位等）
        final calculatedChart = await AstrologyService.calculateChartData(
          chartData,
          houseSystem: 'Placidus',
          includeMinorAspects: false,
        );

        charts.add(calculatedChart);
      } catch (e) {
        logger.w('計算星盤資料失敗: ${birthData.name}, 錯誤: $e');
      }
    }

    // 關閉加載對話框
    if (mounted) Navigator.of(context).pop();

    // 導航到星盤篩選器頁面
    final result = await Navigator.push<ChartFilter>(/*...*/);

    // 處理篩選結果
    if (result != null) {
      ScaffoldMessenger.of(context).showSnackBar(/*...*/);
    }
  } catch (e) {
    // 錯誤處理
    if (mounted) Navigator.of(context).pop(); // 確保關閉加載對話框
    ScaffoldMessenger.of(context).showSnackBar(/*...*/);
    logger.e('開啟星盤篩選器時出錯: $e');
  }
}
```

#### 2.2 資料轉換與計算
- 將FilesViewModel中的`birthDataList`轉換為`ChartData`列表
- 每個出生資料都創建為本命盤類型（`ChartType.natal`）
- 使用AstrologyService計算完整的星盤資料（行星位置、宮位、相位等）
- 限制處理的數量（最多50個），避免計算過多導致性能問題
- 顯示加載對話框，提供良好的用戶體驗
- 傳遞計算好的星盤資料給ChartFilterPage作為`initialCharts`參數

#### 2.3 導航處理
- 使用`Navigator.push`導航到ChartFilterPage
- 使用`ChangeNotifierProvider`提供ChartFilterViewModel
- 等待用戶操作結果並顯示相應的反饋訊息

## 技術細節

### 依賴導入
```dart
import '../../viewmodels/chart_filter_viewmodel.dart';
import '../chart_filter_page.dart';
import '../../../features/astrology/models/chart_filter.dart';
```

### UI整合
- 按鈕只在非多選模式下顯示
- 按鈕顏色根據主題模式動態調整（starmaster/starlight）
- 提供工具提示增強用戶體驗
- 使用PopupMenuButton整合匯入/匯出功能，避免AppBar溢出
- 優化按鈕排列，確保在不同螢幕尺寸下的良好顯示效果

### 錯誤處理
- 包含完整的try-catch錯誤處理
- 使用logger記錄錯誤信息
- 向用戶顯示友好的錯誤訊息

## 用戶體驗

### 操作流程
1. 用戶在出生資料頁面點擊篩選器按鈕
2. 系統自動將所有出生資料轉換為星盤資料
3. 導航到星盤篩選器頁面
4. 用戶可以設定各種篩選條件
5. 查看篩選結果並保存篩選器
6. 返回時顯示操作結果反饋

### 功能優勢
- **無縫整合**：直接從出生資料頁面訪問篩選功能
- **自動轉換**：無需手動處理資料格式轉換
- **完整功能**：支援ChartFilterPage的所有篩選功能
- **用戶友好**：提供清晰的操作反饋和錯誤處理

## 未來擴展

### 可能的改進方向
1. **篩選結果應用**：將篩選結果應用到FilesPage的顯示
2. **快速篩選**：在FilesPage中直接顯示常用篩選選項
3. **篩選歷史**：記錄用戶的篩選歷史
4. **批量操作**：對篩選結果進行批量操作

### 性能優化
- 考慮對大量出生資料的處理優化
- 實現懶加載或分頁處理
- 緩存篩選結果以提升性能

## 測試建議

### 功能測試
1. 測試按鈕顯示和隱藏邏輯
2. 測試資料轉換的正確性
3. 測試導航和返回流程
4. 測試錯誤處理機制

### 用戶體驗測試
1. 測試不同數量出生資料的處理
2. 測試主題切換時的UI表現
3. 測試操作反饋的及時性
4. 測試錯誤情況下的用戶體驗

## 相關文件

- `lib/presentation/pages/main/files_page.dart` - 主要實現文件
- `lib/presentation/pages/chart_filter_page.dart` - 篩選器頁面
- `lib/presentation/viewmodels/chart_filter_viewmodel.dart` - 篩選器ViewModel
- `lib/features/astrology/models/chart_filter.dart` - 篩選器模型
