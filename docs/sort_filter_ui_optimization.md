# 排序與篩選 UI 優化實作文件

## 概述
本文件說明將排序與篩選界面從 Tab 式設計改為更優雅的分段式切換設計的實作過程，提供更好的用戶體驗和視覺效果。

## 設計理念

### 為什麼不使用 Tab？
1. **空間限制**：Tab 在小螢幕上佔用較多垂直空間
2. **視覺單調**：標準 Tab 設計缺乏視覺層次感
3. **功能說明不足**：Tab 標籤無法提供功能的詳細說明
4. **交互體驗**：缺乏動畫過渡效果

### 新設計的優勢
1. **卡片式切換器**：更直觀的功能選擇
2. **豐富的視覺反饋**：圖標、標題、副標題三層信息
3. **流暢的動畫**：平滑的切換過渡效果
4. **更好的空間利用**：緊湊而不失美觀

## UI 設計方案

### 1. 分段式切換器設計

#### 視覺結構
```
┌─────────────────────────────────────┐
│  ┌─────────────┐  ┌─────────────┐   │
│  │    📊       │  │    🔍       │   │
│  │   排序      │  │   篩選      │   │
│  │ 調整顯示順序 │  │ 選擇顯示類型 │   │
│  └─────────────┘  └─────────────┘   │
└─────────────────────────────────────┘
```

#### 設計特點
- **雙欄佈局**：左右對稱的按鈕設計
- **三層信息**：圖標 + 主標題 + 副標題
- **動態反饋**：選中狀態有陰影和顏色變化
- **圓角設計**：現代化的視覺風格

### 2. 模式切換器實現

```dart
Widget _buildModeSelector() {
  return Container(
    margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
    padding: const EdgeInsets.all(4),
    decoration: BoxDecoration(
      color: Colors.grey.shade100,
      borderRadius: BorderRadius.circular(12),
      boxShadow: [
        BoxShadow(
          color: Colors.black.withOpacity(0.05),
          blurRadius: 8,
          offset: const Offset(0, 2),
        ),
      ],
    ),
    child: Row(
      children: [
        Expanded(
          child: _buildModeButton(
            mode: 'sort',
            icon: Icons.sort,
            title: '排序',
            subtitle: '調整顯示順序',
          ),
        ),
        const SizedBox(width: 4),
        Expanded(
          child: _buildModeButton(
            mode: 'filter',
            icon: Icons.filter_list,
            title: '篩選',
            subtitle: '選擇顯示類型',
          ),
        ),
      ],
    ),
  );
}
```

### 3. 動畫過渡效果

#### AnimatedSwitcher 實現
```dart
AnimatedSwitcher(
  duration: const Duration(milliseconds: 300),
  transitionBuilder: (Widget child, Animation<double> animation) {
    return SlideTransition(
      position: Tween<Offset>(
        begin: const Offset(0.3, 0),
        end: Offset.zero,
      ).animate(CurvedAnimation(
        parent: animation,
        curve: Curves.easeOutCubic,
      )),
      child: FadeTransition(
        opacity: animation,
        child: child,
      ),
    );
  },
  child: _currentMode == 'sort' 
    ? _buildSortSection()
    : _buildFilterSection(),
)
```

#### 動畫特點
- **滑動 + 淡入**：組合動畫效果
- **緩動曲線**：使用 `Curves.easeOutCubic` 提供自然的動畫感
- **適中時長**：300ms 的動畫時間，既流暢又不拖沓

## 內容區域優化

### 1. 排序區域改進

#### 新增說明卡片
```dart
Container(
  padding: const EdgeInsets.all(16),
  margin: const EdgeInsets.only(bottom: 20),
  decoration: BoxDecoration(
    color: AppColors.royalIndigo.withOpacity(0.05),
    borderRadius: BorderRadius.circular(12),
    border: Border.all(
      color: AppColors.royalIndigo.withOpacity(0.2),
      width: 1,
    ),
  ),
  child: Row(
    children: [
      Icon(Icons.info_outline, color: AppColors.royalIndigo, size: 20),
      const SizedBox(width: 12),
      Expanded(
        child: Text(
          '選擇一種排序方式來調整出生資料的顯示順序',
          style: TextStyle(color: AppColors.royalIndigo, fontSize: 14),
        ),
      ),
    ],
  ),
)
```

#### 改進特點
- **功能說明**：清楚說明排序功能的作用
- **視覺引導**：使用品牌色彩突出重要信息
- **信息層次**：圖標 + 文字的組合設計

### 2. 篩選區域改進

#### 統計信息顯示
```dart
Text(
  '已選擇 ${_selectedCategories.length} / ${ChartCategory.values.length} 個類型',
  style: TextStyle(
    color: AppColors.textMedium,
    fontSize: 14,
  ),
)
```

#### 增強的底部按鈕
```dart
Row(
  children: [
    Expanded(
      child: OutlinedButton.icon(
        icon: const Icon(Icons.select_all, size: 18),
        label: const Text('全選'),
        // 樣式設定...
      ),
    ),
    const SizedBox(width: 12),
    Expanded(
      child: OutlinedButton.icon(
        icon: const Icon(Icons.clear_all, size: 18),
        label: const Text('清除'),
        // 樣式設定...
      ),
    ),
    const SizedBox(width: 12),
    Expanded(
      flex: 2,
      child: ElevatedButton.icon(
        icon: const Icon(Icons.check, size: 18),
        label: const Text('套用篩選'),
        // 樣式設定...
      ),
    ),
  ],
)
```

#### 改進特點
- **實時統計**：顯示當前選擇的類型數量
- **圖標按鈕**：所有按鈕都添加了相應的圖標
- **視覺層次**：不同按鈕使用不同的樣式突出重要性

## 技術實現細節

### 1. 狀態管理
```dart
class _SortSelectorPageState extends State<SortSelectorPage> {
  // 當前顯示模式：'sort' 或 'filter'
  String _currentMode = 'sort';
  
  // 過濾狀態
  late Set<ChartCategory> _selectedCategories;
  bool _hasChanges = false;
}
```

### 2. 模式切換邏輯
```dart
Widget _buildModeButton({
  required String mode,
  required IconData icon,
  required String title,
  required String subtitle,
}) {
  final isSelected = _currentMode == mode;
  
  return AnimatedContainer(
    duration: const Duration(milliseconds: 200),
    curve: Curves.easeOutCubic,
    decoration: BoxDecoration(
      color: isSelected ? AppColors.royalIndigo : Colors.transparent,
      borderRadius: BorderRadius.circular(8),
      boxShadow: isSelected ? [...] : null,
    ),
    child: InkWell(
      onTap: () {
        setState(() {
          _currentMode = mode;
        });
      },
      // UI 構建...
    ),
  );
}
```

### 3. 內容區域鍵值
```dart
// 為 AnimatedSwitcher 提供唯一鍵值
Container(
  key: const ValueKey('sort'),
  child: _buildSortContent(),
)

Container(
  key: const ValueKey('filter'),
  child: _buildFilterContent(),
)
```

## 用戶體驗提升

### 1. 視覺層次清晰
- **主要功能**：排序和篩選功能平等重要
- **次要信息**：副標題提供功能說明
- **狀態反饋**：選中狀態有明顯的視覺變化

### 2. 操作流程優化
- **直觀選擇**：一眼就能看出當前在哪個功能
- **快速切換**：點擊即可切換，無需滑動
- **流暢過渡**：動畫效果提供連續性體驗

### 3. 信息密度適中
- **不擁擠**：合理的間距和佈局
- **不空曠**：充分利用可用空間
- **重點突出**：重要信息使用品牌色彩

## 響應式設計

### 1. 適配不同螢幕
- **小螢幕**：緊湊的佈局，保證可用性
- **大螢幕**：適當的邊距，保持美觀
- **橫屏**：自動調整佈局比例

### 2. 觸控友好
- **按鈕大小**：符合觸控標準的最小尺寸
- **點擊區域**：足夠大的可點擊區域
- **視覺反饋**：點擊時的漣漪效果

## 性能優化

### 1. 動畫性能
- **硬件加速**：使用 Transform 而非 Layout 動畫
- **合理時長**：避免過長的動畫時間
- **流暢度**：60fps 的動畫表現

### 2. 狀態管理
- **最小重建**：只重建必要的 Widget
- **狀態緩存**：保持用戶的選擇狀態
- **記憶體效率**：及時釋放不需要的資源

## 可訪問性

### 1. 語義化標籤
- **按鈕語義**：明確的按鈕功能描述
- **狀態說明**：當前選中狀態的語音提示
- **操作指導**：清楚的操作說明

### 2. 鍵盤導航
- **Tab 順序**：合理的焦點移動順序
- **快捷鍵**：支援鍵盤快捷操作
- **焦點指示**：清晰的焦點視覺提示

## 未來擴展

### 1. 更多切換方式
- **手勢切換**：支援左右滑動切換
- **快捷鍵**：鍵盤快捷鍵支援
- **語音控制**：語音命令切換功能

### 2. 個人化設定
- **預設模式**：記住用戶的偏好模式
- **佈局選項**：提供不同的佈局風格
- **主題適配**：支援深色模式等主題

### 3. 智能建議
- **使用習慣**：根據使用頻率調整界面
- **情境感知**：根據當前情境提供建議
- **學習能力**：機器學習優化用戶體驗

## 總結

這次 UI 優化從 Tab 式設計改為分段式切換器設計，帶來了以下改進：

1. **視覺效果提升**：更現代化和精緻的界面設計
2. **用戶體驗改善**：更直觀的功能選擇和流暢的切換動畫
3. **信息傳達優化**：更清楚的功能說明和狀態反饋
4. **空間利用改善**：更緊湊而不失美觀的佈局設計
5. **交互體驗增強**：豐富的視覺反饋和動畫效果

新的設計不僅在視覺上更加吸引人，在功能性和可用性方面也有顯著提升，為用戶提供了更好的排序和篩選體驗。
