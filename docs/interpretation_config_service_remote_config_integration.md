# 解讀配置服務 Remote Config 整合文件

## 概述

本文件說明 `InterpretationConfigService` 如何整合 Remote Config 功能，實現配置的動態管理。

## 修改內容

### 1. 新增依賴項

```dart
import '../utils/logger_utils.dart';
import 'remote_config_service.dart';
```

### 2. 修改載入邏輯

原本的 `loadConfig` 方法只從 Assets 載入配置，現在改為：

1. **優先從 Remote Config 載入**：嘗試從 Firebase Remote Config 獲取配置
2. **降級使用 Assets 配置**：如果 Remote Config 失敗或為空，則使用本地 Assets 配置
3. **最後使用預設配置**：如果前兩者都失敗，使用硬編碼的預設配置

### 3. 新增方法

#### `_loadConfigFromRemoteConfig(ChartType chartType)`
- 從 Remote Config 載入指定星盤類型的配置
- 使用 `_getRemoteConfigKey()` 獲取對應的 Remote Config 鍵值
- 返回 `InterpretationConfig?`，失敗時返回 `null`

#### `_loadConfigFromAssets(ChartType chartType)`
- 從 Assets 載入指定星盤類型的配置
- 保留原有的載入邏輯
- 返回 `InterpretationConfig?`，失敗時返回 `null`

#### `_getRemoteConfigKey(ChartType chartType)`
- 根據星盤類型返回對應的 Remote Config 鍵值
- 支援所有星盤類型的配置鍵值映射

#### `clearCacheForChartType(ChartType chartType)`
- 清除特定星盤類型的快取
- 用於需要重新載入特定配置時

#### `reloadConfig(ChartType chartType)`
- 強制重新載入指定星盤類型的配置
- 先清除快取，再重新載入

## Remote Config 鍵值映射

使用統一的 `interpretation_config_` 前綴，便於識別和管理：

| 星盤類型 | Remote Config 鍵值 | Assets 檔案路徑 |
|---------|-------------------|----------------|
| 個人星盤 | `interpretation_config_natal` | `assets/config/interpretation_config_natal.json` |
| 推運盤 | `interpretation_config_transit` | `assets/config/interpretation_config_transit.json` |
| 次推運 | `interpretation_config_secondary_progression` | `assets/config/interpretation_config_secondary_progression.json` |
| 三推運 | `interpretation_config_tertiary_progression` | `assets/config/interpretation_config_tertiary_progression.json` |
| 太陽弧推運 | `interpretation_config_solar_arc` | `assets/config/interpretation_config_solar_arc.json` |
| 太陽返照 | `interpretation_config_solar_return` | `assets/config/interpretation_config_solar_return.json` |
| 月亮返照 | `interpretation_config_lunar_return` | `assets/config/interpretation_config_lunar_return.json` |
| 合盤類 | `interpretation_config_relationship` | `assets/config/interpretation_config_relationship.json` |
| 比較盤推運 | `interpretation_config_synastry_progression` | `assets/config/interpretation_config_synastry_progression.json` |
| 組合盤推運 | `interpretation_config_composite_progression` | `assets/config/interpretation_config_composite_progression.json` |
| 時空中點盤推運 | `interpretation_config_davison_progression` | `assets/config/interpretation_config_davison_progression.json` |
| 馬克思盤推運 | `interpretation_config_marks_progression` | `assets/config/interpretation_config_marks_progression.json` |
| 卜卦占星 | `interpretation_config_horary` | `assets/config/interpretation_config_horary.json` |
| 事件占星 | `interpretation_config_event` | `assets/config/interpretation_config_event.json` |
| 世俗占星 | `interpretation_config_mundane` | `assets/config/interpretation_config_mundane.json` |
| 法達星期 | `interpretation_config_firdaria` | `assets/config/interpretation_config_firdaria.json` |
| 小限法 | `interpretation_config_profection` | `assets/config/interpretation_config_profection.json` |
| 季節節氣 | `interpretation_config_equinox_solstice` | `assets/config/interpretation_config_equinox_solstice.json` |
| 日月蝕 | `interpretation_config_eclipse` | `assets/config/interpretation_config_eclipse.json` |
| 木土合相 | `interpretation_config_jupiter_saturn_conjunction` | `assets/config/interpretation_config_jupiter_saturn_conjunction.json` |
| 火土合相 | `interpretation_config_mars_saturn_conjunction` | `assets/config/interpretation_config_mars_saturn_conjunction.json` |

## 使用方式

### 基本使用
```dart
final config = await InterpretationConfigService.instance.loadConfig(ChartType.natal);
```

### 強制重新載入
```dart
final config = await InterpretationConfigService.instance.reloadConfig(ChartType.natal);
```

### 清除特定快取
```dart
InterpretationConfigService.instance.clearCacheForChartType(ChartType.natal);
```

## 日誌記錄

服務會記錄以下資訊：
- 成功從 Remote Config 載入配置
- Remote Config 載入失敗，降級使用 Assets
- Assets 載入失敗
- 使用預設配置

## 錯誤處理

1. **Remote Config 失敗**：自動降級使用 Assets 配置
2. **Assets 載入失敗**：使用預設配置
3. **JSON 解析失敗**：記錄錯誤並嘗試下一個載入方式

## 快取機制

- 配置載入後會快取在記憶體中
- 相同星盤類型的後續請求會直接返回快取結果
- 可使用 `clearCache()` 或 `clearCacheForChartType()` 清除快取
- 可使用 `reloadConfig()` 強制重新載入

## 測試結果

所有測試都已通過，驗證了以下功能：

1. **配置載入邏輯**：優先 Remote Config → 降級 Assets → 最後預設配置
2. **錯誤處理**：當 Remote Config 和 Assets 都失敗時，正確使用預設配置
3. **快取機制**：配置正確快取和清除
4. **星盤類型映射**：所有星盤類型都有對應的 Remote Config 鍵值

## 實際運行效果

在實際應用中：
- 如果 Remote Config 已初始化且有配置，會優先使用 Remote Config
- 如果 Remote Config 未初始化或配置為空，會降級使用 Assets 配置
- 如果 Assets 配置也載入失敗，會使用硬編碼的預設配置
- 所有過程都有詳細的日誌記錄，便於調試

## 注意事項

1. Remote Config 需要先初始化才能使用
2. 如果 Remote Config 未初始化，會直接使用 Assets 配置
3. 配置的 JSON 格式必須符合 `InterpretationConfig` 模型
4. 建議在應用啟動時預載入常用的配置以提升效能
5. 在測試環境中，由於 Remote Config 和 Assets 都無法正常載入，會使用預設配置
