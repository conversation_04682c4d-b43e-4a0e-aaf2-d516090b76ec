# PaymentService Firebase 完全遷移

## 📋 功能概述

將 `PaymentService` 中所有使用 SharedPreferences 的部分完全移除，改為完全基於 Firebase 的支付服務，確保數據的雲端同步和一致性。

## 🎯 遷移內容

### 1. 移除的 SharedPreferences 常數
```dart
// 移除前
static const String _paymentsKey = 'astreal_payments';
static const String _freeTrialKey = 'astreal_free_trial';
static const String _premiumStatusKey = 'astreal_premium_status';
static const String _singlePurchaseKey = 'astreal_single_purchase';

// 移除後
// 完全不再使用本地存儲常數
```

### 2. 移除的 Import
```dart
// 移除前
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';

// 移除後
// 不再需要 SharedPreferences 和 JSON 轉換
```

## 🔧 主要方法重構

### 1. isPremiumUser() 方法
**重構前**：
```dart
static Future<bool> isPremiumUser() async {
  try {
    final prefs = await SharedPreferences.getInstance();
    final premiumStatus = prefs.getBool(_premiumStatusKey) ?? false;
    
    if (premiumStatus) {
      final payments = await getAllPayments();
      // 檢查訂閱是否過期
    }
    
    return false;
  } catch (e) {
    return false;
  }
}
```

**重構後**：
```dart
static Future<bool> isPremiumUser() async {
  try {
    // 檢查用戶是否已登入
    final currentUser = AuthService.getCurrentUser();
    if (currentUser == null) {
      logger.w('用戶未登入，無法檢查付費狀態');
      return false;
    }

    // 從 Firebase 獲取支付記錄
    final payments = await getAllPayments();
    final validSubscriptions = payments.where((payment) =>
      payment.isValid &&
      payment.expiryDate.isAfter(DateTime.now()) &&
      _isSubscriptionPlan(payment.planType)
    ).toList();

    return validSubscriptions.isNotEmpty;
  } catch (e) {
    logger.e('檢查付費狀態時出錯: $e');
    return false;
  }
}
```

### 2. getRemainingFreeTrials() 方法
**重構前**：
```dart
static Future<int> getRemainingFreeTrials() async {
  final currentUser = AuthService.getCurrentUser();

  if (currentUser != null) {
    try {
      final usedTrials = await FirebasePaymentService.getUserFreeTrialUsage(currentUser.uid);
      return usedTrials;
    } catch (e) {
      logger.w('從 Firebase 獲取失敗，使用本地記錄: $e');
      return 0; // 回退到本地存儲
    }
  } else {
    return 0; // 未登入用戶提醒登入
  }
}
```

**重構後**：
```dart
static Future<int> getRemainingFreeTrials() async {
  try {
    final currentUser = AuthService.getCurrentUser();

    if (currentUser != null) {
      // 已登入用戶：從 Firebase 獲取免費試用記錄
      final usedTrials = await FirebasePaymentService.getUserFreeTrialUsage(currentUser.uid);
      return usedTrials;
    } else {
      // 未登入用戶：無法使用免費試用
      logger.w('用戶未登入，無法獲取免費試用次數');
      return 0;
    }
  } catch (e) {
    logger.e('獲取免費試用次數時出錯: $e');
    return 0;
  }
}
```

### 3. useFreeTrialAttempt() 方法
**重構前**：
```dart
static Future<bool> useFreeTrialAttempt() async {
  // 複雜的本地和 Firebase 雙重更新邏輯
  if (currentUser != null) {
    try {
      await FirebasePaymentService.incrementUserFreeTrialUsage(currentUser.uid);
    } catch (e) {
      // Firebase 失敗時回退到本地存儲
      final prefs = await SharedPreferences.getInstance();
      final usedTrials = prefs.getInt(_freeTrialKey) ?? 0;
      await prefs.setInt(_freeTrialKey, usedTrials + 1);
    }
  } else {
    // 未登入用戶使用本地存儲
    final prefs = await SharedPreferences.getInstance();
    // ...
  }
}
```

**重構後**：
```dart
static Future<bool> useFreeTrialAttempt() async {
  try {
    // 檢查用戶是否已登入
    final currentUser = AuthService.getCurrentUser();
    if (currentUser == null) {
      logger.w('用戶未登入，無法使用免費試用');
      return false;
    }

    final remainingTrials = await getRemainingFreeTrials();
    if (remainingTrials <= 0) {
      logger.w('沒有剩餘的免費試用次數');
      return false;
    }

    // 更新 Firebase 記錄
    await FirebasePaymentService.incrementUserFreeTrialUsage(currentUser.uid);
    logger.i('使用免費試用成功，剩餘次數: ${remainingTrials - 1}');

    return true;
  } catch (e) {
    logger.e('使用免費試用時出錯: $e');
    return false;
  }
}
```

### 4. getAllPayments() 方法
**重構前**：
```dart
static Future<List<PaymentRecord>> getAllPayments() async {
  try {
    final prefs = await SharedPreferences.getInstance();
    final paymentsJson = prefs.getStringList(_paymentsKey) ?? [];

    return paymentsJson
        .map((json) => PaymentRecord.fromJson(jsonDecode(json)))
        .toList();
  } catch (e) {
    logger.e('獲取支付記錄時出錯: $e');
    return [];
  }
}
```

**重構後**：
```dart
static Future<List<PaymentRecord>> getAllPayments() async {
  try {
    // 檢查用戶是否已登入
    final currentUser = AuthService.getCurrentUser();
    if (currentUser == null) {
      logger.w('用戶未登入，無法獲取支付記錄');
      return [];
    }

    // 從 Firebase 獲取支付記錄
    return await FirebasePaymentService.getUserPaymentRecords();
  } catch (e) {
    logger.e('獲取支付記錄時出錯: $e');
    return [];
  }
}
```

### 5. addPaymentRecord() 方法
**重構前**：
```dart
static Future<bool> addPaymentRecord(PaymentRecord payment) async {
  // 1. 保存到本地
  final payments = await getAllPayments();
  payments.add(payment);

  final prefs = await SharedPreferences.getInstance();
  final paymentsJson = payments
      .map((payment) => jsonEncode(payment.toJson()))
      .toList();

  await prefs.setStringList(_paymentsKey, paymentsJson);

  // 更新付費狀態
  if (payment.isValid && _isSubscriptionPlan(payment.planType)) {
    await prefs.setBool(_premiumStatusKey, true);
  }

  // 2. 同步到 Firebase（如果用戶已登入）
  final user = AuthService.getCurrentUser();
  if (user != null) {
    await FirebasePaymentService.savePaymentRecord(payment);
  }
}
```

**重構後**：
```dart
static Future<bool> addPaymentRecord(PaymentRecord payment) async {
  try {
    // 檢查用戶是否已登入
    final user = AuthService.getCurrentUser();
    if (user == null) {
      logger.w('用戶未登入，無法添加支付記錄');
      return false;
    }

    // 保存到 Firebase
    final firebaseSuccess = await FirebasePaymentService.savePaymentRecord(payment);
    if (firebaseSuccess) {
      logger.i('支付記錄已保存到 Firebase: ${payment.id}');

      // 更新用戶訂閱狀態到 Firebase
      final subscriptionData = await getSubscriptionSummary();
      await FirebasePaymentService.updateUserSubscriptionStatus(subscriptionData);

      return true;
    } else {
      logger.w('支付記錄保存到 Firebase 失敗');
      return false;
    }
  } catch (e) {
    logger.e('添加支付記錄時出錯: $e');
    return false;
  }
}
```

## 🗑️ 移除的方法

### 1. 同步相關方法
```dart
// 移除的方法
static Future<void> syncFreeTrialToFirebase()
static Future<bool> syncWithFirebase()
```

**原因**：不再需要本地和雲端之間的同步，因為所有數據都直接存儲在 Firebase 中。

### 2. 測試方法重構
```dart
// 重構前
static Future<void> resetFreeTrials() async {
  final prefs = await SharedPreferences.getInstance();
  await prefs.remove(_freeTrialKey);
}

static Future<void> clearAllPaymentData() async {
  final prefs = await SharedPreferences.getInstance();
  await prefs.remove(_paymentsKey);
  await prefs.remove(_freeTrialKey);
  await prefs.remove(_premiumStatusKey);
  await prefs.remove(_singlePurchaseKey);
}

// 重構後
static Future<void> resetFreeTrials() async {
  final currentUser = AuthService.getCurrentUser();
  if (currentUser != null) {
    await FirebasePaymentService.setUserFreeTrialUsage(currentUser.uid, 0);
  }
}

static Future<void> clearAllPaymentData() async {
  final currentUser = AuthService.getCurrentUser();
  if (currentUser != null) {
    // 重置免費試用次數
    await FirebasePaymentService.setUserFreeTrialUsage(currentUser.uid, 0);
    // 注意：支付記錄通常不應該被刪除，以保持交易歷史
  }
}
```

## 🔒 安全性提升

### 1. 用戶驗證
所有方法現在都會檢查用戶登入狀態：
```dart
final currentUser = AuthService.getCurrentUser();
if (currentUser == null) {
  logger.w('用戶未登入，無法執行操作');
  return false; // 或適當的默認值
}
```

### 2. 數據一致性
- **單一數據源**：所有數據都存儲在 Firebase 中
- **即時同步**：不再有本地和雲端數據不一致的問題
- **跨設備同步**：用戶在不同設備上登入時數據自動同步

## 📱 用戶體驗改善

### 1. 登入要求
- **明確提示**：未登入用戶會收到明確的提示訊息
- **數據保護**：確保用戶數據不會丟失
- **跨設備一致**：登入後在任何設備上都能看到相同的數據

### 2. 錯誤處理
- **統一錯誤處理**：所有方法都有完整的錯誤處理
- **詳細日誌**：提供詳細的日誌信息便於調試
- **優雅降級**：錯誤情況下返回安全的默認值

## 🚀 實作效果

遷移後的效果：
- ✅ **完全雲端化**：所有支付相關數據都存儲在 Firebase 中
- ✅ **數據一致性**：消除了本地和雲端數據不一致的問題
- ✅ **安全性提升**：所有操作都需要用戶登入驗證
- ✅ **代碼簡化**：移除了複雜的同步邏輯
- ✅ **跨設備同步**：用戶數據在所有設備上保持同步

這次遷移成功將 PaymentService 轉換為完全基於 Firebase 的服務，提供了更好的數據一致性、安全性和用戶體驗。
