# Firebase Payment Service Firestore 實現說明

## 概述

完成了 `FirebasePaymentService` 中所有 SDK 方法的 Firestore 實現，將原本的佔位符方法替換為完整的 Firebase Firestore 操作，支援免費試用追蹤、支付記錄管理和訂閱狀態更新。

## 實現的方法

### 1. 免費試用管理

#### `_getUserFreeTrialUsageViaSDK`
```dart
static Future<int> _getUserFreeTrialUsageViaSDK(String userId) async {
  final firestore = FirebaseFirestore.instance;
  
  // 獲取用戶的免費試用使用記錄
  final docRef = firestore.collection('user_free_trial').doc(userId);
  final docSnapshot = await docRef.get();
  
  if (docSnapshot.exists) {
    final data = docSnapshot.data() as Map<String, dynamic>;
    return data['used_count'] as int? ?? 0;
  } else {
    // 創建初始記錄
    await docRef.set({
      'used_count': 0,
      'created_at': FieldValue.serverTimestamp(),
      'updated_at': FieldValue.serverTimestamp(),
    });
    return 0;
  }
}
```

**功能特色**：
- 自動創建初始記錄
- 使用伺服器時間戳
- 完整的錯誤處理

#### `_setUserFreeTrialUsageViaSDK`
```dart
static Future<bool> _setUserFreeTrialUsageViaSDK(String userId, int usedCount) async {
  final firestore = FirebaseFirestore.instance;
  
  await firestore.collection('user_free_trial').doc(userId).set({
    'used_count': usedCount,
    'updated_at': FieldValue.serverTimestamp(),
  }, SetOptions(merge: true));
  
  return true;
}
```

**功能特色**：
- 使用 merge 選項保留其他欄位
- 自動更新時間戳
- 原子性操作

### 2. 支付記錄管理

#### `_savePaymentRecordViaSDK`
```dart
static Future<bool> _savePaymentRecordViaSDK(String userId, PaymentRecord payment) async {
  final firestore = FirebaseFirestore.instance;
  
  final data = payment.toJson();
  data['user_id'] = userId;
  data['created_at'] = FieldValue.serverTimestamp();
  data['updated_at'] = FieldValue.serverTimestamp();
  
  // 保存到主要的 payments 集合
  final docRef = await firestore.collection(_collectionName).add(data);
  
  // 同時保存到用戶專屬的支付記錄集合
  await firestore
      .collection(_userPaymentsCollection)
      .doc(userId)
      .collection('payments')
      .doc(docRef.id)
      .set(data);
  
  return true;
}
```

**功能特色**：
- 雙重儲存（全域 + 用戶專屬）
- 自動添加用戶 ID 和時間戳
- 保持數據一致性

#### `_getUserPaymentRecordsViaSDK`
```dart
static Future<List<PaymentRecord>> _getUserPaymentRecordsViaSDK(String userId) async {
  final firestore = FirebaseFirestore.instance;
  
  final querySnapshot = await firestore
      .collection(_userPaymentsCollection)
      .doc(userId)
      .collection('payments')
      .orderBy('created_at', descending: true)
      .get();
  
  final records = <PaymentRecord>[];
  for (final doc in querySnapshot.docs) {
    try {
      final data = doc.data();
      data['id'] = doc.id;
      final record = PaymentRecord.fromJson(data);
      records.add(record);
    } catch (e) {
      logger.w('解析支付記錄失敗，文檔 ID: ${doc.id}，錯誤: $e');
    }
  }
  
  return records;
}
```

**功能特色**：
- 按時間倒序排列
- 個別記錄解析錯誤處理
- 自動添加文檔 ID

#### `_recordPaymentViaSDK`
```dart
static Future<bool> _recordPaymentViaSDK(dynamic paymentRecord) async {
  final firestore = FirebaseFirestore.instance;
  
  // 處理不同類型的 paymentRecord
  Map<String, dynamic> data;
  if (paymentRecord is PaymentRecord) {
    data = paymentRecord.toJson();
  } else if (paymentRecord is Map<String, dynamic>) {
    data = paymentRecord;
  } else {
    return false;
  }
  
  data['created_at'] = FieldValue.serverTimestamp();
  data['updated_at'] = FieldValue.serverTimestamp();
  
  // 記錄到 payments 集合
  final docRef = await firestore.collection(_collectionName).add(data);
  
  // 同時記錄到用戶專屬集合
  final userId = data['user_id'] as String?;
  if (userId != null && userId.isNotEmpty) {
    await firestore
        .collection(_userPaymentsCollection)
        .doc(userId)
        .collection('payments')
        .doc(docRef.id)
        .set(data);
  }
  
  return true;
}
```

**功能特色**：
- 支援多種輸入類型
- 條件性用戶專屬記錄
- 類型安全檢查

### 3. 訂閱狀態管理

#### `_updateSubscriptionStatusViaSDK`
```dart
static Future<bool> _updateSubscriptionStatusViaSDK(String userId, Map<String, dynamic> data) async {
  final firestore = FirebaseFirestore.instance;
  
  final updateData = Map<String, dynamic>.from(data);
  updateData['updated_at'] = FieldValue.serverTimestamp();
  
  await firestore
      .collection(_subscriptionsCollection)
      .doc(userId)
      .set(updateData, SetOptions(merge: true));
  
  return true;
}
```

**功能特色**：
- 使用 merge 選項
- 自動更新時間戳
- 保留現有數據

## Firestore 數據結構

### 1. 免費試用集合 (`user_free_trial`)
```
user_free_trial/
├── {userId}/
    ├── used_count: int
    ├── created_at: Timestamp
    └── updated_at: Timestamp
```

### 2. 支付記錄集合 (`payments`)
```
payments/
├── {documentId}/
    ├── user_id: string
    ├── amount: number
    ├── currency: string
    ├── product_id: string
    ├── transaction_id: string
    ├── platform: string
    ├── status: string
    ├── created_at: Timestamp
    └── updated_at: Timestamp
```

### 3. 用戶支付記錄集合 (`user_payments`)
```
user_payments/
├── {userId}/
    └── payments/
        ├── {documentId}/
            ├── (same structure as payments collection)
```

### 4. 訂閱狀態集合 (`subscriptions`)
```
subscriptions/
├── {userId}/
    ├── status: string
    ├── plan_id: string
    ├── expires_at: Timestamp
    ├── created_at: Timestamp
    └── updated_at: Timestamp
```

## 安全規則建議

### Firestore Security Rules
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // 免費試用記錄 - 只有用戶本人可以讀寫
    match /user_free_trial/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // 支付記錄 - 管理員可讀寫，用戶只能讀取
    match /payments/{document} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && 
        (request.auth.token.admin == true || 
         resource.data.user_id == request.auth.uid);
    }
    
    // 用戶支付記錄 - 只有用戶本人可以讀取
    match /user_payments/{userId}/payments/{document} {
      allow read: if request.auth != null && request.auth.uid == userId;
      allow write: if request.auth != null && 
        (request.auth.token.admin == true || request.auth.uid == userId);
    }
    
    // 訂閱狀態 - 只有用戶本人可以讀取
    match /subscriptions/{userId} {
      allow read: if request.auth != null && request.auth.uid == userId;
      allow write: if request.auth != null && request.auth.token.admin == true;
    }
  }
}
```

## 錯誤處理策略

### 1. 網路錯誤
- 自動重試機制
- 離線快取支援
- 優雅降級

### 2. 數據錯誤
- 個別記錄錯誤不影響整體
- 詳細的錯誤日誌
- 數據驗證

### 3. 權限錯誤
- 清晰的錯誤訊息
- 適當的後備方案
- 用戶友好的提示

## 性能優化

### 1. 查詢優化
- 使用複合索引
- 限制查詢結果數量
- 分頁載入

### 2. 快取策略
- 本地快取常用數據
- 適當的快取過期時間
- 智能快取更新

### 3. 批次操作
- 使用 batch writes
- 減少網路請求
- 原子性操作

## 監控和分析

### 1. 使用統計
- 免費試用使用率
- 支付成功率
- 訂閱轉換率

### 2. 錯誤監控
- 操作失敗率
- 錯誤類型分析
- 性能指標

### 3. 成本控制
- 讀寫操作統計
- 儲存空間使用
- 網路流量監控

這個完整的 Firestore 實現為 Firebase Payment Service 提供了強大的雲端數據管理能力，支援免費試用追蹤、支付記錄管理和訂閱狀態更新，同時保證了數據安全性和系統性能。
