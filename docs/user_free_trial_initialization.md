# 用戶免費試用記錄初始化功能說明

## 概述

實現了在用戶註冊登入時自動初始化 `user_free_trial` 記錄的功能，確保每個新用戶都有預設的免費試用次數記錄（初始值為 0），避免後續查詢時出現空值或錯誤。

## 功能特色

### 1. 自動初始化
- 用戶註冊時自動創建免費試用記錄
- 用戶首次登入時檢查並初始化記錄
- 預設免費試用次數為 0

### 2. 防重複初始化
- 檢查現有記錄，避免重複創建
- 保護已有的使用記錄
- 智能判斷是否需要初始化

### 3. 多平台支援
- 支援 REST API 和 Firebase SDK 兩種註冊方式
- 統一的初始化邏輯
- 跨平台一致性

## 技術實現

### 1. PaymentService 新增方法

#### `initializeUserFreeTrialRecord`
```dart
/// 初始化新用戶的免費試用記錄（用戶註冊時調用）
static Future<void> initializeUserFreeTrialRecord(String userId) async {
  try {
    logger.i('初始化用戶 $userId 的免費試用記錄');
    
    // 檢查 Firebase 中是否已存在記錄
    final existingUsage = await FirebasePaymentService.getUserFreeTrialUsage(userId);
    if (existingUsage >= 0) {
      // 記錄已存在，不需要重複初始化
      logger.i('用戶 $userId 的免費試用記錄已存在，使用次數: $existingUsage');
      return;
    }
    
    // 設置初始值為 0
    final success = await FirebasePaymentService.setUserFreeTrialUsage(userId, 0);
    
    if (success) {
      logger.i('成功初始化用戶 $userId 的免費試用記錄為 0');
    } else {
      logger.e('初始化用戶 $userId 的免費試用記錄失敗');
    }
  } catch (e) {
    logger.e('初始化用戶免費試用記錄失敗: $e');
  }
}
```

**功能特色**：
- 檢查現有記錄避免重複初始化
- 設置初始值為 0
- 完整的錯誤處理和日誌記錄

### 2. AuthService 修改

#### `_syncUserDataAfterSignIn` 方法增強
```dart
/// 登入成功後同步用戶資料
static Future<void> _syncUserDataAfterSignIn() async {
  try {
    final currentUser = getCurrentUser();
    if (currentUser != null) {
      // 初始化用戶免費試用記錄（如果是新用戶）
      await PaymentService.initializeUserFreeTrialRecord(currentUser.uid);
      
      // 同步免費試用記錄到 Firebase
      await PaymentService.syncFreeTrialToFirebase();
    }
    
    logger.i('用戶登入後資料同步完成');
  } catch (e) {
    logger.e('用戶登入後資料同步失敗: $e');
  }
}
```

**功能特色**：
- 登入時自動初始化免費試用記錄
- 同步本地和雲端記錄
- 確保數據一致性

### 3. FirebaseAuthService 修改

#### 註冊方法增強
```dart
// REST API 註冊
await _saveUserSession(user, data['idToken'], data['refreshToken']);

if (displayName != null) {
  await _updateUserProfileViaRestApi(displayName, null);
}

// 初始化新用戶的免費試用記錄
await _initializeNewUserData(user.uid);

logger.i('REST API 用戶註冊成功: ${user.uid}');
return user;
```

```dart
// Firebase SDK 註冊
await _saveUserSessionSDK(user);

// 初始化新用戶的免費試用記錄
await _initializeNewUserData(user.uid);

logger.i('Firebase SDK 用戶註冊成功: ${user.uid}');
return user;
```

#### 新增輔助方法
```dart
/// 初始化新用戶的數據
static Future<void> _initializeNewUserData(String userId) async {
  try {
    // 初始化免費試用記錄
    await PaymentService.initializeUserFreeTrialRecord(userId);
    logger.i('新用戶 $userId 數據初始化完成');
  } catch (e) {
    logger.e('初始化新用戶數據失敗: $e');
    // 不拋出異常，避免影響註冊流程
  }
}
```

**功能特色**：
- 註冊成功後立即初始化
- 不影響註冊流程的穩定性
- 統一的初始化邏輯

## 數據流程

### 1. 用戶註冊流程
```
用戶提交註冊信息
    ↓
Firebase 創建用戶帳號
    ↓
保存用戶會話信息
    ↓
初始化用戶數據 (_initializeNewUserData)
    ↓
初始化免費試用記錄 (initializeUserFreeTrialRecord)
    ↓
檢查是否已有記錄
    ↓ 沒有
設置免費試用次數為 0
    ↓
註冊完成
```

### 2. 用戶登入流程
```
用戶登入成功
    ↓
觸發認證狀態變化
    ↓
調用 _syncUserDataAfterSignIn
    ↓
初始化免費試用記錄（如果是新用戶）
    ↓
同步本地和雲端記錄
    ↓
登入流程完成
```

### 3. 記錄檢查邏輯
```
調用 getUserFreeTrialUsage(userId)
    ↓
記錄存在？
    ↓ 是
返回現有使用次數
    ↓ 否
調用 setUserFreeTrialUsage(userId, 0)
    ↓
創建初始記錄
    ↓
返回 0
```

## 錯誤處理

### 1. 初始化失敗
- 記錄詳細錯誤日誌
- 不影響註冊/登入流程
- 後續登入時重試初始化

### 2. 網路錯誤
- 使用本地記錄作為後備
- 下次連線時重新同步
- 優雅降級處理

### 3. 數據衝突
- 檢查現有記錄避免覆蓋
- 使用較大值保護用戶權益
- 記錄衝突解決過程

## 測試建議

### 1. 功能測試
```dart
// 測試新用戶註冊
test('新用戶註冊應該初始化免費試用記錄', () async {
  final userId = 'test_user_123';
  
  // 模擬註冊流程
  await PaymentService.initializeUserFreeTrialRecord(userId);
  
  // 驗證記錄已創建
  final usage = await FirebasePaymentService.getUserFreeTrialUsage(userId);
  expect(usage, equals(0));
});

// 測試重複初始化
test('重複初始化不應該覆蓋現有記錄', () async {
  final userId = 'test_user_456';
  
  // 設置初始記錄
  await FirebasePaymentService.setUserFreeTrialUsage(userId, 3);
  
  // 嘗試重新初始化
  await PaymentService.initializeUserFreeTrialRecord(userId);
  
  // 驗證記錄未被覆蓋
  final usage = await FirebasePaymentService.getUserFreeTrialUsage(userId);
  expect(usage, equals(3));
});
```

### 2. 集成測試
- 完整的註冊流程測試
- 登入後數據同步測試
- 多平台一致性測試

### 3. 邊界情況測試
- 網路斷開時的註冊
- 並發註冊的處理
- 異常中斷的恢復

## 監控指標

### 1. 初始化成功率
- 註冊時初始化成功率
- 登入時初始化成功率
- 錯誤類型分析

### 2. 數據一致性
- 本地與雲端記錄一致性
- 重複初始化檢測
- 數據衝突解決統計

### 3. 性能指標
- 初始化操作耗時
- 網路請求成功率
- 用戶體驗影響

## 維護建議

### 1. 定期檢查
- 檢查未初始化的用戶
- 修復數據不一致問題
- 優化初始化邏輯

### 2. 版本升級
- 為現有用戶補充初始化
- 遷移舊版本數據
- 保持向後兼容

### 3. 性能優化
- 批量初始化操作
- 快取常用查詢
- 減少網路請求

這個功能確保了每個用戶都有完整的免費試用記錄，為後續的付費驗證和使用統計提供了可靠的數據基礎。
