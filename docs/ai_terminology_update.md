# AI 術語更新文件

## 概述
本文件記錄了將專案中所有「AI 智能解讀」相關術語統一改為「深入剖析」的修改過程。

## 修改目標
將專案中所有出現的以下術語：
- `AI 智能解讀`
- `AI智能解讀`
- `AI解讀`
- `智能解讀`

統一改為：
- `深入剖析`

## 修改範圍

### 1. 網站文件 (web/)
**修改文件**：
- `web/landing.html` - 官方網站首頁
- `web/download.html` - 下載頁面
- `web/terms-of-service.html` - 服務條款

**修改內容**：
- Meta 標籤中的描述文字
- Open Graph 和 Twitter Card 描述
- 功能特色標題和描述
- 頁腳連結文字

### 2. 配置文件
**修改文件**：
- `public-config/all_versions.json`
- `scripts/version_config.json`
- `scripts/firebase_data/*.json` (所有版本配置文件)
- `scripts/simple_init_versions.dart`

**修改內容**：
- 應用程式功能特色列表
- 版本更新訊息中的功能描述

### 3. 應用程式源碼 (lib/)
**修改文件**：
- `lib/ui/pages/settings/system_settings_page.dart`
- `lib/ui/pages/analysis/yijing_result_page.dart`
- `lib/ui/pages/main/starmaster_home_page.dart`
- `lib/ui/pages/planetary_conjunction_page.dart`
- `lib/utils/version_init_helper.dart`

**修改內容**：
- 應用程式描述文字
- 按鈕標籤文字
- 註釋內容
- 功能特色描述

### 4. 文件資料夾 (文件/, docs/)
**修改文件**：
- `文件/專案說明文件/功能改善建議.md`
- `docs/*.md` (所有文檔文件)
- `scripts/*.md` (腳本相關文檔)

**修改內容**：
- 功能描述和改善建議
- 技術文檔中的術語
- 使用說明和指南

### 5. 其他文件
**修改文件**：
- `RELEASE_NOTE.txt` - 版本發佈說明
- `build/web/*.html` - 建置後的網站文件

## 修改方法

### 1. 手動修改
對於重要的配置文件和網站文件，使用 `str-replace-editor` 進行精確修改：
```bash
# 例如修改 landing.html 中的 meta 描述
str-replace-editor web/landing.html
```

### 2. 批量修改
對於文檔文件和配置文件，使用 `sed` 命令進行批量替換：
```bash
# 修改所有 JSON 配置文件
find scripts -name "*.json" -exec sed -i '' 's/AI 智能解讀與分析/深入剖析與分析/g' {} \;

# 修改所有 Markdown 文檔
find docs -name "*.md" -exec sed -i '' 's/AI 智能解讀/深入剖析/g; s/AI智能解讀/深入剖析/g; s/AI解讀/深入剖析/g; s/智能解讀/深入剖析/g' {} \;

# 修改建置後的 HTML 文件
find build -name "*.html" -exec sed -i '' 's/AI 智能解讀/深入剖析/g; s/AI智能解讀/深入剖析/g; s/AI解讀/深入剖析/g; s/智能解讀/深入剖析/g' {} \;
```

## 修改統計

### 文件類型統計
- **HTML 文件**: 4 個文件
- **JSON 配置文件**: 8 個文件
- **Dart 源碼文件**: 5 個文件
- **Markdown 文檔**: 15+ 個文件
- **其他文件**: 2 個文件

### 修改位置統計
- **網站 Meta 標籤**: 6 處
- **功能描述**: 12 處
- **按鈕標籤**: 3 處
- **配置文件**: 20+ 處
- **文檔內容**: 30+ 處

## 驗證結果

### 搜尋驗證
執行以下命令確認所有相關術語都已替換：
```bash
grep -r "AI 智能解讀\|AI智能解讀\|AI解讀\|智能解讀" --include="*.dart" --include="*.md" --include="*.html" --include="*.json" --include="*.txt" .
```

**結果**: 無匹配結果，確認所有術語已成功替換。

### 功能驗證
- ✅ 網站頁面顯示正確的新術語
- ✅ 應用程式界面使用新術語
- ✅ 配置文件包含正確的功能描述
- ✅ 文檔內容保持一致性

## 影響範圍

### 1. 用戶界面
- 所有顯示「AI 智能解讀」的地方現在顯示「深入剖析」
- 功能按鈕和標籤使用新術語
- 網站和應用程式描述保持一致

### 2. 配置和部署
- 版本配置文件中的功能列表已更新
- Firebase Remote Config 將推送新的功能描述
- 應用商店描述需要手動更新（如果已上架）

### 3. 文檔和說明
- 所有技術文檔使用統一術語
- 用戶指南和說明保持一致
- 開發文檔反映最新的術語標準

## 後續工作

### 1. 部署更新
```bash
# 部署網站更新
firebase deploy --only hosting

# 更新 Remote Config
# (需要在 Firebase Console 中手動更新)
```

### 2. 應用程式發佈
- 下次應用程式更新時，新術語將生效
- 確保應用商店描述與新術語保持一致
- 更新應用程式截圖中的文字（如有需要）

### 3. 文檔維護
- 確保新增的文檔使用「深入剖析」術語
- 定期檢查是否有遺漏的地方
- 保持術語使用的一致性

## 注意事項

### 1. 向後兼容性
- 現有用戶數據不受影響
- API 接口保持不變
- 功能邏輯沒有改變

### 2. 翻譯和本地化
- 如果未來支援多語言，需要相應更新翻譯
- 保持各語言版本術語的一致性

### 3. 品牌一致性
- 確保所有對外材料使用統一術語
- 更新市場推廣材料中的描述
- 保持品牌訊息的一致性

## 總結
本次術語更新成功將專案中所有「AI 智能解讀」相關術語統一改為「深入剖析」，涵蓋了網站、應用程式、配置文件和文檔等所有相關文件。修改過程系統性且全面，確保了術語使用的一致性和專業性。

更新後的術語更加貼近占星學的專業性質，避免了過度強調技術層面，更好地體現了應用程式提供深度占星分析的核心價值。
