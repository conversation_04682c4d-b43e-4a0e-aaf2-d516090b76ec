# 名人資料管理系統

## 🎯 系統概述

完整的名人資料管理系統，提供管理員對名人解讀範例的完整 CRUD 操作，並支援與 Firebase RemoteConfig 的雙向同步。

## ✨ 主要功能

### 1. 名人資料管理
- **新增名人資料**：完整的表單驗證和資料輸入
- **編輯名人資料**：修改現有名人的所有資訊
- **刪除名人資料**：安全的刪除確認機制
- **搜尋和篩選**：支援姓名、描述搜尋和類別篩選

### 2. 出生資料管理
- **整合出生資料編輯器**：重用現有的 BirthDataFormPage
- **完整的出生資訊**：日期時間、地點、經緯度、備註
- **資料驗證**：確保出生資料的完整性和正確性

### 3. 分類系統
- **多種名人類別**：政治人物、娛樂明星、學者專家等
- **視覺化類別選擇**：圖示和顏色區分
- **熱門標記**：支援設定熱門名人資料

### 4. RemoteConfig 同步
- **雙向同步**：支援從 RemoteConfig 導入和同步到 RemoteConfig
- **版本控制**：記錄同步時間和操作者
- **衝突處理**：智能處理重複資料

## 📁 檔案結構

### 核心服務
```
lib/data/services/api/celebrity_management_service.dart
```
- 提供完整的 CRUD 操作
- RemoteConfig 同步功能
- 資料格式轉換
- 操作日誌記錄

### 管理頁面
```
lib/presentation/pages/admin/celebrity_management_page.dart
```
- 名人資料列表顯示
- 搜尋和篩選功能
- 統計資訊展示
- 批量操作支援

### 編輯頁面
```
lib/presentation/pages/admin/celebrity_edit_page.dart
```
- 完整的表單驗證
- 出生資料整合編輯
- 分類和設定管理
- 圖片 URL 設定

## 🔧 技術實現

### 資料模型
```dart
class CelebrityWithId {
  final String id;              // Firestore 文檔 ID
  final CelebrityExample celebrity;  // 名人資料
}
```

### Firestore 集合結構
```
celebrity_management/
├── {documentId}/
│   ├── name: string
│   ├── birthData: object
│   ├── description: string
│   ├── category: string
│   ├── recommendedTopics: array
│   ├── isPopular: boolean
│   ├── imageUrl: string?
│   ├── createdAt: timestamp
│   ├── createdBy: string
│   ├── updatedAt: timestamp
│   └── updatedBy: string
```

### RemoteConfig 格式
```json
{
  "examples": [
    {
      "name": "名人姓名",
      "birthData": { ... },
      "description": "特點描述",
      "category": "類別",
      "recommendedTopics": ["主題1", "主題2"],
      "isPopular": true,
      "imageUrl": "圖片URL"
    }
  ],
  "lastUpdated": "2025-01-19T...",
  "updatedBy": "管理員UID",
  "totalCount": 10
}
```

## 🚀 使用流程

### 管理員訪問
1. 管理員登入應用
2. 進入「設定」→「管理者選項」→「管理後台」
3. 點擊「名人資料管理」卡片

### 新增名人資料
1. 點擊右上角「+」按鈕
2. 填寫基本資訊（姓名、描述）
3. 設定出生資料（日期、時間、地點）
4. 選擇類別和設定
5. 輸入推薦主題
6. 設定圖片 URL（選填）
7. 點擊「儲存」

### 編輯名人資料
1. 在列表中找到要編輯的名人
2. 點擊右側選單按鈕
3. 選擇「編輯」
4. 修改相關資訊
5. 點擊「儲存」

### 同步到 RemoteConfig
1. 在名人資料管理頁面
2. 點擊右上角選單按鈕
3. 選擇「同步到 RemoteConfig」
4. 系統會準備同步資料到 Firestore
5. 需要技術人員完成最終的 RemoteConfig 更新

### 從 RemoteConfig 導入
1. 在名人資料管理頁面
2. 點擊右上角選單按鈕
3. 選擇「從 RemoteConfig 導入」
4. 系統會自動導入新的名人資料
5. 重複的資料會被跳過

## 🔒 權限控制

### 管理員驗證
- 只有管理員可以訪問名人資料管理功能
- 使用 AdminService 進行權限檢查
- 所有操作都會記錄操作日誌

### 操作日誌
```dart
await AdminService.logAdminAction(
  adminUid: adminUid,
  action: 'add_celebrity',
  targetId: celebrityId,
  details: {
    'celebrity_name': celebrity.name,
    'category': celebrity.category.name,
  },
);
```

## 📊 統計功能

### 即時統計
- **總計**：所有名人資料數量
- **顯示**：當前篩選後的數量
- **熱門**：標記為熱門的數量

### 搜尋和篩選
- **文字搜尋**：姓名和描述
- **類別篩選**：按名人類別篩選
- **即時更新**：搜尋結果即時顯示

## 🎨 UI 設計

### 設計原則
- 使用統一的卡片組件系統
- 符合應用主題色彩規範
- 響應式布局設計
- 直觀的操作流程

### 主題色彩
- **主色調**：AppColors.royalIndigo
- **成功色**：AppColors.successGreen
- **警告色**：AppColors.error
- **強調色**：AppColors.solarAmber

### 組件使用
- **UnifiedCard**：統一的卡片容器
- **UnifiedFeatureCard**：功能卡片
- **FilterChip**：類別篩選晶片
- **PopupMenuButton**：操作選單

## 🔄 同步機制

### 資料流向
```
Firestore ←→ 管理系統 ←→ RemoteConfig
```

### 同步策略
1. **管理系統 → Firestore**：即時同步
2. **Firestore → RemoteConfig**：手動觸發
3. **RemoteConfig → 管理系統**：手動導入

### 衝突處理
- 導入時檢查重複資料（根據姓名）
- 重複資料會被跳過
- 提供操作結果回饋

## 🚧 未來擴展

### 計劃功能
1. **批量操作**：批量刪除、批量編輯
2. **資料匯出**：CSV、JSON 格式匯出
3. **圖片上傳**：直接上傳圖片到 Firebase Storage
4. **版本歷史**：追蹤資料變更歷史
5. **審核機制**：多級審核流程

### 技術改進
1. **自動同步**：使用 Firebase Functions 自動同步
2. **即時更新**：使用 Firestore 即時監聽
3. **離線支援**：支援離線編輯和同步
4. **效能優化**：分頁載入、虛擬滾動

## 📝 開發注意事項

### TODO 項目
- [ ] 從認證服務獲取當前管理員 UID
- [ ] 實現 RemoteConfig 的直接更新（需要 Admin SDK）
- [ ] 添加圖片上傳功能
- [ ] 實現批量操作
- [ ] 添加資料驗證規則

### 已知限制
1. 客戶端無法直接更新 RemoteConfig
2. 需要手動觸發最終同步
3. 圖片只支援 URL，不支援直接上傳

---

**這個系統提供了完整的名人資料管理功能，讓管理員可以輕鬆管理和維護名人解讀範例！** 🌟
