# 推運解讀配置文件說明

## 概述

本文檔說明新創建的推運解讀配置文件，這些配置文件專門為不同類型的關係推運分析提供詳細的解讀選項。

## 新增配置文件

### 1. 合盤推運解讀配置
**文件**: `synastry_progression_interpretation_options.json`
**用途**: 分析一方的推運行星對另一方本命盤的影響

#### 主要解讀選項：
- **推運關係總覽**: 整體影響分析
- **情感演變**: 情感連結的變化
- **溝通動態**: 溝通模式的演變
- **關係成長**: 成長機會分析
- **挑戰與轉化**: 困難和轉化過程
- **時機指導**: 重要時機節點
- **未來潛力**: 長期發展指導

### 2. 組合盤推運解讀配置
**文件**: `composite_progression_interpretation_options.json`
**用途**: 分析組合盤推運，關係作為獨立實體的演變

#### 主要解讀選項：
- **關係實體演變**: 整體發展趨勢
- **關係目的**: 使命和目標的演變
- **情感氛圍**: 共同情感的變化
- **共同價值觀**: 價值觀的演變
- **共同行動**: 行動力的變化
- **關係結構**: 穩定性的發展
- **轉化週期**: 深層轉化過程
- **時機顯化**: 重要節點分析

### 3. 戴維森盤推運解讀配置
**文件**: `davison_progression_interpretation_options.json`
**用途**: 分析時空中點盤的推運，關係的宇宙意義

#### 主要解讀選項：
- **時空演變**: 宇宙背景的變化
- **業力發展**: 業力課題的演變
- **命運軌道**: 共同命運的發展
- **靈魂連結**: 靈魂層面的連結
- **宇宙時機**: 宇宙節律的影響
- **靈性成長**: 靈性發展分析
- **轉化門戶**: 意識躍升機會
- **永恆連結**: 超越性的分析

### 4. 馬克思盤推運解讀配置
**文件**: `marks_progression_interpretation_options.json`
**用途**: 分析主觀感受的演變，內在關係體驗

#### 主要解讀選項：
- **主觀感受演變**: 內在體驗的變化
- **內在關係**: 心理映像的演變
- **情感投射**: 投射模式的變化
- **認知轉變**: 對伴侶認知的轉變
- **心理成長**: 關係中的心理發展
- **陰影整合**: 陰影面的整合
- **潛意識模式**: 深層模式的演變
- **內在療癒**: 療癒過程分析

## 配置特色

### 1. 專業性與深度
- 每個配置都針對特定的推運類型
- 提供深入的心理和靈性層面分析
- 包含具體的關鍵點指導

### 2. 顏色和圖標系統
- 使用專門的顏色映射
- 選擇符合主題的圖標
- 視覺上區分不同的解讀類型

### 3. 問題設計
- 針對性的問題設計
- 引導用戶深入思考
- 涵蓋不同層面的關係體驗

## 技術實現

### 1. 文件結構
```json
{
  "version": "1.0.0",
  "lastUpdated": "2025-06-25",
  "description": "配置描述",
  "chartType": "配置類型",
  "options": [...],
  "colorMapping": {...},
  "iconMapping": {...}
}
```

### 2. 解讀選項結構
```json
{
  "id": "唯一標識",
  "title": "標題",
  "subtitle": "副標題",
  "icon": "圖標名稱",
  "color": "顏色名稱",
  "keyPoint": "關鍵點指導",
  "questions": ["問題列表"],
  "enabled": true,
  "order": 排序
}
```

### 3. 服務整合
這些配置文件已經整合到 `InterpretationConfigService` 中：

```dart
// 比較盤推運
case ChartType.synastrySecondary:
case ChartType.synastryTertiary:
  return 'assets/config/synastry_progression_interpretation_options.json';

// 組合盤推運
case ChartType.compositeSecondary:
case ChartType.compositeTertiary:
  return 'assets/config/composite_progression_interpretation_options.json';

// 時空中點盤推運
case ChartType.davisonSecondary:
case ChartType.davisonTertiary:
  return 'assets/config/davison_progression_interpretation_options.json';

// 馬克思盤推運
case ChartType.marksSecondary:
case ChartType.marksTertiary:
  return 'assets/config/marks_progression_interpretation_options.json';
```

## 使用指南

### 1. 配置載入
```dart
final config = await InterpretationConfigService.instance.loadConfig(ChartType.synastrySecondary);
```

### 2. 選項獲取
```dart
final options = config.enabledOptions;
```

### 3. 自定義擴展
可以通過修改 JSON 文件來：
- 添加新的解讀選項
- 調整問題內容
- 修改顏色和圖標
- 更新關鍵點指導

## 維護建議

### 1. 定期更新
- 根據用戶反饋調整問題
- 更新關鍵點指導內容
- 優化解讀選項的順序

### 2. 版本控制
- 更新時修改版本號
- 記錄更新日期
- 保持向後兼容性

### 3. 測試驗證
- 測試配置載入功能
- 驗證圖標和顏色顯示
- 確保問題內容的準確性

## 擴展可能性

### 1. 多語言支持
- 添加不同語言版本
- 保持配置結構一致
- 支持動態語言切換

### 2. 個性化配置
- 允許用戶自定義問題
- 提供個人化的解讀重點
- 支持配置的導入導出

### 3. AI 整合
- 根據配置生成更精準的解讀
- 使用關鍵點指導優化 AI 回應
- 提供更個性化的分析結果
