# Firebase Storage CSV 備份還原功能文件

## 功能概述

實作了真正的 Firebase Storage 備份還原功能，使用 CSV 格式與現有的匯出資料格式完全相同，確保資料的一致性和相容性。

## 主要特色

### 🔥 真實 Firebase Storage 整合
- **真實雲端儲存**：使用 Firebase Storage 進行實際的雲端備份
- **安全隔離**：每個用戶的備份檔案獨立儲存
- **權限控制**：基於 Firebase Auth 的用戶權限管理

### 📊 CSV 格式一致性
- **格式統一**：與現有匯出功能使用相同的 CSV 格式
- **欄位對應**：完整保留所有出生資料欄位
- **編碼標準**：使用 UTF-8 編碼確保中文正確顯示

### 🔄 完整備份還原流程
- **自動備份**：一鍵備份所有出生資料
- **智能還原**：自動檢測並解析備份檔案
- **資料驗證**：確保還原資料的完整性和正確性

## 技術實作

### 📁 檔案結構
```
lib/services/firebase_storage_backup_service.dart  # Firebase Storage 服務
lib/utils/csv_helper.dart                          # CSV 處理工具（新增方法）
pubspec.yaml                                       # 新增 firebase_storage 依賴
```

### 🔧 核心服務更新

#### FirebaseStorageBackupService
```dart
class FirebaseStorageBackupService {
  static final FirebaseStorage _storage = FirebaseStorage.instance;
  
  /// 備份出生資料到 Firebase Storage（CSV 格式）
  static Future<bool> backupBirthData()
  
  /// 從 Firebase Storage 還原出生資料（CSV 格式）
  static Future<bool> restoreBirthData()
  
  /// 檢查 Firebase Storage 備份狀態
  static Future<Map<String, dynamic>?> getBackupInfo()
  
  /// 刪除 Firebase Storage 備份
  static Future<bool> deleteBackup()
}
```

#### CsvHelper 新增方法
```dart
class CsvHelper {
  /// 生成 CSV 內容字串（不儲存檔案）
  static String generateCsvContent(List<BirthData> birthDataList)
  
  /// 從 CSV 字串直接匯入出生資料
  static Future<List<BirthData>> importBirthData(String csvContent)
}
```

### 📊 CSV 格式規範

#### 標題行
```csv
ID,姓名,出生日期時間,出生地點,備註,緯度,經度,分類,最後存取時間,建立時間
```

#### 資料行範例
```csv
uuid-123,張三,2023-01-01 12:00:00,台北市,測試資料,25.0330,121.5654,個人,2023-07-08 10:30:00,2023-01-01 12:00:00
```

### 🔥 Firebase Storage 結構
```
/user_backups/
  /{userId}/
    /birth_data_backup.csv
```

#### Metadata 資訊
```json
{
  "contentType": "text/csv",
  "customMetadata": {
    "backup_version": "1.0",
    "birth_data_count": "5",
    "created_at": "2025-07-08T12:00:00.000Z",
    "format": "csv"
  }
}
```

## 備份流程

### 📤 備份到 Firebase Storage
1. **用戶驗證**：檢查 Firebase Auth 登入狀態
2. **資料收集**：獲取所有本地出生資料
3. **CSV 生成**：使用 `CsvHelper.generateCsvContent()` 生成 CSV
4. **編碼轉換**：轉換為 UTF-8 bytes
5. **雲端上傳**：上傳到 Firebase Storage
6. **Metadata 設定**：設定檔案 metadata 和自訂資訊
7. **結果回報**：回報備份成功或失敗

### 📥 從 Firebase Storage 還原
1. **用戶驗證**：檢查 Firebase Auth 登入狀態
2. **檔案檢查**：檢查 Firebase Storage 中是否存在備份
3. **檔案下載**：從 Firebase Storage 下載 CSV 檔案
4. **內容解析**：解碼 UTF-8 並解析 CSV 內容
5. **資料驗證**：驗證 CSV 格式和資料完整性
6. **資料還原**：將解析的資料儲存到本地資料庫
7. **UI 更新**：更新 FilesViewModel 以反映新資料
8. **結果回報**：回報還原成功或失敗

## 安全性設計

### 🔐 用戶隔離
- **路徑隔離**：`/user_backups/{userId}/` 確保用戶資料隔離
- **權限控制**：基於 Firebase Auth 的身份驗證
- **存取限制**：只能存取自己的備份檔案

### 🛡️ 資料保護
- **格式驗證**：嚴格驗證 CSV 格式和內容
- **錯誤處理**：完善的錯誤處理和回滾機制
- **資料完整性**：確保備份和還原過程中資料不丟失

### 📋 操作安全
- **雙重確認**：重要操作需要用戶明確確認
- **進度顯示**：長時間操作顯示進度避免重複操作
- **錯誤回報**：清楚的錯誤訊息和解決建議

## 依賴更新

### 📦 新增依賴
```yaml
dependencies:
  firebase_storage: ^12.3.6  # 新增 Firebase Storage 支援
```

### 🔧 現有依賴
```yaml
dependencies:
  csv: ^6.0.0                # CSV 處理（已存在）
  firebase_core: ^3.13.0     # Firebase 核心（已存在）
  firebase_auth: ^5.3.1      # Firebase 驗證（已存在）
```

## 使用範例

### 📤 備份操作
```dart
// 執行備份
final success = await FirebaseStorageBackupService.backupBirthData();
if (success) {
  print('備份成功');
} else {
  print('備份失敗');
}
```

### 📥 還原操作
```dart
// 檢查備份存在
final backupInfo = await FirebaseStorageBackupService.getBackupInfo();
if (backupInfo != null) {
  // 執行還原
  final success = await FirebaseStorageBackupService.restoreBirthData();
  if (success) {
    print('還原成功');
  }
}
```

### 🔍 備份資訊
```dart
// 獲取備份詳細資訊
final info = await FirebaseStorageBackupService.getBackupInfo();
if (info != null) {
  print('檔案大小: ${info['size']} bytes');
  print('建立時間: ${info['timeCreated']}');
  print('資料筆數: ${info['customMetadata']['birth_data_count']}');
}
```

## 錯誤處理

### ⚠️ 常見錯誤
- **用戶未登入**：提示需要 Firebase Auth 登入
- **網路連線問題**：提示檢查網路連線
- **Firebase Storage 權限**：檢查 Firebase 專案設定
- **檔案不存在**：提示沒有找到備份檔案
- **CSV 格式錯誤**：提示備份檔案格式無效

### 🔧 錯誤處理策略
- **友善提示**：使用易懂的中文錯誤訊息
- **詳細日誌**：記錄詳細錯誤資訊供開發者調試
- **優雅降級**：部分功能失效時保持基本功能
- **重試機制**：網路問題時提供重試選項

## 效能考量

### 🚀 上傳效能
- **檔案大小**：CSV 格式相對緊湊，上傳快速
- **壓縮傳輸**：Firebase Storage 自動處理壓縮
- **進度顯示**：大檔案上傳時顯示進度

### 💾 下載效能
- **快取機制**：Firebase Storage 提供 CDN 快取
- **增量下載**：只下載變更的部分（未來可實作）
- **本地快取**：下載後可選擇本地快取

## 測試建議

### 🧪 功能測試
1. **備份測試**：測試不同資料量的備份
2. **還原測試**：測試備份檔案的正確還原
3. **格式測試**：測試 CSV 格式的相容性
4. **權限測試**：測試用戶權限隔離

### 🔒 安全測試
1. **身份驗證**：測試未登入用戶的存取限制
2. **權限隔離**：測試用戶間的資料隔離
3. **資料完整性**：測試備份還原的資料完整性
4. **錯誤注入**：測試異常情況的處理

### 📱 整合測試
1. **UI 整合**：測試與系統設定頁面的整合
2. **資料同步**：測試還原後的 UI 資料更新
3. **跨平台**：測試不同平台的相容性
4. **網路狀況**：測試不同網路環境下的表現

## 相關檔案

- `lib/services/firebase_storage_backup_service.dart` - Firebase Storage 服務
- `lib/utils/csv_helper.dart` - CSV 處理工具
- `lib/ui/pages/settings/system_settings_page.dart` - UI 整合
- `pubspec.yaml` - 依賴配置
- `lib/models/birth_data.dart` - 資料模型

---

**功能版本：** v2.0  
**實作日期：** 2025-07-08  
**實作人員：** Augment Agent  
**狀態：** 真實 Firebase Storage 實作  
**格式：** CSV（與匯出功能一致）  
**測試狀態：** ✅ 編譯通過
