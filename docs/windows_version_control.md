# Windows 平台版本控管解決方案

## 🎯 問題背景

Windows 平台的 Flutter 應用對 Firebase SDK 支援有限，特別是 `cloud_firestore` 套件在 Windows 上可能無法正常工作。為了解決這個問題，我們為 Windows 平台實作了基於 REST API 的版本控管方案。

## 🔧 解決方案

### 架構設計

```
┌─────────────────────────────────────┐
│        統一版本檢查服務              │
│    (VersionCheckServiceUnified)     │
└─────────────┬───────────────────────┘
              │
              ├─ Windows 平台
              │  └─ REST API 服務
              │     (VersionCheckServiceWindows)
              │
              └─ 其他平台 (Android/iOS/macOS/Web)
                 └─ Firebase SDK 服務
                    (VersionCheckServiceFirebase)
```

### 核心組件

1. **統一服務** (`VersionCheckServiceUnified`)
   - 自動檢測平台並選擇合適的實作
   - 提供統一的 API 接口
   - 對應用層透明

2. **Windows REST API 服務** (`VersionCheckServiceWindows`)
   - 使用 HTTP 請求直接調用 Firestore REST API
   - 不依賴 Firebase SDK
   - 處理 Firestore 數據格式轉換

3. **Firebase SDK 服務** (`VersionCheckServiceFirebase`)
   - 使用原有的 Firebase SDK
   - 適用於支援 Firebase 的平台

## 📁 文件結構

```
lib/
├── services/
│   ├── version_check_service_unified.dart    # 統一服務
│   ├── version_check_service_windows.dart    # Windows REST API 服務
│   └── version_check_service_firebase.dart   # Firebase SDK 服務
├── config/
│   └── firebase_config_windows.dart          # Windows 配置
└── models/
    └── app_version.dart                       # 版本數據模型
```

## 🚀 使用方式

### 在應用中使用

```dart
import 'package:astreal/services/version_check_service_unified.dart';

// 初始化（會自動選擇合適的服務）
await VersionCheckServiceUnified.initialize();

// 檢查版本更新
final status = await VersionCheckServiceUnified.checkForUpdates();

// 處理更新結果
if (status.needsUpdate) {
  // 顯示更新對話框
}
```

### 平台檢測邏輯

```dart
static bool _shouldUseRestApi() {
  // Windows 平台使用 REST API
  if (!kIsWeb && Platform.isWindows) {
    return true;
  }
  
  // 其他平台使用 Firebase SDK
  return false;
}
```

## 🔑 配置說明

### Windows 配置 (`FirebaseConfigWindows`)

```dart
class FirebaseConfigWindows {
  static const String projectId = 'astreal-d3f70';
  static const String apiKey = 'AIzaSyAeCMo-vuea1Z6nS1_EwOygN8TOY3ncmMc';
  
  // REST API URL 生成
  static String getDocumentUrl(String collection, String document) =>
      'https://firestore.googleapis.com/v1/projects/$projectId/databases/(default)/documents/$collection/$document';
}
```

### 依賴要求

```yaml
dependencies:
  http: ^1.2.0              # Windows REST API 需要
  package_info_plus: ^8.0.0 # 獲取應用版本信息
  
  # 以下僅在支援的平台使用
  firebase_core: ^3.13.0
  firebase_auth: ^5.3.1
  cloud_firestore: ^5.6.7
```

## 🔄 數據格式轉換

### Firestore REST API 格式

Firestore REST API 返回的數據格式與 SDK 不同，需要進行轉換：

```json
{
  "fields": {
    "version": {"stringValue": "1.0.0"},
    "buildNumber": {"integerValue": "1"},
    "forceUpdate": {"booleanValue": false},
    "features": {
      "arrayValue": {
        "values": [
          {"stringValue": "功能1"},
          {"stringValue": "功能2"}
        ]
      }
    }
  }
}
```

### 轉換為標準格式

```dart
static Map<String, dynamic> _convertFirestoreFields(Map<String, dynamic> fields) {
  final result = <String, dynamic>{};
  
  for (final entry in fields.entries) {
    final key = entry.key;
    final value = entry.value as Map<String, dynamic>;
    
    if (value.containsKey('stringValue')) {
      result[key] = value['stringValue'];
    } else if (value.containsKey('integerValue')) {
      result[key] = int.parse(value['integerValue']);
    } else if (value.containsKey('booleanValue')) {
      result[key] = value['booleanValue'];
    } else if (value.containsKey('arrayValue')) {
      final arrayValue = value['arrayValue'] as Map<String, dynamic>;
      final values = arrayValue['values'] as List<dynamic>? ?? [];
      result[key] = values.map((v) => v['stringValue'] ?? '').toList();
    }
  }
  
  return result;
}
```

## 🧪 測試

### 運行測試

```bash
# 運行 Windows 版本檢查測試
flutter test test/version_check_windows_test.dart

# 運行所有版本檢查測試
flutter test test/version_check_test.dart test/version_check_windows_test.dart
```

### 測試覆蓋

- ✅ Windows REST API 服務初始化
- ✅ 統一服務平台檢測
- ✅ 配置文件正確性
- ✅ URL 生成邏輯
- ✅ 版本信息獲取

## 🔒 安全考慮

### API Key 安全

- API Key 已配置為只讀權限
- 僅允許訪問 Firestore 數據
- 不包含敏感操作權限

### 網路安全

- 使用 HTTPS 加密傳輸
- 驗證 SSL 證書
- 處理網路錯誤和超時

## 🚨 故障排除

### 常見問題

1. **網路連接失敗**
   ```
   ❌ HTTP 請求失敗: 網路錯誤
   ```
   **解決方案**: 檢查網路連接和防火牆設置

2. **API Key 無效**
   ```
   ❌ HTTP 請求失敗: 401 Unauthorized
   ```
   **解決方案**: 檢查 Firebase 配置中的 API Key

3. **數據格式錯誤**
   ```
   ❌ 版本信息數據為空
   ```
   **解決方案**: 確認 Firestore 中的數據格式正確

### 調試技巧

1. **啟用詳細日誌**
   ```dart
   logger.i('正在從 Firestore REST API 獲取版本信息...');
   ```

2. **檢查 HTTP 響應**
   ```dart
   print('HTTP Status: ${response.statusCode}');
   print('Response Body: ${response.body}');
   ```

3. **驗證 URL 生成**
   ```dart
   final url = FirebaseConfigWindows.getDocumentUrl('app_versions', 'windows');
   print('Request URL: $url');
   ```

## 🎯 優勢

### 相比其他方案的優勢

1. **無縫集成** - 對應用層完全透明
2. **平台適應** - 自動選擇最佳實作方式
3. **維護簡單** - 統一的 API 接口
4. **性能良好** - REST API 直接訪問，無額外開銷
5. **可靠性高** - 不依賴可能不穩定的 SDK

### 與 Firebase SDK 的兼容性

- 其他平台繼續使用 Firebase SDK
- 數據格式完全兼容
- 功能特性一致
- 無需修改現有代碼

## 📈 未來擴展

### 可能的改進

1. **緩存機制** - 本地緩存版本信息
2. **離線支援** - 離線時使用緩存數據
3. **更多平台** - 支援 Linux 等其他平台
4. **性能優化** - 請求合併和批處理

### 監控和分析

1. **請求統計** - 記錄 API 調用次數
2. **錯誤追蹤** - 監控失敗率和錯誤類型
3. **性能指標** - 測量響應時間

這個解決方案確保了 Windows 平台也能享受到完整的版本控管功能，同時保持了與其他平台的一致性。🚀
