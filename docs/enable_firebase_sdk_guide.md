# 啟用 Firebase SDK 功能指南

## 概述

本指南說明如何啟用 `UserDataInitializationService` 中的 Firebase SDK 功能，從當前的 REST API 實現切換到完整的 Firestore SDK 實現。

## 當前狀態

目前所有 SDK 方法都已實現，但被註釋掉了，因為：
1. `cloud_firestore` 依賴在 `pubspec.yaml` 中被註釋
2. 項目主要使用 REST API 方式
3. SDK 實現作為未來升級的準備

## 啟用步驟

### 步驟 1：啟用 Firestore 依賴

在 `pubspec.yaml` 中找到並取消註釋：
```yaml
dependencies:
  # cloud_firestore: ^4.13.6  # 取消這行的註釋
  cloud_firestore: ^4.13.6    # 變成這樣
```

然後執行：
```bash
flutter pub get
```

### 步驟 2：啟用導入

在 `lib/services/user_data_initialization_service.dart` 中：
```dart
// import 'package:cloud_firestore/cloud_firestore.dart'; // 需要時取消註釋

// 改為：
import 'package:cloud_firestore/cloud_firestore.dart';
```

### 步驟 3：啟用 SDK 實現

對於每個 SDK 方法，需要：

#### 3.1 啟用 _getUserProfileViaSDK
```dart
/// 通過 SDK 獲取用戶檔案
static Future<Map<String, dynamic>?> _getUserProfileViaSDK(String userId) async {
  try {
    // 取消註釋以下代碼：
    final firestore = FirebaseFirestore.instance;
    final docRef = firestore.collection('user_profiles').doc(userId);
    final docSnapshot = await docRef.get();
    
    if (docSnapshot.exists) {
      final data = docSnapshot.data() as Map<String, dynamic>?;
      if (data != null) {
        logger.d('通過 SDK 獲取用戶檔案成功: $userId');
        return data;
      }
    }
    
    logger.d('用戶檔案不存在: $userId');
    return null;
    
    // 移除以下暫時實現：
    // logger.w('SDK 版本需要 cloud_firestore 依賴，暫時返回 null');
    // return null;
  } catch (e) {
    logger.e('通過 SDK 獲取用戶檔案失敗: $e');
    return null;
  }
}
```

#### 3.2 啟用 _setUserProfileViaSDK
```dart
/// 通過 SDK 設置用戶檔案
static Future<bool> _setUserProfileViaSDK(String userId, Map<String, dynamic> data) async {
  try {
    // 取消註釋以下代碼：
    final firestore = FirebaseFirestore.instance;
    final docRef = firestore.collection('user_profiles').doc(userId);
    
    // 添加伺服器時間戳
    data['updated_at'] = FieldValue.serverTimestamp();
    if (!data.containsKey('created_at')) {
      data['created_at'] = FieldValue.serverTimestamp();
    }
    
    await docRef.set(data, SetOptions(merge: true));
    logger.i('通過 SDK 設置用戶檔案成功: $userId');
    return true;
    
    // 移除以下暫時實現：
    // logger.w('SDK 版本需要 cloud_firestore 依賴，暫時返回 false');
    // return false;
  } catch (e) {
    logger.e('通過 SDK 設置用戶檔案失敗: $e');
    return false;
  }
}
```

#### 3.3 對其他方法重複相同操作
- `_getUserSettingsViaSDK` / `_setUserSettingsViaSDK`
- `_getUserPreferencesViaSDK` / `_setUserPreferencesViaSDK`

### 步驟 4：配置 Firestore 安全規則

在 Firebase Console 中設置 Firestore 安全規則：
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // 用戶檔案
    match /user_profiles/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // 用戶設定
    match /user_settings/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // 用戶偏好設定
    match /user_preferences/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
  }
}
```

### 步驟 5：測試功能

1. **編譯測試**：
   ```bash
   flutter analyze
   ```

2. **功能測試**：
   - 註冊新用戶
   - 檢查 Firestore 中是否創建了相應文檔
   - 驗證數據結構正確性

3. **錯誤處理測試**：
   - 測試網路斷開情況
   - 測試權限錯誤處理
   - 驗證日誌記錄

## 驗證清單

### ✅ 依賴檢查
- [ ] `cloud_firestore` 依賴已添加
- [ ] `flutter pub get` 執行成功
- [ ] 編譯無錯誤

### ✅ 代碼檢查
- [ ] 導入語句已取消註釋
- [ ] 所有 SDK 方法實現已啟用
- [ ] 暫時實現代碼已移除
- [ ] 日誌記錄正常

### ✅ Firebase 配置
- [ ] Firestore 數據庫已啟用
- [ ] 安全規則已設置
- [ ] 認證配置正確

### ✅ 功能驗證
- [ ] 用戶註冊創建文檔
- [ ] 數據讀寫正常
- [ ] 錯誤處理正確
- [ ] 日誌輸出清晰

## 回滾方案

如果需要回滾到 REST API 實現：

1. **重新註釋依賴**：
   ```yaml
   # cloud_firestore: ^4.13.6
   ```

2. **重新註釋導入**：
   ```dart
   // import 'package:cloud_firestore/cloud_firestore.dart';
   ```

3. **重新註釋 SDK 實現**，恢復暫時實現

4. **執行清理**：
   ```bash
   flutter clean
   flutter pub get
   ```

## 性能比較

### REST API vs SDK

| 特性 | REST API | Firebase SDK |
|------|----------|--------------|
| 實時同步 | ❌ | ✅ |
| 離線支援 | ❌ | ✅ |
| 本地快取 | ❌ | ✅ |
| 批量操作 | ❌ | ✅ |
| 類型安全 | ⚠️ | ✅ |
| 錯誤處理 | 基本 | 完整 |
| 開發複雜度 | 低 | 中 |
| 功能豐富度 | 基本 | 豐富 |

## 建議的遷移策略

### 階段 1：準備階段
- 在開發環境啟用 SDK
- 進行充分測試
- 準備回滾方案

### 階段 2：灰度發布
- 小範圍用戶使用 SDK
- 監控性能和錯誤
- 收集用戶反饋

### 階段 3：全面遷移
- 所有用戶切換到 SDK
- 移除 REST API 代碼
- 優化性能配置

### 階段 4：後續優化
- 添加更多 SDK 功能
- 實現實時同步
- 優化用戶體驗

## 注意事項

1. **數據一致性**：確保 REST API 和 SDK 的數據格式完全一致
2. **錯誤處理**：SDK 的錯誤類型與 REST API 不同，需要適配
3. **性能監控**：SDK 可能有不同的性能特徵，需要監控
4. **成本考慮**：SDK 的計費方式可能與 REST API 不同

## 支援資源

- [Firebase Firestore 文檔](https://firebase.google.com/docs/firestore)
- [Flutter Firebase 插件](https://firebase.flutter.dev/)
- [Firestore 安全規則](https://firebase.google.com/docs/firestore/security/get-started)
- [性能監控](https://firebase.google.com/docs/perf-mon)

按照這個指南，您可以安全地啟用 Firebase SDK 功能，享受更豐富的 Firestore 特性。
