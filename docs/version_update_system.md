# 版本更新系統說明文件

## 概述

本系統設計用於智能管理 Web 應用的版本更新通知，避免頻繁的無意義更新提示，只在真正有重要更新時才通知用戶。

## 問題背景

原本的版本檢查系統存在以下問題：
1. 每次部署都會生成新的時間戳版本號
2. 只要版本號不同就會顯示更新通知
3. 導致用戶頻繁收到「新版本可用」的提示，即使沒有實質性更新

## 解決方案

### 1. 智能版本比較

系統現在支援三種版本號格式：

#### 語義化版本號（推薦用於正式發布）
- 格式：`x.y.z`（如 1.0.0, 1.2.3）
- 行為：總是通知更新
- 使用場景：正式版本發布

#### 時間戳版本號（用於日常部署）
- 格式：`YYYYMMDD_HHMMSS`（如 20250119_143000）
- 行為：只有當新版本比當前版本新超過 1 小時才通知
- 使用場景：日常開發部署

#### 標記更新版本號（用於重要更新）
- 格式：包含 `update` 字樣的版本號（如 20250119_143000_update）
- 行為：總是通知更新
- 使用場景：重要功能更新或錯誤修復

### 2. 部署命令

```bash
# 一般部署（不觸發更新通知）
./scripts/deploy_web.sh

# 重要更新部署（觸發更新通知）
./scripts/deploy_web.sh --notify-update

# 正式版本發布（使用語義化版本號）
./scripts/deploy_web.sh --version=1.2.0
```

### 3. 通知策略

- **記憶功能**：系統會記住已經通知過的版本，避免重複通知
- **延遲檢查**：首次檢查延遲 1 分鐘，定期檢查間隔 5 分鐘
- **稍後提醒**：用戶點擊「稍後」後，30 分鐘後才會重新提醒

## 使用指南

### 日常開發部署
```bash
./scripts/deploy_web.sh
```
- 生成時間戳版本號
- 不會觸發用戶更新通知
- 適用於小修改、樣式調整等

### 重要功能更新
```bash
./scripts/deploy_web.sh --notify-update
```
- 生成帶 `update` 標記的版本號
- 會觸發用戶更新通知
- 適用於新功能、重要修復等

### 正式版本發布
```bash
./scripts/deploy_web.sh --version=1.2.0
```
- 使用語義化版本號
- 會觸發用戶更新通知
- 適用於里程碑版本發布

## 配置文件

版本更新策略可以通過 `scripts/version_update_config.json` 進行配置：

- `timestampVersionThreshold`：時間戳版本的通知閾值（預設 1 小時）
- `dismissTimeout`：稍後提醒的間隔時間（預設 30 分鐘）
- `checkInterval`：版本檢查間隔（預設 5 分鐘）
- `initialCheckDelay`：首次檢查延遲（預設 1 分鐘）

## 最佳實踐

1. **日常開發**：使用一般部署命令，避免打擾用戶
2. **重要更新**：使用 `--notify-update` 參數，確保用戶及時更新
3. **版本發布**：使用語義化版本號，便於版本管理
4. **測試驗證**：部署後檢查瀏覽器控制台的版本日誌

## 故障排除

### 如果更新通知仍然頻繁出現

1. 檢查是否使用了正確的部署命令
2. 清除瀏覽器的 localStorage：`localStorage.removeItem('lastNotifiedVersion')`
3. 檢查 `version.json` 文件的內容是否正確

### 如果重要更新沒有通知

1. 確認使用了 `--notify-update` 參數
2. 檢查版本號是否包含 `update` 標記
3. 確認用戶沒有在 30 分鐘內點擊過「稍後」

## 技術細節

- 版本比較邏輯位於 `web/simple_version_check.js`
- 部署腳本位於 `scripts/deploy_web.sh`
- 版本信息存儲在 `build/web/version.json`
- 用戶偏好存儲在瀏覽器的 localStorage 中
