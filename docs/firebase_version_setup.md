# Firebase 版本控管設置說明

## 概述

本文檔說明如何在 Firebase Firestore 中設置應用版本控管系統，實現自動版本檢查和強制更新功能。

## Firestore 數據結構

### Collection: `app_versions`

每個平台都有一個獨立的文檔，文檔 ID 為平台名稱：

- `android` - Android 平台版本信息
- `ios` - iOS 平台版本信息  
- `macos` - macOS 平台版本信息
- `windows` - Windows 平台版本信息
- `web` - Web 平台版本信息

### 文檔結構

每個平台文檔包含以下字段：

```json
{
  "version": "1.0.0",                    // 最新版本號
  "buildNumber": 1,                      // 最新構建號
  "minRequiredVersion": "1.0.0",         // 最低要求版本號
  "minRequiredBuildNumber": 1,           // 最低要求構建號
  "forceUpdate": false,                  // 是否強制更新
  "updateMessage": "發現新版本...",       // 更新提示訊息
  "updateUrl": "https://...",            // 更新下載連結
  "releaseDate": "2025-01-17T10:00:00Z", // 發布日期
  "features": [                          // 新功能列表
    "修復已知問題",
    "提升應用性能"
  ],
  "isActive": true                       // 是否啟用版本檢查
}
```

## 設置步驟

### 1. 在 Firebase Console 中創建數據

1. 打開 Firebase Console
2. 選擇您的項目
3. 進入 Firestore Database
4. 創建 Collection `app_versions`
5. 為每個平台創建文檔

### 2. Android 平台設置示例

文檔 ID: `android`

```json
{
  "version": "1.0.0",
  "buildNumber": 1,
  "minRequiredVersion": "1.0.0",
  "minRequiredBuildNumber": 1,
  "forceUpdate": false,
  "updateMessage": "歡迎使用 Astreal 占星應用！建議您更新以獲得最佳體驗。",
  "updateUrl": "https://play.google.com/store/apps/details?id=com.one.astreal",
  "releaseDate": "2025-01-17T10:00:00.000Z",
  "features": [
    "完整的本命盤分析功能",
    "多種星盤類型支持",
    "深入剖析",
    "專業的占星計算引擎"
  ],
  "isActive": true
}
```

### 3. iOS 平台設置示例

文檔 ID: `ios`

```json
{
  "version": "1.0.0",
  "buildNumber": 1,
  "minRequiredVersion": "1.0.0",
  "minRequiredBuildNumber": 1,
  "forceUpdate": false,
  "updateMessage": "歡迎使用 Astreal 占星應用！建議您更新以獲得最佳體驗。",
  "updateUrl": "https://apps.apple.com/app/astreal/id123456789",
  "releaseDate": "2025-01-17T10:00:00.000Z",
  "features": [
    "完整的本命盤分析功能",
    "多種星盤類型支持",
    "深入剖析",
    "專業的占星計算引擎"
  ],
  "isActive": true
}
```

## 版本更新流程

### 發布新版本時

1. **構建新版本**
   - 更新 `pubspec.yaml` 中的版本號
   - 構建並測試新版本

2. **更新 Firestore 數據**
   - 更新對應平台文檔中的 `version` 和 `buildNumber`
   - 更新 `features` 列表
   - 設置適當的 `updateMessage`

3. **可選更新示例**
   ```json
   {
     "version": "1.0.1",
     "buildNumber": 2,
     "minRequiredVersion": "1.0.0",
     "minRequiredBuildNumber": 1,
     "forceUpdate": false,
     "updateMessage": "新版本可用！包含性能優化和錯誤修復。",
     "features": ["修復登入問題", "提升性能", "新增分享功能"]
   }
   ```

4. **強制更新示例**
   ```json
   {
     "version": "1.1.0",
     "buildNumber": 3,
     "minRequiredVersion": "1.1.0",
     "minRequiredBuildNumber": 3,
     "forceUpdate": true,
     "updateMessage": "重要安全更新！必須更新才能繼續使用。",
     "features": ["修復安全漏洞", "重要穩定性改進"]
   }
   ```

## 版本檢查邏輯

### 檢查時機

1. **應用啟動時** - 自動檢查
2. **設定頁面** - 手動檢查

### 檢查結果

- **最新版本** - 不顯示任何提示
- **可選更新** - 顯示更新對話框，用戶可選擇稍後更新
- **強制更新** - 顯示強制更新對話框，用戶必須更新才能繼續使用

### 版本比較規則

1. 比較主版本號 (major)
2. 比較次版本號 (minor)  
3. 比較修訂版本號 (patch)
4. 如果版本號相同，比較構建號 (buildNumber)

## 安全考慮

### Firestore 安全規則

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // 版本信息只允許讀取，不允許客戶端寫入
    match /app_versions/{platform} {
      allow read: if true;
      allow write: if false; // 只能通過管理後台或服務端更新
    }
  }
}
```

### 最佳實踐

1. **版本信息只讀** - 客戶端只能讀取，不能修改版本信息
2. **管理後台** - 通過專門的管理後台或腳本來更新版本信息
3. **測試環境** - 在測試環境中驗證版本檢查邏輯
4. **漸進式發布** - 可以通過 `isActive` 字段控制是否啟用版本檢查

## 測試

### 測試強制更新

1. 在 Firestore 中設置 `forceUpdate: true`
2. 設置 `minRequiredVersion` 高於當前版本
3. 重啟應用測試

### 測試可選更新

1. 在 Firestore 中設置 `forceUpdate: false`
2. 設置 `version` 高於當前版本，但 `minRequiredVersion` 不變
3. 重啟應用測試

## 故障排除

### 常見問題

1. **版本檢查失敗**
   - 檢查網路連接
   - 確認 Firebase 配置正確
   - 檢查 Firestore 安全規則

2. **更新對話框不顯示**
   - 確認版本號設置正確
   - 檢查 `isActive` 字段是否為 true
   - 查看應用日誌

3. **強制更新無法關閉**
   - 這是預期行為，用戶必須更新才能繼續使用

### 調試技巧

1. 查看應用日誌中的版本檢查信息
2. 在 Firebase Console 中確認數據結構正確
3. 使用開發模式測試不同的版本場景
