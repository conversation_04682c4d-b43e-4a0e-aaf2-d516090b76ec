# 版本控管與強制更新系統使用說明

## 系統概述

本系統使用 Firebase Firestore 實現了完整的應用版本控管與強制更新機制，支持：

- ✅ 應用啟動時自動檢查版本
- ✅ 設定頁面手動檢查版本
- ✅ 強制更新機制
- ✅ 可選更新提示
- ✅ 多平台支持（Android、iOS、macOS、Windows、Web）
- ✅ 優雅的用戶界面

## 功能特點

### 1. 自動版本檢查
- 應用每次啟動時自動檢查版本
- 延遲 1 秒執行，確保 UI 完全載入
- 網路錯誤時靜默失敗，不影響應用使用

### 2. 手動版本檢查
- 在設定頁面提供「檢查更新」按鈕
- 顯示載入指示器
- 提供詳細的檢查結果反饋

### 3. 智能更新提示
- **最新版本**：不顯示任何提示
- **可選更新**：顯示更新對話框，用戶可選擇稍後更新
- **強制更新**：顯示強制更新對話框，用戶必須更新才能繼續使用

### 4. 豐富的更新信息
- 版本號和構建號
- 更新說明訊息
- 新功能列表
- 發布日期
- 平台特定的下載連結

## 已實現的文件

### 核心模型
- `lib/models/app_version.dart` - 版本信息數據模型

### 服務層
- `lib/services/version_check_service.dart` - 版本檢查核心服務

### UI 組件
- `lib/widgets/update_dialog.dart` - 更新提示對話框
- `lib/ui/pages/settings/system_settings_page.dart` - 設定頁面（已添加檢查更新功能）

### 工具類
- `lib/utils/version_init_helper.dart` - 版本初始化輔助工具

### 測試
- `test/version_check_test.dart` - 完整的單元測試

### 文檔
- `docs/firebase_version_setup.md` - Firebase 設置詳細說明
- `docs/version_control_usage.md` - 本使用說明

## 使用方式

### 1. 應用啟動檢查
應用啟動時會自動檢查版本，無需任何操作。如果有更新，會自動顯示相應的對話框。

### 2. 手動檢查更新
1. 打開應用
2. 進入「設定」頁面
3. 滑動到「關於應用」區域
4. 點擊「檢查更新」按鈕

### 3. 更新操作
- **可選更新**：可以選擇「稍後更新」或「更新」
- **強制更新**：只能選擇「立即更新」，無法關閉對話框

## Firebase 設置

### 1. Firestore 結構
```
app_versions/
├── android/     # Android 版本信息
├── ios/         # iOS 版本信息
├── macos/       # macOS 版本信息
├── windows/     # Windows 版本信息
└── web/         # Web 版本信息
```

### 2. 文檔字段
```json
{
  "version": "1.0.0",
  "buildNumber": 1,
  "minRequiredVersion": "1.0.0",
  "minRequiredBuildNumber": 1,
  "forceUpdate": false,
  "updateMessage": "更新說明",
  "updateUrl": "下載連結",
  "releaseDate": "發布日期",
  "features": ["新功能列表"],
  "isActive": true
}
```

### 3. 安全規則
```javascript
match /app_versions/{platform} {
  allow read: if true;
  allow write: if false; // 只能通過管理後台更新
}
```

## 版本發布流程

### 1. 準備新版本
1. 更新 `pubspec.yaml` 中的版本號
2. 構建並測試新版本
3. 準備更新說明和新功能列表

### 2. 更新 Firestore
1. 登入 Firebase Console
2. 進入 Firestore Database
3. 找到對應平台的文檔
4. 更新版本信息

### 3. 發布類型

#### 可選更新
```json
{
  "version": "1.0.1",
  "buildNumber": 2,
  "minRequiredVersion": "1.0.0",  // 不變
  "minRequiredBuildNumber": 1,    // 不變
  "forceUpdate": false,
  "updateMessage": "新版本可用！包含性能優化和新功能。"
}
```

#### 強制更新
```json
{
  "version": "1.1.0",
  "buildNumber": 3,
  "minRequiredVersion": "1.1.0",  // 更新為新版本
  "minRequiredBuildNumber": 3,    // 更新為新構建號
  "forceUpdate": true,
  "updateMessage": "重要更新！必須更新才能繼續使用。"
}
```

## 測試建議

### 1. 功能測試
- 測試應用啟動時的版本檢查
- 測試設定頁面的手動檢查
- 測試強制更新和可選更新的不同行為

### 2. 邊界測試
- 測試網路斷線情況
- 測試 Firebase 連接失敗
- 測試版本號格式異常

### 3. 用戶體驗測試
- 確認對話框顯示正常
- 確認更新連結可以正常打開
- 確認強制更新時無法繞過

## 故障排除

### 常見問題

1. **版本檢查不工作**
   - 檢查 Firebase 配置
   - 確認網路連接
   - 查看應用日誌

2. **更新對話框不顯示**
   - 確認 Firestore 中的版本設置
   - 檢查 `isActive` 字段
   - 驗證版本號格式

3. **強制更新可以繞過**
   - 檢查 `forceUpdate` 字段
   - 確認 `minRequiredVersion` 設置正確

### 調試技巧

1. 查看應用日誌中的版本檢查信息
2. 在 Firebase Console 中確認數據正確
3. 使用測試版本號進行調試

## 最佳實踐

1. **漸進式發布**：先發布可選更新，確認無問題後再考慮強制更新
2. **清晰的更新說明**：提供詳細的更新內容和新功能說明
3. **測試環境驗證**：在測試環境中充分驗證版本檢查邏輯
4. **用戶友好**：避免頻繁的強制更新，影響用戶體驗
5. **監控反饋**：關注用戶對更新的反饋和問題報告

## 技術細節

- **版本比較邏輯**：支持語義化版本號（major.minor.patch）
- **平台檢測**：自動識別當前運行平台
- **錯誤處理**：網路錯誤時優雅降級
- **性能優化**：使用緩存減少重複請求
- **安全考慮**：客戶端只讀，防止惡意修改

這個版本控管系統已經完全實現並通過測試，可以立即投入使用！
