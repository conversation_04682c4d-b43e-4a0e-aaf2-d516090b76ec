# 統一評價系統架構文檔

## 概述

本文檔說明星真占星應用中統一的分析滿意度評價系統，該系統整合了原本分散的 `user_reviews` 和 `analysis_ratings` 功能，提供一致的用戶評價體驗。

## 系統架構

### 單一集合設計

**主要集合：`analysis_ratings`**
- 作為唯一的評價數據存儲集合
- 包含完整的分析相關信息
- 支援公開/匿名評價切換
- 記錄用戶暱稱和詳細分析內容

### 數據結構

```javascript
{
  "userId": "用戶唯一ID",
  "userEmail": "用戶郵箱",
  "userName": "用戶暱稱（自動獲取）",
  "ratingId": "唯一評價ID（防重複）",
  "interpretationTitle": "分析標題",
  "subtitle": "分析副標題",
  "keyPoint": "關鍵點",
  "chartType": "星盤類型",
  "primaryPersonDateTime": "主要人物出生時間",
  "primaryPersonBirthPlace": "主要人物出生地點",
  "rating": "評分（1-5星）",
  "comment": "評價內容",
  "isPublic": "是否公開（true/false）",
  "createdAt": "創建時間",
  "updatedAt": "更新時間",
  "analysisContent": "分析內容摘要（前100字符）"
}
```

## 功能特色

### 1. 5星制評分系統
- 從原本的10星制改為更直觀的5星制
- 大型星星圖標（32px），易於點擊
- 實時評分顯示標籤

### 2. 匿名與公開切換
- 用戶可選擇是否公開評價
- 公開評價：顯示用戶暱稱，供其他用戶參考
- 匿名評價：僅供內部改進參考

### 3. 自動用戶暱稱獲取
優先級順序：
1. `user_profiles` 集合中的 `display_name` 或 `displayName`
2. Firebase Auth 的 `displayName`
3. 預設值：`匿名用戶`

### 4. 防重複評價機制
- 使用 `ratingId` 生成唯一標識
- 基於用戶ID、分析標題、副標題、關鍵點生成
- 確保同一分析只能評價一次

## 技術實現

### 評價提交流程

```dart
Future<void> _submitRating() async {
  // 1. 驗證評分和評論
  if (_rating == 0 || _comment.trim().isEmpty) return;
  
  // 2. 獲取用戶信息
  final user = FirebaseAuth.instance.currentUser;
  final userDisplayName = await _getUserDisplayName(user.uid);
  final ratingId = _generateRatingId();
  
  // 3. 構建評價數據
  final ratingData = {
    // ... 完整數據結構
  };
  
  // 4. 保存到 analysis_ratings 集合
  await FirebaseFirestore.instance
      .collection('analysis_ratings')
      .add(ratingData);
}
```

### 評價載入流程

```dart
Future<List<Map<String, dynamic>>> _loadUserReviews() async {
  // 從 analysis_ratings 集合載入公開評價
  final ratingsSnapshot = await firestore
      .collection('analysis_ratings')
      .where('isPublic', isEqualTo: true)
      .orderBy('createdAt', descending: true)
      .limit(10)
      .get();
  
  // 轉換為統一格式
  return ratingsSnapshot.docs.map((doc) => {
    'userName': data['userName'] ?? '匿名用戶',
    'rating': data['rating'] ?? 5,
    'comment': data['comment'] ?? '',
    'createdAt': data['createdAt'] as Timestamp?,
    'analysisType': data['chartType'] ?? '',
  }).toList();
}
```

## UI 設計

### 評分區塊布局

1. **標題區域**
   - 星級圖標 + 標題文字
   - 說明文字：「您的回饋幫助我們提供更好的服務」

2. **評分區域**
   - 5個大型星星（32px）
   - 評分標籤顯示（如：3/5）

3. **評論輸入**
   - 多行文字輸入框（最多200字符）
   - 圓角設計，淺色背景

4. **公開/匿名切換**
   - Switch 組件
   - 詳細說明文字

5. **提交按鈕**
   - 大型按鈕，包含載入狀態
   - 提交後顯示感謝訊息

### 評價展示設計

1. **用戶信息**
   - 圓形頭像（暱稱首字母）
   - 用戶暱稱 + 分析類型標籤

2. **評分顯示**
   - 5星評分可視化
   - 數字評分（如：4/5）

3. **評價內容**
   - 評論文字
   - 創建日期

## 優勢

### 1. 簡化架構
- 單一集合管理所有評價
- 減少數據同步問題
- 降低維護複雜度

### 2. 完整信息
- 保留完整的分析上下文
- 支援詳細的數據分析
- 便於後續功能擴展

### 3. 用戶體驗
- 統一的評價流程
- 直觀的5星制評分
- 靈活的公開/匿名選擇

### 4. 數據一致性
- 防重複評價機制
- 統一的數據格式
- 可靠的錯誤處理

## 遷移說明

### 現有數據處理
- 保留現有的 `user_reviews` 集合作為歷史數據
- 新評價統一使用 `analysis_ratings` 集合
- 載入評價時優先使用新系統

### 向後兼容
- 現有評價展示功能保持正常
- 逐步遷移舊數據到新格式
- 確保用戶體驗無縫過渡

## 未來擴展

### 可能的功能增強
1. 評價標籤系統（如：準確、有用、詳細）
2. 評價回覆功能
3. 評價統計分析
4. 個人評價歷史查看
5. 評價品質評估機制

### 性能優化
1. 評價數據快取機制
2. 分頁載入大量評價
3. 評價搜尋和篩選功能
4. 評價數據分析報表

## 總結

統一評價系統通過整合原本分散的功能，提供了更簡潔、一致且功能完整的用戶評價體驗。系統設計考慮了用戶隱私、數據完整性和未來擴展性，為星真占星應用的持續發展奠定了堅實基礎。
