# Web 導航解決方案

## 問題描述

在 Flutter Web 應用中，當用戶按下瀏覽器的返回按鈕或手機版本的返回按鈕時，應用會重新回到啟動畫面，而不是返回到上一個頁面。這是因為 Flutter Web 的路由管理與瀏覽器的歷史記錄不同步導致的。

## 解決方案組件

### 1. WebNavigationHelper - Web 導航輔助工具

**檔案：`lib/core/utils/web_navigation_helper.dart`**

這是核心的導航管理工具，負責：
- 監聽瀏覽器的 `popstate` 事件
- 管理路由歷史記錄
- 處理瀏覽器返回按鈕
- 同步 Flutter 路由與瀏覽器歷史

**主要功能：**
```dart
// 初始化（在 main.dart 中調用）
WebNavigationHelper.initialize();

// 推送新路由
WebNavigationHelper.pushRoute('/chart');

// 設置自定義返回處理
WebNavigationHelper.setBackPressedHandler(() {
  // 自定義返回邏輯
});

// 設置頁面標題
WebNavigationHelper.setPageTitle('AstReal - 星盤頁面');
```

### 2. WebAwareAppBar - 網頁感知的 AppBar

**檔案：`lib/shared/widgets/web_aware_app_bar.dart`**

替代標準 AppBar，正確處理網頁版的返回按鈕：

```dart
// 基本使用
WebAwareAppBarHelper.simple(
  title: '星盤頁面',
  actions: [...],
)

// 自定義返回處理
WebAwareAppBarHelper.createWithCustomBack(
  title: Text('自定義頁面'),
  onBackPressed: () {
    // 自定義返回邏輯
  },
)
```

### 3. WebAwarePopScope - 網頁感知的 PopScope

**檔案：`lib/shared/widgets/web_aware_pop_scope.dart`**

包裝頁面內容，處理返回行為：

```dart
// 基本包裝
WebAwarePopScope(
  routeName: '/chart',
  child: YourPageContent(),
)

// 自定義返回處理
WebAwarePopScope(
  canPop: false,
  onBackPressed: () {
    // 自定義返回邏輯
  },
  child: YourPageContent(),
)

// 使用擴展方法
YourPageContent().wrapWithWebAwarePopScope(
  routeName: '/chart',
)
```

## 使用方法

### 1. 初始化（已在 main.dart 中完成）

```dart
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  if (kIsWeb) {
    await WebFullscreenHelper.initialize();
    WebNavigationHelper.initialize(); // 已添加
  }
  
  runApp(MyApp());
}
```

### 2. 在頁面中使用

#### 方法一：使用 WebAwarePopScope 包裝整個頁面

```dart
class MyPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return WebAwarePopScope(
      routeName: '/my-page',
      child: Scaffold(
        appBar: WebAwareAppBarHelper.simple(
          title: '我的頁面',
        ),
        body: YourContent(),
      ),
    );
  }
}
```

#### 方法二：使用擴展方法

```dart
class MyPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: WebAwareAppBarHelper.simple(title: '我的頁面'),
      body: YourContent(),
    ).wrapWithWebAwarePopScope(routeName: '/my-page');
  }
}
```

#### 方法三：自定義返回處理

```dart
class MyPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return WebAwarePopScope(
      canPop: false,
      onBackPressed: () {
        // 顯示確認對話框
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text('確認離開'),
            content: Text('您確定要離開此頁面嗎？'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text('取消'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.pop(context); // 關閉對話框
                  Navigator.pop(context); // 返回上一頁
                },
                child: Text('確認'),
              ),
            ],
          ),
        );
      },
      child: Scaffold(
        appBar: WebAwareAppBarHelper.simple(title: '重要頁面'),
        body: YourContent(),
      ),
    );
  }
}
```

### 3. 對話框中使用

```dart
showDialog(
  context: context,
  builder: (context) => WebAwareDialogWrapper(
    child: AlertDialog(
      title: Text('對話框'),
      content: Text('內容'),
      actions: [...],
    ),
  ),
);
```

## 已更新的頁面

### 1. MainScreen（主頁面）
- 已包裝在 `WebAwarePopScope` 中
- 設置路由名稱為 `/main`
- 在主頁面忽略返回操作

### 2. ChartPageNew（星盤頁面）
- 已使用 `WebAwareAppBarHelper.simple`
- 已包裝在 `WebAwarePopScope` 中
- 設置路由名稱為 `/chart`

## 最佳實踐

### 1. 路由命名
- 使用有意義的路由名稱：`/chart`、`/settings`、`/analysis`
- 保持路由名稱簡潔且唯一

### 2. 頁面標題
- 為每個頁面設置適當的標題
- 格式：`AstReal - 頁面名稱`

### 3. 自定義返回處理
- 對於重要頁面（如表單填寫），使用自定義返回處理
- 提供確認對話框避免意外離開

### 4. 對話框處理
- 對話框使用 `WebAwareDialogWrapper`
- 確保對話框也能正確響應返回按鈕

## 技術細節

### 瀏覽器事件監聽
```javascript
// 在 WebNavigationHelper 中監聽
window.addEventListener('popstate', _handlePopState);
```

### 路由同步
```dart
// 推送路由時同步瀏覽器歷史
html.window.history.pushState(null, '', route);

// 替換路由時同步瀏覽器歷史
html.window.history.replaceState(null, '', route);
```

### 返回處理邏輯
1. 檢查是否可以在 Flutter 路由中返回
2. 如果可以，使用 `Navigator.pop()`
3. 如果不可以，執行自定義返回處理或導航到主頁面

## 故障排除

### 常見問題

**Q: 返回按鈕仍然回到啟動畫面**
A: 確保頁面已正確包裝在 `WebAwarePopScope` 中，並設置了路由名稱

**Q: 自定義返回處理沒有執行**
A: 檢查 `canPop` 是否設置為 `false`，並確保 `onBackPressed` 回調已設置

**Q: 頁面標題沒有更新**
A: 確保調用了 `WebNavigationHelper.setPageTitle()`

**Q: 在移動版本中出現問題**
A: 檢查是否正確使用了條件導入，非 Web 平台會使用 stub 實現

### 除錯步驟
1. 檢查瀏覽器控制台是否有錯誤
2. 確認 `WebNavigationHelper.initialize()` 已在 main.dart 中調用
3. 驗證頁面是否正確包裝在 Web 感知組件中
4. 檢查路由名稱是否正確設置

## 未來改進

1. **路由動畫**：添加頁面切換動畫支援
2. **深層連結**：支援直接訪問特定頁面的 URL
3. **狀態保存**：在頁面切換時保存和恢復狀態
4. **SEO 優化**：為不同頁面設置不同的 meta 標籤

## 總結

這個解決方案提供了完整的 Web 導航管理，確保：
- ✅ 瀏覽器返回按鈕正確工作
- ✅ 手機版本返回按鈕正確工作
- ✅ 路由歷史記錄同步
- ✅ 自定義返回處理支援
- ✅ 頁面標題管理
- ✅ 對話框返回處理

通過使用這些組件，您的 Flutter Web 應用將提供與原生 Web 應用相同的導航體驗。
