# 建置資訊優化指南

## 概述

本指南說明如何優化 AstReal 應用的版本顯示和建置時間管理，解決以下問題：

1. **建置時間問題**：將 `DateTime.now()` 改為實際的打包時間
2. **版本號優化**：將複雜的建置號碼（如 `20250807_131801_force_update0`）格式化為更友好的顯示

## 解決方案架構

### 1. BuildInfoUtils 工具類

新建立的 `BuildInfoUtils` 類別提供以下功能：

#### 主要方法

- `getBuildDate(String buildNumber)` - 從建置號碼解析建置時間
- `formatVersionDisplay(String version, String buildNumber)` - 格式化版本顯示
- `getBuildInfo()` - 獲取完整的建置資訊
- `isDevelopmentBuild()` - 檢查是否為開發版本
- `isForceUpdateBuild()` - 檢查是否為強制更新版本
- `isBetaBuild()` - 檢查是否為測試版本

#### 支援的建置號碼格式

1. **完整時間戳格式**：`20250807131801` → `2025-08-07 13:18:01`
2. **帶標記格式**：`20250807_131801_force_update0` → `2025-08-07 13:18:01`
3. **日期格式**：`20250807` → `2025-08-07`
4. **簡單格式**：`1` → 使用編譯時間

### 2. BuildInfo 資料類別

提供結構化的建置資訊：

```dart
class BuildInfo {
  final String appName;
  final String packageName;
  final String version;
  final String buildNumber;
  final String buildDate;        // 格式化的建置日期
  final String formattedVersion; // 格式化的版本顯示
  
  String get versionTag;         // 版本標籤（正式版/測試版/開發版等）
  Color get versionTagColor;     // 版本標籤顏色
}
```

## 版本顯示優化

### 原始顯示
```
版本: 1.0.0 (20250807_131801_force_update0)
建置時間: 2025-01-08 14:30 (當前時間，不正確)
```

### 優化後顯示
```
AstReal 占星應用 [強制更新]
版本: 1.0.0 (20250807.1318-強制更新)
建置時間: 2025-08-07 13:18:01
```

### 版本標籤系統

- **正式版** - 綠色標籤
- **測試版** - 橙色標籤（包含 `beta`）
- **內測版** - 橙色標籤（包含 `alpha`）
- **開發版** - 青色標籤（建置號碼為 `1` 或包含 `dev`/`debug`）
- **強制更新** - 紅色標籤（包含 `force_update`）

## 建置腳本使用

### Linux/macOS 使用方式

```bash
# 基本使用
./scripts/build_with_timestamp.sh web release
./scripts/build_with_timestamp.sh apk release

# 強制更新版本
FORCE_UPDATE=true ./scripts/build_with_timestamp.sh web release

# 測試版本
BETA=true ./scripts/build_with_timestamp.sh apk release

# 內測版本
ALPHA=true ./scripts/build_with_timestamp.sh apk debug

# 帶額外參數
./scripts/build_with_timestamp.sh web release --dart-define=FLAVOR=production
```

### Windows 使用方式

```cmd
REM 基本使用
scripts\build_with_timestamp.bat web release
scripts\build_with_timestamp.bat apk release

REM 強制更新版本
set FORCE_UPDATE=true
scripts\build_with_timestamp.bat web release

REM 測試版本
set BETA=true
scripts\build_with_timestamp.bat apk release

REM 內測版本
set ALPHA=true
scripts\build_with_timestamp.bat apk debug
```

## 建置號碼格式說明

### 標準格式
- **格式**：`YYYYMMDD_HHMMSS[_標記]`
- **範例**：`20250807_131801`、`20250807_131801_force_update`

### 標記說明
- `_debug` - 除錯版本
- `_profile` - 效能分析版本
- `_force_update` - 強制更新版本
- `_beta` - 測試版本
- `_alpha` - 內測版本

### 版本顯示轉換

| 原始建置號碼 | 格式化顯示 | 版本標籤 |
|-------------|-----------|----------|
| `20250807_131801` | `20250807.1318` | 正式版 |
| `20250807_131801_force_update0` | `20250807.1318-強制更新` | 強制更新 |
| `20250807_131801_beta` | `20250807.1318-測試版` | 測試版 |
| `20250807_131801_debug` | `20250807.1318-除錯版` | 開發版 |
| `1` | `1` | 開發版 |

## 實作細節

### 1. about_us_page.dart 更新

```dart
// 原始程式碼
FutureBuilder<PackageInfo>(
  future: PackageInfo.fromPlatform(),
  builder: (context, snapshot) {
    final String buildDate = DateFormat('yyyy-MM-dd HH:mm').format(DateTime.now());
    // ...
  },
)

// 更新後程式碼
FutureBuilder<BuildInfo>(
  future: BuildInfoUtils.getBuildInfo(),
  builder: (context, snapshot) {
    final BuildInfo? buildInfo = snapshot.data;
    final String buildDate = buildInfo?.buildDate ?? '未知';
    // ...
  },
)
```

### 2. 版本標籤 UI

```dart
Row(
  children: [
    const Text('AstReal 占星應用'),
    if (buildInfo != null) ...[
      const SizedBox(width: 8),
      Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
        decoration: BoxDecoration(
          color: buildInfo.versionTagColor.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: buildInfo.versionTagColor.withValues(alpha: 0.3),
          ),
        ),
        child: Text(
          buildInfo.versionTag,
          style: TextStyle(
            fontSize: 10,
            fontWeight: FontWeight.w600,
            color: buildInfo.versionTagColor,
          ),
        ),
      ),
    ],
  ],
),
```

## 測試驗證

### 1. 單元測試

```dart
test('should parse build date correctly', () {
  expect(
    BuildInfoUtils.getBuildDate('20250807_131801'),
    equals('2025-08-07 13:18:01'),
  );
  
  expect(
    BuildInfoUtils.formatVersionDisplay('1.0.0', '20250807_131801_force_update0'),
    equals('1.0.0 (20250807.1318-強制更新)'),
  );
});
```

### 2. 整合測試

使用建置腳本建立測試版本，驗證：
- 建置時間是否正確顯示為打包時間
- 版本號格式化是否正確
- 版本標籤是否正確顯示

## 部署建議

### 1. CI/CD 整合

在 CI/CD 流程中使用建置腳本：

```yaml
# GitHub Actions 範例
- name: Build Web Release
  run: |
    chmod +x scripts/build_with_timestamp.sh
    ./scripts/build_with_timestamp.sh web release
  env:
    FORCE_UPDATE: ${{ github.event.inputs.force_update }}
```

### 2. 版本管理策略

- **開發版本**：使用 `debug` 建置類型
- **測試版本**：使用 `BETA=true` 環境變數
- **正式版本**：使用 `release` 建置類型
- **緊急更新**：使用 `FORCE_UPDATE=true` 環境變數

## 故障排除

### 常見問題

1. **建置時間顯示為當前時間**
   - 確認使用建置腳本而非直接 `flutter build`
   - 檢查建置號碼格式是否正確

2. **版本標籤不顯示**
   - 確認 `BuildInfo` 物件不為 null
   - 檢查建置號碼是否包含正確的標記

3. **建置腳本執行失敗**
   - 確認腳本有執行權限（Linux/macOS）
   - 檢查 Flutter 環境是否正確設定

### 除錯方法

```dart
// 在 about_us_page.dart 中加入除錯資訊
print('BuildInfo: $buildInfo');
print('Build Number: ${buildInfo?.buildNumber}');
print('Formatted Version: ${buildInfo?.formattedVersion}');
```

## 總結

這個優化方案解決了建置時間和版本顯示的問題，提供了：

1. **準確的建置時間**：從建置號碼解析實際打包時間
2. **友好的版本顯示**：將複雜的建置號碼格式化為易讀格式
3. **版本標籤系統**：視覺化區分不同類型的版本
4. **自動化建置腳本**：簡化建置流程並確保一致性

通過這些改進，用戶可以清楚地看到應用的實際建置時間和版本資訊，開發團隊也能更好地管理不同類型的版本發布。
