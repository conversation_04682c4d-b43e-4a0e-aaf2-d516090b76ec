# Starlight 初心者模式分析頁面整合說明

## 概述

`StarlightAnalysisPage` 是為初心者模式（Starlight）設計的分析頁面，使用通俗易懂的語言替代專業占星術語，讓一般大眾能夠理解每個功能的作用。

## 主要特色

### 1. 通俗化語言
- **專業術語** → **通俗語言**
- 星盤分析 → 個人分析
- 本命盤 → 我的性格分析
- 合盤 → 配對分析
- 流年盤 → 當前運勢
- 次限推運 → 人生發展趨勢

### 2. 用戶友好的分類
- **了解自己**：個人性格分析
- **關係分析**：人際關係和配對分析
- **未來預測**：運勢和發展趨勢
- **特殊時刻**：季節和天象影響

### 3. 清晰的功能描述
每個分析功能都有簡單明瞭的描述，讓用戶知道能得到什麼樣的資訊。

## 設計原則

### 1. 顏色系統
- 主色調：`AppColors.solarAmber`（太陽琥珀色）
- 輔助色：`AppColors.indigoLight`、`AppColors.paleAmber`
- 功能色：根據不同分析類型使用不同顏色

### 2. 圖標選擇
- 使用更親民的圖標
- 避免過於專業的符號
- 強調功能性和易理解性

### 3. 文案風格
- 避免專業術語
- 使用日常語言
- 強調實用性和個人化

## 功能對照表

| 專業模式 | 初心者模式 | 說明 |
|---------|-----------|------|
| 星盤分析 | 個人分析 | 頁面標題 |
| 本命盤分析 | 我的性格分析 | 個人特質分析 |
| 合盤分析 | 配對分析 | 關係相容性 |
| 組合盤分析 | 關係深度分析 | 關係化學反應 |
| 戴維森盤 | 關係時空分析 | 相遇意義 |
| 馬克思盤 | 內心感受分析 | 關係中的感受 |
| 流年盤分析 | 當前運勢 | 目前狀況 |
| 次限推運盤 | 人生發展趨勢 | 未來發展 |
| 三限推運盤 | 短期心理變化 | 近期狀態 |
| 太陽返照盤 | 年度運勢 | 整年運勢 |
| 月亮返照盤 | 月度情緒週期 | 每月情緒 |
| 二分二至盤 | 季節能量分析 | 四季影響 |
| 日月蝕盤 | 重要天象影響 | 特殊天象 |

## 整合方式

### 1. 在主導航中整合

需要修改主應用的導航邏輯，根據用戶模式選擇顯示不同的分析頁面：

```dart
// 在 main_page.dart 或相關導航文件中
Widget _buildAnalysisTab() {
  return Consumer<UserModeProvider>(
    builder: (context, userMode, child) {
      if (userMode.isStarlightMode) {
        return const StarlightAnalysisPage();
      } else {
        return const AnalysisPage();
      }
    },
  );
}
```

### 2. 用戶模式檢測

需要在應用啟動時檢測用戶模式：

```dart
// 在 SharedPreferences 中檢查用戶模式
final prefs = await SharedPreferences.getInstance();
final userMode = prefs.getString('user_mode') ?? 'starmaster';
```

### 3. 動態切換

用戶可以在設定頁面切換模式，切換後需要重新載入相應的分析頁面。

## 技術實現

### 1. 文件結構
```
lib/ui/pages/main/
├── analysis_page.dart          # 專業模式分析頁面
├── starlight_analysis_page.dart # 初心者模式分析頁面
└── ...
```

### 2. 共用組件
兩個頁面共用以下組件：
- `StyledCard`
- `ChartSelectionPage`
- `AIInterpretationSelectionPage`
- `FilesPage`

### 3. 導航邏輯
保持相同的導航邏輯和參數傳遞，確保功能一致性。

## 使用說明

### 1. 導入頁面
```dart
import 'package:astreal/ui/pages/main/starlight_analysis_page.dart';
```

### 2. 在路由中註冊
```dart
'/starlight_analysis': (context) => const StarlightAnalysisPage(),
```

### 3. 條件顯示
根據用戶模式條件顯示不同的分析頁面。

## 未來擴展

### 1. 更多通俗化功能
- 添加更多易懂的分析選項
- 提供學習引導
- 增加互動式教學

### 2. 個性化推薦
- 根據用戶興趣推薦分析
- 提供個性化建議
- 智能功能引導

### 3. 社交功能
- 分享分析結果
- 朋友配對分析
- 社群互動

## 注意事項

1. **保持功能一致性**：雖然語言不同，但底層功能應該保持一致
2. **用戶體驗**：確保初心者能夠輕鬆理解和使用
3. **漸進式學習**：可以考慮添加從初心者到專業的學習路徑
4. **反饋收集**：收集用戶反饋，持續優化語言和功能描述

## 測試建議

1. **用戶測試**：邀請非占星背景的用戶測試理解度
2. **功能測試**：確保所有分析功能正常運作
3. **切換測試**：測試模式切換的流暢性
4. **響應式測試**：確保在不同設備上的顯示效果
