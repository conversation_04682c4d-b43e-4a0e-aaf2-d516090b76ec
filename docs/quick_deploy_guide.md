# 快速部署指南

## 🚀 解決「新版本可用」頻繁彈出問題

### 問題描述
之前每次部署都會讓用戶看到「新版本可用，重新載入以獲得最新功能」的通知，即使沒有實質性更新。

### 解決方案
現在系統會智能判斷是否需要通知用戶更新：

## 📋 部署命令速查

### 🔧 日常開發部署
```bash
./scripts/deploy_web.sh
```
- ✅ 適用於：小修改、樣式調整、日常開發
- ✅ 不會打擾用戶
- ✅ 版本格式：`20250119_143000`

### 🔔 重要更新部署
```bash
./scripts/deploy_web.sh --notify-update
```
- ✅ 適用於：新功能、重要修復、安全更新
- ✅ 會通知用戶更新
- ✅ 版本格式：`20250119_143000_update`

### 🎯 正式版本發布
```bash
./scripts/deploy_web.sh --version=1.2.0
```
- ✅ 適用於：里程碑版本、大版本發布
- ✅ 會通知用戶更新
- ✅ 版本格式：`1.2.0`（語義化版本）

## 🧠 智能通知邏輯

### 不會通知的情況
- 版本號相同
- 已經為該版本通知過
- 時間戳版本差距小於 1 小時
- 語義化版本號沒有升級

### 會通知的情況
- 版本號包含 `update` 標記
- 語義化版本號有升級（如 1.1.0 → 1.2.0）
- 時間戳版本差距大於 1 小時

## 🛠️ 測試與驗證

### 測試版本邏輯
```bash
# 在瀏覽器中打開
open scripts/test_version_logic.html
```

### 檢查當前版本
```bash
# 查看版本文件
cat build/web/version.json

# 查看部署範例
./scripts/deploy_examples.sh
```

### 清除用戶通知記錄
```javascript
// 在瀏覽器控制台執行
localStorage.removeItem('lastNotifiedVersion');
```

## 📊 用戶體驗改進

### 之前的問題
- 每次部署都彈出更新通知
- 用戶頻繁被打擾
- 無法區分重要更新和小修改

### 現在的體驗
- 只有重要更新才通知
- 記住已通知的版本，避免重複
- 延長檢查間隔，減少干擾
- 智能判斷版本差異

## 🔧 配置選項

版本更新行為可以通過 `scripts/version_update_config.json` 調整：

- **檢查間隔**：預設 5 分鐘
- **通知閾值**：時間戳版本差距 1 小時
- **稍後提醒**：30 分鐘後重新提醒
- **首次檢查**：頁面載入後 1 分鐘

## 📚 相關文件

- 詳細說明：`docs/version_update_system.md`
- 配置文件：`scripts/version_update_config.json`
- 測試頁面：`scripts/test_version_logic.html`
- 部署腳本：`scripts/deploy_web.sh`

## 💡 最佳實踐

1. **日常開發**：使用一般部署，不打擾用戶
2. **功能更新**：使用 `--notify-update`，確保用戶知道
3. **版本發布**：使用語義化版本號，便於管理
4. **測試先行**：部署前測試版本邏輯
5. **監控日誌**：檢查瀏覽器控制台的版本日誌

---

**現在您可以放心地進行日常部署，不用擔心頻繁打擾用戶了！** 🎉
