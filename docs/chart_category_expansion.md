# ChartCategory 類別擴展實作文件

## 概述
本文件說明在 `ChartCategory` 枚舉中新增同事、名人、寵物、研究四個新類別的實作過程，以及這些類別在系統中的分組和使用方式。

## 新增類別

### 1. 新增的四個類別

| 類別 | 中文名稱 | 圖標 | 顏色 | 分組 |
|------|---------|------|------|------|
| `colleague` | 同事 | `Icons.groups` | `Colors.blue` | 個人相關 |
| `celebrity` | 名人 | `Icons.star` | `Colors.amber` | 特殊類別 |
| `pet` | 寵物 | `Icons.pets` | `Colors.brown` | 個人相關 |
| `research` | 研究 | `Icons.science` | `Colors.indigo` | 專業相關 |

### 2. 類別設計理念

#### 同事 (colleague)
- **用途**：記錄工作夥伴、同事的星盤資料
- **圖標**：`Icons.groups` - 代表團隊和群體
- **顏色**：`Colors.blue` - 專業且友好的藍色
- **分組**：個人相關 - 因為是日常接觸的人際關係

#### 名人 (celebrity)
- **用途**：記錄公眾人物、明星、歷史名人的星盤資料
- **圖標**：`Icons.star` - 代表明星和知名度
- **顏色**：`Colors.amber` - 金色代表光芒和名聲
- **分組**：特殊類別 - 因為是特殊的公眾人物

#### 寵物 (pet)
- **用途**：記錄寵物的星盤資料（寵物占星學）
- **圖標**：`Icons.pets` - 代表寵物和動物
- **顏色**：`Colors.brown` - 溫暖的棕色代表動物
- **分組**：個人相關 - 因為是家庭成員的一部分

#### 研究 (research)
- **用途**：記錄用於學術研究、案例分析的星盤資料
- **圖標**：`Icons.science` - 代表科學和研究
- **顏色**：`Colors.indigo` - 深藍色代表學術和專業
- **分組**：專業相關 - 因為是專業研究用途

## 技術實現

### 1. 枚舉定義更新
```dart
enum ChartCategory {
  personal('個人', Icons.person, AppColors.royalIndigo),
  family('家人', Icons.family_restroom, AppColors.solarAmber),
  friend('朋友', Icons.people, AppColors.indigoLight),
  partner('伴侶', Icons.favorite, Colors.pink),
  colleague('同事', Icons.groups, Colors.blue),        // 新增
  celebrity('名人', Icons.star, Colors.amber),         // 新增
  pet('寵物', Icons.pets, Colors.brown),               // 新增
  work('工作', Icons.work, Colors.green),
  client('客戶', Icons.business, Colors.orange),
  research('研究', Icons.science, Colors.indigo),      // 新增
  event('事件', Icons.event, Colors.purple),
  celestial('天象', Icons.public, Colors.teal),
  other('其他', Icons.more_horiz, Colors.grey);
}
```

### 2. 字符串轉換更新
```dart
static ChartCategory fromString(String value) {
  switch (value) {
    case '個人': return ChartCategory.personal;
    case '家人': return ChartCategory.family;
    case '朋友': return ChartCategory.friend;
    case '伴侶': return ChartCategory.partner;
    case '同事': return ChartCategory.colleague;  // 新增
    case '名人': return ChartCategory.celebrity;  // 新增
    case '寵物': return ChartCategory.pet;        // 新增
    case '工作': return ChartCategory.work;
    case '客戶': return ChartCategory.client;
    case '研究': return ChartCategory.research;   // 新增
    case '事件': return ChartCategory.event;
    case '天象': return ChartCategory.celestial;
    case '其他': return ChartCategory.other;
    default: return ChartCategory.personal;
  }
}
```

### 3. 分組更新

#### 個人相關類別
```dart
static List<ChartCategory> get personalCategories => [
  ChartCategory.personal,
  ChartCategory.family,
  ChartCategory.friend,
  ChartCategory.partner,
  ChartCategory.colleague,  // 新增
  ChartCategory.pet,        // 新增
];
```

#### 專業相關類別
```dart
static List<ChartCategory> get professionalCategories => [
  ChartCategory.work,
  ChartCategory.client,
  ChartCategory.research,   // 新增
];
```

#### 特殊類別
```dart
static List<ChartCategory> get specialCategories => [
  ChartCategory.celebrity,  // 新增
  ChartCategory.event,
  ChartCategory.celestial,
  ChartCategory.other,
];
```

## 使用場景

### 1. 同事類別使用場景
- **職場分析**：分析同事的性格特質，改善工作關係
- **團隊配置**：了解團隊成員的星盤配置，優化合作
- **職場人際**：建立更好的職場人際關係
- **工作溝通**：根據星盤特質調整溝通方式

### 2. 名人類別使用場景
- **學習研究**：研究成功人士的星盤特徵
- **案例分析**：分析歷史名人的命運軌跡
- **占星教學**：作為教學案例使用
- **趨勢分析**：研究不同時代名人的星盤特點

### 3. 寵物類別使用場景
- **寵物占星**：分析寵物的性格和行為特徵
- **主寵關係**：研究主人和寵物的星盤相容性
- **寵物健康**：根據星盤分析寵物的健康傾向
- **行為預測**：預測寵物的行為模式

### 4. 研究類別使用場景
- **學術研究**：收集用於學術論文的星盤資料
- **統計分析**：進行大規模的占星統計研究
- **案例收集**：建立特定主題的案例資料庫
- **實驗對照**：作為對照組進行比較研究

## 系統整合

### 1. 過濾功能整合
新增的類別會自動整合到現有的過濾系統中：
- 在過濾選擇器中按分組顯示
- 支援多選和單選操作
- 與搜尋功能協同工作

### 2. 統計功能整合
- 在統計報告中包含新類別的數據
- 支援按新類別進行數據分析
- 提供類別分佈的視覺化圖表

### 3. 導入導出功能整合
- CSV 導入時支援新類別的識別
- 導出時包含新類別的標識
- 向後兼容舊版本的資料格式

## 用戶界面更新

### 1. 類別選擇器
- 新類別會出現在相應的分組中
- 使用對應的圖標和顏色顯示
- 支援快速搜尋和選擇

### 2. 資料卡片顯示
- 資料卡片會顯示新類別的圖標和顏色
- 類別標籤會顯示正確的中文名稱
- 保持視覺一致性

### 3. 過濾界面
- 過濾選擇器中按分組顯示新類別
- 提供清晰的視覺區分
- 支援批量選擇操作

## 數據遷移

### 1. 現有數據兼容性
- 現有的出生資料不受影響
- 新類別只對新建立的資料生效
- 支援手動更改現有資料的類別

### 2. 預設值處理
- 新建資料時提供新類別選項
- 保持「個人」作為預設類別
- 提供類別建議功能

## 測試驗證

### 1. 功能測試
- ✅ 新類別正確顯示在選擇器中
- ✅ 過濾功能支援新類別
- ✅ 搜尋功能正常工作
- ✅ 資料保存和載入正常

### 2. 界面測試
- ✅ 圖標和顏色正確顯示
- ✅ 分組邏輯正確
- ✅ 多語言支援正常
- ✅ 響應式設計適配

### 3. 兼容性測試
- ✅ 向後兼容現有資料
- ✅ 導入導出功能正常
- ✅ 不同平台表現一致
- ✅ 性能影響最小

## 未來擴展

### 1. 子類別支援
可以考慮為某些類別添加子類別：
- 寵物：狗、貓、鳥類等
- 名人：演員、歌手、政治家等
- 研究：醫學、心理學、社會學等

### 2. 自定義類別
- 允許用戶創建自定義類別
- 支援類別的個人化設定
- 提供類別模板功能

### 3. 智能分類
- 基於姓名或其他資訊自動建議類別
- 機器學習輔助分類
- 批量分類功能

## 總結

新增同事、名人、寵物、研究四個類別大大豐富了星盤資料的分類體系：

1. **覆蓋更全面**：涵蓋了更多的人際關係和使用場景
2. **分組合理**：按照邏輯關係進行適當的分組
3. **視覺清晰**：每個類別都有獨特的圖標和顏色
4. **功能完整**：與現有系統完美整合
5. **擴展性好**：為未來的功能擴展奠定基礎

這些新類別讓用戶能夠更精確地管理和分類他們的星盤資料，無論是職場關係、學術研究、寵物占星還是名人分析，都能找到合適的分類方式，大大提升了應用的實用性和專業性。
