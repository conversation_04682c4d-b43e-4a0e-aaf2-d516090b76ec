rules_version = '2';

// Firebase Storage 安全規則
// 用於 Astreal 應用的出生資料備份功能
service firebase.storage {
  match /b/{bucket}/o {
    
    // 用戶備份資料夾規則
    // 路徑格式: user_backups/{userId}/birth_data_backup.csv
    match /user_backups/{userId}/{allPaths=**} {
      // 允許已認證的用戶訪問自己的備份資料
      // 在開發環境中放寬 App Check 要求
      allow read, write: if request.auth != null
                      && request.auth.uid == userId;
    }
    
    // 公共資源（如果需要）
    match /public/{allPaths=**} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    // 預設拒絕所有其他訪問
    match /{allPaths=**} {
      allow read, write: if false;
    }
  }
}
