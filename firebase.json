{"storage": {"rules": "storage.rules"}, "hosting": [{"site": "astreal", "public": "build/web", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "destination": "/index.html"}], "headers": [{"source": "/version.json", "headers": [{"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}, {"key": "Access-Control-Allow-Origin", "value": "*"}]}, {"source": "**/simple_version_check.js", "headers": [{"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}]}, {"source": "**/sw_enhanced.js", "headers": [{"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}]}, {"source": "**/*.@(html|js|css|dart)", "headers": [{"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}]}, {"source": "**/main.dart.js", "headers": [{"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}]}, {"source": "**/flutter_service_worker.js", "headers": [{"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}]}, {"source": "**/manifest.json", "headers": [{"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}]}, {"source": "**/*.@(png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf)", "headers": [{"key": "Cache-Control", "value": "public, max-age=3600"}]}]}, {"site": "astreal-website", "public": "website", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "/", "destination": "/landing.html"}, {"source": "/home", "destination": "/home.html"}, {"source": "/download", "destination": "/download.html"}, {"source": "/privacy-policy", "destination": "/privacy-policy.html"}, {"source": "/terms-of-service", "destination": "/terms-of-service.html"}, {"source": "**", "destination": "/index.html"}], "headers": [{"source": "**/*.@(html|js|css)", "headers": [{"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}]}, {"source": "**/*.@(png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf)", "headers": [{"key": "Cache-Control", "value": "public, max-age=86400"}]}]}], "flutter": {"platforms": {"android": {"default": {"projectId": "astreal-d3f70", "appId": "1:470077449550:android:4971c9e15686127296aa1f", "fileOutput": "android/app/google-services.json"}}, "ios": {"default": {"projectId": "astreal-d3f70", "appId": "1:470077449550:ios:dcbc192966cde49096aa1f", "uploadDebugSymbols": false, "fileOutput": "ios/Runner/GoogleService-Info.plist"}}, "macos": {"default": {"projectId": "astreal-d3f70", "appId": "1:470077449550:ios:5de969696d97c30b96aa1f", "uploadDebugSymbols": false, "fileOutput": "macos/Runner/GoogleService-Info.plist"}}, "dart": {"lib/firebase_options.dart": {"projectId": "astreal-d3f70", "configurations": {"android": "1:470077449550:android:4971c9e15686127296aa1f", "ios": "1:470077449550:ios:dcbc192966cde49096aa1f", "macos": "1:470077449550:ios:5de969696d97c30b96aa1f", "web": "1:470077449550:web:18e235ee5adb571396aa1f", "windows": "1:470077449550:web:bd0469a89cc9b70896aa1f"}}}}}}